const MathItemHandler = require("./mathItemHandler");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const {BaseRcj2022} = require("../../models/BaseRcj2022");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. HXXXX YYYY *n 对应材料编码 乘
 *    2. HXXXX YYYY n 对应材料编码 重置为 n
 */
class Hxxx$yyy$VMathHandler extends MathItemHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 2;
        let mathSubArr = mathItem.math.substring(1).split(/\s+/);
        mathItem.fromRCJCode = mathSubArr[0];
        mathItem.fromRCJLibraryCode = this.rule.libraryCode;
        mathItem.toRCJCode = mathSubArr[1];
        mathItem.toRCJLibraryCode = this.rule.libraryCode;
        mathItem.parseMath = mathSubArr[2];
        mathItem.operator = this.mathOperator(mathItem.parseMath);
    }

    async activeRCJ() {
        let item = this.mathItem;
        let fromRCjS = this.findActiveRCJByCode(item.fromRCJCode);
        if(ObjectUtil.isEmpty(fromRCjS)){
            let rcj = await this.addNewRCJ(item.toRCJLibraryCode, item.toRCJCode);
            item.activeRCJs = [rcj];
        }else{
            if(item.fromRCJCode != fromRCjS[0].materialCode) {
                this.notStandardActiveRcjCodes.push([item.fromRCJCode, fromRCjS[0].materialCode]);
            }
            if(item.toRCJCode === item.fromRCJCode){
                this.mathItem.activeRCJs.push(fromRCjS[0]);
            }else{
                let toRcj = await this.app.gljAppDataSource
                    .getRepository(BaseRcj2022)
                    .findOneBy({
                        libraryCode: item.toRCJLibraryCode,
                        materialCode: item.toRCJCode,
                    });

                let newRcj = await this.editRcj(fromRCjS[0],toRcj);
                item.activeRCJs.push(newRcj);

                // for(let curRcj of fromRCjS){
                //     let newRcj = await this.editRcj(curRcj,toRcj);
                //     item.activeRCJs.push(newRcj);
                // }
            }
        }
    }
}

module.exports = Hxxx$yyy$VMathHandler;