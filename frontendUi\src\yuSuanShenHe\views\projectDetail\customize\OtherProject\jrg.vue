<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-03-25 11:15:09
-->
<template>
  <div class="table-content">
    <child-page-table
      :pageType="'jrg'"
      :columnList="columnList"
    ></child-page-table>
  </div>
</template>
<script setup>
import ChildPageTable from './childPageTable.vue';
const columnList = [
  {
    field: '',
    title: '',
    minWidth: 60,
    slots: { default: 'changeIdentification' },
  },
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    type: 'text',
  },
  {
    field: 'worksName',
    title: '名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'worksName_edit' },
  },
  {
    field: 'specification',
    title: '规格',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'spec_edit' },
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },

  {
    title: '送审',
    children: [
      {
        field: 'tentativeQuantity',
        minWidth: 80,
        title: '数量',
        slots: { default: 'tentativeQuantity_default' },
      },
      {
        field: 'csPrice',
        title: '除税单价',
        minWidth: 100,
        slots: { default: 'csPrice_default' },
      },
      {
        field: 'price',
        title: '综合单价',
        minWidth: 100,
        slots: { default: 'price_default' },
      },
      {
        field: 'total',
        minWidth: 100,
        title: '合价',
        slots: { default: 'total_default' },
      },
      {
        field: 'taxRemoval',
        title: '除税系数(%)',
        minWidth: 180,
        slots: { default: 'taxRemoval_default' },
      },
      {
        field: 'jxTotal',
        title: '进项税额',
        minWidth: 180,
        slots: { default: 'jxTotal_default' },
      },
    ],
  },
  {
    title: '审定',
    children: [
      {
        field: 'tentativeQuantity',
        minWidth: 80,
        title: '数量',
      },
      {
        field: 'csPrice',
        title: '除税单价',
        minWidth: 100,
      },
      {
        field: 'price',
        title: '综合单价',
        minWidth: 100,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'price_edit' },
      },
      {
        field: 'total',
        minWidth: 100,
        title: '合价',
      },
      {
        field: 'taxRemoval',
        title: '除税系数(%)',
        minWidth: 180,
      },
      {
        field: 'jxTotal',
        title: '进项税额',
        minWidth: 180,
      },
    ],
  },
  {
    field: 'quantitativeExpression',
    title: '数量表达式',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'quant_edit' },
  },
  {
    field: 'changeTotal',
    minWidth: 100,
    title: '增减金额',
    slots: { default: 'changeTotal_default' },
  },
  {
    field: 'ysshSysj.changeExplain',
    title: '增减说明',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { default: 'changeExplain_default', edit: 'changeExplain_edit' },
  },
];
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
