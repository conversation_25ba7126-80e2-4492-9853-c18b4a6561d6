<!--
 * @Descripttion: 选择报表
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: sunchen
 * @LastEditTime: 2024-10-21 11:09:38
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="600px"
    v-model:modelValue="dialogVisible"
    title="报表新建"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="tree-content-wrap">
      <div class="form-item">
        <div class="title">选择模板：</div>
        <a-select
          class="select-report"
          v-model:value="workbookName"
          show-search
          placeholder="报表模板"

        >
        <a-select-option :value="item.value" v-for="(item,k) of options" :key="k">{{item.label}}</a-select-option>

      </a-select>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          @click="save"
          :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {workBookData} from '@/components/reportTemplate/index.js';

import {
  ref,
  reactive,
  watch,
  nextTick,
  markRaw,
  defineExpose,
  onBeforeUnmount,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import XEUtils from 'xe-utils';

const store = projectDetailStore();
const emit = defineEmits(['handleOk']);
const route = useRoute();

const treeData = ref(null);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const checkedKeys = ref([]); // 选中的数据，为了后续要处理其他字段
const dataStatus = ref(null); // 全选，单选
const useType = ref('招标项目报表'); // 弹窗里的文件类型
let treeLoading = ref(false);
let timer = ref(null);
let expandedKeys = ref([]);
/**
 * 切换了导出文件类型
 */
const changeType = XEUtils.debounce(() => {
  treeData.value = null;
  expandedKeys.value = [];
  checkedKeys.value = [];
  getTreeList();
}, 300);

/**
 * 获取树数据
 */
const getTreeList = () => {
  treeLoading.value = true;
  csProject
    .exportProjectTree(
      route.query.constructSequenceNbr,
      useType.value,
    )
    .then(res => {
      console.log('数数据', res.result);
      if (res.status === 200 && res.result) {
        treeData.value = [res.result];
        expandedKeys.value.push(treeData.value[0].id);
      }
    })
    .finally(() => {
      nextTick(() => {
        treeLoading.value = false;
      });
    });
};

const cancel = () => {
  treeData.value = null;
  useType.value = 1;
  dialogVisible.value = false;
  checkedKeys.value = [];
  expandedKeys.value = [];
  dataStatus.value = null;
};



const save = async () => {
  if (!workbookName.value) {
    message.error('请选择模板');
    return;
  }
  try {
    submitLoading.value = true;
    const data = options.value.find(i => i.value == workbookName.value);
    emit('handleOk',{label:data.label,data:data.data})
    cancel();
  } catch (error) {

  } finally {
    submitLoading.value = false;
  }
};



onBeforeUnmount(() => {
  console.log('销毁了');
  // debugger
  treeLoading.value = false;
  timer.value = null;
});



let workbookName = ref(null)
let options = ref(null)
let lanMuNames = ref()

const setOptions = () =>{
  const { levelType,children  } = store.currentTreeInfo;
  const deType = store.deStandardReleaseYear
  const taxMade = store.taxMade
  // 根据定额，以及12还是22等区分
  let showList = workBookData.filter(i=>{
    return i.levelType.includes(+levelType) &&
    (i?.deType || []).includes(+deType) &&
     i.lanMuName.includes(lanMuNames.value) &&
     ((i?.taxMade && taxMade == i?.taxMade) || !i?.taxMade)

  })

  // 如果是单项层级，则区分子级是不是单项或者单位
  if(levelType == 2){
    let hasDx = children.some(i=>i.levelType == 2)
    let filterName = hasDx?'childeDx':'childeDw'
    showList = showList.filter(i=>{
      return !i.showStatus || i.showStatus.includes(filterName)
    })
  }

  // showList 去重复
  console.log("🚀 ~ lists ~ showList:", showList)
  const lists = Array.from(new Set(showList)).map(i=>{
    return {
      label: i.name,
      value: i.name+'|'+ JSON.stringify(i?.levelType) + '|'+ JSON.stringify(i?.lanMuName)+ '|'+ JSON.stringify(i?.deType),
      data: i.data
    }
  })
  options.value = Array.from(new Set(lists))
  // const itemLevelList = ['project', 'single', 'unit'];
  // const { levelType } = store.currentTreeInfo;
  // let postData = {
  //   constructId: route.query.constructSequenceNbr,
  //   singleId: null,
  //   unitId: null,
  //   itemLevel:  itemLevelList[levelType - 1],
  //   lanMuName: lanMuNames.value,
  // }

  // switch (levelType) {
  //     case 1:
  //     postData.constructId = route.query?.constructSequenceNbr;
  //     postData.singleId = null;
  //     break;
  //   case 2:
  //     postData.singleId = store.currentTreeInfo.id;
  //     postData.unitId = null;
  //     break;
  //   case 3:
  //     postData.singleId = store.currentTreeInfo?.parentId;
  //     postData.unitId = store.currentTreeInfo.id;
  //     break;
  // }

  // csProject.getReportSheet(postData).then(res => {
  //   options.value = res.result.map(i=>{
  //     return {
  //       label: i.headLine,
  //       value: i.headLine,
  //       data: i.excelDataTemplate
  //     }
  //   })
  // })
}




/**
 *
 * @param {*} type 点击类型
 */
 const open = ({lanMuName}) => {
  treeLoading.value = true;
  dialogVisible.value = true;
  submitLoading.value = false;
  workbookName.value = null;
  lanMuNames.value = lanMuName
  setOptions()
};


defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss" scoped>

.dialog-comm {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  display: flex;
  flex-direction: column;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;
  &:hover {
    overflow: auto;
  }
  .form-item{
    display: flex;
    margin-bottom: 30px;
    .title{
      font-size: 16px;
    }
    .select-report{
      flex: 1;
    }
  }

}


</style>
