const Jie<PERSON>uanMenuBarEnum = Object.freeze(Object.fromEntries([
    ['PROJECT_OVERVIEW', {
        type: '1',
        levelType: '1',
        code: '1',
        value: '项目概况'
    }],
    ['COST_ANALYSIS', {
        type: '1',
        levelType: '1',
        code: '2',
        value: '造价分析'
    }],
    ['RCJ_SUMMARY', {
        type: '1',
        levelType: '1',
        code: '4',
        value: '人材机调整'
    }]
    ,
    ['SINGLE_COST_ANALYSIS', {
        type: '1',
        levelType: '2',
        code: '1',
        value: '造价分析'
    }]
    ,
    ['UNIT_PROJECT_OVERVIEW', {
        type: '1',
        levelType: '3',
        code: '1',
        value: '项目概况'
    }]
    ,
    // ['UNIT_COST_ANALYSIS', {
    //     type: '1',
    //     levelType: '3',
    //     code: '2',
    //     value: '造价分析'
    // }],
    ['UNIT_ITEM_BILL', {
        type: '1',
        levelType: '3',
        code: '4',
        value: '分部分项'
    }],
    ['UNIT_MEASURE_PROJECT', {
        type: '1',
        levelType: '3',
        code: '5',
        value: '措施项目'
    }],
    ['UNIT_RCJ_SUMMARY', {
        type: '1',
        levelType: '3',
        code: '6',
        value: '人材机调整'
    }],
    ['UNIT_OTHER_PROJECT', {
        type: '1',
        levelType: '3',
        code: '7',
        value: '其他项目'
    }],
    ['UNIT_COST_AGGREGATION', {
        type: '1',
        levelType: '3',
        code: '8',
        value: '费用汇总'
    }]
]));

module.exports = JieSuanMenuBarEnum;