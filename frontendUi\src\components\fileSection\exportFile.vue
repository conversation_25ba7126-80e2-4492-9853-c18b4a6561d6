<!--
 * @Descripttion: 导出。ysf文件
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: sunchen
 * @LastEditTime: 2024-03-16 11:13:35
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="auto"
    @close="cancel"
    v-model:modelValue="dialogVisible"
    title="导出项目"
  >
    <div class="tree-content-wrap">
      <div class="dialog-content">
        <div class="title">请选择需要导出的项目</div>
        <div class="list" v-if="treeData">
          <a-tree
            :defaultExpandAll="true"
            checkable
            show-line
            :tree-data="treeData"
            :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
            v-model:checkedKeys="checkedKeys"
            @check="checkSelect($event)"
          >
            <template #switcherIcon="{ switcherCls, children }">
              <down-outlined :class="switcherCls" />
            </template>
          </a-tree>
        </div>
      </div>
      <div class="group-list">
        <a-radio-group v-model:value="dataStatus" @change="changeCheck">
          <a-radio value="all">全部</a-radio>
          <a-radio value="part">取消全部</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="handleOk" :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, watch, nextTick, toRaw, defineExpose } from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import { DownOutlined } from '@ant-design/icons-vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import csProjectGlj, { constructLevelTreeStructureList as constructLevelTreeStructureListGlj } from '@gongLiaoJi/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();

const emits = defineEmits(['closeDialog']);
const route = useRoute();
const treeData = ref(null);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const checkedKeys = ref([]);
const dataStatus = ref(null);

const getTreeList = async () => {
  let list = null;
  let res = null;
  if (projectStore.type === 'glj') {
    res = await constructLevelTreeStructureListGlj(
      route.query.constructSequenceNbr
    );
    res.result.forEach(item => {
      item.id = item.sequenceNbr;
    })
  } else {
    res = await constructLevelTreeStructureList(
      route.query.constructSequenceNbr
    );
  }
  console.log('🚀 ~ file: exportFile.vue:69 ~ getTreeList ~ res:', res.result);
  if (res.status === 200) {
    list = res.result;
  } else {
    message.error(res.message);
    return;
  }
  treeData.value = xeUtils.toArrayTree(list);
};
const checkSelect = eve => {
  let allList = xeUtils.toTreeArray(treeData.value);
  if ((eve.length === allList.length) > 0) {
    dataStatus.value = 'all';
  } else if (eve.length === 0 && allList.length > 0) {
    dataStatus.value = 'part';
  }
};
const cancel = () => {
  dialogVisible.value = false;
  checkedKeys.value = [];
  dataStatus.value = null;
  emits('closeDialog');
};

const changeCheck = () => {
  if (dataStatus.value === 'all') {
    checkedKeys.value = [treeData.value[0]?.id];
  } else {
    checkedKeys.value = [];
  }
};

const handleOk = async () => {
  if (!checkedKeys.value || !checkedKeys.value.length) {
    message.error('请选择要导出的工程');
    return;
  }

  submitLoading.value = true;
  try {
    const list = await flattenTree(treeData.value)
    setStatus(list)
    list[0].selected = true
    console.log("🚀 ~ handleOk ~ list:", list[0])
    let res;
    if (projectStore.type === 'glj') {
      res = await csProjectGlj.exportGljFile(toRaw(list[0]));
    } else {
      res = await csProject.ysfFileOutput(toRaw(list[0]));
    }
    // downloadYsfFile(res, list.projectName);

    if (res.status == 200) {
      switch (res.result) {
        case 0:
          console.log('点击了取消');
          break;
        case 2:
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-qiangtixing',
            infoText: '文件已打开，不能覆盖!',
            confirm: () => {
              infoMode.hide();
            },
          });
          break;
        case 1:
          message.success('导出成功');
          cancel();
          break;
        default:
          break;
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    submitLoading.value = false;
  }
};



const flattenTree = treeList => {
  const result = [];

  function traverse(node) {
    result.push(node); // 将当前节点添加到结果数组中

    if (node.children && node.children.length > 0) {
      // 递归处理子节点
      for (let i = 0; i < node.children.length; i++) {
        const data = node.children[i];
        data.selected =
          checkedKeys.value.includes(data.id) ||
          ['all'].includes(dataStatus.value);
        traverse(data);
      }
    }
  }
  // 遍历树列表中的每个根节点
  for (let i = 0; i < treeList.length; i++) {
    const root = treeList[i];
    root.selected =
      checkedKeys.value.includes(root.id) || ['all'].includes(dataStatus.value);
    traverse(root);
  }

  return result;
};


const updateParentSelection = (node) => {
  if (node.children && node.children.length > 0) {
    let hasSelectedChild = node.children.some(child => child.selected || setStatus(node.children));
    if (hasSelectedChild) {
      node.selected = true;
    }
  } else {
    return node.selected;
  }
}

const setStatus = (list) =>{
 for(let i of list){
    updateParentSelection(i)
    if(i.children && i.children.length){
      setStatus(i.children)
    }
  }
}



const open = () => {
  getTreeList();
  dialogVisible.value = true;
};
open();
</script>

<style lang="scss" scoped>
.tree-content-wrap {
  width: 60vw;
  max-width: 800px;
  height: 60vh;
  display: flex;
  flex-direction: column;
}
.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  display: flex;
  flex-direction: column;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;
  &:hover {
    overflow: auto;
  }
  .title {
    padding: 7px 13px;
    background-color: #eaeaea;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
  }
  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    ::v-deep .ant-tree {
      background-color: #fafafa;
      .ant-tree-switcher-noop {
        opacity: 0;
      }
      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 14px 0 30px 16px;
}
</style>
