<!--
 * @Author: wangru
 * @Date: 2023-08-04 10:39:12
 * @LastEditors: liuxia
 * @LastEditTime: 2024-10-12 16:18:10
 *装饰垂运
-->
<template>
  <div>
    <div class="content">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        :rules="rules"
        @finish="qdExistDe"
        @validate="handleValidate"
        @finishFailed="handleFinishFailed"
      >
        <a-form-item
          label="请选择对应檐口高度（层高）"
          name="up"
          v-if="tableData.length > 0"
        >
          <a-row>
            <a-select
              v-model:value="formState.up"
              style="width: 230px; margin-right: 10px"
              :options="upList"
              :field-names="{ label: 'storey', value: 'zsDeCode' }"
              placeholder="请选择地上最高檐口高度/层数"
            ></a-select>
            <a-select
              v-model:value="formState.down"
              style="width: 230px"
              :options="downList"
              :field-names="{ label: 'storey', value: 'zsDeCode' }"
              placeholder="请选择地下最低檐口高度/层数"
            ></a-select>
          </a-row>
        </a-form-item>

        <div class="table-content">
          <vxe-table
            align="center"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            :data="tableData"
            height="350"
            width="450"
			class='table-line'
            :tree-config="{
              transform: true,
              rowField: 'sequenceNbr',
              parentField: 'parentId',
              line: true,
              showIcon: true,
              expandAll: true,
			  iconOpen: 'icon-caret-down',
			  iconClose: 'icon-caret-right'
            }"
            :row-class-name="rowClassName"
          >
            <vxe-column field="fxCode" width="20%" title="项目编码" tree-node></vxe-column>
            <vxe-column field="type" width="5%" title="类型"> </vxe-column>
            <vxe-column field="name" width="55%" title="名称"> </vxe-column>
            <vxe-column
              field="±0.00以上/±0.00以下"
              width="20%"
              title="±0.00以上"
            >
              <template #default="{ row }">
                <vxe-select
                  v-model="row.upOrDown"
                  transfer
                  @change="selectChange(row, $event)"
                >
                  <vxe-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  ></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
          </vxe-table>

          <a-button
            :disabled="tableData.length === 0"
            @click="locationModel = true"
            type="primary"
            ghost
            >计取位置</a-button
          >
        </div>
        <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
          <a-button type="primary" html-type="submit" :loading="submitLoading"
            >提交</a-button
          >
        </a-form-item>
      </a-form>
    </div>
  </div>
  <common-modal
    title="装饰垂运计取位置"
    width="600"
    height="350"
    className="dialog-comm"
    v-model:modelValue="locationModel"
  >
    <a-radio-group
      v-model:value="optionType"
      style="width: 100%"
      @change="radioChange"
    >
      <p class="radioP" v-if="haveQdList.length > 0">
        <a-radio :value="1">计取至已有清单</a-radio>
        <a-select
          v-model:value="constructionMeasureType"
          style="width: 65%"
          :options="haveQdList"
          placeholder="请选择"
          :field-names="{ label: 'className', value: 'classCode' }"
          @change="typeChange(false)"
        ></a-select>
      </p>
      <p class="radioP">
        <a-radio :value="2">新建清单</a-radio>
        <a-select
          v-model:value="constructionMeasureType1"
          style="width: 65%"
          :options="addQDTypeList"
          placeholder="请选择"
          :field-names="{ label: 'className', value: 'classCode' }"
        ></a-select>
      </p>
    </a-radio-group>
    <p class="line"></p>
    <p class="radioP">
      <span class="title">具体清单</span>
      <span class="detail"
        ><a-input
          v-model:value="selectInputInfo"
          :disabled="selectInputInfo"
          placeholder="具体清单名称"
          style="width: 78%; margin-left: 10px"
        />
        <a-button
          type="primary"
          :disabled="optionType === 2"
          @click="detailModel = true"
          >详情</a-button
        >
      </span>
    </p>

    <p class="btns">
      <a-button type="primary" @click="sureLocation()">确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="指定具体清单"
    width="750"
    height="500"
    className="dialog-comm"
    v-model:modelValue="detailModel"
  >
    <div class="table-content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="
          haveQdList.filter(x => x.classCode === constructionMeasureType)[0]
            .data
        "
        height="350"
        width="450"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: true,
          showIcon: false,
          expandAll: true,
        }"
      >
        <vxe-column field="fxCode" width="20%" title="项目编码"> </vxe-column>
        <vxe-column field="type" width="20%" title="类型"> </vxe-column>
        <vxe-column field="name" width="20%" title="名称"> </vxe-column>
        <vxe-column field="±0.00以上" width="40%" title="计取至该清单">
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.isCheck"
              :disabled="row.kind !== '03'"
              :checked-value="1"
              :unchecked-value="2"
              @change="selectQDChange(row)"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <p class="btns">
      <a-button type="primary" @click="detailModel = false" ghost
        >取消</a-button
      >
      <a-button type="primary" @click="sureJQ()">确定</a-button>
    </p>
  </common-modal>
  <info-modal
    v-model:infoVisible="infoVisible"
    :infoText="infoText"
    :isSureModal="isSureModal"
    :iconType="iconType"
    @updateCurrentInfo="updateCurrentInfo"
  ></info-modal>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import api from '../../../../api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';

const emits = defineEmits(['updateData']);
const projectStore = projectDetailStore();
let locationModel = ref(false);
let detailModel = ref(false);
let upList = ref([]); // 对应檐口高度地上数据列表
let downList = ref([]); // 对应檐口高度地下数据列表
let up = ref(''); // 选中的檐口高度地上数据
let down = ref(''); // 选中的檐口高度地下数据

let formState = reactive({
  up: '',
  down: '',
});
let tableData = ref([]); // 装饰垂运列表数据
const typeList = ref([
  {
    label: '±0.00以上',
    value: 'up',
  },
  {
    label: '±0.00以下',
    value: 'down',
  },
]);

let haveQdList = ref([]);
let optionType = ref(); // 记取位置选择 1 已有清单 2 新建清单
let constructionMeasureType = ref(null); // 已有清单选择的类型
let constructionMeasureType1 = ref(null); // 新建清单选择的类型
let selectQDInfo = ref(); // 选择具体清单信息
let selectInputInfo = ref('');
const addQDTypeList = reactive([
  {
    className: '措施项目-单价措施',
    classCode: 1,
  },
  {
    className: '措施项目-其他总价措施',
    classCode: 3,
  },
  {
    className: '分部分项',
    classCode: 0,
  },
]);
let infoVisible = ref(false); // 提示信息框是否显示
let infoText = ref(''); // 提示信息框的展示文本
let iconType = ref(''); // 提示信息框的图标
let isSureModal = ref(false); // 提示信息框是否为确认提示框
let submitLoading = ref(false); //点击确定按钮loading
let cacheData = ref(null); // 缓存数据

onMounted(() => {
  storeyList();
  conditionDeList();
  // cyCostMathCache();
});
const submit = () => {};
const sureLocation = () => {
  locationModel.value = false;
};

const validateSelect = async (_rule, value) => {
  if (!value && !formState.down) {
    return Promise.reject('请选择对应檐口高度');
  } else {
    return Promise.resolve();
  }
};
const rules = {
  up: [{ required: true, validator: validateSelect, trigger: 'change' }],
  down: [{ required: true, validator: validateSelect, trigger: 'change' }],
};
const sureJQ = () => {
  detailModel.value = false;
  let allList = haveQdList.value.filter(
    x => x.classCode === constructionMeasureType.value
  )[0].data;
  selectQDInfo.value = allList.filter(item => item.isCheck)[0];
  selectInputInfo.value =
    selectQDInfo.value.fxCode + ' ' + selectQDInfo.value.name;
};

const storeyList = () => {
  api
    .storeyList({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
      unitId: projectStore.currentTreeInfo?.id, //单位ID
    })
    .then(res => {
      if (res.status === 200) {
        console.log('檐高列表', res.result);
        upList.value = res.result.up;
        downList.value = res.result.down;
      }
    });
};

// 列表数据获取
const conditionDeList = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.conditionDeList(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('列表数据', res.result);
      tableData.value = res.result;
      cyCostMathCache();
    }
  });
};

// 页面缓存数据列表
const cyCostMathCache = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('🚀 ~ cyCostMathCache ~ apiData:', apiData);
  api.cyCostMathCache(apiData).then(res => {
    if (res.status === 200 && res.result) {
      cacheData.value = res.result;
      console.log('页面缓存数据', res.result);
      formState.up = res.result.up;
      formState.down = res.result.down;
      // constructionMeasureType.value = Number(
      //   res.result.constructionMeasureType
      // );
      // optionType.value = res.result.optionType;
      // tableData.value.map(item => {
      //   //列表数据和缓存数据对比
      //   let same =
      //     cacheData.value &&
      //     cacheData.value.data.filter(
      //       cg => cg.sequenceNbr === item.sequenceNbr
      //     );
      //   if (same && same.length > 0) {
      //     item.upOrDown = same[0].upOrDown;
      //   }
      // });
      // let tempList = replaceDuplicates(tableData.value, res.result.data);
      // console.log('tempList', tempList)
      // tableData.value = JSON.parse(JSON.stringify(tempList));
      // console.log('1111111111111', tableData.value)
      // recordPosition();
    }
    if (res.status === 200) {
      recordPosition();
    }
  });
};

const getFinallyData = () => {
  //对比缓存数据和获取到的列表数据
  tableData.value.map(item => {
    //列表数据和缓存数据对比
    let same =
      cacheData.value &&
      cacheData.value.data.filter(cg => cg.sequenceNbr === item.sequenceNbr);
    if (same && same.length > 0) {
      item.upOrDown = same[0].upOrDown;
    }
  });
  if (cacheData.value) {
    console.log('haveQdList.value', haveQdList.value);
    if (
      haveQdList.value.find(
        item => item.classCode === cacheData.value.constructionMeasureType
      )
    ) {
      optionType.value = 1;
    } else {
      optionType.value = cacheData.value.optionType;
    }

    if (optionType.value === 2) {
      constructionMeasureType1.value = Number(
        cacheData.value.constructionMeasureType
      );
      constructionMeasureType.value = null;
      getCyQd();
    } else {
      constructionMeasureType.value = Number(
        cacheData.value.constructionMeasureType
      );
      constructionMeasureType1.value = null;
      typeChange();
    }
  } else if (!cacheData.value) {
    optionType.value = haveQdList.value.length > 0 ? 1 : 2;
    if (optionType.value === 2) {
      constructionMeasureType1.value = addQDTypeList[0].classCode;
      getCyQd();
    } else {
      constructionMeasureType.value = haveQdList.value[0]?.classCode;
    }
  }
};

const replaceDuplicates = (originalArray, cacheArray) => {
  for (let i = 0; i < originalArray.length; i++) {
    for (let j = 0; j < cacheArray.length; j++) {
      if (originalArray[i].sequenceNbr === cacheArray[j].sequenceNbr) {
        originalArray[i] = cacheArray[j];
        break;
      }
    }
  }

  return originalArray;
};

const recordPosition = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.recordPosition(apiData).then(res => {
    console.log('记取直已有数据', res.result);
    if (res.status === 200 && res.result) {
      haveQdList.value = res.result;
      getFinallyData();
      // if (!optionType.value) {
      //   optionType.value = haveQdList.value.length > 0 ? 1 : 2;
      // }
      // if (constructionMeasureType.value === null) {
      //   constructionMeasureType.value = res.result[0]?.classCode;
      // }
      // if (optionType.value === 2) {
      //   if (constructionMeasureType.value === null) {
      //     constructionMeasureType1.value = addQDTypeList[0].classCode;
      //   } else {
      //     constructionMeasureType1.value = constructionMeasureType.value;
      //   }
      //   getCyQd();
      //   constructionMeasureType.value = null;
      // } else {
      //   typeChange(true);
      // }
    }
    if (optionType.value === 1) {
      typeChange(true);
    }
    if (optionType.value === 2 && !constructionMeasureType1.value) {
      constructionMeasureType1.value = addQDTypeList[0].classCode;
    }
  });
};

const selectQDChange = row => {
  //具体清单列表空值单选
  if (row.isCheck) {
    //具体清单记取只可以选择一项
    haveQdList.value.map(item => {
      item.data.map(i => {
        if (i.sequenceNbr !== row.sequenceNbr) {
          i.isCheck = 0;
        }
      });
    });
  }
};

const radioChange = e => {
  if (optionType.value === 2) {
    constructionMeasureType.value = null;
    constructionMeasureType1.value = 1;
    getCyQd();
  } else {
    constructionMeasureType1.value = null;
    selectInputInfo.value = '';
    constructionMeasureType.value = haveQdList.value[0].classCode;
    typeChange();
  }
};

const typeChange = (reset = false) => {
  console.log('切换', reset);
  if (cacheData.value && reset) {
    haveQdList.value.map(item => {
      item.data.map(i => {
        if (i.sequenceNbr === cacheData.value.qdId) {
          i.isCheck = 1;
          selectQDInfo.value = i;
        }
      });
    });
  } else {
    if (
      constructionMeasureType.value === cacheData.value?.constructionMeasureType
    ) {
      haveQdList.value.map(item => {
        item.data.map(i => {
          if (i.sequenceNbr === cacheData.value.qdId) {
            i.isCheck = 1;
            selectQDInfo.value = i;
          }
        });
      });
    }
    let defaultQdList = haveQdList.value.filter(
      x => x.classCode === constructionMeasureType.value
    )[0].data;
    let isCheckItem = defaultQdList.find(
      item => item.kind === '03' && item.isCheck === 1
    );
    selectQDInfo.value = isCheckItem
      ? isCheckItem
      : defaultQdList.find(item => item.kind === '03');
    selectQDInfo.value.isCheck = 1;
    console.log('!reset', defaultQdList);
  }
  selectInputInfo.value =
    selectQDInfo.value.fxCode + ' ' + selectQDInfo.value.name;
};

const getCyQd = () => {
  api.getCyQd().then(res => {
    console.log('指定清单数据', res);
    if (optionType.value === 2) {
      selectQDInfo.value = res.result;
      selectInputInfo.value =
        selectQDInfo.value.bdCodeLevel04 +
        ' ' +
        selectQDInfo.value.bdNameLevel04;
    }
  });
};

const czysCostMath = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    up: formState.up,
    down: formState.down,
    data: JSON.parse(JSON.stringify(tableData.value)),
    optionType: optionType.value,
    constructionMeasureType:
      optionType.value === 1
        ? constructionMeasureType.value
        : constructionMeasureType1.value,
    qdId: selectQDInfo.value?.sequenceNbr,
  };
  if (apiData.data.length === 0) {
    message.warning('请先创建定额');
    emits('close');
    return;
  }
  console.log('装饰垂运api参数', apiData);
  api
    .czysCostMath(apiData)
    .then(res => {
      console.log('装饰垂运结果', res);
      if (res.status === 200 && res.result) {
        console.log('装饰垂运计取成功', res.result);
        infoVisible.value = true;
        isSureModal.value = true;
        infoText.value = res.result;
        iconType.value = 'icon-ruotixing';
      } else {
        emits('updateData');
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

const qdExistDe = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    qdId: selectQDInfo.value?.sequenceNbr,
    constructionMeasureType: constructionMeasureType.value,
    isCostDe: 4,
  };
  if (submitLoading.value) return;
  submitLoading.value = true;
  api.qdExistDe(apiData).then(res => {
    console.log('dddddddddddddd', res);
    if (res.status === 200) {
      // 弹框提示
      if (res.result) {
        infoVisible.value = true;
        isSureModal.value = false;
        infoText.value = '该清单下已计取定额，是否更新？';
      } else {
        czysCostMath();
      }
    }
  });
};

const handleFinishFailed = errors => {
  console.log('77777777777777777777777', errors);
};

const handleValidate = (...args) => {
  console.log('88888888888888888888', args);
};

const updateCurrentInfo = type => {
  if (type === 1) {
    emits('updateData');
  } else {
    czysCostMath();
  }
};
const selectChange = (row, { value }) => {
  console.log('value', row, value);
  assignPropertyValue(tableData.value, row.sequenceNbr, value);
};

const assignPropertyValue = (items, parentId, propertyValue) => {
  items.forEach(item => {
    if (item.parentId === parentId) {
      item.upOrDown = propertyValue;
      assignPropertyValue(items, item.sequenceNbr, propertyValue);
    }
  });
};

const rowClassName = ({ row }) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }

  return ClassStr;
};
</script>
<style lang="scss" scoped>
@use './tableIcon.scss';
.content {
  .selectContent {
    display: flex;
    justify-content: space-around;
    height: 40px;
  }
  .table-content {
    height: 85%;
  }
}
.radioP {
  display: flex;
  justify-content: space-between;
  margin: 10px auto;
  width: 80%;
  .title {
    margin-left: 5%;
    font-size: 14px;
    color: #2a2a2a;
  }
}
.btns {
  width: 10%;
  display: flex;
  margin: auto;
  justify-content: space-around;
}
.line {
  width: 100%;
  height: 7px;
  background: rgba(221, 221, 221, 0.39);
  opacity: 0.52;
  margin: 20px 0;
}
.detail {
  width: 80%;
}
::v-deep .table-content .ant-btn-primary {
  margin: 10px !important;
}
::v-deep .ant-btn-primary {
  margin: 10px 42% !important;
}
::v-deep .detail .ant-btn-primary {
  margin: 0 0 !important;
}

::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2 !important;
}
::v-deep(.vxe-table .row-qd) {
  background: #dce6fa;
}
::v-deep(.vxe-input--inner) {
  background: transparent;
}
</style>
