import {JieSuanDifferencePrice} from "./JieSuanDifferencePrice";

export class RcjDifferenceInfo {


    public rcjDifferenceType : number;//人材机调整类型

    public JieSuanPriceAdjustmentMethodCode : number;//人材机调整类型



    /**
     * 基期价
     */
    public jieSuanBasePrice: number;


    /**
     * 基期价来源
     */
    public jieSuanBasePriceSource: string;


    /**
     * 单价信息集合
     */
    public jieSuanDifferencePriceList: Array<JieSuanDifferencePrice>;


    constructor(rcjDifferenceType: number, jieSuanBasePrice: number, jieSuanBasePriceSource: string, jieSuanDifferencePriceList: Array<JieSuanDifferencePrice>) {
        this.rcjDifferenceType = rcjDifferenceType;
        this.jieSuanBasePrice = jieSuanBasePrice;
        this.jieSuanBasePriceSource = jieSuanBasePriceSource;
        this.jieSuanDifferencePriceList = jieSuanDifferencePriceList;
    }
}
