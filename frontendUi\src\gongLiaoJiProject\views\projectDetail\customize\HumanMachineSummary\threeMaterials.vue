<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2024-05-13 17:11:27
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-18 10:37:47
-->
<template>
  <div class="table-content">
    <vxe-grid ref="xGrid" v-bind="gridOptions" height="auto" v-on="gridEvents">
      <template #baseJournalPrice_default="{ row }">
        {{
          decimalFormat(
            row.baseJournalPrice,
            'RCJ_COLLECT_BASEJOURNALPRICE_PATH'
          )
        }}
      </template>
      <template #baseJournalTaxPrice_default="{ row }">
        <!-- 含税基期价 -->
        {{
          decimalFormat(
            row.baseJournalTaxPrice,
            'RCJ_COLLECT_BASEJOURNALTAXPRICE_PATH'
          )
        }}
      </template>

      <template #marketPrice_default="{ row }">
        <!-- 不含税市场价 -->
        {{ decimalFormat(row.marketPrice, 'RCJ_COLLECT_MARKETPRICE_PATH') }}
      </template>

      <template #marketTaxPrice_default="{ row }">
        <!-- 含税市场价 -->
        {{
          decimalFormat(row.marketTaxPrice, 'RCJ_COLLECT_MARKETTAXPRICE_PATH')
        }}
      </template>

      <template #taxRate_default="{ row }">
        <!-- 税率（ -->
        {{ decimalFormat(row.taxRate, 'RCJ_COLLECT_TAXRATE_PATH') }}
      </template>

      <template #priceDifferenc_default="{ row }">
        <!-- 价差 -->
        {{ decimalFormat(row.priceDifferenc, 'RCJ_COLLECT_JC_PATH') }}
      </template>

      <template #priceDifferencSum_default="{ row }">
        <!-- 价差合计 -->
        {{ decimalFormat(row.priceDifferencSum, 'RCJ_COLLECT_JCHJ_PATH') }}
      </template>

      <template #totalNumber_default="{ row }">
        <!-- 数量 -->
        {{ decimalFormat(row.totalNumber, 'RCJ_COLLECT_TOTALNUMBER_PATH') }}
      </template>

      <template #transferFactor_default="{ row }">
        <!-- 三材系数 -->
        {{
          decimalFormat(row.transferFactor, 'RCJ_COLLECT_TRANSFERFACTOR_PATH')
        }}
      </template>
      <template #scCount_default="{ row }">
        <!-- 三材量 -->
        {{ decimalFormat(row.scCount, 'COST_ANALYSIS_SCNUMBER_PATH') }}
      </template>

      <template #index_render="{ row }">
        <!-- <div class="multiple-select">
          {{ row.index + 1 }}
        </div> -->
      </template>
      <template #marketPrice_edit="{ row }">
        <vxe-input
          v-model.number="row.marketPrice"
          :clearable="false"
          v-if="
            !row.ifProvisionalEstimate &&
            row.ifLockStandardPrice !== 1 &&
            isPartEdit &&
            !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
            Number(row.edit) !== 1 &&
            !deMapFun.isTz(row.materialCode) &&
            !deMapFun.isJxxs(row.materialCode) &&
            !deMapFun.isQtclf(row.materialCode) &&
            !isChangeAva(row)
          "
          @input="handleInput(row, 'marketPrice')"
        ></vxe-input>
        <span v-else>{{ isChangeAva(row) ? '-' : row.marketPrice }}</span>
      </template>
      <template #marketTaxPrice_edit="{ row }">
        <vxe-input
          v-model.number="row.marketTaxPrice"
          :clearable="false"
          v-if="
            !row.ifProvisionalEstimate &&
            row.ifLockStandardPrice !== 1 &&
            isPartEdit &&
            !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
            Number(row.edit) !== 1 &&
            !deMapFun.isTz(row.materialCode) &&
            !deMapFun.isJxxs(row.materialCode) &&
            !deMapFun.isQtclf(row.materialCode) &&
            !isChangeAva(row)
          "
          @input="handleInput(row, 'marketTaxPrice')"
        ></vxe-input>
        <span v-else>{{ isChangeAva(row) ? '-' : row.marketTaxPrice }}</span>
      </template>
      <template #transferFactor_edit="{ row }">
        <vxe-input
          :clearable="false"
          v-model.trim="row.transferFactor"
          type="text"
          @input="handleInput(row, 'transferFactor')"
        ></vxe-input>
      </template>
      <template #taxRate_edit="{ row }">
        <vxe-input
          v-if="
            (row.taxRate || row.taxRate == 0) &&
            !row.ifProvisionalEstimate &&
            row.unit != '%' &&
            row.ifLockStandardPrice !== 1 &&
            isPartEdit &&
            !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
            Number(row.edit) !== 1 &&
            !deMapFun.isTz(row.materialCode) &&
            !deMapFun.isJxxs(row.materialCode) &&
            !deMapFun.isQtclf(row.materialCode) &&
            !isChangeAva(row)
          "
          :clearable="false"
          v-model.trim="row.taxRate"
          type="number"
          @blur="taxRateBlur(row)"
        ></vxe-input>
        <span v-else>{{ isChangeAva(row) ? '-' : row.taxRate }}</span>
      </template>
      <template #kindSc_edit="{ row }">
        <vxe-select v-model="row.kindSc" transfer>
          <vxe-option
            v-for="item in props.scList"
            :key="item.name"
            :value="item.name"
            :label="item.name"
          ></vxe-option>
        </vxe-select>
      </template>
      <template #remark_edit="{ row }">
        <vxe-input v-model="row.remark" :clearable="false"></vxe-input>
      </template>
      <template #empty>
        <span
          style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          "
        >
          <img :src="getUrl('newCsProject/none.png')" />
        </span>
      </template>
    </vxe-grid>
  </div>
</template>
<script setup>
import {
  ref,
  reactive,
  onMounted,
  defineAsyncComponent,
  watch,
  watchEffect,
  nextTick,
  computed,
  toRaw,
  onActivated,
} from 'vue';
import csProject from '@gongLiaoJi/api/csProject';
import { useRoute } from 'vue-router';
import { getUrl, pureNumber, pureNumber0 } from '@/utils/index';
import xeUtils from 'xe-utils';
import deMapFun from '../deMap';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();
const route = useRoute();
import { projectDetailStore } from '@/store/projectDetail';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';

const projectStore = projectDetailStore();
let xGrid = ref(null);
let oldData = ref([]);
const props = defineProps({
  scList: {
    type: Array,
    default: () => [],
  },
});
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
} = useFormatTableColumns({
  type: 7,
  initColumnsCallback: () => {
    initColumns({
      columns: gridOptions.columns,
    });
  },
});
watch(
  () => handlerColumns,
  val => {
    console.log('val', val);
    gridOptions.columns = val;
  },
  { deep: true }
);

let currentInfo = ref(null);
const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return ![
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
  ].includes(currentInfo.value?.materialCode);
});

// 基期价、市场价为“-
const isChangeAva = row => {
  return Number(row.isDataTaxRate) == 0;
};

const gridOptions = reactive({
  border: true,
  keepSource: true,
  showOverflow: true,
  id: 'toolbar_demo_1',
  height: 500,
  treeConfig: {
    line: true,
    showIcon: true,
    expandAll: true,
    iconOpen: 'vxe-icon-square-minus',
    iconClose: 'vxe-icon-square-plus',
    transform: true,
    rowField: 'sequenceNbr',
    parentField: 'parentId',
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  cellClassName: ({ column }) => {
    let list = ['dispNo', 'name', 'jzFee', 'azFee', 'gzFee', 'qtFee', 'price'];
    if (list.includes(column.field)) {
      return 'disabled-filed';
    }
  },
  editConfig: {
    trigger: 'manual',
    mode: 'cell',
    beforeEditMethod: ({ row, column }) => {
      return true;
    },
  },
  customConfig: {
    storage: true,
  },
  // 编码、名称、规格型号、单位、定额价、市场价、价差、价差合计、数量、三材量、三材系数、三材类别、备注
  columns: [
    {
      field: 'materialCode',
      title: '编码',
      width: 120,
      treeNode: true,
    },
    {
      field: 'materialName',
      title: '名称',
      width: 120,
    },
    {
      field: 'specification',
      title: '规格型号',
    },
    {
      field: 'unit',
      title: '单位',
    },
    {
      field: 'baseJournalPrice',
      title: '不含税基期价',
      visible: projectStore.taxMade == 1,
      slots: { default: 'baseJournalPrice_default' },
    },
    {
      field: 'baseJournalTaxPrice',
      title: '含税基期价',
      visible: projectStore.taxMade == 0,
      slots: { default: 'baseJournalTaxPrice_default' },
    },
    {
      field: 'marketPrice',
      title: '不含税市场价',
      editRender: { autofocus: '.vxe-input--inner' },
      slots: { edit: 'marketPrice_edit', default: 'marketPrice_default' },
    },
    {
      field: 'marketTaxPrice',
      title: '含税市场价',
      editRender: { autofocus: '.vxe-input--inner' },
      slots: { edit: 'marketTaxPrice_edit', default: 'marketTaxPrice_default' },
    },
    {
      field: 'taxRate',
      title: '税率（%）',
      editRender: { autofocus: '.vxe-input--inner' },
      slots: { edit: 'taxRate_edit', default: 'taxRate_default' },
    },
    {
      field: 'priceDifferenc',
      title: '价差',
      slots: { default: 'priceDifferenc_default' },
    },
    {
      field: 'priceDifferencSum',
      title: '价差合计',
      slots: { default: 'priceDifferencSum_default' },
    },
    {
      field: 'totalNumber',
      title: '数量',
      slots: { default: 'totalNumber_default' },
    },
    {
      field: 'scCount',
      title: '三材量',
      slots: { default: 'scCount_default' },
    },
    {
      field: 'transferFactor',
      title: '三材系数',
      editRender: { autofocus: '.vxe-input--inner' },
      slots: { edit: 'transferFactor_edit', default: 'transferFactor_default' },
    },
    {
      field: 'kindSc',
      title: '三材类别',
      editRender: { autofocus: '.vxe-input--inner' },
      slots: { edit: 'kindSc_edit' },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: { autofocus: '.vxe-input--inner' },
      slots: { edit: 'remark_edit' },
      width: 100,
    },
  ],
  data: [],
});

const gridEvents = {
  editClosed({ row, column }) {
    if (!xGrid.value.isUpdateByRow(row, column.field)) return;

    if (projectStore.currentTreeInfo.type == 3) {
      console.log('🚀 ~ filnt ~ upDate:', row);
      updateData(row, column);
    } else {
      row.isChange = true; //标识编辑行
      getSameUnit();
      let upDateList = getPropData();
      if (upDateList && upDateList.length > 0) {
        console.log('upDateList', upDateList, oldData.value);
        projectStore.SET_HUMAN_UPDATA_DATA({
          isEdit: true,
          name: 'unify-humanMachineSummary',
          updataData: upDateList,
        });
      }
    }
  },
  cellClick(cellData) {
    const isEditByRow = xGrid.value.isEditByRow(cellData.row);
    if (!isEditByRow) {
      xGrid.value.clearEdit();
    }
  },

  cellDblclick({ row, column }) {
    if (row.isTitle) return;
    if (column.field == 'marketPrice' || column.field == 'marketTaxPrice') {
      if (
        row.ifLockStandardPrice !== 1 &&
        !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
        Number(row.edit) !== 1 &&
        !deMapFun.isTz(row.materialCode) &&
        !deMapFun.isJxxs(row.materialCode) &&
        !deMapFun.isQtclf(row.materialCode)
      ) {
        xGrid.value.setEditCell(row, column);
      }
    } else {
      xGrid.value.setEditCell(row, column);
    }
  },
  currentChange({ row }) {
    currentInfo.value = row;
  },
};
const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  gridOptions.data &&
    gridOptions.data.map(item => {
      let otherSameUnit = gridOptions.data.filter(
        unit =>
          (projectStore.taxMade == 1 &&
            unit.materialCode == item.materialCode &&
            unit.materialName == item.materialName &&
            // unit.unitId == item.unitId &&
            unit.unit == item.unit &&
            unit.specification == item.specification &&
            Number(unit.baseJournalPrice) == Number(item.baseJournalPrice) &&
            unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
            unit.sequenceNbr !== item.sequenceNbr &&
            Number(unit.marketPrice) !== Number(item.marketPrice)) ||
          (projectStore.taxMade == 0 &&
            unit.materialCode == item.materialCode &&
            unit.materialName == item.materialName &&
            // unit.unitId == item.unitId &&
            unit.unit == item.unit &&
            unit.specification == item.specification &&
            Number(unit.baseJournalTaxPrice) ==
              Number(item.baseJournalTaxPrice) &&
            unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
            unit.sequenceNbr !== item.sequenceNbr &&
            Number(unit.marketTaxPrice) !== Number(item.marketTaxPrice))
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  gridOptions.data &&
    gridOptions.data.map(
      item =>
        (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );
};

let prevTaxRate = ref(0);
// 税率失去焦点的blur事件
const taxRateBlur = row => {
  if (row.taxRate >= 0) {
    row.taxRate = pureNumber0(row.taxRate);
    prevTaxRate.value = row.taxRate;
  } else {
    gridOptions.data.map(item => {
      if (item.sequenceNbr == row.sequenceNbr) {
        item.taxRate = prevTaxRate.value;
      }
    });
    const $table = xGrid.value;
    $table.revertData(currentInfo.value, 'taxRate');
  }
};

// 自动模拟价格修改
const autoChangePrice = (row, filed) => {
  if (!row[filed]) {
    return;
  }
  if (filed == 'marketPrice') {
    // 不含税市场价
    row.total = (row.totalNumber * row[filed]).toFixed(2);
    row.priceDifferenc =
      (Number(row.marketPrice) - Number(row.baseJournalPrice)).toFixed(2) ?? 0;
    row.priceDifferencSum =
      (row.totalNumber * row.priceDifferenc).toFixed(2) ?? 0;
  } else {
    row.totalTax = (row.totalNumber * row[filed]).toFixed(2) ?? 0;
    row.priceDifferenc =
      (Number(row.marketTaxPrice) - Number(row.baseJournalTaxPrice)).toFixed(
        2
      ) ?? 0;
    row.priceDifferencSum =
      (row.totalNumber * row.priceDifferenc).toFixed(2) ?? 0;
  }
};

const getPropData = () => {
  let constructProjectRcjList = [];
  let upDateList = gridOptions.data.filter(item => item.isChange == true);
  upDateList.map(item => {
    let obj = {};
    let same = oldData.value.filter(l => l.sequenceNbr == item.sequenceNbr)[0];
    obj.taxRate = item.taxRate;
    obj.marketTaxPrice = item.marketTaxPrice;
    obj.marketPrice = item.marketPrice;
    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial == 1) {
        obj.totalNumber = item.totalNumber;
      } else {
        obj.totalNumber = '';
      }
    }
    if (item.ifProvisionalEstimate != same.ifProvisionalEstimate) {
      obj.ifProvisionalEstimate = item.ifProvisionalEstimate;
    }
    if (item.ifLockStandardPrice != same.ifLockStandardPrice) {
      obj.ifLockStandardPrice = item.ifLockStandardPrice;
    }
    if (item.kindSc != same.kindSc) {
      obj.kindSc = item.kindSc;
    }
    if (item.transferFactor != same.transferFactor) {
      obj.transferFactor = item.transferFactor;
    }
    if (item.materialName != same.materialName) {
      obj.materialName = item.materialName;
    }
    if (item.remark != same.remark) {
      obj.remark = item.remark;
    }
    if (
      obj.hasOwnProperty('ifDonorMaterial') &&
      !obj.hasOwnProperty('totalNumber')
    ) {
      obj.totalNumber = obj.ifDonorMaterial == 1 ? item.totalNumber : '';
    }
    obj.sequenceNbr = item.sequenceNbr;
    obj.libraryCode = item.libraryCode;
    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const getOldData = () => {
  oldData.value = [];
  gridOptions.data &&
    gridOptions.data.map(item => {
      oldData.value.push({
        sequenceNbr: item.sequenceNbr,
        marketPrice: item.marketPrice,
        marketTaxPrice: item.marketTaxPrice,
        ifDonorMaterial: item.ifDonorMaterial,
        donorMaterialNumber: item.donorMaterialNumber,
        ifProvisionalEstimate: item.ifProvisionalEstimate,
        ifLockStandardPrice: item.ifLockStandardPrice,
      });
    });
  console.log('getOldData', oldData.value);
};
const updateData = (row, column) => {
  let constructProjectRcj = {};
  constructProjectRcj[column.field] = row[column.field];
  let typeData = {};
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    constructProjectRcj.sequenceNbr = row.sequenceNbr;
    typeData['constructProjectRcjList'] = [constructProjectRcj];
  } else {
    typeData['constructProjectRcj'] = constructProjectRcj;
  }
  let apiData = {
    levelType: projectStore.currentTreeInfo.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    ...typeData,
  };

  if (projectStore.currentTreeInfo.type == 2) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
  }

  if (projectStore.currentTreeInfo.type == 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  console.log('🚀 ~ editClosed ~ apiData:', apiData);
  csProject.updateRcjCellect(apiData).then(res => {
    getList();
    xGrid.value.clearEdit();
  });
};
const handleInput = (row, name) => {
  row[name] = pureNumber0(row[name]);
};

const getList = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId:
      projectStore.currentTreeInfo.type == 3
        ? projectStore.currentTreeGroupInfo?.singleId
        : '',
    unitId:
      projectStore.currentTreeInfo.type == 3
        ? projectStore.currentTreeInfo?.id
        : '',
  };

  if (projectStore.currentTreeInfo.type == 2) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
  }

  if (projectStore.currentTreeInfo.type == 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
  }

  csProject.getScList(apiData).then(res => {
    console.log('🚀 ~ csProject.getGljAdjustList ~ res:', res);
    gridOptions.data = res.result.map((i, k) => {
      i.materialName = i.name || i.materialName;
      i.index = k;
      i.isTitle = ['0', '1', '2', '3', '4', '5'].includes(i.code);
      autoChangePrice(
        i,
        projectStore.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice'
      );
      return i;
    });
    if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
      setTimeout(() => {
        getOldData();
        getSameUnit();
      }, 200);
    }
    nextTick(() => {
      xGrid.value.setAllTreeExpand(true);
    });
  });
};
onMounted(() => {
  getList();
  initColumns({
    columns: gridOptions.columns,
  });
});

const handleDiffAmount = (data = '') => {
  if (!data) {
    return '';
  }
  return +data > 0 ? 'redColor' : 'greenColor';
};

onActivated(() => {
  getList();
  initColumns({
    columns: gridOptions.columns,
  });
});

defineExpose({
  getTableData: getList,
  handlerColumns,
  updateColumns,
  getDefaultColumns,
});
</script>

<style lang="scss" scoped>
.disabled-filed {
  background-color: #f3f3f3;
}

.table-content {
  // height: calc(65%);
  height: 100%;
  //user-select: none;
  .redColor {
    color: red;
  }
  .greenColor {
    color: green;
  }
  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }

  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
}

.table-content ::v-deep(.vxe-table--render-default .vxe-tree--node-btn) {
  color: #87b2f2 !important;
}
</style>
