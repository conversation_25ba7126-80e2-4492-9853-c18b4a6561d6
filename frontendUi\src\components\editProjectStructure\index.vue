<!--
 * @Descripttion: 编辑项目结构
 * @Author: renmingming
 * @Date: 2023-05-22 15:36:24
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-22 09:58:41
-->
<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="props.visible"
    :title="dialogTitle"
    width="auto"
    @cancel="cancel"
    @close="cancel"
    :loadingModal="loadingModal"
  >
    <div class="edit-wrap">
      <div
        class="head"
        v-if="props.config.showMenu"
      >
        <a-button
          type="primary"
          :disabled="!item.isValid"
          v-for="item of menus"
          :key="item.menuLevel"
          @click="menuClick(item.menuLevel)"
        >{{ item.name }}</a-button>
      </div>
      <p
        class="title"
        v-if="props.config.type==='importExcel'"
      >
        导入到{{importPath}}
      </p>
      <div class="table-wrap">
        <vxe-table
          ref="vexTable"
          border="full"
          height="100%"
          align="center"
          :loading="loading"
          show-overflow="tooltip"
          :scroll-y="{ gt: 0 }"
          :column-config="{ resizable: true }"
          :row-config="{ isCurrent: true, keyField: 'id' }"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'cell',
            showIcon: false,
            showStatus: false,
          }"
          :tree-config="props.config.type!=='importExcel'?{
              children: 'children',
              expandAll: true,
              transform: true,
            }:{}"
          :data="treeList"
          :row-class-name="
            ({ row }) => {
              return `level-${row.levelType}`;
            }
          "
          @current-change="currentChangeEvent"
        >
          <vxe-column
            width="100"
            tree-node
            v-if=" props.config.type!=='importExcel'"
          ></vxe-column>
          <vxe-column
            type="seq"
            width="70"
            title="序号"
            v-if=" props.config.type==='importExcel'"
          ></vxe-column>
          <vxe-column
            field="name"
            title="名称"
            :edit-render="{
              autofocus: '.my-input',
              autoselect: true,
            }"
          >
            <template #edit="{ row }">
              <a-input
                :placeholder="getPlaceholder(row.levelType, row)"
                v-model:value="row.name"
                type="text"
                ref="editInputRef"
                class="my-input"
                @change="inputChange(row, $event)"
                @blur="changeRepeat(row, row.name)"
              ></a-input>
            </template>
          </vxe-column>
          <vxe-column
            field="deStandardId"
            title="定额标准"
            v-if="store.type === 'ys' && store.deType === '22'"
          >
            <template #default="{ row }">
              <a-select
                v-if="row.levelType === 3"
                v-model:value="row.deStandardId"
                placeholder="请选择定额标准"
                :options="deTypeRationList"
                :disabled="!row.isNew && !props.config.isEdit"
                @change="deTypeChange(row)"
                :fieldNames="{
                    label: 'name',
                    value: 'sequenceNbr',
                  }"
              >
              </a-select>
            </template>
          </vxe-column>
          <vxe-column
            field="constructMajorType"
            title="工程专业"
          >
            <template #default="{ row }">
              <!-- :open="true" -->
              <a-cascader
                v-if="
                    store.type === 'ys' &&row.levelType === 3 &&
                    engineerMajorListYS(row.deStandardId).length > 0
                  "
                :disabled="!row.isNew && !props.config.isEdit"
                v-model:value="row.constructMajorType"
                placeholder="请选择工程专业"
                :options="engineerMajorListYS(row.deStandardId)"
                @change="constructMajorTypeDropdownList(row)"
              />
              <!-- <a-cascader
                v-if="row.levelType === 3 && engineerMajorList.length > 0"
                :disabled="!row.isNew && !props.config.isEdit"
                v-model:value="row.constructMajorType"
                placeholder="请选择工程专业"
                :options="engineerMajorList"
              /> -->
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="footer-btn-list">
        <a-button
          @click="cancel"
          v-if=" props.config.type!=='importExcel'"
        >取消</a-button>
        <a-button
          @click="afterSave(false, 'later')"
          :loading="afterSubmitLoading"
          :disabled="submitLoading"
          v-if="!props.config.showMenu && props.config.type!=='importExcel'"
        >稍后设置</a-button>
        <a-button
          type="primary"
          :disabled="afterSubmitLoading"
          @click="handleOk"
          :loading="submitLoading"
        >确定</a-button>
      </div>

      <info-modal
        v-model:infoVisible="showInfoStatus"
        infoText="当前存在未设置专业的单位工程，是否确认提交？"
        descText="您可在后续工作台页面进行设置"
        :isSureModal="false"
        @updateCurrentInfo="afterSave()"
      ></info-modal>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, watch, nextTick, toRaw, onMounted } from 'vue';
import xeUtils from 'xe-utils';
import { ConstructMenuOperator } from './ConstructMenuOperator';
import csProject from '@/api/csProject';
import {
  constructLevelTreeStructureList,
  // getTemplateData,
  // getCostSummaryTemplate,
} from '@/api/csProject';
import { inputName } from '@/utils/index';
import { useRoute } from 'vue-router';
import { proModelStore } from '@/store/proModel.js';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();

const ModelStore = proModelStore();

const showInfoStatus = ref(false);

const emit = defineEmits(['update:visible', 'success', 'editClose']);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object,
    default: {
      showMenu: true, //隐藏编辑按钮
      constructObj: null, //项目树
      isEdit: false, // 是否可以编辑工程专业
      type: 'import', // 区分工作台导入import   预算控制台导入excel--importExcel
    },
  },
  majorList: {
    type: Array,
    default: () => [],
  },
  qdStandardId: {
    type: String,
    default: null,
  },
});

let treeList = ref([]);
const route = useRoute();

const getTreeList = async () => {
  let list = null;
  if (props.config.constructObj) {
    // 直接传递树过来的
    list = props.config.constructObj;
    if (props.config.type === 'importExcel') {
      treeList.value = props.config.constructObj;
      // init();
      // return;
    }
  } else {
    const res = await constructLevelTreeStructureList(
      route.query.constructSequenceNbr
    );
    if (res.status === 200) {
      list = res.result;
    } else {
      message.error(res.message);
      return;
    }
  }
  console.log(list);
  // debugger;
  let storeDeStandardId = deTypeRationList.value.find(
    item => item.releaseYear === store.deType
  )?.sequenceNbr;
  console.log(
    engineerMajor.value[getDeType(props.qdStandardId || storeDeStandardId)]
  );
  let tar;
  treeList.value = list.map(i => {
    i.isNew = !i.constructMajorType;
    i.constructMajorType = i.constructMajorType
      ? i.secondInstallationProjectName
        ? [i.constructMajorType, i.secondInstallationProjectName]
        : [i.constructMajorType]
      : [];
    if (i.constructMajorType?.length === 0 && i.defaultMajorType) {
      tar = engineerMajor.value[
        getDeType(props.qdStandardId || storeDeStandardId)
      ].find(
        a => a.value === i.constructMajorType || a.value === i.defaultMajorType
      );
      i.constructMajorType = [i.defaultMajorType, tar?.children[0].value];
    }
    i.oldName = i.name;
    if (i.defaultMajorType)
      i.deStandardId = props.qdStandardId || storeDeStandardId;
    if (!i.deStandardId && i.isNew && props.qdStandardId)
      i.deStandardId = props.qdStandardId;
    return i;
  });
  init();
};

const inputChange = (row, e) => {
  const value = inputName(e.target.value);
  let handleValue = value;
  if (xeUtils.trim(value).length > 50) {
    handleValue = value.slice(0, 50);
    message.warn('名称过长，请输入50个字符范围内');
  }
  row.name = handleValue;
};
const changeRepeat = (row, name) => {
  if (!name.trim()) return;
  if (
    isRepeat(row, name) ||
    (props.config.type === 'importExcel' && isRepeatImportExcel(row, name))
  ) {
    row.name = row.oldName; //重复恢复原来的名字
    message.error('同级名称不可重复，请重新输入名称');
    return;
  }
  row.oldName = row.name;
};
const isRepeat = (row, name) => {
  //判断统计添加的单位/单项名称是否重复出现
  // debugger;
  // console.log('row, name', row, name);
  let flag = treeList.value.some(
    item =>
      (item.levelType === row.levelType ||
        item.levelType === row.levelType - 0.5 ||
        item.levelType === row.levelType + 0.5) &&
      item.parentId === row.parentId &&
      item.id !== row.id &&
      item.name.trim() === name.trim()
  );
  return flag; //true--名称重复，flse---名称不重复
};
const isRepeatImportExcel = async (row, name) => {
  //判断统计添加的单位/单项名称是否重复出现
  let tarList = [];
  if (store.currentTreeInfo.levelType === 3) {
    const asideTreeList = await constructLevelTreeStructureList(
      store.currentTreeGroupInfo?.constructId
    );
    tarList = asideTreeList.result.filter(
      a => a.parentId === store.currentTreeInfo.parentId
    );
  } else {
    tarList = store.currentTreeInfo.children;
  }
  let flag = treeList.value.some(
    item => item.levelType === row.levelType && item.name.trim() === name.trim()
  );
  return flag; //true--名称重复，flse---名称不重复
};
const getPlaceholder = (levelType, row) => {
  // console.log(levelType, row);
  // debugger;
  const map = {
    1: '项目工程',
    2: '单项工程',
    3: '单位工程',
    2.5: '子单项工程',
  };
  // if (levelType === 2.5) {
  //   row.levelType = 2; //添加子单项工程，将子单项的levelType改为2即可
  // }
  // if(editInputRef.value){
  //   editInputRef.value.focus()
  // }
  return `请输入${map[levelType]}名称`;
};

let cMenuOper = new ConstructMenuOperator();
let menus = ref(cMenuOper.menusJson().slice(0, 4));
console.log('cMenuOper.menusJson()', menus);
let loading = ref(false);
let submitLoading = ref(false);
let afterSubmitLoading = ref(false);
let dialogTitle = ref('编辑项目结构');

let vexTable = ref();

let insertRowIndex = ref(0);

const editInputRef = ref(null);

// 单选
const currentChangeEvent = ({ row, $rowIndex }) => {
  if (props.config.type === 'importExcel') return;
  if (!row?.parent || !Object.keys(row.parent).length) {
    let parent = vexTable.value?.getParentRow(row) || {};
    row.parent = parent;
  }
  insertRowIndex.value = $rowIndex;
  cMenuOper.resetMenus(row, treeList.value);
  menus.value = cMenuOper.menusJson().slice(0, 4);
};
const resetInsertRowIndex = () => {
  //inserRowIndex重置处理
  const currentRow = vexTable.value.getCurrentRecord();
  insertRowIndex.value = treeList.value.findIndex(
    item => item.id === currentRow.id
  );
  console.log('重置后的Index', insertRowIndex.value, currentRow);
};
// 添加单项单位
const menuClick = menuLevel => {
  // console.log('menuLevel', menuLevel);
  vexTable.value.clearEdit();
  cMenuOper.addLocation(
    vexTable,
    treeList.value,
    menuLevel,
    insertRowIndex.value
  );

  if ([9999].includes(menuLevel)) {
    // 删除操作
    const clickNode = vexTable.value?.getCurrentRecord();
    cMenuOper.removeData(clickNode).then(res => {
      if (res) {
        if (store.type === 'ys') {
          resetInsertRowIndex();
        }
        handleMenus();
      }
    });
    return;
  }
  setTimeout(() => {
    if (store.type === 'ys') {
      resetInsertRowIndex();
      if (menuLevel === 3) {
        let newInfo = treeList.value[insertRowIndex.value];
        newInfo.deStandardId = deTypeRationList.value.find(
          item => item.releaseYear === store.deType
        )?.sequenceNbr;
        treeList.value.splice(insertRowIndex.value, 1, newInfo);
        console.log(insertRowIndex.value, newInfo);
      }
    }
  }, 100);
  handleMenus();
};

//更新按钮状态
const handleMenus = () => {
  if (cMenuOper.newRecord) {
    cMenuOper.resetMenus(cMenuOper.newRecord, treeList.value);
    menus.value = cMenuOper.menusJson().slice(0, 4);
  }
};
/**
 *
 * @param {*} isComplete 单位是否完整
 */
const afterSave = (isComplete = false, fr = '') => {
  if (!isComplete && !checkData(true, false)) {
    return message.error('请补全名称后提交');
  }
  showInfoStatus.value = false;
  save('import', isComplete, fr);
};

// 点击其他过来的
const beforeSet = () => {
  if (!checkData(true, false)) {
    message.error('请补全名称后提交');
    return;
  }

  if (!checkData()) {
    showInfoStatus.value = true;
    return;
  }
  afterSave(true);
};

const handleOk = () => {
  if (!props.config.showMenu && props.config.type !== 'importExcel') {
    // 首页导入项目过来的
    beforeSet();
    return;
  }

  if (!checkData()) {
    return message.error('请补全信息后提交');
  }

  save();
};
const getImportExcelPostdata = list => {
  //导入excel接口传参
  let postList = [];
  list.map(a => {
    let tar = props.config.constructObj.find(b => b.id === a.id);
    a.children = JSON.parse(JSON.stringify(tar.children));
    a.directory = tar.directory;
    a.fileType = tar.fileType;
    a.filePath = tar.filePath;
    a.level = tar.level;
    a.status = tar.status;
    postList.push(a);
  });
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    units: list,
  };
  return postData;
};
/**
 * 保存数据
 * @param {*} type edit   编辑项目结构
 *                 import   导入过来的
 */
let loadingModal = ref(false);
const save = async (type = 'edit', isComplete = false, fr = '') => {
  if (submitLoading.value || afterSubmitLoading.value) return;
  const list = await handleList();
  const isUnitComplete = isComplete || (await checkData());
  const apiName = props.config.constructObj
    ? props.config.type === 'importExcel'
      ? 'importUnits'
      : 'editImportProjectAfter'
    : 'postEditStructure';
  let postData = xeUtils.toArrayTree(list)[0];
  if (props.config.type === 'importExcel') {
    postData = getImportExcelPostdata(list);
  }
  if (fr === 'later') {
    postData.laterSet = 1;
    afterSubmitLoading.value = true;
  } else {
    submitLoading.value = true;
  }
  console.log('🚀保存的数据:', type, postData, treeList.value, 'list', list);
  loadingModal.value = true;
  setTimeout(() => {
    csProject[apiName](postData)
      .then(res => {
        if (type === 'import') {
          // 设置是否是完整的结构
          ModelStore.setUnitStatus(isUnitComplete);
        }
        ModelStore.setUpdateProjectStructure(true);
        if (apiName == 'editImportProjectAfter') {
          if (res.result) {
            message.success('保存成功');
            emit('success');
          }
          cancel(res.result ? 'noBack' : 'back');
        } else {
          emit('success');
          cancel();
        }
      })
      .finally(() => {
        submitLoading.value = false;
        afterSubmitLoading.value = false;
        loadingModal.value = false;
        handleStore();
      });
  }, 0);
};

// 如果编辑弹窗还是自动打开，则关闭
const handleStore = () => {
  if (ModelStore.openEditModalStatus) {
    ModelStore.onEditModal(false);
  }
};

// 处理chenglist
const handleList = () => {
  return toRaw(treeList.value).map(
    ({
      id,
      parentId,
      name,
      levelType,
      constructMajorType,
      children,
      deStandardId,
      libraryCode,
    }) => {
      let unitAfterSet = false;
      if (
        levelType === ConstructMenuOperator.unitLevel &&
        (!constructMajorType || !constructMajorType[0])
      ) {
        unitAfterSet = true;
      }
      if (levelType === ConstructMenuOperator.singleChildLevel) {
        levelType = 2;
      }
      return {
        id,
        parentId,
        name,
        levelType,
        constructMajorType: constructMajorType ? constructMajorType[0] : null,
        secondInstallationProjectName: constructMajorType
          ? constructMajorType[1]
          : null,
        children,
        unitAfterSet,
        deStandardId,
        libraryCode,
      };
    }
  );
};

/**
 *
 * @param {*} list 平铺的数组树
 */
const handlePost = list => {
  let initData = {};
  list.map(i => {
    delete i.parent;
    delete i._X_ROW_CHILD;
    i.children = null;

    if (!initData[i.id]) {
      initData[i.id] = toRaw(i);
    }
  });
  return Object.values(initData);
};

/**
 *
 * @param {*} checkName 是否检查名字
 * @param {*} checkType 是否检查专业
 */
const checkData = (checkName = true, checkType = true) => {
  for (let item of treeList.value) {
    if (!xeUtils.trim(item.name).length && checkName) {
      return false;
    }
    if (
      item.levelType === ConstructMenuOperator.unitLevel &&
      (!item.constructMajorType || !item.constructMajorType[0]) &&
      checkType
    ) {
      return false;
    }
    // if (
    //   store.type === 'ys' &&
    //   Number(store.deType) === 22 &&
    //   item.levelType === ConstructMenuOperator.unitLevel &&
    //   !item.deStandardId
    // ) {
    //   //22定额需要设置单位项的定额标准
    //   return false;
    // }
  }
  return true;
};
// 是否返回上一页
const cancel = (type = 'back') => {
  // 处理全局打开编辑弹窗状态
  handleStore();
  emit('editClose', type);
  emit('update:visible');
};
const init = () => {
  nextTick(() => {
    setTimeout(() => {
      vexTable.value.setCurrentRow(treeList.value[0]);
      vexTable.value.setAllTreeExpand(true);
      currentChangeEvent({ row: treeList.value[0] });
    }, 10);
  });
};
let importPath = ref('');
const getImportPath = async () => {
  if (props.config.type !== 'importExcel') return;
  if (store.currentTreeInfo.levelType !== 1) {
    //查找单项名称
    let singleItem;
    const asideTreeList = await constructLevelTreeStructureList(
      store.currentTreeGroupInfo?.constructId
    );
    singleItem =
      store.currentTreeInfo.levelType === 3
        ? asideTreeList.result.find(
            a => a.id === store.currentTreeInfo.parentId
          )
        : store.currentTreeInfo;
    let list = getParentItem(singleItem, asideTreeList.result, []);
    importPath.value = list.join('/');
    console.log('getImportPath-list', list, importPath.value);
  } else {
    importPath.value = store.currentTreeInfo.name;
  }
};
const getParentItem = (tar, treeList, newList) => {
  //获取目标平铺树结构最外层单项父级
  newList.unshift(tar.name);
  let parent = treeList?.find(i => i.id === tar.parentId);
  parent ? getParentItem(parent, treeList, newList) : '';
  return newList;
};
onMounted(() => {
  // queryEngineerMajorList();
  getImportPath();
});
const getDeType = deStandardId => {
  return deTypeRationList.value.find(item => item.sequenceNbr === deStandardId)
    ?.releaseYear;
};
let engineerMajor = ref({});
const engineerMajorListYS = deStandardId => {
  const deType = getDeType(deStandardId);
  return engineerMajor.value[deType] || [];
};
const deTypeChange = row => {
  row.constructMajorType = '';
};
let engineerMajorList = ref([]);
//获取专业列表下拉列表
const queryEngineerMajorList = async deType => {
  if (deType && engineerMajor.value[deType]) return;
  const response = await csProject.getEngineerMajorList({ deStandard: deType });
  if (response.status === 200) {
    engineerMajor.value[deType] = [];
    response.result.map(async (item, index) => {
      let obj = {
        label: item.unitProjectName,
        value: item.unitProjectName,
      };
      obj.children = await getSecondProName(item.unitProjectName);
      engineerMajor.value[deType].push(obj);
    });
    console.log(engineerMajor.value, '专业列表', deType);
  }
};
//获取专业列表下拉列表
const queryEngineerMajorListYS = async deType => {
  if (deType && engineerMajor.value[deType]) return;
  const response = await csProject.getEngineerMajorList({ deStandard: deType });
  if (response.status === 200) {
    engineerMajor.value[deType] = [];
    for (let item of response.result) {
      let obj = {
        label: item.unitProjectName,
        value: item.unitProjectName,
      };
      obj.children = await getSecondProName(item.unitProjectName, deType);
      engineerMajor.value[deType].push(obj);
    }
    console.log(engineerMajor.value, '专业列表', deType, engineerMajor.value);
  }
};
const getSecondProName = async (firstName, deType) => {
  let childrenList = [];
  // vexTable.value.getCurrentRecord()
  console.log('getSecondProName', deType, {
    constructMajorType: firstName,
    deStandard: deType,
  });
  const list = await csProject.getSecondInstallationProjectName({
    constructMajorType: firstName,
    deStandard: deType,
  });

  if (list.status === 200) {
    list.result.map(i => {
      childrenList.push({
        label: i.cslbName,
        value: i.cslbName,
      });
    });
  }
  return childrenList;
};
watch(
  () => [props.visible, props.majorList],
  async ([val, oldVal], [listVal, listOldVal]) => {
    treeList.value = [];
    if (val) {
      if (!store.type) store.type = 'ys'; //store.type没有值默认为ys-预算
      console.log(store.deType, store.type);
      if (store.type === 'ys') {
        await queryRationList();
        // 多个业务调用，没必要外部传递列表
        for (let item of deTypeRationList.value) {
          await queryEngineerMajorListYS(item.releaseYear);
        }
      } else {
        await queryEngineerMajorList();
      }
      getTreeList();
      getImportPath();
    }

    // 多个业务调用，没必要外部传递列表
    // queryEngineerMajorList();
    // if (!listVal.length) {

    // } else {
    // 	engineerMajorList.value = props.majorList;
    // }
  }
);
//获取定额标准下拉列表
let deTypeRationList = ref([]);
const queryRationList = async () => {
  const postData = {
    areaId: 130000,
    type: '2',
  };
  const result = await csProject.quotaStandardDropdownList(postData);
  if (result.status === 200) {
    deTypeRationList.value = result.result || [];
  }
};
const constructMajorTypeDropdownList = row => {
  let apiData = {
    deType: getDeType(row.deStandardId),
    constructMajorType: row.constructMajorType[0],
  };
  csProject.getMainDeLibrary(apiData).then(res => {
    if (res.status === 200) {
      res.result.forEach(item => {
        if (item.defaultDeFlag === 1) {
          row.libraryCode = item.libraryCode;
        }
      });
    }
  });
};
</script>
<style lang="scss" scoped>
.edit-wrap {
  width: 60vw;
  max-width: 800px;
  height: 60vh;
  display: flex;
  flex-direction: column;
  .title {
    margin: 0;
  }
}

.head {
  .ant-btn {
    margin-right: 10px;
  }
}
.table-wrap {
  flex: 1;
  margin: 15px 0 30px 0;
  overflow: hidden;
}
.table-wrap :deep(.vxe-table) {
  .level-1 {
    background-color: #d7d7d7;
  }
  .level-2 {
    background-color: #ececec;
  }
  .level-3 {
    background-color: #e9eefa;
  }
  .ant-select-selector {
    border: none !important;
    background-color: transparent !important;
  }
  .vxe-body--column,
  .vxe-header--column,
  .vxe-footer--column {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  .row--current {
    background-color: #a6c3fa;
    .ant-select,
    .ant-select-selection-placeholder,
    .ant-select-arrow {
      color: var(--primary-color);
    }
  }
}

:deep(.ant-select-clear) {
  border-radius: 50%;
}
</style>
