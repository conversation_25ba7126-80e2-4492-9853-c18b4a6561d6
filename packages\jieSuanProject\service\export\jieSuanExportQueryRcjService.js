const {ConvertUtil} = require("../../../../electron/utils/ConvertUtils");

const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {Service} = require("../../../../core");
class JieSuanExportQueryRcjService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 表1-8 单位工程人材机汇总表   常用报表
     * @param args
     * @returns {Promise<*|unknown|any[]|ResponseData>}
     */
    async getUnitCgRcjHzb(args){
        args.type = 2;
        args.kind = 0;
        let unitRcjQuery = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(unitRcjQuery)){
            for (let i = 0; i <unitRcjQuery.length ; i++) {
                unitRcjQuery[i].dispNo = i+1;
            }
        }

        return unitRcjQuery;
    }

    /**
     * 表1-9 工程议价材料表 常用报表
     * @param args
     * @returns {Promise<void>}
     */
    async getUnitCgGcclyjb(args){

        args.type = 2;
        //人工
        args.kind = 1;
        let rg =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(rg)){
            for (let i = 0; i <rg.length ; i++) {
                rg[i].dispNo = i+1;
            }
        }

        //材料
        args.kind = 2;
        let cl = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(cl)){
            for (let i = 0; i <cl.length ; i++) {
                cl[i].dispNo = i+1;
            }
        }

        //机械
        args.kind = 3;
        let jx = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(jx)){
            for (let i = 0; i <jx.length ; i++) {
                jx[i].dispNo = i+1;
            }
        }

        //暂估
        args.kind = 8;
        let zg = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(zg)){
            for (let i = 0; i <zg.length ; i++) {
                zg[i].dispNo = i+1;
            }
        }

        let array = new Array();
        let rgH = {
            dispNo:"一",
            materialName:"人工价差"};
        let clH = {
            dispNo:"二",
            materialName:"材料(主材，设备)价差"};
        let jxH = {
            dispNo:"三",
            materialName:"暂估材料价差"};
        let zgH = {
            dispNo:"四",
            materialName:"机械价差"};
        array.push(rgH);
        array.push(...rg);
        array.push(clH);
        array.push(...cl);
        array.push(jxH);
        array.push(...jx);
        array.push(zgH);
        array.push(...zg);


        return  array;
    }


    /**
     * 表1-10 人材机调整明细表-1 常用报表
     * @returns {Promise<void>}
     */
    async getUnitRcjtzmx1(args){

        args.type = 2;
        //人工
        args.kind = 1;
        let rg =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(rg)){
            for (let i = 0; i <rg.length ; i++) {
                rg[i].dispNo = i+1;
            }
        }

        //材料
        args.kind = 2;
        let cl = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(cl)){
            for (let i = 0; i <cl.length ; i++) {
                cl[i].dispNo = i+1;
            }
        }

        //机械
        args.kind = 3;
        let jx = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(jx)){
            for (let i = 0; i <jx.length ; i++) {
                jx[i].dispNo = i+1;
            }
        }

        //暂估
        args.kind = 8;
        let zg = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(zg)){
            for (let i = 0; i <zg.length ; i++) {
                zg[i].dispNo = i+1;
            }
        }

        let array = new Array();
        let rgH = {
            dispNo:"一",
            materialName:"人工"};
        let clH = {
            dispNo:"二",
            materialName:"材料(主材，设备)"};
        let jxH = {
            dispNo:"三",
            materialName:"暂估材料"};
        let zgH = {
            dispNo:"四",
            materialName:"机械"};
        array.push(rgH);
        array.push(...rg);
        array.push(clH);
        array.push(...cl);
        array.push(jxH);
        array.push(...jx);
        array.push(zgH);
        array.push(...zg);


        return  array;

    }


    /**
     * 表1-11 人材机调整明细表-2 常用报表
     * @returns {Promise<void>}
     */
    async getUnitRcjtzmx2(args){
        let unitRcjtzmx1 = await this.getUnitRcjtzmx1(args);
        return unitRcjtzmx1;

    }


    /**
     * 表1-12 主材汇总表 常用报表
     * @returns {Promise<void>}
     */
    async getUnitZchzb(args){

        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.kind == 5);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }



        return all;
    }

    /**
     * 表1-13 甲方供应材料表
     * @returns {Promise<void>}
     */
    async getUnitJgclb(args){

        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.ifDonorMaterial == 1);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }




        return all;
    }


    /**
     * 表1-14 甲供材料汇总表
     * @returns {Promise<void>}
     */
    async getUnitJgclhzb(args){

        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.ifDonorMaterial == 1);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }




        return all;
    }


    /**
     * 表1-12 发包人提供材料和工程设备一览表
     * @returns {Promise<void>}
     */
    async getUnitFbrtgclhgcsbb(args){

        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.ifDonorMaterial == 1);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }



        return all;

    }


    /**
     * 表1-15 材料、机械、设备增值税计算表
     * @returns {Promise<void>}
     */
    async getUnitClJxSbzzsb(args){

        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.kind != 1);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }

        return all;
    }


    /**
     * 表1-1 单位工程人材机汇总表 常用报表 合同外
     * @returns {Promise<void>}
     */
    async getUnitRcjhzbhtw(args){

        args.type = 2;
        //人工
        args.kind = 1;
        let rg =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(rg)){
            for (let i = 0; i <rg.length ; i++) {
                rg[i].dispNo = i+1;
            }
        }

        //材料
        args.kind = 2;
        let cl = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(cl)){
            for (let i = 0; i <cl.length ; i++) {
                cl[i].dispNo = i+1;
            }
        }

        //机械
        args.kind = 3;
        let jx = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(jx)){
            for (let i = 0; i <jx.length ; i++) {
                jx[i].dispNo = i+1;
            }
        }

        //暂估
        args.kind = 8;
        let zg = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(zg)){
            for (let i = 0; i <zg.length ; i++) {
                zg[i].dispNo = i+1;
            }
        }

        let array = new Array();
        let rgH = {
            dispNo:"一",
            materialName:"人工"};
        let clH = {
            dispNo:"二",
            materialName:"材料(主材，设备)"};
        let jxH = {
            dispNo:"三",
            materialName:"暂估材料"};
        let zgH = {
            dispNo:"四",
            materialName:"机械"};
        array.push(rgH);
        array.push(...rg);
        array.push(clH);
        array.push(...cl);
        array.push(jxH);
        array.push(...jx);
        array.push(zgH);
        array.push(...zg);


        return  array;

    }


    /**
     * 表1-2 单位工程人材机价差汇总表 合同外
     * @returns {Promise<void>}
     */
    async getUnitRcjjchzbhtw(args){

        args.type = 2;
        //人工
        args.kind = 1;
        let rg =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(rg)){
            for (let i = 0; i <rg.length ; i++) {
                rg[i].dispNo = i+1;
            }
        }

        //材料
        args.kind = 2;
        let cl = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(cl)){
            for (let i = 0; i <cl.length ; i++) {
                cl[i].dispNo = i+1;
            }
        }

        //机械
        args.kind = 3;
        let jx = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(jx)){
            for (let i = 0; i <jx.length ; i++) {
                jx[i].dispNo = i+1;
            }
        }

        //暂估
        /*args.kind = 8;
        let zg = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(zg)){
            for (let i = 0; i <zg.length ; i++) {
                zg[i].dispNo = i+1;
            }
        }*/

        let array = new Array();
        let rgH = {
            dispNo:"一",
            materialName:"人工类别"};
        let clH = {
            dispNo:"二",
            materialName:"材料(主材，设备)类别"};
        let jxH = {
            dispNo:"三",
            materialName:"机械类别"};
        array.push(rgH);
        array.push(...rg);
        array.push(clH);
        array.push(...cl);
        array.push(jxH);
        array.push(...jx);
        /*array.push(zgH);
        array.push(...zg);*/
        return  array;

    }


    /**
     * 表1-3 人材机价差调整表
     * @returns {Promise<void>}
     */
    async getUnitRcjcjtzbhtw(args){

        args.type = 2;
        //人工
        args.kind = 1;
        let rg =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(rg)){
            for (let i = 0; i <rg.length ; i++) {
                rg[i].dispNo = i+1;
            }
        }

        //材料
        args.kind = 2;
        let cl = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(cl)){
            for (let i = 0; i <cl.length ; i++) {
                cl[i].dispNo = i+1;
            }
        }

        //机械
        args.kind = 3;
        let jx = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        if (!ObjectUtils.isEmpty(jx)){
            for (let i = 0; i <jx.length ; i++) {
                jx[i].dispNo = i+1;
            }
        }


        let array = new Array();
        let rgH = {
            dispNo:"一",
            materialName:"人工调差"};
        let clH = {
            dispNo:"二",
            materialName:"材料调差"};
        let jxH = {
            dispNo:"三",
            materialName:"机械调差"};

        array.push(rgH);
        array.push(...rg);
        array.push(clH);
        array.push(...cl);
        array.push(jxH);
        array.push(...jx);

        return  array;


    }


    /**
     * 表1-4 主材汇总表
     * @returns {Promise<void>}
     */
    async getUnitZchzbHtw(args){


        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.kind == 5);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }


        return all;


    }





    /**
     * 表1-12 材料、机械、设备增值税计算表 合同外
     * @returns {Promise<void>}
     */
    async getUnitClJxSbzzsbHtw(args){

        args.type = 2;
        //所有
        args.kind = 0;

        let all =  await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
        all = all.filter(i=>i.kind != 1);
        if (!ObjectUtils.isEmpty(all)){
            for (let i = 0; i <all.length ; i++) {
                all[i].dispNo = i+1;
            }
        }

        return all;

    }














}

JieSuanExportQueryRcjService.toString = () => '[class JieSuanExportQueryRcjService]';
module.exports = JieSuanExportQueryRcjService;
