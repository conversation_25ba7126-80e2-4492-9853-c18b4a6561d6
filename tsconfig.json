//{
//  "compilerOptions": {
//    "target": "es5",
//    "module": "commonjs",
////    "outDir": "electron",
////    "rootDir": "electron",
//    "strict": true,
//    "esModuleInterop": true,
//    "experimentalDecorators":true
//  },
//  "include": ["electron/**/*.ts"],
//  "exclude": ["node_modules"]
//}
{
  "compilerOptions": {
    "skipLibCheck": true,
    "target": "ES2019",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "module": "commonjs",
    "noImplicitAny": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "*": ["node_modules/*"]
    }
  },
  "include": [
    "*",
    "electron/**/*.ts",
    "packages/**/*.ts",
    "electron/**/*.ts",
    "packages/**/*.ts",
    "packages/PreliminaryEstimate/models/*.ts",
    "packages/gongLiaoJiProject/models/*.ts"
  ],
  "exclude": ["node_modules"]
}
