const RuleHandler = require("./ruleHandler");

class Kind3RuleHandler extends RuleHandler {

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = `${this.rule.relation}:${this.rule.selectedRule}`;
    }

    mathAfterCalculation(numStr){
        return this.ctx.conversionService.mathAfterCalculation(numStr);
    }

    deCodeUpdateInfo() {
        let redSubArray = [];
        for (let handler of this.rule.mathHandlers) {
            let math = handler.mathItem.math;
            let parseMath = handler.mathItem.parseMath;
            let result = this.mathAfterCalculation(parseMath);
            redSubArray.push(math.replace(parseMath, result));
        }
        return {redStr: "["+redSubArray.join(",")+"]", blackStr: null}
    }

    deNameUpdateInfo(rule) {
        return `${rule.relation}:${rule.selectedRule}`;
    }
}
module.exports = Kind3RuleHandler;
