import {GljModel} from "./GljModel";
import {unit} from "mathjs";
import {Column} from "typeorm";

/**
 * 人材机
 */
export class GljRcj extends GljModel {
    public level1: string; // 层级一

    public level2: string; // 层级二

    public level3: string; // 层级三

    public level4: string; // 层级四

    public level5: string; // 层级五

    public level6: string; // 层级六

    public level7: string; // 层级七

    public materialCode: string; // 编码

    public kind: number; // kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)

    public materialName: string; // 名称

    public specification: string; // 规格及型号

    public unit: string; // 单位

    public baseJournalPrice: number;

    public baseJournalTaxPrice: number;

    public marketPrice: number;

    public marketTaxPrice: number;

    public libraryCode: string; // 定额册编码

    public levelMark: string; // 是否下沉标识（2：下沉机械；1：下沉配比；0：无需下沉）

    public sortNo: number; // 排序序号

    public isChangeAva: number; // 0 不可更改（既基价为-），1 可更改

    public isFyrcj: number; // 是否费用人材机(0:是，1否)

    public kindSc: string; // 三材分类：钢材、水泥、商砼、钢筋、木材、商品砂浆

    public transferFactor: string; // 三材转化系数

    public taxRate: number; // 税率

    public isDataTaxRate: number; // 税率是否为数值（0 为 -，1为数值,2为空）

    public taxRemoval: number;

    public price: number; // 定额价格

    public agencyCode: string;

    public productCode: string;


    public resQty: number; //材料消耗量

    public initResQty: number; // 原始含量

    public changeResQty: number; //变更前一次的消耗量，临时删除取消时用到

    public scCount: number; // '三材量'

    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string, level1: string, level2: string, level3: string, level4: string, level5: string, level6: string, level7: string, materialCode: string, kind: number, materialName: string, specification: string, unit: string, baseJournalPrice: number, baseJournalTaxPrice: number, marketPrice: number, marketTaxPrice: number, libraryCode: string, levelMark: string, sortNo: number, isChangeAva: number, isFyrcj: number, kindSc: string, transferFactor: string, taxRate: number, isDataTaxRate: number, taxRemoval: number, price: number, agencyCode: string, productCode: string, resQty: number, initResQty: number, changeResQty: number, scCount: number) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.level1 = level1;
        this.level2 = level2;
        this.level3 = level3;
        this.level4 = level4;
        this.level5 = level5;
        this.level6 = level6;
        this.level7 = level7;
        this.materialCode = materialCode;
        this.kind = kind;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.baseJournalPrice = baseJournalPrice;
        this.baseJournalTaxPrice = baseJournalTaxPrice;
        this.marketPrice = marketPrice;
        this.marketTaxPrice = marketTaxPrice;
        this.libraryCode = libraryCode;
        this.levelMark = levelMark;
        this.sortNo = sortNo;
        this.isChangeAva = isChangeAva;
        this.isFyrcj = isFyrcj;
        this.kindSc = kindSc;
        this.transferFactor = transferFactor;
        this.taxRate = taxRate;
        this.isDataTaxRate = isDataTaxRate;
        this.taxRemoval = taxRemoval;
        this.price = price;
        this.agencyCode = agencyCode;
        this.productCode = productCode;
        this.resQty = resQty;
        this.initResQty = initResQty;
        this.changeResQty = changeResQty;
        this.scCount = scCount;
    }
}
