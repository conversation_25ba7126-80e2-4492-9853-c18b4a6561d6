import {BaseModel} from "./BaseModel";
import { Column, Entity } from 'typeorm';

/**
 * 机械配比表
 */
@Entity({name: "gs_base_clpb"})
export class GsBaseClpb extends BaseModel {
  @Column({name: "library_code", nullable: true})
  public libraryCode: string; // '定额库编码',
  @Column({name: "rcj_id", nullable: true})
  public rcjId: string; // '材料表id',

  @Column({name: "pb_code", nullable: true})
  public pbCode: string; // '配比材料编码',

  @Column({name: "pb_name", nullable: true})
  public pbName: string; // '配比材料名称',

  @Column({name: "material_code", nullable: true})
  public materialCode: string; // '材料编码',

  @Column("decimal",{name: "kind", nullable: true})
  public kind: number; // '项目类别(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)',

  @Column({name: "material_name", nullable: true})
  public materialName: string; // '材料名称',

  @Column({name: "specification", nullable: true})
  public specification: string; // '规格',

  @Column({name: "unit", nullable: true})
  public unit: string; // '单位',

  @Column("decimal",{name: "res_qty", nullable: true})
  public resQty: number; // '材料消耗量',

  @Column("decimal",{name: "de_price", nullable: true})
  public dePrice: number; // '定额价',

  @Column("decimal",{name: "market_price", nullable: true})
  public marketPrice: number; // '市场价',

  @Column("decimal",{name: "tax_removal", nullable: true})
  public taxRemoval: number; // '除税系数',

  @Column({name: "kind_sc", nullable: true})
  public kindSc: string; // '三材分类：钢材、水泥、商砼、钢筋、木材、商品砂浆',

  @Column({name: "transfer_factor", nullable: true})
  public transferFactor: string; // '三材转化系数',

}