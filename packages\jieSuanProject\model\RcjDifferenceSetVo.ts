/**
 * 调差设置
 */
export class RcjDifferenceSetVo{



    public kind : number;//材料类别

    public rcjDifferenceType : number;//人材机调整类型


    public rcjPeriodsSet : number;//单/双期设置


    public frequencyList : Array<any>;//次数明细  数据内容 {"num": 1 , scope: }


    constructor(kind: number, rcjDifferenceType: number, rcjPeriodsSet: number, frequencyList: Array<any>) {
        this.kind = kind;
        this.rcjDifferenceType = rcjDifferenceType;
        this.rcjPeriodsSet = rcjPeriodsSet;
        this.frequencyList = frequencyList;
    }
}