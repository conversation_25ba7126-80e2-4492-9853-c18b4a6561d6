<!--
 * @Descripttion: 
 * @Author: renmingming
 * @Date: 2024-06-24 09:55:23
 * @LastEditors: renmingming
 * @LastEditTime: 2024-06-28 10:46:18
-->
<template>
  <a-tooltip v-if="contrastData()">
    <template #title>
      <div>
        <div>送审：{{ row.ysshSysj[contrast] }}</div>
        <div>审定：{{ row[contrast] }}</div>
      </div>
    </template>
    <span style="cursor: pointer" v-show="contrastData()"
      >&nbsp;<icon-font type="icon-tishi" class="iconType"
    /></span>
  </a-tooltip>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
const props = defineProps({
  row: {
    type: Object,
  },
  contrast: {
    type: String,
  },
});
const contrastData = () => {
  if (
    ['03', '04'].includes(props.row.kind) &&
    props.row.ysshSysj &&
    props.row[props.contrast]
  ) {
    return props.row[props.contrast] !== props.row.ysshSysj[props.contrast];
  } else {
    return false;
  }
};
</script>
