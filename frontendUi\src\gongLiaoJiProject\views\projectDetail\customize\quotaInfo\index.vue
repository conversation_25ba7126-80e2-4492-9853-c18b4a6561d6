<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-05-29 15:11:09
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-11 10:56:50
-->
<template>
  <div class="quota-info">
    <div class="head-action">
      <a-tabs
        v-model:activeKey="componentName"
        type="card"
        @change="tableChange"
        :hideAdd="true"
      >
        <a-tab-pane v-for="pane in tabList" :key="pane.key" :tab="pane.title">
          <!--          {{ pane.title }}-->
        </a-tab-pane>
      </a-tabs>
      <!--			<a-radio-group-->
      <!--				:value="componentName"-->
      <!--				@change="tableChange"-->
      <!--				button-style="solid"-->
      <!--				style="margin-right: 15px"-->
      <!--			>-->
      <!--			</a-radio-group>-->
    </div>
    <keep-alive>
      <div class="content">
        <component
          ref="materialRef"
          :is="components.get(componentName)"
          :tableDataList="tableDataList"
          :tableData="tableData"
          :currentInfo="props.currentInfo"
          :gsListVal="gsListVal"
          v-model:currentMaterialInfo="currentMaterialInfo"
          :type="props.type"
          :interfaceData="interfaceData"
          :RefreshList="refreshList"
          @dbClickFile="dbClickFile"
          @updateData="updateData"
          @cellDBLClickEvent="cellDBLClickEvent"
        ></component>
      </div>
    </keep-alive>
    <material-machine-index
      v-model:indexVisible="indexVisible"
      v-model:currentMaterialInfo="currentMaterialInfo"
      :currentInfo="props.currentInfo"
      :indexLoading="indexLoading"
      @currentRcjInfo="currentRcjInfo"
      @addChildrenRcjData="addChildrenRcjData"
      @currentInfoReplace="currentInfoReplace"
    ></material-machine-index>
  </div>
</template>

<script setup>
import {
  defineAsyncComponent,
  markRaw,
  nextTick,
  ref,
  watch,
  defineExpose,
} from 'vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import api from '@gongLiaoJi/api/projectDetail';
import csProject from '@gongLiaoJi/api/csProject.js';
import feePro from '@gongLiaoJi/api/feePro';
import { globalData } from './status.js';

import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import deMapFun from '../deMap';
import operateList from '../operate';
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
let { updateGljSelrowId } = recordProjectData();
const projectStore = projectDetailStore();

const props = defineProps([
  'currentInfo',
  'isAttrContent',
  'type',
  'isUpdateFile',
  'isUpdateQuantities',
  'isComplete',
  'isUpdate',
  'tableDataList',
]);
const emits = defineEmits(['refreshCurrentInfo', 'tabClickBefore']);
const indexVisible = ref(false);

deMapFun.businessType = props.type;

let tabList = ref([
  {
    title: '人材机明细',
    key: 'materialMachineTable',
  },
  // {
  //   title: '单价构成',
  //   key: 'priceCompositionTable',
  // },
  {
    title: '标准换算',
    key: 'standardConversionTable',
  },
  {
    title: '换算信息',
    key: 'conversionInfoTable',
  },
  // {
  //   title: '特征及内容',
  //   key: 'contentsTable',
  // },
  {
    title: '工程量明细',
    key: 'quantitiesTable',
  },
  // {
  //   title: '安文费明细',
  //   key: 'safeFeeInfoTable',
  // },
  {
    title: '说明信息',
    key: 'descriptiveInfo',
  },
]);
let tableData = ref([]);

let currentSequenceNbr = ref('');
let currentMaterialInfo = ref(null);
let indexLoading = ref(false); // 索引页面loading
let materialRef = ref(null);
let interfaceData = ref(null);
const gsListVal = ref({});
const isCsYs = ref(false);
const checkList = projectStore.checkCgZsIdList;
const rcjDetailList = ref([]);
watch(
  () => materialRef.value,
  val => {
    projectStore.materialRef = val;
  },
  { deep: true }
);

const components = markRaw(new Map());
components.set(
  'materialMachineTable',
  defineAsyncComponent(() => import('./materialMachineTable.vue'))
);
components.set(
  'priceCompositionTable',
  defineAsyncComponent(() => import('./priceCompositionTable.vue'))
);
components.set(
  'standardConversionTable',
  defineAsyncComponent(() => import('./standardConversionTable.vue'))
);
components.set(
  'conversionInfoTable',
  defineAsyncComponent(() => import('./conversionInfoTable.vue'))
);
components.set(
  'directAllocation',
  defineAsyncComponent(() => import('./directAllocation.vue'))
);
components.set(
  'contentsTable',
  defineAsyncComponent(() => import('./contentsTable.vue'))
);
components.set(
  'quantitiesTable',
  defineAsyncComponent(() => import('./quantitiesTable.vue'))
);
components.set(
  'safeFeeInfoTable',
  defineAsyncComponent(() => import('./safeFeeInfo.vue'))
);

components.set(
  'groupSchemeTable',
  defineAsyncComponent(() => import('./groupSchemeTable.vue'))
);

// 说明信息
components.set(
  'descriptiveInfo',
  defineAsyncComponent(() => import('./descriptiveInfo.vue'))
);

let componentName = ref('materialMachineTable');
const key = ref(new Date().getTime());

if (projectStore.tabSelectName === '措施项目') {
  tabList.value.splice(3, 0, {
    title: '直接费分摊',
    key: 'directAllocation',
  });
}

watch(
  () => componentName.value,
  (newVal, oldVal) => {
    console.log('componentName.value--------', componentName.value);
    updateGljSelrowId(newVal, projectStore.tabSelectName, 'bottomTabName');
  },
  { deep: true }
);
const tableChange = event => {
  if (event && !props.isComplete) {
    componentName.value = 'materialMachineTable';
    emits('tabClickBefore');
    return;
  }
  if (componentName.value === 'materialMachineTable') {
    queryRcjDataByDeId();
  } else if (componentName.value === 'priceCompositionTable') {
    mathDePrice();
  } else if (componentName.value === 'standardConversionTable') {
    queryRule();
  } else if (componentName.value === 'conversionInfoTable') {
    queryRuleInfo();
  } else if (componentName.value === 'contentsTable') {
    qdFeature();
  } else if (componentName.value === 'quantitiesTable') {
    queryAll();
  } else if (componentName.value === 'safeFeeInfoTable') {
    querySafeFeeData();
  } else if (componentName.value === 'groupSchemeTable') {
    queryGroupSchemeData();
  } else if (componentName.value === 'directAllocation') {
    getDirectAllocation();
  }
};
watch(
  () => props.currentInfo,
  (newVal, oldVal) => {
    let gljCheckTab = projectStore.gljCheckTab;
    let upSelRow = gljCheckTab[
      projectStore.currentTreeInfo.sequenceNbr
    ]?.tabList.find(a => a.tabName == projectStore.tabSelectName);
    if (upSelRow) {
      if (upSelRow.bottomTabName == '') {
        componentName.value = 'materialMachineTable';
      } else if (upSelRow.bottomTabName) {
        componentName.value = upSelRow.bottomTabName;
      }
    }
    // 预算书下方tab不在人材机明细的时候，切换选择的定额或者主材，更新 修改未计价材料 disabled 状态
    const hasCurrentInfo = props.tableDataList?.find(
      item => item.sequenceNbr === props.currentInfo.sequenceNbr
    );
    if (
      (projectStore.tabSelectName === '措施项目' &&
        componentName.value === 'materialMachineTable' &&
        hasCurrentInfo) ||
      projectStore.tabSelectName === '预算书'
    ) {
      queryRcjDataByDeId();
    }
    console.log('nonono1', newVal, oldVal);
    if (props.currentInfo) {
      // isSupplement 是否未补充清单
      let { kind, standardId, zjcsClassCode, isSupplement, bdCode, fxCode } =
        props.currentInfo;
      let unpriced = operateList.value.find(item => item.name === 'unpriced');
      setTimeout(() => {
        let unpricedData = rcjDetailList.value.filter(
          x => x.kind == 5 || x.kind == 4
        );
        if (
          unpricedData.length > 0 &&
          props.currentInfo.kind != '00' &&
          props.currentInfo.kind != '01' &&
          props.currentInfo.kind != '02'
        ) {
          unpriced.disabled = false;
        } else {
          unpriced.disabled = true;
        }
      }, 100);
      // 组价方案交互判断，
      // 组价方案，切换分部清单，刷新数据。
      // 如果单纯的双击组价方案左侧的话，不需要组价自身刷新数据
      if (
        newVal?.sequenceNbr !== oldVal?.sequenceNbr &&
        materialRef.value &&
        materialRef.value?.editRefresh
      ) {
        materialRef.value?.editRefresh();
        console.log('刷新');
      }

      let initTabList = tabList.value.filter(
        item => !['groupSchemeTable', 'safeFeeInfoTable'].includes(item.key)
      );
      if (props.currentInfo && Number(props.currentInfo.isCostDe) === 1) {
        initTabList.push({
          title: '安全生产、文明施工费明细',
          key: 'safeFeeInfoTable',
        });
      }

      // 仅选中清单行时，明细区有的组价方案页签展示；
      // zjcsClassCode "0" 安文费
      let hasGroupSchemeTableStatus =
        (bdCode || fxCode) &&
        ['03'].includes(kind) &&
        !checkList.includes(standardId) &&
        (zjcsClassCode === null || typeof zjcsClassCode == 'undefined') &&
        isSupplement === 0;

      if (hasGroupSchemeTableStatus && [12].includes(+projectStore.deType)) {
        initTabList.push({
          title: '组价方案',
          key: 'groupSchemeTable',
        });
      }

      // 1.如果上一次选中了组价方案页签，但当前行不是清单，就取消选中
      // 2. 如果上一次选中了安全文明费页签，但当前行不是1，就取消选中
      if (
        (componentName.value === 'groupSchemeTable' &&
          !hasGroupSchemeTableStatus) ||
        (componentName.value === 'safeFeeInfoTable' &&
          Number(props.currentInfo.isCostDe) !== 1)
      ) {
        componentName.value = 'materialMachineTable';
      }

      tabList.value = initTabList;

      // 组价方案刷新判断
      // 非组价方案的沿用以前的
      if (
        (componentName.value === 'groupSchemeTable' &&
          !isDeepEqual(newVal, oldVal)) ||
        componentName.value !== 'groupSchemeTable'
      ) {
        tableChange();
      }
    }
  },
  { deep: true }
);
// watch([props.currentInfo, tableData.value], ([newValue1, newValue2], [oldValue1, oldValue2]) => {
//   console.log('ref1 changed from', oldValue1, 'to', newValue1);
//   console.log('ref2 changed from', oldValue2, 'to', newValue2);
// });
watch(
  () => props.isAttrContent,
  () => {
    if (props.isAttrContent) {
      componentName.value = 'contentsTable';
      qdFeature();
    }
  }
);
watch(
  () => props.isUpdateFile,
  () => {
    console.log('isUpdateFile', props.isUpdateFile);
    if (props.isUpdateFile) {
      if (componentName.value === 'priceCompositionTable') {
        mathDePrice();
      } else if (componentName.value === 'materialMachineTable') {
        queryRcjDataByDeId();
      }
    }
  }
);

watch(
  () => props.isUpdateQuantities,
  () => {
    if (props.isUpdateQuantities) {
      if (componentName.value === 'quantitiesTable') {
        clearAll();
      }
    }
  }
);

watch(
  () => props.isUpdate,
  () => {
    if (props.isUpdate) {
      tableChange();
    }
  }
);

const isDeepEqual = (obj1, obj2) => {
  if (obj1 === obj2) return true; // 如果引用相同，则直接返回true
  if (
    typeof obj1 !== 'object' ||
    obj1 === null ||
    typeof obj2 !== 'object' ||
    obj2 === null
  ) {
    return false; // 如果一方不是对象或者为null，则不相等
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false; // 属性数量不同则不相等
  }

  for (let key of keys1) {
    const val1 = obj1[key];
    const val2 = obj2[key];
    const areObjects = isObject(val1) && isObject(val2);

    if (
      (areObjects && !isDeepEqual(val1, val2)) ||
      (!areObjects && val1 !== val2)
    ) {
      return false; // 对象属性值不相等或类型不同则不相等
    }
  }

  return true;
};

// 辅助函数，检查值是否为对象
const isObject = value => {
  return value != null && typeof value === 'object';
};

const refreshList = (bol = false) => {
  emits('refreshCurrentInfo', bol);
};

let queryRcjDataByDeIdLoading = ref(false);
const queryRcjDataByDeId = (isSet = true) => {
  // currentMaterialInfo.value = null;
  // console.log('projectStore', projectStore);
  console.log(
    '🚀 ~ queryRcjDataByDeId ~ props.currentInfo:',
    props.currentInfo
  );

  if (!props.currentInfo || queryRcjDataByDeIdLoading.value) return;

  if (props.currentInfo?.kind === '05' || props.currentInfo?.awfType === 2) {
    tableData.value = [];
    rcjDetailList.value = [];
    return false;
  }
  let apiData = {
    deRowId: props.currentInfo?.sequenceNbr || '',
    type: props.currentInfo?.type,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  if (!apiData.deRowId) return;

  queryRcjDataByDeIdLoading.value = true;
  api
    .getAllRcjDetail(apiData)
    .then(res => {
      let tableList = [];
      console.log('定额明细数据', res, apiData);
      if (res.status === 200 && res.result) {
        // console.log('定额明细数据', res.result.map(a => {
        //   return {...a ,pbs:a.pbs || []}
        // }));

        for (let i = 0; i < res.result?.length; ++i) {
          let strNum = '' + res.result[i].totalNumber;
          res.result[i].originalMaterialCode = res.result[i].materialCode;
          if (res.result[i]?.pbs) {
            res.result[i].pbs.forEach(child => {
              child.parentType = res.result[i].type;
              child.parentLevel2 = res.result[i].level2;
              child.originalMaterialCode = child.materialCode;
            });
          }
        }

        if (currentSequenceNbr.value) {
          tableList = res.result.map(item => {
            if (item.rcjId === currentSequenceNbr.value) {
              console.log('currentSequenceNbr', currentSequenceNbr.value, item);
              currentMaterialInfo.value = item;
              item.pbs = item.pbs || [];
            }
            if (!currentMaterialInfo.value?.pbs) {
              item?.pbs?.forEach(child => {
                if (child.rcjId === currentSequenceNbr.value) {
                  currentMaterialInfo.value = child;
                }
              });
            }
            return item;
          });
        }

        tableList = res.result.map(a => {
          return { ...a, pbs: a.pbs || [] };
        });

        getSortNo(tableList);
        console.log(
          'rcjtableData.value',
          tableData.value,
          currentMaterialInfo.value
        );
        setTimeout(() => {
          currentSequenceNbr.value = '';
        });
      } else {
        tableList = [];
      }

      if (isSet) {
        tableData.value = tableList;
      }

      rcjDetailList.value = tableList;
    })
    .finally(() => {
      queryRcjDataByDeIdLoading.value = false;
    });
};
const getSortNo = list => {
  if (list) {
    list.map((item, index) => {
      item.sortNo = index + 1 + '';
      item.pbs &&
        item.pbs.map((child, i) => {
          child.sortNo = `${item.sortNo}.${i + 1}`;
        });
    });
  }
};
const mathDePrice = () => {
  let apiData = {
    pointLineId: props.currentInfo?.sequenceNbr,
    unitWorkId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('单价构成参数', apiData);
  api.mathDePrice(apiData).then(res => {
    console.log('单价构成数据', res);
    if (res.status === 200) {
      res.result &&
        res.result.map(item => {
          if (item.name === '安文费') {
            item.name = '安全生产、文明施工费';
          }
        });
      tableData.value = res.result;
    }
  });
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const dbClickFile = v => {
  emits('onDbClickFile', {
    isRefresh: false,
    ...v,
  });
};

const updateData = column => {
  if (componentName.value === 'materialMachineTable') {
    queryRcjDataByDeId();
  } else if (componentName.value === 'standardConversionTable') {
    queryRule();
    //标准换算tab替换主材后，需更新顶部 '修改未计价材料' disabled 状态
    queryRcjDataByDeId(false);
  } else if (componentName.value === 'conversionInfoTable') {
    queryRuleInfo();
  } else if (componentName.value === 'contentsTable') {
    qdFeature();
    if (column) {
      emits('refreshCurrentInfo');
    }
  } else if (componentName.value === 'quantitiesTable') {
    queryAll();
  } else if (componentName.value === 'directAllocation') {
    getDirectAllocation();
  }
  if (column) {
    emits('refreshCurrentInfo');
  }
};

const cellDBLClickEvent = (row, type) => {
  console.log('column', row);
  if (type == 1) {
    isCsYs.value = true;
  } else {
    isCsYs.value = false;
  }
  if (deMapFun.isDe(props.currentInfo?.kind)) {
    indexVisible.value = true;
  }
  currentMaterialInfo.value = row;
};
const queryRule = () => {
  if (!props.currentInfo) return;

  if (props.currentInfo?.awfType === 2) {
    tableData.value = [];
    return false;
  }
  let apiData = {
    standardDeId: props.currentInfo?.standardId,
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    libraryCode: props.currentInfo?.libraryCode,
  };
  console.log('概算标准换算列表参数', apiData, projectStore);
  csProject.conversionRuleList(apiData).then(res => {
    console.log('概算标准换算列表数据', res);
    if (res.status === 200 && res.result) {
      gsListVal.value = res.result;
      let result = res.result.conversionList;
      const group = getList(result);
      group.forEach(item => {
        if (item[0].relationGroupName) {
          item.forEach((child, index) => {
            if (index === 0) {
              child.rowSpan = item.length;
            } else {
              child.rowSpan = 0;
            }
            child.old_selected = child.selected || false;
            child.chapterStatus = false;
          });
        } else {
          item.forEach(child => {
            child.old_selected = child.selected || false;
            child.rowSpan = 1;
            child.chapterStatus = false;
          });
        }
      });
      group.forEach(item => {
        item.forEach(child => {
          result.map(original => {
            original = child;
          });
        });
      });
      console.log('group', result);
      tableData.value = result;
      console.log('==========', tableData.value);
    } else {
      tableData.value = [];
    }
  });
};

const getList = list => {
  const map = new Map();
  list.forEach((item, index, arr) => {
    if (!map.has(item.relationGroupName)) {
      map.set(
        item.relationGroupName,
        arr.filter(a => a.relationGroupName === item.relationGroupName)
      );
    }
  });
  return Array.from(map).map(item => [...item[1]]);
};

const queryRuleInfo = () => {
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('换算信息参数', apiData);
  api.queryRuleInfo(apiData).then(async res => {
    console.log('换算信息数据', res);
    if (res.status === 200) {
      let result = [];
      // if (res.result.length > 0 && res.result[0].children) {
      //   await flattenTree(result, res.result);
      // } else {
      //   result = res.result;
      // }
      res.result.map((item, index) => {
        item.sortNumber = index + 1 + '';
        item.children &&
          item.children.map((child, i) => {
            child.sortNumber = `${item.sortNumber}.${i + 1}`;
          });
      });
      tableData.value = res.result;
    }
  });
};

// 递归函数，将树结构转换为一维数组
const flattenTree = (result, data) => {
  data.forEach((item, index) => {
    result.push(item);
    if (item?.children && item?.children.length > 0) {
      flattenTree(result, item.children);
    }
  });
  return result;
};
const querySafeFeeData = () => {
  // isCostDe判别是否是安文费
  console.log('props.currentInfo', props.currentInfo);
  if (props.currentInfo && Number(props.currentInfo.isCostDe) === 1) {
    let apiData = {
      deId: props.currentInfo?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
    };
    console.log('安文费明细参数', apiData);
    api.awfDetails(apiData).then(res => {
      console.log('安文费明细数据', res.result, typeof res.result);
      if (res.status === 200) {
        tableData.value = [res.result];
        console.log('==========', tableData.value);
      }
    });
  } else {
    tableData.value = [];
  }
};
const qdFeature = () => {
  if (props.currentInfo?.kind !== '03') {
    tableData.value = [];
    return;
  }
  let apiData = {
    fbFxQdId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.qdFeature(apiData).then(res => {
    console.log('特征及内容数据', res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
      console.log('==========', tableData.value);
    } else {
      tableData.value = [];
    }
  });
};

const getDirectAllocation = () => {
  console.log('projectStore', projectStore);
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    deId: props.currentInfo?.sequenceNbr,
  };
  console.log('params,', params);
  csProject.getShareCost(params).then(res => {
    console.log('直接费分摊', res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
    }
  });
};

// 工程量明细查询所有数据
const queryAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    // singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    // type: props.type,
    deId: props.currentInfo?.sequenceNbr,
  };
  console.log('工程量明细参数', apiData, props.currentInfo);
  api
    .queryAll(apiData)
    .then(res => {
      if (res.status === 200 && res.result && props.currentInfo.kind !== '00') {
        console.log('工程量明细结果', res.result);
        tableData.value = res.result;
      } else {
        tableData.value = [];
      }
    })
    .catch(err => {
      console.error('工程量明细结果报错', err);
      tableData.value = [];
    });
};

// 工程量明细清空所有数据
const clearAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    quotaListId: props.currentInfo?.sequenceNbr,
  };
  console.log('工程量清空数据参数', apiData);
  api.clearAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('工程量清空数据结果', res.result);
      queryAll();
    }
  });
};

let SchemeDataLoading = ref(false);

// 组价方案查询所有数据
const queryGroupSchemeData = async () => {
  const { result } = await feePro.isOnline();
  if (!result) {
    tableData.value = [];
    message.error('网络未连接!');
    return;
  }

  if (SchemeDataLoading.value) return;
  const postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type == 1 ? 'fbfx' : 'csxm',
    qdId: props.currentInfo?.sequenceNbr,
  };
  interfaceData.value = postData;
  // tableData.value = [];
  SchemeDataLoading.value = true;
  csProject
    .qdMergePlanQuery(postData)
    .then(res => {
      console.log('组价方案查询所有数据');
      if (res.status === 200) {
        tableData.value = res.result || [];
      }
    })
    .finally(() => {
      SchemeDataLoading.value = false;
    });
};

const currentRcjInfo = (row, rcjId) => {
  console.log('currentRcjInfo', row, rcjId);
  indexLoading.value = true;
  let apiData = {
    deId:
      props.currentInfo.kind === '05'
        ? props.currentInfo?.parentId
        : props.currentInfo?.sequenceNbr,
    baseRcjModel: JSON.parse(JSON.stringify(row)),
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: '1',
    deRowId:
      props.currentInfo.kind === '05'
        ? props.currentInfo?.parentId
        : props.currentInfo?.deRowId,
    rcjId,
  };
  if (props.currentInfo.kind === '06')
    apiData.rcjId = tableData.value[0].parentId;
  console.log('人材机增加参数', apiData);
  indexLoading.value = false;
  api.addRcjData(apiData).then(res => {
    indexLoading.value = false;
    if (res.status === 200 && res.result) {
      console.log('人材机增加结果', res.result);
      message.success('插入成功');
      currentSequenceNbr.value = res.result;

      emits('refreshCurrentInfo');
      queryRcjDataByDeId();
    } else {
      console.log(
        '人材机增加结果',
        res.message && res.message != '操作成功' ? res.message : ''
      );
      message.error(
        (res.message && res.message != '操作成功' ? res.message : '') ||
          '插入子目必须与其他子目类型相同且不能具有层级结构，请重新插入！'
      );
    }
  });
};

const addChildrenRcjData = (row, currentMaterialInfo) => {
  console.log('materialRef.value', currentMaterialInfo);
  currentRcjInfo(row, currentMaterialInfo.parentId);
};

const currentInfoReplace = row => {
  console.log(props.type);
  // if (props.type === 1) {
  //   replaceItemBillData(row);
  // } else {
  //   itemReplaceFromIndexPage(row);
  // }
  replaceItemBillData(row);
};

// 人材机替换功能
const replaceItemBillData = row => {
  if (!currentMaterialInfo.value) {
    currentRcjInfo(row);
    return;
  }
  indexLoading.value = true;
  let rcjId = '';
  if (['06', '09'].includes(props.currentInfo.kind)) {
    rcjId = currentMaterialInfo.value.parentId;
  }
  if (
    Object.hasOwnProperty.call(currentMaterialInfo.value, 'pbs') ||
    isCsYs.value
  ) {
    // if (currentMaterialInfo.value.pbs.length === 0) {
    //   rcjId = currentMaterialInfo.value.parentId;
    // }
  } else {
    rcjId = currentMaterialInfo.value.parentId;
  }
  let apiData = {
    deId: isCsYs.value
      ? props.currentInfo?.parentId
      : props.currentInfo?.sequenceNbr,
    baseRcjModel: JSON.parse(JSON.stringify(row)),
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: '1',
    deRowId: props.currentInfo?.deRowId,
    rcjDetailId: JSON.parse(JSON.stringify(currentMaterialInfo.value))
      .sequenceNbr,
    rcjId,
    factor: Number(row.conversionCoefficient) || '',
  };
  console.log('res// 人材机替换功能', apiData);
  api
    .replaceItemBillData(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        message.success('替换成功');
        emits('refreshCurrentInfo');
        console.log('替换成功', res.result, row);
        currentSequenceNbr.value = row.sequenceNbr;
        queryRcjDataByDeId();
      } else {
        message.error(
          '插入子目必须与其他子目类型相同且不能具有层级结构，请重新插入！'
        );
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

// 措施项目替换功能
const itemReplaceFromIndexPage = row => {
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentMaterialInfo.value?.sequenceNbr,
    type: 2,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    libraryCode: row.libraryCode,
  };
  api.itemReplaceFromIndexPage(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('替换成功');
      emits('refreshCurrentInfo');
      indexLoading.value = false;
      console.log('替换成功', res.result, row);
      currentSequenceNbr.value = res.result;
      queryRcjDataByDeId();
    }
  });
};

// 外部切换tab
const manualTabChange = name => {
  console.log('切换', name);
  componentName.value = name;
  tableChange(null);
};

defineExpose({
  manualTabChange,
  materialRef,
  cellDBLClickEvent,
});
</script>

<style lang="scss" scoped>
.quota-info {
  height: 100%;
  .head-action {
    //margin-bottom: 5px;
    height: 35px;
    background: #e7e7e7;
    flex: 1;
    :deep(.ant-tabs-tab) {
      height: 35px;
      background: transparent;
      border: none;
      color: #7c7c7c;
    }
    :deep(.ant-tabs-tab-active) {
      background: #ffffff;
      border-top: 2px solid #4786ff;
      .ant-tabs-tab-btn {
        color: #000000;
      }
    }
    button {
      float: right;
      margin-right: 15px;
    }
  }
  .content {
    height: calc(100% - 40px);
  }
}
</style>
