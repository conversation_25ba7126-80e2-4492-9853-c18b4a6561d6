<!--
 * @Descripttion: 人材机分期调整
 * @Author: liuxia
 * @Date: 2024-03-04 15:53:12
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-06 16:07:08
-->
<template>
  <div class="diff-price-setting">
    <common-modal
      className="dialog-comm resizeClass"
      title="人材机分期调整"
      width="500"
      height="420"
      v-model:modelValue="props.visible"
      :mask="false"
      @close="cancel"
    >
      <div class="range-content">
        <div class="stage-select">
          <span class="name">是否对人材机进行分期调整</span>
          <vxe-select
            v-model="formData.isStage"
            transfer
          >
            <vxe-option
              v-for="item in typeList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </div>
        <div
          class="stage-way"
          v-if="formData.isStage"
        >
          <span class="name">分期输入方式</span>
          <vxe-select
            v-model="formData.stageType"
            transfer
          >
            <vxe-option
              v-for="item in stageWayList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
          <span class="name all">总期数</span>
          <vxe-input
            v-model.trim="formData.periods"
            className="count"
            @keyup="formData.periods = getPositiveInteger(formData.periods)"
          ></vxe-input>
          <span class="tag">期</span>
        </div>
        <div class="tips">
          <p>提示</p>
          <div v-if="!formData.isStage">
            <span>选择【不分期】：</span>
            <span>即统一调差，直接在结算工程量输入数值</span>
          </div>
          <div>
            <span>选择【分期】：</span>
            <span>即分期调差，在分期工程量明细中输入分期工作量，结算工程量等于分期量之和</span>
          </div>
        </div>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          @click="rcjStageSet"
        >确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { getPositiveInteger } from '@/utils/index.js';
import infoMode from '@/plugins/infoMode.js';
const projectStore = projectDetailStore();
const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'successCallback']);
let formData = reactive({
  isStage: false, // 是否分期
  stageType: null, // 分期方式
  periods: null, // 分期总数
});
const originalData = ref(null);
const typeList = reactive([
  {
    name: '分期',
    code: true,
  },
  {
    name: '不分期',
    code: false,
  },
]);

const stageWayList = reactive([
  {
    name: '按分期比例输入',
    code: 1,
  },
  {
    name: '按分期工程量输入',
    code: 2,
  },
]);

watch(
  () => props.visible,
  () => {
    console.log('人材机分期调整。。。。');
    getRcjStageSet();
  }
);

const getRcjStageSet = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.getRcjStageSet(apiData).then(res => {
    console.log('res11111111111', res);
    if (res.status === 200) {
      if (res.result) {
        originalData.value = res.result;
        formData = reactive({
          isStage: true,
          periods: res.result.periods,
          stageType: res.result.stageType,
        });
      } else {
        formData = reactive({
          isStage: false,
          periods: null,
          stageType: null,
        });
      }
    }
  });
};

const rcjStageSet = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId,
    unitId: projectStore.currentTreeInfo?.id,
    isStage: formData.isStage,
    periods: Number(formData.periods),
    stageType: formData.stageType,
  };
  console.log('参数', apiData);
  if (formData.isStage && !formData.stageType) {
    message.warn('请选择分期输入方式');
    return;
  }
  if (formData.isStage && !formData.periods) {
    message.warn('请输入总期数');
    return;
  }

  if (originalData.value?.isStage && !formData.isStage) {
    infoMode.show({
      iconType: 'icon-qiangtixing',
      infoText: '切换调整方法，当前页面数据不保留，是否确认切换？',
      confirm: () => {
        api.rcjStageSet(apiData).then(res => {
          console.log('res222222', res);
          if (res.status === 200 && res.result) {
            message.success('分期调整设置成功');
            projectStore.SET_STAGE_COUNT(0);
            emits('successCallback');
            cancel();
          }
        });
        infoMode.hide();
      },
      close: () => {
        infoMode.hide();
      },
    });
  } else {
    api.rcjStageSet(apiData).then(res => {
      console.log('res222222', res);
      if (res.status === 200 && res.result) {
        message.success('分期调整设置成功');
        projectStore.SET_STAGE_COUNT(Number(formData.periods));
        emits('successCallback');
        cancel();
      }
    });
  }
};

const cancel = () => {
  emits('update:visible', false);
};
</script>

<style lang="scss" scoped>
.range-content {
  width: 444px;
  margin-bottom: 30px;
  .name {
    font-size: 14px;
    color: #000000;
    margin-right: 10px;
  }
  .stage-select {
    display: flex;
    :deep(.vxe-select) {
      flex: 1;
    }
  }
  .stage-way {
    display: flex;
    align-items: center;
    margin-top: 15px;
    .all {
      margin-left: 15px;
    }
    .tag {
      font-size: 14px;
      color: #287cfa;
      margin-left: 8px;
    }
    :deep(.count) {
      width: 80px;
    }
  }
  .tips {
    font-size: 14px;
    color: #000000;
    border: 1px solid #d9d9d9;
    margin-top: 15px;
    padding: 15px;
    span {
      display: block;
      margin-bottom: 6px;
    }
    div:nth-of-type(1) {
      margin-bottom: 20px;
    }
  }
}
</style>
