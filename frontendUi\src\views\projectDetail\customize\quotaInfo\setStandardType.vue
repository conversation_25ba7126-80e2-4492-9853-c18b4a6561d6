<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-10-20 15:40:25
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-03 17:56:51
-->
<template>
  <div class="set-standard-type">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.standardVisible"
      title="设置标准换算"
      :mask="true"
      :lockView="false"
      :lockScroll="false"
      width="900px"
      height="50%"
      @cancel="close"
      @close="close"
    >
      <div class="content">
        <div class="single">
          <span class="title">标准换算</span>
          <div class="table-box">
            <standard-conversion-table
              :currentInfo="props.currentInfo"
              :tableData="tableData"
              :type="props.type"
              :isSetStandard="true"
              :placement="'bottom'"
              @batchConversionRule="batchConversionRule"
            ></standard-conversion-table>
          </div>
        </div>
        <div class="single">
          <span class="title">统一换算</span>
          <div class="table-box">
            <standard-type-table
              :currentInfo="props.currentInfo"
              :isSetStandard="true"
              @saveData="saveData"
            ></standard-type-table>
          </div>
        </div>
      </div>
      <div class="footer-btn-list">
        <vxe-checkbox
          v-model="standardConversionShowFlag"
          :checked-value="true"
          :unchecked-value="false"
          @change="standardConversionShowFlagColl"
          >不再显示该窗口</vxe-checkbox
        >
        <div>
          <a-button @click="close">取消</a-button>
          <a-button type="primary" @click="handleOk">确定</a-button>
        </div>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import StandardConversionTable from './standardConversionTable.vue';
import StandardTypeTable from './standardTypeTable.vue';
import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import { nextTick, onMounted, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

const props = defineProps(['standardVisible', 'currentInfo', 'type']);
const emits = defineEmits(['update:standardVisible', 'closeDialog']);
const projectStore = projectDetailStore();
const tableData = ref([]);
const rightTableData = ref([]);
let standardConversionShowFlag = ref(false);

watch(
  () => props.standardVisible,
  () => {
    if (props.standardVisible) {
      console.log('==============设置标准换算', props.currentInfo);
      queryRule();
    }
  }
);

onMounted(() => {
  nextTick(() => {
    let document1 = document.querySelector(
      '.standard-conversion-table .vxe-header--row'
    );
    if (document1) {
      document1.children[1].setAttribute('colspan', '2');
    }
    console.log('document', document1);
  });
});
const close = () => {
  emits('closeDialog');
};

const queryRule = () => {
  let apiData = {
    standardDeId: props.currentInfo?.standardId,
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    libraryCode: props.currentInfo?.libraryCode,
  };
  console.log('标准换算列表参数1', apiData);
  api.queryRule(apiData).then(res => {
    console.log('标准换算列表数据', res);
    if (res.status === 200 && res.result) {
      let result = res.result;
      const group = getList(result);
      group.forEach(item => {
        if (item[0].relationGroupName) {
          item.forEach((child, index) => {
            if (index === 0) {
              child.rowSpan = item.length;
            } else {
              child.rowSpan = 0;
            }
            child.old_selected = child.selected || false;
          });
        } else {
          item.forEach(child => {
            child.old_selected = child.selected || false;
            child.rowSpan = 1;
          });
        }
      });
      group.forEach(item => {
        item.forEach(child => {
          result.map(original => {
            original = child;
          });
        });
      });
      console.log('group', result);
      tableData.value = result;
      console.log('==========', tableData.value);
    } else {
      tableData.value = [];
    }
  });
};

const getList = list => {
  const map = new Map();
  list.forEach((item, index, arr) => {
    if (!map.has(item.relationGroupName)) {
      map.set(
        item.relationGroupName,
        arr.filter(a => a.relationGroupName === item.relationGroupName)
      );
    }
  });
  return Array.from(map).map(item => [...item[1]]);
};

const handleOk = () => {
  updateDefDonversion();
};

const batchConversionRule = data => {
  console.log('batchConversionRule', data);
  tableData.value = data;
};

const saveData = data => {
  console.log('saveData', data);
  rightTableData.value = data;
};

const updateDefDonversion = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    donversions: JSON.parse(JSON.stringify(rightTableData.value)),
  };
  console.log('右侧表格数据更新参数api', apiData);
  api.updateDefDonversion(apiData).then(res => {
    console.log('右侧表格数据更新', res);
    if (res.status === 200 && res.result) {
      batchOperationalConversionRule();
    }
  });
};

const batchOperationalConversionRule = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    fbFxDeId: props.currentInfo.sequenceNbr,
    rules: JSON.parse(JSON.stringify(tableData.value)),
  };
  console.log('batchOperationalConversionRule参数', apiData);
  api.batchOperationalConversionRule(apiData).then(res => {
    console.log('batchOperationalConversionRule', res);
    if (res.status === 200 && res.result) {
      message.success('保存成功');
      close();
      emits('refreshCurrentInfo');
    }
  });
};

// 设置标准换算
const standardConversionShowFlagColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    standardConversionShowFlag: !standardConversionShowFlag.value,
  };
  api.standardConversionShowFlagColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('设置成功');
      queryConstructProjectMessageColl();
    } else {
      message.error('设置失败');
    }
  });
};

// 获取项目数据
const queryConstructProjectMessageColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  api.queryConstructProjectMessageColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      projectStore.SET_GLOBAL_SETTING_INFO({
        mainRcjShowFlag: res.result.mainRcjShowFlag,
        standardConversionShowFlag: res.result.standardConversionShowFlag,
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.content {
  height: 85%;
  display: flex;
  width: 100%;
  margin-bottom: 15px;
  .single {
    border: 1px solid #d9d9d9;
    padding: 5px 0 5px 5px;
    display: flex;
    flex-direction: column;
    width: 50%;
    .title {
      display: block;
      font-size: 14px;
      color: #4a85de;
      padding: 0 0 10px 10px;
    }
  }
  .single:nth-of-type(1) {
    margin-right: 15px;
  }
  .table-box {
    flex: 1;
    overflow: hidden;
  }
}
.footer-btn-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
