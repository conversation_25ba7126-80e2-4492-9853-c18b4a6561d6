const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const WildcardMap = require('../../../core/container/WildcardMap');
const {DERules, deCalculatorBaseFn} = require("./DeCalculatorCodes");
const DeTypeConstants = require("../../../constants/DeTypeConstants");
const ResourceKindConstants = require("../../../constants/ResourceKindConstants");
const LogUtil = require("../../../core/tools/logUtil");
const {NumberUtil} = require("../../../utils/NumberUtil");
const CommonConstants = require("../../../constants/CommonConstants");
const ZSFeeConstants = require("../../../constants/ZSFeeConstants");
const CostDeMatchConstants = require('../../../constants/CostDeMatchConstants');
const DeUtils = require('../../utils/DeUtils');
const EE = require('../../../../../core/ee');

/**
 * 定额计算器
 */
class DeCalculator extends CalculateEngine{
  static SPLITOR = "_";
  constructId;
  deRowId;
  currentDe;
  precision;
  unitId;
  isPricingTax = false;
  ctx;
  digitPropertyMap = new  Map();
  //不要打乱顺序，先计price=Rsum+cSum+jSum,,现计算基础数，然后才能汇总
  static  deMap = [
    "RSum",
    "CSum",
    "JSum",
    "SSum",
    "ZSum",
    "RDSum",
    "CDSum",
    "JDSum",
    "ZDSum",
    "SDSum",
    "rTotalSum",
    "cTotalSum",
    "jTotalSum",
    "sTotalSum",
    "zTotalSum",
    "rdTotalSum",
    "cdTotalSum",
    "jdTotalSum",
    "sdTotalSum",
    "zdTotalSum",
    "price",
    "baseJournalPrice",
    "totalNumber",
    "baseJournalTotalNumber",
  ];


  static getInstance({constructId, unitId,deRowId},ctx){
    return new DeCalculator(constructId,unitId,deRowId,null,ctx);
  }

  /**
   *
   * @param constructId 当前工程
   * @param unitId 当前修改的人材机所属的单位工程
   * @param deRowId 当前修改的人材机所属的定额
   * @param resourceId 为当前修改的人材机ID
   * @param ctx
   */
  constructor(constructId,unitId,deRowId,resourceId,ctx) {
    super(ctx);
    this.ctx = ctx;
    this.constructId = constructId;
    this.unitId = unitId;
    this.deRowId = deRowId;
    this.initDigitPropertyMap();
  }
  
  convertValue(value,param) {
    // let paramArray = param.split(DeCalculator.SPLITOR);
    // let digits = this.digitPropertyMap.get(paramArray[0]);
    // if(ObjectUtils.isEmpty(digits)) {
    //   digits = 2;
    // }

    // return NumberUtil.numberScale(value, digits);
    return value;
  }
  initDigitPropertyMap()
  {
    this.digitPropertyMap.set("resQty",5);
    this.digitPropertyMap.set("quantity",5);
    this.digitPropertyMap.set("totalNumber",5);//精度控制影响结果,定额是合价，人材机位数量
    // this.digitPropertyMap.set("rTotalSum",5);
    // this.digitPropertyMap.set("cTotalSum",5);
    // this.digitPropertyMap.set("jTotalSum",5);
    // this.digitPropertyMap.set("sTotalSum",5);
    // this.digitPropertyMap.set("zTotalSum",5);

    // this.digitPropertyMap.set("baseJournalTotalNumber",5);//精度控制影响结果,定额是合价，人材机位数量
    // this.digitPropertyMap.set("rdTotalSum",5);//人工费用按基期价
    // this.digitPropertyMap.set("cdTotalSum",5);//材料费用按基期价
    // this.digitPropertyMap.set("jdTotalSum",5);//机械费用按基期价
    // this.digitPropertyMap.set("zdTotalSum",5);//主材费用按基期价
    // this.digitPropertyMap.set("sdTotalSum",5);//设备费用按基期价
  }
  async prepare()
  {
    let rootProject = this.ctx.treeProject.root;
    this.isPricingTax = rootProject.projectTaxCalculation.taxCalculationMethod == '0';//1? 一般计税: 简易计税
    this.currentDe = this.ctx.allDeMap.getNodeById(this.deRowId);
    //加载精度数据
    let {service} = EE.app;
    this.precision = await service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(this.constructId);
  }

  async analyze() {
    await this.prepare();
    this.preload(deCalculatorBaseFn);
    this.buildRules();
    await this.render()
  }
  
  /**
   * 填充人材机数据
   */
  async render() {
    let deRow = this.currentDe;
    if(ObjectUtils.isNotEmpty(deRow) && (deRow.type != DeTypeConstants.DE_TYPE_ZFB && deRow.type != DeTypeConstants.DE_TYPE_FB
        && deRow.type != DeTypeConstants.DE_TYPE_DEFAULT &&  deRow.type != DeTypeConstants.DE_TYPE_DELIST  ))
    {
      let precisionObj = DeUtils.getPrecisionByDe(this.precision,deRow);
      for(let key of DeCalculator.deMap)
      {
        let digitalKey =  DeUtils.convertDigitalKey(key);
        let digital = precisionObj[digitalKey];

        if(ObjectUtils.isEmpty(digital)){
          digital = this.digitPropertyMap.get(key);
          if(ObjectUtils.isEmpty(digital) || key == 'totalNumber')  
            //特殊处理，因为人材机的totalNumber要求5位，定额的 totalNumber要求2位，digitPropertyMap在convertValue中有使用 人材机的totalNumber 5位，复杂关联
          {
            digital = 2;
          }
        }
        let columnKey = key+"_"+ deRow.sequenceNbr;
        deRow[key] = NumberUtil.numberScale(this.parser(columnKey),digital);
        //更新instanceMap,计算过程中节省位数
        this.instanceMap[columnKey] = deRow[key];
        LogUtil.renderLogger("DeCalculator :" + deRow.sequenceNbr + "---------key :" + key + "---------value :" + deRow[key]);
      }
    }
    deRow.updateDate = Date.now();
    //LogUtil.renderLogger(deRow);
  }

  buildRules() {
    let rules = {};
    // let dePriceRules = "0";//单价
    // let deDPriceRules = "0";//基期 单价

    let rSum = "0";//人 基数 单价
    let cSum = "0";//材 基数 单价
    let jSum = "0";//机 基数 单价
    let sSum = "0";//主设 基数 单价
    let zSum = "0";//主材 基数 单价

    let rdSum = "0";//人 基数 单价  消耗量*定额价
    let cdSum = "0";//人 基数 单价  消耗量*定额价
    let jdSum = "0";//人 基数 单价  消耗量*定额价
    let zdSum = "0";//人 基数 单价  消耗量*定额价
    let sdSum = "0";//人 基数 单价  消耗量*定额价

    //单价(Σ (人材机的消耗量*人材机的市场价))
    let deRow = this.currentDe;
    let subResource = this.ctx.resourceMap.getValues(WildcardMap.generateKey(this.unitId, this.deRowId) + WildcardMap.WILDCARD);
    let deList = this.ctx.allDeMap.getAllNodes().filter(item => item.unitId === deRow.unitId && item.type !== DeTypeConstants.DE_TYPE_FB && item.type !== DeTypeConstants.DE_TYPE_ZFB);

    if (!ZSFeeConstants.ZS_DE_LIST.includes(deRow.deCode)) {
      
      rules[this.getQuantity(deRow.sequenceNbr)] = DERules["quantity"].mathFormula;
      if(this.isCaculatorCost()){
        //如果定额是主材或者设备定额，需要计算主材和设备
        if ((deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB)
            && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)) {
          subResource.forEach(item => {
            rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
            
            //默认数据
            let marketPriceKey = this.getRcjMarketPriceKey(item.sequenceNbr);
            let baseJournalPriceKey = this.getRcjBaseJournalPriceKey(item.sequenceNbr);
            rules[this.getRcjBaseJournalPriceKey(item.sequenceNbr)] = DERules["baseJournalPrice"].mathFormula;
            rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
            //含税覆盖
            if(this.isPricingTax){
              marketPriceKey = this.getRcjMarketTaxPriceKey(item.sequenceNbr);
              baseJournalPriceKey = this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr);
              rules[this.getRcjMarketTaxPriceKey(item.sequenceNbr)] = DERules["marketTaxPrice"].mathFormula;
              rules[this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr)] = DERules["baseJournalTaxPrice"].mathFormula;
            }
            
            rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
            // dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
            // deDPriceRules  += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            if(item.kind == ResourceKindConstants.INT_TYPE_ZC){
              zSum += "+1*" + marketPriceKey;
              zdSum += "+1*" + baseJournalPriceKey;
            }else{
              sSum += "+1*" + marketPriceKey;
              sdSum += "+1*" + baseJournalPriceKey;
            }
          })
        } else {
          for(let item of subResource){
            if(!this.isCaculatorCost()){
              continue;
            }
            rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
            
            //默认数据
            let marketPriceKey = this.getRcjMarketPriceKey(item.sequenceNbr);
            let baseJournalPriceKey = this.getRcjBaseJournalPriceKey(item.sequenceNbr);
            rules[this.getRcjBaseJournalPriceKey(item.sequenceNbr)] = DERules["baseJournalPrice"].mathFormula;
            rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
            //含税覆盖
            if(this.isPricingTax){
              marketPriceKey = this.getRcjMarketTaxPriceKey(item.sequenceNbr);
              baseJournalPriceKey = this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr);
              rules[this.getRcjMarketTaxPriceKey(item.sequenceNbr)] = DERules["marketTaxPrice"].mathFormula;
              rules[this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr)] = DERules["baseJournalTaxPrice"].mathFormula;
            }
            rules[this.getRcjDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
            if (item.isFyrcj == 0 && !ResourceKindConstants.isCsxmRcj(item.materialCode)) {//同一级算
              rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
              let tempOtherBasePrice = '(0';
              let tempOtherMarketPrice = '(0';
              subResource.forEach(noItem => {
                if(noItem.isFyrcj!=0 && (item.kind == noItem.kind ||this._checkCkind(item,noItem))){
                  let noBasePrice = this.getRcjBaseJournalPriceKey(noItem.sequenceNbr);
                  // let noMarketPrice = this.getRcjMarketPriceKey(noItem.sequenceNbr);
                  if(this.isPricingTax){
                    noBasePrice = this.getRcjBaseJournalTaxPriceKey(noItem.sequenceNbr);
                    // noMarketPrice = this.getRcjMarketTaxPriceKey(noItem.sequenceNbr);
  
                  }
                  tempOtherBasePrice += "+" + noBasePrice + "*" + this.getRcjResQtyKey(noItem.sequenceNbr);
                  tempOtherMarketPrice  += "+" + noBasePrice + "*" + this.getRcjResQtyKey(noItem.sequenceNbr);
                }
              });
            
              tempOtherBasePrice += '+0)*'+this.getRcjResQtyKey(item.sequenceNbr)+'/100';
              tempOtherMarketPrice += '+0)*'+this.getRcjResQtyKey(item.sequenceNbr)+'/100';
              if(item.kind == ResourceKindConstants.INT_TYPE_R){
                rSum += '+' + tempOtherMarketPrice;
                rdSum +=  '+' + tempOtherBasePrice;
              }
              if(this._checkCkind(item,item)){
                cSum +=  '+' + tempOtherMarketPrice;
                cdSum +=  '+' + tempOtherBasePrice;
              }
              if(item.kind == ResourceKindConstants.INT_TYPE_J){
                jSum +=  '+' + tempOtherMarketPrice;
                jdSum +=  '+' + tempOtherBasePrice;
              }
              
            }else{
              
              //主材和设备不计算在单价里面
              if (item.kind != ResourceKindConstants.INT_TYPE_ZC && item.kind != ResourceKindConstants.INT_TYPE_SB) {
                // dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
                // deDPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
              }
              //乱改，兼容统一处理
              if (item.kind == ResourceKindConstants.INT_TYPE_ZC){
                zSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
                zdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
              
                deRow.isExistedZcSb = CommonConstants.COMMON_YES;
              }
              if(item.kind == ResourceKindConstants.INT_TYPE_SB) {
                sSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
                sdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
              
                deRow.isExistedZcSb = CommonConstants.COMMON_YES;
              }
              rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
              // rules[this.getDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
              // 修改item.kind 赋值为字符串，此处与int比较不了 暂时用if==
              // switch(item.kind){
              if (item.kind == ResourceKindConstants.INT_TYPE_R) {
                rSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;

                rdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
                
                // break;
              }
              //从那里抄袭过来了，，这么坑的不行
              if (this._checkCkind(item,item)) {
                cSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;

                cdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
                // break;
              }
              if (item.kind == ResourceKindConstants.INT_TYPE_J) {
                jSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;

                jdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
                // break;
              }
            }
          }
        }
      }else{

        for(let item of subResource){
          //默认数据
          //默认数据
          let marketPriceKey = this.getRcjMarketPriceKey(item.sequenceNbr);
          let baseJournalPriceKey = this.getRcjBaseJournalPriceKey(item.sequenceNbr);
          rules[this.getRcjBaseJournalPriceKey(item.sequenceNbr)] = DERules["baseJournalPrice"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          //含税覆盖
          if(this.isPricingTax){
            marketPriceKey = this.getRcjMarketTaxPriceKey(item.sequenceNbr);
            baseJournalPriceKey = this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr);
            rules[this.getRcjMarketTaxPriceKey(item.sequenceNbr)] = DERules["marketTaxPrice"].mathFormula;
            rules[this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr)] = DERules["baseJournalTaxPrice"].mathFormula;
          }
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;

          if (item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
            zdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
          
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          if(item.kind == ResourceKindConstants.INT_TYPE_SB) {
            sSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
            sdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
          
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          // switch(item.kind){
          if (item.kind == ResourceKindConstants.INT_TYPE_R) {
            if (item.isFyrcj == 0) {
              let operateLogic = "/"
              if(this.currentDe.quantity == 0){
                operateLogic = "*";
              }
              let specialCaculate = "+" + this.getTotalNumberKey(item.sequenceNbr)+operateLogic+this.getQuantity(this.deRowId);
              rSum += specialCaculate;
              rdSum += specialCaculate;
            }else{
              rSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
              rdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            }
          }
          //从那里抄袭过来了，，这么坑的不行
          if (this._checkCkind(item,item)) {
            if (item.isFyrcj == 0) {
              let operateLogic = "/"
              if(this.currentDe.quantity == 0){
                operateLogic = "*";
              }
              let specialCaculate = "+" + this.getTotalNumberKey(item.sequenceNbr)+operateLogic+this.getQuantity(this.deRowId);
              cSum += specialCaculate;
              cdSum += specialCaculate;
            }else{
              cSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
              cdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            }
          }
          if (item.kind == ResourceKindConstants.INT_TYPE_J) {
            if (item.isFyrcj == 0) {
              let operateLogic = "/"
              if(this.currentDe.quantity == 0){
                operateLogic = "*";
              }
              let specialCaculate = "+" + this.getTotalNumberKey(item.sequenceNbr)+operateLogic+this.getQuantity(this.deRowId);
              jSum += specialCaculate;
              jdSum += specialCaculate;
            }else{
              jSum +="+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
              jdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            }
          }           
        }
      }   
      
      //人材机基数
      rules[this.getRSum(this.deRowId)] = rSum;//当前定额下人的基数汇总
      rules[this.getCSum(this.deRowId)] = cSum;//当前定额下材的基数汇总
      rules[this.getJSum(this.deRowId)] = jSum;//当前定额下机的基数汇总
      rules[this.getSSum(this.deRowId)] = sSum;//当前定额下材的基数汇总
      rules[this.getZSum(this.deRowId)] = zSum;//当前定额下机的基数汇总

      rules[this.getRDSum(this.deRowId)] = rdSum;//当前定额下人的基数汇总
      rules[this.getCDSum(this.deRowId)] = cdSum;//当前定额下人的基数汇总
      rules[this.getJDSum(this.deRowId)] = jdSum;//当前定额下人的基数汇总
      rules[this.getZDSum(this.deRowId)] = zdSum;//当前定额下人的基数汇总
      rules[this.getSDSum(this.deRowId)] = sdSum;//当前定额下人的基数汇总
      //市场价
      rules[this.getRTotalSum(this.deRowId)] = this.getRSum(this.deRowId)+"*quantity";     //当前定额下人的基数汇总
      rules[this.getCTotalSum(this.deRowId)] = this.getCSum(this.deRowId)+"*quantity";     //当前定额下材的基数汇总
      rules[this.getJTotalSum(this.deRowId)] = this.getJSum(this.deRowId)+"*quantity";     //当前定额下机的基数汇总
      rules[this.getSTotalSum(this.deRowId)] = this.getSSum(this.deRowId)+"*quantity";     //当前定额下材的基数汇总
      rules[this.getZTotalSum(this.deRowId)] = this.getZSum(this.deRowId)+"*quantity";     //当前定额下机的基数汇总
      //基期价
      rules[this.getRdTotalSum(this.deRowId)] = this.getRDSum(this.deRowId)+"*quantity";     //当前定额下人的基数 定额价汇总
      rules[this.getCdTotalSum(this.deRowId)] = this.getCDSum(this.deRowId)+"*quantity";     //当前定额下材的基数 定额价汇总
      rules[this.getJdTotalSum(this.deRowId)] = this.getJDSum(this.deRowId)+"*quantity";     //当前定额下机的基数 定额价汇总
      rules[this.getSdTotalSum(this.deRowId)] = this.getSDSum(this.deRowId)+"*quantity";     //当前定额下材的基数 定额价汇总
      rules[this.getZdTotalSum(this.deRowId)] = this.getZDSum(this.deRowId)+"*quantity";     //当前定额下机的基数 定额价汇总
      //单价
      // if(deRow.type === DeTypeConstants.DE_TYPE_DE) {
      // if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE)
      // {
      //   rules[this.getPriceKey(this.deRowId)] = DERules["price"].mathFormula;
      // }
      // else
      // {
      if ((deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB)
        && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)) {
      
        rules[this.getPriceKey(this.deRowId)] = this.getZSum(this.deRowId)+'+'+this.getSSum(this.deRowId);
        rules[this.getBaseJournalPriceKey(this.deRowId)] = this.getZDSum(this.deRowId)+'+'+this.getSDSum(this.deRowId);
    
      }else{
        rules[this.getPriceKey(this.deRowId)] = this.getRSum(this.deRowId)+'+'+this.getCSum(this.deRowId)+'+'+this.getJSum(this.deRowId);
        rules[this.getBaseJournalPriceKey(this.deRowId)] = this.getRDSum(this.deRowId)+'+'+this.getCDSum(this.deRowId)+'+'+this.getJDSum(this.deRowId);
      }
      // }
      //合价(当前定额的工程量 * 当前定额的单价)
      rules[this.getTotalNumberKey(this.deRowId)] = this.getPriceKey(this.deRowId) + "*quantity";      
      rules[this.getBaseJournalTotalNumberKey(this.deRowId)] = this.getBaseJournalPriceKey(this.deRowId) + "*quantity";

      // }
      // }
    } else {
        let linshijisuanR = 0;
        let linshijisuanC = 0;
        let linshijisuanJ = 0;
      //如果是装饰超高定额，先算合价=人材机合价，单价=合价/工程量
      //如果定额是主材或者设备定额，需要计算主材和设备
      if ((deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB)
          && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)) {
        subResource.forEach(item => {
          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
          //默认数据
          let marketPriceKey = this.getRcjMarketPriceKey(item.sequenceNbr);
          let baseJournalPriceKey = this.getRcjBaseJournalPriceKey(item.sequenceNbr);
          rules[this.getRcjBaseJournalPriceKey(item.sequenceNbr)] = DERules["baseJournalPrice"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          //含税覆盖
          if(this.isPricingTax){
            marketPriceKey = this.getRcjMarketTaxPriceKey(item.sequenceNbr);
            baseJournalPriceKey = this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr);
            rules[this.getRcjMarketTaxPriceKey(item.sequenceNbr)] = DERules["marketTaxPrice"].mathFormula;
            rules[this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr)] = DERules["baseJournalTaxPrice"].mathFormula;
          }
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          // dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
          // deDPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
          if(item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+1*" + marketPriceKey;
            zdSum += "+1*" + baseJournalPriceKey;
          }else{
            sSum += "+1*" + marketPriceKey;
            sdSum += "+1*" + baseJournalPriceKey;
          }
        })
      } else {
        subResource.forEach(item => {
          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
          //默认数据
          let marketPriceKey = this.getRcjMarketPriceKey(item.sequenceNbr);
          let baseJournalPriceKey = this.getRcjBaseJournalPriceKey(item.sequenceNbr);
          rules[this.getRcjBaseJournalPriceKey(item.sequenceNbr)] = DERules["baseJournalPrice"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          //含税覆盖
          if(this.isPricingTax){
            marketPriceKey = this.getRcjMarketTaxPriceKey(item.sequenceNbr);
            baseJournalPriceKey = this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr);
            rules[this.getRcjMarketTaxPriceKey(item.sequenceNbr)] = DERules["marketTaxPrice"].mathFormula;
            rules[this.getRcjBaseJournalTaxPriceKey(item.sequenceNbr)] = DERules["baseJournalTaxPrice"].mathFormula;
          }
          rules[this.getRcjTotal(item.sequenceNbr)] = DERules["total"].mathFormula;
         
          //乱改，兼容统一处理
          if (item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
            zdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
           
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          if(item.kind == ResourceKindConstants.INT_TYPE_SB) {
            sSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
            sdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
           
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          // 修改item.kind 赋值为字符串，此处与int比较不了 暂时用if==
          // switch(item.kind){
          if (item.kind == ResourceKindConstants.INT_TYPE_R) {
            if (!ZSFeeConstants.ZS_RCJ_LIST.includes(item.materialCode)) {
              //非降效系数的人材机单价 = 消耗量*市场价
              rSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" +marketPriceKey;
              // dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
              // deDPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            } else {
              //效系数的人材机单价 = 父级定额同级的定额所有人材机 （消耗量*定额价） * 人材机消耗量 /100
              let deParentChildDeList = deList.filter(p => p.parentId === deRow.parentId && !ZSFeeConstants.ZS_DE_LIST.includes(p.deCode) && p.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE && p.deRowId !== deRow.deRowId);
              //2统计平级定额的计算基数 各自人工/机械定额价*各自消耗量）*除最父级外，其余各父级定额的消耗量
              let baseCount = 0;
              baseCount = this.calRSum(deParentChildDeList, deList, 0, 1);
              let number = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.multiplyParams(baseCount, item.resQty), 100), 5);
              linshijisuanR = NumberUtil.add(linshijisuanR,number);
            }

            rdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            // break;
          }
          if (item.kind == ResourceKindConstants.INT_TYPE_C || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10) {
            // dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" +marketPriceKey;
            // deDPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;

            cSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
            cdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            // break;
          }
          if (item.kind == ResourceKindConstants.INT_TYPE_J) {
            if (!ZSFeeConstants.ZS_RCJ_LIST.includes(item.materialCode)) {
              //非降效系数的人材机单价 = 消耗量*市场价
              jSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
              // dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + marketPriceKey;
              // deDPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            } else {
              //效系数的人材机单价 = 父级定额同级的定额所有人材机 （消耗量*定额价） * 人材机消耗量 /100
              let deParentChildDeList = deList.filter(p => p.parentId === deRow.parentId && !ZSFeeConstants.ZS_DE_LIST.includes(p.deCode) && p.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE && p.deRowId !== deRow.deRowId);
              //2统计平级定额的计算基数 各自人工/机械定额价*各自消耗量）*除最父级外，其余各父级定额的消耗量
              let baseCount = 0;
              baseCount = this.calJSum(deParentChildDeList, deList, 0, 1);
              let number = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.multiplyParams(baseCount, item.resQty), 100), 5);
              linshijisuanJ = NumberUtil.add(linshijisuanJ,number);
            }
            jdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + baseJournalPriceKey;
            // break;
          }
          // }

        });
      }

      linshijisuanR = NumberUtil.numberScale(linshijisuanR,2);
      linshijisuanC = NumberUtil.numberScale(linshijisuanC,2);
      linshijisuanJ = NumberUtil.numberScale(linshijisuanJ,2);
      rSum = linshijisuanR + rSum.slice(1);
      cSum = linshijisuanC + cSum.slice(1);
      jSum = linshijisuanJ + jSum.slice(1);

      rules[this.getRSum(this.deRowId)] = rSum;
      rules[this.getCSum(this.deRowId)] = cSum;
      rules[this.getJSum(this.deRowId)] = jSum;
      rules[this.getSSum(this.deRowId)] = sSum;
      rules[this.getZSum(this.deRowId)] = zSum;
      rules[this.getRDSum(this.deRowId)] = rdSum;//当前定额下人的基数汇总
      rules[this.getCDSum(this.deRowId)] = cdSum;//当前定额下材的基数汇总
      rules[this.getJDSum(this.deRowId)] = jdSum;//当前定额下材的基数汇总
      rules[this.getZDSum(this.deRowId)] = zdSum;//当前定额下材的基数汇总
      rules[this.getSDSum(this.deRowId)] = sdSum;//当前定额下材的基数汇总

      //人材机基数
      rules[this.getRTotalSum(this.deRowId)] = "(" + rSum + ")" +"*quantity";//当前定额下人的基数汇总
      rules[this.getCTotalSum(this.deRowId)] = cSum + "*quantity";//当前定额下材的基数汇总
      rules[this.getJTotalSum(this.deRowId)] = "(" + jSum + ")" + "*quantity";//当前定额下机的基数汇总
      rules[this.getSTotalSum(this.deRowId)] = sSum + "*quantity";//当前定额下材的基数汇总
      rules[this.getZTotalSum(this.deRowId)] = zSum + "*quantity";//当前定额下机的基数汇总
      rules[this.getRdTotalSum(this.deRowId)] = rdSum + "*quantity";//当前定额下人的基数 定额价汇总
      rules[this.getCdTotalSum(this.deRowId)] = cdSum + "*quantity";//当前定额下材的基数 定额价汇总
      rules[this.getJdTotalSum(this.deRowId)] = jdSum + "*quantity";//当前定额下机的基数 定额价汇总
      rules[this.getZdTotalSum(this.deRowId)] = zdSum + "*quantity";//当前定额下材的基数 定额价汇总
      rules[this.getSdTotalSum(this.deRowId)] = sdSum + "*quantity";//当前定额下机的基数 定额价汇总

      // let linshijisuan = NumberUtil.addParams(linshijisuanR,linshijisuanC,linshijisuanJ);
      // linshijisuan = NumberUtil.numberScale(linshijisuan,2);
      // dePriceRules = linshijisuan + dePriceRules.slice(1);
      // deDPriceRules = linshijisuan + deDPriceRules.slice(1);
      if ((deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB)
        && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)) {
          rules[this.getPriceKey(this.deRowId)] = this.getSSum(this.deRowId)+'+'+this.getZSum(this.deRowId);
          rules[this.getBaseJournalPriceKey(this.deRowId)] =  this.getZDSum(this.deRowId)+'+'+this.getSDSum(this.deRowId);
        }else{
          rules[this.getPriceKey(this.deRowId)] = this.getRSum(this.deRowId)+'+'+this.getCSum(this.deRowId)+'+'+this.getJSum(this.deRowId);
          rules[this.getBaseJournalPriceKey(this.deRowId)] =  this.getRDSum(this.deRowId)+'+'+this.getCDSum(this.deRowId)+'+'+this.getJDSum(this.deRowId);

        }
      rules[this.getTotalNumberKey(this.deRowId)] = this.getPriceKey(this.deRowId) + "*quantity";
      rules[this.getBaseJournalTotalNumberKey(this.deRowId)] = this.getBaseJournalPriceKey(this.deRowId) + "*quantity";
    }

    this.loadRules(rules);
  }

  _checkCkind(item,noItem){
    return (item.kind == ResourceKindConstants.INT_TYPE_C || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10)
    &&(noItem.kind == ResourceKindConstants.INT_TYPE_C || noItem.kind == 6 || noItem.kind == 7 || noItem.kind == 8 || noItem.kind == 9 || noItem.kind == 10)
  
  }

  calRSum(deParentChildDeList, deList, RSum, level) {
    for (let item of deParentChildDeList) {
      if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
          || item.type === DeTypeConstants.DE_TYPE_DELIST) {
        RSum = NumberUtil.add(RSum, item.RSum);
      }
    }
    return RSum;
  }

  calJSum(deParentChildDeList, deList, JSum, level) {
    for (let item of deParentChildDeList) {
      if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
          || item.type === DeTypeConstants.DE_TYPE_DELIST) {
        JSum = NumberUtil.add(JSum, item.JSum);
      }
    }
    return JSum;
  }

  getValue({type,kind,column})
  {
    let value;
    switch (type) {
      case `De`:{
        if (typeof column == 'function') {
          value = column({ de: this.currentDe});
        } else {
          value = this.currentDe[column];
           //计算过程处理位数
          let precisionObj = DeUtils.getPrecisionByDe(this.precision, this.currentDe);
          let digitalKey = DeUtils.convertDigitalKey(column);
          let digital = precisionObj[digitalKey];
          if(ObjectUtils.isNotEmpty(digital)){
            value = NumberUtil.numberScale(value,digital);
          }
        }
        break;
      }

      default:{
        value = {type,kind,column};
        break;
      }
    }
    return value;
  }
  getRuntimeValue({type,kind,column},param)
  {
    let value= 0;
    let key = param.split(DeCalculator.SPLITOR)[1];
    let item  = this.ctx.resourceMap.asArray().map(item => item[1]).find(item => item.sequenceNbr === key);
    switch (type) {
      case `item`: {
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
        //计算过程处理位数
        let precisionObj = DeUtils.getPrecisionByRcj(this.precision);
        let digitalKey = column;
        if(["total","totalTax", "baseJournalTotal", "baseJournalTotalTax"].includes(digitalKey)){
          digitalKey='total';
        }
        let digital = precisionObj[digitalKey];        
        if(ObjectUtils.isNotEmpty(digital)){
          value = NumberUtil.numberFormat(value,digital);
        }
        break;
      }
      case `de`: {
        let item = this.ctx.allDeMap.getNodeById(key);
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
        //计算过程处理位数
        let precisionObj = DeUtils.getPrecisionByDe(this.precision,item);
        let digitalKey = DeUtils.convertDigitalKey(column);
        let digital = precisionObj[digitalKey];
        if(ObjectUtils.isNotEmpty(digital)){
          value = NumberUtil.numberFormat(value,digital);
        }
        break;
      }
    }    

    return value;
  }
  /**
   * 定额价之和
   * @param {*} sequenceNbr 
   * @returns 
   */
  getRdTotalSum = (sequenceNbr) => {
    return "rdTotalSum_" + sequenceNbr;
  }
  getCdTotalSum = (sequenceNbr) => {
    return "cdTotalSum_" + sequenceNbr;
  }
  getJdTotalSum = (sequenceNbr) => {
    return "jdTotalSum_" + sequenceNbr;
  }
  getZdTotalSum = (sequenceNbr) => {
    return "zdTotalSum_" + sequenceNbr;
  }
  getSdTotalSum = (sequenceNbr) => {
    return "sdTotalSum_" + sequenceNbr;
  }
  getDePriceKey = (sequenceNbr)=>{
    return "dePrice_" + sequenceNbr;
  }

  getRTotalSum = (sequenceNbr) => {
    return "rTotalSum_" + sequenceNbr;
  }
  getCTotalSum = (sequenceNbr) => {
    return "cTotalSum_" + sequenceNbr;
  }
  getJTotalSum = (sequenceNbr) => {
    return "jTotalSum_" + sequenceNbr;
  }
  getSTotalSum = (sequenceNbr) => {
    return "sTotalSum_" + sequenceNbr;
  }
  getZTotalSum = (sequenceNbr) => {
    return "zTotalSum_" + sequenceNbr;
  }
  getRSum = (sequenceNbr) => {
    return "RSum_" + sequenceNbr;
  }
  getJSum = (sequenceNbr) => {
    return "JSum_" + sequenceNbr;
  }
  getCSum = (sequenceNbr) => {
    return "CSum_" + sequenceNbr;
  }
  getSSum = (sequenceNbr) => {
    return "SSum_" + sequenceNbr;
  }
  getZSum = (sequenceNbr) => {
    return "ZSum_" + sequenceNbr;
  }
  getTotalNumberKey = (sequenceNbr) => {
    return "totalNumber_" + sequenceNbr;
  }
  getBaseJournalTotalNumberKey = (sequenceNbr) => {
    return "baseJournalTotalNumber_" + sequenceNbr;
  }
  getRcjResQtyKey = (sequenceNbr) => {
    return "resQty_" + sequenceNbr;
  }
  getRcjMarketPriceKey = (sequenceNbr) => {
    return "marketPrice_" + sequenceNbr;
  }
  getRcjBaseJournalPriceKey = (sequenceNbr) => {
    return "baseJournalPrice_" + sequenceNbr;
  }
  getRcjMarketTaxPriceKey = (sequenceNbr) => {
    return "marketTaxPrice_" + sequenceNbr;
  }
  getRcjBaseJournalTaxPriceKey = (sequenceNbr) => {
    return "baseJournalTaxPrice_" + sequenceNbr;
  }  
  getRcjDePriceKey = (sequenceNbr) => {
    return "dePrice_" + sequenceNbr;
  }
  getPriceKey = (sequenceNbr) => {
    return "price_" + sequenceNbr;
  }
  getBaseJournalPriceKey = (sequenceNbr) => {
    return "baseJournalPrice_" + sequenceNbr;
  }
  getQuantity = (sequenceNbr) => {
    return "quantity_" + sequenceNbr;
  }
  getRcjTotal = (sequenceNbr) => {
    return "total_" + sequenceNbr;
  }
  getRDSum = (sequenceNbr) => {
    return "RDSum_" + sequenceNbr;
  }
  getCDSum = (sequenceNbr) => {
    return "CDSum_" + sequenceNbr;
  }
  getJDSum = (sequenceNbr) => {
    return "JDSum_" + sequenceNbr;
  }
  getSDSum = (sequenceNbr) => {
    return "SDSum_" + sequenceNbr;
  }
  getZDSum = (sequenceNbr) => {
    return "ZDSum_" + sequenceNbr;
  }
  /**
   * 是否计算费用人材机的totalNumber和total
   * @returns 
   */
  isCaculatorCost(){
    if ([CostDeMatchConstants.CG_DE,
        CostDeMatchConstants.AZ_DE,
        CostDeMatchConstants.FXTJ_CG,
        CostDeMatchConstants.FXTJ_CZYS,
        CostDeMatchConstants.FXTJ_ZXXJX,
        CostDeMatchConstants.GJMQ_DE_CG,
        CostDeMatchConstants.GJMQ_DE_CZYS,
        CostDeMatchConstants.GJMQ_DE_ZXXJX,
        CostDeMatchConstants.FGJZ_DE_ZXXJX,
        CostDeMatchConstants.ZJCS_DE,
        CostDeMatchConstants.FXTJ_GCSDF].includes(this.currentDe.isCostDe)){
      return false;
    }
    return true;
  }


}
module.exports = {DeCalculator};