import { Column, Entity } from 'typeorm';
import { BaseModel } from './BaseModel';

@Entity({ name: "base_rcj_2022" })
export class BaseRcj2022 extends BaseModel {

    @Column({ name: "level1", length: 50, nullable: true })
    public level1: string; // 层级一

    @Column({ name: "level2", length: 50, nullable: true })
    public level2: string; // 层级二

    @Column({ name: "level3", length: 50, nullable: true })
    public level3: string; // 层级三

    @Column({ name: "level4", length: 50, nullable: true })
    public level4: string; // 层级四

    @Column({ name: "level5", length: 50, nullable: true })
    public level5: string; // 层级五

    @Column({ name: "level6", length: 50, nullable: true })
    public level6: string; // 层级六

    @Column({ name: "level7", length: 50, nullable: true })
    public level7: string; // 层级七

    @Column({ name: "material_code", length: 50, nullable: true })
    public materialCode: string; // 编码

    @Column({ name: "kind", type: "int", nullable: true })
    public kind: number; // kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)

    @Column({ name: "material_name", length: 100, nullable: true })
    public materialName: string; // 名称

    @Column({ name: "specification", length: 500, nullable: true })
    public specification: string; // 规格及型号

    @Column({ name: "unit", length: 10, nullable: true })
    public unit: string; // 单位

    @Column({ name: "price_base_journal", type: "decimal", nullable: true })
    public baseJournalPrice: number;

    @Column({ name: "price_base_journal_tax", type: "decimal", nullable: true })
    public baseJournalTaxPrice: number;

    @Column({ name: "price_market", type: "decimal", nullable: true })
    public marketPrice: number;

    @Column({ name:"price_market_tax", type:"decimal", nullable : true})
    public marketTaxPrice: number;

    @Column({ name:"library_code", length : 50 , nullable : true})
    public libraryCode: string; // 定额册编码

    @Column({ name:"level_mark" , length : 10 , nullable : true})
    public levelMark: string; // 是否下沉标识（2：下沉机械；1：下沉配比；0：无需下沉）

    @Column({ name:"sort_no", type:"bigint", nullable:true })
    public sortNo: number; // 排序序号

    @Column({ name:"is_change_ava", type:"int", nullable:true })
    public isChangeAva: number; // 0 不可更改（既基价为-），1 可更改

    @Column({ name:"is_fyrcj", type:"int", nullable:true })
    public isFyrcj: number; // 是否费用人材机(0:是，1否)

    @Column({ name:"kind_sc" , length : 255 , nullable : true})
    public kindSc: string; // 三材分类：钢材、水泥、商砼、钢筋、木材、商品砂浆

    @Column({ name:"transfer_factor" , length : 255 , nullable : true})
    public transferFactor: string; // 三材转化系数

    @Column({ name:"tax_rate" , type:"decimal", nullable:true})
    public taxRate: number; // 税率

    @Column({ name:"is_data_tax_rate" , type :"int" ,nullable:true})
    public isDataTaxRate: number; // 税率是否为数值（0 为 -，1为数值,2为空）

    @Column({ name: "price_base_journal_tax", type: "decimal", nullable: true })
    public price: number;

    @Column({ name: "tax_rate", type: "decimal", nullable: true })
    public taxRemoval: number;

}