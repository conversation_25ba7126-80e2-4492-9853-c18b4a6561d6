const { ObjectUtil } = require('../../../common/ObjectUtil');
const { Service } = require('../../../core');
const { dialog } = require('electron');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const { JieSuanFileUtils } = require('../utils/JieSuanFileUtils');
const { AccordingFileVo } = require('../model/AccordingFileVo');
const path = require('path');


class JieSuanAccordingFileService extends Service {


  constructor(ctx) {
    super(ctx);
  }

  /**
   * 上传依据文件
   */
  async uploadAccordingFile(args) {
    let { constructId, singleId, unitId, pointLineId } = args;
    // 获取默认的存储路径
    let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
    const options = {
      properties: ['openFile'],
      //defaultPath: defaultStoragePath.toString(), // 默认保存路径
      defaultPath: defaultStoragePath, // 默认保存路径
      filters: [
        { name: '依据文件', extensions: ['pdf', 'xlsx', 'docx', 'png', 'jpg'] } // 可选的文件类型
      ]
    };
    let result = dialog.showOpenDialogSync(null, options);
    if (ObjectUtil.isEmpty(result)) {
      console.log('未选中任何文件');
      return;
    }
    //获取选中的路径
    let filePath = result[0];
    if (!await this.service.constructProjectService.checkFileExistence(filePath)) {
      console.log('路径有误');
      return;
    }
    // 上传文件至YJS文件
    let fileInfo = await JieSuanFileUtils.getFileInfo(filePath);
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const pointLine = unit.itemBillProjects.getNodeById(pointLineId);
    if (ObjectUtil.isEmpty(pointLine.accordingFiles)) {
      pointLine.accordingFiles = [];
    }
    const existsFile = pointLine.accordingFiles.find(accordingFile => accordingFile.fileName == fileInfo.fileName);
    if (ObjectUtil.isNotEmpty(existsFile)) {
      throw new Error('上传的文件已存在');
    }
    let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
    await JieSuanFileUtils.copyFile2YJS(filePath, projectObj.path, 'accordingFile/' + `${constructId}${singleId}${unitId}${pointLineId}`);
    // 依据文件同名覆盖
    pointLine.accordingFiles = pointLine.accordingFiles.filter(accordingFile => accordingFile.fileName !== fileInfo.fileName);
    pointLine.accordingFiles.push(new AccordingFileVo(fileInfo.fileName, fileInfo.fileSize));
    return pointLine.accordingFiles;
  }

  /**
   * 删除依据文件
   */
  async deleteAccordingFile(args) {
    let { constructId, singleId, unitId, pointLineId, fileName } = args;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let pointLine = unit.itemBillProjects.getNodeById(pointLineId);
    //YJS文件删除
    let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
    await JieSuanFileUtils.deleteYJSFile(projectObj.path, path.join('accordingFile/' + `${constructId}${singleId}${unitId}${pointLineId}`, fileName));

    pointLine.accordingFiles = pointLine.accordingFiles.filter(accordingFile => accordingFile.fileName !== fileName);
    return pointLine.accordingFiles;
  }

  /**
   * 打开依据文件
   */
  async openAccordingFile(args) {
    let { constructId, singleId, unitId, pointLineId, fileName } = args;
    let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
    let filePath = 'accordingFile/' + `${constructId}${singleId}${unitId}${pointLineId}`;
    let tempDir = defaultStoragePath + '/' + filePath;
    let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
    await JieSuanFileUtils.openAccordingFile(tempDir, projectObj.path, path.join(filePath, fileName));
  }

}


JieSuanAccordingFileService.toString = () => '[class JieSuanAccordingFileService]';
module.exports = JieSuanAccordingFileService;