<template>
  <div class="de-description-info" v-if="deData">
    <div class="header">
      <span class="taxTitle">定额相关描述</span>
    </div>
    <div class="basic-content">
      <span v-if="deData.jobContent">工作内容：{{ deData.jobContent }}</span>
      <span v-if="deData.remark">附注信息：{{ deData.remark }}</span>
      <span>计量单位：{{ deData.unit }}</span>
    </div>
    <div class="table-content">
      <div class="basic-info">
        <span>定额编码</span>
        <span>{{ deData.deCode }}</span>
      </div>
      <div class="basic-info">
        <span>项目名称</span>
        <span>{{ deData.deName }}</span>
      </div>
      <div class="basic-info">
        <span>基价</span>
        <span>{{ deData.basePrice }}</span>
      </div>
      <div class="base-data-list">
        <div class="title">其中</div>
        <div class="list first-list">
          <span v-for="(key, index) in deData.baseData" :key="index">{{
            getDataName(Object.keys(key)[0])
          }}</span>
        </div>
        <div class="list">
          <span v-for="(value, index) in deData.baseData" :key="index">{{
            Object.values(value)[0]
          }}</span>
        </div>
      </div>
      <div class="rcj-list">
        <div class="data-header">
          <span>名称</span>
          <span>单位</span>
          <span>单价(元)</span>
          <span>消耗量</span>
        </div>
        <div class="content" v-for="(item, idx) in deData.rcjData" :key="idx">
          <span class="title">{{ getRcjType(item[0].kind) }}</span>
          <div class="single-item">
            <div class="left-content" v-for="data in item">
              <span>{{ data.materialName }}</span>
              <span>{{ data.unit }}</span>
              <span>{{ handleDePrice(data) }}</span>
              <span>{{ data.resQty }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { projectDetailStore } from '../../../../store/projectDetail';
import api from '@/api/projectDetail';
const projectStore = projectDetailStore();
const props = defineProps(['currentInfo', 'type']);
const deData = ref();

const getDataName = value => {
  if (value === '人工费') {
    return '人工费(元)';
  } else if (value === '材料费') {
    return '材料费(元)';
  } else if (value === '机械费') {
    return '机械费(元)';
  } else if (value === '主材费') {
    return '主材费(元)';
  }
  return '';
};

const getRcjType = value => {
  if (value === 1) {
    return '人工';
  } else if (value === 2) {
    return '材料';
  } else if (value === 3) {
    return '机械';
  } else if (value === 5) {
    return '主材';
  }
  return '';
};

onMounted(() => {
  console.log('props', props.currentInfo);
  queryDeDescribe();
});

watch(
  () => props.currentInfo.standardId,
  () => {
    queryDeDescribe();
  }
);

const queryDeDescribe = () => {
  if (props.currentInfo.rcjFlag === 1) {
    deData.value = null;
    return;
  }
  let apiData = {
    standardDeId: props.currentInfo.standardId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    branchType: props.type,
    libraryCode: props.currentInfo.libraryCode,
  };
  console.log('apiData', apiData, props.currentInfo);
  if (!apiData.standardDeId) {
    deData.value = null;
    return;
  }
  api.queryDeDescribe(apiData).then(res => {
    console.log('是否进来。。。', res);
    if (res.status === 200 && res.result) {
      deData.value = res.result;
    }
  });
};

const handleDePrice = data => {
  let filed = 'dePrice';

  if (data.libraryCode.startsWith('2022')) {
    filed =
      projectStore.taxMade == 1 ? 'priceBaseJournal' : 'priceBaseJournalTax';
  }
  return data[filed];
};
</script>

<style lang="scss" scoped>
.de-description-info {
  font-size: 12px;
  width: 33%; //明细去留白样式调整
  background: #ffffff;
  box-shadow: -7px 0px 4px 0px rgba(0, 0, 0, 0.06);
  padding: 13px;
  overflow: hidden;
  &:hover {
    overflow: auto;
  }
  .header {
    position: relative;
    margin-bottom: 15px;
    .taxTitle {
      position: relative;
      color: #2a2a2a;
      text-align: left;
      padding-left: 7px;
      line-height: 16px;
      font-size: 14px;
      border-left: 2px solid #287cfa;
    }
  }
  .basic-content {
    span {
      display: block;
    }
  }
  .table-content {
    border: 1px solid #eeeeee;
    margin-top: 10px;
    .basic-info {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #eeeeee;
      span {
        width: 50%;
        text-align: center;
        border-right: 1px solid #eeeeee;
        padding: 10px 0;
      }
      span:nth-last-of-type(1) {
        border-right: none;
      }
    }
    .basic-info:nth-of-type(1),
    .basic-info:nth-of-type(3) {
      background: #f3f3f3;
    }
    .base-data-list {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eeeeee;
      .title {
        width: 15px;
      }
      .list {
        border-left: 1px solid #eeeeee;
        width: 50%;
        span {
          display: block;
          padding: 5px 0;
          text-align: center;
        }
      }
      .first-list {
        width: calc(50% - 16px);
      }
    }
    .rcj-list {
      .data-header {
        border-bottom: 1px solid #eeeeee;
        background: #f3f3f3;
        span {
          display: inline-block;
          text-align: center;
          border-right: 1px solid #eeeeee;
          padding: 5px 0;
        }
        span:nth-of-type(1) {
          width: 25%;
        }
        span:nth-of-type(2) {
          width: 10%;
        }
        span:nth-of-type(3) {
          width: 15%;
        }
        span:nth-of-type(4) {
          width: 50%;
          border-right: none;
        }
      }
      .content {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eeeeee;
        .title {
          display: inline-block;
          width: 15px;
          line-height: 20px;
        }
        .single-item {
          display: flex;
          width: 100%;
          flex-direction: column;
          border-left: 1px solid #eeeeee;
          span {
            display: block;
            text-align: center;
            //border-right: 1px solid #eeeeee;
            padding: 5px 0;
          }
          .left-content {
            display: flex;
            width: 100%;
            span {
              display: inline-block;
              text-align: center;
              border-right: 1px solid #eeeeee;
              padding: 5px 0;
            }
            span:nth-of-type(1) {
              width: calc(25% - 13px);
            }
            span:nth-of-type(2) {
              width: calc(10% + 2px);
            }
            span:nth-of-type(3) {
              width: calc(15% + 2px);
            }
            span:nth-of-type(4) {
              width: 50%;
              border-right: none;
            }
          }
        }
      }
    }
  }
}
</style>
