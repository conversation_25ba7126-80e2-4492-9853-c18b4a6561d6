const TaxCalculationMethodEnum = require("../../../../electron/enum/TaxCalculationMethodEnum");
const {ConvertUtil} = require("../../../../electron/utils/ConvertUtils");

const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const {PricingFileFindUtils} = require("../../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const {Service} = require("../../../../core");
class JieSuanExportQueryOtherService extends Service{
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 表1-1 建设项目费用汇总表
     */
    async buildingProjectsCostSummary(args){

        let singleProjectList = await PricingFileFindUtils.getSingleProjectList(args.constructId)
        let res = await this.getAllSingleProject_sbfTax(singleProjectList)
        return res
    }
    /**
     * 表1-2 建设项目费用汇总表(沧州)
     */
    async buildingProjectsCostSummary1_2(args){

        let singleProjectList = await PricingFileFindUtils.getSingleProjectList(args.constructId)
        let res = await this.getAllSingleProject_sbfTax(singleProjectList)


        return await this.addSafe(args,res)
    }

    /**
     * 1-3 工程项目竣工结算汇总表
     */
    async buildingProjectsCostSummary1_3(args){

        let singleProjectList = await PricingFileFindUtils.getSingleProjectList(args.constructId)
        let res = await this.getAllSingleProject_sbfTax_1_3(singleProjectList)


        return res
    }
    async buildingProjectsCostSummary1_4(args){

        let res = await this.buildingProjectsCostSummary1_3(args)

        return  await this.addSafe(args, res);
    }

    /**
     * 增加固定安文费
     * @param args
     * @param res
     * @returns {Promise<void>}
     */
    async addSafe(args, res) {
        let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let jieSuanSecurityFee = {
            projectName: '安全生产、文明施工费（含税）',
            jck: ObjectUtils.isEmpty(projectObj.jieSuanSecurityFee) ? 0 : projectObj.jieSuanSecurityFee
        }
        res.push(jieSuanSecurityFee)
        return  res;
    }

    /**
     * 表1-2 建设项目竣工结算汇总表
     * @param args
     * @returns {Promise<void>}
     */
    async buildingProjectsCostSummary1_2gf(args){
        let singleProjectList = await PricingFileFindUtils.getSingleProjectList(args.constructId)
        let res = await this.getAllSingleProject_sbfTax(singleProjectList)
        return res
    }

    /**
     * 表-1-3 建设项目竣工结算汇总表(沧州)
     * @param args
     * @returns {Promise<void>}
     */
    async buildingProjectsCostSummary1_3gf(args){
       let res = await this.buildingProjectsCostSummary1_2gf(args)
        return await this.addSafe(args,res)
    }


    /**
     * 表1-1 单项工程竣工结算汇总表
     * @param args
     * @returns {Promise<*[]>}
     */
    async buildingProjectSingle1_1(args){
        let singleProjectList = await PricingFileFindUtils.getSingleProjectList(args.constructId)

        let singleProject = singleProjectList.find(item =>item.sequenceNbr ==args.singleId)
        let array =[]
        if(ObjectUtils.isNotEmpty(singleProject)){
            let resArray =await this.getSubSingleUnits(singleProject,array);
            array= array.concat(resArray);
        }
        return array;
    }
    async getSubSingleUnits(singleProject,array) {

        if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {  //达到最后一层包含单位的子单项
            array = array.concat(singleProject.unitProjects);
        }else {
            if(!ObjectUtils.isEmpty(singleProject.subSingleProjects)){
                for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                    array =await this.getSubSingleUnits(singleProject.subSingleProjects[i],array);
                }
            }
        }
        return array;
    }
    /**
     * 表1-1 单位工程竣工结算汇总表
     * @param args
     * @returns {Promise<T[]>}
     */
    async buildingProjectUnit1_1(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId)
        let unitCostSummarys =ConvertUtil.deepCopy(unit.unitCostSummarys);
        let unitCostSummarie_ys = unitCostSummarys.filter(i =>i.sourceFlag!=='1');
        // 计税方式获取
        let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);

        //简易计税
        if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            let jc          = unitCostSummarys.find(i =>i.type=='竣工结算价差')
            let jcgf        = unitCostSummarys.find(i =>i.type=='价差规费')
            let jcaqwmsgf  = unitCostSummarys.find(i =>i.type=='价差安全生产、文明施工费')
            let jcsj        = unitCostSummarys.find(i =>i.type=='价差税金')
            let jck         = unitCostSummarys.find(i =>i.type=='调差后工程造价')
            jc.dispNo = 1;
            jcgf.dispNo = 2;
            jcaqwmsgf.dispNo = 3;
            jcsj.dispNo = 4;
            jck.dispNo = 5;
            unitCostSummarie_ys.push(jc       )
            unitCostSummarie_ys.push(jcgf     )
            unitCostSummarie_ys.push(jcaqwmsgf)
            unitCostSummarie_ys.push(jcsj     )
            unitCostSummarie_ys.push(jck      )
        }else {
            let jchj = unitCostSummarys.find(i =>i.type=='价差取费合计');
            let jck  = unitCostSummarys.find(i =>i.type=='调差后工程造价');
            jchj.dispNo = 1;
            jck.dispNo = 2;
            unitCostSummarie_ys.push(jchj)
            unitCostSummarie_ys.push(jck)
        }

        if(ObjectUtils.isNotEmpty( unitCostSummarie_ys.find(i =>i.type=='工程造价'))){
            //lzh说序号
            unitCostSummarie_ys.find(i =>i.type=='工程造价').dispNo = ''
        }



        return unitCostSummarie_ys;
    }

    /**
     * 表1-10 规费、税金项目结算表（不含价差）
     * @param args
     * @returns {Promise<*[]>}
     */
     async buildingProjectUnit1_10(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        let unitCostSummarys = unit.unitCostSummarys;
        let res = []
        let gf = unitCostSummarys.find(i =>i.type=='规费');
        res.push(gf)
        let sj = unitCostSummarys.find(i =>i.type=='税金');
        res.push(sj)

        return  res;

    }

    /**
     * 表1-11 规费、税金项目结算表（含价差）
     * @param args
     * @returns {Promise<*[]>}
     */
     async buildingProjectUnit1_11_jc(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        let unitCostSummarys = unit.unitCostSummarys;
        let res = []
        let gf = unitCostSummarys.find(i =>i.type=='规费');
        res.push(gf)
        let sj = unitCostSummarys.find(i =>i.type=='税金');
        res.push(sj)
        let jc_gf = unitCostSummarys.find(i =>i.type=='价差规费');
        res.push(jc_gf)
        let jc_sj = unitCostSummarys.find(i =>i.type=='价差税金');
        res.push(jc_sj)
        return  res;
    }


    /**
     * 表1-1 单位工程费用汇总表
     * @param args
     * @returns {Promise<void>}
     */
    async buildingUnitCostSummarys1_1(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        let unitCostSummarys = unit.unitCostSummarys;
        let ys =unitCostSummarys.filter(i =>i.sourceFlag !=='1')
        let jchj = unitCostSummarys.find(i =>i.type=='价差取费合计');
        let jck  = unitCostSummarys.find(i =>i.type=='调差后工程造价');
        ys.push(jchj)
        ys.push(jck)
        return ys
    }

    async buildingProjectUnit1_16(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        let inputTaxDetails  =unit.inputTaxDetails
        // let unitCostCodePrices  =unit.unitCostCodePrices
        let ys= inputTaxDetails.filter(i =>Number.isInteger(i.dispNo))

        return ys.filter(i=>i.code!=='JXSETCH')


    }

    /**
     * 报表专用 包含设备费税金
     * @param singleProjectList
     * @returns {Promise<*[]>}
     */
    async getAllSingleProject_sbfTax(singleProjectList){
        let array = [];
        let sbfTaxObject ={
            projectName:'设备费及税金'
        }
        let total = 0;
        if(ObjectUtils.isNotEmpty(singleProjectList)){
            for (let i = 0; i < singleProjectList.length; i++) {
                let resArray =await this.getSonSingleProject(singleProjectList[i]);
                array= array.concat(resArray);
                total = NumberUtil.addParams(total,singleProjectList[i].sbfTax,singleProjectList[i].jsjcsbf)
            }
        }
        total = NumberUtil.numberScale2(total)
        sbfTaxObject.jck = total;
        array.push(sbfTaxObject)
        return array;
    }



    async getAllSingleProject_sbfTax_1_3(singleProjectList){
        let array = [];
        let sbfTaxObject ={
            projectName:'设备费及税金'
        }
        let total = 0;
        let jsjc = 0;
        if(ObjectUtils.isNotEmpty(singleProjectList)){
            for (let i = 0; i < singleProjectList.length; i++) {
                let resArray =await this.getSonSingleProject(singleProjectList[i]);
                array= array.concat(resArray);
                total = NumberUtil.addParams(total,singleProjectList[i].jsjcsbf)
                jsjc = NumberUtil.addParams(jsjc,singleProjectList[i].jsjc)
            }
        }
        total = NumberUtil.numberScale2(total)
        jsjc = NumberUtil.numberScale2(jsjc)
        sbfTaxObject.jck = total;
        sbfTaxObject.jsjc = jsjc;
        array.push(sbfTaxObject)
        return array;
    }


    /**
     * 报表专用 包含设备费税金
     * @param singleProjectList
     * @returns {Promise<*[]>}
     */
    async getAllSingleProject_sbfTax(singleProjectList){
        let array = [];
        let sbfTaxObject ={
            projectName:'设备费及税金'
        }
        let total = 0;
        if(ObjectUtils.isNotEmpty(singleProjectList)){
            for (let i = 0; i < singleProjectList.length; i++) {
                let resArray =await this.getSonSingleProject(singleProjectList[i]);
                array= array.concat(resArray);
                total = NumberUtil.addParams(total,singleProjectList[i].sbfTax,singleProjectList[i].jsjcsbf)
            }
        }
        total = NumberUtil.numberScale2(total)
        sbfTaxObject.jck = total;
        array.push(sbfTaxObject)
        return array;
    }

    async getSonSingleProject(singleProject){
        let array = [];
        array.push(singleProject)
        // 子单项处理
        let subSingleProjects = singleProject.subSingleProjects;
        if(!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i = 0; i < subSingleProjects.length; i++) {
                let resArray =await this.getSonSingleProject(subSingleProjects[i]);
                array = array.concat(resArray);
            }
        }
        return array;
    }



}

JieSuanExportQueryOtherService.toString = () => '[class JieSuanExportQueryOtherService]';
module.exports = JieSuanExportQueryOtherService;
