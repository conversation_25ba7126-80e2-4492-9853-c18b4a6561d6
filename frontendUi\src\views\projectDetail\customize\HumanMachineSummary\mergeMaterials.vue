<!--
 * @Descripttion: 合并相似材料
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-11-26 17:44:14
-->
<template>
  <common-modal
    className="dialog-comm mergeMaterials-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="合并相似材料"
    width="900px"
    height="600px"
    min-width="900px"
    min-height="80vh"
    :mask="true"
    show-zoom
    resize
    :lock-view="false"
    destroy-on-close
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div class="group-content-wrap">
      <div class="group-title-box">
        <div class="range-title">
          <div>
            合并条件：
            <a-radio-group
              v-model:value="prodType"
              @change="handleChangeProdType"
              :options="prodOptions"
            />
          </div>
          <div class="right-box" v-if="prodType === 2">
            <a-input
              v-model:value="searchKey"
              @pressEnter="getTableList"
              :placeholder="'请输入名称/规格型号关键字搜索'"
            >
              <template #suffix>
                <i
                  @click="getTableList"
                  class="vxe-icon-search"
                  style="color: rgba(191, 191, 191, 1)"
                ></i>
              </template>
            </a-input>
          </div>
          <div style="margin-left: auto; margin-top: -5px">
            <a-checkbox v-model:checked="allSelect" @change="filterChange()"
              >全选</a-checkbox
            >
          </div>
        </div>
      </div>
      <div class="content content-table">
        <vxe-table
          border
          ref="vexTable"
          align="center"
          :column-config="{ resizable: true }"
          :data="TableData"
          :checkStrictly="false"
          :row-class-name="rowClassName"
          height="98%"
          :row-config="{
            isCurrent: true,
            keyField: 'sequenceNbr',
          }"
          :checkbox-config="{
            showHeader: false,
            visibleMethod: ({ row }) => {
              return !['04'].includes(row.kind);
            },
            checkMethod: ({ row }) => {
              return true;
            },
          }"
          :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
          :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parent',
            showLine: true,
            showIcon: true,
            expandAll: true,
            iconOpen: 'vxe-icon-caret-down',
            iconClose: 'vxe-icon-caret-right',
          }"
          @cell-click="cellClick"
          @cell-dblclick="cellDBLClickEvent"
          :show-overflow="true"
        >
          <vxe-column field="sort" width="40" title="序号">
            <template #default="{ row, $rowIndex }">
              {{ $rowIndex + 1 }}
            </template>
          </vxe-column>
          <vxe-column field="materialCode" tree-node width="150" title="编码">
          </vxe-column>
          <vxe-column field="type" width="60" title="类别" />
          <vxe-column field="materialName" width="110" title="名称">
            <template #default="{ row }">
              <span>{{ row.materialName }}</span>
            </template>
          </vxe-column>
          <vxe-column field="specification" width="100" title="规格型号" />
          <vxe-column field="unit" width="80" title="单位"> </vxe-column>
          <vxe-column
            field="dePrice"
            :title="
              store.deType == '12'
                ? '定额价'
                : store.taxMade == 1
                ? '不含税基期价'
                : '含税基期价'
            "
          >
          </vxe-column>
          <vxe-column field="marketPrice" v-if="isDeType('12')" title="市场价">
          </vxe-column>
          <vxe-column
            v-if="isDeType('22') && store.taxMade == 1"
            field="priceMarket"
            title="不含税市场价"
          >
            <template #default="{ row, rowIndex, column }">
              {{ getValueByDeType('12', row, column.field) }}
            </template>
          </vxe-column>
          <vxe-column
            v-if="isDeType('22') && store.taxMade != 1"
            field="priceMarketTax"
            title="含税市场价"
          >
            <template #default="{ row, rowIndex, column }">
              {{ getValueByDeType('12', row, column.field) }}
            </template>
          </vxe-column>
          <vxe-column
            field="merge"
            min-width="60"
            title="参与合并"
            :cell-render="{}"
          >
            <template #default="{ row, rowIndex }">
              <vxe-checkbox
                v-model="row.cyhb"
                name="参与合并"
                @change="handleClearMergeMer(row, rowIndex)"
                v-if="row.children?.length === 0"
              ></vxe-checkbox>
            </template>
          </vxe-column>
          <vxe-column
            field="mergeMer"
            min-width="60"
            title="合并后材料"
            :cell-render="{}"
          >
            <template #default="{ row, rowIndex }">
              <vxe-radio
                v-model="row.mergeTarget"
                :name="`radio${row.parent}`"
                @change="handleChangeMergeMer(row, rowIndex)"
                v-if="row.children?.length === 0"
              ></vxe-radio>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="footer-box">
        <span class="handle-tips">
          <div>
            <icon-font type="icon-querenshanchu"></icon-font>&nbsp;提示：
            <br />
            1、如需调整人材机，请先关闭本功能
            <br />
            2、配比材料合并时，建议优先合并配比明细材料
          </div>
        </span>
        <p style="margin-bottom: 0; margin-top: 20px">
          <a-button type="primary" ghost @click="cancel()">取消</a-button>
          <a-button type="primary" style="margin-left: 14px" @click="handleOk()"
            >确认合并</a-button
          >
        </p>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, watch } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail.js';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { v4 } from 'uuid';
import { isDeType } from './tableColumns';
import { cloneDeep } from 'lodash';
const { linkagePosition } = useReversePosition();
const props = defineProps(['formData', 'list']);

const route = useRoute();
const emits = defineEmits(['closeDialog', 'refresh', 'selfTestLocateTable']);

let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);

let searchKey = ref('');
let prodType = ref(0);
let loading = ref(false);

let prodOptions = computed(() => {
  let options = [
    { label: '编码', value: 0 },
    { label: '名称', value: 1 },
  ];
  if ([3].includes(store.currentTreeInfo?.levelType)) {
    options.push({ label: '自定义', value: 2 });
  }
  return options;
});

const originalData = computed(() => {
  // 保持数据单向响应
  return cloneDeep(props.list);
});

const getValueByDeType = (deType, row, field = '') => {
  if (isDeType(deType, row.deStandardReleaseYear)) {
    if (['priceMarketTax', 'priceMarket'].includes(field)) {
      const taxMade = 'priceMarket' === field ? 1 : 0;
      if (Number(store.taxMade) === taxMade) {
        return row.marketPrice;
      }
    }
    return '/';
  }
  return row[field];
};

onMounted(() => {
  // open();
});

const handleChangeProdType = e => {
  prodType.value = e.target.value;
  searchKey.value = '';
  getTableList();
};

const cancel = (refresh = false) => {
  if (refresh) {
    emits('refresh');
  } else {
    // emits('closeDialog');
    prodType.value = 0;
    searchKey.value = '';
    dialogVisible.value = false;
  }
};

const open = k => {
  dialogVisible.value = true;
  getTableList();
};

/**
 * 工程项目——合并相似材料数据处理
 * prodType 0 编码  1 名称  2 自定义
 */
const getProjectMergeMaterials = ({ list, prodType }) => {
  return new Promise((resolve, reject) => {
    let allData = new Array();
    if (prodType === 0) {
      //分组
      const groups = arrayToGroups(list, 'materialCode');
      groupsAddParent(groups, 'materialCode', allData);
    }
    if (prodType === 1) {
      //分组
      const groups = arrayToGroups(list, 'materialName');
      groupsAddParent(groups, 'materialName', allData);
    }
    resolve({ result: allData });
  });
};
const groupsAddParent = (groups, type, allData) => {
  for (let group in groups) {
    if (groups.hasOwnProperty(group)) {
      if (groups[group].length >= 2) {
        const info = groups[group][0];
        let parentRcj = {};
        parentRcj.sequenceNbr = v4();
        parentRcj.parent = 0;
        parentRcj.name =
          type === 'materialName'
            ? info.materialName
            : info.materialCode.replace(/#\d+/g, '');
        allData.push(parentRcj);
        groups[group].forEach(item => {
          item.parent = parentRcj.sequenceNbr;
          allData.push(item);
        });
      }
    }
  }
};
const arrayToGroups = (list, type) => {
  return list.reduce((accumulator, currentValue) => {
    // 将分组作为对象的 key，相同分组的项放入同一个数组

    const key =
      type === 'materialName'
        ? currentValue.materialName
        : currentValue.materialCode.replace(/#\d+/g, '') + currentValue.markSum;
    if (!accumulator[key]) {
      accumulator[key] = [];
    }
    accumulator[key].push(currentValue);
    return accumulator;
  }, {});
};
const getTableList = () => {
  let postData = {
    ...props.formData,
    code: prodType.value == 0 ? '1' : '',
    name: prodType.value == 1 ? '1' : '',
    userDefined: searchKey.value,
  };

  let apiFun = csProject.getUnitMergeMaterials;
  if (store.currentTreeInfo?.levelType == 1) {
    apiFun = getProjectMergeMaterials;
    postData = { list: originalData.value, prodType: prodType.value };
  }
  console.log('postData', postData);
  apiFun(postData).then(res => {
    TableData.value = res.result.map((i, index) => {
      i.cyhb = false;
      i.mergeTarget = false;
      if (i.parent === 0) {
        i.parent = null;
        i.materialCode = i.name;
      }
      return i;
    });
    console.log('TableData.value', TableData.value);
    nextTick(() => {
      vexTable.value.setAllTreeExpand(true);
    });
  });
};

// 处理相同的name只有一个单选按钮能选中
const handleChangeMergeMer = (row, index) => {
  let parent = row.parent;
  row.cyhb = true;
  if (parent) {
    TableData.value.forEach(i => {
      if (i.parent == parent && i.sequenceNbr != row.sequenceNbr) {
        i.mergeTarget = false;
      }
    });
  }
};
const handleClearMergeMer = (row, index) => {
  if (row.mergeTarget === null && !row.cyhb) {
    row.mergeTarget = false;
  }
};
let allSelect = ref(false);
const filterChange = () => {
  let data = TableData.value;
  data.forEach(i => {
    i.cyhb = allSelect.value;
  });
};
// 双击事件
const cellDBLClickEvent = async ({ row }) => {
  selfTestLocate(row);
};
const selfTestLocate = row => {
  emits('selfTestLocateTable', row);
};

const rowClassName = ({ row }) => {
  if (row.parentId == 1) {
    return 'row-tree-title';
  }
};

// 确认合并
const handleOk = () => {
  let rcjs = {};
  let rcjsList = JSON.parse(JSON.stringify(TableData.value));
  rcjsList.forEach(i => {
    if (i.cyhb && i.parent) {
      if (![false].includes(i.mergeTarget)) {
        i.mergeTarget = true;
      }

      if (!rcjs[i.parent]) {
        rcjs[i.parent] = [{ ...i, children: [] }];
      } else {
        rcjs[i.parent].push({ ...i, children: [] });
      }
    }
  });

  let errStatus = false;

  let postData = {
    levelType: store.currentTreeInfo?.type,
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    rcjs: [],
  };
  if (!Object.values(rcjs)?.length) {
    message.error('当前无符合条件的合并项');
    return;
  }
  // 判断材料类别
  for (let i of Object.values(rcjs)) {
    let typeStatus = true;
    let levelMarkStatus = true;
    let mergeTargetStatus = false;
    if (i.length > 1) {
      postData.rcjs.push(i);
      typeStatus = i.every(j => j.kind == i[0].kind);
      levelMarkStatus = i.every(j => j.levelMark == i[0].levelMark);
      mergeTargetStatus = i.every(j => j.mergeTarget == i[0].mergeTarget);
    } else {
      console.log('🚀 ~ handleOk ~ i:', i);
      mergeTargetStatus = !i[0].mergeTarget;
    }

    if (!typeStatus) {
      errStatus = true;
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '材料类别不同不支持合并！',
        confirm: () => {
          infoMode.hide();
        },
      });
      break;
    }

    if (!levelMarkStatus) {
      errStatus = true;
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '选中数据中包含配比数据，不支持合并！',
        confirm: () => {
          infoMode.hide();
        },
      });
      break;
    }

    if (mergeTargetStatus) {
      errStatus = true;
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '请选择合并后材料！',
        confirm: () => {
          infoMode.hide();
        },
      });
      break;
    }
    console.log(
      '🚀 人材机列表',
      Object.values(rcjs),
      Object.values(rcjs).some(subArr => subArr.length > 1)
    );
    if (!Object.values(rcjs).some(subArr => subArr.length > 1)) {
      errStatus = true;
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '当前无符合条件合并项！',
        confirm: () => {
          infoMode.hide();
        },
      });
      break;
    }
  }

  if (errStatus) return;
  console.log('🚀 ~ handleOk ~ postData:', postData);
  if (store.currentTreeInfo.levelType == 1) {
    infoMode.show({
      iconType: 'icon-qiangtixing',
      infoText: '您确定合并吗？合并材料后，将会统一应用到所有的单位工程中',
      confirm: () => {
        saveMergeMaterials(postData);
      },
      close: () => {
        infoMode.hide();
      },
    });
  } else {
    saveMergeMaterials(postData);
  }
};

const saveMergeMaterials = async postData => {
  let res = await csProject.saveMergeMaterials(postData);
  if (res.code == 200) {
    message.success('合并成功！');
    infoMode.hide();
    cancel(true);
  }
};
defineExpose({
  open,
});
</script>

<style lang="scss">
.mergeMaterials-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .check-labels {
    white-space: nowrap;
  }
  .content-table {
    height: 80%;
    padding: 0 0px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .group-title-box {
    .range-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      height: 32px;
      margin-bottom: 5px;
    }
    .right-box {
      width: 240px;
    }
  }
  .condition-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    font-size: 14px;
    margin-top: 0px;
    border-top: 1px solid rgba(185, 185, 185, 1);
    .left-box {
      display: flex;
      flex-wrap: wrap;
    }
    .items {
      margin-left: 13px;
      display: flex;
      align-items: center;
    }
    .list {
      display: flex;
      align-items: center;
    }
  }

  .footer-box {
    display: flex;
    justify-content: space-between;
    .footer-handle {
      display: flex;
      justify-content: space-between;
      .rang-tips {
        font-size: 14px;
      }
    }
  }

  .handle-tips {
    font-weight: 400;
    font-size: 12px;
    color: #2a2a2a;
  }

  .vxe-table {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
}
</style>
