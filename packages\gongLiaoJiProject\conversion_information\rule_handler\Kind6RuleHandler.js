const Kind1RuleHandler = require("../../standard_conversion/rule_handler/Kind1RuleHandler");

class Kind6RuleHandler extends Kind1RuleHandler {

    constructor(strategyCtx, rule) {
        super(strategyCtx, rule);
        this.rule.math = this.rule.conversionString;
    }

    async initEffectRCJ(){
        //return this.ctx.deInitialRcjs;
        let deRcjs = await super.initEffectRCJ();
        deRcjs = deRcjs || [];
        return deRcjs.filter(rcj => rcj.libraryCode === "2025-SZSS-DEX" && !!rcj.standardId && rcj.isSupplement != 1);
    }
}10

module.exports = Kind6RuleHandler;
