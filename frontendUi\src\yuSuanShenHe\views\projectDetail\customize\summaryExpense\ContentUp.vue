<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-13 14:44:43
-->
<template>
  <div class="table-content">
    <split
      horizontal
      ratio="3/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <vxe-table
          align="center"
          :loading="loading"
          :column-config="{ resizable: true }"
          :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
          :data="tableData"
          height="auto"
          ref="upTable"
          border="full"
          keep-source
          @edit-closed="editClosedEvent"
          :menu-config="menuConfig"
          @menu-click="contextMenuClickEvent"
          :cell-class-name="cellClassName"
          @current-change="currentChangeEvent"
          :header-cell-class-name="headerCellClassName"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethodYSSH,
            // beforeEditMethod({ row }) {
            //   if (row.ysshSysj.change === 2) return false;
            //  return beforeCellEditStatus.value;
            // },
          }"
          class="table-edit-common"
          :row-class-name="shChangeLabel"
          @cell-click="
            cellData => {
              useCellClickEvent(cellData, tableCellClickEvent, [
                'code',
                'name',
                'rate',
                'remark',
                'dispNo'
              ]);
            }
          "
        >
          <vxe-column
            field=""
            width="60"
            title=""
          >
            <template #default="{ row }">
              <span :class="shChangeLabel(row.ysshSysj?.change).class">{{shChangeLabel(row.ysshSysj?.change).label}}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="dispNo"
            width="60"
            title="序号"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ isSheHeDelete(row) ? row.ysshSysj.dispNo:row.dispNo}}</span>
            </template>
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model.trim="row.dispNo"
                type="text"
                @blur="clear()"
                @keyup="row.dispNo = row.dispNo.replace(/[^\w.]/g, '')"
              ></vxe-input>
            </template>
          </vxe-column>
          <vxe-column
            field="code"
            width="120"
            title="费用代号"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ isSheHeDelete(row) ? row.ysshSysj.code:row.code}}</span>
            </template>
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model.trim="row.code"
                type="text"
                @blur="clear()"
                @keyup="validateAndFormatCode(row)"
              ></vxe-input>
            </template>
          </vxe-column>
          <vxe-column
            field="name"
            min-width="220"
            title="名称"
            :edit-render="{ autofocus: '.vxe-textarea--inner' }"
          >
            <template #default="{ row }">
              <span>{{ isSheHeDelete(row) ? row.ysshSysj.name:row.name}}</span>
            </template>
            <template #edit="{ row }">
              <cell-textarea
                v-if="row.whetherTax !== 1"
                :clearable="false"
                v-model.trim="row.name"
                @blur="clear()"
                placeholder="请输入名称"
                :textHeight="row.height"
                @keyup="
                  row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')
                "
              ></cell-textarea>
              <span v-else>{{ row.name }}</span>
            </template>
          </vxe-column>
          <vxe-colgroup title="送审">
            <vxe-column
              field="calculateFormula"
              min-width="150"
              title="计算基数"
            >
              <template #default="{ row }">
                {{ row.ysshSysj?.calculateFormula }}
              </template>
            </vxe-column>

            <vxe-column
              field="rate"
              width="120"
              title="费率（%）"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.rate }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="price"
              width="120"
              title="金额"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.price }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup title="审定">
            <vxe-column
              field="calculateFormula"
              min-width="150"
              title="计算基数"
              :edit-render="{autofocus: '.vxe-textarea--inner'}"
            >
              <template #default="{  column, row, $columnIndex  }">
                <icon-font
                  type="icon-bianji"
                  class="more-icon"
                  v-if="
                          isSelectedCell({
                            $columnIndex,
                            column,
                            row
                          })
                       && !isSheHeDelete(row) "
                  @click.stop="editCalc(row)"
                ></icon-font>
                <span>{{ row.calculateFormula }}</span>

              </template>
              <template #edit="{ row, $columnIndex ,column }">
                <cell-textarea
                  :clearable="false"
                  v-model.trim="row.calculateFormula"
                  placeholder="请输入计算基数"
                  :textHeight="row.height"
                  @blur="clear()"
                  @keyup="
                    row.calculateFormula = row.calculateFormula.replace(
                      /[^\w\-\+\*\/]/g,
                      ''
                    )
                  "
                ></cell-textarea>

              </template>
            </vxe-column>
            <vxe-column
              field="rate"
              width="120"
              title="费率（%）"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
            >
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model="row.rate"
                  @blur="clear()"
                  @keyup="row.rate = pureNumber(row.rate)"
                ></vxe-input>
              </template>
            </vxe-column>
            <vxe-column
              field="price"
              width="120"
              title="金额"
            > </vxe-column>
          </vxe-colgroup>
          <vxe-column
            field="changeTotal"
            title="增减金额"
            width="100"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.changeTotal }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="remark"
            title="备注"
            min-width="100"
            :edit-render="{ autofocus: '.vxe-textarea--inner' }"
          >
            <template #edit="{ row }">
              <cell-textarea
                :clearable="false"
                v-model.trim="row.remark"
                @blur="clear()"
                placeholder="请输入备注"
                :textHeight="row.height"
              ></cell-textarea>
            </template>
          </vxe-column>
          <vxe-column
            field="whetherPrint"
            title="打印"
            width="80"
            :cell-render="{}"
          >
            <template #default="{ row }">
              <vxe-checkbox
                v-model="row.whetherPrint"
                size="small"
                content=""
                :checked-value="1"
                :unchecked-value="0"
                @change="update(row, 'whetherPrint')"
              ></vxe-checkbox>
            </template>
          </vxe-column>
        </vxe-table>
      </template>
      <template #two>
        <comparisonPage
          :currentInfo="currentInfo"
          pageType="fyhz"
          :needTitle="true"
        ></comparisonPage>
      </template>
    </split>
  </div>
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModel = false"
    :mask="false"
    style="position: releative"
  >
    <content-down
      :isTextArea="isTextArea"
      :textValue="textValue"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button
        type="primary"
        @click="sureData()"
      >确定</a-button>
    </span>
  </common-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  onActivated,
  getCurrentInstance,
} from 'vue';
// import feePro from '@/views/shenHeYuSuanProject/api/feePro';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { add } from 'xe-utils';
import { getUrl, shChangeLabel } from '@/utils/index';
import ContentDown from '@/views/projectDetail/customize/summaryExpense/ContentDown.vue';
import { Modal } from 'ant-design-vue';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
import split from '@/components/split/index.vue';
import { insetBus } from '@/hooks/insetBus';
import infoMode from '@/plugins/infoMode';
import { summaryExpenseJs } from '@/views/projectDetail/customize/summaryExpense/comContent.js';
const {
  useCellClickEvent,
  useCellDBLClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
  beforeCellEditStatus,
} = useCellClick();
const store = projectDetailStore();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const upTable = ref();
let tableData = ref([]);
let totalFee = ref([]);
let taxMode = ref(); //1-一般计税，2-简易计税
let isCurrent = ref(0);
let comModel = ref(false); //计算基数编写弹框
let isTextArea = ref(true);
let textValue = ref('');
let rowValue = ref('');
let deleteInfo = ref();
let loading = ref(false);
let oldValue = ref('');
const currentInfo = ref({});
const comArea = ref();
let selectData = ref(null); //批量选择的数据
const emits = defineEmits(['getMoveInfo']);
const setMoveInfoObj = () => {
  let obj = {
    isCurrent: isCurrent.value,
    isLast: tableData.value.length - 1 === isCurrent.value,
  };
  console.log('111111111111-obj', obj);
  emits('getMoveInfo', obj);
};
const {
  keyDownOperate,
  validateAndFormatCode,
  editCalc,
  cancelData,
  sureData,
  getTaxMethods,
  // getTotalFeeCode,
  getTableData,
  clear,
  editClosedEvent,
  update,
  pasteIsDisabled,
  menuConfig,
  getCurrentIndex,
  operate,
  contextMenuClickEvent,
  pasteItem,
  addItem,
  copyItem,
  deleteItem,
  isSheHeDelete,
} = summaryExpenseJs({
  pageType: 'yssh',
  upTable: upTable,
  comModel,
  textValue,
  oldValue,
  comArea,
  tableData,
  taxMode,
  totalFee,
  isCurrent,
  loading,
  deleteInfo,
  currentInfo,
  setMoveInfoObj,
});
onMounted(() => {
  getTableData();
  taxMode.value = store.taxMade;
  // getTotalFeeCode();
  setUse();
});
const setUse = () => {
  window.addEventListener('keydown', keyDownOperate);
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
};
onActivated(() => {
  setUse();
});
const currentChangeEvent = ({ $rowIndex }) => {
  isCurrent.value = $rowIndex;
  setMoveInfoObj();
};

const moveDeData = ({ state, type }) => {
  console.log('moveDeData', state, type);
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    index: isCurrent.value,
    move: state === 1 ? 'up' : 'down',
  };
  console.log('apiData', apiData);
  if (type === 'move') {
    feePro.moveUpDown(apiData).then(res => {
      console.log('res移动', res);
      if (res.status === 200) {
        isCurrent.value =
          state === 1 ? isCurrent.value - 1 : isCurrent.value + 1;
        message.success('移动成功');
        getTableData();
      }
    });
  }
};
const cellBeforeEditMethodYSSH = ({ row }) => {
  if (isSheHeDelete(row)) return false;
  return beforeCellEditStatus.value;
};
const props = defineProps({
  isCharu: {
    type: Boolean,
  },
});
watch(
  () => props.isCharu,
  () => {
    operate('insert', {});
  }
);
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  ([val, oldVal], [newY, oldy]) => {
    if (
      store.tabSelectName === '费用汇总' &&
      store.currentTreeInfo.levelType === 3
    ) {
      isCurrent.value = 0; //切换页面选中行默认选第一行
      getTableData();
    }
  }
);
const headerCellClassName = ({ column }) => {
  if (column.field === 'index') {
    return 'index-bg';
  }
  return null;
};
const cellClassName = ({ $columnIndex, row, column }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};
bus.off('refreshzj');
bus.on('refreshzj', type => {
  if (store.tabSelectName === '费用汇总') {
    getTableData(); // 刷新费用汇总
  }
});
const tableCellClickEvent = ({ row, column, cell, event }) => {
  currentInfo.value = row;
  console.log(currentInfo.value, 'currentInfo.value-=-=-==-=--=');
  return true;
};
</script>
<style lang="scss" scoped>
.btns {
  position: absolute;
  width: 200px;
  bottom: -20px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.multiple-select {
  width: 50px;
  height: 30px;
  line-height: 30px;
  margin-left: -10px;
  text-indent: 10px;
  cursor: pointer;
}

.table-content {
  height: 100%;
  background: #ffffff;
  user-select: none;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #fff;
  }
}
// }
</style>
