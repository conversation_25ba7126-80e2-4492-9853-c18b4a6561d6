<template>
  <div class="content">
    <div class="selectTree">
      <div class="tree">
        <a-tree
          v-model:expandedKeys="expandedkeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeData"
          @select="selectChildren"
          :defaultExpandAll="true"
        >
          <template #icon="{ key }">
            <template v-if="key !== '0-0'"></template>
          </template>
        </a-tree>
      </div>
    </div>
    <div class="table-content">
      <vxe-grid
        class="trends-table-column"
        v-bind="gridOptions"
        ref="editTable"
        height="auto"
        v-on="gridEvents"
      >
      </vxe-grid>
    </div>
  </div>
  <!-- </div> -->
</template>

<script setup>
import { ref, watch, onMounted, reactive } from 'vue';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const expandedkeys = ref(['0-0']);
const selectedKeys = ref(['0-0']);
const treeData = ref([]);
const selectTree = ref('');
let title = ref('费用金额');
const emits = defineEmits(['selectTarget']);
const editTable = ref();
const selectChildren = e => {
  const select = treeData.value[0].children.find(item => item.key === e[0]);
  selectTree.value = select ? select : '';
  getTableData();
};

let tableData = ref([]);
let value = ref();
const props = defineProps({
  tableType: {
    type: String,
  },
  propInfo: {
    type: Object,
  },
});
const gridOptions = reactive({
  align: 'center',
  headerAlign: 'center',
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  columns: [],
  data: [],
  keepSource: true,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
});

const initData = () => {
  console.log('重新获取表格initData');
  switch (props.tableType) {
    case 'DJGC':
      // 单价构成
      gridOptions.columns = [
        { field: 'sortNo', title: '序号', minWidth: 50 },
        { field: 'code', title: '费用代号', minWidth: 70 },
        {
          field: 'name',
          title: '名称',
          minWidth: 70,
          showOverflow: 'ellipsis',
        },
        { field: 'unitPrice', title: '费用金额', minWidth: 70 },
      ];
      break;
    case 'QTXM':
      //其他项目
      gridOptions.columns = [
        { field: 'sortNo', title: '序号', minWidth: 50 },
        { field: 'code', title: '费用代号', minWidth: 70 },
        {
          field: 'name',
          title: '名称',
          minWidth: 70,
          showOverflow: 'ellipsis',
        },
        { field: 'price', title: '费用金额', minWidth: 70 },
      ];
      break;
  }
};
onMounted(() => {
  initData();
  expandedkeys.value = ['0-0'];
  selectedKeys.value = ['0-0'];
  selectTree.value = '';
  getTableData();
  getTypeList();
});
const currentRow = ({ row }) => {
  //选中行的费用代码自动连接到输入框内
  editTable.value.setCurrentRow(row);
  console.log('currentRow', row);
};
const getTableData = () => {
  const formdata = {
    type: selectTree.value ? selectTree.value.id + '' : '',
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  let apiName;
  switch (props.tableType) {
    case 'DJGC':
      // 单价构成
      apiName = 'costCodeListBytypeDJGC';
      formdata.sequenceNbr = props.propInfo?.sequenceNbr;
      break;
    case 'QTXM':
      //其他项目
      apiName = 'costCodePriceQTXM';
      break;
  }
  console.log('---------单位工程的费用代码和对应金额传参', formdata);
  feePro[apiName](formdata).then(res => {
    console.log('单位工程的费用代码和对应金额', res);
    if (res.status === 200) {
      res.result?.map((item, index) => (item.sortNo = index + 1));
      // tableData.value = res.result ? res.result : [];
      // gridOptions.data = res.result ? res.result : []
      let list = res.result ? res.result : [];

      if (list.length && +store.deType === 22) {
        list = list.filter(i => {
          return !['FHZMZJF_DEJ'].includes(i.code);
        });
      }
      gridOptions.data = list;
    }
  });
};
const getTypeList = () => {
  let apiName;
  switch (props.tableType) {
    case 'DJGC':
      // 单价构成
      apiName = 'costCodeTypeListDJGC';
      break;
    case 'QTXM':
      //其他项目
      apiName = 'costCodeTypeListQTXM';
      break;
  }
  feePro[apiName]({
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  }).then(res => {
    if (res.status === 200) {
      console.log('getTypeList', res);
      getKeyValue(res.result);
    }
  });
};
const getKeyValue = data => {
  let keyList = [];
  let valueList = [];
  for (let i in data) {
    console.log('i=================', i);
    keyList.push(data[i].code);
    valueList.push(data[i].desc);
  }
  let tree = {
    title: '费用代码',
    key: '0-0',
    children: [],
  };
  keyList.map((item, index) => {
    tree.children.push({
      title: valueList[index],
      key: `0-0-${index}`,
      id: keyList[index],
    });
  });
  treeData.value = [tree];
  console.log('treeData.value', treeData.value);
};
const sureData = ({ row }) => {
  emits('selectTarget', row.code);
};
const gridEvents = ref({
  cellDblclick: sureData,
  currentChange: currentRow,
});
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  width: 100%;
  height: 94%;
  justify-content: space-around;
  .selectTree {
    flex: 1;
    height: 100%;
    margin: 0 10px;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    .tree {
      height: 100%;
      border: 1px solid #dcdfe6;
      font-size: 14px;
    }
  }
  .table-content {
    flex: 3;
    overflow: hidden;
    margin-left: 30;
  }
}
.content :deep(.ant-tree) {
  padding-top: 20px;
  height: 100%;
  font-size: 12px;
}
.table-content {
  // width: 100%;
  height: 100%;
  // overflow: hidden;
  background: #ffffff;
  user-select: none;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .index-bg) {
    // background-color: rgba(243, 243, 243, 1);
    background-color: #fff;
  }
}
</style>
