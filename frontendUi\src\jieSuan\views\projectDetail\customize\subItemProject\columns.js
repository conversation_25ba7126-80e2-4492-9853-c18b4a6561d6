import { ref } from 'vue'

export const originalTableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 50,
    align: 'center',
    classType: 1,
    fixed: 'left',
  },
  {
    title: '项目编码',
    field: 'bdCode',
    align: 'left',
    headerAlign: 'center',
    width: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    fixed: 'left',
    showTooltip: true,
  },
  {
    title: '类型',
    field: 'type',
    width: 50,
    align: 'center',
    classType: 1,
    showTooltip: true,
    slot: true,
  },
  {
    title: '项目名称',
    field: 'name',
    width: 200,
    align: 'center',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    showTooltip: true,
  },
  {
    title: '工作内容',
    field: 'workContent',
    width: 120,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '项目特征',
    field: 'projectAttr',
    width: 120,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    visible: false,
    initialize: false
  },
  // {
  //   title: '规格型号',
  //   field: 'specification',
  //   width: 120,
  //   slot: true,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   classType: 1,
  // },
  {
    title: '单位',
    field: 'unit',
    width: 60,
    slot: true,
    classType: 1,
  },
  // {
  //   title: '含量',
  //   field: 'deProportion',
  //   slot: true,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   classType: 1,
  //   initialize: false
  // },
  {
    title: '合同工程量',
    field: 'backQuantity',
    width: 100,
    classType: 3,
  },
  {
    title: '工程量表达式',
    field: 'quantityExpression',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
  },
  {
    title: '锁定综合单价',
    field: 'lockPriceFlag',
    slot: true,
    classType: 1,
  },
  {
    title: '结算单价',
    field: 'price',
    width: 100,
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '结算工程量',
    field: 'quantity',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true
  },
  {
    title: '合同单价',
    field: 'backPrice',
    width: 100,
    classType: 2,
    initialize: true
  },
  {
    title: '合同合价',
    field: 'backTotal',
    width: 100,
    classType: 2,
    visible: false,
    initialize: false
  },
  {
    title: '结算合价',
    field: 'total',
    classType: 2,
    width: 100,
    initialize: true
  },
  {
    title: '量差',
    field: 'quantityDifference',
    classType: 3,
    initialize: true
  },
  {
    title: '量差比例(%)',
    field: 'quantityDifferenceProportion',
    slot: true,
    classType: 3,
    initialize: true
  },
  {
    title: '取费专业',
    field: 'costMajorName',
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '防寒子目',
    field: 'coldResistantSuborder',
    slot: true,
    classType: 1,
  },
  {
    title: '备注',
    field: 'description',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true
  },
]);

export const tableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 50,
    align: 'center',
    classType: 1,
    initialize: true
  },
  {
    title: '项目编码',
    field: 'bdCode',
    align: 'left',
    headerAlign: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    initialize: true
  },
  {
    title: '类型',
    field: 'type',
    width: 50,
    align: 'center',
    classType: 1,
    initialize: true,
    slot: true,
  },
  {
    title: '项目名称',
    field: 'name',
    width: 200,
    align: 'center',
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
  },
  {
    title: '项目特征',
    field: 'projectAttr',
    width: 120,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '单位',
    field: 'unit',
    width: 60,
    slot: true,
    classType: 1,
  },
  {
    title: '工程量表达式',
    field: 'quantityExpression',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
  },
  {
    title: '结算工程量',
    field: 'quantity',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
  },
  {
    title: '单价',
    field: 'zjfPrice',
    slot: true,
    width: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true
  },
  {
    title: '合价',
    field: 'zjfTotal',
    classType: 1,
    width: 100,
    initialize: true
  },
  {
    title: '取费专业',
    field: 'costMajorName',
    width: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '施工组织措施类别',
    field: 'measureType',
    editRender: {},
    slot: true,
    classType: 1,
    visible: false,
    initialize: false
  },
  {
    title: '人工费单价',
    field: 'rfee',
    classType: 2,
    initialize: true
  },
  {
    title: '人工费合价',
    field: 'totalRfee',
    classType: 2,
    initialize: true
  },
  {
    title: '材料费单价',
    field: 'cfee',
    classType: 2,
    initialize: true
  },
  {
    title: '材料费合价',
    field: 'totalCfee',
    classType: 2,
    initialize: true
  },
  {
    title: '机械费单价',
    field: 'jfee',
    classType: 2,
    initialize: true
  },
  {
    title: '机械费合价',
    field: 'totalJfee',
    classType: 2,
    initialize: true
  },
  {
    title: '管理费单价',
    field: 'managerFee',
    classType: 2,
    initialize: true
  },
  {
    title: '管理费合价',
    field: 'totalManagerFee',
    classType: 2,
    initialize: true
  },
  {
    title: '利润单价',
    field: 'profitFee',
    classType: 2,
    initialize: true
  },
  {
    title: '利润合价',
    field: 'totalProfitFee',
    classType: 2,
    initialize: true
  },
  {
    title: '主材费单价',
    field: 'zcfee',
    classType: 2,
    initialize: true
  },
  {
    title: '主材费合价',
    field: 'totalZcfee',
    classType: 2,
    initialize: true
  },
  {
    title: '结算单价',
    field: 'price',
    classType: 2,
    initialize: true
  },
  {
    title: '结算合价',
    field: 'total',
    classType: 2,
    initialize: true
  },
  {
    title: '关联合同清单',
    field: 'relevanceName',
    slot: true,
    classType: 3,
    initialize: true
  },
  {
    title: '依据文件',
    field: 'accordingDocument',
    classType: 3,
    slot: true,
    initialize: true
  },
  {
    title: '归属',
    field: 'parentProjectName',
    classType: 3,
    initialize: true
  },
  // {
  //   title: '清单工作内容',
  //   field: 'workContent',
  //   width: 260,
  //   slot: true,
  //   editRender: { autofocus: '.vxe-textarea--inner' },
  //   classType: 3,
  //   initialize: false
  // },
  // {
  //   title: '锁定综合单价',
  //   field: 'lockPriceFlag',
  //   slot: true,
  //   classType: 3,
  //   initialize: false
  // },
  {
    title: '防寒子目',
    field: 'coldResistantSuborder',
    slot: true,
    classType: 1,
  },
  {
    title: '备注',
    field: 'description',
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
    initialize: true
  },
]);

