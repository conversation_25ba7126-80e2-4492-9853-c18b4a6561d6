<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-08-09 17:50:28
 * @LastEditors: liuxia
 * @LastEditTime: 2024-05-24 20:53:08
-->
<template>
  <common-modal
    className="dialog-comm djgcModal"
    width="920px"
    height="700px"
    v-model:modelValue="dialogVisible"
    title="载入模板"
    @close="emit('onSuccess', 'close')"
  >
    <div class="content-wrap">
      <div class="header">
        <p>
          过滤条件：
          <vxe-pulldown ref="pulldownRef" class="my-dropdown" transfer>
            <template #default>
              <vxe-input
                v-model="searchPath"
                :suffix-icon="
                  pulldownRef?.isPanelVisible()
                    ? 'vxe-icon-arrow-up'
                    : 'vxe-icon-arrow-down'
                "
                placeholder="输入或选择文件路径"
                @blur="keyupEvent"
                @focus="focusEvent"
                :clearable="false"
              ></vxe-input>
              <!-- @suffix-click="suffixClick" -->
            </template>
            <template #dropdown>
              <div class="my-bodydown4">
                <vxe-table
                  ref="vexTable"
                  border="none"
                  align="left"
                  :show-header="false"
                  :showIcon="false"
                  :column-config="{ resizable: true }"
                  :row-config="{ isCurrent: true, keyField: 'id' }"
                  :tree-config="{
                    children: 'children',
                    transform: false,
                  }"
                  :data="fileAllTreeData"
                  @current-change="currentChangeEvent"
                  height="auto"
                  keep-source
                >
                  <vxe-column tree-node field="path"> </vxe-column>
                </vxe-table>
              </div>
            </template>
          </vxe-pulldown>
        </p>
        <a-button @click="save(false)" class="resetBtn"
          ><icon-font type="icon-huifumoren" class="iconFont"></icon-font
          >恢复默认</a-button
        >
      </div>
      <div class="list-wrap">
        <splitpanes>
          <pane size="25" min-size="15" max-size="30">
            <div class="menus">
              <vxe-table
                ref="AsideMenu"
                border="none"
                align="left"
                :show-header="false"
                :showIcon="false"
                :column-config="{ resizable: true }"
                :row-config="{
                  isCurrent: true,
                  keyField: 'sequenceNbr',
                  height: 30,
                }"
                :tree-config="{
                  children: 'children',
                  transform: false,
                  line: false,
                  // showIcon: true,
                  expandAll: true,
                }"
                :scroll-x="{
                  enabled: false,
                }"
                :data="AsideMenuList"
                @current-change="currentChangeAsideItem"
                height="95%"
                keep-source
              >
                <vxe-column
                  tree-node
                  field="name"
                  show-overflow="title"
                  width="100%"
                >
                  <template #default="{ row }">
                    <icon-font
                      style="font-size: 14px"
                      :type="
                        row.isDirectory
                          ? 'icon-danxianggongcheng'
                          : 'icon-danweigongcheng1'
                      "
                    />{{ row.name }}
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
          </pane>
          <pane>
            <div class="table-content">
              <vxe-grid
                class="trends-table-column"
                v-bind="gridOptions"
                ref="gridRef"
                height="100%"
              >
                <template #csxm_default="{ row }">
                  <i
                    @click="changeStatus(row)"
                    v-if="row.displaySign === 1"
                    class="vxe-icon-caret-down"
                  ></i>
                  <i
                    @click="changeStatus(row)"
                    v-if="row.displaySign === 2"
                    class="vxe-icon-caret-right"
                  ></i>
                  <span>{{ row.bdCodeLevel04 }} </span>
                </template>
              </vxe-grid>
            </div>
          </pane>
        </splitpanes>
      </div>
      <div class="footer-btn-list">
        <div>
          <a-button type="primary" @click="close" ghost>取消</a-button>

          <a-button type="primary" :loading="submitLoading" @click="save(true)"
            >确定</a-button
          >
        </div>
      </div>
    </div>
  </common-modal>
</template>
<script>
export default {
  name: 'djgcMould',
};
</script>
<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, markRaw, nextTick, toRaw, defineExpose } from 'vue';
import csProject from '@/api/csProject';
import XEUtils from 'xe-utils';
import { useRoute } from 'vue-router';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import { projectDetailStore } from '@/store/projectDetail';
const props = defineProps(['saveData']);
const projectStore = projectDetailStore();
const route = useRoute();
const emit = defineEmits(['onSuccess']);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const tableData = ref([]);
let pagesType = ref('fyhz');
const gridRef = ref(null);
let isReset = ref(false); //是否恢复默认
let searchPath = ref(null); //搜索框路径
let fileAllTreeData = ref([]); //搜索框下拉树列表数据
const pulldownRef = ref();
const focusEvent = () => {
  const $pulldown = pulldownRef.value;
  if ($pulldown) {
    $pulldown.showPanel();
  }
};
const keyupEvent = () => {
  //此处处理展开输入路径对应的自己数据
  console.log('keyupEvent', searchPath.value);
  getAllTreeData();
};
const currentChangeEvent = ({ row }) => {
  const $pulldown = pulldownRef.value;
  if ($pulldown) {
    searchPath.value = row.path;
    $pulldown.hidePanel();
  }
  //刷新左侧表格树
  getAllTreeData();
};
const suffixClick = () => {
  const $pulldown = pulldownRef.value;
  console.log('$pulldown', $pulldown);
  if ($pulldown) {
    $pulldown.showPanel();
  }
};
const initData = () => {
  isReset.value = false;
  submitLoading.value = false;
  switch (pagesType.value) {
    case 'djgc':
      // 单价构成
      gridOptions.columns = [
        { field: 'sort', title: '序号', minWidth: 30 },
        { field: 'code', title: '费用代号', minWidth: 50 },
        {
          field: 'name',
          title: '名称',
          minWidth: 70,
          showOverflow: 'ellipsis',
        },
        { field: 'caculateBase', title: '计算基数', minWidth: 70 },
        { field: 'desc', title: '基数说明', minWidth: 70 },
        { field: 'rate', title: '费率', minWidth: 30 },
        { field: 'type', title: '费用类别', minWidth: 70 },
      ];
      break;
    case 'fyhz':
      // 费用汇总
      gridOptions.columns = [
        { field: 'dispNo', title: '序号', minWidth: 50 },
        { field: 'code', title: '费用代号', minWidth: 70 },
        { field: 'calculateFormula', title: '计算基数', minWidth: 110 },
        {
          field: 'instructions',
          title: '基数说明',
          align: 'left',
          minWidth: 110,
        },
        { field: 'rate', title: '费率', minWidth: 70 },
      ];
      break;
    case 'csxm':
      // 措施项目
      //
      gridOptions.columns = [
        {
          field: 'bdCodeLevel04',
          title: '项目编码',
          width: 120,
          treeNode: true,
        },
        { field: 'bdNameLevel04', title: '名称', align: 'left' },
        { field: 'unit', title: '单位', width: 40 },
        { field: 'calculateFormula', title: '计算基数', width: 110 },
        { field: 'rate', title: '费率', width: 40 },
      ];
      break;
    case 'qtxm':
      //其他项目
      gridOptions.columns = [
        {
          field: 'dispNo',
          title: '序号',
          minWidth: 120,
          treeNode: true,
        },
        { field: 'extraName', title: '名称', minWidth: 120 },
        { field: 'unit', title: '单位', minWidth: 40 },
        // { field: 'amount', title: '数量', minWidth: 40 },
        { field: 'calculationBase', title: '计算基数', minWidth: 100 },
        { field: 'instructions', title: '基数说明', minWidth: 100 },
        {
          field: 'taxRemoval',
          visible:
            Number(projectStore.deType) === 12 &&
            Number(projectStore.taxMade) === 1,
          title: '除税系数',
          minWidth: 90,
        },
        { field: 'extraName', title: '费用类别', minWidth: 90 },
      ];
      break;
  }
};
const changeStatus = row => {
  if (row.displaySign === 1) {
    row.displaySign = 2;
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
  }
};

const gridOptions = reactive({
  headerAlign: 'center',
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  columns: [],
  data: [],
  align: 'center',
  treeConfig: {
    line: true,
    showIcon: true,
    expandAll: true,
    iconOpen: 'vxe-icon-caret-down',
    iconClose: 'vxe-icon-caret-right',
  },
});

/**
 * type false, 恢复默认
 */
const save = type => {
  let data = allPostData.value;
  if (!type) {
    data.template = AsideMenuList.value[0].val;
  }
  console.log('allPostData.value', allPostData.value);
  if (!data?.path) {
    message.error('请选择模板！');
    return;
  }

  let apiName = 'selectCostSummaryTemplate';
  if (pagesType.value == 'csxm') {
    // 措施项目
    apiName = 'applyMeasureTemplate';
    data.templateName = data.template;
    if (!type) {
      data.templateName = null;
    }
  } else if (pagesType.value == 'djgc') {
    // 措施项目
    apiName = type ? 'loadUPCtemplateDJGC' : 'cancelEditorDJGC';
    data = type
      ? { ...props.saveData, path: allPostData.value.path }
      : { ...props.saveData };
  } else if (pagesType.value == 'qtxm') {
    //其他项目
    apiName = 'settingsTemplateData';
  }
  console.log('🚀 ~ save ~ data:', data, apiName, type);
  csProject[apiName](JSON.parse(JSON.stringify(data))).then(res => {
    delete data.template;
    console.log('载入模板确定活恢复', res);
    isReset.value = !type;
    if (pagesType.value == 'djgc' && !type) {
      //   type true载入   false恢复默认
      emit('onSuccess');
      message.success('恢复模板成功');
    } else {
      if (res.status === 200) {
        emit('onSuccess');
      } else if (res?.message) {
        message.error(res.message);
      }
      message.success(`${type ? '载入' : '恢复'}模板成功！`);
    }
  });
};

const close = () => {
  initData();
  tableData.value = [];
  dialogVisible.value = false;
};

let allPostData = ref({});

// 获取左侧模板
let AsideMenuList = ref([]);
let useMenu = reactive({
  label: '',
  val: '',
});
let allFileList = ref([]); //左侧树-普通数组形式
const getAllTreeData = () => {
  const { taxMode, deStandardReleaseYear } = projectStore.constructConfigInfo;
  let apiData;
  let apiName;
  switch (pagesType.value) {
    case 'fyhz':
      // 费用汇总模板数据
      apiData = {
        libraryCodeVersion: deStandardReleaseYear,
        taxMode,
      };
      apiName = 'getCostSummaryTemplate';
      break;
    case 'djgc':
      // 单价构成模板数据
      apiData = {
        path: searchPath.value,
      };
      apiName = 'upcFolderDJGC';
      break;

    case 'csxm':
      // 措施项目
      apiData = {
        constructId: route.query?.constructSequenceNbr,
      };
      apiName = 'getMeasureTemplates';
      break;
    case 'qtxm':
      //其他项目
      apiData = {
        constructId: route.query?.constructSequenceNbr,
        singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
        unitId: projectStore.currentTreeInfo?.id, //单位ID
        libraryCodeVersion: projectStore.deType,
        taxMode: projectStore.taxMade,
      };
      apiName = 'getOtherProjectTemplate';
      break;
  }
  console.log('搜索下拉框文件列表数据', apiData);
  csProject[apiName](apiData)
    .then(res => {
      console.log('搜索下拉框文件列表数据', apiData, res);
      if (res.status === 200) {
        if (res.status === 200) {
          searchPath.value = res.result.folder[0]; //默认搜索路径
          AsideMenuList.value = res.result.files; //左侧树数据列表
          let pullList = [];
          res.result.folder.map((i, index) => {
            pullList.push({
              id: index + i,
              path: i,
              children: [],
            });
          });
          fileAllTreeData.value = pullList;
          allFileList.value = XEUtils.toTreeArray(AsideMenuList.value);
          console.log(
            'XEUtils.toTreeArray(dataTree);',
            AsideMenuList.value,
            XEUtils.toTreeArray(AsideMenuList.value[0])
          );
          let target = allFileList.value.find(i => !i.isDirectory);
          nextTick(() => {
            AsideMenu.value.setAllTreeExpand(true);
            AsideMenu.value.setCurrentRow(target);
          });
          AsideMenu.value.setAllTreeExpand(true);
          AsideMenu.value.setCurrentRow(target);
          handClickMenu(target);
        }
      }
    })
    .catch(err => {
      if (err) {
        AsideMenuList.value = [];
        gridOptions.data = [];
        allPostData.value = null;
      }
    });
};
const currentChangeAsideItem = ({ row }) => {
  handClickMenu(row);
};
const AsideMenu = ref();

//点击了左侧，获取右侧数据
const handClickMenu = v => {
  if (v.isDirectory) return;
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    path: v.path,
  };
  let apiName = 'getTemplateData';
  if (pagesType.value == 'csxm') {
    // 措施项目
    apiName = 'getBaseListByTemplate';
    postData.templateName = useMenu.val;
  } else if (pagesType.value == 'djgc') {
    apiName = 'upcPreviewDJGC';
  } else if (pagesType.value == 'qtxm') {
    postData.taxMode = projectStore.taxMade;
    apiName = 'getOtherProjectTemplateData';
  }

  allPostData.value = postData;
  csProject[apiName](postData).then(res => {
    console.log('右侧数据1', apiName, postData, res);
    if (pagesType.value == 'csxm') {
      // 展示树形
      let treeListAwf = {
        id: 1,
        bdCodeLevel04: 1,
        bdNameLevel04: '安全生产、文明施工费',
        children: res.result.get(2),
      };
      let treeListQt = {
        id: 2,
        bdCodeLevel04: 2,
        bdNameLevel04: '其他总价措施项目',
        children: res.result.get(3),
      };

      gridOptions.data = [treeListAwf, treeListQt];
      nextTick(() => {
        gridRef.value.setAllTreeExpand(true);
      });
    } else {
      gridOptions.data = res.result;
    }
  });
};

const open = async (type = 'fyhz') => {
  console.log('载入模板---页签', type);
  pagesType.value = type;
  searchPath.value = null;
  dialogVisible.value = true;
  initData();
  getAllTreeData();
};
const getIsReset = () => {
  return isReset.value;
};

let isEdit = ref(true);
const getIsEdit = () => {
  return isEdit.value;
};
defineExpose({
  open,
  close,
  getIsReset,
  getIsEdit,
});
</script>

<style lang="scss">
.djgcModal {
  .splitpanes {
    flex: 1;
    overflow: auto;
    .content {
      width: 100%;
      height: 100%;
      overflow: auto;
      padding: 2px;
      border: 1px solid #b9b9b9;
    }
  }
  .splitpanes__splitter {
    min-width: 6px;
    border-radius: 4px;
    margin: 0 3px;
    background: #fff;
    &:hover {
      background-color: rgba(24, 144, 255, 0.8);
    }
  }
  .my-dropdown {
    width: 90%;
    .vxe-pulldown--content .vxe-input {
      width: 100%;
    }
  }
  .my-bodydown4 {
    min-height: 50px;
  }
  .content-wrap {
    width: 100%;
    min-width: 500px;
    height: 100%;
    .header {
      display: flex;
      width: 100%;
      p {
        width: 90%;
        display: flex;
        :deep(.vxe-input) {
          width: 100%;
        }
      }
      .resetBtn {
        margin-left: 20px;
        font-size: 12px;
        height: 28px;
      }
    }
    .list-wrap {
      display: flex;
      // height: 60vh;
      // height: 80%;
      height: 85%;
      margin-bottom: 15px;
      .menus {
        padding: 10px 5px;
        border: 1px solid rgba(185, 185, 185, 0.5);
        height: 100%;
        overflow-y: auto;
        .menu-item {
          margin-bottom: 2px;
          line-height: 1.6;
          opacity: 0.8;
          padding: 4px 0;
          cursor: pointer;
          &:hover {
            opacity: 1;
          }
          .iconFont {
            margin-right: 4px;
          }
          &.active {
            opacity: 1;
            color: #000;
          }
        }
      }
      .table-content {
        flex: 1;
        height: 100%;
      }
    }

    .footer-btn-list {
      float: right;
    }
  }

  .vxe-table {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .multiple-check {
      background: #a6c3fa !important;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
}
</style>
