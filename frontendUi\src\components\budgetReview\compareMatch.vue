<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-04 09:23:33
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-02 17:38:47
-->

<template>
  <common-modal
    width="100vw"
    height="100vh"
    v-model:modelValue="props.visible"
    @close="cancel"
    title="对比匹配"
    className="dialog-self dialog-selfMax"
    class="compareMatch"
  >
    <!-- {{ props.sequenceNbr }} -->
    <!-- 增加计税方式后样式变化   height="500" -->
    <div class="headerPro">
      <section>
        <aside
          class="aside"
          v-if="!isInner"
          :style="{ width: '225px' }"
        >
          <aside-tree
            ref="asideTreeRef"
            :isExpand="true"
            :compareMatch="true"
            :treeData="treeData"
            :treeListCache="treeListCache"
            @getTreeList="getTreeList"
          />
        </aside>
        <main :style="{ width: isInner ? '100vw' : 'calc(100vw - 230px)}' }">
          <div
            class="content"
            v-if="
              projectStore.currentTreeInfo?.levelType === 3 &&
              treeData.length > 0
            "
          >
            <tabs v-model:activeKey="activeKey"></tabs>
            <contentpro
              :isInner="isInner"
              :innerConstructId="innerConstructId"
              :activeKey="activeKey"
            ></contentpro>
          </div>
          <div
            class="nodata"
            v-else
          >暂无数据</div>
        </main>
        <footer :style="{ marginBottom: isInner ? '-50px' : '20px' }">
          <a-button
            type="primary"
            :loading="loading"
            @click="cancel"
          >确定</a-button>
          <!--          <a-button :loading="loading" @click="cancel">取消</a-button>-->
        </footer>
      </section>
    </div>
  </common-modal>
</template>

<script setup>
import {
  defineEmits,
  onMounted,
  reactive,
  ref,
  computed,
  watch,
  defineAsyncComponent,
  provide,
} from 'vue';
import AsideTree from '@/views/projectDetail/customize/AsideTree.vue';
import xeUtils from 'xe-utils';
import contentpro from './content.vue';
import { projectDetailStore } from '@/store/projectDetail';
import shApi from '@/api/shApi';
import feePro from '@/api/feePro';
import { generateLevelTreeNodeStructureSH } from '@/api/budgetReview';
const activeKey = ref(1);
const projectStore = projectDetailStore();
const loading = ref(false);
const innerConstructId = ref('');
provide(
  'activeKey',
  computed(() => activeKey.value)
);
const tabs = defineAsyncComponent(() => import('./tabs.vue'));
watch(
  () => projectStore.currentTreeInfo,
  () => {
    if (projectStore.currentTreeInfo) {
      activeKey.value =
        props.original && props.original > 1 ? props.original : 1;
      console.log(projectStore, '树点击更新！！！！！！！！！！！！！');
    }
  }
);

const iptList = reactive({
  iptType: String,
  default: 'zhaobiao',
});
const props = defineProps({
  showType: {
    type: String, //类型字符串
  },
  visible: {
    type: Boolean,
    default: false,
  },
  sequenceNbr: {
    type: String, //项目id
  },
  isInner: {
    type: Boolean,
    default: false,
  },
  original: {
    type: Number,
    default: 1, // 1 新建项目 2 分部分项 3 措施项目
  },
});

watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      activeKey.value =
        props.original && props.original > 1 ? props.original : 1;
      console.log(
        props.sequenceNbr,
        'props.sequenceNbr',
        props.original,
        activeKey.value
      );
      getTreeList();
    }
  }
);
const treeData = ref([]);
let treeListCache = ref();

const emit = defineEmits(['update:visible', 'updateData']);
const cancel = () => {
  if (props.original !== 0) {
    activeKey.value = 1;
    treeData.value = [];
    innerConstructId.value = '';
    emit('update:visible'); //关闭弹框
    emit('updateData'); //关闭弹框
    return;
  }
  let apiData = {
    constructId: props.sequenceNbr,
    type: 'match',
  };
  if (!props.isInner) apiData.isSave = true;
  shApi.shRecoveryData(apiData).then(res => {
    console.log(res, 'shRecoveryData');
    if (res.status === 200) {
      activeKey.value = 1;
      treeData.value = [];
      innerConstructId.value = '';
      emit('update:visible'); //关闭弹框
    } else {
      emit('update:visible'); //关闭弹框
    }
  });
};
const confirm = () => {
  loading.value = true;
  let apiData = {
    constructId: props.sequenceNbr,
    type: 'match',
  };
  if (!props.isInner) apiData.isSave = true;
  shApi.shSaveData(apiData).then(res => {
    console.log(res, 'shSaveData');
    loading.value = false;
    if (res.status === 200) {
      treeData.value = [];
      innerConstructId.value = '';
      emit('update:visible', false); //关闭弹框
    }
  });
};

onMounted(() => {
  // getBSToken();
});
const getTreeList = async () => {
  console.log('获取树', props.sequenceNbr);
  generateLevelTreeNodeStructureSH(props.sequenceNbr).then(res => {
    console.log('res-getTreeList', res);
    if (res.status === 200) {
      projectStore.SET_TYPE('yssh');
      treeData.value = res.result;
      innerConstructId.value = res.result[0].id;
      treeListCache.value = xeUtils.clone(res.result, true);
    }
  });
};
</script>
<style lang="scss" scoped>
section {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: calc(100vh - 90px);
}
main {
  // flex: 1;
  width: calc(100% - 230px);
  height: calc(100vh - 250px);
}
footer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  .ant-btn {
    margin-right: 20px;
  }
}
.nodata {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}
.content {
  width: 100%;
  height: 100%;
}
.headerPro {
  width: 100%;
  display: flex;
  height: 100%;
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    background: rgba(216, 216, 216, 0.15);
  }
  aside {
    width: 225px;
    height: calc(100%);
    border-right: 2px solid #dcdfe6;
    background: #f8fbff;
    border-right: 2px solid #dcdfe6;
    position: relative;
    color: white;
    font-size: 12px;
    text-align: center;
    transition: width 0.3s linear;
    &:hover .btn {
      display: block;
    }
  }
}
</style>
