<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-06-13 08:57:40
 * @LastEditors: k<PERSON>weiqiang
 * @LastEditTime: 2024-11-06 16:14:37
-->
<template>
  <div class="description-info">
    <div class="header">
      <span class="taxTitle">清单相关描述</span>
    </div>
    <vxe-table  :data="tableData" max-height="75%">
      <vxe-column field="qdCode" title="项目编码" width="100"></vxe-column>
      <vxe-column field="qdName" title="项目名称" width="100"></vxe-column>
      <vxe-column
        field="projectFeatures"
        title="项目特征"
        width="100"
        align="left"
      >
        <template #default="{ row }">
          <span class="feature-name" v-for="item in row.projectFeatures">{{
            item.featureName
          }}</span>
        </template>
      </vxe-column>
      <vxe-column field="unit" title="计量单位" width="80"></vxe-column>
      <vxe-column
        field="quantityCalcRule"
        title="工程量计算规则"
        width="100"
      ></vxe-column>
      <vxe-column field="jobContent" title="工作内容" width="100" align="left">
        <template #default="{ row }">
          <span class="feature-name" v-for="item in row.jobContent">{{
            item
          }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import api from '@/api/projectDetail';
import { projectDetailStore } from '../../../../store/projectDetail';

const props = defineProps(['currentInfo', 'type']);
const tableData = ref();
const projectStore = projectDetailStore();

onMounted(() => {
  queryListDescribe();
});

watch(
  () => props.currentInfo.standardId,
  () => {
    queryListDescribe();
  }
);

const queryListDescribe = () => {
  let apiData = {
    standardId: props.currentInfo.standardId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    branchType: props.type,
  };
  if (!apiData.standardId) {
    tableData.value = [];
    return;
  }
  console.log('清单描述api', apiData);
  api.queryListDescribe(apiData).then(res => {
    console.log('res清单描述', res);
    if (res.status === 200 && res.result) {
      tableData.value = [res.result];
    }
  });
};
</script>

<style lang="scss" scoped>
.description-info {
  font-size: 12px;
  width: 33%;
  background: #ffffff;
  box-shadow: -7px 0px 4px 0px rgba(0, 0, 0, 0.06);
  overflow-y: auto;
  padding: 13px;
  :deep(.vxe-table) {
    width: 100% !important;
  }
  .header {
    position: relative;
    margin-bottom: 15px;
    .taxTitle {
      position: relative;
      color: #2a2a2a;
      text-align: left;
      padding-left: 7px;
      line-height: 16px;
      font-size: 14px;
      border-left: 2px solid #287cfa;
    }
  }
  .feature-name {
    display: block;
  }
}
</style>
