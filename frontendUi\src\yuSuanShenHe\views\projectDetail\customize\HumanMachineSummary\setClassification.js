import { ref, defineAsyncComponent } from 'vue'
import { message } from 'ant-design-vue';
import api from '@/api/projectDetail.js';

const SetClassification = defineAsyncComponent(() =>
  import('./SetClassification.vue')
);
let classificationVisible = ref(false);
const openClassification = () => {
  classificationVisible.value = true;
};
const removeClassification = (params, callback) => {
  api.deleteRcjClassificationTable(params).then(res => {
    if (res.status === 200) {
      callback()
      message.success('删除成功')
    } else {
      message.error('删除失败')
    }
  })
}
export {
  SetClassification,
  classificationVisible,
  openClassification,
  removeClassification
}