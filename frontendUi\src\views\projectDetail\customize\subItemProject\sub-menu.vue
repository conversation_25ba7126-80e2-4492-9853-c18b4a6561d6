<template>
  <div
    class="submenu"
    ref="submenu"
    :style="{
      position: 'fixed',
      zIndex: '9998',
      boxShadow: 'var(--surely-table-popup-shadow)',
    }"
  >
    <a-menu
      style="width: 180px; font-size: 12px"
      v-for="(item, index) in menuConfig.options"
      mode="vertical"
    >
      <a-sub-menu
        :key="item.code"
        v-if="item.children?.length > 0 && item.visible"
      >
        <template #title>{{ item.name }}</template>
        <a-menu-item
          style="height: 30px; line-height: 30px; font-size: 12px"
          :style="{ display: cItem.visible ? 'block' : 'none' }"
          v-for="(cItem, cIndex) in item.children"
          :key="cItem.code"
          @click="
            emit('contextMenuClickEvent', {
              menu: cItem,
              row: props.currentInfo,
            }),
              props.args.hidePopup()
          "
          :disabled="cItem.disabled"
          >{{ cItem.name }}</a-menu-item
        >
      </a-sub-menu>
      <a-menu-item
        :key="item.code"
        :disabled="item.disabled"
        @click="
          emit('contextMenuClickEvent', { menu: item, row: props.currentInfo }),
            props.args.hidePopup()
        "
        v-if="!item.children && item.visible"
      >
        {{ item.name }}
      </a-menu-item>
    </a-menu>
  </div>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useSubItem } from '@/hooks/useSubItemStable.js';

const projectStore = projectDetailStore();
const props = defineProps({
  args: {
    type: Object,
  },
  deleteStateFn: {
    type: Function,
  },
  pasteRowVisible: {
    type: Function,
  },
  type: {
    type: String,
    default: 'fbfx',
  },
  hangMenuList: {
    type: Array,
  },
  currentInfo: {
    type: Object,
  },
  tableData: {
    type: Object,
  },
  copyData: {
    type: Object,
  },
});
const emit = defineEmits([
  'update:currentInfo',
  'contextMenuClickEvent',
  'handleNote',
  'handleMainList',
]);
let submenu = ref();
// 定位右键元素
let menuEl = ref();
onMounted(() => {
  // 获取目标元素
  let tableEl = document.querySelector('.surely-table-body-viewport-container');
  // 添加滚动事件监听器
  tableEl.addEventListener('scroll', function () {
    // 输出滚动位置
    if (submenu.value) {
      updatePosition();
    }
  });
  function calculateDistanceToViewportBottom(element) {
    const rect = element.getBoundingClientRect();
    const viewportBottom = window.innerHeight;
    const elementBottom = rect.bottom;
    return viewportBottom - elementBottom;
  }
  let isFixed = false;
  function updatePosition() {
    const distance = calculateDistanceToViewportBottom(submenu.value);
    console.log('distance', distance);
    if (!isFixed && distance <= 33) {
      submenu.value.classList.add('fixed-bottom');
      isFixed = true;
    } else if (
      isFixed &&
      calculateDistanceToViewportBottom(menuEl.value) >
        submenu.value.offsetHeight
    ) {
      // 当滚动距离大于元素高度时解锁
      submenu.value.classList.remove('fixed-bottom');
      isFixed = false;
    }
  }
  setTimeout(() => {
    updatePosition();
  }, 50);

  // 假设你有一个ID为"myElement"的元素
});
let { needAddQDandFB } = useSubItem({
  pageType: props.type,
});
const menuConfig = reactive({
  options: [
    {
      code: 'add',
      name: '插入',
      visible: true,
      children: [
        {
          code: 0,
          name: `添加${props.type === 'fbfx' ? '分部' : '标题'}`,
          kind: '01',
          visible: true,
          disabled: true,
        },
        {
          code: 1,
          name: `添加${props.type === 'fbfx' ? '子分部' : '子项'}`,
          kind: '02',
          visible: true,
          disabled: true,
        },
        {
          code: 2,
          name: '添加清单',
          kind: '03',
          visible: true,
          disabled: true,
        },
        {
          code: 3,
          name: '添加定额',
          kind: '04',
          visible: true,
          disabled: true,
        },
      ],
    },
    {
      code: 'zcsbAdd',
      name: '插入',
      visible: false,
      disabled: true,
    },
    {
      code: 'supplement',
      name: '补充',
      visible: true,
      children: [
        {
          code: 'supplement-qd',
          type: 2,
          name: '补充清单',
          kind: '03',
          visible: true,
          disabled: true,
        },
        {
          code: 'supplement-de',
          type: 3,
          name: '补充定额',
          kind: '04',
          visible: true,
          disabled: true,
        },
        {
          code: 'supplement-rcj',
          type: 3,
          name: '补充人材机',
          kind: '05',
          visible: true,
          disabled: true,
        },
      ],
    },
    {
      code: 'copy',
      name: '复制',
      visible: true,
      disabled: false,
    },
    {
      code: 'copyCell',
      name: '复制单元格内容',
      visible: true,
      disabled: false,
    },
    {
      code: 'paste',
      name: '粘贴',
      visible: true,
      disabled: false,
    },
    {
      code: 'delete',
      name: '删除',
      visible: true,
      disabled: true,
    },
    {
      code: 'lock',
      name: '清单锁定',
      visible: true,
      disabled: false,
    },
    {
      code: 'tempDelete',
      name: '临时删除',
      visible: true,
      disabled: false,
    },
    {
      code: 'MainList',
      name: '主要清单',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'set-list',
          name: '设置主要清单',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-current',
          name: '取消当前行',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-all',
          name: '取消所有清单',
          type: 3,
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'noteList',
      name: '批注',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'add-note',
          name: '插入批注',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'edit-note',
          name: '编辑批注',
          type: 2,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-note',
          name: '删除批注',
          type: 3,
          visible: true,
          disabled: false,
        },
        {
          code: 'show-note',
          name: '显示批注',
          type: 4,
          visible: true,
          disabled: false,
        },
        {
          code: 'hide-note',
          name: '隐藏批注',
          type: 5,
          visible: true,
          disabled: false,
        },
        {
          code: 'del-all-note',
          name: '删除所有批注',
          type: 6,
          visible: true,
          disabled: false,
        },
      ],
    },
    {
      code: 'batchDelete',
      name: '批量删除',
      visible: true,
      disabled: false,
      children: [
        {
          code: 'batchDelete-child1',
          name: '批量删除所有临时删除项',
          type: 1,
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete-child2',
          name: '批量删除所有工程量为0项',
          type: 2,
          visible: true,
          disabled: false,
        },
      ],
    },
    ...props.hangMenuList,
    {
      code: 'pageColumnSetting',
      name: '页面显示列设置',
      visible: true,
      disabled: false,
    },
  ],
});
const visibleMethod = async () => {
  let options = menuConfig.options;
  let row = props.args.record;
  Array.from(document.querySelectorAll('.surely-table-row')).map(a => {
    if (a.dataset.rowKey === row.sequenceNbr) {
      menuEl.value = a;
    }
  });
  if (!row) return;
  emit('update:currentInfo', row);
  projectStore.SET_SUB_CURRENT_INFO(row);
  props.deleteStateFn();
  console.log('row', row);
  options.forEach(item => {
    item.disabled = false;
    item.children?.forEach(childItem => {
      childItem.disabled = false;
    });
    if (item.code === 'add') item.visible = ![94, 95].includes(row.kind);
    if (item.code === 'zcsbAdd') item.visible = [94, 95].includes(row.kind);
    if (
      (!props.copyData || props.copyData?.length === 0) &&
      item.code === 'paste'
    ) {
      item.disabled = true;
    }
    if (props.copyData?.length > 0 && item.code === 'paste') {
      item.disabled = false;
      //粘贴代码中已经有此逻辑判断-注释
      // try {
      //   await frameSelectRef.value.frameSelectJs.isPasteBranch(
      //     row,
      //     props.copyData
      //   );
      //   item.disabled = false;
      // } catch (error) {
      //   item.disabled = true;
      // }
    }
    if (item.code === 'delete') {
      if (props.type === 'fbfx') {
        if (
          (row.optionMenu.includes(4) || row.optionMenu.includes(5)) &&
          !row.isLocked
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      } else {
        if (
          (row.optionMenu.includes(4) || row.optionMenu.includes(5)) &&
          ((row.kind === '03' &&
            row.hasOwnProperty('zjcsClassCode') &&
            row.zjcsClassCode !== null &&
            row.zjcsClassCode !== undefined &&
            Number(row.zjcsClassCode) === 0) ||
            row.constructionMeasureType === 2)
        ) {
          item.disabled = true;
        } else {
          item.disabled = row.isLocked;
        }
        if (
          (row.optionMenu.includes(4) || row.optionMenu.includes(5)) &&
          row.name !== '安全生产、文明施工费' &&
          !row.isLocked
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      }
    }
    if (item.code === 'lock') {
      if (projectStore.standardGroupOpenInfo.isOpen) {
        item.disabled = true;
      } else {
        if (row.kind === '03') {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
        if (row.isLocked) {
          item.name = '清单解锁';
        } else {
          item.name = '清单锁定';
        }
      }
    } else if (item.code === 'tempDelete') {
      let parentInfo = props.tableData.filter(
        x => x.sequenceNbr === row.parentId
      )[0];
      if (props.type === 'fbfx') {
        if (
          (row.kind === '03' && !row.isLocked) ||
          (row.kind === '04' && !parentInfo.tempDeleteFlag)
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      } else {
        if (
          ((row.kind === '03' && !row.isLocked) ||
            (row.kind === '04' && !parentInfo.tempDeleteFlag)) &&
          row.name !== '安全生产、文明施工费'
        ) {
          item.disabled = false;
        } else {
          item.disabled = true;
        }
      }

      if (row.tempDeleteFlag) {
        item.name = '取消临时删除';
      } else {
        item.name = '临时删除';
      }
    }
    if (
      item.children &&
      !['batchDelete', 'noteList', 'MainList'].includes(item.code)
    ) {
      item.disabled = false;
      item.children.forEach(childItem => {
        childItem.disabled = true;
        row.optionMenu.forEach(child => {
          if (child === childItem.code) {
            childItem.disabled = false;
          }
        });
      });
    }
    if (item.code === 'supplement') {
      item.children.forEach(childItem => {
        childItem.disabled = true;
        row.optionMenu.forEach(child => {
          if (
            child === childItem.type ||
            ([94, 95].includes(row.kind) && childItem.name === '补充人材机')
          ) {
            childItem.disabled = false;
          }
        });
      });
    }
    if (item.code === 'add') needAddQDandFB(item);
    emit('handleNote', item, row);
    emit('handleMainList', item, row);
    emit('hangMenuDisabledHandler', item, row);
  });
  console.log(options);
};
visibleMethod();
</script>
<style lang="scss" scoped>
.submenu {
}
::v-deep(.ant-menu-item) {
  font-size: 11px;
  height: 24px;
  line-height: 24px;
}
::v-deep(.ant-menu-submenu-title) {
  font-size: 11px;
  height: 24px !important;
  line-height: 24px !important;
}
.fixed-bottom {
  bottom: 33px;
}
</style>
