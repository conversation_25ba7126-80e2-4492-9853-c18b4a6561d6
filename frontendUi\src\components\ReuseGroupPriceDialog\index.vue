<!--
 * @Descripttion: 复用组价
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: liuxia
 * @LastEditTime: 2024-10-10 13:51:09
-->
<template>
  <common-modal
    className="dialog-comm ReuseGroupPrice-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    :title="title"
    width="80vw"
    height="70vh"
    min-width="900px"
    min-height="80vh"
    :mask="false"
    :lock-view="[0, 2].includes(businessType)"
    show-zoom
    resize
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div class="group-content-wrap">
      <div class="group-title-box">
        <div class="range-title">
          <div>
            <a-radio-group
              v-model:value="prodType"
              @change="handleChangeProdType"
              :options="prodOptions"
            />
            <a-button
              size="small"
              v-if="prodType == 1 && leftInfo.historyFile"
              @click="selectFile(true)"
              >选择历史工程</a-button
            >
          </div>
          <div class="right-box" v-if="[0].includes(businessType)">
            组价范围：
            <a-radio-group v-model:value="rangeType" :options="rangeOptions" />
          </div>
          <div class="right-box" v-if="[1].includes(businessType)">
            <a-button
              type="primary"
              ghost
              @click="handleZj('add')"
              :disabled="lockBtnStatus"
              >添加组价</a-button
            >
            <a-button
              type="primary"
              style="margin-left: 14px"
              ghost
              :disabled="lockBtnStatus"
              @click="handleZj('replace')"
              >替换组价</a-button
            >
          </div>
        </div>
        <div class="condition-wrap">
          <div class="left-box" v-if="[0, 1].includes(businessType)">
            <span class="title">过滤条件:</span>
            <div class="list">
              <div class="items">
                <a-checkbox
                  v-model:checked="filterList.bm"
                  @change="filterChange('bm')"
                  >编码</a-checkbox
                >
                <a-select
                  ref="select"
                  size="small"
                  v-model:value="filterList.num"
                >
                  <a-select-option :value="0">9位</a-select-option>
                  <a-select-option :value="1">12位</a-select-option>
                </a-select>
              </div>
              <div class="items">
                <a-checkbox
                  v-model:checked="filterList.name"
                  @change="filterChange('name')"
                  >名称</a-checkbox
                >
              </div>
              <div class="items">
                <a-checkbox
                  v-model:checked="filterList.projectAttr"
                  @change="filterChange('projectAttr')"
                  >项目特征
                  <a-tooltip>
                    <template #title>
                      <!-- v-html="projectAttrtooltip" -->
                      <data>模糊：过滤换行、特殊符号</data>
                    </template>
                    <icon-font type="icon-bangzhu"></icon-font>
                  </a-tooltip>
                </a-checkbox>
                <a-select
                  ref="select"
                  size="small"
                  v-model:value="filterList.type"
                >
                  <a-select-option :value="0">模糊</a-select-option>
                  <a-select-option :value="1">精准</a-select-option>
                </a-select>
              </div>
            </div>
          </div>
          <div class="left-box" v-if="[2].includes(businessType)">
            <span class="title">选择源工程及需要复用的清单:</span>
          </div>

          <div class="right-box-wrap">
            <a-input
              v-model:value="searchKey"
              @pressEnter="getTableList"
              :placeholder="`${
                [1].includes(businessType)
                  ? '名称或项目特征关键字过滤，回车...'
                  : '清单编码、名称或项目特征关键字过滤，回车执行'
              }`"
            >
              <template #suffix>
                <i
                  class="vxe-icon-search"
                  style="color: rgba(191, 191, 191, 1)"
                ></i>
              </template>
            </a-input>

            <div class="right-box" v-if="[0].includes(businessType)">
              选择复用到工程：
              <a-button size="small" @click="selectSameMajor"
                >选择同专业</a-button
              >
            </div>
          </div>
        </div>
      </div>
      <splitpanes>
        <pane
          size="15"
          min-size="15"
          max-size="30"
          v-if="[0, 2].includes(businessType)"
        >
          <div class="content">
            <a-radio-group
              v-model:value="leftInfo.selectedKeysLeft"
              v-show="prodType == 0"
            >
              <a-tree
                v-model:expandedKeys="leftInfo.expandedKeysLeft"
                :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                :tree-data="leftInfo.treeData"
                @select="selectLeft"
              >
                <template #title="{ id, name, levelType, constructMajorType }">
                  <a-tooltip placement="leftTop">
                    <template #title>{{ name }}</template>
                    <a-radio
                      :value="id"
                      v-if="[3].includes(levelType)"
                      :disabled="!constructMajorType"
                    ></a-radio>
                    <span class="check-labels">{{ name }}</span>
                  </a-tooltip>
                </template>
              </a-tree>
            </a-radio-group>

            <a-radio-group
              v-show="prodType == 1"
              v-model:value="leftInfo.selectedKeysLeft"
            >
              <a-tree
                v-model:expandedKeys="leftInfo.expandedKeysLeft"
                :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                :tree-data="leftInfo.historyFile"
                @select="selectLeft"
              >
                <template #title="{ id, name, levelType, constructMajorType }">
                  <a-tooltip placement="leftTop">
                    <template #title>{{ name }}</template>
                    <a-radio
                      :value="id"
                      v-if="[3].includes(levelType)"
                      :disabled="!constructMajorType"
                    ></a-radio>
                    <span class="check-labels">{{ name }}</span>
                  </a-tooltip>
                </template>
              </a-tree>
            </a-radio-group>
          </div>
        </pane>
        <pane>
          <div class="content content-table">
            <vxe-table
              border
              ref="vexTable"
              align="center"
              :column-config="{ resizable: true }"
              :data="TableData"
              :checkStrictly="false"
              :row-class-name="rowClassName"
              height="98%"
              :row-config="{
                isCurrent: true,
                keyField: 'sequenceNbr',
              }"
              :checkbox-config="{
                showHeader: false,
                visibleMethod: ({ row }) => {
                  return !['04'].includes(row.kind);
                },
                checkMethod: ({ row }) => {
                  return true;
                },
              }"
              :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
              :tree-config="{
                transform: true,
                rowField: 'sequenceNbr',
                parentField: 'parentId',
                line: true,
                showIcon: true,
                expandAll: true,
                iconOpen: 'vxe-icon-caret-down',
                iconClose: 'vxe-icon-caret-right',
              }"
              show-overflow="title"
              @cell-click="cellClick"
              @cell-dblclick="dbClick"
            >
              <vxe-column
                align="center"
                type="checkbox"
                width="40"
                title=""
                v-if="![1].includes(businessType)"
              ></vxe-column>
              <vxe-column field="sort" width="40" title="" />
              <vxe-column field="bdCode" tree-node width="120" title="编码">
              </vxe-column>
              <vxe-column field="type" width="40" title="类别" />
              <vxe-column
                field="quotaName"
                align="left"
                width="250"
                title="名称"
              >
                <template #default="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </vxe-column>
              <vxe-column
                field="projectAttr"
                align="left"
                width="250"
                title="项目特征"
              >
                <template #default="{ row }">
                  <span>{{ row.projectAttr }}</span>
                </template>
              </vxe-column>
              <vxe-column field="unit" width="80" title="单位"> </vxe-column>
              <vxe-column field="quantity" min-width="50" title="工程量">
              </vxe-column>
              <vxe-column field="price" min-width="80" title="综合单价">
              </vxe-column>
              <vxe-column
                field="belongsPath"
                v-if="[1].includes(businessType)"
                min-width="80"
                title="单位工程归属路径"
              >
              </vxe-column>
            </vxe-table>
          </div>
        </pane>
        <pane
          size="16"
          min-size="15"
          max-size="30"
          v-if="[0].includes(businessType)"
        >
          <div class="content">
            <a-tree
              v-model:checkedKeys="rightInfo.checkedKeys"
              v-model:expandedKeys="rightInfo.expandedKeysRight"
              :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
              checkable
              :tree-data="treeData"
            >
              <template #title="{ levelType, id, name, whetherNew, children }">
                <a-tooltip placement="leftTop">
                  <template #title>{{ name }}</template>
                  <span class="check-labels">{{ name }}</span>
                </a-tooltip>
              </template>
            </a-tree>
          </div>
        </pane>
      </splitpanes>

      <div class="footer-box">
        <div
          class="footer-history"
          style="opacity: 0"
          :style="{ opacity: filePath ? '1' : '0' }"
        >
          <p>历史工程目录：{{ filePath }}</p>
          <!-- <p>清单库：工程量清单项目计量规范(2013-河北)       定额库：全国统一建筑，工程基础定额河北省消耗量定额(2012)</p> -->
        </div>
        <div class="footer-handle">
          <a-button
            size="small"
            type="primary"
            :disabled="percentInfo.show"
            @click="autoGroup"
            v-if="[0].includes(businessType)"
            >自动组价</a-button
          >
          <div class="" v-if="[1].includes(businessType)">
            <a-checkbox
              size="small"
              v-model:checked="isLaunch"
              @change="handleChangeLaunch"
              >展开到组价</a-checkbox
            >
            <span class="handle-tips">
              <!-- <a-tooltip>
                <template #title>
                  <data v-html="projectAttrtooltip"></data>
                </template>
                <icon-font type="icon-querenshanchu"></icon-font>
              </a-tooltip> -->
              <icon-font type="icon-querenshanchu"></icon-font>
              提示:双击直接添加组价
            </span>
          </div>

          <a-checkbox
            v-if="[2].includes(businessType)"
            size="small"
            v-model:checked="isLaunch"
            @change="handleChangeLaunch"
            >展开到组价</a-checkbox
          >
          <div class="rang-box" v-if="[2].includes(businessType)">
            <span class="rang-tips">提取范围:</span>
            <a-radio-group v-model:value="ExtractionScope" name="radioGroup">
              <a-radio :value="1">清单+组价</a-radio>
              <a-radio :value="2">仅淸单</a-radio>
            </a-radio-group>
          </div>
          <a-button
            v-if="[2].includes(businessType)"
            :disabled="percentInfo.show"
            size="small"
            type="primary"
            @click="insertData"
            >插入数据</a-button
          >
        </div>
      </div>
      <a-progress
        :percent="percentInfo.percent"
        v-if="percentInfo.show"
        status="active"
      />
    </div>
  </common-modal>
</template>
<script setup>
import {
  ref,
  toRaw,
  watchEffect,
  nextTick,
  reactive,
  computed,
  shallowRef,
  shallowReactive,
  defineExpose,
  getCurrentInstance,
  watch,
} from 'vue';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { Splitpanes, Pane } from 'splitpanes';
import { constructLevelTreeStructureList } from '@/api/csProject';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import 'splitpanes/dist/splitpanes.css';
import { message } from 'ant-design-vue';

const cxt = getCurrentInstance();
const $ipc = cxt.appContext.config.globalProperties.$ipc;
const props = defineProps({
  currentInfo: {
    type: Object,
    default: null,
  },
  type: {
    type: String,
    default: 'fbfx',
  },
  lockBtnStatus: {
    type: Boolean,
    default: false,
  },
});

let editAreaCurrentInfo = shallowRef(null);

const route = useRoute();
const emits = defineEmits(['closeDialog', 'refresh']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);
const projectAttrtooltip = `<p>项目特征：指标的具体特征，<br>如：规划、设计、施工、监理等。</p>`;

let searchKey = ref('');
let title = ref('复用组价');
let prodType = ref(0);
let loading = ref(false);

let filterList = reactive({
  bm: true,
  num: 0,
  name: true,
  projectAttr: true,
  type: 0,
});
let prodOptions = [
  { label: '本工程', value: 0 },
  { label: '历史工程', value: 1 },
];

let rangeType = ref(1);
let ExtractionScope = ref(1);
let rangeOptions = [
  { label: '所有清单', value: 1 },
  { label: '未组价清单', value: 0 },
];

const treeData = shallowRef([]);
const initTreeData = shallowRef([]);

let leftInfo = shallowReactive({
  expandedKeysLeft: [],
  selectedKeysLeft: '',
  currentInfo: {},
  historyFile: [],
  loading: false,
  treeData: [],
});
let qdList = ref([]);
let rightInfo = shallowReactive({
  checkedKeys: [],
  expandedKeysRight: '',
});
let isLaunch = ref(true);
let percentInfo = shallowReactive({
  percent: 0,
  show: false,
});
let filePath = ref(null);

const startPercent = () => {
  percentInfo.show = true;
  percentInfo.percent = 0;
  $ipc.on(store.currentTreeGroupInfo?.constructId, (event, arg) => {
    if (arg.percent >= percentInfo.percent) {
      percentInfo.percent = arg.percent;
    }
    if (arg.percent >= 100) {
      setTimeout(() => {
        percentInfo.show = false;
      }, 500);
    }
  });
};

// 切换工程
const handleChangeProdType = v => {
  if (v.target.value == 1) {
    if (
      [1].includes(businessType.value) &&
      ['0', '01', '02'].includes(props.currentInfo?.kind)
    ) {
      message.error('请编辑区选择清单');
      prodType.value = 0;
      return;
    }
    upFile();
  } else {
    leftInfo.treeData = treeData.value;
    getList();
    leftInfo.selectedKeysLeft = '';
  }
};

let openLocalProStatus = ref(false);
const selectFile = (type = false) => {
  if (openLocalProStatus.value) {
    message.info('已打开系统弹窗');
    return;
  }
  upFile(type);
};

// 上传文件文件
const upFile = (type = false) => {
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: leftInfo.currentInfo?.parentId, //单项ID
    type: 'history',
    selectHistory: type,
  };

  //  提取已有组价，
  if ([1].includes(businessType.value)) {
    const { kind, sequenceNbr, parentId, displayStatu, displaySign } = {
      ...toRaw(editAreaCurrentInfo.value),
    };
    postData.qdId = kind == '03' ? sequenceNbr : kind == '04' ? parentId : null;
    postData.fbfxOrCsxm = props.type;
    postData.is12BitQdCode = filterList.bm
      ? filterList.num == 1
        ? true
        : false
      : false;
    postData.is9BitQdCode = filterList.bm
      ? filterList.num == 0
        ? true
        : false
      : false;
    postData.isQdName = filterList.name;
    (postData.isProjectAttr = filterList.projectAttr
      ? filterList.type == 1
        ? 'precise'
        : 'similar'
      : null),
      (postData.singleId = store.currentTreeInfo?.parentId); //单位ID
    postData.unitId = store.currentTreeInfo?.id; //单位ID
  }

  let nameList = [
    'projectConstructQuery',
    'matchQueryBySelected',
    'projectConstructQuery',
  ];
  console.log('🚀 ~上传文件:', postData);

  openLocalProStatus.value = true;
  csProject[nameList[businessType.value]](postData)
    .then(res => {
      console.log('🚀 ~ selectFile ~ res:', res);
      if ([200].includes(res.code)) {
        filePath.value = res.result?.path.replaceAll('\\', '/');

        if ([0, 2].includes(businessType.value)) {
          // 自动复用组价 提取已有清单
          const expandedKeys = [];
          res.result.treeNode.forEach(e => {
            if (e.levelType != 3) {
              expandedKeys.push(e.id);
            }

            if (!e.constructMajorType) {
              e.disabled = true;
            }
          });

          leftInfo.historyFile = xeUtils.toArrayTree(res.result.treeNode, {
            parentKey: 'parentId',
            childrenKey: 'children',
          });

          nextTick(() => {
            leftInfo.expandedKeysLeft = expandedKeys;
            let unit = res.result.treeNode.find(a => a.levelType === 3);
            if (unit) {
              leftInfo.selectedKeysLeft = unit.id;
              leftInfo.currentInfo = unit;
              getTableList();
            }
          });
        } else {
          leftInfo.historyFile = res.result.data;
          handleTable(res.result?.data);
        }

        const node = findLastLeafNode(leftInfo.historyFile[0], true);
        if (node.levelType == 3) {
          leftInfo.selectedKeysLeft = node.id;
        } else {
          leftInfo.selectedKeysLeft = '';
        }
      } else {
        if (res.message) {
          message.error(res.message);
        }
        prodType.value = 0;
        nextTick(() => {
          leftInfo.expandedKeysLeft = currentExpandKeys.value;
        });
      }
    })
    .finally(() => {
      openLocalProStatus.value = false;
    });
};

const cancel = (refresh = false) => {
  if (refresh) {
    emits('refresh');
    // getTableList()
    return;
  }
  businessType.value = null;
  dialogVisible.value = false;
  emits('closeDialog');
};

const selectLeft = (selectedKeys, { selected, node }) => {
  if (selected && node.levelType == 3) {
    leftInfo.selectedKeysLeft = node.dataRef.id;
    leftInfo.currentInfo = node.dataRef;
    console.log(
      '🚀 ~ selectLeft ~ leftInfo.currentInfo:',
      leftInfo.currentInfo
    );
    getTableList();
  }
};

let currentExpandKeys = shallowRef([]);
let originalList = ref([]);
const getList = () => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      const expandedKeys = [];

      res.result.forEach(e => {
        if (e.levelType != 3) {
          expandedKeys.push(e.id);
        }

        if (!e.constructMajorType) {
          e.disabled = true;
        }
      });

      rightInfo.expandedKeysRight = expandedKeys;
      rightInfo.checkedKeys = [];
      leftInfo.expandedKeysLeft = expandedKeys;
      currentExpandKeys.value = expandedKeys;
      leftInfo.selectedKeysLeft = store.currentTreeInfo.constructMajorType
        ? store.currentTreeInfo.id
        : '';
      leftInfo.currentInfo = store.currentTreeInfo;
      leftInfo.treeData = xeUtils.toArrayTree(res.result, {
        parentKey: 'parentId',
        childrenKey: 'children',
      });

      initTreeData.value = res.result;
      let obj = res.result.filter(x => x.id === leftInfo.selectedKeysLeft)[0];
      originalList.value = JSON.parse(JSON.stringify(res.result));
      treeData.value = originalList.value.filter(
        x =>
          x.levelType !== 3 ||
          (x.levelType === 3 &&
            x.deStandardReleaseYear === obj?.deStandardReleaseYear)
      );
      treeData.value = xeUtils.toArrayTree(treeData.value, {
        parentKey: 'parentId',
        childrenKey: 'children',
      });

      if (![1].includes(businessType.value)) {
        getTableList();
      }
    }
  );
};

watch(
  () => leftInfo.selectedKeysLeft,
  () => {
    let obj = originalList.value.filter(
      x => x.id === leftInfo.selectedKeysLeft
    )[0];
    treeData.value = originalList.value.filter(
      x =>
        x.levelType !== 3 ||
        (x.levelType === 3 &&
          x.deStandardReleaseYear === obj?.deStandardReleaseYear)
    );
    treeData.value = xeUtils.toArrayTree(treeData.value, {
      parentKey: 'parentId',
      childrenKey: 'children',
    });
  }
);
const getTableList = async () => {
  //处理临时锁定数据  新增的是定额需要处理
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: leftInfo.currentInfo?.parentId, //单项ID
    unitId: leftInfo.selectedKeysLeft, //单位ID
    type: !prodType.value ? 'current' : 'history',
    searchKey: searchKey.value,
  };

  if (!postData.unitId) return;

  //  提取已有组价，
  if ([1].includes(businessType.value)) {
    const { kind, sequenceNbr, parentId, displayStatu, displaySign } = {
      ...toRaw(editAreaCurrentInfo.value),
    };
    postData.qdId = kind == '03' ? sequenceNbr : kind == '04' ? parentId : null;
    postData.fbfxOrCsxm = props.type;
    postData.is12BitQdCode = filterList.bm
      ? filterList.num == 1
        ? true
        : false
      : false;
    postData.is9BitQdCode = filterList.bm
      ? filterList.num == 0
        ? true
        : false
      : false;
    postData.isQdName = filterList.name;
    postData.isProjectAttr = filterList.projectAttr
      ? filterList.num == 1
        ? 'precise'
        : 'similar'
      : null;
  }

  let nameList = [
    'unitProjectQdQuery',
    'matchQueryBySelected',
    'unitProjectQdQuery',
  ];

  const res = await csProject[nameList[businessType.value]]({
    ...postData,
    containDe: [0, 1].includes(businessType.value),
  });
  console.log('🚀 ~ 列表数据 ~ res:', postData, res);

  if (res.code != 200) return;
  let resultData = [0, 2].includes(businessType.value)
    ? res.result
    : res.result?.data;

  handleTable(resultData);
};

const handleTable = resultData => {
  let allList = [];
  let cxList = [];
  let useQdList = [];

  let isBusinessType2 = [0, 2].includes(businessType.value);

  allList =
    resultData[isBusinessType2 ? 1 : 0]?.map(i => {
      if (['03'].includes(i.kind)) {
        i.parentId = '01';
      }
      useQdList.push(i.sequenceNbr);
      return i;
    }) || [];
  cxList =
    resultData[isBusinessType2 ? 3 : 1]?.map(i => {
      if (['03'].includes(i.kind)) {
        i.parentId = '02';
      }
      useQdList.push(i.sequenceNbr);
      return i;
    }) || [];

  let list = [
    {
      sequenceNbr: '01',
      parentId: 0,
      kind: '01',
      name: isBusinessType2 ? resultData[0]?.name : '整个项目',
    },
    {
      sequenceNbr: '02',
      parentId: 0,
      kind: '02',
      name: isBusinessType2 ? resultData[2]?.name : '措施项目',
      children: [],
    },
    ...cxList,
    ...allList,
  ];

  TableData.value = list;

  nextTick(() => {
    if (vexTable.value) {
      console.log('🚀 ~ nextTick ~ vexTable.value:', vexTable.value);
      vexTable.value.setAllTreeExpand(true);

      console.log('🚀 ~ nextTick ~ businessType.value:', businessType.value);
      if ([0, 2].includes(businessType.value)) {
        // vexTable.value.toggleAllCheckboxRow()
        vexTable.value.setAllCheckboxRow(true);
      } else {
        vexTable.value.setCurrentRow(0);
      }
    }
  });
};

const handleChangeLaunch = () => {
  // isLaunch
  if (isLaunch.value) {
    vexTable.value.setAllTreeExpand(true);
  } else {
    vexTable.value.clearTreeExpand();
  }
};

const selectSameMajor = e => {
  let data = initTreeData.value.filter(item => {
    return (
      item.secondInstallationProjectName ==
        leftInfo.currentInfo.secondInstallationProjectName &&
      leftInfo.currentInfo.constructMajorType == item.constructMajorType
    );
  });
  rightInfo.checkedKeys = data.map(item => item.id);
};

let businessType = ref(0);
const open = k => {
  let titles = ['自动复用组价', '提取已有组价', '提取已有清单'];
  title.value = titles[k];
  businessType.value = k;
  dialogVisible.value = true;
  searchKey.value = '';
  prodType.value = 0;
  qdList.value = [];
  percentInfo.show = false;
  loading.value = false;
  filePath.value = null;

  getList();
};

const filterChange = type => {
  let { bm, name, projectAttr } = filterList;
  if (!bm && !name && !projectAttr) {
    filterList[type] = true;
  }
};

const rowClassName = ({ row }) => {
  let ClassStr = '';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.parentId == 1) {
    return `row-tree-title  ${ClassStr}`;
  } else {
    return ClassStr;
  }
};

const cellClick = ({ row }) => {
  if ([0, 2].includes(businessType.value)) return;

  let { parentId, sequenceNbr, kind, children } = row;

  if (!['03', '04'].includes(kind)) return;

  let ids = [sequenceNbr];
  if (['03'].includes(kind) && children.length) {
    ids = children.map(i => i.sequenceNbr);
  }

  if (['04'].includes(kind)) {
    ids.push(parentId);
    TableData.value.forEach(i => {
      if (i.parentId == parentId) {
        ids.push(i.sequenceNbr);
      }
    });
  }

  qdList.value = ids;
  vexTable.value.$el
    .querySelectorAll('.vxe-body--row')
    .forEach((node, index) => {
      if (ids.includes(node.attributes['rowid'].value)) {
        node.classList.add('row--current');
      }
    });
};

const dbClick = () => {
  if ([0, 2].includes(businessType.value)) return;
  if (props.lockBtnStatus || loading.value) return;
  handleZj('add');
};

const checkStatus = () => {
  let msg = '';

  const list = vexTable.value.getCheckboxRecords();
  if (list.length) {
    const newQdList = list.filter(i => {
      return !['01', '02'].includes(i.sequenceNbr);
    });
    qdList.value = newQdList.map(i => i.sequenceNbr);
  }

  if (!qdList.value.length) {
    msg = '未选择任何已组价，请选择后再执行组价';
  } else if (
    [0].includes(businessType.value) &&
    !rightInfo.checkedKeys?.length
  ) {
    msg = '未选择目标单位工程，请选择后再执行自动组价';
  } else if (
    [0, 2].includes(businessType.value) &&
    !leftInfo.selectedKeysLeft
  ) {
    msg = '未选择源工程单位工程，请选择后再执行自动组价';
  }

  if (msg) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: msg,
      confirm: () => {
        infoMode.hide();
      },
    });
  }

  return !!msg;
};

const handlePostData = () => {
  // const {kind,sequenceNbr,parentId,displayStatu,displaySign} = {...toRaw(editAreaCurrentInfo.value)}
  const postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo.id, //单位ID
    type: !prodType.value ? 'current' : 'history',
    sequenceNbrArray: toRaw(qdList.value),
    pointLine: JSON.parse(JSON.stringify(editAreaCurrentInfo.value)),
    dataType: ExtractionScope.value == 1 ? 'qdDe' : 'qd',
    newConstructId: leftInfo.treeData[0]?.id,
    newSingleId: leftInfo.currentInfo?.parentId, //单项ID
    newUnitId: leftInfo.currentInfo.id, //单位ID
    qdType: props.type,
  };

  return postData;
};

// 自动组价
const autoGroup = () => {
  if (checkStatus()) return;
  let data = handlePostData();
  const postData = {
    ...data,
    is12BitQdCode: filterList.bm ? (filterList.num == 1 ? true : false) : false,
    is9BitQdCode: filterList.bm ? (filterList.num == 0 ? true : false) : false,
    isQdName: filterList.name,
    isProjectAttr: filterList.projectAttr
      ? filterList.type == 1
        ? 'precise'
        : 'similar'
      : null,
    isMerge: !!rangeType.value,
    unitIdsApply: rightInfo.checkedKeys,
    qdDeIds: data.sequenceNbrArray,
    unitId: data.newUnitId,
    singleId: data.newSingleId,
    constructId: data.newConstructId,
  };

  console.log('🚀 ~ autoGroup ~ postData:', postData);
  startPercent();
  csProject.unitProjectSelectedMatchMerge(postData).then(res => {
    console.log('🚀 ~ csProject.unitProjectSelectedMatchMerge ~ res:', res);
    let { code, result } = res;
    setTimeout(() => {
      if ([200].includes(code)) {
        cancel(true);
        infoMode.show({
          isSureModal: true,
          infoText: `成功执行组价清单${result.successMergeQdNum}条，未执行组价清单${result.notExecute}条`,
          confirm: () => {
            infoMode.hide();
          },
        });
      }
    }, 600);
  });
};

// 添加替换组价
const handleZj = addOrReplace => {
  // if(checkStatus())return
  let data = handlePostData();
  const { kind, sequenceNbr, parentId } = {
    ...toRaw(editAreaCurrentInfo.value),
  };

  const list = vexTable.value.getCurrentRecord();

  if (!['03', '04'].includes(list?.kind)) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: '请选择清单或者定额进行操作',
      confirm: () => {
        infoMode.hide();
      },
    });
    return;
  }

  let postData = {
    ...data,
    qdId: kind == '03' ? sequenceNbr : kind == '04' ? parentId : null,
    fbfxOrCsxm: props.type,
    qdDeIds: data.sequenceNbrArray,
    addOrReplace,
  };
  loading.value = true;
  console.log('🚀 ~ handleZj ~ postData:', postData);
  csProject
    .addOrReplaceMerge(postData)
    .then(res => {
      console.log('🚀 ~ 提取已有清单 ~ res:', res);
      cancel(true);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 插入数据
const insertData = () => {
  if (checkStatus()) return;
  const postData = handlePostData();
  console.log('🚀 ~ insertData ~ postData:', postData);

  loading.value = true;
  csProject
    .insertQdDe({ ...postData })
    .then(res => {
      console.log('🚀 ~ csProject.insertQdDe ~ res:', res);
      // startPercent()
      cancel(true);
    })
    .finally(() => {
      loading.value = false;
    });
};

const findLastLeafNode = (node, isFirst = false) => {
  if (node?.children && node.children.length > 0) {
    return findLastLeafNode(
      node.children[isFirst ? 0 : node.children.length - 1],
      isFirst
    );
  } else {
    return node;
  }
};

watchEffect(() => {
  editAreaCurrentInfo.value = props.currentInfo;
  if ([1].includes(businessType.value) && dialogVisible.value) {
    getTableList();
  }
});

watchEffect(() => {
  if ([1].includes(businessType.value)) {
    leftInfo.currentInfo = store.currentTreeInfo;
    leftInfo.selectedKeysLeft = store.currentTreeInfo.constructMajorType
      ? store.currentTreeInfo.id
      : '';
  }
});

defineExpose({
  open,
});
</script>

<style lang="scss">
.ReuseGroupPrice-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .check-labels {
    white-space: nowrap;
  }
  .content-table {
    padding: 0 10px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .group-title-box {
    .range-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
    }
  }
  .condition-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 17px 0;
    font-size: 14px;
    margin-top: 17px;
    border-top: 1px solid rgba(185, 185, 185, 1);
    .left-box {
      display: flex;
      flex-wrap: wrap;
    }
    .items {
      margin-left: 13px;
      display: flex;
      align-items: center;
    }
    .list {
      display: flex;
      align-items: center;
    }
  }

  .footer-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 17px;
    .footer-history {
      max-width: 50%;
      p {
        font-weight: 400;
        font-size: 12px;
        color: #2a2a2a;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
    .footer-handle {
      display: flex;
      align-items: center;
      .rang-tips {
        margin-right: 10px;
        font-size: 14px;
      }
    }
  }

  .right-box-wrap {
    display: flex;
    align-items: center;
    .right-box {
      margin-left: 14px;
      word-break: normal;
      white-space: pre;
    }
  }

  .splitpanes {
    flex: 1;
    overflow: auto;
    .content {
      width: 100%;
      height: 100%;
      overflow: auto;
      padding: 2px;
      border: 1px solid #b9b9b9;
    }
  }

  .handle-tips {
    font-weight: 400;
    font-size: 12px;
    color: #2a2a2a;
    margin-left: 16px;
  }

  .splitpanes__splitter {
    min-width: 6px;
    border-radius: 4px;
    margin: 0 3px;
    background: #fff;
    &:hover {
      background-color: rgba(24, 144, 255, 0.8);
    }
  }

  .vxe-table {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
    .row-unit {
      background: #e6dbeb !important;
    }
    .row-sub {
      background: #efe9f2 !important;
    }
    .row-qd {
      background: #dce6fa !important;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
}
</style>
