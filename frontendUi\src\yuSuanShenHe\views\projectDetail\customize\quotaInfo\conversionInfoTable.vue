<!--
 * @Descripttion: 换算信息
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: liuxia
 * @LastEditTime: 2023-06-20 16:12:20
-->
<template>
  <vxe-table :data="props.tableData" height="auto" class="table-scrollbar">
    <vxe-column width="60">
      <template #default="{ row, $rowIndex }">
        {{ $rowIndex + 1 }}
      </template>
    </vxe-column>
    <vxe-column title="换算串" field="conversionString"></vxe-column>
    <vxe-column title="说明" field="conversionExplain"></vxe-column>
    <vxe-column title="来源" field="source"></vxe-column>
  </vxe-table>
</template>

<script setup>
const props = defineProps(['tableData']);
</script>

<style lang="scss" scoped></style>
