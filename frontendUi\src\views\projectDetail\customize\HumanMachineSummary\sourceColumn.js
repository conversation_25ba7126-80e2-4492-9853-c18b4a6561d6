import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { includes } from 'lodash';
const store = projectDetailStore();
/**
 * 定额类型判断
 * @param {*} type
 * @param {*} rowType 有则判断得是工程项目重得行数据类型
 * @returns
 */
export const isDeType = (type, rowType = '') => {
  // debugger;
  if (store.currentTreeInfo.levelType === 1 && rowType) {
    return rowType === type;
  }
  return store.deStandardReleaseYear === type;
};
const getTableColumns = (emits, type) => {
  let tableColumns = [
    // 常用项
    {
      title: '所在单位工程',
      field: 'location',
      width: 150,
      classType: 1,
      fixed: 'left',
      slot: false,
    },
    {
      title: '名称',
      field: 'materialName',
      width: 150,
      editRender: {
        enabled: true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      fixed: 'left',
      slot: true,
    },
    {
      title: '规格型号',
      field: 'specification',
      width: 100,
      editRender: {
        enabled: true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '单位',
      field: 'unit',
      width: 100,
      classType: 1,
      slot: false,
    },
    {
      title: '数量',
      field: 'totalNumber',
      width: 100,
      classType: 1,
      slot: false,
    },
    {
      title: '市场价',
      field: 'marketPrice',
      width: 110,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '不含税市场价',
      field: 'priceMarket',
      width: 110,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    // {
    //   title: '含税市场价',
    //   field: 'priceMarketTax',
    //   width: 110,
    //   editRender: {
    //     autofocus: '.vxe-input--inner',
    //   },
    //   classType: 1,
    //   slot: true,
    // },
    {
      title: '市场价合计',
      field: 'total',
      width: 110,
      classType: 1,
      slot: false,
    },
    {
      title: '不含税市场价合计',
      field: 'priceMarketTotal',
      width: 110,
      classType: 1,
      slot: false,
    },
    // {
    //   title: '含税市场价合计',
    //   field: 'priceMarketTaxTotal',
    //   width: 110,
    //   classType: 1,
    //   slot: false,
    // },
    {
      title: '供应类别',
      field: 'ifDonorMaterial',
      width: 100,
      classType: 1,
      slot: false,
    },
    {
      title: '甲供数量',
      field: 'donorMaterialNumber',
      width: 100,
      classType: 1,
      slot: false,
    },
    {
      title: '价格来源',
      field: 'sourcePrice',
      width: 150,
      classType: 1,
    },
    {
      title: '产地',
      field: 'producer',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '厂家',
      field: 'manufactor',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '品牌',
      field: 'brand',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '送达地点',
      field: 'deliveryLocation',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '质量等级',
      field: 'qualityGrade',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
  ];
  tableColumns = tableColumns.filter(item => {
    const field = item.field;

    if (['priceMarketTotal'].includes(field) && Number(store.taxMade) === 0) {
      // 'priceMarket',如果是不含税并且是简易计税的话，不显示;一般-不含税
      return false;
    }
    if (
      ['priceMarketTaxTotal'].includes(field) &&
      Number(store.taxMade) === 1
    ) {
      // 'priceMarketTax',如果是含税并且是一般计税的话，不显示;简易-含税
      return false;
    }
    if (store.currentTreeInfo.levelType !== 3) {
      // 工程项目级别
      // if (
      //   [
      //     'producer',
      //     'manufactor',
      //     'brand',
      //     'deliveryLocation',
      //     'qualityGrade',
      //   ].includes(field)
      // ) {
      //   return false;
      // }
      if (isDeType('22') && ['marketPrice', 'total'].includes(field)) {
        return false;
      }
      if (store.deType === '12') {
        if (
          [
            'priceMarketTax',
            'priceMarket',
            'priceMarketTaxTotal',
            'priceMarketTotal',
          ].includes(field)
        ) {
          // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
          return false;
        }
      }
      return true;
    }

    if (
      isDeType('12') &&
      [
        'priceMarketTax',
        'priceMarket',
        'priceMarketTaxTotal',
        'priceMarketTotal',
      ].includes(field)
    ) {
      // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
      return false;
    }
    if (isDeType('22')) {
      if (['marketPrice', 'total'].includes(field)) {
        // 市场价、市场价合价、除税系数22不显示
        return false;
      }
    }
    return true;
  });
  return tableColumns;
};

export const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];
export const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
export default getTableColumns;
