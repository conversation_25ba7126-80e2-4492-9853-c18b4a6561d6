const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const { ObjectUtils } = require('../utils/ObjectUtils');
const DeTypeConstants = require("../constants/DeTypeConstants");

class GsQuantitiesController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    async getList(args) {
        let {constructId, unitId, deId} = args;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let deMap = unitQuantiesMap?.get(deId);
        if (ObjectUtils.isEmpty(deMap) || ObjectUtils.isEmpty(deMap.quantities)) {
            let deLine = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
            if (deLine.type !== DeTypeConstants.DE_TYPE_DEFAULT
                && deLine.type !== DeTypeConstants.DE_TYPE_EMPTY
                && deLine.type !== DeTypeConstants.DE_TYPE_FB
                && deLine.type !== DeTypeConstants.DE_TYPE_ZFB
            ) {
                await this.service.PreliminaryEstimate.gsInitDeService.initDeQuantities(deLine);
            }
            deMap = unitQuantiesMap?.get(deId);
        }
        if (ObjectUtils.isNotEmpty(deMap)){
            let result = deMap.quantities.filter(item=> item.variables !== 'GCGM')
            return ResponseData.success(result);
        }
        return ResponseData.success([]);
    }

    /**
     * 工程量明细 新增
     * @param param
     * @returns {ResponseData}
     */
    async insert(param) {
        let {constructId, singleId, unitId, type, lineId, selectId}  = param;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineId);
        let newLine = await this.service.PreliminaryEstimate.gsQuantitiesService.addAfter(pointLine, selectId);
        newLine.quotaListId = pointLine.quotaListId;
        return ResponseData.success(newLine);
    }

    /**
     * 工程量明细 新增
     * @param param
     * @returns {ResponseData}
     */
    async copy(param) {
        let {constructId, singleId, unitId, type, lineId, selectId}  = param;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineId);
        let quantities = await this.service.PreliminaryEstimate.gsQuantitiesService.getQuantitiesList(pointLine);
        let newLine = await this.service.PreliminaryEstimate.gsQuantitiesService.addAfter(pointLine, selectId);
        newLine.quotaListId = pointLine.quotaListId;
        newLine.mathFormula = quantities.filter(item => item.sequenceNbr === selectId)[0].mathFormula;
        newLine.accumulateFlag = 1
        let args = {constructId, singleId, unitId, type, quotaListId: lineId, pointLine: newLine};
        await this.updateQuantityData(args);
        return ResponseData.success(newLine);
    }

    /**
     * 工程量明细 追加
     * @param param
     * @returns {ResponseData}
     */
    async append(param) {
        let {constructId, singleId, unitId, type, lineId, pointLine}  = param;
        let deLine = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, lineId);
        let originalQuantity = deLine.originalQuantity;

        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let quotaLine = unitQuantiesMap.get(lineId);
        let quantities = await this.service.PreliminaryEstimate.gsQuantitiesService.getQuantitiesList(quotaLine);
        let args0 = {constructId, singleId, unitId, type, quotaListId: lineId, pointLine: pointLine};
        await this.updateQuantityData(args0);

        let newLine = await this.service.PreliminaryEstimate.gsQuantitiesService.add(quotaLine, quantities[0].sequenceNbr);
        newLine.quotaListId = quotaLine.quotaListId;
        newLine.mathFormula = String(originalQuantity);
        newLine.accumulateFlag = 1
        let args = {constructId, singleId, unitId, type, quotaListId: lineId, pointLine: newLine};
        await this.updateQuantityData(args);
        return ResponseData.success(newLine);
    }

    /**
     * 移动
     * @param param
     * @returns {ResponseData}
     */
    async move(param){
        let {constructId, singleId, unitId, type, lineInfo, direction}  = param;
        // let pointLine = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === lineInfo.quotaListId);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineInfo.quotaListId);
        let qtId = lineInfo.sequenceNbr;
        if (direction === 1) { // 0上 1下
            await this.service.PreliminaryEstimate.gsQuantitiesService.moveDown(pointLine, qtId);
        } else {
            await this.service.PreliminaryEstimate.gsQuantitiesService.moveUp(pointLine, qtId);
        }
        return ResponseData.success(true);
    }


    /**
     * 工程量明细
     * @param param
     * @returns {Promise<ResponseData>}
     */
    async delete(param) {
        let {constructId, singleId, unitId, type, lineId, deleteId} = param;
        // let pointLine = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === lineId);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineId);
        let newLine = await this.service.PreliminaryEstimate.gsQuantitiesService.delete(pointLine, deleteId);
        return ResponseData.success(true);
    }

    /**
     * 工程量明细处修改数据
     * @param param
     * @returns {Promise<ResponseData>}
     */
    async updateQuantityData(param) {
        let {constructId, singleId, unitId, type, quotaListId, pointLine} = param;
        let {sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag} = pointLine;
        // let quotaLine = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === quotaListId);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let quotaLine = unitQuantiesMap.get(quotaListId);
        if (ObjectUtils.isEmpty(pointLine.mathFormula)) {
            quotaLine.quantities.find(item => item.sequenceNbr === sequenceNbr).accumulateFlag = pointLine.accumulateFlag
            return ResponseData.success(true);
        }

        await this.service.PreliminaryEstimate.gsQuantitiesService.upDate(quotaLine, sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag, type, constructId, singleId, unitId);

        let gcgmParams = {
            constructId,
            unitId,
            type: "13",
            levelType: 3
        }
        let gctz = await this.service.PreliminaryEstimate.gsOverviewService.getList(gcgmParams);
        let gcgm = gctz.find(item => item.name === '工程规模')?.context
        let gcgmQuantityLine = quotaLine.quantities.find(item => item.variables  === 'GCGM')
        if (gcgm !== gcgmQuantityLine.mathResult) {
            await this.service.PreliminaryEstimate.gsQuantitiesService.upDate(quotaLine, gcgmQuantityLine.sequenceNbr, String(gcgm),
                gcgmQuantityLine.mathIllustrate, gcgmQuantityLine.mathResult, gcgmQuantityLine.variables,
                gcgmQuantityLine.accumulateFlag, type, constructId, singleId, unitId);
        }
        return ResponseData.success(true);
    }


}

GsQuantitiesController.toString = () => '[class GsQuantitiesController]';
module.exports = GsQuantitiesController;
