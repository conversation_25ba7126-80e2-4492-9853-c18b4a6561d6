const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const { rcjBaseFn, RJCRules, PBRules} = require('./ResourceCodes');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const ResourceKindConstants = require("../../../constants/ResourceKindConstants");
const WildcardMap = require('../../../core/container/WildcardMap');
const LogUtil = require("../../../core/tools/logUtil");
const {NumberUtil} = require("../../../utils/NumberUtil");
//const { textSpanIntersectsWithTextSpan } = require('typescript');
const CommonConstants = require('../../../constants/CommonConstants');
const ZSFeeConstants = require("../../../constants/ZSFeeConstants");
const TSRCJConstants = require("../../../constants/TSRCJConstants");
const CostDeMatchConstants = require('../../../constants/CostDeMatchConstants');
const DeCommonConstants = require("../../../constants/DeCommonConstants");
const DeUtils = require('../../utils/DeUtils');
const EE = require('../../../../../core/ee');

class ResourceCalculator extends CalculateEngine{
  static SPLITOR = "_";
  constructId;
  deRowId;
  currentDe;
  precision;
  unitId;
  resourceId;
  ctx;
  rcjContext = [];
  digitPropertyMap = new  Map();

  static rcjMap = [
    "totalNumber",//数量
    "total", //总价
    "totalTax",
    "baseJournalTotal",
    "baseJournalTotalTax",
    //"dePrice",//价格
    // "resQty",
    // "baseJournalPrice",//基期价
    // "baseJournalTaxPrice",//基期含税价
    // "marketPrice",//市场价
    // "marketTaxPrice",//市场含税价
    "scCount"
  ];
   static  rcjSubMap = [
    "total",
    "totalNumber",
    "scCount"
  ];





  convertValue(value,param) {
    // let paramArray = param.split(ResourceCalculator.SPLITOR);
    // let digits = this.digitPropertyMap.get(paramArray[0]);
    // if(ObjectUtils.isEmpty(digits)) {
    //   digits = 2;
    // }
    // return NumberUtil.numberScale(value, digits);
    return  value;
  }
  initDigitPropertyMap()
  {
    this.digitPropertyMap.set("resQty",5);
    this.digitPropertyMap.set("totalNumber",5);
    this.digitPropertyMap.set("quantity",5);
    this.digitPropertyMap.set("scCount",4);
  }

  static getInstance({constructId, unitId,deRowId},ctx){
    return new ResourceCalculator(constructId,unitId,deRowId,null,ctx);
  }

  /**
   *
   * @param constructId 当前工程
   * @param unitId 当前修改的人材机所属的单位工程
   * @param deRowId 当前修改的人材机所属的定额
   * @param resourceId 为当前修改的人材机ID
   * @param ctx
   */
  constructor(constructId,unitId,deRowId,resourceId,ctx) {
    super(ctx);
    this.ctx = ctx;
    this.constructId = constructId;
    this.unitId = unitId;
    this.deRowId = deRowId;
    this.resourceId = resourceId;
    this.initDigitPropertyMap();

  }
  async prepare()
  {
    let {service} = EE.app;
    this.precision = await service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(this.constructId);
  }

  async analyze() {
    await this.prepare();
    this.preload(rcjBaseFn);
    this.initCurrentDe();
    this.buildRules();
    await this.render()
  }

  /**
   * 填充人材机数据
   */
  async render() {
    let precisionObj = DeUtils.getPrecisionByRcj(this.precision);
    this.rcjContext.forEach(item =>{
      for (const key of ResourceCalculator.rcjMap) {
        
        LogUtil.renderLogger("ResourceCalculator :" + item.sequenceNbr + "---key :" + key );
        
        if (key !== "scCount") {
          let digitalKey = key;
          if(["total","totalTax", "baseJournalTotal", "baseJournalTotalTax"].includes(key)){
              digitalKey = 'total';
          }
          let digital = precisionObj[digitalKey];
          if(ObjectUtils.isEmpty(digital)){
            digital = 2;
          }
          let columnKey = key + "_" + item.sequenceNbr;
          let result = this.parser(columnKey);
          if(key === 'totalNumber'){
            //totalNumber 参与计算取4位，赋值不四舍五入
            this.instanceMap[columnKey] = NumberUtil.numberScale(result,digital);
          }else{
            result = NumberUtil.numberScale(result,digital);
          }
          item[key] =  result;
        } else {
          item[key] = NumberUtil.numberScale(this.parser(key + "_" + item.sequenceNbr), 4);
        }

        LogUtil.renderLogger("ResourceCalculator :" + item.sequenceNbr + "---key :" + key + "---value :" + item[key]);
      }
    })

  }

  initCurrentDe(){
    this.currentDe = this.ctx.allDeMap.getNodeById(this.deRowId);
  }

  /**
   * sequenceNbr  配比材料的父级， 如果定额是人材机，则为定额的id
   * @param {*} rules 
   * @param {*} pbs 
   * @param {*} sequenceNbr 
   * @returns 
   */
  buildPbRules(rules, pbs, sequenceNbr){
    if(ObjectUtils.isEmpty(pbs)){
      return;
    }
    //定额价
    let dePriceRules = "0";
    let deTaxPriceRules = "0";
    //市场价
    let marketPriceRules = "0";
    let marketTaxPriceRules = "0";
    pbs.forEach(i => {
      this.rcjContext.push(i);
      rules[this.getMarketPriceKey(i.sequenceNbr)] = PBRules['marketPrice'].mathFormula;
      rules[this.getMarketTaxPriceKey(i.sequenceNbr)] = PBRules['marketTaxPrice'].mathFormula;
      rules[this.getRCJPriceKey(i.sequenceNbr)] = PBRules['baseJournalPrice'].mathFormula;
      rules[this.getRCJTaxPriceKey(i.sequenceNbr)] = PBRules['baseJournalTaxPrice'].mathFormula;
      rules[this.getResQtyKey(i.sequenceNbr)] = PBRules['resQty'].mathFormula;
      //子数量= 子消耗量*父工程量
      
      rules[this.getTotalNumberKey(i.sequenceNbr)] = this.getResQtyKey(i.sequenceNbr) + "*" + this.getTotalNumberKey(sequenceNbr);
      //合价=工程量*市场价
      rules[this.getTotalKey(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getMarketPriceKey(i.sequenceNbr);
      rules[this.getTotalTaxKey(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getMarketTaxPriceKey(i.sequenceNbr);
      rules[this.getBaseJournalTotalKey(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getRCJPriceKey(i.sequenceNbr);
      rules[this.getBaseJournalTotalTaxKey(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getRCJTaxPriceKey(i.sequenceNbr);
      //Σ 消耗量*定额价
      dePriceRules += "+" + this.getResQtyKey(i.sequenceNbr) + "*" + this.getRCJPriceKey(i.sequenceNbr);
      deTaxPriceRules += "+" + this.getResQtyKey(i.sequenceNbr) + "*" + this.getRCJTaxPriceKey(i.sequenceNbr);
      //Σ 子消耗量*子市场
      marketPriceRules += "+" + this.getMarketPriceKey(i.sequenceNbr) + "*" + this.getResQtyKey(i.sequenceNbr);
      marketTaxPriceRules += "+" + this.getMarketTaxPriceKey(i.sequenceNbr) + "*" + this.getResQtyKey(i.sequenceNbr);

      rules[this.getScCount(i.sequenceNbr)] = PBRules['scCount'].mathFormula;
      rules[this.getTransferFactor(i.sequenceNbr)] = PBRules['transferFactor'].mathFormula;
      //三材量
      rules[this.getScCount(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getTransferFactor(i.sequenceNbr);
    });
    rules[this.getRCJPriceKey(sequenceNbr)] = dePriceRules;
    rules[this.getRCJTaxPriceKey(sequenceNbr)] = deTaxPriceRules;
    rules[this.getMarketPriceKey(sequenceNbr)] = marketPriceRules;
    rules[this.getMarketTaxPriceKey(sequenceNbr)] = marketTaxPriceRules;
  }

  buildRules() {
    let rules = {};
    let rcjs = this.ctx.resourceMap.getValues(WildcardMap.generateKey(this.unitId, this.deRowId) + WildcardMap.WILDCARD);

    rcjs.forEach(item => {
      //不计算totalNumber      
      let totalNumberFlag = this.isCaculatorCost() && item.kind != ResourceKindConstants.TYPE_SB && item.kind != ResourceKindConstants.TYPE_ZC;
      
      //调整人材机正常计算数据
      if(this.currentDe.resourceTZ === CommonConstants.COMMON_YES &&(item.materialCode=== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
              || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
              || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ)){
        totalNumberFlag = false;
      }
      if (!ZSFeeConstants.ZS_RCJ_LIST.includes(item.materialCode)) {
        this.rcjContext.push(item);
        let sequenceNbr = item.sequenceNbr;

        rules[this.getRCJPriceKey(sequenceNbr)] = RJCRules['baseJournalPrice'].mathFormula;
        rules[this.getRCJTaxPriceKey(sequenceNbr)] = RJCRules['baseJournalTaxPrice'].mathFormula;
        rules[this.getMarketPriceKey(sequenceNbr)] = RJCRules['marketPrice'].mathFormula;
        rules[this.getMarketTaxPriceKey(sequenceNbr)] = RJCRules['marketTaxPrice'].mathFormula;
        rules[this.getResQtyKey(sequenceNbr)] = RJCRules['resQty'].mathFormula;


        //合计数量
        if (item.isDeResource === CommonConstants.COMMON_YES) {
          rules[this.getTotalNumberKey(this.deRowId)] = RJCRules['quantity'].mathFormula;
        }
        if(totalNumberFlag){
          rules[this.getTotalNumberKey(sequenceNbr)] = "totalNumber";
        }else{
          //是不是定额人材机都需要处理此项
          rules[this.getTotalNumberKey(sequenceNbr)] = this.getResQtyKey(sequenceNbr) + "*quantity";//定额：合计数量 = 工程量*消耗量
        }
        //合价
        rules[this.getBaseJournalTotalKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getRCJPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        rules[this.getBaseJournalTotalTaxKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getRCJTaxPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        rules[this.getTotalKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getMarketPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        rules[this.getTotalTaxKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getMarketTaxPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价

        //有配比
        if (item.markSum == CommonConstants.COMMON_YES && ObjectUtils.isNotEmpty(item.pbs)) {
          this.buildPbRules(rules, item.pbs, sequenceNbr);
        } else {
          rules[this.getRCJPriceKey(sequenceNbr)] = "baseJournalPrice";
          rules[this.getRCJTaxPriceKey(sequenceNbr)] = "baseJournalTaxPrice";
          rules[this.getMarketPriceKey(sequenceNbr)] = "marketPrice";
          rules[this.getMarketTaxPriceKey(sequenceNbr)] = "marketTaxPrice";
        }

        rules[this.getScCount(sequenceNbr)] = RJCRules['scCount'].mathFormula;
        rules[this.getTransferFactor(sequenceNbr)] = RJCRules['transferFactor'].mathFormula;
        //三材量
        rules[this.getScCount(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getTransferFactor(sequenceNbr);


        // 措施人材机其计算基数来源于【预算书】，当在【预算书】中非措施定额中插入措施人材机后，其数量恒定为0且不可编辑。若为措施定额，则按照措施定额标准计算规则进行计算。
        if (TSRCJConstants.TS_RCJ_LIST.includes(item.materialCode.replace(/#\d+/g, '')) && !totalNumberFlag) {
          // let csxmRowObject = this.ctx.allDeMap.csxmMap.getNodeById(item.deRowId);
          if (this.currentDe.isZj != 1) {
            //预算书非措施定额下数量恒为0
            rules[this.getTotalNumberKey(item.sequenceNbr)] = this.getResQtyKey(item.sequenceNbr)+"*0";
          }else{
            let rdTotalSum = this.ctx.treeProject.root.pricingMethod === 0 ? this.currentDe.rdTotalSum : this.currentDe.rTotalSum;
            let jdTotalSum = this.ctx.treeProject.root.pricingMethod === 0 ? this.currentDe.jdTotalSum : this.currentDe.jTotalSum;
            let rjdTotalSum = NumberUtil.add(rdTotalSum, jdTotalSum);
            rules[this.getTotalNumberKey(item.sequenceNbr)] = this.getResQtyKey(item.sequenceNbr) + "*" + this.currentDe.quantity + "*" + rjdTotalSum;
          }
        }
      } else {
        //降效系数人材机数量=await this.service.PreliminaryEstimate.gsDeService.calculateZSFee
         
        this.rcjContext.push(item);
        let sequenceNbr = item.sequenceNbr;

        rules[this.getRCJPriceKey(sequenceNbr)] = RJCRules['baseJournalPrice'].mathFormula;
        rules[this.getRCJTaxPriceKey(sequenceNbr)] = RJCRules['baseJournalTaxPrice'].mathFormula;
        rules[this.getMarketPriceKey(sequenceNbr)] = RJCRules['marketPrice'].mathFormula;
        rules[this.getMarketTaxPriceKey(sequenceNbr)] = RJCRules['marketTaxPrice'].mathFormula;
        rules[this.getResQtyKey(sequenceNbr)] = RJCRules['resQty'].mathFormula;

        //合计数量  delete 重复
        // if (item.isDeResource === CommonConstants.COMMON_YES) {
        //   rules[this.getTotalNumberKey(sequenceNbr)] = RJCRules['quantity'].mathFormula;
        // }   
        //是不是定额人材机都需要处理此项
        rules[this.getTotalNumberKey(sequenceNbr)] = "totalNumber";

        //合价
        rules[this.getBaseJournalTotalKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getRCJPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        rules[this.getBaseJournalTotalTaxKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getRCJTaxPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        rules[this.getTotalKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getMarketPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        rules[this.getTotalTaxKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getMarketTaxPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价

        //有配比
        if (item.markSum == CommonConstants.COMMON_YES && ObjectUtils.isNotEmpty(item.pbs)) {
          this.buildPbRules(rules, item.pbs, sequenceNbr);
        } else {
          rules[this.getRCJPriceKey(sequenceNbr)] = "baseJournalPrice";
          rules[this.getRCJTaxPriceKey(sequenceNbr)] = "baseJournalTaxPrice";
          rules[this.getMarketPriceKey(sequenceNbr)] = "marketPrice";
          rules[this.getMarketTaxPriceKey(sequenceNbr)] = "marketTaxPrice";
        }

        rules[this.getScCount(sequenceNbr)] = RJCRules['scCount'].mathFormula;
        rules[this.getTransferFactor(sequenceNbr)] = RJCRules['transferFactor'].mathFormula;
        //三材量
        rules[this.getScCount(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getTransferFactor(sequenceNbr);
      }

      // QTCLF1（其他材料费）的数量=归属定额中与其平级的材料∑（类型为材料的市场价*消耗量）*该条材料的消耗量/100 * 归属定额工程量
      if (item.isFyrcj == 0 && !ResourceKindConstants.isCsxmRcj(item.materialCode) && !totalNumberFlag) {
          let totalNumberFormula = 0;
          let sequenceNbr = item.sequenceNbr;
          let noneQtclfRcjs = rcjs.filter(noItem => noItem.isFyrcj!=0 && (item.kind == noItem.kind || this._checkCkind(item,noItem)));
          noneQtclfRcjs.forEach(noneQtclfRcj => {
            let sequenceNbr2 = noneQtclfRcj.sequenceNbr;
            rules[this.getMarketPriceKey(sequenceNbr2)] = "marketPrice";
            // QTCLF1（其他材料费）的数量=归属定额中与其平级的材料∑（类型为材料的市场价*消耗量）*该条材料的消耗量/100 * 归属定额工程量
            let RCJTaxPriceKey = this.ctx.treeProject.root.projectTaxCalculation.taxCalculationMethod === 0 ? this.getRCJTaxPriceKey(sequenceNbr2) : this.getRCJPriceKey(sequenceNbr2);
            totalNumberFormula = totalNumberFormula + '+(' + this.getResQtyKey(sequenceNbr2) + "*" + RCJTaxPriceKey + ')';
          })
          rules[this.getTotalNumberKey(sequenceNbr)] = "(" + totalNumberFormula + ")" + "*" + this.getResQtyKey(sequenceNbr) + "*" + this.currentDe.quantity + "/100";
      }
      //处理人材机数量锁定的情况，此时不需要处理人材机数量
      if(!totalNumberFlag && item.isNumLock && this.currentDe.quantity != 0){
        rules[this.getTotalNumberKey(item.sequenceNbr)] = "totalNumber";
      }

      //补充 普通定额下的费用人材机的数量计算为0
      if (this.currentDe.isZj != 1 && item.isFyrcj == 0) {
          //预算书非措施定额下数量恒为0
          rules[this.getTotalNumberKey(item.sequenceNbr)] = this.getResQtyKey(item.sequenceNbr)+"*0";
      }
    });

    this.loadRules(rules);
  }
      
  _checkCkind(item,noItem){
      return (item.kind == ResourceKindConstants.INT_TYPE_C || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10)
      &&(noItem.kind == ResourceKindConstants.INT_TYPE_C || noItem.kind == 6 || noItem.kind == 7 || noItem.kind == 8 || noItem.kind == 9 || noItem.kind == 10)
  
  }
  


  getValue({type,kind,column})
  {
    let currentDe = this.ctx.allDeMap.getNodeById(this.deRowId);
    let value;
    switch (type) {
      case `DE`:{
        if (typeof column == 'function') {
          value = column({ de: currentDe});
        } else {
          value = currentDe[column];
        }
        break;
      }

      default:{
        value = {type,kind,column};
        break;
      }
    }
    return value;
  }
  getRuntimeValue({type,kind,column},param)
  {
    let value= 0;
    let key = param.split(ResourceCalculator.SPLITOR)[1];
    let item  = this.rcjContext.find(item => item.sequenceNbr === key);
    switch (type) {
      case `DE`: {
        if (typeof column == "function") {
          value = column(item);
        } else {
          
          value = item[column];
        }
      }
      case `item`: {
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
        if(ObjectUtils.isEmpty(value)){
          value = 0;
        }
        //计算过程处理位数
        let precisionObj = DeUtils.getPrecisionByRcj(this.precision);
        let digital = precisionObj[column];
        if(ObjectUtils.isNotEmpty(digital)){
          value = NumberUtil.numberFormat(value,digital);
        }
      }
    }
    return value;
  }


  getResQtyKey(sequenceNbr)
  {
    return "resQty_" + sequenceNbr;
  }

  getTotalNumberKey(sequenceNbr){
    return "totalNumber_" + sequenceNbr;
  }
  getTotalKey = (sequenceNbr) => {
    return "total_" + sequenceNbr;
  }
  getTotalTaxKey = (sequenceNbr) => {
    return "totalTax_" + sequenceNbr;
  }
  getBaseJournalTotalKey = (sequenceNbr) => {
    return "baseJournalTotal_" + sequenceNbr;
  }
  getBaseJournalTotalTaxKey = (sequenceNbr) => {
    return "baseJournalTotalTax_" + sequenceNbr;
  }
  getRCJPriceKey = (sequenceNbr) => {
    return "baseJournalPrice_" + sequenceNbr;
  }
  getRCJTaxPriceKey = (sequenceNbr) => {
    return "baseJournalTaxPrice_" + sequenceNbr;
  }
  getMarketPriceKey = (sequenceNbr) => {
    return "marketPrice_" + sequenceNbr;
  }
  getMarketTaxPriceKey = (sequenceNbr) => {
    return "marketTaxPrice_" + sequenceNbr;
  }

  getScCount = (sequenceNbr) => {
    return "scCount_" + sequenceNbr;
  }

  getTransferFactor = (sequenceNbr) => {
    return "transferFactor_" + sequenceNbr;
  }

  /**
   * 是否计算费用人材机的totalNumber和total
   * @returns 
   */
  isCaculatorCost(){
    if ([CostDeMatchConstants.AZ_DE,
        CostDeMatchConstants.FXTJ_CG,
        CostDeMatchConstants.FXTJ_CZYS,
        CostDeMatchConstants.FXTJ_ZXXJX,
        CostDeMatchConstants.GJMQ_DE_CG,
        CostDeMatchConstants.GJMQ_DE_CZYS,
        CostDeMatchConstants.GJMQ_DE_ZXXJX,
        CostDeMatchConstants.FGJZ_DE_ZXXJX,
        CostDeMatchConstants.ZJCS_DE,
        CostDeMatchConstants.FXTJ_GCSDF].includes(this.currentDe.isCostDe)){
      return true;
    }
    return false;
  }
}
module.exports = {ResourceCalculator};