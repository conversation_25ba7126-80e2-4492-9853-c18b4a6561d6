const { Controller } = require('../../../core');
const { ResponseData } = require('../../../electron/utils/ResponseData');

/**
 * 结算其他项目Controller
 */
class JieSuanOtherProjectController extends Controller {


  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }


  /**
   * 获取总承包服务费的结算方式
   */
  async getServiceCostSettlementType(arg) {
    return await this.service.jieSuanProject.jieSuanOtherProjectService.getServiceCostSettlementType(arg);
  }

  /**
   * 修改总承包服务费的结算方式或者结算金额
   */
  async updateServiceCost(arg) {
    return await this.service.jieSuanProject.jieSuanOtherProjectService.updateServiceCost(arg);
  }

  /**
   * 操作暂列金数据
   */
  async operateOtherProjectProvisional(arg) {
    const result = await this.service.otherProjectProvisionalService.otherProjectProvisional(arg);
    await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(arg);
    return ResponseData.success(result);
  }

  /**
   * 操作专业工程暂估价数据
   */
  async operateOtherProjectZygcZgj(arg) {
    const result = await this.service.otherProjectZgjService.otherProjectZygcZgj(arg);
    await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(arg);
    return ResponseData.success(result);
  }

  /**
   * 操作 总承包服务费 数据
   */
  async operateOtherProjectServiceCost(arg) {
    const res = await this.service.jieSuanProject.jieSuanOtherProjectService.operateOtherProjectServiceCost(arg);
    await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(arg);
    return ResponseData.success(res);
  }

  /**
   * 操作计日工数据
   */
  async operateOtherProjectDayWork(arg) {
    const res = await this.service.otherProjectDayWorkService.otherProjectDayWork(arg);
    await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(arg);
    return ResponseData.success(res);
  }


}

JieSuanOtherProjectController.toString = () => '[class JieSuanOtherProjectController]';
module.exports = JieSuanOtherProjectController;

