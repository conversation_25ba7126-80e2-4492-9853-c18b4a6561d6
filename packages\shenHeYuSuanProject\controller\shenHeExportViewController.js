const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const fs = require('fs');
const {array} = require("is-type-of");

/**
 * 报表查询controller
 */
class ShenHeExportViewController extends Controller {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 点击报表名称  展示对应的报表页面
     * @param arg
     * @returns {Promise<*>}
     */
    async showSheetStyle(args) {

        const {itemLevel, sheetName, constructObj} = args
        //首先判断计税方式
        const result = await this.service.shenHeYuSuanProject.shenHeExportViewService.showSheetStyle(itemLevel, sheetName, constructObj);
        return ResponseData.success(result);
    }

    /**
     * 点击左侧工程项目层级树  展示对应的报表结构目录
     * @param arg
     * @returns {Promise<*>}
     */
    async showExportHeadLine(args) {
        //首先判断计税方式
        const {itemLevel, constructObj} = args
        //首先判断计税方式
        const result = await this.service.shenHeYuSuanProject.shenHeExportViewService.showExportHeadLine(itemLevel, constructObj);
        return ResponseData.success(result);
    }

    //导出excel弹框树结构筛选
    async queryLanMuData(args) {
        let {constructId} = args;
        let queryLanMuData = await this.service.shenHeYuSuanProject.shenHeExportViewService.queryLanMuData(constructId);
        return ResponseData.success(queryLanMuData);
    }

    //导出勾选的excel zip 包
    async exportExcelZip(args) {
        let {params} = args;
        let result = await this.service.shenHeYuSuanProject.shenHeExportViewService.exportExcel(params);
        // let result2 = await this.service.shenHeYuSuanProject.pdfService.exportPdf(lanMuName,params,"D:\\csClient11_14\\pricing-cs\\build\\extraResources\\excelTemplate\\export\\工程.pdf");
        return ResponseData.success(result);
    }


    //导出勾选的pdf
    async exportPdfFile(args) {
        let {params} = args;
        let result = await this.service.shenHeYuSuanProject.shenHeExportPdfService.excelToPdf(params);
        return ResponseData.success(result);
    }

    /**
     * 返回一个sheet表的格式
     * @param arg
     * @returns {Promise<*>}
     */
    async showSheetStyleSample() {
        const result = await this.service.shenHeYuSuanProject.shenHeExportViewService.showSheetStyleSample();
        return ResponseData.success(result);
    }

}

ShenHeExportViewController.toString = () => '[class ShenHeExportViewController]';
module.exports = ShenHeExportViewController;
