<!--
 * @Descripttion: 智能询价弹窗
 * @Author: sunchen
 * @Date: 2023-12-25 10:34:45
 * @LastEditors: liuxia
 * @LastEditTime: 2024-11-25 17:30:03
-->
<template>
  <common-modal
    className="dialog-comm resizeClass inquiryPopup-dialog"
    width="1000"
    @close="cancel()"
    :mask="false"
    :lockView="false"
    show-zoom
    resize
    v-model:modelValue="dialogVisible"
    title="价格选择"
  >
    <!-- :lockView="false" -->

    <div class="inquiry-dialog-content" v-if="initData">
      <div class="inquiry-dialog-content-tabs">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="informationPriceList" tab="信息价"> </a-tab-pane>
          <a-tab-pane key="marketPriceList" tab="市场价"></a-tab-pane>
          <a-tab-pane key="recommendPriceList" tab="推荐价"></a-tab-pane>
        </a-tabs>
        <div class="inquiry-dialog-content-table">
          <vxe-table
            v-if="initData[activeKey] && initData[activeKey].length"
            align="center"
            show-footer
            height="100%"
            :row-class-name="rowClassName"
            @cell-dblclick="handleCellDblclick"
            :scroll-x="{ enabled: true, gt: 20 }"
            :scroll-y="{ enabled: true, gt: 100 }"
            :data="initData[activeKey]"
          >
            <vxe-column type="seq" width="70" title="序号"></vxe-column>
            <vxe-column field="materialName" title="名称"></vxe-column>
            <vxe-column
              field="specification"
              width="100"
              title="规格型号"
            ></vxe-column>
            <vxe-column field="unit" title="单位"></vxe-column>
            <vxe-column
              field="marketPrice"
              :title="
                activeKey === 'informationPriceList'
                  ? '含税市场价'
                  : activeKey === 'marketPriceList'
                  ? '工程价'
                  : '推荐价'
              "
            ></vxe-column>
            <vxe-column
              field="notIncludingTaxMarketPrice"
              title="不含税市场价"
              v-if="activeKey === 'informationPriceList'"
            ></vxe-column>
            <vxe-column field="area" title="地区/产地"> </vxe-column>
            <vxe-column field="priceDate" title="期数/报价时间"></vxe-column>
            <vxe-column field="taxRemoval" title="税率"></vxe-column>
          </vxe-table>

          <div class="nodata" v-else>
            <img
              src="https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/jijiasoft/zanwushuju.png"
              class="bg-img"
              alt=""
            />
            <span>未查询到对应数据价格</span>
          </div>
        </div>
      </div>
      <div class="inquiry-dialog-footer">
        <div class="inquiry-footer">
          <icon-font class="icon" type="icon-tishi" style="margin-right: 5px" />
          您可进入
          <a
            href="https://www.yunsuanfang.com"
            class="small-tips"
            target="_blank"
            >https://www.yunsuanfang.com</a
          >
          查看最新的信息价、市场价数据或您可联系客服
          <span class="small-tips">400-005-8008</span> 进行人工查价
        </div>

        <div class="inquiry-footer">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />
          <span>双击数据行即可跳转</span>
        </div>
      </div>
    </div>
  </common-modal>
  <unitConvert
    v-if="unitConvertVisible"
    @closeDialog="closeUnitConvert"
    :priceInfo="postData"
    :isDiffDeType="true"
  />
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  shallowRef,
  toRaw,
  defineExpose,
  defineAsyncComponent,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail';

const unitConvert = defineAsyncComponent(() =>
  import('@/components/unitConvert.vue')
);
const store = projectDetailStore();
const props = defineProps({
  info: {
    type: Object,
    default: null,
  },
});
const emits = defineEmits(['success']);
const route = useRoute();
const dialogVisible = ref(false);
const initData = shallowRef(null);
let activeKey = ref('informationPriceList');
let postData = ref();
let unitConvertVisible = ref(false);
let loading = ref(false);

const getTreeList = () => {
  initData.value = null;
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: store.currentTreeInfo?.parentId,
    unitId: store.currentTreeInfo.id,
    standardId: props.info.standardId,
    materialName: props.info.materialName,
  };

  csProject.smartLoadPrice(postData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('getTreeList', res.result);
      initData.value = res.result[0];
    }
  });
};

const closeUnitConvert = val => {
  unitConvertVisible.value = false;
  if (val) {
    postData.value = {
      ...val,
      loadPrice: {
        ...val.loadPrice,
        marketPrice: val.marketPriceAfter,
      },
    };
    beforeClick();
  }
};

const handleCellDblclick = ({ row }) => {
  console.log('水电费1', props.info);

  let {
    sequenceNbr,
    marketPrice,
    sourcePrice,
    unit,
    notIncludingTaxMarketPrice,
  } = row;
  postData.value = {
    constructId: route.query?.constructSequenceNbr,
    singleId: store.currentTreeInfo?.parentId,
    unitId: store.currentTreeInfo.id,
    sequenceNbr: props.info.sequenceNbr,
    loadPrice: { ...row },
    beforeUnit: unit,
    nowUnit: props.info.unit,
    marketPrice:
      activeKey.value === 'informationPriceList' &&
      Number(store.deStandardReleaseYear) === 22 &&
      Number(store?.taxMade) === 1
        ? row.notIncludingTaxMarketPrice
        : row.marketPrice,
    marketPriceAfter: '',
    sourcePrice,
    loadingPriceFlag: 1,
    thisTimeLoadingPriceFlag: 1,
  };

  if (row.unit !== props.info.unit) {
    unitConvertVisible.value = true;
    return;
  }
  beforeClick();
};

const rowClassName = ({ row }) => {
  if (row.kind === '01' || row.kind === '02') {
    return 'row-sub';
  }
  return null;
};

const beforeClick = () => {
  loading.value = true;
  csProject
    .smartLoadPriceUse(toRaw(postData.value))
    .then(res => {
      console.log(
        '🚀 ~ file: index.vue:183 ~ csProject.smartLoadPriceUse ~ res:',
        res
      );
      if (res.result) {
        cancel(true);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const cancel = (isOk = false) => {
  dialogVisible.value = false;
  emits('closeDialog', isOk);
};

const open = () => {
  getTreeList();
  dialogVisible.value = true;
};

watch(
  () => props.info,
  () => {
    getTreeList();
  }
);

open();
</script>

<style lang="scss">
.inquiryPopup-dialog {
  .row-sub {
    background-color: red;
  }
  .vxe-modal--content {
    padding-bottom: 15px !important;
    .ant-tabs-tab {
      padding: 0 20px 5px 20px;
    }
  }
  .inquiry-dialog-content {
    display: flex;
    flex-direction: column;
    min-height: 400px;
    height: 100%;
    .inquiry-dialog-content-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
  .inquiry-dialog-content-table {
    flex: 1;
    position: relative;
    .nodata {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      .bg-img {
        display: block;
        width: 180px;
        margin: 0 auto;
      }
      span {
        font-size: 14px;
        color: #606060;
        text-align: center;
      }
    }
  }
  .inquiry-dialog-footer {
    margin-top: 18px;
  }
  .inquiry-footer {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #2a2a2a;
    &:first-child {
      margin-top: 4px;
    }
    .small-tips {
      color: rgba(40, 124, 250, 1);
    }
  }
}
</style>
