<!--
 * @Descripttion: 
 * @Author: kong<PERSON>qiang
 * @Date: 2024-06-11 09:30:45
 * @LastEditors: sunchen
 * @LastEditTime: 2024-12-02 14:58:20
-->
<template>
  <div class="subItem-project-gs">
    <split
      horizontal
      ratio="4/3"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
      @onDragHeight="dragHeight"
    >
      <template #one>
        <div class="table-content">
          <s-table
            size="small"
            ref="stableRef"
            class="s-table"
            :columns="showColumns"
            bordered
            rowKey="sequenceNbr"
            :loading="loading"
            :delay="200"
            :rangeSelection="true"
            :custom-cell="customCell"
            :row-selection="rowSelection"
            :custom-row="customRow"
            :custom-header-cell="customHeaderCell"
            :rowClassName="(row, index) => rowClassName(row, index, tableData)"
            :animateRows="false"
            :scroll="{ y: stableHeight }"
            :pagination="false"
            :data-source="tableData"
            @openEditor="openEditor"
            @closeEditor="closeEditor"
            @mouseup="cellMouseup"
            @cellKeydown="cellKeydown"
            :formatRangeCellText="formatRangeCellText"
          >
            <!-- 自定义头部 -->
            <template #headerCell="{ title, column }">
              <span style="font-weight: bold">
                <i class="vxe-icon-edit" v-show="column.edit"></i>&nbsp;{{
                  title
                }}
              </span>
            </template>
            <!--自定义内容 -->
            <template
              #bodyCell="{
                text,
                record: row,
                index,
                column,
                key,
                openEditor,
                closeEditor,
              }"
            >
              <div v-if="column.dataIndex === 'dispNo'">
                <span>{{ row.dispNo || '‎' }}</span>
              </div>
              <template v-if="column.dataIndex === 'deCode'">
                <i
                  @click.stop="changeStatus(row)"
                  v-if="row.displaySign === 1 && tableData.length > 1"
                  class="vxe-icon-minus"
                ></i>
                <i
                  @click.stop="changeStatus(row)"
                  v-if="row.displaySign === 2"
                  class="vxe-icon-add"
                ></i>
                <span class="code"
                  >{{ row.deCode }}&nbsp;{{
                    row.redArray?.length > 0 ? row.redArray.join(',') : ''
                  }}
                </span>
                <span class="code-black" v-if="row.blackArray?.length > 0">{{
                  row.blackArray.join(',')
                }}</span>
              </template>
              <template v-if="column.dataIndex == 'deName'">
                <div
                  v-if="row.kind !== '05'"
                  class="note-tips-yss"
                  v-show="row.annotations"
                  @mouseover="cellMouseEnterEvent(row)"
                  @mouseout="cellMouseLeaveEvent(row)"
                ></div>
                <icon-font
                  type="icon-bianji"
                  class="more-icon"
                  v-show="
                    row.isTempRemove !== 1 &&
                    isSelectedCell({
                      $columnIndex,
                      column,
                      row,
                    })
                  "
                  @click.stop="openEditDialog('deName')"
                ></icon-font>
                <a-popover
                  v-if="row.annotationsVisible && row.kind !== '05'"
                  placement="rightTop"
                  v-model:visible="row.annotationsVisible"
                  trigger="click"
                  @visibleChange="val => visibleChange(val, row)"
                  :getPopupContainer="triggerNode => deNameRef(triggerNode)"
                >
                  <template #content>
                    <div style="width: 250px; height: 140px">
                      <Annotations
                        @close="v => closeAnnotations(v, row)"
                        @onfocusNode="onFocusNode(row)"
                        :note="row.annotations"
                        :isDisabled="row?.noteEditVisible"
                        :ref="el => getAnnotationsRef(el, row)"
                        :type="1"
                      ></Annotations>
                    </div>
                  </template>
                  <div class="nameEdit" @mouseout="cellMouseLeaveEvent(row)">
                    <pre class="pre-name" v-html="row.deName"></pre>
                    <span v-if="row.initDeRcjNameList?.length > 0">{{
                      row.initDeRcjNameList
                        .filter(a => a.replaceMaterialName)
                        .map(a => a.replaceMaterialName)
                        .join('')
                    }}</span>
                  </div>
                </a-popover>
                <div
                  class="nameEdit"
                  v-else
                  @mouseover="cellMouseEnterEvent(row)"
                >
                  <pre class="pre-name" v-html="row.deName"></pre>
                  <span v-if="row.initDeRcjNameList?.length > 0">{{
                    row.initDeRcjNameList
                      .filter(a => a.replaceMaterialName)
                      .map(a => a.replaceMaterialName)
                      .join('')
                  }}</span>
                </div>
              </template>
              <!-- 单位 -->
              <template v-if="column.dataIndex == 'unit'">
                <div>{{ row.unit }}</div>
              </template>
              <template v-if="column.dataIndex === 'type'">
                <span
                  v-if="
                    (!row.borrowFlag && !row.changeFlag) || row.type === '费'
                  "
                  >{{
                    row.type === '05' || row.type === '06' || row.type === '09'
                      ? deMapFun.rcjMap[row.deResourceKind]
                      : row.type === '03' || row.type === '04'
                        ? row.displayType
                        : deMapFun.deMap[row.type]
                  }}</span
                >
                <span v-if="row.type === '-1'">定</span>
                <span class="code-flag" v-if="row.type !== '费'"
                  >{{ row.changeFlag ? row.changeFlag : '' }}
                </span>
                <span
                  class="code-flag"
                  v-if="row.type !== '费' && !row.changeFlag"
                  >{{ row.borrowFlag ? row.borrowFlag : '' }}
                </span>
              </template>
              <template v-if="column.dataIndex == 'originalQuantity'">
                <span>{{ row.quantity }}</span>
              </template>
              <template
                v-if="column.dataIndex == 'costMajorName' && row.kind === '05'"
              >
                <span></span>
              </template>
              <template
                v-if="
                  row.kind === '-1' &&
                  [
                    'originalQuantity',
                    'price',
                    'totalNumber',
                    'costMajorName',
                  ].includes(column.dataIndex)
                "
              >
                <div></div>
              </template>
            </template>
            <!--自定义编辑 -->
            <template
              #cellEditor="{
                column,
                modelValue,
                save,
                closeEditor,
                record: row,
                editorRef,
                getPopupContainer,
              }"
            >
              <template v-if="column.dataIndex == 'type'">
                <a-select
                  :value="row.deResourceKind"
                  :ref="el => setInputRef(el, column.field)"
                  :options="typeList"
                  :bordered="false"
                  size="small"
                  :field-names="{ label: 'name', value: 'value' }"
                  @blur="closeEditor()"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.deResourceKind
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>
              <template v-if="column.dataIndex === 'deCode'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  :readOnly="
                    row.isTempRemove == 1 ||
                    ['00', '01', '02'].includes(row.kind)
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      timeCLose(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row.bdCode
                      );
                    }
                  "
                  @click="cellDBLClickEvent({ row, column })"
                />
                <a-popover
                  overlayClassName="association-popover"
                  placement="bottom"
                  v-model:visible="showRepeat"
                  trigger="click"
                  :autoAdjustOverflow="false"
                >
                  <template #content>
                    <vxe-table
                      border
                      auto-resize
                      v-if="showRepeat"
                      height="auto"
                      :treeConfig="{
                        rowField: 'sequenceNbr',
                        childrenField: 'children',
                        expandAll: true,
                      }"
                      :row-config="{ isHover: true }"
                      :data="codeTable"
                      @cell-click="cellClickEvent"
                    >
                      <vxe-column
                        tree-node
                        title="编码"
                        field="deCode"
                        width="120"
                      ></vxe-column>
                      <vxe-column title="类别" field="type" width="60">
                        <template #default="{ row }">
                          <span
                            v-if="
                              (!row.borrowFlag && !row.changeFlag) ||
                              row.type === '费'
                            "
                            >{{
                              row.type === '05' ||
                              row.type === '06' ||
                              row.type === '09'
                                ? deMapFun.rcjMap[row.deResourceKind]
                                : row.type === '03' || row.type === '04'
                                  ? row.displayType
                                  : deMapFun.deMap[row.type]
                            }}</span
                          >
                          <span v-if="row.type === '-1'">定</span>
                          <span class="code-flag" v-if="row.type !== '费'"
                            >{{ row.changeFlag ? row.changeFlag : '' }}
                          </span>
                          <span
                            class="code-flag"
                            v-if="row.type !== '费' && !row.changeFlag"
                            >{{ row.borrowFlag ? row.borrowFlag : '' }}
                          </span>
                        </template>
                      </vxe-column>
                      <vxe-column
                        title="名称"
                        field="deName"
                        width="100"
                      ></vxe-column>
                      <vxe-column
                        title="单位"
                        field="unit"
                        width="60"
                      ></vxe-column>
                      <vxe-column
                        title="单价"
                        field="price"
                        width="100"
                      ></vxe-column>
                    </vxe-table>
                  </template>
                </a-popover>
              </template>
              <template v-if="column.dataIndex === 'deName'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  :readOnly="row.isTempRemove == 1"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.deName
                      );
                      closeEditor();
                    }
                  "
                  @click="cellDBLClickEvent({ row, column })"
                />
              </template>
              <!-- 单位 -->
              <template v-if="column.dataIndex == 'unit'">
                <a-select
                  size="small"
                  :bordered="false"
                  show-search
                  :notFoundContent="null"
                  :ref="el => setInputRef(el, column.field)"
                  :showArrow="false"
                  :value="modelValue.value"
                  :filter-option="
                    input => {
                      modelValue.value = input;
                    }
                  "
                  :get-popup-container="getPopupContainer"
                  :options="
                    projectStore.unitListString.split(',').map(a => {
                      return { value: a, label: a };
                    })
                  "
                  open
                  @blur="
                    (editClosedEvent(
                      { row, column },
                      modelValue.value,
                      row.unit
                    ),
                    closeEditor())
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.unit
                      );
                      closeEditor();
                    }
                  "
                ></a-select>
              </template>
              <!-- 消耗量 -->
              <template v-if="column.dataIndex == 'resQty'">
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :get-popup-container="getPopupContainer"
                  :maxlength="10"
                  @update:value="
                    v => {
                      modelValue.value = v === '' ? '' : v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.resQty
                      );
                      closeEditor();
                    }
                  "
                />
              </template>
              <!-- 工程量 -->
              <template v-if="column.dataIndex == 'originalQuantity'">
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :controls="false"
                  :get-popup-container="getPopupContainer"
                  :maxlength="100"
                  @update:value="
                    v => {
                      modelValue.value = v === '' ? '' : v;
                    }
                  "
                  @blur="
                    () => {
                      timeCLoselQuantity(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row.originalQuantity
                      );
                    }
                  "
                />
                <div class="nameEdit">
                  <icon-font
                    @click.stop="comModel = true"
                    type="icon-bianji"
                    class="more-icon"
                    v-show="!['05', '07'].includes(row.kind)"
                  ></icon-font>
                </div>
              </template>
              <!-- 单价 -->
              <template v-if="column.dataIndex == 'price'">
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :get-popup-container="getPopupContainer"
                  :maxlength="10"
                  @update:value="
                    v => {
                      modelValue.value = v === '' ? '' : v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.price
                      );
                      closeEditor();
                    }
                  "
                />
              </template>
              <!-- 人工费单价 -->
              <template
                v-if="
                  ['RSum', 'CSum', 'JSum', 'ZSum', 'SSum'].includes(
                    column.dataIndex
                  )
                "
              >
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :get-popup-container="getPopupContainer"
                  :maxlength="10"
                  @update:value="
                    v => {
                      modelValue.value = v === '' ? '' : v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                />
              </template>
              <template v-if="column.dataIndex == 'costMajorName'">
                <a-select
                  :value="modelValue.value"
                  :options="feeFileList"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  :field-names="{ label: 'qfName', value: 'qfName' }"
                  :get-popup-container="getPopupContainer"
                  @blur="closeEditor()"
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.costMajorName
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>
              <!-- 规格型号 -->
              <template v-if="column.dataIndex == 'specification'">
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :controls="false"
                  :get-popup-container="getPopupContainer"
                  :maxlength="100"
                  @update:value="
                    v => {
                      modelValue.value = v === '' ? '' : v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.specification
                      );
                      closeEditor();
                    }
                  "
                />
              </template>
            </template>
            <!-- 右键菜单 -->
            <template #contextmenuPopup="args">
              <sub-menu
                :args="args"
                :copyData="copyData"
                :addRowVisible="addRowVisible"
                :pasteRowVisible="pasteRowVisible"
                @contextMenuClickEvent="
                  ({ menu, row }) => contextMenuClickEvent({ menu, row }, args)
                "
                v-model:currentInfo="currentInfo"
              ></sub-menu>
            </template>
          </s-table>
        </div>
      </template>
      <template #two>
        <div class="quota-content">
          <quota-info
            ref="quotaInfoRef"
            :currentInfo="currentInfo"
            :isAttrContent="isAttrContent"
            :type="1"
            :isUpdateFile="isUpdateFile"
            :isUpdateQuantities="isUpdateQuantities"
            @refreshCurrentInfo="refreshCurrentInfo"
            @tabClickBefore="showInfo"
            @onDbClickFile="onDbClickFile"
            @unpriced="materialVisible = true"
            :isComplete="isComplete"
            :isUpdate="isUpdate"
          ></quota-info>
        </div>
      </template>
    </split>
    <inventory-and-quota-index
      v-model:indexVisible="indexVisible"
      @currentQdDeInfo="
        (row, rowType, isAdd) =>
          getMainMaterialAndEquipment(row, rowType, isAdd, 'add')
      "
      @currentInfoReplace="
        (row, rowType, isAdd) =>
          getMainMaterialAndEquipment(row, rowType, isAdd, 'replace')
      "
      :dataType="dataType"
      :indexLoading="indexLoading"
      :originInfo="currentInfo"
    ></inventory-and-quota-index>
    <common-modal
      v-model:modelValue="deleteVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px"
    >
      <template v-if="['01', '02'].includes(currentInfo.type)">
        <div class="content">是否删除当前分部下所有数据</div>
        <div class="footer-btn-list">
          <a-button @click="deleteVisible = false" :disabled="deleteLoading"
            >取消</a-button
          >
          <a-button @click="removeDeRow(false)" :disabled="deleteLoading"
            >否</a-button
          >
          <a-button
            type="primary"
            @click="removeDeRow(true)"
            :disabled="deleteLoading"
            >是</a-button
          >
        </div>
      </template>
      <template v-else>
        <div class="content">
          执行本操作将会删除本项及其下所有数据和关联关系
        </div>
        <div class="footer-btn-list">
          <a-button @click="deleteVisible = false" :disabled="deleteLoading"
            >取消</a-button
          >
          <a-button
            type="primary"
            @click="delFbData(true)"
            :disabled="deleteLoading"
            >确定</a-button
          >
        </div>
      </template>
    </common-modal>

    <common-modal
      v-model:modelValue="deleteAllVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px"
    >
      <div class="content">该操作会移除本单位工程下所有数据，是否执行？</div>
      <div class="footer-btn-list">
        <a-button @click="deleteAllVisible = false" :disabled="deleteAllLoading"
          >取消</a-button
        >
        <a-button
          type="primary"
          @click="subtileAllDeleteClick()"
          :disabled="deleteAllLoading"
          >确定</a-button
        >
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="isShowModel"
      className="dialog-comm"
      :title="showModelTitle"
      width="700"
      height="350"
    >
      <a-textarea
        v-model:value="editContent"
        placeholder="请输入编辑内容"
        :rows="8"
        class="edit-content"
      />
      <div class="footer-btn-list">
        <a-button @click="editCancel">取消</a-button>
        <a-button type="primary" @click="saveContent()">确定</a-button>
      </div>
    </common-modal>
    <info-modal
      v-model:infoVisible="infoVisible"
      :infoText="infoText"
      :isSureModal="isSureModal"
      :iconType="iconType"
      @updateCurrentInfo="updateCurrentInfo"
    ></info-modal>
    <bcDe
      v-model:visible="deVisible"
      :code="deCode"
      :type="1"
      :currentInfo="currentInfo"
      @deSaveData="deSaveData"
      @bcCancel="bcCancel"
    ></bcDe>
    <bcRcj
      :code="deCode"
      v-model:visible="rcjVisible"
      :showResqty="false"
      supplement
      @rcjSaveData="rcjSaveData"
      @bcCancel="bcCancel"
    ></bcRcj>
    <price-model
      v-model:visible="priceVisible"
      :currentInfo="currentInfo"
      @updateData="queryBranchDataById"
    ></price-model>
    <common-modal
      v-model:modelValue="isPriceModel"
      className="dialog-comm"
      :title="showPriceTitle"
      :width="
        showModelType === 'csfy'
          ? '1000'
          : showModelType === 'azfy' || showModelType === 'zmAzfy'
            ? 1200
            : 800
      "
      height="auto"
      @close="closePriceModel"
    >
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          @updateData="updateData"
          :currentInfo="currentInfo"
          @close="closePriceModel"
        ></component>
      </keep-alive>
    </common-modal>
    <common-modal
      v-model:modelValue="organizeModel"
      className="dialog-comm"
      :title="showModelType === 'fb' ? '分部整理' : '子目排序'"
      :width="600"
      height="auto"
      @close="closePriceModel"
    >
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          @updateData="updateData(1)"
          :currentInfo="currentInfo"
          @close="closePriceModel"
        ></component>
      </keep-alive>
    </common-modal>
    <set-standard-type
      v-model:standardVisible="standardVisible"
      :type="1"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      @refreshCurrentInfo="refreshCurrentInfo"
    ></set-standard-type>
    <!-- 主材 -->
    <set-main-material
      v-model:materialVisible="materialVisible"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      :materialType="materialType"
      :rowType="rowType"
      :materialRow="materialRow"
      :mainMaterialTableData="mainMaterialTableData"
      :deOrRcj="deOrRcj"
      @setUpdate="setUpdate"
    >
    </set-main-material>

    <!-- 导入项目 -->
    <common-modal
      className="dialog-comm"
      title="导入工程文件"
      width="auto"
      v-model:modelValue="importProjectVisible"
      @close="importYgsDeCancel"
    >
      <import-project
        @cancel="
          ((importProjectVisible = false),
          queryBranchDataById('load'),
          emits('updateMenuList'))
        "
        @close="importProjectVisible = false"
        ref="importProjectRef"
      />
    </common-modal>
    <!-- 导入excel -->
    <common-modal
      className="dialog-comm"
      title="导入excel"
      width="auto"
      v-model:modelValue="importExcelVisible"
    >
      <import-excel
        @cancel="importExcelVisible = false"
        @close="importExcelVisible = false"
        ref="importExcelRef"
      />
    </common-modal>
    <gs-batch-delete
      v-model:batchDeleteVisible="batchDeleteVisible"
      :batchDataType="batchDataType"
      @updateData="queryBranchDataById"
    ></gs-batch-delete>
    <areaModal
      v-if="areaStatus"
      :type="areaVisibleType"
      @closeDialog="closeAreaModal"
    ></areaModal>
    <!-- 局部汇总 -->
    <gsPartialSummary
      ref="gsPartialSummaryRef"
      @closeDialog="gsPartialSummaryStatus = false"
      v-if="gsPartialSummaryStatus"
    ></gsPartialSummary>
  </div>

  <!-- 查找 -->
  <lookFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
    @updateData="queryBranchDataById"
  />
  <!-- 过滤 -->
  <FiltErate
    ref="filtErateRef"
    v-model:filterateVisible="filterateVisible"
    @updateData="queryBranchDataById"
    @queryBack="filtQuery"
    @changeCurrentInfo="changeCurrentInfo"
  />
  <dataReplacement v-model:visible="dataReplacementvisible" />
  <!-- 工程规模编辑 -->
  <common-modal
    className="dialog-comm noMask"
    title="工程量编辑"
    width="800"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModelCLose"
    :mask="false"
    style="position: releative"
  >
    <IntroductionQuantity
      :textValue="currentInfo.originalQuantity"
      ref="comArea"
    ></IntroductionQuantity>
    <span class="btns">
      <a-button @click="comModelCLose()">取消</a-button>
      <a-button type="primary" @click="comModelsureData()">确定</a-button>
    </span>
  </common-modal>
</template>

<script setup>
import {
  onMounted,
  onBeforeUnmount,
  reactive,
  ref,
  watch,
  watchEffect,
  nextTick,
  computed,
  markRaw,
  defineAsyncComponent,
  onActivated,
  onDeactivated,
  provide,
  inject,
  toRaw,
  getCurrentInstance,
} from 'vue';
import {
  quantityExpressionHandler,
  removeSpecialCharsFromPrice,
  everyNumericHandler,
} from '@/utils/index';
import { checkisOnline } from '@/utils/publicInterface';
import QuotaInfo from '../quotaInfo/index.vue';
import InventoryAndQuotaIndex from '../inventoryAndQuotaIndex/index.vue';
import api from '@gaiSuan/api/projectDetail.js';
import csProject from '@gaiSuan/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import ComponentMatching from '../measuresItem/componentMatching.vue';
import MatchPic from '../measuresItem/MatchPic.vue';
import { message } from 'ant-design-vue';
import { ItemBillMenuOperator } from './ItemBillMenuOperator';
import xeUtils from 'xe-utils';
import frameSelect from '@/components/gsFrameSelect/index.vue';
import gsBatchDelete from '@/components/batchDelete/gsIndex.vue';
import split from '@/components/split/index.vue';
import gsPartialSummary from '@/components/gsPartialSummary/index.vue';
import { insetBus } from '@gaiSuan/hooks/insetBus';
import operateList from '../operate';
import getTableColumns from './tableColumns';
import {
  customCell,
  rowClassName,
  customHeaderCell,
} from './classAndStyleMethod';
import { useCheckBefore } from '@gaiSuan/hooks/useCheckBefore';
import infoMode from '@/plugins/infoMode';
import SetStandardType from '../quotaInfo/setStandardType.vue';
import SetMainMaterial from '../quotaInfo/setMainMaterial.vue';
import ProjectAttrAssociation from '@/components/ProjectAttrAssociation/ProjectAttrAssociation.vue';
import { useCellClick } from '@gaiSuan/hooks/useCellClick';
import { useReversePosition } from '@gaiSuan/hooks/useReversePosition.js';
import { useSubItem } from '@gaiSuan/hooks/useGsSubItem.js';
import { useFormatTableColumns } from '@gaiSuan/hooks/useGsFormatTableColumns.js';
import { useRoute } from 'vue-router';
import importExcel from './components/importExcel.vue';
import importProject from './components/importProject.vue';
import priceModel from './components/priceModel.vue';

//
import deMapFun from '../deMap';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';
import subMenu from './sub-menu.vue';
import { stableHook } from './stableHook.js';
import dataReplacement from './components/dataReplacement.vue';
import IntroductionQuantity from './components/IntroductionQuantity.vue';

const route = useRoute();
const { dataSearchPosition } = useReversePosition();
const { isSelectedCell, resetCellData } = useCellClick();
const stableRef = ref();
const stableHeight = ref(400);
const { checkUnit, showInfo, isComplete } = useCheckBefore();
const selectState = reactive({
  selectedRowKeys: [],
});
const originalData = ref([]); //复制原始数据源
const components = markRaw(new Map());
components.set(
  'azfy',
  defineAsyncComponent(() => import('../measuresItem/azfyContent.vue'))
);
components.set(
  'zmAzfy',
  defineAsyncComponent(() => import('../measuresItem/azfyContentZm.vue'))
);
components.set(
  'fb',
  defineAsyncComponent(() => import('./components/organizeFb.vue'))
);
components.set(
  'zm',
  defineAsyncComponent(() => import('./components/organizeZm.vue'))
);
let isPriceModel = ref(false);
let organizeModel = ref(false);
let dataReplacementvisible = ref(false);
let comModel = ref(false);
let comArea = ref();
let showModelType = ref('');
let showPriceTitle = ref('');
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let vexTable = ref();
let frameSelectRef = ref();
const projectStore = projectDetailStore();
const menuOperator = new ItemBillMenuOperator();
let contextmenuList = ref(menuOperator.menus);
let bcContextmenuList = ref(menuOperator.menus);
let dataType = ref('03');
let isAttrContent = ref(false);
let unitList = ref([]);
let oldTableData = ref([]);
let deleteVisible = ref(false);
let deleteAllVisible = ref(false);
let deleteAllLoading = ref(false);
let splitPercent = ref(0.55);
const emits = defineEmits(['updateMenuList', 'getCurrentInfo', 'resetMenu']);
let importProjectVisible = ref(false);
let importProjectRef = ref(null);
let importExcelVisible = ref(false);
let importExcelRef = ref(null);
let copyData = ref(null);
let filterateVisible = ref(false);
const filtErateRef = ref();
const deOrRcj = ref(1);
provide('subItemProjectData', {
  subItemProjectData: computed(() => tableData.value),
});
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备',
    value: 4,
  },
]);
let menuList = ref([
  {
    type: 0,
    name: '添加分部',
    kind: '01',
    isValid: false,
  },
  {
    type: 1,
    name: '添加子分部',
    kind: '02',
    isValid: false,
  },
  {
    type: 2,
    name: '添加子目',
    kind: '-1',
    isValid: false,
  },
]);
let bcMenuList = ref([
  {
    type: 2,
    name: '补充定额',
    kind: '04',
    isValid: false,
  },
  {
    type: 2,
    name: '补充人材机',
    kind: '05',
    isValid: false,
  },
]);
const quotaInfoRef = ref();
let page = ref(1);
let limit = ref(300000);
let deleteLoading = ref(false);
let selectData = ref(null);
let indexLoading = ref(false); // 索引页面loading

const bcDe = defineAsyncComponent(() => import('./components/bcDe.vue'));
const bcRcj = defineAsyncComponent(() => import('./components/bcRcj.vue'));

// let standardVisible = ref(false); //设置标准换算
// let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
let isBatchDelete = ref(false); // 是否批量删除
let standardData = ref([]); // 定额数据下挂的标准换算数据,如若为空,则不展示设置标准换算弹框
// let materialVisible = ref(false); // 是否设置主材市场价弹框
// let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
let isUpdate = ref(false); // 修改主材市场价刷新人材机明细数据
const checkList = ref([]); // 组价方案匹配筛选选中值
let {
  editClosedEvent,
  queryBranchDataById,
  mainMaterialTableData,
  updateFbData,
  queryFeefieldata,
  loading,
  saveContent,
  openEditDialog,
  showModelTitle,
  currentInfo,
  isShowModel,
  editContent,
  editKey,
  infoVisible,
  infoText,
  iconType,
  isSureModal,

  ishasRCJList,
  isClearEdit,
  isSortQdCode,
  deCode,
  rcjVisible,
  deVisible,
  priceVisible,

  isUpdateFile,
  indexVisible,
  isUpdateQuantities,
  selectUnit,
  addCurrentInfo,
  isIndexAddInfo,
  addDataSequenceNbr,
  lockFlag,
  feeFileList,
  tableData,
  originalTableData,
  materialVisible,
  materialType,
  rowType,
  materialRow,
  DJGCrefreshFeeFile,
  updateDelTempStatusColl,
  updateCancelDelTempStatusColl,
  handleNoteClick,
  batchDeleteFun,
  batchDeleteVisible,
  batchDataType,
  onFocusNode,
  closeAnnotations,
  getAnnotationsRef,
  AnnotationsRefList,
  areaStatus,
  areaVisibleType,
  closeAreaModal,
  handleNewTable,
  standardVisible,
  addDeInfo,
  getSubItemProjectBtn,
  openLevelCheckList,
} = useSubItem({
  operateList,
  frameSelectRef: frameSelectRef.value,
  resetCellData,
  checkUnit,
  vexTable: stableRef,
  emits,
  pageType: 'fbfx',
});
let {
  sTableState,
  inputRefs,
  setInputRef,
  cellMouseup,
  openEditor,
  closeEditor,
  rowSelection,
  customRow,
  pasteRowVisible,
  cellKeydown,
  copyAndPaste,
  copyFun,
  pasteFun,
} = stableHook(
  {
    selectState: selectState,
    stableRef: stableRef,
    currentInfo: currentInfo,
    tableData: tableData,
    originalData: originalData,
    copyData: copyData,
  },
  msg => {
    if (msg === 'refresh') {
      queryBranchDataById();
      emits('updateMenuList');
    }
  }
);

const {
  showColumns,
  handlerColumns,
  closeColumn,
  isDefault,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 1003,
  initCallback: () => {
    renderLine();
  },
  initColumnsCallback: () => {
    initColumns({
      columns: getTableColumns(emits),
      pageName: 'fbfx',
    });
  },
});

const cellMouseEnterEvent = row => {
  if (row?.annotations && row.isTempRemove !== 1) {
    row.noteViewVisible = true;
    row.annotationsVisible = true;
  }
};
const cellMouseLeaveEvent = row => {
  if (row?.annotations) {
    row.noteViewVisible = false;
    row.annotationsVisible = row?.isShowAnnotations || row?.noteEditVisible;
  }
};

const formatRangeCellText = ({ column, record, value }) => {
  if (column.dataIndex === 'originalQuantity') {
    return record.quantity;
  } else {
    return value;
  }
};
const { isMove } = inject('mainData');
watch(
  () => projectStore.tabSelectName,
  () => {
    indexVisible.value = false;
    if (projectStore.tabSelectName === '预算书') {
      page.value = 1;
      queryBranchDataById('other');
    }
  }
);
watch(
  () => projectStore.asideMenuCurrentInfo?.sequenceNbr,
  () => {
    indexVisible.value = false;
    if (projectStore.tabSelectName === '预算书') {
      initColumns({
        columns: getTableColumns(emits),
        pageName: 'fbfx',
      });
      page.value = 1;
      stableRef.value.clearAllSelectedRange();
      // 不是自动定位的才调用接口
      if (!projectStore.isAutoPosition) {
        // 如果过滤条件为全选
        if (projectStore.yssFiltAllCheck) {
          queryBranchDataById('other');
        } else {
          filtErateRef.value.queryClick2();
        }
      }
    }
  }
);

watch(
  () => projectStore.positionId,
  () => {
    if (projectStore.tabSelectName === '预算书' && projectStore.positionId) {
      currentInfo.value = { sequenceNbr: projectStore.positionId };
      queryBranchDataById('other', projectStore.positionId);
    }
  }
);
watch(
  () => currentInfo.value,
  (newVal, oldVal) => {
    if (newVal) {
      isMoveRow(newVal);
      emits('getCurrentInfo', newVal);
      let options = operateList.value.find(
        item => item.name === 'rcj-color-sign'
      );

      nextTick(() => {
        console.log('quotaInfoRef.value', quotaInfoRef.value.tableData);
      });
      if (newVal.kind === '05') {
        options.disabled = true;
      } else {
        options.disabled = false;
      }
      if (
        selectState.selectedRowKeys.includes(newVal.sequenceNbr) &&
        selectState.selectedRowKeys.length > 0
      ) {
        selectState.selectedRowKeys = Array.from(
          new Set([...selectState.selectedRowKeys, ...[newVal.sequenceNbr]])
        );
      } else {
        selectState.selectedRowKeys = [newVal.sequenceNbr];
      }
    }
  }
);
watch(
  () => standardVisible.value,
  val => {
    if (
      localStorage.getItem('STANDARD_CONVERSION') === 'false' ||
      !localStorage.getItem('STANDARD_CONVERSION')
    ) {
      standardVisible.value = false;
    }
  }
);
let gsPartialSummaryRef = ref();
let gsPartialSummaryStatus = ref(false);
onMounted(() => {
  initColumns({
    columns: getTableColumns(emits),
    pageName: 'fbfx',
  });
  if (!projectStore?.subItemProjectAutoPosition) {
    projectStore.subItemProjectAutoPosition = {
      copyAndPaste,
      posRow,
      queryBranchDataById,
    };
  }
  if (projectStore.asideMenuCurrentInfo?.sequenceNbr) {
    page.value = 1;
    if (!projectStore.isAutoPosition) {
      // 不是自动定位的才调用接口
      queryBranchDataById('other');
    }
  }
});
onActivated(() => {
  console.log(bus, 'bus');
  window.addEventListener('keydown', openLookup);
  bus.off('handleCopyEvent');
  // bus.on('handleCopyEvent', ({ event, name }) => {
  //   if (name === 'subItemProject') copyAndPaste(event);
  // });
  console.log('handleCopyEvent', bus);
  bus.off('moveDeData');
  bus.on('moveDeData', type => {
    console.log('handleCopyEvent', bus);
    moveDeData(type);
  });

  insetBus(bus, projectStore.componentId, 'subItemProject', async data => {
    console.log('概算bbb', data);
    if (
      !['insert-subItem', 'supplement'].includes(data.name) &&
      !(await showInfo())
    )
      return;

    if (data.name === 'partial-summary') {
      gsPartialSummaryStatus.value = true;
      nextTick(() => {
        gsPartialSummaryRef.value.open(tableData.value[0]?.deRowId);
      });
      return;
    }

    if (data.name === 'insert-subItem') {
      console.log('insert-subItem', data);
      if (!data.activeKind) {
        contextMenu();
      } else {
        addData(
          data.activeKind === '03' ? '-1' : data.activeKind,
          currentInfo.value
        );
      }
    }
    if (data.name === 'unpriced') {
      addDeInfo.value = currentInfo.value;
      console.log('addDeInfo.value', addDeInfo.value);
      // getMainMaterialAndEquipment({ sequenceNbr: addDeInfo.value.standardId });
      // materialVisible = true
      queryRcjDataByDeId(true);
    }
    if (data.name === 'lookup') {
      // 查找
      openLookup();
    }
    if (data.name === 'filterate') {
      // 过滤
      filterateVisible.value = true;
    }

    if (data.name === 'rcj-color-sign') {
      // 颜色设置
      changeRowColor(data);
    }

    if (data.name === 'supplement') {
      if (!data.activeKind) {
        bcContextMenu();
      } else {
        bcData(data);
      }
    }
    if (data.name === 'Import-project') {
      importProjectFun(data.activeKind);
    }

    if (data.name == 'openData') {
      if (data.activeKind !== undefined) {
        openData(data.activeKind);
      }
      console.log('🚀 ~ onActivated ~ data.activeKind:', data.activeKind);
    }

    if (data.name == 'dataReplacement') {
      dataReplacementvisible.value = true;
    }

    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
    if (data.name === 'delete-subItem') deleteType(null);
    if (data.name === 'installation-costs') {
      if (data.activeKind === '01') {
        showModel('azfy');
      }
      if (data.activeKind === '02') {
        showModel('zmAzfy');
      }
    }
    // 整理子目
    if (data.name === 'organize-subitems') {
      if (data.activeKind === 'fb') {
        showOrganizeModel(data.activeKind);
      }
      if (data.activeKind === 'zm') {
        showOrganizeModel(data.activeKind);
      }
    }
  });
});

onDeactivated(() => {
  lookupVisible.value = false;
  window.removeEventListener('keydown', openLookup);
});

/**
 * 判断当前选择的行是否可以上下移动
 * @function isMoveRow
 * @param {Object} newVal - 传入的参数
 * @returns {void} - 无返回值
 */
const isMoveRow = newVal => {
  let selectRows = selectState.selectedRowKeys.map(item => {
    return tableData.value.find(i => i.sequenceNbr === item);
  });
  console.log('newVal', selectRows);
  if (newVal?.kind) {
    if (selectState.selectedRowKeys.length === 1) {
      //上下移动判断
      if (newVal?.isFirst || selectRows.some(item => item.isFirst)) {
        isMove.value.isFirst = true;
      } else {
        isMove.value.isFirst = false;
      }
      if (newVal?.isLast || selectRows.some(item => item.isLast)) {
        isMove.value.isLast = true;
      } else {
        isMove.value.isLast = false;
      }
    } else {
      isMove.value.isFirst = true;
      isMove.value.isLast = true;
    }
  }
};
const deNameRef = node => {
  return node.parentNode.parentNode.parentNode.parentNode.parentNode;
};
// 导入项目
const importProjectFun = kind => {
  if (kind === '01') {
    importExcelVisible.value = true;
  }
  if (kind === '02') {
    api
      .importYgs({
        importConstructId: projectStore.currentTreeGroupInfo?.constructId,
        importUnitId: projectStore.currentTreeInfo?.id,
      })
      .then(res => {
        if (res.status !== 200) {
          return message.error(res.message);
        }
        importProjectVisible.value = true;
        nextTick(() => {
          console.log(importProjectRef.value);
          importProjectRef.value.init(res.result);
        });
      });
  }
};
const codeTable = ref([]);
const showRepeat = ref();
const repeatCode = ref({
  deRowId: '',
  deStandardId: '',
  row: {},
});
const isClickRepeat = ref(false);
watch(
  () => showRepeat.value,
  val => {
    if (!val) {
      if (isClickRepeat.value) {
        console.log('点击重复弹窗关闭');
        isClickRepeat.value = false;
        selectRepeatDe();
      } else {
        console.log('不选择数据重复弹窗关闭');
        stableRef.value.closeEditor();
      }
    }
  }
);
const timeCLose = (
  closeEditor,
  { row, column },
  modelValue,
  name,
  late = 200
) => {
  console.log('modelValue', modelValue, !modelValue);
  // deCodeInput.value.focus();
  if (showRepeat.value) return;
  if (row[column.field] === modelValue) return closeEditor();
  if (!modelValue) return closeEditor();
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deCode: modelValue,
  };
  api.checkAndQueryDe(apiData).then(res => {
    console.log('checkAndQueryDe', apiData, res);
    if (res.code == 500 && !showRepeat.value) {
      editClosedEvent({ row, column }, modelValue, name);
      closeEditor();
    } else {
      showRepeat.value = true;
      let arr = [];
      if (res.result.db.length > 0) {
        arr.push({
          deCode: '定额库中子目',
          children: res.result.db,
        });
      }
      if (res.result.local.length > 0) {
        arr.push({
          deCode: '当前工程中子目',
          children: res.result.local,
        });
      }
      repeatCode.value.deRowId = row.sequenceNbr;
      if (res.result.db.length > 0) {
        repeatCode.value.deStandardId = res.result.db[0].sequenceNbr;
      } else {
        repeatCode.value.deStandardId = res.result.local[0].sequenceNbr;
      }
      codeTable.value = arr;
      inputRefs[column.field + 'Input'].focus();
    }
  });
};
const timeCLoselQuantity = (
  closeEditor,
  { row, column },
  modelValue,
  oldval,
  late = 200
) => {
  setTimeout(() => {
    if (!comModel.value) {
      editClosedEvent({ row, column }, modelValue, oldval);
      closeEditor();
    }
  }, late);
};
const comModelCLose = () => {
  comModel.value = false;
  stableRef.value.closeEditor();
};
const comModelsureData = () => {
  editClosedEvent(
    { row: currentInfo.value, column: { field: 'originalQuantity' } },
    comArea.value.value,
    currentInfo.value.originalQuantity
  );
  stableRef.value.closeEditor();
  comModel.value = false;
};
const cellClickEvent = ({ row }) => {
  if (!row.sequenceNbr) return;
  repeatCode.value.deStandardId = row.sequenceNbr;
  repeatCode.value.row = row;
  isClickRepeat.value = true;
  showRepeat.value = false;
};
const selectRepeatDe = () => {
  // queryBranchDataById();
  console.log('selectRepeatDe', {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRow: repeatCode.value.row,
    deRowId: repeatCode.value.deRowId,
    deStandardId: repeatCode.value.deStandardId,
  });
  api
    .selectDe({
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: repeatCode.value.deRowId,
      deRow: JSON.parse(JSON.stringify(repeatCode.value.row)), //repeatCode.value,
      deStandardId: repeatCode.value.deStandardId,
    })
    .then(res => {
      repeatCode.value.deRowId = '';
      repeatCode.value.deStandardId = '';
      repeatCode.value.row = {};
      stableRef.value.closeEditor();
      queryBranchDataById();
    });
};
const importYgsDeCancel = () => {
  console.log(
    'importYgsDeCancel',
    importProjectRef.value.treeData[0].sequenceNbr
  );

  api.importYgsDeCancel({
    importConstructId: importProjectRef.value.treeData[0].sequenceNbr,
  });
};
onBeforeUnmount(() => {
  // window.removeEventListener('keydown', copyAndPaste);
});

// 定位方法
const posRow = sequenceNbr => {
  console.log('posRow', sequenceNbr);
  currentInfo.value = { sequenceNbr };
  queryBranchDataById('other', sequenceNbr);
};

// 删除数据
const deleteType = async () => {
  isBatchDelete.value = false;
  if (!(await showInfo())) {
    return;
  }
  if (currentInfo.value.kind === '00') {
    message.warning('该行不可删除');
    return;
  }
  deleteVisible.value = true;
};
// 批量删除数据
const deleteAllType = async () => {
  isBatchDelete.value = false;
  // delBatchData()
  deleteVisible.value = true;
};

const cancel = () => {
  deleteVisible.value = false;
};
const addData = async (kind, row) => {
  // console.info(111111111111);
  console.log('addData', kind, row, tableData.value);
  // 如果有过滤条件并且是处理过父级id的数据
  if (!projectStore.yssFiltAllCheck && row.oldParentId) {
    row.parentId = row.oldParentId;
  }
  if (!(await showInfo())) return;
  let parentId = '';
  let prevDeRowId = row.sequenceNbr || '';
  if (
    (kind === row.kind && ['00', '01'].includes(row.kind)) ||
    (deMapFun.isDe(row.kind) && kind === '-1') ||
    (kind === '01' && row.kind === '02')
  ) {
    if (row.kind === '02') kind = '02';
    parentId = row.parentId;
  } else {
    parentId = row.sequenceNbr;
  }
  if (row.kind === '05') {
    parentId = tableData.value.find(
      a => a.sequenceNbr === row.parentId
    ).parentId;
    prevDeRowId = row.parentId;
  }
  if (row.kind !== '00') {
    if (
      ['00', '01', '02'].includes(
        tableData.value.find(a => a.sequenceNbr === row.parentId)?.kind
      ) &&
      deMapFun.isDe(row.kind) &&
      ['00', '01', '02'].includes(kind)
    ) {
      parentId =
        tableData.value.find(a => a.sequenceNbr === row.parentId).parentId ||
        tableData.value.find(a => a.sequenceNbr === row.parentId).sequenceNbr;
      prevDeRowId = row.parentId;
    }
  }

  let apiData = {
    de: {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      type: kind,
      displaySign: 0,
      parentId,
    },
    deRow: !['00', '01', '02'].includes(currentInfo.value.kind)
      ? JSON.parse(JSON.stringify(currentInfo.value))
      : '',
    prevDeRowId,
  };
  console.log('addDataapiData', apiData);
  api.createDe(apiData).then(res => {
    if (res.status === 200 && res.result) {
      if (kind === '01' || kind === '02') {
        emits('updateMenuList');
      }
      console.log(res);
      currentInfo.value = res.result;
      // addDataSequenceNbr.value = res.result.data.sequenceNbr;
      // page.value = Math.ceil((res.result.index + 1) / limit.value);
      message.success('插入成功');
      projectStore.yssFiltAllCheck = true;
      queryBranchDataById('position');
    }
  });
};

// 弹框提示点击确定按钮事件
const updateCurrentInfo = () => {
  updateFbData(currentInfo.value, 'quantityExpression');
};

// 工程量明细更改后刷新当前行数据
const refreshCurrentInfo = (refreshFeeFile = false) => {
  addDataSequenceNbr.value = addCurrentInfo.value
    ? addCurrentInfo.value.sequenceNbr
    : currentInfo.value.sequenceNbr;
  DJGCrefreshFeeFile.value = refreshFeeFile ? true : false;
  queryBranchDataById();
};

/**
 * 设置主材市场价弹框操作更新
 * @param type 1 关闭弹框, 2 刷新数据
 */
const setUpdate = (type, row) => {
  materialVisible.value = false;
  if (materialType.value) {
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
    materialType.value === 'add'
      ? fillFromIndexPage(row, type, 1)
      : currentInfoReplace(row, type, 2);
    queryRule();
  } else if (type === 2) {
    isUpdate.value = true;
    setTimeout(() => {
      isUpdate.value = false;
    }, 100);
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
  }
  queryBranchDataById();
  materialType.value = '';
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    field:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const onDbClickFile = v => {
  updateFbData(
    {
      ...currentInfo.value,
      projectAttr: v.value,
    },
    'projectAttr'
  );
};

const changeStatus = row => {
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // updateFbData(row, 'seq');
};

const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.openTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      if (projectStore.yssFiltAllCheck) {
        queryBranchDataById('load');
      } else {
        // filtErateRef.value.queryClick2();
        let arr = [];
        openFiltTree(row.sequenceNbr);
      }
    }
  });
};
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.closeTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      if (projectStore.yssFiltAllCheck) {
        queryBranchDataById('load');
      } else {
        closeFiltTree(row);
      }
    }
  });
};
// 过滤展开
const openFiltTree = id => {
  let itemFilt = JSON.parse(
    JSON.stringify(oldTableData.value.filter(a => a.parentId === id))
  );
  for (let item of itemFilt) {
    let datas = JSON.parse(JSON.stringify(oldTableData.value));
    let itemFilt = datas.find(a => a.parentId === item.sequenceNbr);
    // 如果没有子级则不显示展开收起符号
    if (itemFilt == undefined) {
      item.displaySign = 0;
    } else {
      item.displaySign = 2;
    }
  }
  let newArr = JSON.parse(JSON.stringify(tableData.value));
  for (let i in newArr) {
    if (newArr[i].sequenceNbr == id) {
      let num = parseInt(i) + 1;
      tableData.value = [
        ...newArr.slice(0, num),
        ...itemFilt,
        ...newArr.slice(num),
      ];
    }
  }
};
// 过滤收起
const closeFiltTree = row => {
  let itemFilt = JSON.parse(
    JSON.stringify(
      oldTableData.value.filter(a => a.parentId === row.sequenceNbr)
    )
  );
  let idsInArray2 = new Set(itemFilt.map(item => item.sequenceNbr));
  tableData.value = tableData.value.filter(
    item => !idsInArray2.has(item.sequenceNbr)
  );
  for (let item of itemFilt) {
    if (item.displaySign == 1) {
      closeFiltTree(item);
    }
  }
};
const cellDBLClickEvent = async ({ row, column }) => {
  console.log('column', column);
  console.log('column--------==', row);
  if (!sTableState.prevDbTime) return;
  if (['deCode', 'projectAttr'].includes(column.field) && !(await showInfo()))
    return;
  // if (['00', '01', '02'].includes(row.kind)) return;
  if (column.field === 'deCode') {
    if (row.kind === '05') {
      console.log('openeen', row, quotaInfoRef.value);
      // quotaInfoRef.value.cellDBLClickEvent({ ...row, rcjId: row.standardId });
    } else {
      indexVisible.value = true;
      dataType.value = row.kind;
    }
  }
  currentInfo.value = row;
};
const getMainMaterialAndEquipment = (row, rowType, isAdd, type) => {
  return new Promise((resolve, reject) => {
    if (rowType === 0) {
      type === 'add'
        ? fillFromIndexPage(row, rowType, isAdd)
        : currentInfoReplace(row, rowType, isAdd);
    } else {
      api
        .getMainMaterialAndEquipment({ deStandardId: row.sequenceNbr })
        .then(res => {
          deOrRcj.value = rowType;
          if (res.result.length > 0) {
            console.log('mainMaterialTableData', row, res.result);
            addDeInfo.value = currentInfo.value;
            if (addDeInfo.value.deName == undefined) {
              addDeInfo.value.deName = row.deName;
            }
            mainMaterialTableData.value = res.result;
            materialVisible.value = true;
            materialType.value = type;
            materialRow.value = row;
            console.log('mainMaterialTableData', mainMaterialTableData.value);
            // updateMainMaterialAndEquipment(res.result).then(() => {
            //   console.log(res, row.sequenceNbr, 'getMainMaterialAndEquipment');
            // resolve();
            // });
          } else {
            type === 'add'
              ? fillFromIndexPage(row, rowType, isAdd)
              : currentInfoReplace(row, rowType, isAdd);
          }
        })
        .catch(error => {
          reject(error);
        });
    }
  });
};

const fillFromIndexPage = async (row, type, isAdd) => {
  console.log(
    '🚀 ~ fillFromIndexPage ~ row:getMainMaterialAndEquipment3',
    type,
    row,
    currentInfo.value
  );
  indexLoading.value = true;
  // 如果有过滤条件并且是处理过父级id的数据
  if (!projectStore.yssFiltAllCheck && currentInfo.value.oldParentId) {
    currentInfo.value.parentId = currentInfo.value.oldParentId;
  }
  let apiName =
    type === 1 ? 'createDeRowAppendBaseDe' : 'createDeRowAppendBaseResource';
  let parentId = ['00', '01', '02'].includes(currentInfo.value.kind)
    ? currentInfo.value.sequenceNbr
    : currentInfo.value.parentId;
  let prevRowId = ['00', '01', '02'].includes(currentInfo.value.kind)
    ? ''
    : currentInfo.value.sequenceNbr;
  let apiData = {
    deRow: {
      sequenceNbr: null,
      type: '03',
      prevRowId,
      parentId,
      displaySign: 0,
      name: '',
      code: '',
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
    },

    [type === 1 ? 'baseDeId' : 'resourceId']: row.sequenceNbr,
  };
  console.log('api[apiName](apiData)', apiName, apiData);
  api[apiName](apiData).then(res => {
    indexLoading.value = false;
    console.log(res);
    if (res.status === 200) {
      console.log('createDeRowAppendBaseDe', row, res);
      if (['03', '04', '-1'].includes(row.kind) && row.isDeResource === 0) {
        addDeInfo.value = res.result;
        setTimeout(() => {
          queryRcjDataByDeId(false);
        }, 1000);
      }
      message.success(isAdd === 1 ? '插入成功' : '替换成功');
      currentInfo.value = res.result;
      projectStore.yssFiltAllCheck = true;
      queryBranchDataById('position');
    } else {
      return message.error(res.message);
    }
  });
};
// 插入功能
const currentInfoReplace = async (row, type, isAdd) => {
  console.log(
    '🚀 ~ fillFromIndexPage ~ row:getMainMaterialAndEquipment3',
    row,
    currentInfo.value
  );
  let isDe = row.isDe || row.isDeResource === 0 ? 1 : 0;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    deStandardId: row.sequenceNbr,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    isDe,
  };
  console.log('apiData', apiData, row, currentInfo.value);
  let apiName = 'fillFromIndexPage';
  indexLoading.value = true;
  api[apiName](apiData).then(res => {
    console.log('成功！', row, res);
    indexLoading.value = false;
    if (res.status === 200) {
      if (
        apiName === 'fillFromIndexPage' &&
        (res.result.type === '03' || res.result.type === '04') &&
        res.result.isDeResource === 0
      ) {
        // 插入子目
        addDeInfo.value = res.result;
        console.log('addDeInfo.value', addDeInfo.value);
        setTimeout(() => {
          queryRcjDataByDeId(false);
        }, 1000);
      }
      message.success(isAdd == 1 ? '插入成功' : '替换成功');
      projectStore.yssFiltAllCheck = true;
      queryBranchDataById('position');
    } else {
      return message.error(res.message);
    }
  });
};

const contextMenu = async () => {
  let tempList = xeUtils.clone(menuList.value, true);
  await addRowVisible(currentInfo.value);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  console.log('tempList', tempList);
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'insert-subItem').options =
    tempList;
};

const bcContextMenu = async () => {
  let tempList = xeUtils.clone(bcMenuList.value, true);
  await addRowVisible(currentInfo.value);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  bcContextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'supplement').options = tempList;
};

const bcData = async item => {
  if (!(await showInfo())) return;
  deCode.value = '';
  if (item.activeKind === '04') {
    deVisible.value = true;
  } else {
    rcjVisible.value = true;
  }
};

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};
const removeDeRow = isDelAll => {
  if (deleteLoading.value) return;
  deleteLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    isDelAll,
  };
  api
    .delFbData(apiData)
    .then(res => {
      console.log(res);
      if (res.status === 200) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');
        emits('updateMenuList');
        queryBranchDataById();
      }
    })
    .catch(err => {
      deleteLoading.value = false;
    });
};
const delFbData = type => {
  if (deleteLoading.value) return;
  if (currentInfo.value.kind == '05') {
    quotaInfoRef.value.materialRef.delRcjData(currentInfo.value);
    setTimeout(() => {
      deleteVisible.value = false;
      deleteLoading.value = false;
    }, 200);
    return;
  }
  deleteLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value.sequenceNbr)),
  };
  console.log(apiData);
  let index = tableData.value.findIndex(
    x => x.sequenceNbr === currentInfo.value.sequenceNbr
  );
  console.log(index, tableData.value[index - 1]);
  if (tableData.value[index].kind === '03') {
    currentInfo.value = tableData.value[index - 1];
  } else {
    if (index === tableData.value.length - 1) {
      currentInfo.value = tableData.value[index - 1];
    } else {
      currentInfo.value = tableData.value[index + 1];
    }
  }
  api
    .delFbData(apiData)
    .then(res => {
      console.log(res);
      if (res.status === 200) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');
        emits('updateMenuList');
        queryBranchDataById();
      }
    })
    .catch(err => {
      deleteLoading.value = false;
    });
};

// 插入分部子分部控制
const addRowVisible = async row => {
  console.log('addRowVisible', row);
  let data = originalTableData.value.find(
    a => a.sequenceNbr === row?.sequenceNbr
  );
  let res = await api.queryDeChildType({
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    singleId: '',
    deRowId: data.deRowId,
  });

  let children = res.result.map(a => {
    return { kind: a };
  });
  row.children = children;
  console.log('res', res);
  methodVisible(row);

  function methodVisible(row) {
    console.log('rowa0', row);
    if (row.kind === '00') {
      // 整个项目添加
      if (row.children.length === 0) {
        currentInfo.value.optionMenu = [0, 2];
      } else if (
        row.children.filter(a => a.kind === '01' || a.kind === '02').length ===
          row.children.length ||
        row.customLevel > 4
      ) {
        currentInfo.value.optionMenu = [0];
      } else if (
        row.children.filter(a => deMapFun.isDe(a.kind) || a.kind === '-1')
          .length > 0
      ) {
        currentInfo.value.optionMenu = [0, 2];
      }
    }
    if (row.kind === '01') {
      // 分部添加
      if (row.children.length === 0) {
        currentInfo.value.optionMenu = [0, 1, 2];
      } else if (row.children.filter(a => a.kind === '02').length > 0) {
        currentInfo.value.optionMenu = [0, 1];
      } else if (row.children.filter(a => deMapFun.isDe(a.kind)).length > 0) {
        currentInfo.value.optionMenu = [0, 1, 2];
      }
      if (
        calculateDepth(row) +
          calculateLevelToRoot(row.sequenceNbr, tableData.value[0]) >=
        4
      ) {
        if (row.children.filter(a => a.kind === '02').length > 0) {
          currentInfo.value.optionMenu = [0, 1];
        } else {
          currentInfo.value.optionMenu = [0];
        }
      }
    }
    if (row.kind === '02') {
      // 分部添加
      if (row.children.length === 0 && row.customLevel < 5) {
        currentInfo.value.optionMenu = [0, 1, 2];
      } else if (row.customLevel > 4) {
        currentInfo.value.optionMenu = [0, 2];
      } else if (row.children.filter(a => a.kind === '02').length > 0) {
        currentInfo.value.optionMenu = [0, 1];
      } else if (row.children.filter(a => deMapFun.isDe(a.kind)).length > 0) {
        currentInfo.value.optionMenu = [0, 1, 2];
      }
      console.log(
        'calculateDepth',
        calculateDepth(row),
        calculateLevelToRoot(row.sequenceNbr, tableData.value[0])
      );
      if (
        calculateDepth(row) +
          calculateLevelToRoot(row.sequenceNbr, tableData.value[0]) >=
        4
      ) {
        if (row.children.filter(a => a.kind === '02').length > 0) {
          currentInfo.value.optionMenu = [0, 1];
        } else {
          currentInfo.value.optionMenu = [0, 2];
        }
      }
    }
    if (deMapFun.isDe(row.kind)) {
      // 只可以添加子目
      currentInfo.value.optionMenu = [2];
    }
    if (
      (deMapFun.isDe(row.kind) &&
        ['00', '01', '02'].includes(
          tableData.value.find(a => a.sequenceNbr == row.parentId).kind
        )) ||
      row.kind === '-1'
    ) {
      let rowIndex = tableData.value.findIndex(
        a => a.sequenceNbr == row.sequenceNbr
      );
      // console.log(,'add111')
      let lastKind = tableData.value[rowIndex - 1].kind;
      let nextKind = tableData.value[rowIndex + 1];
      // if(['00','01','02'].includes(lastKind)){
      if (['01', '02'].includes(lastKind)) {
        currentInfo.value.optionMenu = [2];
      } else {
        currentInfo.value.optionMenu = [0, 2];
      }
    }
    // 如果空定额行父级为定额则只可以增加子目
    if (
      deMapFun.isDe(row.kind) &&
      ['03'].includes(
        tableData.value.find(a => a.sequenceNbr == row.parentId).kind
      ) &&
      row.kind === '-1'
    ) {
      currentInfo.value.optionMenu = [2];
    }
    // 如果是过滤情况下只可以增加子目，不可以增加分部
    if (!projectStore.yssFiltAllCheck) {
      currentInfo.value.optionMenu = [2];
    }
  }
};
function calculateDepth(node) {
  if (!node.children || node.children.length === 0) {
    return 0; // Leaf node, depth is 0
  } else {
    let maxChildDepth = 0;
    if (node.children.filter(a => deMapFun.isDe(a.kind)).length == 0) {
      for (let child of node.children) {
        const childDepth = calculateDepth(child);
        maxChildDepth = Math.max(maxChildDepth, childDepth);
      }
    }
    return maxChildDepth + 1; // Add 1 to include current node
  }
}
function calculateLevelToRoot(nodeId, treeData) {
  // 辅助函数，用于递归地查找节点
  function findNode(currentNode, targetId, currentLevel) {
    if (currentNode.sequenceNbr === targetId) {
      return currentLevel;
    }
    if (currentNode.children) {
      for (let child of currentNode.children) {
        const result = findNode(child, targetId, currentLevel + 1);
        if (result !== -1) {
          return result;
        }
      }
    }
    return -1;
  }

  // 调用辅助函数来计算层级数
  const levelFromRoot = findNode(treeData, nodeId, 0);
  return levelFromRoot;
}
const contextMenuClickEvent = ({ menu, row }, args) => {
  // console.log(menu, row, args);
  if (
    menu.code == 'noteList' ||
    menu.code == 'add-note' ||
    menu.code == 'edit-note' ||
    menu.code == 'del-note' ||
    menu.code == 'show-note' ||
    menu.code == 'hide-note' ||
    menu.code == 'del-all-note'
  ) {
    if (menu.code != 'noteList') {
      handleNoteClick(menu, row);
    }
  } else if (menu.code === 'delete') {
    deleteType();
  } else if (menu.code === 'copy') {
    copyFun(args);
    // copyFun();
  } else if (menu.code === 'paste' || menu.code === 'pasteChild') {
    pasteFun(args, menu.code);
  } else if (menu.code === 'tempDelete') {
    updateDelTempStatusColl(row);
  } else if (menu.code === 'cancelTempDelete') {
    updateCancelDelTempStatusColl(row);
  } else if (
    menu.code === 'batchDelete-child1' ||
    menu.code === 'batchDelete-child2' ||
    menu.code === 'batchDelete-child3'
  ) {
    batchDeleteVisible.value = true;
    batchDataType.value = menu.type;
  } else if (menu.code === 'generateMainMaterials') {
    supplementRcjDataByDe('5');
  } else if (menu.code === 'syncGenerateDevice') {
    let data = tableData.value.find(a => a.sequenceNbr == row.parentId);
    console.log('syncGenerateDevice', data);
    quotaInfoRef.value.materialRef.setSynchronization(row, data);
    args.hidePopup();
  } else if (menu.code === 'generateDevice') {
    supplementRcjDataByDe('4');
  } else if (menu.code === 'subtileAllDelete') {
    deleteAllVisible.value = true;
  } else if (menu.code === 'copyCell') {
    copyClick(args);
  } else if ([0, 1, 2].includes(menu.code)) {
    addData(menu.kind, row);
  }
};
const copyClick = (args, type = 'cell') => {
  if (type === 'cell') {
    copyData.value = [];
    stableRef.value.copySelectedRange();
    message.success('复制单元格成功');
  }
  args.hidePopup();
};
// 删除所有子目
const subtileAllDeleteClick = async () => {
  deleteAllLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.batchDeleteAll(apiData).then(res => {
    queryBranchDataById();
    deleteAllVisible.value = false;
    deleteAllLoading.value = false;
    emits('updateMenuList');
  });
};
const supplementRcjDataByDe = async kind => {
  let kindType = {
    4: '设备',
    5: '主材',
  };
  let materialCode = await getDefaultCode(kindType[kind]);
  console.log('materialCode', materialCode);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    deId: currentInfo.value.sequenceNbr,
    deRowId: currentInfo.value.deRowId,
    kind,
    materialCode,
  };
  console.log('supplementRcjDataByDeapiData', apiData);
  api.supplementRcjDataByDe(apiData).then(res => {
    console.log('supplementRcjDataByDe', res);
    queryBranchDataById();
  });
};
const getDefaultCode = async materialCode => {
  return new Promise((resolve, reject) => {
    api
      .getDefaultCode({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        prefix: '补充' + materialCode,
        unitId: projectStore.currentTreeInfo?.id,
      })
      .then(res => {
        console.log('getDefaultCode', res);
        resolve('补充' + materialCode + res.result);
      })
      .catch(error => {
        reject(error);
      });
  });
};

// 编辑弹框关闭方法
const editCancel = () => {
  isShowModel.value = false;
};

const deSaveData = inputData => {
  if (deCode.value) {
    addBcDeData(inputData);
  } else {
    updateDeByPage(inputData);
  }
};

const rcjSaveData = inputData => {
  if (deCode.value) {
    addBjqBcRcjData(inputData);
  } else {
    spRcjByPage(inputData);
  }
};

// 点击补充按钮补充定额数据
const updateDeByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    prevDeRowId:
      currentInfo.value.kind != '05'
        ? currentInfo.value.sequenceNbr
        : currentInfo.value.parentId,
    userDe: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('appendUserDeNextRow', apiData);
  api.appendUserDeNextRow(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      deCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};
// 点击补充按钮补充定额数据
const addBcDeData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowId: JSON.parse(JSON.stringify(currentInfo.value)).sequenceNbr,
    userDe: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('点击补充按钮补充定额数据', apiData);
  api.appendUserDe(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = JSON.parse(
        JSON.stringify(currentInfo.value)
      ).sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      deCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};

// 预算书 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    prevDeRowId:
      currentInfo.value.kind != '05'
        ? currentInfo.value.sequenceNbr
        : currentInfo.value.parentId,
    userResource: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('spRcjByPage', apiData);
  api.spRcjByPage(apiData).then(res => {
    addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
    message.success('人材机插入成功');
    rcjVisible.value = false;
    queryBranchDataById();
  });
};
// 预算书 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    deId: currentInfo.value.sequenceNbr,
    detail: JSON.parse(JSON.stringify(inputData)),
    deRowId: props.currentInfo.deRowId,
  };
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      rcjVisible.value = false;
      message.success('人材机新建成功');
      queryBranchDataById();
    }
  });
};
const showModel = type => {
  isPriceModel.value = true;
  switch (type) {
    case 'azfy':
      // 安装费用
      showModelType.value = 'azfy';
      showPriceTitle.value = '安装费用记取';
      break;
    case 'zmAzfy':
      // 子目安装费用设置
      showModelType.value = 'zmAzfy';
      showPriceTitle.value = '子目安装费用设置';
      break;
  }
};
// 整理子目
const showOrganizeModel = type => {
  organizeModel.value = true;
  switch (type) {
    case 'fb':
      // 分部
      showModelType.value = 'fb';
      showPriceTitle.value = '分部整理';
      break;
    case 'zm':
      // 子目
      showModelType.value = 'zm';
      showPriceTitle.value = '子目排序';
      break;
  }
};
// 记取费用数据更新
const updateData = type => {
  isPriceModel.value = false;
  organizeModel.value = false;
  queryBranchDataById();
  emits('updateMenuList');
  // queryFeefieldata();
  // querySzType();
  // queryUnit();
};

const closePriceModel = () => {
  isPriceModel.value = false;
  organizeModel.value = false;
};

// 补充页面点击取消操作 type 1 清单 2 定额 3 人材机
const bcCancel = type => {
  if (type === 1) {
    qdVisible.value = false;
  } else if (type === 2) {
    deVisible.value = false;
  } else {
    rcjVisible.value = false;
  }
  console.log(deCode.value);
  if (deCode.value) {
    currentInfo.value.deCode = currentInfo.value.originalDeCode;
  }
};

// 批量删除
const delBatchData = () => {
  let ids = [];
  selectData.value.forEach(item => {
    if (item.kind !== '00' && item.kind !== '05') {
      ids.push(item.sequenceNbr);
    }
  });
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    deRowIdList: ids,
  };
  api.batchDeleteDe(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      if (currentInfo.value.kind === '01' || currentInfo.value.kind === '02') {
        emits('updateMenuList');
      }
      queryBranchDataById();
    }
  });
};

// 获取当前点击行下挂所有数据
/**
 * 以前的这里也修改了用户选择的数据，用户选择了数据，然后又在这掉接口了，不知道为啥，待以后优化吧
 */
const queryAllDataByBranchId = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: currentInfo.value?.sequenceNbr,
    pageSize: 10000,
    pageNum: page.value,
  };
  api.searchForSequenceNbr(apiData).then(async res => {
    if (res.status === 200 && res.result) {
      // 后端给的所有的数据，需要过滤。
      const handleData =
        await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
          res.result
        );
      selectData.value = handleData;
      // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
      // if (handleData.length) {
      //   selectData.value.isCopy = true;
      // }
    }
  });
};

const batchPasteQdDeData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  api.fbBatchPaste(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('粘贴成功');
      queryBranchDataById();
    } else {
      message.error('粘贴失败');
    }
  });
};

// 获取定额是否存在标准换算信息
const queryRule = () => {
  if (!addDeInfo.value?.standardId) {
    return;
  }
  let apiData = {
    standardDeId: addDeInfo.value?.standardId,
    fbFxDeId: addDeInfo.value?.sequenceNbr,
    libraryCode: addDeInfo.value?.libraryCode,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('标准换算列表参数4', apiData);
  api.queryRule(apiData).then(res => {
    console.log('标准换算列表参数结果', res);
    if (res.status === 200 && res.result) {
      if (res.result.conversionList && res.result.conversionList.length > 0) {
        standardVisible.value = true;
        standardData.value = res.result.conversionList;
      }
    }
  });
};

// 获取人材机明细数据
const queryRcjDataByDeId = (bol = true, deItem = null) => {
  let apiData = {
    deRowId: addDeInfo.value?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    type: addDeInfo.value?.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  console.log('// 获取人材机明细数据', addDeInfo.value, deItem, apiData);
  if (!apiData.deRowId) return;
  api.getAllRcjDetail(apiData).then(res => {
    // console.log('定额明细数据', res);
    if (res.status === 200) {
      if (res.result?.length > 0) {
        ishasRCJList.value = true;
      }
      console.log(
        '  mainMaterialTableData.value',
        res,
        mainMaterialTableData.value
      );
      if (bol) {
        mainMaterialTableData.value = res.result.filter(
          x => x.kind == 5 || x.kind == 4
        );
        if (mainMaterialTableData.value.length > 0) {
          materialVisible.value = true;
        }
      } else {
        queryRule();
      }
    }
  });
};

/**
 * 拖动了高度
 */
const dragHeight = h => {
  stableHeight.value = h - (window.innerWidth < 1370 ? 0 : 45);
};
const dragHeight2 = h => {
  stableHeight2.value = h - 45;
  console.log(window.innerWidth);
};
window.addEventListener('resize', function () {
  let tableEl = document.querySelector('.table-content');
  stableHeight.value =
    tableEl.clientHeight - (window.innerWidth < 1370 ? 0 : 50);
});
// 预算书定额上移下移
const moveDeData = type => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    upId: projectStore.currentTreeInfo?.id,
    qdId: currentInfo.value.parentId,
    deId: currentInfo.value.sequenceNbr,
    operateAction: type === 1 ? 'up' : 'down',
    type: 'fbfx',
  };
  api.moveDeData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('移动成功');
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      queryBranchDataById('other');
    }
  });
};

// 分部分项，判断展开的状态
watchEffect(() => {
  if (
    projectStore.componentId == 'subItemProject' &&
    projectStore.currentTreeInfo?.id
  ) {
    getSubItemProjectBtn();
  }
});

/**
 * 展开到
 */
const openData = type => {
  const postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    type: openLevelCheckList.value[type]?.type,
  };
  console.log('展开到', postData);
  csProject.openLevel(postData).then(res => {
    if (res.status == 200) {
      message.success('展开成功');
      queryBranchDataById('other');
    }
  });
};

//颜色设置，
const handleColorSign = data => {
  let isAllDe = true;
  if (data?.length) {
    for (let i of selectState.selectedRowKeys) {
      let rowType = tableData.value.find(a => a.sequenceNbr == i)?.kind;
      if (['00', '01', '02'].includes(rowType)) {
        isAllDe = false;
        break;
      }
    }
  } else {
    isAllDe = false;
  }

  let options = operateList.value.find(
    item => item.name === 'rcj-color-sign'
  )?.options;
  options.forEach(item => {
    item.isValid = isAllDe;
  });
};

// 设置颜色
const changeRowColor = data => {
  let selectdata = toRaw(selectState.selectedRowKeys);
  let datalist = selectdata
    .map(item => {
      return tableData.value.find(a => {
        if (a.sequenceNbr == item && a.kind != '05') {
          return a;
        }
      })?.sequenceNbr;
    })
    .filter(item => item);
  let apiData = {
    levelType: projectStore.currentTreeInfo.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    deRowId: datalist,
  };
  if (data.activeKind) {
    console.log(data, currentInfo.value);
    apiData.color = 'default';
    if (data.activeKind !== 'default') {
      apiData.color = data.options.find(a => a.kind == data.activeKind).color;
    }
    console.log('apiData', apiData);
    csProject.setDeColor(apiData).then(res => {
      queryBranchDataById();
    });
  }
};
watch(
  () => selectState.selectedRowKeys,
  (newV, oldV) => {
    isMoveRow(newV);
    handleColorSign(newV);
  },
  {
    immediate: true,
  }
);

// 查找功能
let lookupVisible = ref(false);
let isFiltErate = ref(false);
const openLookup = event => {
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};

const lookupConfig = reactive({
  columns: [
    {
      field: 'deName',
      label: '名称',
      type: 'input',
      value: '',
      placeholder: '请输入名称',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'deCode',
      label: '编码',
      type: 'input',
      placeholder: '请输入编码',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      placeholder: '请输入或选择单位',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'quantity',
      label: '工程量',
      type: 'area',
      startValue: '',
      endValue: '',
    },
    {
      type: 'areaList',
      icon: 'icon-danjia',
      name: '单价',
      index: 1,
      fieldList: [
        {
          field: 'price',
          label: '单价',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'RSum',
          label: '人工',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'CSum',
          label: '材料',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'JSum',
          label: '机械',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
      ],
    },
    {
      type: 'areaList',
      icon: 'icon-hejia',
      name: '合价',
      index: 2,
      fieldList: [
        {
          field: 'totalNumber',
          label: '合价',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'rTotalSum',
          label: '人工',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'cTotalSum',
          label: '材料',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'jTotalSum',
          label: '机械',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
      ],
    },
  ],
  conditionType: 'OR',
  tableData: tableData,
});
// 查找确认回调
const lookupCallback = async rows => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData.value, true);
  } else {
    await handleNewTable(rows);
  }

  // if(tableData.value.length!==originalTableData.length){
  //   isFiltErate.value=true
  // }else{
  //   isFiltErate.value=false
  // }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    selectState.selectedRowKeys = [info.sequenceNbr];
    stableRef.value.scrollTo(
      {
        rowKey: info.sequenceNbr,
      },
      'smooth'
    );
    currentInfo.value = info;
  });
};
// 过滤确认回调
const filtQuery = row => {
  tableData.value = row;
  oldTableData.value = row;
  nextTick(() => {
    const info = row[0];
    selectState.selectedRowKeys = [info.sequenceNbr];
    stableRef.value.scrollTo(
      {
        rowKey: info.sequenceNbr,
      },
      'smooth'
    );
    currentInfo.value = info;
    emits('updateMenuList');
  });
};
const visibleChange = (val, row) => {
  if (
    !val &&
    (row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible)
  ) {
    row.annotationsVisible =
      row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible;
  }
};
const changeCurrentInfo = row => {
  if (row) {
    selectState.selectedRowKeys = [row.sequenceNbr];
    stableRef.value.scrollTo(
      {
        rowKey: row.sequenceNbr,
      },
      'auto'
    );
    currentInfo.value = row;
  }
};

defineExpose({
  copyAndPaste,
  posRow,
  getTableData: queryBranchDataById,
  isDefault,
  selectState,
  handlerColumns,
  updateColumns,
  getDefaultColumns,
});
</script>

<style lang="scss" scoped>
@import './s-table.scss';
.association-selected {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
}
.code-flag {
  color: #a73d3d;
}
.code-black {
  color: #2a2a2a;
}
.flag-green {
  background: #90c942;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-orange {
  background: #ffaa09;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-red {
  background: #de3f3f;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}

.multiple-select {
  width: 35px;
  height: 16px;
  line-height: 16px;
  margin-left: -10px;
  //text-indent: 10px;
  cursor: pointer;
}
.subItem-project-gs {
  background: #ffffff;
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    // height: 40px;
    padding: 0 10px;
  }
  .table-content {
    height: 100%;
    //user-select: none;
    ::v-deep(.surely-table) {
      font-size: var(--project-detail-table-font-size);
      ::-webkit-scrollbar-thumb {
        background-color: rgba(24, 144, 255, 0.2);
        background-clip: padding-box;
        min-height: 28px;
        border-radius: 5px;
      }
      :hover::-webkit-scrollbar-thumb {
        background-color: rgba(24, 144, 255, 0.8);
        background-clip: padding-box;
        min-height: 28px;
        border-radius: 5px;
      }
    }
    .s-table {
      min-height: 300px;
      ::v-deep(.ant-spin-nested-loading) {
        min-height: 300px;
        height: 100%;
      }
      ::v-deep(.ant-spin-container) {
        min-height: 300px;
        height: 100%;
      }
    }
    ::v-deep(.surely-table-cell-content) {
      padding: 4px 4px;
    }
    ::v-deep(.deCode-center .surely-table-header-cell-title-inner) {
      text-align: center !important;
    }
    ::v-deep(.surely-table-body) {
      .vxe-icon-caret-right:before {
        position: relative;
        top: 1px;
        content: '+';
      }
      .rotate90 {
        transform: rotate(180deg) !important;
      }
      .rotate90:before {
        content: '-';
      }
      .ant-popover-inner-content {
        padding: 0;
      }
    }
    ::v-deep(.surely-table-body-cell) {
      overflow: visible !important;
    }
    ::v-deep(.surely-table-body .row-unit) {
      background: #e6dbeb;
    }
    ::v-deep(.surely-table-body .row-sub) {
      background: #efe9f2;
    }
    ::v-deep(.surely-table-body .row-qd) {
      background: #dce6fa;
    }
    ::v-deep(.vxe-body--row.row--current) {
      background: #a6c3fa;
    }
    ::v-deep(.surely-table-body .code-color) {
      color: #a73d3d;
      padding-left: 10px;
    }
    ::v-deep(.surely-table-body .index-bg) {
      background-color: #ffffff;
    }
    ::v-deep(.surely-table-cell-edit-wrapper .ant-select) {
      width: 100%;
    }
    ::v-deep(
      .surely-table-body--render-default.is--tree-line
        .vxe-body--row
        .vxe-body--column
    ) {
      background-image:
        linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(
      .surely-table-body--render-default.is--tree-line .vxe-header--column
    ) {
      background-image:
        linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(.surely-table-body .temp-delete) {
      background: #f3f2f3;
      color: #a7a7a7;
      .surely-table-cell-content > *:not(i) {
        text-decoration: line-through;
      }
    }
    ::v-deep(.surely-table-row.surely-table-row-selected) {
      background-color: var(--surely-table-row-selected-bg);
    }
    ::v-deep(
      .surely-table-row.surely-table-row-selected.surely-table-row-hover
    ) {
      background: var(--surely-table-row-selected-hover-bg);
    }
    ::v-deep(.surely-table-cell-edit-inner) {
      // padding: 16px 0;
    }
    ::v-deep(.ant-select) {
      font-size: 12px;
    }
  }

  .project-attr {
    white-space: pre-wrap;
    text-align: left;
  }
}
.content {
  margin-bottom: 20px;
}
.edit-content {
  margin-bottom: 20px;
}
.my-dropdown4 {
  width: 600px;
  height: 200px;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}
.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.code {
  margin-left: 8px;
}
.vxe-icon-minus,
.vxe-icon-add {
  color: #87b2f2;
  font-size: 9px;
  position: relative;
  left: -8px;
  top: -1px;
  border: 1px solid #87b2f2;
  border-radius: 50%;
}
.btns {
  position: absolute;
  width: 200px;
  bottom: 10px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.note-tips-yss {
  position: relative;
  &::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(167, 61, 61, 1);
    top: -7px;
    right: -10px;
    z-index: 8;
    cursor: pointer;
    transform: rotate(44deg);
    position: absolute;
  }
}
</style>
