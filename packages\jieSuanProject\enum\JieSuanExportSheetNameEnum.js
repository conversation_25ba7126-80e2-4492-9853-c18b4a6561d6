const JieSuanExportSheetNameEnum = Object.freeze(Object.fromEntries([



        //htFlag  1 合同内   0 合同外
        //jsfs  1 一般计税
        ['常用报表',[
            {
                headLine:"表1-1 建设项目费用汇总表",
                projectLevel:"project",
                htFlag:1
            },
            {
                headLine:"表1-2 建设项目费用汇总表(沧州)",
                projectLevel:"project",
                city:"cz",
                htFlag:1
            },
            {
                headLine:"表1-3 工程项目竣工结算汇总表",
                projectLevel:"project",
                htFlag:1
            },
            {
                headLine:"表1-4 工程项目竣工结算汇总表(沧州)",
                projectLevel:"project",
                city:"cz",
                htFlag:1
            },
            {
                headLine:"表1-1 单项工程竣工结算汇总表",
                projectLevel:"single",
                htFlag:1
            },
            {
                headLine:"表1-1 单位工程竣工结算汇总表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-2 分部分项合同清单工程量及结算工程量对比表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-3 分部分项工程和单价措施项目清单与计价表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-4 总价措施项目清单与计价表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-5 其他项目清单与计价汇总表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-6 材料暂估单价及调整表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-7 计日工表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-8 单位工程人材机汇总表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-9 工程议价材料表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-10 人材机调整明细表-1",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-11 人材机调整明细表-2",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-12 主材汇总表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-13 甲方供应材料表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-14 甲供材料汇总表",
                projectLevel:"unit",
                htFlag:1
            },
            {
                headLine:"表1-1 单位工程人材机汇总表",
                projectLevel:"unit",
                htFlag:0
            },
            {
                headLine:"表1-2 单位工程人材机价差汇总表",
                projectLevel:"unit",
                htFlag:0
            },
            {
                headLine:"表1-3 人材机价差调整表",
                projectLevel:"unit",
                htFlag:0
            },
            {
                headLine:"表1-4 主材汇总表",
                projectLevel:"unit",
                htFlag:0
            },
        ]],

    ['规范报表',[

        {
            headLine:"表1-1 总说明",
            projectLevel:"project",
            htFlag:1
        },
        {
            headLine:"表1-2 建设项目竣工结算汇总表",
            projectLevel:"project",
            htFlag:1
        },
        {
            headLine:"表-1-3 建设项目竣工结算汇总表(沧州)",
            projectLevel:"project",
            city:"cz",
            htFlag:1
        },
        {
            headLine:"扉1-1 竣工结算总价扉页",
            projectLevel:"project",
            htFlag:1,
        },
        {
            headLine:"封1-1 竣工结算书封面",
            projectLevel:"project",
            htFlag:1
        },
        {
            headLine:"表1-1 单项工程竣工结算汇总表",
            projectLevel:"single",
            htFlag:1
        },
        {
            headLine:"表1-1 合同外单项工程竣工结算汇总表",
            projectLevel:"single",
            htFlag:0
        },
        {
            headLine:"表1-1 单位工程竣工结算汇总表（含价差）",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-2 分部分项工程和单价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-3 综合单价分析表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-4 总价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-5 其他项目清单与计价汇总表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-6 材料(工程设备)暂估价及调整表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-7 专业工程结算价表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-8 计日工表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-9 总承包服务费计价表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-10 规费、税金项目结算表（不含价差）",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-11 规费、税金项目结算表（含价差）",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-12 发包人提供材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-13 承包人提供主要材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-14 承包人提供主要材料和工程设备一览表",
            projectLevel:"unit",
            htFlag:1
        },
        {
            headLine:"表1-15 材料、机械、设备增值税计算表",
            projectLevel:"unit",
            htFlag:1,
            jsfs:1,
        },
        {
            headLine:"表1-16 增值税进项税额计算汇总表",
            projectLevel:"unit",
            htFlag:1,
            jsfs:1,  //如果是简易计税  需要过滤掉
        },

        //合同外
        {
            headLine:"表1-1 单位工程费用汇总表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-2 分部分项工程和单价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-3 综合单价分析表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-4 总价措施项目清单与计价表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-5 综合单价调整表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-6 其他项目清单与计价汇总表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-7 暂列金额表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-8 专业工程结算价表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-9 计日工表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-10 总承包服务费计价表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-11 规费、税金项目清单与计价表",
            projectLevel:"unit",
            htFlag:0
        },
        {
            headLine:"表1-12 材料、机械、设备增值税计算表",
            projectLevel:"unit",
            htFlag:0,
            jsfs:1,  //如果是简易计税  需要过滤掉
        },
        {
            headLine:"表1-13 增值税进项税额计算汇总表",
            projectLevel:"unit",
            htFlag:0,
            jsfs:1,  //如果是简易计税  需要过滤掉
        },



    ]],
]));

module.exports = JieSuanExportSheetNameEnum;
