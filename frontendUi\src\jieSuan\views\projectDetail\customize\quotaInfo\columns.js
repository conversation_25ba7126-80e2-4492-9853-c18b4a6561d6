import { ref } from 'vue'

/**
 * 合同内人材机明细
 */
export const originalMaterialTableColumns = ref([
  {
    treeNode: true,
    title:"编码",
    field:"materialCode",
    width:"120",
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' }
  },
  {
    title: '类别',
    field: 'type',
    editRender: {  },
    width:"100",
    slot: true,
  },
  {
    title: '名称',
    field: 'materialName',
    editRender: { autofocus: '.vxe-input--inner' },
    width:"100",
    slot: true,
  },
  {
    title: '规格型号',
    field: 'specification',
    editRender: { autofocus: '.vxe-input--inner' },
    width:"100",
    slot: true,
  },
  {
    title: '单位',
    field: 'unit',
    editRender: {  },
    width:"70",
    slot: true,
  },
  {
    title: '消耗量',
    field: 'resQty',
    editRender: { autofocus: '.vxe-input--inner' },
    width:"90",
    slot: true,
  },
  {
    title: '合同合计数量',
    field: 'jieSuanTotalNumber',
    width:"100",
  },
  {
    title: '结算合计数量',
    field: 'totalNumber',
    width:"100",
  },
  {
    title: '预算价',
    field: 'dePrice',
    width:"70",
  },
  {
    title: '合同/确认单价',
    field: 'marketPrice',
    editRender: {  },
    width:"120",
    slot: true,
  },
  {
    title: '合同合价',
    field: 'jieSuanTotal',
    width:"70",
  },
  {
    title: '结算合价',
    field: 'total',
    width:"70",
  },
])
/**
 * 合同外人材机明细
 */
export const materialTableColumns = ref([
  {
    treeNode: true,
    title:"编码",
    field:"materialCode",
    width:"120",
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' }
  },
  {
    title: '类别',
    field: 'type',
    editRender: {  },
    width:"100",
    slot: true,
  },
  {
    title: '名称',
    field: 'materialName',
    editRender: { autofocus: '.vxe-input--inner' },
    width:"100",
    slot: true,
  },
  {
    title: '规格型号',
    field: 'specification',
    editRender: { autofocus: '.vxe-input--inner' },
    width:"100",
    slot: true,
  },
  {
    title: '单位',
    field: 'unit',
    editRender: {  },
    width:"70",
    slot: true,
  },
  {
    title: '消耗量',
    field: 'resQty',
    editRender: { autofocus: '.vxe-input--inner' },
    width:"90",
    slot: true,
  },
  // {
  //   title: '合同合计数量',
  //   field: 'totalNumber',
  //   width:"100",
  // },
  {
    title: '合计数量',
    field: 'totalNumber',
    width:"100",
  },
  {
    title: '定额价',
    field: 'dePrice',
    width:"100",
  },
  {
    title: '合同/确认单价',
    field: 'marketPrice',
    editRender: {  },
    width:"120",
    slot: true,
  },
  {
    title: '结算合价',
    field: 'total',
    width:"70",
  },
])
