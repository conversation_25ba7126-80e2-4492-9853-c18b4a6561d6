<!--
 * @Author: wangru
 * @Date: 2023-05-29 14:51:25
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-12 19:20:33
-->
<template>
  <div class="policyTable">
    <p class="title"><span class="text">政策文件</span></p>
    <div class="doc-content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, isCurrent: true }"
        :edit-config="{ trigger: 'click', mode: 'cell' }"
        :tooltip-config="{
          showAll: true,
          enterable: true,
          contentMethod: null,
        }"
        :tree-config="{
          transform: true,
          rowField: 'id',
          parentField: 'parentId',
          line: true,
          iconOpen: 'vxe-icon-square-minus-fill',
          iconClose: 'vxe-icon-square-plus-fill',
        }"
        :data="tableData"
        height="auto"
        :cell-class-name="cellClassName"
        keep-source
        ref="policyTable"
      >
        <vxe-column
          field=""
          width="50"
          title=""
          fixed="left"
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          min-width="700"
          title="说明"
          tree-node
          align="left"
        >
          <template #default="{ row }">
            <span
              v-if="!row.parentId && row.name === '人工费文件'"
              class="grandFatherFilePeople title"
            >
              <img
                :src="peopleFile.icon"
                :alt="peopleFile.title"
              />
              <span>{{ row.name }}</span>
            </span>
            <span
              v-if="row.parentId && row.parentId === '人工费文件'"
              class="grandFatherFilePeople"
            >
              <vxe-select
                v-model="rgSelected"
                placeholder=""
                style="width: 150px"
                @change="getTableData($event)"
                placement="bottom"
              >
                <vxe-option
                  v-for="item in showRGList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                ></vxe-option>
              </vxe-select>
            </span>

            <span
              class="grandFatherFileSafe title"
              v-else-if="!row.parentId && row.name === '安防费率文件'"
            >
              <img
                :src="safeFile.icon"
                :alt="safeFile.title"
              />
              <span>{{ row.name }}</span>
            </span>
            <span
              class="grandFatherFileGui title"
              v-else-if="!row.parentId && row.name === '规费文件'"
            >
              <img
                :src="guiFile.icon"
                :alt="guiFile.title"
              />
              <span>{{ row.name }}</span>
            </span>
            <span v-else-if="row.parentId">
              <span>{{ row.name }}</span>
            </span>
          </template></vxe-column>
        <vxe-column
          field="sketch"
          title="简要说明"
          min-width="100"
        ></vxe-column>
        <vxe-column
          field="releaseDate"
          title="发布日期"
          min-width="100"
        ></vxe-column>
        <vxe-column
          field="executeDate"
          title="执行日期"
          min-width="100"
        ></vxe-column>
        <vxe-column
          field="checked"
          min-width="80"
          title="执行"
          :cell-render="{}"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.selectNo"
              name="人工费文件"
              @change="CheckboxChange(row, row.sequenceNbr)"
              v-if="row.grandFather && row.grandFather === '人工费文件'"
            ></vxe-checkbox>
            <vxe-checkbox
              v-model="row.selectNo"
              :name="row.parentId"
              @change="CheckboxRGChange(row, row.sequenceNbr, row.parentId)"
              v-if="
                row.parentId &&
                row.parentId !== '人工费文件' &&
                !row.grandFather
              "
              :disabled="isDisabledGF"
            ></vxe-checkbox>
          </template>
        </vxe-column>
        <vxe-column
          field="fileUrl"
          min-width="80"
          title="内容文件"
          fixed="right"
          :cell-render="{}"
        >
          <template #default="{ row }">
            <span
              class="fileUrlStyle"
              @click="isOnline(row)"
              v-if="row.fileUrl"
            >
              查看文件
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="remark"
          title="备注"
          min-width="80"
          fixed="right"
        ></vxe-column>
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </div>
    <common-modal
      v-model:modelValue="fileModel"
      width="80vw"
      className="dialog-comm noMask"
      :height="modelHeight"
      :title="fileInfo && fileInfo.name"
      :show-zoom="true"
    >
      <iframe
        :src="fileInfo.fileUrl"
        width="100%"
        height="98%"
        v-if="fileInfo.fileUrl"
      ></iframe>
    </common-modal>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  defineEmits,
  getCurrentInstance,
} from 'vue';
import { getUrl } from '@/utils/index';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { message, Modal } from 'ant-design-vue';
import { useWindowSize } from '@vueuse/core';
import { checkisOnline } from '@/utils/publicInterface';
import infoMode from '@/plugins/infoMode.js';
const emit = defineEmits(['isConfirm', 'getpolicyData', 'isUpdateDoc']);
const store = projectDetailStore();
let tableData = ref([]);
const policyTable = ref();
let docList = ref([]);
let isDisabledSafe = ref(false); //安防费复选框是否禁用
let isDisabledGF = ref(false); //规费复选框是否禁用
let selectFileList = ref([]);
let firstSelectList = ref([]); //最开始获取到的数据里面选中的文件
let fileModel = ref(false);
let fileInfo = ref(null);
let showRGList = ref([]); //人工费文件下拉列表
let noRGlist = ref([]);
let rgSelected = ref(); //人工费最初展示的市
let currentRow = ref(null);
let selectListNo = ref([]); //选中的数据
const { height } = useWindowSize();
let modelHeight = ref();
const peopleFile = ref({
  icon: getUrl('detailImg/rengongfei.png'),
  isRadio: '',
  title: '人工费文件',
});
const safeFile = ref({
  icon: getUrl('detailImg/anwenfeiIcon.png'),
  isRadio: '',
  title: '安防费率文件',
});
const guiFile = ref({
  icon: getUrl('detailImg/guifeiIcon.png'),
  isRadio: '',
  title: '规费文件',
});
const CheckboxChange = (row, seNbr) => {
  // if (row.selectNo) {
  //   tableData.value.map(item => {
  //     //如果不是取消选中，选择别的复选框将人工费下所有文件的复选框设置为未选中只有当前复选框设置为选中状态
  //     if (item.sequenceNbr !== seNbr && item.grandFather === '人工费文件') {
  //       item.selectNo = false;
  //     }
  //   });
  //   savePolicyFile();
  // } else {
  //   //如果将人工费当前选中文件取消选中-----需要将人工费下选中清空并取消请求
  //   // tableData.value.map(item => {
  //   //   if (item.grandFather === '人工费文件') {
  //   //     item.selectNo = false;
  //   //   }
  //   // });
  //   // savePolicyFile();
  //   //三期改为不可取消勾选，只可以切换选中
  //   row.selectNo = true;
  // }
  //预算迭代六----设置为勾选只能勾选一项，不勾选也是可以
  setSelectRGF(docList.value, row);
  setSelectRGF(tableData.value, row);
  savePolicyFile();
};
const setSelectRGF = (tar, row) => {
  tar.map(item => {
    if (
      item.grandFather === '人工费文件' &&
      item.sequenceNbr !== row.sequenceNbr
    ) {
      item.selectNo = false;
    } else if (
      item.grandFather === '人工费文件' &&
      item.sequenceNbr === row.sequenceNbr
    ) {
      item.selectNo = row.selectNo;
    }
  });
};
const CheckboxRGChange = (row, seNbr, checkBoxName) => {
  //安防费文件必须有一项选中
  const list = tableData.value.filter(
    item => item.parentId === checkBoxName && item.selectNo
  );
  let isSave = true; //判断目前选中的文件是否是最初加载时被选中的，是的话不进行保存
  firstSelectList.value.map(item => {
    if (item.parentId === checkBoxName) {
      isSave = item.sequenceNbr === seNbr ? false : true;
    }
  });
  if (list.length > 0 && row.selectNo && isSave) {
    tableData.value.map(item => {
      if (
        item.sequenceNbr !== seNbr &&
        item.parentId &&
        item.parentId === checkBoxName
      ) {
        item.selectNo = false;
      }
    });
    savePolicyFile();
  } else if (list.length === 0) {
    //安防费和规费如果只选中一个的情况下取消选中是不可以的
    tableData.value.map(item => {
      if (item.sequenceNbr === seNbr) {
        item.selectNo = true;
      }
    });
  }
};

const getFeePolicyDocData = () => {
  let apiData = {
    type: store.currentTreeInfo.levelType === 1 ? 1 : 2,
    constructId:
      store.currentTreeInfo.levelType === 1
        ? store.currentTreeInfo?.id
        : store.currentTreeGroupInfo?.constructId,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  selectListNo.value = [];
  feePro.getPolicyDocument(apiData).then(res => {
    console.log('政策文件', apiData, res);
    if (res.status === 200) {
      selectListNo.value = [
        Number(res.result.rgfId),
        Number(res.result.awfId),
        Number(res.result.gfId),
      ];
      getFinallyData(res.result && res.result.basePolicyDocumentMap);
    } else {
      tableData.value = [];
    }
  });
};
const getList = (data, parId) => {
  let keyList = [];
  let valueList = [];
  let list = [];
  for (let key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      if (key === '人工费文件') {
        docList.value.push({ id: key, parentId: null, name: key });
        getList(data['人工费文件'], key);
      } else {
        if (parId) {
          keyList.push({
            id: key,
            parentId: parId,
            name: key,
          });
          data &&
            data[key] &&
            data[key].map((item, index) => {
              item.parentId = item.cityName;
              item.id = `${key}${index}`;
              item.grandFather = '人工费文件';
            });
        } else {
          keyList.push({ id: key, parentId: null, name: key });
          data &&
            data[key] &&
            data[key].map((item, index) => {
              item.parentId = key;
              item.id = `${key}${index}`;
            });
        }
        valueList.push(...data[key]);
        list.push(...keyList, ...valueList);
      }
    }
  }
  docList.value.push(...list);
};
//获取筛选项之后的tableData
const getTableData = (eve = null) => {
  let city = eve?.value;
  console.log('--------', city);
  let rgList = [];
  if (eve) {
    rgList = docList.value.filter(
      item => item.cityName === city || item.id === city
    );
  } else {
    let rgSelect = docList.value.find(
      item => item.selectNo && item.grandFather === '人工费文件'
    );
    if (!rgSelect) {
      rgSelect = docList.value.find(
        item =>
          item.cityName === '石家庄市' && item.grandFather === '人工费文件'
      );
    }
    rgSelected.value = rgSelect && rgSelect.cityName;
    rgList = docList.value.filter(
      item =>
        item.cityName === rgSelect.cityName || item.id === rgSelect.cityName
    );
  }
  tableData.value = [...rgList, ...noRGlist.value];
  renderData();
};
//渲染表格数据
const renderData = () => {
  setTimeout(() => {
    const $table = policyTable.value;
    $table.loadData(tableData.value);
    $table.setTreeExpand(tableData.value, true);
  }, 0);
  setTimeout(() => {
    if (currentRow.value) {
      console.log('currentRow.value', currentRow.value);
      let select = tableData.value.find(
        item => item.sequenceNbr === currentRow.value.sequenceNbr
      );
      policyTable.value.setCurrentRow(select);
      policyTable.value.scrollToRow(currentRow.value);
    } else {
      console.log('firstSelectList.value[0]', firstSelectList.value[0]);
      policyTable.value.setCurrentRow(firstSelectList.value[0]);
      policyTable.value.scrollToRow(firstSelectList.value[0]);
    }
  }, 100);
};
//政策文件处理
const getFinallyData = data => {
  docList.value = [];
  getList(data, null);

  let newobj = {};
  docList.value = docList.value.reduce((preVal, curVal) => {
    newobj[curVal.id] ? '' : (newobj[curVal.id] = preVal.push(curVal));
    return preVal;
  }, []);
  docList.value.map((item, index) => {
    item.sort = index + 1; //每条数据加序号
    if (selectListNo.value.includes(Number(item.sequenceNbr))) {
      item.selectNo = true;
    } else {
      item.selectNo = false;
    }
  });
  getIsDisabled(); //设置安防费文件和规费文件禁止取消
  // tableData.value = docList.value;
  showRGList.value = docList.value.filter(
    item => item.parentId === '人工费文件'
  );
  noRGlist.value = docList.value.filter(
    item => item.parentId !== '人工费文件' && item.grandFather !== '人工费文件'
  );
  firstSelectList.value = docList.value.filter(item => item.selectNo);
  currentRow.value = firstSelectList.value[0];
  getTableData();
  console.log('tableData.value', tableData.value);
};

const getIsDisabled = () => {
  const list = docList.value.filter(item => item.parentId === '安防费率文件');
  const GfList = docList.value.filter(item => item.parentId === '规费文件');
  isDisabledSafe.value =
    list.length === 1 && list[0].selectNo
      ? true
      : list.length === 1 && !list[0].selectNo
      ? false
      : false; //如果安防费下的文件只有一个就禁止取消安防费文件
  isDisabledGF.value =
    GfList.length === 1 && GfList[0].selectNo
      ? true
      : GfList.length === 1 && !GfList[0].selectNo
      ? false
      : false; //如果规费文件下的文件只有一个就禁止取消规费文件
};
const savePolicyFile = () => {
  selectFileList.value = tableData.value.filter(item => item.selectNo);
  const rgf =
    selectFileList.value &&
    selectFileList.value.filter(item => item.grandFather === '人工费文件');
  const awf =
    selectFileList.value &&
    selectFileList.value.filter(item => item.parentId === '安防费率文件');
  const gf =
    selectFileList.value &&
    selectFileList.value.filter(item => item.parentId === '规费文件');
  let apiData = {
    type: store.currentTreeInfo.levelType === 1 ? 1 : 2,
    constructId:
      store.currentTreeInfo.levelType === 1
        ? store.currentTreeInfo?.id
        : store.currentTreeGroupInfo?.constructId,
    rgfId: rgf && rgf[0] ? rgf[0].sequenceNbr : '', //所选人工费
    awfId: awf && awf[0] ? awf[0].sequenceNbr : '', //所选安文费
    gfId: gf && gf[0] ? gf[0].sequenceNbr : '', //所选规费
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  currentRow.value = rgf[0];
  if (store.currentTreeInfo.levelType === 3) {
    infoMode.show({
      iconType: 'icon-querenshanchu',
      infoText: '修改后将影响当前单位工程下人工费计取，是否确认修改',
      // descText: '建议清空以防相关费用重复计取',
      // descTextStyle,
      confirm: () => {
        infoMode.hide();
        saveData(apiData);
      },
      close: () => {
        infoMode.hide();
        tableData.value.map(item => {
          if (
            item.grandFather === '人工费文件' &&
            item.sequenceNbr === firstSelectList.value[0].sequenceNbr
          ) {
            item.selectNo = true;
          } else if (
            item.grandFather === '人工费文件' &&
            item.sequenceNbr !== firstSelectList.value[0].sequenceNbr
          ) {
            item.selectNo = false;
          }
        });
        policyTable.value.setCurrentRow(firstSelectList.value[0]);
      },
    });
    // Modal.confirm({
    //   title: '修改后将影响当前单位工程下人工费记取，是否确认修改',
    //   zIndex: '99999',
    //   okText: '确认修改',
    //   onOk() {
    //     saveData(apiData);
    //   },
    //   onCancel() {
    //     //单位工程更改取消要定位到上一次选中的数据上
    //     tableData.value.map(item => {
    //       if (
    //         item.grandFather === '人工费文件' &&
    //         item.sequenceNbr === firstSelectList.value[0].sequenceNbr
    //       ) {
    //         item.selectNo = true;
    //       } else if (
    //         item.grandFather === '人工费文件' &&
    //         item.sequenceNbr !== firstSelectList.value[0].sequenceNbr
    //       ) {
    //         item.selectNo = false;
    //       }
    //     });
    //     policyTable.value.setCurrentRow(firstSelectList.value[0]);
    //   },
    // });
  } else {
    emit('getpolicyData', apiData);
    let selectFGF = docList.value.find(
      i => i.selectNo && i.grandFather === '人工费文件'
    );
    if (
      (selectListNo.value[0] && !selectFGF) ||
      (!selectListNo.value[0] && selectFGF)
    ) {
      emit('isConfirm', false); //修改费率总览统一应用按钮可点击
      emit('isUpdateDoc', true);
    } else if (selectListNo.value[0] && selectFGF) {
      emit(
        'isConfirm',
        Number(selectFGF.sequenceNbr) === selectListNo.value[0]
      ); //修改费率总览统一应用按钮可点击
      emit(
        'isUpdateDoc',
        Number(selectFGF.sequenceNbr) === selectListNo.value[0]
      );
    }
    // emit('isConfirm', false); //修改费率总览统一应用按钮可点击
  }
};
const saveData = apiData => {
  apiData = JSON.parse(JSON.stringify(apiData));
  console.log('保存saveData传参', apiData);
  feePro.checkPolicyDocument(apiData).then(res => {
    console.log('保存saveData返回参数', apiData, res);
    if (res.status === 200) {
      getFeePolicyDocData();
    }
  });
};
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  ([val, oldVal], [newY, oldy]) => {
    if (
      store.tabSelectName === '取费表' &&
      store.currentTreeInfo.levelType !== 2
    ) {
      getFeePolicyDocData();
    }
  }
);
//单元格样式
const cellClassName = ({ row, column }) => {
  if (column.field === 'fileUrl') {
    return 'fileUrlStyle';
  }
};
onMounted(() => {
  if (store.tabSelectName === '取费表') {
    getFeePolicyDocData();
    getHeight();
  }
});
const isOnline = async info => {
  // 判断查看文件的时候有没有网;
  const hasOnline = await checkisOnline(true);
  if (hasOnline) {
    fileInfo.value = info;
    fileModel.value = true;
  }
};
const getHeight = () => {
  modelHeight.value = height.value * 0.85;
};
watch(
  () => height.value, //监听窗口大小自适应iframe大小
  () => {
    // iframe自适应高度
    if (
      store.tabSelectName === '取费表' &&
      store.currentTreeInfo.levelType === 1
    ) {
      getHeight();
    }
  }
);
defineExpose({
  saveData,
  getFeePolicyDocData,
});
</script>
<style lang="scss" scoped>
.policyTable {
  min-width: 80%;
  height: 100%;
  max-width: 100%;
  // min-width: 80%;
  .title {
    width: 100%;
    background-color: #e7e7e7;
    height: 35px;
    text-align: left;
    margin: 2px 0 2px;
    .text {
      display: inline-block;
      width: 128px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: #f8fbff;
      border-top: 2px solid #4786ff;
    }
  }

  .doc-content {
    height: calc(100% - 45px);
    // width: 100%;
    max-width: 1300px;
  }
  .model {
    position: relative;
    .closeBtn {
      position: absolute;
      right: 19px;
      top: 12px;
      font-size: 17px;
      cursor: pointer;
      background: white;
      z-index: 10;
    }
  }
  .content :deep(.ant-tree) {
    padding-top: 20px;
    height: 100%;
  }
  ::v-deep(.vxe-table) {
    .vxe-body--column {
      // text-align: left !important;
      // text-indent: 2rem;
    }
    .fileUrlStyle {
      color: #287cfa;
      text-decoration-line: underline;
      cursor: pointer;
    }
    .title {
      border-radius: 8px;
      color: #f8fbff;
      padding: 3px 5px;
      img {
        margin-right: 5px;
      }
    }
    .grandFatherFilePeople {
      // width: 130px;
      // height: 25px;
      background: #15b426;
    }
    .grandFatherFileSafe {
      background: #287cfa;
    }
    .grandFatherFileGui {
      background: #fa8928;
    }
    .vxe-select dl {
      top: auto;
      bottom: 42px;
    }
  }
}
</style>
