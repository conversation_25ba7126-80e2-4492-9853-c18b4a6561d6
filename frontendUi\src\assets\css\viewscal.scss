:root{
  --vxe-table-row-height-mini: 30px !important;
}
#pricing_body pre{
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
}
.vxe-header--column .vxe-cell{
  text-align: center;
}
.surely-table-header-cell-title span{
  text-align: center !important;
}
// @media (max-width: 1366px) {
//   :root{
//     --vxe-table-row-height-mini: 28px !important;
//   }
//   #pricing_body .ant-layout-content{
//     transform: scale(0.92);
//     transform-origin: 0 0;
//     width: 109vw;
//     height: 87.5vh;
//     .ant-spin-container>section{
//       height: calc(108vh - 143px);
//     }
//     .main-content {
//       display: flex;
//       height: calc(108vh - 185px);
//     }
//   }
//   // 
//   .xl{
    
//   }
// }