<!-- 组价方案 -->
<template>
  <div class="groupSchemeTable-warper">
    <vxe-table
      ref="groupTable"
      class="groupSchemeTable-table"
      keep-source
      border
      height="100%"
      :row-config="{ isCurrent: true }"
      :column-config="{ resizable: true }"
      :scroll-y="{ enabled: false }"
      :data="props.tableData"
      :cell-class-name="selectedClassName"
      @cell-click="changeScheme"
      @cell-dblclick="selectInfo"
    >
      <vxe-column width="60" title="序号">
        <template #default="{ row, $rowIndex }">
          {{ $rowIndex + 1 }}
        </template>
      </vxe-column>
      <vxe-column field="name" title="清单名称"></vxe-column>
      <vxe-column field="unit" title="单位"></vxe-column>
      <vxe-column field="projectAttr" title="项目特征" align="left">
        <template #default="{ row }">
          <div v-html="row.projectAttr"></div>
        </template>
      </vxe-column>
      <vxe-column field="source" title="指标来源">
        <template #default="{ row }">
          <div class="source">
            <p><span>地区：</span>{{ row.areaName }}</p>
            <p><span>编制时间：</span>{{ row.compileDate }}</p>
            <p><span>类型：</span>{{ row.fileType }}</p>
          </div>
        </template>
      </vxe-column>
    </vxe-table>
    <div class="scheme-list" ref="groupSchemeList">
      <div class="data-empty" v-if="!mergeSchemeList.length">
        <div class="data-tips">
          <img
            src="https://hzjt-ui-publiclib.oss-cn-beijing.aliyuncs.com/jijiasoft/zanwushuju.png"
            class="bg-img"
            alt=""
          />
          <span>未查询到对应数据价格</span>
        </div>
        <div class="empty-footer">
          <icon-font class="icon" type="icon-tishi" style="margin-right: 5px" />
          获取更多组价方案，您可进入
          <a
            href="https://www.yunsuanfang.com"
            class="small-tips"
            target="_blank"
            >https://www.yunsuanfang.com</a
          >
          查看最新的组价方案指标数据
        </div>
      </div>
      <div v-else class="groupSchemeList">
        <div
          class="scheme-item"
          v-for="(i, k) of mergeSchemeList"
          :key="k"
          @dblclick="handleDoubleClick(i, k)"
          :class="{ active: useIndex === k }"
        >
          <div class="item-name">组价方案{{ k + 1 }}</div>
          <vxe-table
            ref="groupItem"
            class="groupScheme-item"
            keep-source
            border
            :row-config="{ isCurrent: false, keyField: 'sequenceNbr' }"
            :column-config="{ resizable: true }"
            :scroll-y="{ enabled: false }"
            :data="i"
            min-height="45px"
          >
            <vxe-column width="60" title="序号">
              <template #default="{ row, $rowIndex }">
                {{ $rowIndex + 1 }}
              </template>
            </vxe-column>
            <vxe-column field="deCode" title="编码"></vxe-column>
            <vxe-column field="name" title="名称"></vxe-column>
            <vxe-column field="unit" title="单位"></vxe-column>
            <vxe-column field="price" title="单价"></vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import infoMode from '@/plugins/infoMode.js';
const props = defineProps([
  'tableData',
  'currentInfo',
  'type',
  'isSetStandard',
  'interfaceData',
]);
import { shallowRef, toRaw, nextTick, ref, watch } from 'vue';
import { useCellClick } from '@/hooks/useCellClick';
import csProject from '@/api/csProject.js';

const { selectedClassName } = useCellClick();

const emits = defineEmits([
  'updateData',
  'currentInfo',
  'type',
  'dbClickFile',
  'batchConversionRule',
]);
const leftInfo = ref(null);
const useIndex = ref(-1);
let dataList = shallowRef([]);
let mergeSchemeList = ref([]);
let groupTable = ref();
let groupSchemeList = ref();

let isRefresh = ref(true);

const selectRowEvent = () => {
  const $table = groupTable.value;
  if ($table) {
    $table.setCurrentRow(dataList.value[0]);
  }
};

const handleDoubleClick = (data, k) => {
  isRefresh.value = true;
  let schemeDeIdList = [];
  data.forEach(e => {
    schemeDeIdList.push(e.sequenceNbr);
  });
  useIndex.value = k;
  const postData = {
    ...props.interfaceData,
    schemeDeIdList,
  };
  csProject.qdMergePlanApply(postData).then(res => {
    if (res.status === 200 && res.result) {
      emits('updateData', 1);
    }
  });
};

// 左侧双击选中
const selectInfo = ({ row }) => {
  if (props.currentInfo.isLocked) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: '清单已锁定,请先解除锁定......',
      confirm: () => {
        infoMode.hide();
      },
    });
    return;
  }
  emits('dbClickFile', {
    type: 'groupSchemeTable',
    filed: 'projectAttr',
    value: row.projectAttr,
  });
  isRefresh.value = false;
};

// 左侧单机
const changeScheme = ({ row }) => {
  leftInfo.value = row;
  mergeSchemeList.value = row.mergeScheme || [];
  nextTick(() => {
    const listEl = groupSchemeList.value;
    listEl.scrollTop = 0;
  });
};

watch(
  () => props.tableData,
  (newVal, oldVal) => {
    if (isRefresh.value) {
      let List = toRaw(props.tableData);
      dataList.value = List;
      useIndex.value = -1;
      if (List.length) {
        mergeSchemeList.value = List[0]?.mergeScheme || [];
        nextTick(() => {
          selectRowEvent();
          const listEl = groupSchemeList.value;
          listEl.scrollTop = 0;
        });
      } else {
        mergeSchemeList.value = [];
      }
    }
  }
);

const editRefresh = (status = true) => {
  isRefresh.value = status;
};

defineExpose({
  editRefresh,
});
</script>

<style lang="scss" scoped>
.groupSchemeTable-warper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  .source {
    p {
      color: #333333;
      font-size: 12px;
      text-align: left;
      margin-bottom: 0;
      span {
        font-weight: bold;
      }
    }
  }
  .groupSchemeTable-table {
    width: 55%;
  }
  .scheme-list {
    width: 43%;
    overflow-y: auto;
    .data-empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .data-tips {
        position: relative;
        margin-bottom: 2%;
        img {
          display: block;
          width: 180px;
          margin: 0 auto;
        }
        span {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 100%;
          transform: translateX(-50%);
          font-size: 12px;
          color: #606060;
          text-align: center;
          z-index: 2;
        }
      }
      .empty-footer {
        font-size: 12px;
        font-weight: 400;
        color: #2a2a2a;
        opacity: 1;
        a {
          color: rgba(40, 124, 250, 1);
        }
      }
    }
  }

  .scheme-item {
    border: 1px solid #d9d9d9;
    margin-bottom: 5px;
    cursor: pointer;
    .groupScheme-item {
      user-select: none;
    }
    .item-name {
      font-size: 14px;
      font-weight: 400;
      line-height: 1.6;
      color: #287cfa;
      padding: 5px 9px;
    }
    &.active {
      border-color: #287cfa;
    }
  }
}
</style>
