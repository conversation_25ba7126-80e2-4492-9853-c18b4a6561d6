/*
 * @Descripttion: 表1-17 单价措施项目工程量清单综合单价分析表
 * @Author: sunchen
 * @Date: 2024-07-06 10:32:39
 * @LastEditors: sunchen
 * @LastEditTime: 2024-10-19 10:17:49
 */

export const CSXM17BookData = [
  {
    name: '表1-17 单价措施项目工程量清单综合单价分析表',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表', '其他'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        '4P098S': {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        Osy4pV: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        BSn91u: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        wOY_K5: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '0q5mkw': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        fvFMdO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        DLcQ5b: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        Zdr98l: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '7DbVzx': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        nY8ix3: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        QuEH_P: {
          bd: {
            b: null,
            l: null,
            r: null,
            t: null,
          },
          ff: null,
          fs: null,
          it: null,
          bl: null,
          ul: null,
          st: null,
          ol: null,
          tr: null,
          td: null,
          cl: null,
          bg: null,
          ht: null,
          vt: null,
          tb: null,
          pd: null,
          n: null,
        },
        S3v4Tb: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        IXwQSt: {
          bd: {},
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        aR5GeU: {
          bd: {},
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        sBtTsp: {
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        EcF0JR: {
          bd: {},
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        nH0f33: {
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          n: null,
        },
        '0dT8pn': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        iNSthQ: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        Jmydco: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        zbJBvj: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        '38jbKK': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        _lZegW: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        IqzZK6: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        lbBDP0: {
          ff: 'SimSun',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        qtfDKP: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        tWL2P1: {
          bd: {},
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        JrAzu9: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        '-C59Ah': {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        QgzVdd: {
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          n: null,
        },
        '3CpXlV': {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
          },
          n: null,
        },
        r6KxRa: {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
          n: null,
        },
        'ON-i6u': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
          n: null,
        },
        pjLIi4: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        YUENEX: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        lmieqK: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
          n: null,
        },
        '6rvyY5': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
          bg: null,
          n: null,
        },
        MC8Fow: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        Wrgfwh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        Qxm5rq: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '8i2Y_V': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '2NESHo': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        '4ivHuT': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        MHQlqn: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        wpSjq4: {
          ff: '微软雅黑 Light',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        Lvqb5z: {
          bd: {},
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        KDXit7: {
          ff: '等线',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        'MQ-RkU': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        'g-X6T8': {
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        RdTxl7: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        j1v21B: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        tNRCX9: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        ZRW1zm: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        xjqWK2: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        '-BTSxy': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        '9UzEcK': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        '8V5kI5': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        NUNoQ_: {
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        Vl13hH: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        '6UpGEP': {
          bd: {
            l: null,
          },
        },
        '3f1bc_': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        uQ18oA: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        a6Y8QQ: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        Gzv7Li: {
          bd: {},
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'Vl13hH',
                p: '',
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              1: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              2: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              3: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              4: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              5: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              6: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              7: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              8: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              9: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              10: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
              11: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageEyeBrow',
                  rowType: '页眉行',
                  parentName: null,
                },
              },
            },
            1: {
              0: {
                v: ' `单价措施项目工程量清单综合单价分析表`',
                t: 1,
                s: 'Gzv7Li',
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                },
                p: '',
              },
              1: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              2: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              3: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              4: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              5: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              6: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              7: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              8: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              9: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              10: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
              11: {
                s: 'NUNoQ_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'header',
                  rowType: '报表标题行',
                  parentName: null,
                },
              },
            },
            2: {
              0: {
                s: 'g-X6T8',
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                p: '',
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              1: {
                s: 'g-X6T8',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              2: {
                s: 'g-X6T8',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              3: {
                s: 'g-X6T8',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              4: {
                s: 'g-X6T8',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              5: {
                s: 'g-X6T8',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              6: {
                s: '3f1bc_',
                v: '`第`+{页码}+`页 `+` 共 `+{总页数}+`页`',
                t: 1,
                p: '',
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              7: {
                s: '3f1bc_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              8: {
                s: '3f1bc_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              9: {
                s: '3f1bc_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              10: {
                s: '3f1bc_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
              11: {
                s: '3f1bc_',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'sheetEyeBrow',
                  rowType: '表眉行',
                  parentName: null,
                },
              },
            },
            3: {
              0: {
                s: 'lmieqK',
                v: '`序号`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              1: {
                s: 'lmieqK',
                v: '`项目编码(定额编号)`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              2: {
                s: 'lmieqK',
                v: '`项目名称`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              3: {
                s: 'lmieqK',
                v: '`单位`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              4: {
                s: 'lmieqK',
                v: '`数量`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              5: {
                s: 'lmieqK',
                v: '`综合单价(元)`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              6: {
                s: 'lmieqK',
                v: '`合价(元)`',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
                p: '',
              },
              7: {
                s: 'lmieqK',
                v: '`综合单价组成(元)`',
                t: 1,
                p: '',
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              8: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              9: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              10: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              11: {
                s: 'lmieqK',
                v: '`人工单价(元/工日)`',
                t: 1,
                p: '',
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
            },
            4: {
              0: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              1: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              2: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              3: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              4: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              5: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              6: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              7: {
                s: 'lmieqK',
                v: '`人工费`',
                t: 1,
                p: '',
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              8: {
                s: 'lmieqK',
                v: '`材料费`',
                t: 1,
                p: '',
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              9: {
                s: 'lmieqK',
                v: '`机械费`',
                t: 1,
                p: '',
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              10: {
                s: 'lmieqK',
                v: '`管理费和利润`',
                t: 1,
                p: '',
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              11: {
                s: 'lmieqK',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'headLine',
                  rowType: '明细标题行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
            },
            5: {
              0: {
                s: 'lmieqK',
                v: '[记录号]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              1: {
                s: 'lmieqK',
                v: '[BM]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              2: {
                s: 'lmieqK',
                v: '[MC]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              3: {
                s: 'lmieqK',
                v: '[DW]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              4: {
                s: 'lmieqK',
                v: '[GCL]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              5: {
                s: 'lmieqK',
                v: '[ZHDJ]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              6: {
                s: 'lmieqK',
                v: '[ZHHJ]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              7: {
                s: 'lmieqK',
                v: '[RGHJ]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              8: {
                s: 'lmieqK',
                v: '[CLHJ]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              9: {
                s: 'lmieqK',
                v: '[JXHJ]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              10: {
                s: 'lmieqK',
                v: '[GLFHJ]+[LRHJ]',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
              11: {
                s: '6rvyY5',
                v: '',
                t: 1,
                p: '',
                custom: {
                  field: '',
                  rowType: '细节行',
                  dataSourceType: '单价措施清单',
                  parentName: null,
                },
              },
            },
            6: {
              0: {
                v: '单价措施清单.[记录号]+`.`+[记录号]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              1: {
                v: '[BM]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              2: {
                v: '[MC]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              4: {
                v: '[GCL]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              5: {
                v: '[ZHDJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              6: {
                v: '[ZHHJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              7: {
                v: '[RGHJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              8: {
                v: '[CLHJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              9: {
                v: '[JXHJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              10: {
                v: '[GLFHJ]+[LRHJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
              11: {
                v: '[GRDJ]',
                t: 1,
                s: 'lmieqK',
                p: '',
                custom: {
                  rowType: '细节行',
                  dataSourceType: '单价措施定额',
                  parentName: '单价措施清单',
                },
              },
            },
            7: {
              0: {
                v: '',
                t: 1,
                s: 'Vl13hH',
                p: '',
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              1: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              2: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              3: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              4: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              5: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              6: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              7: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              8: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              9: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              10: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
              11: {
                s: 'Vl13hH',
                v: '',
                p: '',
                t: 1,
                custom: {
                  field: 'pageFoot',
                  rowType: '页脚行',
                  parentName: null,
                },
              },
            },
            8: {
              0: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              1: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              2: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              3: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              4: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              5: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              6: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              7: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              8: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              9: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              10: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
              11: {
                s: '6UpGEP',
                v: '',
                p: '',
                t: 1,
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 8,
          columnCount: 12,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 0,
              endRow: 0,
              startColumn: 0,
              endColumn: 11,
            },
            {
              startRow: 1,
              endRow: 1,
              startColumn: 0,
              endColumn: 11,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 5,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 6,
              endColumn: 11,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 1,
              endColumn: 1,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 2,
              endColumn: 2,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 3,
              endColumn: 3,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 4,
              endColumn: 4,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 5,
              endColumn: 5,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 6,
              endColumn: 6,
            },
            {
              startRow: 3,
              endRow: 3,
              startColumn: 7,
              endColumn: 10,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 11,
              endColumn: 11,
            },
            {
              startRow: 7,
              endRow: 7,
              startColumn: 0,
              endColumn: 11,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
              parentName: null,
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ia: 0,
              parentName: null,
            },
            3: {
              h: 50,
              hd: 0,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '单价措施清单',
              parentName: null,
            },
            4: {
              h: 50,
              hd: 0,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '单价措施清单',
              parentName: null,
            },
            5: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              rowType: '细节行',
              dataSourceType: '单价措施清单',
              parentName: null,
            },
            6: {
              h: 30,
              hd: 0,
              parentName: '单价措施清单',
              rowType: '细节行',
              dataSourceType: '单价措施定额',
              ah: 30,
            },
            7: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
              ah: 30,
              ia: 0,
              parentName: null,
            },
          },
          columnData: {
            0: {
              w: 53,
              hd: 0,
            },
            1: {
              w: 121,
              hd: 0,
            },
            2: {
              w: 147,
              hd: 0,
            },
            3: {
              w: 52,
              hd: 0,
            },
            4: {
              w: 72,
              hd: 0,
            },
            5: {
              w: 80,
              hd: 0,
            },
            6: {
              w: 81,
              hd: 0,
            },
            7: {
              w: 60,
              hd: 0,
            },
            8: {
              w: 60,
              hd: 0,
            },
            9: {
              w: 60,
              hd: 0,
            },
            10: {
              w: 60,
              hd: 0,
            },
            11: {
              w: 52,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
      updateName: '',
    },
  },
];
