<template>
  <div class="table">
    <vxe-table
      align="center"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="decData.summary"
      height="auto"
      ref="upTable"
      border="full"
      keep-source
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      @cell-click="useCellClickEvent"
      class="table-edit-common"
    >
      <vxe-column field="dispNo" width="60" title="序号"> </vxe-column>
      <vxe-column
        field="name"
        min-width="220"
        title="名称"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
        <template #edit="{ row }">
          <cell-textarea
            v-if="row.whetherTax !== 1"
            :clearable="false"
            v-model.trim="row.name"
            @blur="clear(row)"
            placeholder="请输入名称"
            :textHeight="row.height"
            @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')"
          ></cell-textarea>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-colgroup title="标准模板">
        <vxe-column field="templateCode" min-width="90" title="费用代号">
        </vxe-column>
        <vxe-column
          field="templateCalculateFormula"
          min-width="150"
          title="计算基数"
        >
        </vxe-column>
        <vxe-column field="templateRate" width="120" title="费率（%）">
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="本工程审定模板">
        <vxe-column
          field="code"
          width="120"
          title="费用代号"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model.trim="row.code"
              type="text"
              @blur="clear(row)"
              @keyup="row.code = row.code.replace(/[^\w_]/g, '')"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="calculateFormula"
          min-width="150"
          title="计算基数"
          :edit-render="{ autofocus: '.vxe-textarea--inner' }"
        >
          <template #default="{ column, row, $columnIndex }">
            <icon-font
              type="icon-bianji"
              class="more-icon"
              v-if="
                isSelectedCell({
                  $columnIndex,
                  column,
                  row,
                })
              "
              @click.stop="editCalc(row)"
            ></icon-font>
            <span>{{ row.calculateFormula }}</span>
          </template>
          <template #edit="{ row, $columnIndex, column }">
            <cell-textarea
              :clearable="false"
              v-model.trim="row.calculateFormula"
              placeholder="请输入计算基数"
              :textHeight="row.height"
              @blur="clear(row)"
              @keyup="
                row.calculateFormula = row.calculateFormula.replace(
                  /[^\w\-\+\*\/]/g,
                  ''
                )
              "
            ></cell-textarea>
          </template>
        </vxe-column>

        <vxe-column
          field="rate"
          width="120"
          title="费率（%）"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span :style="row.templateRate != row.rate ? 'color: red;' : ''">{{
              row.rate
            }}</span>
          </template>
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model="row.rate"
              @blur="clear(row)"
              @keyup="row.rate = row.rate.replace(/[^\d.]/g, '')"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="instructions" min-width="200" title="基数说明">
        </vxe-column>
        <!-- <vxe-column field="费用类别" min-width="200" title="基数说明">
                </vxe-column> -->
      </vxe-colgroup>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModel = false"
    :mask="false"
    style="position: releative"
  >
    <content-down
      :isTextArea="isTextArea"
      :textValue="textValue"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button type="primary" @click="sureData()">确定</a-button>
    </span>
  </common-modal>
</template>
<script setup>
import { computed, ref, watch, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import { pureNumber } from '@/utils/index';
import { useCellClick } from '@/hooks/useCellClick';
import ContentDown from '@/views/projectDetail/customize/summaryExpense/ContentDown.vue';
import { summaryExpenseJs } from '@/views/projectDetail/customize/summaryExpense/comContent.js';
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
} = useCellClick();
const store = projectDetailStore();
const upTable = ref();
let comModel = ref(false); //计算基数编写弹框
let isTextArea = ref(true);
let textValue = ref('');
let oldValue = ref('');
const comArea = ref();
const { editCalc, cancelData } = summaryExpenseJs({
  pageType: 'modal',
  comModel,
  textValue,
  oldValue,
  comArea,
});
const props = defineProps({
  decData: {
    type: Object,
  },
  pushData: {
    type: Array,
  },
});
let taxMode = ref('');
const editClosedEvent = ({ row, column }) => {
  console.log('费用汇总修改', row);
  const $table = upTable.value;
  const field = column.field;
  let value = row[field];
  const reg = /[^\d\.]/g;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  // 判断单元格值是否被修改
  if ((field === 'name' || field === 'remark') && value?.length > 50) {
    console.log('field', field, 'value', value);
    row[field] = value.slice(0, 50);
    message.warn(`输入字符应50个字符范围内`);
    // return;
  }
  // let feeList = [...totalFee.value];
  // feeList.map(item => (item = item.toUpperCase()));
  // if (field === 'code' && feeList.includes(value.toUpperCase())) {
  //   let list = tableData.value.filter(item => item.code === value);
  //   list && list.length > 0
  //     ? message.warn(`当前费用代号已被使用`)
  //     : message.warn(`当前费用代号与费用代码重复，请修改`);
  //   $table.revertData(row, field);
  //   return;
  // }
  if (field === 'name' && value?.length === 0) {
    $table.revertData(row, field);
    message.warn(`输入名称不可为空!`);
    return;
  }
  if (field === 'calculateFormula' && value?.length === 0) {
    message.warn(`计算基数不可为空`);
    $table.revertData(row, field);
    return;
  }
  // if (field === 'calculateFormula' && !isHasCalculateFormula(value, row.code)) {
  //   message.warn(`计算基数输入不符合规格`);
  //   $table.revertData(row, field);
  //   return;
  // }
  if (field === 'rate' && value !== '' && reg.test(value)) {
    console.log('----------,不和規則');
    //不可以输入除数字和小数点之外的
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && value === '') {
    row[field] = '0';
  } else if ((field === 'rate' && Number(value) > 1000) || Number(value) < 0) {
    message.warn(`费率可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate') {
    if (
      (taxMode.value === 1 &&
        (row.type === '附加税费' || row.type === '销项税额')) ||
      (taxMode.value === 0 && row.type === '税金')
    ) {
      if ($table.isUpdateByRow(row, field)) {
        // rowValue.value = { ...row };
        Modal.confirm({
          title: '是否确认修改税率？',
          content: '修改税率将会同步关联取费表中计税设置的费率，是否确认修改?',
          okText: '确定',
          cancelText: '取消',
          onOk() {
            update(row, field);
          },
          onCancel() {},
        });

        return;
      }
    }
  }
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    clear(row);
  }
};
const sureData = () => {
  const value = comArea.value.value;
  if (!value) {
    //不可以输入空
    message.warn(`输入不可为空`);
    return;
  }
  textValue.value.calculateFormula = value;
  comModel.value = false;
  clear(textValue.value);
};
const clear = row => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    unitCostSummary: { ...row },
  };
  if (
    props.pushData.summary.find(
      a => a.unitCostSummary.sequenceNbr === row.sequenceNbr
    )
  ) {
    Object.keys(apiData).forEach(key => {
      props.pushData.summary.find(
        a => a.unitCostSummary.sequenceNbr === row.sequenceNbr
      )[key] = apiData[key];
    });
  } else {
    props.pushData.summary.push(apiData);
  }
  console.log(props.pushData, 'props.pushData');
  //清除编辑状态
  const $table = upTable.value;
  $table.clearEdit();
};
</script>
<style lang="scss" scoped>
.table {
  height: 60vh;
}
.btns {
  position: absolute;
  width: 200px;
  bottom: -20px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
</style>
