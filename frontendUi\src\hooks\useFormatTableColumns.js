/*
 * @Descripttion: 表格表头列显示隐藏处理
 * @Author: renmingming
 * @Date: 2024-03-04 14:49:02
 * @LastEditors: k<PERSON><PERSON><PERSON>ang
 * @LastEditTime: 2024-11-05 17:35:02
 * vxe-table需要添加trends-table-column类名
 * 添加:header-cell-class-name="setHeaderCellClassName"  在vxeGrid下不用添加header-cell-class-name
 */
import { ref, toRaw, watch, nextTick } from 'vue';
import api from '@/api/projectDetail.js';
import jsApi from '@/api/jiesuanApi.js';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';

const projectStore = projectDetailStore();
export const useFormatTableColumns = ({
  type = 'ys',
  vxeGrid = null,
  initCallback = null,
  initColumnsCallback = null,
} = {}) => {
  let handlerColumns = ref([]);
  let showColumns = ref([]);
  let columnsMap = {};
  let dynamicColumns = null;
  let orgColumns = [];
  let page = 'fbfx';
  /**
   * 接口获取默认数据
   * @param {boolean} Inception 0获取初始设置数据
   * @returns
   */
  const getDefaultColumns = async (Inception = '0') => {
    const { constructId, singleId } = projectStore?.currentTreeGroupInfo;
    const { id } = projectStore.currentTreeInfo;
    let apiFun = api.queryColumn
    if (projectStore.type === 'jieSuan') {
      apiFun = jsApi.jieSuanQueryColumn
    }
    const res = await apiFun({
      type,
      page,
      constructId,
      singleId,
      unitId: id,
      Inception,
      datas: toRaw(orgColumns),
    });
    // const res = {code: 200, result: orgColumns}
    if (res.code === 200 && res.result) {
      dynamicColumns = res.result;
      console.log('dynamicColumns', dynamicColumns);
      dynamicColumns.forEach(item => {
        columnsMap[item.field] = item;
        if (item?.children?.length > 0) {
          item.children.forEach(a => (columnsMap[a.field] = a));
        }
      });
      return res.result;
    }
    return [];
  };

  /**
   * 初始化
   * @param {*} param0
   */
  const initColumns = async ({ columns, pageName = 'fbfx' }) => {
    // //22定额不存在的字段
    const deType22Rule = ['gfPrice', 'gfTotal'];
    //12定额一般计税存在
    const deType12Rule = [
      'jxsePrice',
      'jxseTotal',
      'xxsePrice',
      'xxseTotal',
      'zzsynsePrice',
      'zzsynseTotal',
      'fjsePrice',
      'fjseTotal',
    ];
    //造价分析taxMode！==1时需要隐藏列
    const zjfxRule = ['jxse', 'xxse', 'zzsynse', 'fjse'];
    columns = columns.map(item => {
      item.visible = item.visible === undefined ? true : item.visible;
      if (item?.children?.length > 0) {
        item.children.map(
          b => (b.visible = b.visible === undefined ? true : b.visible)
        );
      }
      item.initialize = item.visible;
      return item;
    });
    if (projectStore.deStandardReleaseYear === '22') {
      columns = columns.filter(i => !deType22Rule.includes(i.field));
    }
    if (
      !(
        projectStore.deStandardReleaseYear === '12' &&
        Number(projectStore.taxMade) === 1
      )
    ) {
      columns = columns.filter(i => !deType12Rule.includes(i.field));
    }
    if (
      pageName === 'zjfx' &&
      projectStore.deStandardReleaseYear !== '12' &&
      Number(projectStore.taxMade) !== 1
    ) {
      columns = columns.filter(i => !zjfxRule.includes(i.field));
    }
    orgColumns = JSON.parse(JSON.stringify(columns));
    page = pageName;
    await getDefaultColumns();
    console.log('columns', columns);
    handlerColumns.value = columns
      .map((column, index) => {
        if (!column.width) {
          column.width = 160;
        }
        column.id = index;
        const defaultParams = { visible: true };
        const { visible, initialize, children } =
          columnsMap[column.field] || defaultParams;
        console.log('initialize', initialize);
        column.dataIndex = column.field;
        column.key = column.field;
        column.visible = visible === undefined ? true : visible;
        column.resizable = true;
        if (column.field != 'bdCode') column.align = column.align || 'center';
        column.initialize =
          initialize === undefined ? true : !!column.initialize;
        if (column?.children?.length > 0) {
          column.children.map(b => {
            const { visibleItem, initializeItem } =
              columnsMap[b.field] || defaultParams;
            b.visible = visibleItem === undefined ? true : visibleItem;
            column.initialize =
              initializeItem === undefined ? true : initializeItem;
          });
          column.children = children;
        }
        if (!column.editRender && !vxeGrid) {
          // 使用 v-for 去循环静态列是非常糟糕的，仅用于改变属性，需确保 key 唯一性（动态场景需使用 vxe-grid 进行渲染）
          // 为了解决使用v-for循环静态切换报editRender为null问题
          column.editRender = { notCanEdit: true };
        }
        return column;
      })
      .filter(item => columnsMap[item.field]);
    console.log('dynamicColumns', handlerColumns.value);
    showColumns.value = handlerColumns.value.filter(item => item.visible);
    // nextTick(() => {
    //   if (vxeGrid) vxeGrid.value?.loadColumn(handlerColumns.value);
    //   if (initCallback) initCallback();
    // });
  };
  /**
   * 隐藏不是编辑得列头icon
   * @param {*} param0
   * @returns
   */
  const setHeaderCellClassName = ({ column }) => {
    return column.editRender?.notCanEdit ? 'fix-not-can-edit' : '';
  };
  /**
   * 保存更新
   * @param {*} checkedList
   */
  const updateColumns = (checkedList = [], allList = []) => {
    if (allList.length > 0) {
      dynamicColumns = JSON.parse(JSON.stringify(allList));
    } else {
      dynamicColumns = dynamicColumns.map(item => {
        item.visible = checkedList.includes(item.field);
        return item;
      });
    }
    const { constructId, singleId } = projectStore.currentTreeGroupInfo;
    const { id } = projectStore.currentTreeInfo;
    console.log('queryColumn', {
      type,
      page,
      constructId,
      singleId: singleId,
      unitId: id,
      datas: dynamicColumns,
    });
    let apiFun = api.updateColumn
    if (projectStore.type === 'jieSuan') {
      apiFun = jsApi.jieSuanUpdateColumn
    }
    apiFun({
        type,
        page,
        constructId,
        singleId: singleId,
        unitId: id,
        datas: dynamicColumns,
      })
      .then(res => {
        console.log('queryColumn', res);
        if (res.code === 200) {
          initColumnsCallback();
          // initColumns({ columns: orgColumns, pageName: page });
          message.success('操作成功');
        }
        console.log('dynamicColumns', dynamicColumns, res);
      });
  };

  /**
   * 关闭更新
   * @param {*} param0
   */
  const closeColumn = ({ column, columns }) => {
    const checkedList = [];
    const list = handlerColumns.value.filter(item => item.visible)?.length;
    let visibleList = [];
    if (columns)
      visibleList = columns.children.filter(
        a => a.visible && a.field !== column.field
      );
    if (
      (list <= 6 && !columns) ||
      (list <= 6 && columns && visibleList.length === 0)
    ) {
      message.error('不可少于六列');
      return;
    }
    handlerColumns.value = handlerColumns.value.map(item => {
      if (item.visible && item.field === column.field) item.visible = false;
      if (page === 'zjfx') {
        //子级随父级
        if (
          ['fbfx', 'djcs', 'qtzjcs', 'qtxm'].includes(column.field) &&
          item.field === column.field
        )
          item.children.map(b => (b.visible = item.visible));
        if (columns && item.field === columns.field) {
          item.children.map(b => {
            if (b.field === column.field && b.visible) b.visible = false;
          });
          item.visible = item.children.some(c => c.visible);
        }
      }
      if (item.visible) checkedList.push(item.field);
      return item;
    });

    updateColumns(checkedList);
  };
  return {
    showColumns,
    closeColumn,
    handlerColumns,
    initColumns,
    updateColumns,
    getDefaultColumns,
    setHeaderCellClassName,
  };
};
