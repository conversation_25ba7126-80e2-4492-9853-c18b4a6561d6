<!--
 * @Descripttion: 分部分项
 * @Author: sunchen
 * @Date: 2024-02-21 11:10:03
 * @LastEditors: liuxia
 * @LastEditTime: 2024-09-12 15:08:53
-->
<template>
  <div class="subItem-project custom-tree-table">
    <Teleport to=".tab-menus" v-if="projectStore.tabSelectName === '分部分项'">
      <icon-font
        @click="showPageColumnSetting"
        style="position: absolute; right: 20px"
        type="icon-xianshilieshezhi"
      ></icon-font>
    </Teleport>
    <split
      horizontal
      ratio="4/3"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <frameSelect
          eventDom="multiple-select"
          ref="frameSelectRef"
          type="branch"
          :tableData="renderedList"
          @scrollTo="scrollTo"
          @selectData="getSelectData"
          class="table-content"
        >
          <vxe-table
            ref="vexTable"
            class="table-edit-common trends-table-column"
            keep-source
            id="sequenceNbr"
            :column-config="{ resizable: true }"
            :mouse-config="{ selected: true }"
            height="auto"
            :data="renderedList"
            :scroll-y="{ enabled: true, gt: 0 }"
            :tooltip-config="{
              showAll: false,
              trigger: 'cell',
              enterable: true,
            }"
            @keydown="tableKeydown"
            :keyboard-config="{
              isArrow: true,
              isDel: true,
              isEnter: true,
              isTab: true,
              isChecked: true,
            }"
            @cell-mouseenter="cellMouseEnterEvent"
            @cell-mouseleave="cellMouseLeaveEvent"
            @cell-click="
              cellData => {
                useCellClickEvent(cellData, tableCellClickEvent, [
                  'unit',
                  'bdCode',
                  'costMajorName',
                  'measureType',
                  'itemCategory',
                  'accordingDocument',
                ]);
              }
            "
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              beforeEditMethod: cellBeforeEditMethod,
            }"
            :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
            @current-change="currentChangeEvent"
            @edit-closed="editClosedEvent"
            :menu-config="menuConfig"
            @menu-click="contextMenuClickEvent"
            :row-class-name="rowClassName"
            :cell-class-name="cellClassName"
            :cell-style="cellStyle"
            :custom-config="{ storage: true }"
            :header-cell-class-name="setHeaderCellClassName"
            emptyText=" "
          >
            <vxe-column fixed="left" field="index" width="35" align="center">
              <template #default="{ row }">
                <div class="multiple-select" @click="clickIndex(row)">
                  {{ row.index }}
                </div>
              </template>
            </vxe-column>

            <vxe-column
              v-for="columns of handlerColumns"
              v-bind="columns"
              :header-class-name="headerClass(columns)"
            >
              <template
                #header="{
                  column,
                  columnIndex,
                  $columnIndex,
                  _columnIndex,
                  $rowIndex,
                }"
              >
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>

              <template
                v-if="columns.slot"
                #default="{ column, row, $columnIndex }"
              >
                <div
                  class="cell-line-break-el"
                  v-if="columns.field == 'bdCode'"
                >
                  <icon-font
                    v-if="row.isLocked"
                    type="icon-qingdan-suoding"
                  ></icon-font>
                  <i
                    @click="changeStatus(row)"
                    v-if="row.displaySign === 1 && tableData.length > 1"
                    class="vxe-icon-caret-down"
                  ></i>
                  <i
                    @click="changeStatus(row)"
                    v-if="row.displaySign === 2"
                    class="vxe-icon-caret-right"
                  ></i>
                  <span class="code">
                    <a-tooltip>
                      <template #title
                        >{{ row.bdCode }}
                        {{
                          row.redArray?.length > 0 ? row.redArray.join(',') : ''
                        }}</template
                      >
                      {{ row.bdCode }}
                      {{
                        row.redArray?.length > 0 ? row.redArray.join(',') : ''
                      }}
                    </a-tooltip> </span
                  ><span class="code-black" v-if="row.blackArray?.length > 0">{{
                    row.blackArray.join(',')
                  }}</span>
                </div>

                <template
                  v-else-if="
                    columns.field == 'name' || columns.field === 'bdName'
                  "
                >
                  <Annotations
                    @close="v => closeAnnotations(v, row)"
                    @onfocusNode="onFocusNode(row)"
                    v-if="
                      row?.noteViewVisible ||
                      row?.isShowAnnotations ||
                      row?.noteEditVisible
                    "
                    :note="row.annotations"
                    :isDisabled="row?.noteEditVisible || row?.isShowAnnotations"
                    :ref="el => getAnnotationsRef(el, row)"
                  ></Annotations>
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    v-if="
                      isSelectedCell({
                        $columnIndex,
                        column,
                        row,
                      }) && !['01', '02'].includes(row.kind)
                    "
                    @click.stop="openEditDialog('name')"
                  ></icon-font>
                  <div v-maxLineNumber="row">{{ row.name }}</div>
                </template>
                <template v-else-if="columns.field == 'qfCode'">
                  <a-tooltip placement="topLeft">
                    <template #title>{{ row.qfName }}</template>
                    <span class="cell-line-break-el">{{ row.qfName }}</span>
                  </a-tooltip>
                </template>
                <div v-else-if="columns.field == 'type'">
                  <span
                    v-if="
                      row.matchStatus === '1' && projectStore.combinedVisible
                    "
                    class="flag-green"
                    >精</span
                  >
                  <span
                    v-if="
                      row.matchStatus === '2' && projectStore.combinedVisible
                    "
                    class="flag-orange"
                    >近</span
                  >
                  <span
                    v-if="
                      row.matchStatus === '0' && projectStore.combinedVisible
                    "
                    class="flag-red"
                    >未</span
                  >
                  <span
                    v-if="
                      (!row.borrowFlag && !row.changeFlag) || row.type === '费'
                    "
                    >{{ row.type }}</span
                  ><span class="code-flag" v-if="row.type !== '费'"
                    >{{ row.changeFlag ? row.changeFlag : '' }}
                  </span>
                  <span
                    class="code-flag"
                    v-if="row.type !== '费' && !row.changeFlag"
                    >{{ row.borrowFlag ? row.borrowFlag : '' }}
                  </span>
                </div>

                <!-- 项目特征 -->
                <template v-else-if="columns.field == 'projectAttr'">
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    v-if="
                      row.kind === '03' &&
                      isSelectedCell({
                        $columnIndex,
                        column,
                        row,
                      })
                    "
                    @click.stop="openEditDialog('projectAttr')"
                  ></icon-font>
                  <a-tooltip placement="topLeft">
                    <template #title>{{ row.projectAttr }}</template>
                    <span class="cell-line-break-el">{{
                      row.projectAttr
                    }}</span>
                  </a-tooltip>
                </template>
                <template v-else-if="columns.field === 'quantity'">
                  <span
                    :style="
                      quantityDifferenceProportionStyle(
                        row.quantityDifferenceProportionColour
                      )
                    "
                    >{{ row.quantity }}</span
                  >
                </template>
                <template
                  v-else-if="columns.field === 'quantityDifferenceProportion'"
                >
                  <span
                    :style="
                      quantityDifferenceProportionStyle(
                        row.quantityDifferenceProportionColour
                      )
                    "
                    >{{ row.quantityDifferenceProportion }}</span
                  >
                </template>
                <!-- 锁定综合单价 -->
                <template v-else-if="columns.field === 'lockPriceFlag'">
                  <a-checkbox
                    v-if="row.kind === '03'"
                    :disabled="row.originalFlag"
                    v-model:checked="row.lockPriceFlag"
                    @change="updateFbData(row, 'lockPriceFlag')"
                  ></a-checkbox>
                </template>
                <!-- coldResistantSuborder防寒子目 -->
                <template v-else-if="columns.field === 'coldResistantSuborder'">
                  <a-checkbox
                    v-if="row.kind === '04'"
                    :disabled="row.originalFlag || projectStore.deType !== '12'"
                    v-model:checked="row.coldResistantSuborder"
                    @change="updateFbData(row, 'coldResistantSuborder')"
                  ></a-checkbox>
                </template>
                <!-- 工程量表达式 -->
                <template v-else-if="columns.field == 'quantityExpression'">
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    v-if="
                      (row.kind === '03' || row.kind === '04') &&
                      isSelectedCell({
                        $columnIndex,
                        column,
                        row,
                      })
                    "
                    @click="openEditDialog('quantityExpression')"
                  ></icon-font>
                  <span>{{ quantityExpressionTextHandler(row) }}</span>
                </template>
                <template v-else-if="columns.field == 'zjfPrice'">
                  <span v-if="row.kind !== '04'">{{}}</span>
                  <span v-else>{{ row.zjfPrice }}</span>
                </template>
                <template v-else-if="columns.field == 'zjfTotal'">
                  <span v-if="row.kind !== '04'"></span>
                  <span v-else>{{ row.zjfTotal }}</span>
                </template>
                <template v-else-if="columns.field == 'price'">
                  <!-- <span v-if="row.kind !== '04'">{{}}</span> -->
                  <!-- bug14554-清单行展示综合单价 -->
                  <span>{{ row.price }}</span>
                </template>
                <template
                  v-else-if="
                    columns.field == 'ifMainQd' && !['04'].includes(row.kind)
                  "
                >
                  <a-checkbox
                    v-model:checked="row.ifMainQd"
                    @change="updateFbData(row, 'ifMainQd')"
                  ></a-checkbox>
                </template>

                <template v-else-if="columns.field == 'ceilingPrice'">
                  <span v-if="['01', '02', '03'].includes(row.kind)">{{
                    row.ceilingPrice
                  }}</span>
                </template>
                <template
                  v-else-if="
                    ['description', 'costMajorName'].includes(column.field)
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>{{ row[columns.field] }}</template>
                    <span class="cell-line-break-el">{{
                      row[columns.field]
                    }}</span>
                  </a-tooltip>
                </template>
                <template v-else-if="columns.field == 'measureType'">
                  <a-tooltip placement="topLeft">
                    <template #title>{{ row[columns.field] }}</template>
                    <span class="cell-line-break-el">{{
                      ![2, 5].includes(Number(row.isCostDe))
                        ? row[columns.field]
                        : ''
                    }}</span>
                  </a-tooltip>
                </template>
                <template v-else-if="columns.field == 'relevanceName'">
                  {{ row?.relatedContractsQd?.name || '' }}
                </template>
                <template v-else-if="columns.field == 'accordingDocument'">
                  <ViewAccordingDocument
                    :row="row"
                    @successCallback="queryBranchDataById"
                  />
                </template>
                <!-- 备注 施工组织措施类别 取费文件 单价 工程量 单位-->
                <template v-else>
                  <span>{{ row[columns.field] }}</span>
                </template>
              </template>

              <template
                v-if="columns.slot"
                #edit="{ column, row, $columnIndex }"
              >
                <template
                  v-if="columns.field == 'name' || columns.field === 'bdName'"
                >
                  <Annotations
                    @close="v => closeAnnotations(v, row)"
                    @onfocusNode="onFocusNode(row)"
                    v-if="
                      row?.noteViewVisible ||
                      row?.isShowAnnotations ||
                      row?.noteEditVisible
                    "
                    :note="row.annotations"
                    :isDisabled="row?.noteEditVisible || row?.isShowAnnotations"
                    :ref="el => getAnnotationsRef(el, row)"
                  ></Annotations>
                  <bdNameSelect
                    v-if="['01', '02'].includes(row.kind)"
                    v-model="row.name"
                  ></bdNameSelect>

                  <vxe-pulldown
                    ref="bdNamePulldownRef"
                    transfer
                    v-else-if="['03'].includes(row.kind)"
                  >
                    <template #default>
                      <vxe-textarea
                        v-model="row.name"
                        rows="1"
                        className="custom-input"
                        placeholder="请输入项目名称"
                        @compositionstart="onCompositionStart(row)"
                        @compositionend="onCompositionEnd(row)"
                        @change="bdNameKeyupEvent(row, $event)"
                      >
                      </vxe-textarea>
                    </template>
                    <template #dropdown>
                      <div class="my-dropdown4">
                        <vxe-grid
                          border
                          auto-resize
                          :show-header="false"
                          height="auto"
                          width="500"
                          :row-config="{ isHover: true }"
                          :data="bdNameTableList"
                          :columns="tableColumn"
                          @cell-click="dbNameCellClickEvent"
                        >
                        </vxe-grid>
                      </div>
                    </template>
                  </vxe-pulldown>

                  <cell-textarea
                    v-else
                    :maxlength="2000"
                    v-model="row.name"
                    :clearable="false"
                    :textHeight="row.height"
                  ></cell-textarea>
                </template>

                <template v-else-if="columns.field == 'bdCode'">
                  <vxe-pulldown
                    ref="pulldownRef"
                    transfer
                    v-if="row.kind === '03' || row.kind === '04'"
                  >
                    <template #default>
                      <vxe-input
                        v-model="row.bdCode"
                        placeholder="请输入项目编码"
                        @keyup="keyupEvent(row, $event)"
                      ></vxe-input>
                    </template>
                    <template #dropdown>
                      <div class="my-dropdown4" v-if="row.kind === '03'">
                        <vxe-grid
                          border
                          auto-resize
                          :show-header="false"
                          height="auto"
                          width="500"
                          :row-config="{ isHover: true }"
                          :loading="loading"
                          :data="tableList"
                          :columns="tableColumn"
                          @cell-click="cellClickEvent"
                        >
                        </vxe-grid>
                      </div>
                    </template>
                  </vxe-pulldown>
                  <template v-else
                    ><i
                      @click="changeStatus(row)"
                      v-if="row.displaySign === 1"
                      class="vxe-icon-caret-down"
                    ></i>
                    <i
                      @click="changeStatus(row)"
                      v-if="row.displaySign === 2"
                      class="vxe-icon-caret-right"
                    ></i>
                    <span class="code">{{ row.bdCode }} </span></template
                  >
                </template>

                <!-- 项目特征 -->
                <template v-else-if="columns.field == 'projectAttr'">
                  <cell-textarea
                    v-if="row.kind === '03'"
                    :maxlength="2000"
                    v-model="row.projectAttr"
                    :clearable="false"
                    :textHeight="'28'"
                    @focus="projectAttrFocus(row)"
                    @change="projectAttrChange(row)"
                  ></cell-textarea>
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    @click.stop="openEditDialog('projectAttr')"
                  ></icon-font>
                  <div class="association-selected" v-if="row.kind == '03'">
                    <project-attr-association
                      ref="associationRef"
                      @dblclickHandler="data => associationDblClick(data, row)"
                      v-if="associationVisible"
                    />
                  </div>
                </template>

                <!-- 单位 -->
                <template v-else-if="columns.field == 'unit'">
                  <vxe-select
                    v-if="row.kind === '03' && row.bdCode"
                    v-model="row.unit"
                    transfer
                  >
                    <vxe-option
                      v-for="item in row.unitList?.split('/')"
                      :key="item"
                      :value="item"
                      :label="item"
                    ></vxe-option>
                  </vxe-select>
                  <vxeTableEditSelect
                    v-if="row.kind === '04' && row.bdCode"
                    :filedValue="row.unit"
                    :list="projectStore.unitListString"
                    :transfer="true"
                    @update:filedValue="
                      newValue => {
                        saveCustomInput(newValue, row, 'unit', $rowIndex);
                      }
                    "
                  ></vxeTableEditSelect>
                </template>

                <!-- 工程量表达式 -->
                <template v-else-if="columns.field == 'quantityExpression'">
                  <vxe-input
                    v-if="row.kind === '03' || row.kind === '04'"
                    v-model="row.quantityExpression"
                    :clearable="false"
                  >
                  </vxe-input>
                </template>

                <!-- 工程量 -->
                <template v-else-if="columns.field == 'quantity'">
                  <vxe-input
                    v-if="row.kind === '03' || row.kind === '04'"
                    v-model="row.quantity"
                  />
                </template>

                <!-- 单价 -->
                <template v-else-if="columns.field == 'zjfPrice'">
                  <vxe-input
                    v-if="
                      row.kind === '04' &&
                      !ishasRCJList &&
                      row.showZjfPrice != '0' &&
                      isNotCostDe
                    "
                    v-model.trim="row.zjfPrice"
                    :maxlength="10"
                    type="text"
                    @blur="row.zjfPrice = pureNumber(row.zjfPrice, 2)"
                  />
                  <span v-else>{{
                    row.kind === '03' ? '' : row.zjfPrice
                  }}</span>
                </template>
                <!-- 合价 -->
                <template v-else-if="columns.field == 'zjfTotal'">
                  <span v-if="row.kind === '03'"></span>
                  <span v-else>{{ row.zjfTotal }}</span>
                </template>
                <!-- 单价构成文件 -->
                <template v-else-if="columns.field == 'qfCode'">
                  <vxe-select
                    v-if="row.kind === '04' && row.bdCode"
                    v-model="row.qfCode"
                    transfer
                    @change="qfCodeChange(row)"
                  >
                    <vxe-option
                      v-for="item in djgcFileList"
                      :key="item.qfCode"
                      :value="item.qfCode"
                      :label="item.qfName"
                    ></vxe-option>
                  </vxe-select>
                  <span v-else class="cell-line-break-el">
                    {{ row.qfName }}
                  </span>
                </template>
                <!-- 取费文件 -->
                <template v-else-if="columns.field == 'costMajorName'">
                  <vxe-select
                    v-if="row.kind === '04' && row.bdCode"
                    v-model="row.costMajorName"
                    transfer
                    @change="costMajorNameChange(row)"
                  >
                    <vxe-option
                      v-for="item in feeFileList"
                      :key="item.qfCode"
                      :value="item.qfName"
                      :label="item.qfName"
                    ></vxe-option>
                  </vxe-select>
                  <span v-else class="cell-line-break-el">{{
                    row.costMajorName
                  }}</span>
                </template>

                <!-- 施工组织措施类别 -->
                <template v-else-if="columns.field == 'measureType'">
                  <vxe-select
                    v-if="
                      row.kind === '04' &&
                      row.bdCode &&
                      !row.zjcsClassCode &&
                      ![2, 5].includes(Number(row.isCostDe))
                    "
                    v-model="row.measureType"
                    transfer
                  >
                    <vxe-option
                      v-for="item in szTypeList"
                      :key="item.qfCode"
                      :value="item.cslbName"
                      :label="item.cslbName"
                    ></vxe-option>
                  </vxe-select>
                  <span v-else class="cell-line-break-el">{{
                    ![2, 5].includes(Number(row.isCostDe))
                      ? row.measureType
                      : ''
                  }}</span>
                </template>

                <!-- 备注 -->
                <template v-else-if="columns.field == 'description'">
                  <cell-textarea
                    v-model="row.description"
                    :textHeight="row.height"
                  ></cell-textarea>
                </template>
                <!-- 规格型号 -->
                <template v-else-if="columns.field == 'specification'">
                  <vxe-input
                    v-model="row.specification"
                    v-if="row.kind === '04' && row.rcjFlag === 1"
                  />
                  <span v-else>{{ row.specification }}</span>
                </template>
                <!-- 最高限价 -->
                <template v-else-if="columns.field == 'ceilingPrice'">
                  <vxe-input
                    class="ceilingPrice-wrap"
                    v-model.trim="row.ceilingPrice"
                    @blur="row.ceilingPrice = pureNumber(row.ceilingPrice, 2)"
                  />
                </template>

                <!-- 备注 -->
                <template v-else>
                  <vxe-input v-model="row[columns.field]" />
                </template>
              </template>
            </vxe-column>
          </vxe-table>
        </frameSelect>
      </template>
      <template #two>
        <div class="quota-content">
          <quota-info
            ref="quotaInfoRef"
            :currentInfo="currentInfo"
            :isAttrContent="isAttrContent"
            :type="1"
            :fatherLoading="tableLoading"
            :isUpdateFile="isUpdateFile"
            :isUpdateQuantities="isUpdateQuantities"
            @refreshCurrentInfo="refreshCurrentInfo"
            @tabClickBefore="showInfo"
            @onDbClickFile="onDbClickFile"
            :isComplete="isComplete"
            :isUpdate="isUpdate"
          ></quota-info>
        </div>
      </template>
    </split>
    <inventory-and-quota-index
      v-model:indexVisible="indexVisible"
      @currentQdDeInfo="currentQdDeInfo"
      @currentInfoReplace="currentInfoReplace"
      :dataType="dataType"
      :indexLoading="indexLoading"
      :originInfo="currentInfo"
    ></inventory-and-quota-index>
    <common-modal
      v-model:modelValue="deleteVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px"
    >
      <div class="content" v-if="deleteList.length === 2">请选择删除范围？</div>
      <div class="content" v-if="deleteList[0] === 5">
        是否确定删除？将删除{{
          currentInfo.kind === '01' ? '分部' : '清单'
        }}及其下挂所有数据.
      </div>
      <div
        class="content"
        v-if="deleteList.length === 1 && deleteList[0] === 4"
      >
        是否确定删除？
      </div>
      <div class="footer-btn-list">
        <a-button
          v-if="deleteList.length === 2"
          @click="delFbData(false)"
          :disabled="deleteLoading"
          >删除当前行</a-button
        >
        <a-button
          v-if="deleteList.length === 2"
          type="primary"
          @click="delFbData(true)"
          :disabled="deleteLoading"
          >删除关联数据</a-button
        >
        <div class="content" v-if="isBatchDelete">
          是否确定删除选中所有数据？
        </div>
        <a-button
          v-if="deleteList.length === 1 || isBatchDelete"
          type="primary"
          ghost
          @click="cancel"
          >取消</a-button
        >
        <a-button
          v-if="deleteList.length === 1 || isBatchDelete"
          type="primary"
          @click="delFbData(currentInfo.kind !== '04')"
          :disabled="deleteLoading"
          >确定</a-button
        >
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="isShowModel"
      className="dialog-comm"
      :title="showModelTitle"
      width="700"
      height="350"
    >
      <a-textarea
        v-model:value="editContent"
        placeholder="请输入编辑内容"
        :rows="8"
        class="edit-content"
      />
      <div class="footer-btn-list">
        <a-button @click="editCancel">取消</a-button>
        <a-button type="primary" @click="saveContent()">确定</a-button>
      </div>
    </common-modal>

    <info-modal
      v-model:infoVisible="infoVisible"
      :infoText="infoText"
      :isSureModal="isSureModal"
      :iconType="iconType"
      @updateCurrentInfo="updateCurrentInfo"
    ></info-modal>
    <bcQd
      v-model:qdVisible="qdVisible"
      :code="bdCode"
      :type="1"
      :currentInfo="currentInfo"
      @saveData="saveData"
      @bcCancel="bcCancel"
    ></bcQd>
    <bcDe
      v-model:visible="deVisible"
      :code="bdCode"
      :type="1"
      :currentInfo="currentInfo"
      @deSaveData="deSaveData"
      @bcCancel="bcCancel"
    ></bcDe>
    <bcRcj
      :code="bdCode"
      v-model:visible="rcjVisible"
      @rcjSaveData="rcjSaveData"
      @bcCancel="bcCancel"
    ></bcRcj>
    <common-modal
      v-model:modelValue="isPriceModel"
      className="dialog-comm"
      :title="showPriceTitle"
      :width="
        showModelType === 'csfy'
          ? '1000'
          : showModelType === 'azfy'
            ? 1200
            : 800
      "
      height="auto"
      @close="closePriceModel"
    >
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          @updateData="updateData"
          @close="closePriceModel"
        ></component>
      </keep-alive>
    </common-modal>
    <div class="unit-radio" v-if="showUnitTooltip">
      <div class="title">选择单位</div>
      <a-radio-group v-model:value="selectUnit">
        <a-radio
          v-for="(unit, index) in addCurrentInfo?.unit"
          :key="index"
          :value="unit"
        >
          {{ unit }}
        </a-radio>
      </a-radio-group>
      <a-button type="link" @click="selectHandler(addCurrentInfo)"
        >确定</a-button
      >
    </div>
    <set-standard-type
      v-model:standardVisible="standardVisible"
      :type="1"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      @refreshCurrentInfo="refreshCurrentInfo"
      @closeDialog="closeDialog"
    ></set-standard-type>
    <set-main-material
      v-model:materialVisible="materialVisible"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      :mainMaterialTableData="mainMaterialTableData"
      @setUpdate="setUpdate"
    >
    </set-main-material>
    <!-- 第一个弹框-组价匹配的 -->
    <common-modal
      v-model:modelValue="comMatchModal"
      className="dialog-comm"
      title="组价方案匹配"
      width="600"
      height="400"
    >
      <component-matching @closeComMatch="closeComMatch"></component-matching>
    </common-modal>
    <schedule-file
      v-model:dialogVisible="showSchedule"
      strokeColor="#54a1f3"
      :percent="percent"
      :desc="percentInfo?.dec"
      :pageType="'comMatch'"
      @isContinue="isContinue"
      :isNoClose="isNoClose"
      :percentInfo="percentInfo"
      :width="600"
    ></schedule-file>
    <common-modal
      className="titleNoColor noHeaderHasclose"
      v-model:modelValue="resetModal"
      title=" "
      width="400"
      height="200"
    >
      <div class="reCheck">
        <p style="font-weight: 600">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />组价进行中，是否确定关闭？
        </p>
        <p style="padding-left: 20px; margin-bottom: 26px">
          当前数据已发生变化是否应用组价后数据
        </p>

        <a-button style="margin: 0 30px 0 20px" @click="recover(true)"
          >否，恢复至组价前数据
        </a-button>
        <a-button type="primary" @click="recover(false)"> 确定</a-button>
      </div>
    </common-modal>
    <!-- 饼图-组价匹配的 -->
    <common-modal
      className="dialog-comm"
      title="组价方案匹配"
      width="550"
      height="400
    "
      v-model:modelValue="reportModel"
      @cancel="reportModel = false"
      @close="reportModel = false"
    >
      <match-pic
        @lookView="lookView"
        :startMatchData="startMatchData"
      ></match-pic>
    </common-modal>
    <!--    <combined-search @filterData="filterData"></combined-search>-->

    <!-- 复用组价 -->
    <ReuseGroupPriceDialog
      ref="ReuseGroupPriceRef"
      @refresh="queryBranchDataById('Refresh', '', false)"
      :currentInfo="currentInfo"
      :lockBtnStatus="lockBtnStatus"
    ></ReuseGroupPriceDialog>

    <!-- 清单快速组价 -->
    <qdQuickPricing
      ref="qdQuickPricingRef"
      @refresh="queryBranchDataById('Refresh', '', false)"
      @posRow="posRow"
      :currentInfo="currentInfo"
      :lockBtnStatus="lockBtnStatus"
    ></qdQuickPricing>

    <batch-delete
      v-model:batchDeleteVisible="batchDeleteVisible"
      :batchDataType="batchDataType"
      @updateData="queryBranchDataById"
    ></batch-delete>
    <PageColumnSetting
      :columnOptions="handlerColumns"
      ref="columnSettingRef"
      title="页面显示列设置"
      @save="updateColumns"
      :getDefaultColumns="getDefaultColumns"
    />
    <standard-group-price
      ref="standardGroupRef"
      :currentInfo="currentInfo"
    ></standard-group-price>

    <areaModal
      v-if="areaStatus"
      :type="areaVisibleType"
      @closeDialog="closeAreaModal"
    ></areaModal>
    <AccordingDocument
      v-model:visible="accordingDocumentVisible"
      @successCallback="queryBranchDataById"
    ></AccordingDocument>
    <AssociationContracts
      v-model:visible="associationContractVisible"
      @successCallback="queryBranchDataById"
    ></AssociationContracts>
  </div>
</template>

<script setup>
import {
  onMounted,
  onBeforeUnmount,
  reactive,
  ref,
  watch,
  nextTick,
  markRaw,
  defineAsyncComponent,
  onActivated,
  onDeactivated,
  getCurrentInstance,
} from 'vue';
import {
  quantityExpressionHandler,
  removeSpecialCharsFromPrice,
  pureNumber,
  everyNumericHandler,
} from '@/utils/index';
import { checkisOnline } from '@/utils/publicInterface';
import QuotaInfo from '@/jieSuan/views/projectDetail/customize/quotaInfo/index.vue';
import InventoryAndQuotaIndex from '@/views/projectDetail/customize/inventoryAndQuotaIndex/index.vue';
import api from '@/api/projectDetail.js';
import jsApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import ComponentMatching from '@/views/projectDetail/customize/measuresItem/componentMatching.vue';
import MatchPic from '@/views/projectDetail/customize/measuresItem/MatchPic.vue';
import { message } from 'ant-design-vue';
import { ItemBillMenuOperator } from '@/views/projectDetail/customize/subItemProject/ItemBillMenuOperator';
import xeUtils from 'xe-utils';
import frameSelect from '@/components/frameSelect/index.vue';
import split from '@/components/split/index.vue';
import { insetBus } from '@/hooks/insetBus';
import operateList from '@/views/projectDetail/customize/operate';
import { useCheckBefore } from '@/hooks/useCheckBefore';
import infoMode from '@/plugins/infoMode';
import SetStandardType from '@/views/projectDetail/customize/quotaInfo/setStandardType.vue';
import SetMainMaterial from '@/views/projectDetail/customize/quotaInfo/setMainMaterial.vue';
import ProjectAttrAssociation from '@/components/ProjectAttrAssociation/ProjectAttrAssociation.vue';

import { useCellClick } from '@/hooks/useCellClick';
import { useVirtualList } from '@/hooks/useVirtualList';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { useAttrAssociation } from '@/hooks/useAttrAssociation.js';

import { useSubItem } from '@/hooks/useSubItem.js';
import { CloseOutlined } from '@ant-design/icons-vue';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import { useRoute } from 'vue-router';

import ReuseGroupPriceDialog from '@/components/ReuseGroupPriceDialog/index.vue';
import qdQuickPricing from '@/components/qdQuickPricing/index.vue';
import bdNameSelect from '@/components/bdNameSelect/index.vue';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';
import { setGlobalLoading } from '@/hooks/publicApiData';
import { originalTableColumns, tableColumns } from './columns.js';
import { commonJieSuan } from './commonJieSuan.js';
import ViewAccordingDocument from './components/ViewAccordingDocument.vue';
const AccordingDocument = defineAsyncComponent(
  () => import('./components/AccordingDocument.vue')
);
const AssociationContracts = defineAsyncComponent(
  () => import('./components/AssociationContracts.vue')
);
const route = useRoute();

const { dataSearchPosition } = useReversePosition();
const {
  useCellClickEvent,
  useCellDBLClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
  resetCellData,
} = useCellClick();

const { checkUnit, showInfo, isComplete } = useCheckBefore();

const components = markRaw(new Map());
components.set(
  'zscy',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/zscyContent.vue')
  )
);
components.set(
  'zscg',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/zscgContent.vue')
  )
);
components.set(
  'azfy',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/azfyContent.vue')
  )
);
components.set(
  'commercial',
  defineAsyncComponent(
    () =>
      import(
        '@/views/projectDetail/customize/measuresItem/commercialContent.vue'
      )
  )
);
// components.set(
//   'csfy',
//   defineAsyncComponent(() => import('../measuresItem/csfyContent.vue'))
// );
let isPriceModel = ref(false);
let showModelType = ref('');
let showPriceTitle = ref('');
// let comMatchModal = ref(false); //组件方案弹框
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
// let currentInfo = ref();
let vexTable = ref();
let frameSelectRef = ref();
const projectStore = projectDetailStore();
const menuOperator = new ItemBillMenuOperator();
let contextmenuList = ref(menuOperator.menus);
let bcContextmenuList = ref(menuOperator.menus);
let szTypeList = ref([]);
let dataType = ref('03');
let isAttrContent = ref(false);
let unitList = ref([]);
let deleteVisible = ref(false);
let splitPercent = ref(0.55);
const emits = defineEmits(['updateMenuList', 'getCurrentInfo']);

//组价部分
const scheduleFile = defineAsyncComponent(
  () => import('@/components/schedule/schedule.vue')
);
const $ipc = cxt.appContext.config.globalProperties.$ipc;
let comMatchModal = ref(false); //组价方案弹框
let percentInfo = ref(); //进度条描述
let percent = ref(0); //进度条百分比
let resetModal = ref(false); //是否确认关闭进度条
let isNoClose = ref(false); //进度条关闭前执行函数
let showSchedule = ref(false);
let reportModel = ref(false); //组价饼图弹框
let startMatchData = ref();

let {
  updateQdByName,
  dbNameCellClickEvent,
  bdNameTableList,
  bdNamePulldownRef,
  showUnitTooltipType,
  bdNameKeyupEvent,
  onCompositionEnd,
  onCompositionStart,

  editClosedEvent,
  onDragHeight,
  initVirtual,
  renderedList,
  loading: tableLoading,
  init,
  addDeInfo,
  EnterType,
  scrollToPosition,
  currentChangeEvent,
  mainMaterialTableData,
  updateFbData,
  queryBranchDataById,
  queryRcjDataByDeId,
  queryFeeFileData,
  queryDjgcFileData,
  saveContent,
  openEditDialog,
  showModelTitle,

  currentInfo,
  isShowModel,
  editContent,
  editKey,
  infoVisible,
  infoText,
  iconType,
  isSureModal,

  ishasRCJList,
  isClearEdit,
  isSortQdCode,
  bdCode,
  rcjVisible,
  currentUpdateData,
  deVisible,
  qdVisible,

  isUpdateFile,
  indexVisible,
  isUpdateQuantities,
  selectUnit,
  showUnitTooltip,
  addCurrentInfo,
  isIndexAddInfo,
  addDataSequenceNbr,
  lockFlag,
  feeFileList,
  djgcFileList,
  tableData,
  originalTableData,
  materialVisible,
  DJGCrefreshFeeFile,
  updateDelTempStatusColl,
  batchDelByTypeOfColl,
  batchDeleteVisible,
  codeType,
  radioStyle,
  isOpenLockedStatus,
  batchDataType,
  selectData,
  handleNote,
  handleNoteClick,
  areaStatus,
  areaVisibleType,
  handleMainList,
  handleMainListClick,
  closeAreaModal,
  closeAnnotations,
  getAnnotationsRef,
  cellMouseEnterEvent,
  cellMouseLeaveEvent,
  onFocusNode,
  standardVisible,
  queryRule,
  renderLine,
  isNotCostDe,
  deleteStateFn,
  needAddQDandFB,
  expandLevelValue,
  tableKeydown,
  setTableKeydownEnd,
} = useSubItem({
  api: {
    updateData: api.updateFbData,
    getList: jsApi.queryHierarchyFb,
  },
  operateList,
  frameSelectRef,
  resetCellData,
  checkUnit,
  vexTable,
  emits,
  pageType: 'fbfx',
});

const isContinue = type => {
  if (type === '关闭') {
    isNoClose.value = true;
    resetModal.value = true;
  } else if (type === '暂停') {
    api
      .pauseMerge({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {});
  } else if (type === '继续') {
    api
      .startMerge(JSON.parse(JSON.stringify(startMatchData.value)))
      .then(res => {});
  }
};
const closeComMatch = data => {
  //关闭组价方案匹配
  startMatchData.value = data;
  // comMatchModal.value = false;
  // reportModel.value = true; //饼图弹框
  startMatch(data);
};
const startMatch = async data => {
  isNoClose.value = true;
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  //初始化进度条
  percentInfo.value = {
    finish: 0,
    noFinish: 0,
    total: 0,
    dec: '组价方案匹配中，请稍后…',
  };
  percent.value = 0;
  comMatchModal.value = false;
  // setTimeout(() => {
  showSchedule.value = true; //组价进度条开始

  $ipc.on(formData.constructId, (event, arg) => {
    if (arg.percent >= percent.value) {
      percentInfo.value = {
        finish: arg.succeed,
        noFinish: arg.notSuccess,
        total: arg.total,
        dec: arg.percent >= 100 ? '组价方案完成' : '组价方案匹配中，请稍后…',
      };
      percent.value = arg.percent;
    }
    if (arg.percent >= 100) {
      $ipc.removeAllListeners('formData.constructId'); //监听事件移除
      if (isopenReport.value) {
        console.log('ReuseGroupPriceRef.value', ReuseGroupPriceRef.value);
        isopenReport.value = false;
        closeSchedule();
      }
    }
  });
  let res = await api.startMerge(data).then();
  if (res.status === 500 && percent.value === 0) {
    setTimeout(() => {
      percentInfo.value = {
        finish: 0,
        noFinish: 0,
        total: 0,
        dec: '组价方案完成',
      };
      percent.value = 100;
      closeSchedule();
    }, 1000);
  }
};
const closeSchedule = () => {
  isopenReport.value = false;
  setTimeout(() => {
    isNoClose.value = false;
    showSchedule.value = false; //组价进度条关闭
    if (resetModal.value) {
      resetModal.value = false;
    }
    $ipc.removeAllListeners('formData.constructId'); //监听事件移除
    if (projectStore.tabSelectName === '分部分项') {
      reportModel.value = true; //饼图弹框
      queryBranchDataById('Refresh');
    }
  }, 2000);
};
const recover = async bol => {
  //否，恢复至组价前数据
  //  bol--为true恢复
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  let getRes;
  if (bol) {
    getRes = await api.beforeRestoring(formData).then();
  } else {
    getRes = await api.determine(formData).then();
  }
  isNoClose.value = false;
  resetModal.value = false;
  showSchedule.value = false;
  queryBranchDataById('Refresh'); //点击是或否都更新数据
};
//扇形图点击查看功能
const lookView = data => {
  checkList.value = [];
  reportModel.value = false; //饼图弹框
  switch (data.name) {
    case '精准组价':
      checkList.value.push('1');
      break;
    case '近似组价':
      checkList.value.push('2');
      break;
    case '未匹配组价':
      checkList.value.push('0');
      break;
  }
  dataSearchPosition({
    treeId: startMatchData.value.selectedUnitIdList[0],
    tabMenuName: '分部分项',
    type: checkList.value,
  });
};
//项目特征关联
let {
  associationVisible,
  associationRef,
  dblClickHandler,
  projectAttrChange,
  projectAttrFocus,
} = useAttrAssociation({ type: 'fbfx' });

// 关联数据双击应用回调
const associationDblClick = (data, row) => {
  dblClickHandler({
    data,
    row,
    callback: () => {
      queryBranchDataById();
      setTimeout(() => {
        quotaInfoRef.value.manualTabChange('groupSchemeTable');
      }, 500);
    },
  });
};

let flag = ref(true); //判断输入的内容是否合法

let menuList = ref([
  {
    type: 0,
    name: '添加分部',
    kind: '01',
    isValid: false,
  },
  {
    type: 1,
    name: '添加子分部',
    kind: '02',
    isValid: false,
  },
  {
    type: 2,
    name: '添加清单',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '添加定额',
    kind: '04',
    isValid: false,
  },
]);
let bcMenuList = ref([
  {
    type: 2,
    name: '补充清单',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '补充定额',
    kind: '04',
    isValid: false,
  },
  {
    type: 3,
    name: '补充人材机',
    kind: '05',
    isValid: false,
  },
]);
const quotaInfoRef = ref();
let deleteList = ref([]);
let page = ref(1);
let limit = ref(300000);
let scrollSwitch = ref(false);
let loading = ref(false);
let deleteLoading = ref(false);
let copyData = ref(null);
// let showModelTitle = ref('清单名称编辑');
// let isShowModel = ref(false);
// let editContent = ref('');
// let editKey = ref('');
// let infoVisible = ref(false); // 提示信息框是否显示
// let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
// let iconType = ref(''); // 提示信息框的图标
// let isSureModal = ref(false); // 提示信息框是否为确认提示框
// let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
const pulldownRef = ref(null); // 编码推荐数据ref
let indexLoading = ref(false); // 索引页面loading

const bcQd = defineAsyncComponent(
  () =>
    import('@/views/projectDetail/customize/subItemProject/components/bcQd.vue')
);
const bcDe = defineAsyncComponent(
  () =>
    import('@/views/projectDetail/customize/subItemProject/components/bcDe.vue')
);
const bcRcj = defineAsyncComponent(
  () =>
    import(
      '@/views/projectDetail/customize/subItemProject/components/bcRcj.vue'
    )
);

let isBatchDelete = ref(false); // 是否批量删除
let standardData = ref([]); // 定额数据下挂的标准换算数据,如若为空,则不展示设置标准换算弹框
// let materialVisible = ref(false); // 是否设置主材市场价弹框
// let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
let isUpdate = ref(false); // 修改主材市场价刷新人材机明细数据
const checkList = ref([]); // 组价方案匹配筛选选中值

watch(
  () => [
    projectStore.asideMenuCurrentInfo?.sequenceNbr,
    projectStore.currentTreeInfo,
  ],
  async () => {
    if (
      projectStore.tabSelectName === '分部分项' &&
      projectStore.currentTreeInfo?.levelType === 3
    ) {
      // projectStore.currentTreeInfo?.originalFlag ? fbOriginalTableColumns.value : fbTableColumns.value,
      initColumns({
        columns: projectStore.currentTreeInfo?.originalFlag
          ? originalTableColumns.value
          : tableColumns.value,
        pageName: `${
          projectStore.currentTreeInfo?.originalFlag ? 'htnfbfx' : 'htwfbfx'
        }`,
      });
      page.value = 1;
      originalFlagMenuConfigHandler(menuConfig);
      queryFeeFileData();
      // await queryDjgcFileData();
      // console.log('index-queryDjgcFileData', djgcFileList);
      querySzType();
      // queryUnit();

      // nextTick(() => {
      //   initVirtual(vexTable.value);
      // });
      if (!projectStore.isAutoPosition) {
        // 不是自动定位的才调用接口
        queryBranchDataById('other');
      }
    }
  }
);

watch(
  () => lockFlag.value,
  () => {
    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
  }
);

watch(
  () => projectStore.stageCount,
  () => {
    queryBranchDataById('Refresh');
  }
);
watch(
  () => projectStore.standardGroupOpenInfo.isOpen,
  () => {
    let reuse = operateList.value.find(
      item => item.name === 'reuse-group-price'
    ).options[2]; //标准组价子窗口复用组价提取已有清单隐藏
    if (projectStore.standardGroupOpenInfo.isOpen) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-ruotixing',
        infoText: '提取成功！',
        descText: projectStore.standardGroupOpenInfo.modalTip,
        confirm: () => {
          infoMode.hide();
        },
      });
      reuse.isValid = false;
    } else {
      reuse.isValid = true;
    }
  }
);
watch(
  () => projectStore.positionId,
  () => {
    if (
      projectStore.tabSelectName === '分部分项' &&
      projectStore.positionId &&
      projectStore.currentTreeInfo?.levelType === 3
    ) {
      currentInfo.value = { sequenceNbr: projectStore.positionId };
      queryBranchDataById('other', projectStore.positionId);
    }
  }
);

watch(
  () => projectStore.combinedSearchList,
  () => {
    if (
      projectStore.tabSelectName === '分部分项' &&
      projectStore.combinedSearchList &&
      projectStore.currentTreeInfo?.levelType === 3
    ) {
      if (!projectStore.combinedVisible)
        projectStore.SET_COMBINED_VISIBLE(true);
      nextTick(() => {
        filterData(projectStore.combinedSearchList);
      });
    }
  }
);
watch(
  () => tableLoading.value,
  () => {
    setGlobalLoading(tableLoading.value, '加载中，请稍后');
  }
);

onMounted(async () => {
  projectStore.isOpenIndexModal = {
    open: false,
    tab: null,
  };
  if (!projectStore?.subItemProjectAutoPosition) {
    projectStore.subItemProjectAutoPosition = {
      copyAndPaste,
      posRow,
      selectData,
      queryBranchDataById,
    };
  }

  if (
    projectStore.asideMenuCurrentInfo?.sequenceNbr &&
    projectStore.tabSelectName === '分部分项'
  ) {
    page.value = 1;
    if (!projectStore.isAutoPosition) {
      // 不是自动定位的才调用接口
      queryBranchDataById('other');
    }
    queryFeeFileData();
    // await queryDjgcFileData();
    // console.log('index-queryDjgcFileData', djgcFileList);
    querySzType();
    // queryUnit();
  }

  // window.addEventListener('keydown', copyAndPaste);
});

let lockBtnStatus = ref(false);
onActivated(() => {
  lockBtnStatus.value = false;
  bus.off('handleCopyEvent');
  bus.on('handleCopyEvent', ({ event, name }) => {
    if (name === 'subItemProject') copyAndPaste(event);
  });
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
  bus.off('asideMenuRightClickHandler');
  bus.on('asideMenuRightClickHandler', ({ name, data, menuKey }) => {
    if (name === 'subItemProject') {
      console.log(name, data, menuKey);
      if (menuKey !== 0) {
        // 显示分部
        openBranchHandler(data);
      } else {
        // 删除分部
        branchIndexDeleteHandler(data);
      }
    }
  });
  insetBus(bus, projectStore.componentId, 'subItemProject', async data => {
    console.log('🚀 ~ onActivated ~ data:', data);

    if (
      !['insert-subItem', 'supplement', 'reuse-group-price'].includes(
        data.name
      ) &&
      !(await showInfo())
    )
      return;
    if (data.name === 'insert-subItem') {
      if (!data.activeKind) {
        contextMenu();
      } else {
        addData(data.activeKind);
      }
    }
    if (data.name === 'supplement') {
      if (!data.activeKind) {
        bcContextMenu();
      } else {
        bcData(data);
      }
    }
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
    if (data.name === 'delete-subItem') deleteType(null);
    if (data.name === 'code-reset') {
      batchRefresh();
    }
    if (data.name === 'vertical-transport') showModel('zscy');
    if (data.name === 'superelevation') showModel('zscg');
    if (data.name === 'installation-costs') showModel('azfy');
    if (data.name === 'calculate-commercial') showModel('commercial');
    if (data.name === 'lock-subItem') allLock();
    if (data.name === 'component-matching') checkOnline(data);
    if (data.name === 'reuse-group-price') openReuseGroupPrice(data);
    if (data.name === 'qd-group-price') openQdQuickPricing(data);
    if (data.name === 'standard-group-price') openStandardGroupPrice(data);
    if (data.name === 'expandLevel') {
      if (!data.activeKind) {
      } else {
        expandLevelHandler(data.activeKind);
      }
    }
    jieSuanBusHandler(data);
  });
});
const expandLevelHandler = activeKind => {
  expandLevelValue.value = activeKind;
  queryBranchDataById('Refresh', '');
};

onDeactivated(() => {
  lockBtnStatus.value = true;
  qdQuickPricingRef.value?.cancel(false);
});

const openBranchHandler = data => {
  // 展开分部逻辑
};
const branchIndexDeleteHandler = async data => {
  let postRow = tableData.value.find(i => i.sequenceNbr == data.sequenceNbr);
  currentInfo.value = postRow;
  vexTable.value?.setCurrentRow(postRow);
  vexTable.value?.scrollToRow(postRow);
  deleteType();
};
let isopenReport = ref(false);
const checkOnline = async data => {
  if (Object.prototype.hasOwnProperty.call(data, 'activeKind')) {
    if (data.activeKind === '01') {
      //点击组件方案匹配
      comMatchModal.value = true;
      isopenReport.value = true;
    } else if (data.activeKind === '02') {
      //点击组件筛选
      if (!projectStore.combinedVisible)
        projectStore.SET_COMBINED_VISIBLE(true);
    }
  } else {
    const isOnline = checkisOnline(true);
    isOnline
      ? data.options.forEach(item => (item.isValid = true))
      : data.options.forEach(item => (item.isValid = false));
  }
};
onBeforeUnmount(() => {
  // window.removeEventListener('keydown', copyAndPaste);
});

/**
 *
 * @param {*} event
 * @param {*} isHandCopy 是否手动执行复制操作
 */
const copyAndPaste = (event, isHandCopy = false) => {
  if (['input', 'textarea'].includes(event.target.nodeName.toLowerCase()))
    return;
  console.log('复制', event.target.nodeName.toLowerCase());
  // 如果选中数据为空，情景1，刚开始进入页面，2点击了input,然后点击空白处
  if (!selectData.value || !selectData.value?.data?.length) {
    frameSelectRef.value?.isBranchCopy([currentInfo.value?.sequenceNbr]);
  }
  if (isHandCopy) {
    copyFun();
  }

  if (event.ctrlKey && event.code === 'KeyC') {
    copyFun();
  }
  if (event.ctrlKey && event.code === 'KeyV') {
    if (!vexTable.value.getSelectedCell()) return; //vexTable.value.getSelectedCell()如果当前表格不是选中，就不进行ctr+v
    pasteFun();
  }
};
const batchRefresh = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '是否确认刷新数据？',
    confirm: () => {
      api.batchRefresh(apiData).then(res => {
        if (res.status === 200 && res.result) {
          page.value = 1;
          message.success('项目编码重刷成功');
          queryBranchDataById();
        }
      });
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const copyFun = () => {
  if (!selectData.value) {
    message.error('暂无选中数据');
  } else {
    // copyTableToClipboard(vexTable.value.$el)
    if (selectData.value.isCopy) {
      copyData.value = selectData.value;
      let apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
      };
      api.copyQdDeFbData(apiData).then(async res => {
        console.log('复制数据', apiData, projectStore);
        if (res.status === 200 && res.result) {
          await jsApi.updatePasteLineHtDataColl({
            constructId: projectStore.currentTreeGroupInfo?.constructId,
            singleId: projectStore.currentTreeGroupInfo?.singleId,
            unitId: projectStore.currentTreeInfo?.id,
            type: 1,
          });
          message.success('已复制');
        }
      });
    } else {
      message.error(selectData.value.msg);
    }
  }
};
const pasteFun = async () => {
  // let clipboardText;
  // const clipPromise = navigator.clipboard.readText();
  // await clipPromise.then(function (clipText) {
  //   //粘贴板粘贴的数据
  //   clipboardText = clipText;
  //   if (!copyData.value && clipboardText) {
  //     copyData.value = clipboardText;
  //   }
  // });
  // console.log('clipboardText', clipboardText);
  if (projectStore.stageCount) return; // 分期不粘贴
  if (!copyData.value) {
    message.error('暂无复制数据');
  } else {
    if (
      projectStore.standardGroupOpenInfo.isOpen &&
      copyData.value.data.find(i => i.kind === '03')
    ) {
      message.error('标准组价不可粘贴包含清单行数据');
      return;
    }
    if (!frameSelectRef.value.getRowCurrent()) {
      return message.error('请选中需要粘贴行！');
    } else {
      let row = frameSelectRef.value.getRowCurrent();
      try {
        await frameSelectRef.value.frameSelectJs.isPasteBranch(
          row,
          copyData.value
        );
        console.log('粘贴数据到此页面：', copyData.value);
        if (
          projectStore.currentTreeInfo?.originalFlag &&
          copyData.value.data[0].originalFlag &&
          copyData.value.data[0].kind === '04'
        ) {
          return message.error('原始数据下不可粘贴定额！');
        }
        batchPasteQdDeData();
        // frameSelectRef.value.clearSelect();
        // copyData.value = null;
        // selectData.value = null;
      } catch (error) {
        message.error(error);
      }
    }
  }
};

const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = val => {
  selectData.value = val;
};

// 定位方法
const posRow = sequenceNbr => {
  currentInfo.value = { sequenceNbr };
  queryBranchDataById('other', sequenceNbr);
};

const addType = () => {
  console.log('插入类型');
};

const bcHandle = async () => {
  if (!(await showInfo())) return;
};

// 删除数据
const deleteType = async () => {
  isBatchDelete.value = false;
  if (!(await showInfo())) {
    return;
  }
  if (currentInfo.value.kind === '0') {
    message.warning('该行不可删除');
    return;
  }
  deleteList.value = [];
  if (selectData.value && selectData.value.data?.length > 1) {
    isBatchDelete.value = true;
  } else {
    currentInfo.value.optionMenu.forEach(item => {
      if (item === 4 || item === 5) {
        deleteList.value.push(item);
      }
    });
  }
  if (deleteList.value.length > 0 || isBatchDelete.value) {
    deleteVisible.value = true;
  }
};

const cancel = () => {
  deleteVisible.value = false;
};

const addData = async kind => {
  if (!(await showInfo())) return;
  const { constructId, singleId } = projectStore.currentTreeGroupInfo;
  const { id: unitId } = projectStore.currentTreeInfo;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    pointLine: {
      kind: currentInfo.value.kind,
      sequenceNbr: currentInfo.value.sequenceNbr,
      parentId: currentInfo.value.parentId,
      displayStatu: currentInfo.value.displayStatu,
      displaySign: currentInfo.value.displaySign,
    },
    newLine: {
      kind: kind,
    },
  };
  api.addQdDeFbData(apiData).then(async res => {
    if (res.status === 200 && res.result) {
      if (kind === '01' || kind === '02') {
        emits('updateMenuList');
      }

      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      await updateNewAddHtDataColl(addDataSequenceNbr.value);
      message.success('插入成功');
      queryBranchDataById();
      queryDjgcFileData();
    }
  });
};

/**
 * 点击index事件 为什么从上面单独拿下来，因为收起分部，然后这时候多次点击，没有触发currentChangeEvent事件。所以拿不到当前行子级数据了就是空数据了
 * @param {*} row
 */
const clickIndex = row => {
  const $table = vexTable.value;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(currentInfo.value)) return;
  currentInfo.value = row;
  projectStore.SET_SUB_CURRENT_INFO(row);
  nextTick(() => {
    if (row.kind === '04') return;
    // 等选中的样式更新完，
    queryAllDataByBranchId();
  });
};

const isFlag = row => {
  //判断输入工程量表达式是否合法
  let input = row.quantityExpression.replace(
    row.originalQuantityExpression,
    ''
  );
  let flag = true;
  let inputList = input.match(/[A-Za-z0-9_]+(\.\d+)?/g);
  const reg = /[^\d.]/g;
  inputList &&
    inputList.map(item => {
      if (reg.test(item)) {
        flag = false;
        return;
      }
    });
  return flag;
};

const saveCustomInput = (newValue, row, name, index) => {
  if (newValue) {
    row[name] = newValue;
  }
};

// 弹框提示点击确定按钮事件
const updateCurrentInfo = () => {
  updateFbData(currentInfo.value, 'quantityExpression');
};

const closeDialog = () => {
  standardVisible.value = false;
};

// 工程量明细更改后刷新当前行数据
const refreshCurrentInfo = (refreshFeeFile = false) => {
  console.log(
    '🚀 ~ r工程量明细更改后刷新当前行数据eeFile:',
    projectStore.tabSelectName,
    refreshFeeFile,
    currentInfo.value
  );
  if (projectStore.tabSelectName !== '分部分项') return;
  addDataSequenceNbr.value = addCurrentInfo.value
    ? addCurrentInfo.value.sequenceNbr
    : currentInfo.value.sequenceNbr;
  DJGCrefreshFeeFile.value = refreshFeeFile ? true : false;
  queryBranchDataById();
};

/**
 * 设置主材市场价弹框操作更新
 * @param type 1 关闭弹框, 2 刷新数据
 */
const setUpdate = type => {
  materialVisible.value = false;
  if (type === 2) {
    isUpdate.value = true;
    setTimeout(() => {
      isUpdate.value = false;
    }, 100);
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
    queryBranchDataById();
  }
  //if (projectStore.globalSettingInfo.standardConversionShowFlag) {
  queryRule();
  //}
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const onDbClickFile = v => {
  updateFbData(
    {
      ...currentInfo.value,
      projectAttr: v.value,
    },
    'projectAttr'
  );
};

const changeStatus = row => {
  let index = tableData.value.findIndex(x => x.sequenceNbr === row.sequenceNbr);
  page.value = Math.ceil((index + 1) / limit.value);
  expandLevelValue.value = 'none';
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // updateFbData(row, 'seq');
};

const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.openTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.closeTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};

const toggleMethod = ({ expanded, row }) => {
  if (expanded) {
  } else {
  }
  // row.closeFlag = expanded
  // updateFbData(row)
  return true;
};

const cellDBLClickEvent = async ({ row, column }) => {
  if (['bdCode', 'projectAttr'].includes(column.field) && !(await showInfo()))
    return;
  // if (projectStore.standardGroupOpenInfo.isOpen) return;
  if (column.field === 'bdCode') {
    indexVisible.value = true;
    dataType.value = row.kind;
  } else if (column.field === 'projectAttr') {
    isAttrContent.value = true;
    setTimeout(() => {
      isAttrContent.value = false;
    }, 100);
  }
  currentInfo.value = row;
};

// 表格单击事件
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  setTableKeydownEnd({ $table: vexTable.value });
  if (dbClickTime < 250) {
    cellDBLClickEvent({ row, column });
    // 前后点击相差250毫秒触发双击
  }
  const $table = vexTable.value;
  if ($event.ctrlKey) {
    const document1 = document.querySelector('.multiple-check');
    if (document1) {
      const firstInfo = $table.getRowNode(document1);
      $table.setCurrentRow(firstInfo.item);
      currentInfo.value = firstInfo.item;
      projectStore.SET_SUB_CURRENT_INFO(firstInfo.item);
    }
  }
  if (row.isLocked || row.tempDeleteFlag) return false;
  if (stageEditIntercept({ row, column })) return false;
  if (originalEditIntercept({ row, column })) return false;
  if (lockPriceFlagEditIntercept({ row, column })) return false;

  return true;
};

const currentQdDeInfo = row => {
  // let apiData = {
  //   unit: row.unit,
  //   sequenceNbr: row.sequenceNbr,
  // };
  fillFromIndexPage(row);
  // console.log('1111111111', apiData.formData);
  // addData(row.deName ? '04' : '03', apiData);
};
const fillFromIndexPage = row => {
  let pointLine = JSON.parse(JSON.stringify(currentInfo.value));
  if (pointLine?.originalFlag && !pointLine?.bdCode) {
    // 结算原始数据空数据bdCode为空，插入得时候手动添加bdCode为123
    pointLine.bdCode = '123';
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine,
    kind: row.kind,
    indexId: row.sequenceNbr,
    unit: row.unit,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    rcjFlag: row.rcjFlag,
    fbfxOrCsxm: 'fbfx',
    libraryCode: row.libraryCode,
  };

  let apiName = 'fillFromIndexPage';

  if (row.deArray?.length) {
    // 清单指引点击插入子目保存
    apiData.deArray = row.deArray;
    apiName = 'saveDeArray';
  }

  if (row.baseListModel?.sequenceNbr) {
    // 清单指引点击插入清单
    apiData.baseListModel = row.baseListModel;
    apiName = 'saveQdAndDeArray';
  }

  indexLoading.value = true;

  console.log('发送数据', apiData);

  api[apiName](apiData)
    .then(async res => {
      console.log('插入成功！', res);

      if (res.status === 200 && res.result) {
        indexLoading.value = false;
        // if (
        //   currentInfo.value?.kind !== '03' &&
        //   currentInfo.value?.kind !== '04' &&
        //   row.kind === '03'
        // ) {
        // } else {
        isIndexAddInfo.value = true;
        // }

        if (apiName === 'saveDeArray') {
          // 插入子目
          addDataSequenceNbr.value = res.result[0].data.sequenceNbr;
          queryRcjDataByDeId();
        } else if (apiName === 'saveQdAndDeArray') {
          addDataSequenceNbr.value =
            res.result?.saveQdResult?.data?.sequenceNbr;
        } else {
          addDataSequenceNbr.value = res.result.data.sequenceNbr;
          page.value = Math.ceil((res.result.index + 1) / limit.value);
          // if (row.kind === '01' || row.kind === '02') {
          //   emits('updateMenuList');
          // }
          if (row.kind === '04' && row.rcjFlag === 0) {
            addDeInfo.value = res.result.data;
            queryRcjDataByDeId();
            // standardVisible.value = true;
          }
        }
        await updateNewAddHtDataColl(addDataSequenceNbr.value);
        message.success('插入成功');
        queryBranchDataById();
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};
// 替换功能
const currentInfoReplace = row => {
  console.log('🚀 ~ currentInfoReplace ~ row:', row);
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentInfo.value.sequenceNbr,
    type: 1,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    fbfxOrCsxm: 'fbfx',
    libraryCode: row.libraryCode,
    rootLineId: route.query.constructSequenceNbr,
  };

  let apiName = 'replaceItemBillData';
  if (row?.qdzyReplace) {
    apiName = 'replaceQdAndSaveDeArray';
    apiData.pointLine = JSON.parse(JSON.stringify(currentInfo.value));
    apiData.deArray = row.deArray;
    apiData.baseListModel = row.baseListModel;
  }
  console.log('🚀 ~ currentInfoReplace ~ apiData:', apiData);

  api[apiName](apiData)
    .then(async res => {
      if (res.status === 200 && res.result) {
        indexLoading.value = false;
        if (apiData.deArray?.length) {
          queryRcjDataByDeId();
        } else if (row.kind === '04' && row.rcjFlag === 0) {
          addDeInfo.value = res.result;
          queryRcjDataByDeId();
        }
        addDataSequenceNbr.value = res.result.sequenceNbr;
        await updateNewAddHtDataColl(addDataSequenceNbr.value);
        queryBranchDataById();
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

const contextMenu = () => {
  let tempList = xeUtils.clone(menuList.value, true);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  contractOriginalFlagMenuInsert(tempList, currentInfo.value);
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'insert-subItem').options =
    tempList;
};

const bcContextMenu = () => {
  let tempList = xeUtils.clone(bcMenuList.value, true);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  contractOriginalFlagMenuInsert(tempList, currentInfo.value);
  bcContextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'supplement').options = tempList;
};

const bcData = async item => {
  if (!(await showInfo())) return;
  bdCode.value = '';
  if (item.activeKind === '03') {
    qdVisible.value = true;
  } else if (item.activeKind === '04') {
    deVisible.value = true;
  } else {
    rcjVisible.value = true;
  }
};

const querySzType = () => {
  api
    .querySzType({ constructId: route.query.constructSequenceNbr })
    .then(res => {
      if (res.status === 200 && res.result) {
        szTypeList.value = res.result;
      }
    });
};

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};

const delFbData = type => {
  if (deleteLoading.value) return;
  deleteLoading.value = true;
  if (isBatchDelete.value) {
    let index = tableData.value.findIndex(
      x => x.sequenceNbr === selectData.value.data[0].sequenceNbr
    );
    page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
    delBatchData();
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    isBlock: type,
    sequenceNbr: currentInfo.value.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  let index = tableData.value.findIndex(
    x => x.sequenceNbr === currentInfo.value.sequenceNbr
  );
  if (isBatchDelete.value) {
    let index = tableData.value.findIndex(
      x => x.sequenceNbr === selectData.value.data[0].sequenceNbr
    );
    page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
    delBatchData();
    return;
  }
  api
    .delFbData(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');
        page.value = Math.ceil((index + 1) / limit.value);
        if (
          currentInfo.value.kind === '01' ||
          currentInfo.value.kind === '02'
        ) {
          emits('updateMenuList');
        }
        queryBranchDataById();
      } else {
        message.error(res.message);
        deleteVisible.value = false;
        deleteLoading.value = false;
        selectData.value.data = [];
      }
    })
    .catch(err => {
      console.log('----------分部分项删除数据', err);
      message.error('删除失败');
      selectData.value.data = [];
      deleteLoading.value = false;
      deleteVisible.value = false;
    });
};

const menuConfig = reactive({
  className: 'my-menus-subItem',
  body: {
    options: [
      [
        {
          code: 'add',
          name: '插入',
          children: [
            {
              code: 0,
              name: '添加分部',
              kind: '01',
              visible: true,
              disabled: true,
            },
            {
              code: 1,
              name: '添加子分部',
              kind: '02',
              visible: true,
              disabled: true,
            },
            {
              code: 2,
              name: '添加清单',
              kind: '03',
              visible: true,
              disabled: true,
            },
            {
              code: 3,
              name: '添加定额',
              kind: '04',
              visible: true,
              disabled: true,
            },
          ],
        },
        {
          code: 'copy',
          name: '复制',
          visible: true,
          disabled: false,
        },
        {
          code: 'paste',
          name: '粘贴',
          visible: true,
          disabled: false,
        },
        {
          code: 'delete',
          name: '删除',
          visible: true,
          disabled: true,
        },
        {
          code: 'lock',
          name: '清单锁定',
          visible: true,
          disabled: false,
        },
        {
          code: 'pageColumnSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    // frameSelectRef.value?.clearSelect();
    currentInfo.value = row;
    projectStore.SET_SUB_CURRENT_INFO(row);
    vexTable.value.setCurrentRow(row);
    deleteStateFn();
    let index = selectData.value?.data.findIndex(
      x => x.sequenceNbr === row.sequenceNbr
    );
    if (index === -1 && selectData.value?.data.length > 1) {
      selectData.value?.data.push(row);
    }
    options.forEach(list => {
      list.forEach(async (item, index) => {
        item.disabled = false; // 初始化重置一下
        if (!copyData.value && item.code === 'paste') {
          item.disabled = true;
        }
        if (copyData.value && item.code === 'paste') {
          item.disabled = false;
          //粘贴代码中已经有此逻辑判断-注释
          try {
            await frameSelectRef.value.frameSelectJs.isPasteBranch(
              row,
              copyData.value
            );
            item.disabled = false;
          } catch (error) {
            item.disabled = true;
          }
        }
        if (item.code === 'delete') {
          if (
            (currentInfo.value.optionMenu.includes(4) ||
              currentInfo.value.optionMenu.includes(5)) &&
            !currentInfo.value.isLocked
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        }
        if (item.code === 'lock') {
          if (projectStore.standardGroupOpenInfo.isOpen) {
            item.disabled = true;
          } else {
            if (currentInfo.value.kind === '03') {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
            if (currentInfo.value.isLocked) {
              item.name = '清单解锁';
            } else {
              item.name = '清单锁定';
            }
          }
        } else if (item.code === 'tempDelete') {
          let parentInfo = renderedList.value.filter(
            x => x.sequenceNbr === currentInfo.value.parentId
          )[0];
          if (
            (currentInfo.value.kind === '03' && !currentInfo.value.isLocked) ||
            (currentInfo.value.kind === '04' && !parentInfo.tempDeleteFlag)
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
          if (currentInfo.value.tempDeleteFlag) {
            item.name = '取消临时删除';
          } else {
            item.name = '临时删除';
          }
        }
        if (
          item.children &&
          !['batchDelete', 'noteList', 'MainList'].includes(item.code)
        ) {
          item.disabled = false;
          item.children.forEach(childItem => {
            childItem.disabled = true;
            currentInfo.value.optionMenu.forEach(child => {
              if (child === childItem.code) {
                childItem.disabled = false;
              }
            });
          });
        }
        if (item.code === 'add') needAddQDandFB(item);

        handleNote(item, row);
        handleMainList(item, row);
        contractOriginalFlagInsert(item, currentInfo.value);
      });
    });
    return true;
  },
});

const contextMenuClickEvent = async ({ menu, row }) => {
  console.log('🚀 ~ contextMenuClickEvent ~ menu:', menu);
  if (menu?.fatherCode === 'noteList' || menu.code == 'noteList') {
    if (menu.code != 'noteList') {
      handleNoteClick(menu, row);
    }
  } else if (menu?.fatherCode === 'MainList' || menu.code == 'MainList') {
    if (menu.code != 'MainList') {
      handleMainListClick(menu, row);
    }
  } else if (menu.code === 'delete') {
    deleteType();
  } else if (menu.code === 'copy') {
    console.log(selectData.value.data);
    //和ctrl+v逻辑代码保持一致
    // const handleData =
    //   await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
    //     selectData.value.data
    //   );
    // selectData.value.data = handleData;
    // // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
    // if (handleData.length) {
    // selectData.value.isCopy = true;
    // }
    // copyFun();
    copyFun();
  } else if (menu.code === 'paste') {
    pasteFun();
  } else if (menu.code === 'lock') {
    if (row.isLocked === 1) {
      fbUnLockQd();
    } else {
      fbLockQd();
    }
  } else if (menu.code === 'pageColumnSetting') {
    showPageColumnSetting();
  } else if (menu.code === 'tempDelete') {
    updateDelTempStatusColl();
  } else if (
    menu.code === 'batchDelete-child1' ||
    menu.code === 'batchDelete-child2'
  ) {
    batchDeleteVisible.value = true;
    batchDataType.value = menu.type;
  } else if (
    menu.code !== 'add' &&
    menu.code !== 'batchDelete' &&
    typeof menu.code === 'number'
  ) {
    addData(menu.kind);
  }
  jieSuanMenuClickEvent(menu);
};

const rowClassName = ({ row }) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.tempDeleteFlag) {
    ClassStr = 'temp-delete';
  }

  if (row.sequenceNbr == renderedList.value[0]?.sequenceNbr) {
    ClassStr += ' first-row';
  }
  const originalName = originalDataClass(row);
  return `${ClassStr} ${originalName}`;
};

const cellStyle = ({ row, column }) => {
  if (['bdCode'].includes(column.field)) {
    return {
      paddingLeft: row.customLevel * 12 + 'px',
    };
  }
};

const headerClass = column => {
  if (column?.align === 'center') {
    return 'headerCenter';
  } else if (column?.align === 'right') {
    return 'headerRight';
  } else {
    return 'headerLeft';
  }
};

const cellClassName = ({
  column,
  columnIndex,
  $columnIndex,
  row,
  rowIndex,
  $rowIndex,
}) => {
  let className = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'bdCode') {
    className += ' code-color ' + `Virtual-pdLeft${row.customLevel} `;
  } else if (column.field === 'index') {
    className += ' index-bg';
  }

  // 批注提示
  if (['name', 'bdName'].includes(column.field) && row?.annotations) {
    className += ' note-tips ';
  }

  if (column.field === 'bdCode' && row.kind !== '04') {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line}`;
  }

  // 添加默认两行类名
  if (
    [
      'qfCode',
      'measureType',
      'description',
      'costMajorName',
      'projectAttr',
    ].includes(column.field) ||
    (column.field === 'bdCode' && row.kind === '04')
  ) {
    const line = row.maxLine || 2;
    className += ` cell-line-break-${line}`;
  }

  if (['projectAttr'].includes(column.field)) {
    className += ` projectAttr-item `;
  }

  return className;
};

// 编辑弹框关闭方法
const editCancel = () => {
  isShowModel.value = false;
};

const tableColumn = ref([
  { field: 'bdCodeLevel04', title: '项目编码' },
  { field: 'bdNameLevel04', title: '项目名称' },
  { field: 'unit', title: '单位' },
]);
const tableList = ref([]);

const keyupEvent = (row, e) => {
  if (row.kind !== '03') return;
  if (e.value.length > 1) {
    const $pulldown = pulldownRef.value;
    console.log('keyupEvent', $pulldown);
    if ($pulldown) {
      $pulldown[0]?.showPanel();
    }
    searchQdByCode(e.value);
  }
};

const cellClickEvent = ({ row }) => {
  addCurrentInfo.value = row;
  const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
  row.unit = unit;
  const $pulldown = pulldownRef.value;
  if ($pulldown) {
    const $table = vexTable.value;
    if ($table) {
      isClearEdit.value = true;
      $table.clearEdit();
    }
    if (row.unit && row.unit.length > 1) {
      showUnitTooltip.value = true;
      showUnitTooltipType.value = 'code';
    } else {
      updateQdByCode(row.bdCodeLevel04, row.unit[0]);
    }
    $pulldown[0]?.hidePanel();
    isClearEdit.value = false;
  }
};

// 根据编码模糊搜索标准清单
const searchQdByCode = code => {
  api.searchQdByCode({ code: code }).then(res => {
    if (res.status === 200 && res.result) {
      tableList.value = res.result;
    }
  });
};

// 通过标准编码插入清单
const updateQdByCode = (code, unit) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    code: code,
    unit: unit,
    isSortQdCode: isSortQdCode.value,
  };
  api.updateQdByCode(apiData).then(res => {
    if (res.status === 200 && res.result) {
      selectUnit.value = '';
      if (currentInfo.value.standardId) {
        message.success('清单替换成功');
      } else {
        message.success('清单插入成功');
      }
      queryBranchDataById();
    }
  });
};

const saveData = inputData => {
  if (bdCode.value) {
    updateQdByPage(inputData);
  } else {
    addBcQdData(inputData);
  }
};

const deSaveData = inputData => {
  if (bdCode.value) {
    updateDeByPage(inputData);
  } else {
    addBcDeData(inputData);
  }
};

const rcjSaveData = inputData => {
  if (bdCode.value) {
    spRcjByPage(inputData);
  } else {
    addBjqBcRcjData(inputData);
  }
};

// 通过修改编码补充清单替换当前行数据
const updateQdByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
  };
  api.updateQdByPage(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

const updateDeByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
  };
  api.updateDeByPage(apiData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};
// 点击补充按钮补充清单数据
const addBcQdData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };

  api.addBcQdData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

// 点击补充按钮补充定额数据
const addBcDeData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    type: 1,
  };

  api.addBcDeData(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};

// 分部分项 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentUpdateData.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    region: 0,
  };
  api.spRcjByPage(apiData).then(res => {
    addDataSequenceNbr.value = currentUpdateData.value.sequenceNbr;
    message.success('人材机替换成功');
    rcjVisible.value = false;
    queryBranchDataById();
  });
};
// 分部分项 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    region: 0,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      rcjVisible.value = false;
      message.success('人材机新建成功');
      queryBranchDataById();
    }
  });
};
const showModel = type => {
  isPriceModel.value = true;
  switch (type) {
    case 'zscy':
      // 装饰垂运
      showModelType.value = 'zscy';
      showPriceTitle.value = '设置装饰垂运';
      break;
    case 'zscg':
      // 装饰超高
      showModelType.value = 'zscg';
      showPriceTitle.value = '设置装饰超高降效';
      break;
    case 'azfy':
      // 安装费用
      showModelType.value = 'azfy';
      showPriceTitle.value = '安装费用计取';
      break;
    case 'commercial':
      // 安装费用
      showModelType.value = 'commercial';
      showPriceTitle.value = '计算预拌混凝土泵送增加费';
      break;
    // case 'csfy':
    //   // 自动计算措施费用
    //   showModelType.value = 'csfy';
    //   showPriceTitle.value = '自动记取总价措施';
    //   break;
  }
};
// 记取费用数据更新
const updateData = async () => {
  isPriceModel.value = false;
  queryBranchDataById();
  queryFeeFileData();
  // await queryDjgcFileData();
  querySzType();
  // queryUnit();
};

const closePriceModel = () => {
  isPriceModel.value = false;
};

// 整体锁定
const allLock = () => {
  if (lockFlag.value) {
    fbUnLockAll();
  } else {
    fbLockAll();
  }
};

// 清单整体锁定
const fbLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体解锁';
      queryBranchDataById();
    }
  });
};

// 清单整体解锁
const fbUnLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitUnLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体锁定';
      queryBranchDataById();
    }
  });
};

// 清单锁定
const fbLockQd = () => {
  if (!showInfo()) return;
  currentInfo.value.isLocked = 1;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.fbLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      queryBranchDataById();
    }
  });
};

// 清单解锁
const fbUnLockQd = () => {
  if (!showInfo()) return;
  currentInfo.value.isLocked = 0;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.fbUnLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      queryBranchDataById();
    }
  });
};

// 补充页面点击取消操作 type 1 清单 2 定额 3 人材机
const bcCancel = type => {
  if (type === 1) {
    qdVisible.value = false;
  } else if (type === 2) {
    deVisible.value = false;
  } else {
    rcjVisible.value = false;
  }
  if (bdCode.value) {
    currentInfo.value.bdCode = currentInfo.value.originalBdCode;
  }
};

// 清单多单位时选择单位确定事件
const selectHandler = dataRef => {
  if (!selectUnit.value) {
    return message.warning('请选择单位');
  }
  showUnitTooltip.value = false;
  dataRef.unit = selectUnit.value;
  let obj = {
    bdCode: dataRef.bdCodeLevel04,
    name: dataRef.bdNameLevel04,
    sequenceNbr: dataRef.sequenceNbr,
    unit: dataRef.unit,
    quantityExpression: dataRef.quantityExpression,
    libraryCode: dataRef.libraryCode,
  };

  console.log('🚀 ~ selectHandler ~ name:', name);
  updateQdByCode(obj.bdCode, obj.unit);
};

// 批量删除
const delBatchData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
  };
  api.fbBatchDelete(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      if (currentInfo.value.kind === '01' || currentInfo.value.kind === '02') {
        emits('updateMenuList');
      }
      queryBranchDataById();
    } else {
      message.error(res.message);
      deleteVisible.value = false;
      deleteLoading.value = false;
    }
  });
};

// 获取当前点击行下挂所有数据
/**
 * 以前的这里也修改了用户选择的数据，用户选择了数据，然后又在这掉接口了，不知道为啥，待以后优化吧
 */
const queryAllDataByBranchId = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: currentInfo.value?.sequenceNbr,
    pageSize: 10000,
    pageNum: page.value,
  };
  api.searchForSequenceNbr(apiData).then(async res => {
    if (res.status === 200 && res.result) {
      // 后端给的所有的数据，需要过滤。
      const handleData =
        await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
          res.result
        );
      selectData.value.data = handleData;
      // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
      if (handleData.length) {
        selectData.value.isCopy = true;
      }
    }
  });
};

const batchPasteQdDeData = async () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  await jsApi.updatePasteDataColl({
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: 1,
  });
  console.log('粘贴数据', apiData);
  api.fbBatchPaste(apiData).then(async res => {
    if (res.status === 200 && res.result) {
      console.log('粘贴数据', apiData, res.result);
      // updatePasteLineHtDataColl(addDataSequenceNbr.value);
      message.success('粘贴成功');
      queryBranchDataById();
    } else {
      message.error('粘贴失败');
    }
  });
};

// 分部分项定额上移下移
const moveDeData = ({ state, type }) => {
  console.log(state, type);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    upId: projectStore.currentTreeInfo?.id,
    selectId: currentInfo.value.sequenceNbr,
    operateAction: state === 1 ? 'up' : 'down',
    type: 'fbfx',
  };
  if (type === 'move') {
    api.moveDeData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('移动成功');
        emits('updateMenuList');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById();
      }
    });
  }
  if (type === 'level') {
    api.fbDataUpAndDownController(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('移动成功');
        emits('updateMenuList');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById();
      }
    });
  }
};

// 组价方案匹配条件筛选
const filterData = val => {
  let tempList = [];
  tableData.value = [];
  if (val.length === 0 || !val) {
    tableData.value = originalTableData.value;
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  } else {
    originalTableData.value.forEach(item => {
      if (val.includes(item.matchStatus)) {
        tempList.push(item.sequenceNbr);
      }
    });
    for (let i = 0; i < originalTableData.value.length; i++) {
      if (
        tempList.includes(originalTableData.value[i].sequenceNbr) ||
        tempList.includes(originalTableData.value[i].parentId)
      ) {
        tableData.value.push(originalTableData.value[i]);
      }
    }
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  }
  // const initList = init(tableData.value);
  // nextTick(() => {
  //   initList();
  // });
};

const copyTableToClipboard = tableElement => {
  const rows = Array.from(tableElement.querySelectorAll('.multiple-check'));
  const theadRows = Array.from(tableElement.querySelectorAll('thead  tr'));
  const tempTable = document.createElement('table');
  const tempEl = document.createDocumentFragment();

  // 表格头部
  theadRows.forEach(row => {
    const tempRow = document.createElement('tr');
    const tds = row.querySelectorAll('th');
    if (tds.length) {
      tds.forEach(c => {
        const tempCell = document.createElement('td');
        tempCell.textContent = c.textContent.trim();
        tempCell.style.color = '#000';
        tempCell.style.fontWeight = 'bold';
        tempRow.appendChild(tempCell);
      });
    }
    tempRow.style.background = '#f3f3f3';

    tempEl.appendChild(tempRow);
  });

  rows.forEach(row => {
    const tempRow = document.createElement('tr');
    const tds = row.querySelectorAll('td');
    if (tds.length) {
      tds.forEach(c => {
        const tempCell = document.createElement('td');
        tempCell.textContent = c.textContent.trim();

        const color = window.getComputedStyle(c).getPropertyValue('color');
        if (color) {
          tempCell.style.color = color;
        }

        const childElement = c.querySelector('.code-flag');
        if (childElement) {
          tempCell.style.color = window
            .getComputedStyle(childElement)
            .getPropertyValue('color');
        }

        tempRow.appendChild(tempCell);
      });

      // 行背景
      let bgColor = '#fff';
      let classLists = row.classList;
      if (classLists.contains('row-unit')) {
        bgColor = '#f0ecf2';
      } else if (classLists.contains('row-sub')) {
        bgColor = '#ececec';
      } else if (classLists.contains('row-qd')) {
        bgColor = '#f0f3fb';
      }
      if (bgColor) {
        tempRow.style.background = bgColor;
      }
    }

    tempEl.appendChild(tempRow);
  });

  tempTable.appendChild(tempEl);

  document.body.appendChild(tempTable);

  try {
    // 创建一个选择区域，选中临时元素内的所有内容
    const range = document.createRange();
    range.selectNodeContents(tempTable);
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    var aFileParts = [serializeFragment(range.cloneContents()).toString()];
    var oMyBlob = new Blob(aFileParts, { type: 'text/html' });
    navigator.clipboard
      .write([
        new ClipboardItem({
          'text/html': oMyBlob,
        }),
      ])
      .then(() => {
        console.log('HTML content copied to clipboard.');
      });

    // navigator.clipboard.writeText(selection.toString());
    selection.removeAllRanges();
  } catch (err) {
    console.error('Failed to copy table:', err);
  } finally {
    // 移除临时元素
    document.body.removeChild(tempTable);
  }
};

const serializeFragment = fragment => {
  const div = document.createElement('div');
  div.appendChild(fragment);
  return `<table border="1"  cellspacing="0" cellpadding="0">${div.innerHTML}</table>`;
};

// 复用组价
let ReuseGroupPriceRef = ref(null);

const openReuseGroupPrice = ({ activeKind }) => {
  console.log('🚀 ~', activeKind);
  if (!ReuseGroupPriceRef.value) {
    console.log('🚀 ~ openReuseGroupPrice ~ ReuseGroupPriceRef:');
    nextTick(() => {
      openReuseGroupPrice({ activeKind });
    });
    return;
  }
  if ([0, 1, 2].includes(activeKind)) {
    ReuseGroupPriceRef.value.open(activeKind);
  }
};

// 清单快速组价
const qdQuickPricingRef = ref(null);
const openQdQuickPricing = () => {
  qdQuickPricingRef.value.open('fbfx');
};

const standardGroupRef = ref(null);
const openStandardGroupPrice = () => {
  standardGroupRef.value.open('fbfx');
};
const costMajorNameChange = row => {
  //取费文件变更
  // console.log('costMajorNameChange');
  // let target = feeFileList.value.find(i => i.qfName === row.costMajorName);
  // row.qfCode = target.qfCode; //编辑区内切换【取费文件】时会默认带出与其关联的标准模板的【单价构成文件】
};
const qfCodeChange = row => {
  //单价构成文件变更--
  // let target = djgcFileList.value.find(i => i.qfCode === row.qfCode);
  // if (!(target.qfName.indexOf('_') === -1)) return; //仿制模板变更不影响取费文件对应字段变更
  // row.costMajorName = target.qfName; //编辑区内切换【取费文件】时会默认带出与其关联的标准模板的【单价构成文件】
};
const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'jieSuan',
  initCallback: () => {},
  initColumnsCallback: () => {
    initColumns({
      columns: projectStore.currentTreeInfo?.originalFlag
        ? originalTableColumns.value
        : tableColumns.value,
      pageName: `${
        projectStore.currentTreeInfo?.originalFlag ? 'htnfbfx' : 'htwfbfx'
      }`,
    });
  },
});

// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};

const {
  quantityDifferenceProportionStyle,
  originalFlagDisabled,
  originalDataClass,
  originalEditIntercept,
  lockPriceFlagEditIntercept,
  stageEditIntercept,
  updateNewAddHtDataColl,
  originalFlagMenuConfigHandler,
  accordingDocumentVisible,
  associationContractVisible,
  jieSuanBusHandler,
  contractOriginalFlagInsert,
  contractOriginalFlagMenuInsert,
  jieSuanMenuClickEvent,
  quantityExpressionTextHandler,
} = commonJieSuan({
  renderedList: renderedList,
  tableData: tableData,
  refresh: queryBranchDataById,
});

defineExpose({
  copyAndPaste,
  posRow,
  selectData,
  queryBranchDataById,
});
</script>
<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped></style>
