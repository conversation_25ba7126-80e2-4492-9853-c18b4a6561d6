<!--
 * @Descripttion: 特征及内容
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: wangru
 * @LastEditTime: 2024-05-29 17:37:15
-->
<template>
  <div class="contents-table">
    <div
      class="head"
      v-show="
        !['0', '01', '02', '04'].includes(props.currentInfo?.kind) &&
        !props.currentInfo?.tempDeleteFlag
      "
    >
      <a-button
        type="text"
        @click="addLine(null)"
        :disabled="props.currentInfo.isLocked"
      ><icon-font type="icon-biaodan-charu"></icon-font>插入</a-button>

      <a-button
        type="text"
        @click="moveFeature(1)"
        :disabled="props.tableData.length === 0 || currentInfo?.sort === 1"
      ><icon-font type="icon-biaodan-charu"></icon-font>上移</a-button>

      <a-button
        type="text"
        @click="moveFeature(0)"
        :disabled="
          props.tableData.length === 0 ||
          currentInfo?.sort === (props.tableData && props.tableData.length)
        "
      ><icon-font type="icon-biaodan-charu"></icon-font>下移</a-button>

      <a-button
        type="text"
        @click="removeFeature"
        :disabled="currentInfo?.isDefault !== 1"
      ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button>
    </div>
    <div class="content">
      <vxe-table
        ref="vexTable"
        :data="props.tableData"
        show-overflow
        keep-source
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :column-config="{ resizable: true }"
        @current-change="currentChangeEvent"
        @edit-closed="editClosedEvent"
        height="auto"
        class="table-scrollbar table-edit-common"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
          enabled: isEditEnabled,
        }"
        :cell-class-name="selectedClassName"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, cellClickEvent);
          }
        "
      >
        <vxe-column width="60">
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          title="特征"
          field="featureName"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              v-model="row.featureName"
              v-if="row.isDefault === 1"
              type="text"
              placeholder="请输入特征名称"
            ></vxe-input>
            <template v-else>{{ row.featureName }}</template>
          </template>
        </vxe-column>
        <vxe-column
          title="特征值"
          field="featureValue"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <!-- <vxe-input
              v-model="row.featureValue"
              type="text"
              placeholder="请输入特征值"
            ></vxe-input> -->
            <vxeTableEditSelect
              :transfer="true"
              :filedValue="row.featureValue"
              :maxlength="2000"
              :isNotLimit="true"
              :list="row.featureValueOptions || []"
              @update:filedValue="
                newValue => {
                  row.featureValue = newValue;
                }
              "
            ></vxeTableEditSelect>
          </template>
        </vxe-column>
        <vxe-column
          title="输出"
          field="checkFlag"
        >
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.checkFlag"
              :checked-value="1"
              :unchecked-value="0"
              :disabled="checkFlagDisabled"
              @change="editQdFeature(row, 'checkFlag')"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script setup>
import api from '@/api/projectDetail';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { watch, ref, onMounted, nextTick } from 'vue';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();
const props = defineProps(['tableData', 'currentInfo', 'fatherLoading']);
const emits = defineEmits(['updateData']);
const projectStore = projectDetailStore();

const currentInfo = ref(null);
const vexTable = ref();
let isEditEnabled = ref(true); // 是否可编辑行

let checkFlagDisabled = ref(true);
const setCheckFlagDisabled = () => {
  checkFlagDisabled.value =
    props.currentInfo.isLocked ||
    props.currentInfo.tempDeleteFlag ||
    props.fatherLoading;
};
watch(
  () => props.fatherLoading,
  (val, old) => {
    if (val) {
      setCheckFlagDisabled();
    } else {
      setTimeout(() => {
        // 防止输出连续选择过快，编辑区数据还没有处理完加的延时
        setCheckFlagDisabled();
      }, 600);
    }
  },
  {
    immediate: true,
  }
);
// 选中单条分部分项数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
  setEditEnabled();
};

const cellClickEvent = ({ column, row }) => {
  if (column.field === 'featureValue') {
    const params = {
      featureName: row.featureName,
      qdCode: props.currentInfo.bdCode,
    };
    api.listFeatureDownPullMenu(params).then(res => {
      console.log(res);
      if (res.code === 200) {
        row.featureValueOptions = res.result;
      }
    });
  }
  return true;
};

// 单元格退出编辑事件
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  if ($table.isUpdateByRow(row, field)) {
    editQdFeature(row, field);
  }
};

// 编辑数据
const editQdFeature = (row, field) => {
  let apiData = {
    upDateLine: JSON.parse(JSON.stringify(row)),
    qdId: props.currentInfo.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.editQdFeature(apiData).then(res => {
    console.log('编辑', res);
    if (res.status === 200 && res.result) {
      message.success('修改成功');
      if (field === 'checkFlag' || (row.checkFlag && field !== 'checkFlag')) {
        emits('updateData', 1);
      } else {
        emits('updateData', field === 'featureValue' ? 1 : 0);
      }
    }
  });
};
watch(
  () => props.tableData,
  (newVal, oldVal) => {
    if (props.tableData && props.tableData.length > 0) {
      if (newVal.length === oldVal.length + 1) {
        let change = newVal.filter(
          x => !oldVal.some(y => y.sequenceNbr === x.sequenceNbr)
        );
        console.log('--------------', change);
        vexTable.value.setCurrentRow(change[0]);
        currentInfo.value = change[0];
      } else if (oldVal.length === newVal.length + 1) {
        vexTable.value.setCurrentRow(props.tableData[0]);
        currentInfo.value = props.tableData[0];
      }
      nextTick(() => {
        setEditEnabled();
      });
    }
    // else {
    //   props.tableData = [];
    // }
  }
);
onMounted(() => {
  if (!currentInfo.value && props.tableData && props.tableData.length > 0) {
    vexTable.value.setCurrentRow(props.tableData[0]);
    currentInfo.value = props.tableData[0];
  }
  setEditEnabled();
});
// 插入数据
const addLine = row => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    fbFxQdId: props.currentInfo.sequenceNbr,
    pointLine: !currentInfo.value
      ? null
      : JSON.parse(JSON.stringify(currentInfo.value)),
  };
  console.log('apiDAta', apiData);
  api.addLine(apiData).then(res => {
    console.log('新增', res);
    message.success('插入成功');
    if (res.status === 200 && res.result) {
      emits('updateData');
    }
  });
};

// 删除数据
const removeFeature = () => {
  if (!currentInfo.value) return;
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    fbFxQdId: props.currentInfo.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  console.log('删除数据', apiData);
  api.removeFeature(apiData).then(res => {
    console.log('removeFeature', res);
    if (res.status === 200 && res.result) {
      message.success('删除成功');
      emits('updateData', currentInfo.value.featureValue ? 1 : 0);
    }
    currentInfo.value = null;
  });
};

// 上移下移
const moveFeature = direction => {
  if (!currentInfo.value) return;
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    fbFxQdId: props.currentInfo.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    direction: direction,
  };
  console.log('移动参数', apiData);
  api.moveFeature(apiData).then(res => {
    console.log('移动数据', res);
    if (res.status === 200 && res.result) {
      emits('updateData', currentInfo.value.featureValue ? 1 : 0);
    }
  });
};

// 是否编辑处理
const setEditEnabled = () => {
  if (props.currentInfo.isLocked || props.currentInfo.tempDeleteFlag) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};
</script>

<style lang="scss" scoped>
.contents-table {
  height: 100%;
  .content {
    height: calc(100% - 40px);
  }
}
</style>
