/*
 * @Author: renmingming
 * @Date: 2023-05-17 09:28:32
 * @LastEditors: renmingming
 * @LastEditTime: 2024-06-27 10:42:56
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';

export function constructLevelTreeStructureList(id) {
  return ipc.invoke(
    ipcApiRoute.generateLevelTreeNodeStructure,
    JSON.parse(
      JSON.stringify({
        sequenceNbr: id,
      })
    )
  );
}

export default {
  /**
   * 人材机汇总，列表页面
   */
  ysshRcjCollectComparison(params) {
    return ipc.invoke(ipcApiRoute.getysshRcjCollectComparison, params);
  },
  /**
   * 对比匹配保存项目
   * @param {*} params
   * @returns
   */
  shSaveData(params) {
    return ipc.invoke(ipcApiRoute.shSaveData, params);
  },
  /**获取单位预算审核造价分析
   *
   * @param {*} params
   * @returns
   */
  getCostAnalysisData(params) {
    return ipc.invoke(ipcApiRoute.getCostAnalysisDataSH, params);
  },
  /**获取预算审核造价分析
   *
   * @param {*} params
   * @returns
   */
  updateCostAnalysis(params) {
    return ipc.invoke(ipcApiRoute.updateCostAnalysisSH, params);
  },

  /**
   * 点击获取预算审核工程基本信息、工程特征
   */
  getBasicInfo(params) {
    return ipc.invoke(ipcApiRoute.getProjectOverviewSh, params);
  },
  /**
   * SH其他项目
   */
  getOtherProjectList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectComparisonList, params);
  },
  /**
   * SH其他项目-增减说明
   */
  updateChangeExplain(params) {
    return ipc.invoke(ipcApiRoute.updateChangeExplain, params);
  },
  /**
   * SH其他项目 暂列金额
   */
  getOtherProjectZljeList(params) {
    return ipc.invoke(
      ipcApiRoute.getOtherProjectProvisionalComparisonList,
      params
    );
  },
  /**
   * SH其他项目 获取 专业工程暂估价 列表
   */
  getOtherProjectZygcZgjList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectZygcZgjComparisonList, params);
  },
  /**
   * SH其他项目 获取 总承包服务费 列表
   */
  getOtherProjectZcbfwfList(params) {
    return ipc.invoke(
      ipcApiRoute.getOtherProjectServiceCostComparisonList,
      params
    );
  },
  /**
   * SH其他项目 获取 计日工列表
   */
  getOtherProjectJrgList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectDayWorkComparisonList, params);
  },
  /**
   * SH费用汇总
   */
  getUnitCostSummary(params) {
    return ipc.invoke(ipcApiRoute.getCostSummaryComparisonListSH, params);
  },
  /**
   * SH费用汇总-安文费明细
   */
  getSafeFee(params) {
    return ipc.invoke(ipcApiRoute.getSafeFeeSH, params);
  },
  /**
   * SH费用汇总-规费明细
   */
  getGfeeFee(params) {
    return ipc.invoke(ipcApiRoute.getGfeeFeeSH, params);
  },
  /**
   * 措施项目列表
   * @param {*} params
   * @returns
   */
  csxmListSearch(params) {
    return ipc.invoke(ipcApiRoute.csxmListSearch, params);
  },
  /**
   * 分部分项列表
   * @returns
   */
  fbfxDataPiPeiColl(params) {
    return ipc.invoke(ipcApiRoute.fbfxDataPiPeiColl, params);
  },
  /**
   * 送审到审定数据转换
   * @returns
   */
   ssToSdDataConvert(params) {
    return ipc.invoke(ipcApiRoute.ssToSdDataConvert, params);
  },
  /**
   * 审定到送审数据转换
   * @returns
   */
   sdToSsDataConvert(params) {
    return ipc.invoke(ipcApiRoute.sdToSsDataConvert, params);
  },
  /**
   * 查询 一键审取费 单价构成 和 费用汇总
   * @returns
   */
   getYjsqf(params) {
    return ipc.invoke(ipcApiRoute.getYjsqf, params);
  },
  /**
   * 修改一键审取费
   * @param {*} params
   * @returns
   */
  updataYjsqf(params) {
    return ipc.invoke(ipcApiRoute.updataYjsqf, params);
  },
   /**导入依据  导入
   *
   * @param {*} params
   * @returns
   */
  importYiJuFile(params){
    return ipc.invoke(ipcApiRoute.importYiJuFile, params);
  },
  /**导入依据  查看
   *
   * @param {*} params
   * @returns
   */
  openYiJuFile(params){
    return ipc.invoke(ipcApiRoute.openYiJuFile, params);
  },
  /**导入依据  删除
   *
   * @param {*} params
   * @returns
   */
  removeYiJuFile(params){
    return ipc.invoke(ipcApiRoute.removeYiJuFile, params);
  },
  /**转为预算 预览
   *
   * @param {*} params
   * @returns
   */
   shYsfSaveLocation(params){
    return ipc.invoke(ipcApiRoute.shYsfSaveLocation, params);
  },
  /**转为预算  保存
   *
   * @param {*} params
   * @returns
   */
  shProjectToBudget(params){
    return ipc.invoke(ipcApiRoute.shProjectToBudget, params);
  },
  /**
   * 查询某个清单可选择的清单关联项
   * @param {*} params
   * @returns
   */
   queryQdAssociation(params) {
    return ipc.invoke(ipcApiRoute.queryQdAssociation, params);
  },
  /**
   * 绑定清单关联关系
   * @param {*} params
   * @returns
   */
   bindQdAssociation(params) {
    return ipc.invoke(ipcApiRoute.bindQdAssociation, params);
  },
  /**
   * 新建清单关联关系
   * @param {*} params
   * @returns
   */
   createQdAssociation(params) {
    return ipc.invoke(ipcApiRoute.createQdAssociation, params);
  },
  /**
   * 查询所有的关联项
   * @param {*} params
   * @returns
   */
   queryAllQdAssociationList(params) {
    return ipc.invoke(ipcApiRoute.queryAllQdAssociationList, params);
  },
  /**
   * 重点项过滤 默认数据
   * @param {*} params
   * @returns
   */
  getDefaultZdxglData(params) {
    return ipc.invoke(ipcApiRoute.getDefaultZdxglData, params);
  },
  /**
   * 重点项过滤 获取 单位工程数据
   * @param {*} params
   * @returns
   */
  getUnitZdxglData(params) {
    return ipc.invoke(ipcApiRoute.getUnitZdxglData, params);
  },
  /**
   * 重点项过滤 清除 单位工程数据
   * @param {*} params
   * @returns
   */
  deleteUnitZdxglData(params) {
    return ipc.invoke(ipcApiRoute.deleteUnitZdxglData, params);
  },
  /**
   * 重点项过滤 修改 单位工程数据
   * @param {*} params
   * @returns
   */
  updateUnitZdxglData(params) {
    return ipc.invoke(ipcApiRoute.updateUnitZdxglData, params);
  },
  /**
   * 分析与报告 - 预览word
   */
  createWordData(params) {
    return ipc.invoke(ipcApiRoute.createWordData, params);
  },
  /**
   * 分析与报告 - 预览word
   */
  readCreateWordData(params) {
    return ipc.invoke(ipcApiRoute.readCreateWordData, params);
  },
  /**
   * 分析与报告 - 导出word
   */
  downloadWordFile(params) {
    return ipc.invoke(ipcApiRoute.downloadWordFile, params);
  },
  /**
   * 导出excel返回某一栏目下的数据
   * @param {*} constructId
   * @param {*} lanMuName
   * @returns
   */
  exportProjectTreeSH(constructId, lanMuName) {
    return ipc.invoke(ipcApiRoute.shqueryLanMuData, {
      constructId,
      lanMuName,
    });
  },
  exportPdfFileSH(params) {
    return ipc.invoke(ipcApiRoute.shexportPdfFile, {
      ...params,
    });
  },
  // 导出excel
  exportExcelSH(params) {
    return ipc.invoke(ipcApiRoute.shexportExcelZip, params);
  },
  getBottomSummary(formData) {
    return ipc.invoke(ipcApiRoute.getBottomSummary, formData);
  },
  /**
   * 组合键保存
   * @param {*} constructId
   * @returns
   */
  saveShfFile(constructId) {
    return ipc.invoke(ipcApiRoute.saveShfFile, {
      constructId,
    });
  },
  generateXml(params) {
    return ipc.invoke(ipcApiRoute.generateXml, {
      ...params,
    });
  },
  // 通过工程项目id查询工程项目配置信息
  getConstructConfigByConstructId(constructSequenceNbr) {
    return ipc.invoke(ipcApiRoute.getConstructConfigByConstructId, {
      constructId: constructSequenceNbr,
    });
  },
  showExportHeadLineSH(params) {
    return ipc.invoke(ipcApiRoute.shshowExportHeadLine, {
      ...params,
    });
  },
  showSheetStyleSH(params) {
    return ipc.invoke(ipcApiRoute.shshowSheetStyle, {
      ...params,
    });
  },
  /**
   * 对比匹配取消事件
   */
  shRecoveryData(params) {
    return ipc.invoke(ipcApiRoute.shRecoveryData, params);
  },
  /**
   * 费用汇总修改关联关系
   */
  changeCostSummaryRelation(params) {
    return ipc.invoke(ipcApiRoute.changeCostSummaryRelation, params);
  },
  /**
   * 其他项目修改关联关系
   */
  updateMatch(params) {
    return ipc.invoke(ipcApiRoute.updateMatch, params);
  },
  /**
   * 措施项目修改关联关系
   */
  changeMeasureRelation(params) {
    return ipc.invoke(ipcApiRoute.changeMeasureRelation, params);
  },
  /**
   * 人材机更改匹配关联
   */
  unitRcjChangeGL(params) {
    return ipc.invoke(ipcApiRoute.unitRcjChangeGL, params);
  },
  /**
   * 明细区的人材机显示模块
   */
  rcjComparison(params) {
    return ipc.invoke(ipcApiRoute.rcjComparison, params);
  },
  /**
   * 明细区的单价构成显示模块
   */
  feeBuildComparison(params) {
    return ipc.invoke(ipcApiRoute.feeBuildComparison, params);
  },
  /**
   * 预算审核另存为
   */
  shFileSaveAs(params) {
    return ipc.invoke(ipcApiRoute.shFileSaveAs, JSON.parse(
        JSON.stringify({
          constructId: params,
        })
    ));
  },
  /**
   * 预算审核文件-打开
   */
  shFileOpenProject(params) {
    return ipc.invoke(ipcApiRoute.shFileOpenProject, params);
  },
  /**
   * 打开修改送审
   */
  openSongShen(params) {
    return ipc.invoke(ipcApiRoute.openSongShen, params);
  },
  /**
   * 应用修改送审数据
   */
  saveSongShenDatas(params) {
    return ipc.invoke(ipcApiRoute.saveSongShenDatas, params);
  },
  /**
   * 取消应用送审
   */
  removeSongShenDatas(params) {
    return ipc.invoke(ipcApiRoute.removeSongShenDatas, params);
  },
  /**
   * 获取单位工程信息
   * @param {*} params 
   * @returns 
   */
  getUnitProject(params) {
    return ipc.invoke(ipcApiRoute.getUnitProject, params);
  }
};
