const {Service} = require("../../../core");
const ProjectDomain = require('../domains/ProjectDomain');
const {ObjectUtils} = require('../utils/ObjectUtils');
const {Snowflake} = require("../utils/Snowflake");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {GljProjectOverview} = require('../models/GljProjectOverview');
const {GsProjectOrgInstructions} = require('../models/GsProjectOrgInstructions');
const gsjbxx = require("../jsonData/glj_jbxx.json");
const gsUnitJbxx = require("../jsonData/glj_unit_jbxx.json");
const gsUnitGclx = require("../jsonData/glj_unit_gctz.json");
const gsUnitGclxAz = require("../jsonData/glj_unit_gctz_az.json");
const gsUnitGclxSz = require("../jsonData/glj_unit_gctz_sz.json");
const gsUnitGclxLh = require("../jsonData/glj_unit_gctz_lh.json");
const gljJbxxType = require('../jsonData/glj_jbxx_type.json');
const {listeners} = require("process");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ResponseData} = require("../utils/ResponseData");
const xeUtils = require("xe-utils");
const ConstructMajorTypeEnum = require("../enums/ConstructMajorTypeEnum");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const DeTypeConstants = require("../constants/DeTypeConstants");

/**
 * 工程基本信息  service
 */
class GljOverviewService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    getChildrenMenuList(args) {
        let array = new Array();
        let obj1 = {};
        obj1[FunctionTypeConstants.JBXX_KEY_TYPE_11] = "基本工程信息";
        let obj2 = {};
        obj2[FunctionTypeConstants.JBXX_KEY_TYPE_12] = "编制说明";
        array.push(obj1);
        array.push(obj2);

        if (args.levelType == "3") {
            let obj3 = {};
            obj3[FunctionTypeConstants.JBXX_KEY_TYPE_13] = "工程特征";
            array.push(obj3);
        }
        let treeList = {};
        treeList.itemList = array;
        return treeList;
    }

    /**
     * 保存 编制说明
     * @param args
     */
    saveOrgInstructions(args) {
        const {constructId, unitId, context} = args;
        if (ObjectUtils.isEmpty(context)) {
            context = "";
        }
        let orgInstruction = new GsProjectOrgInstructions(constructId, unitId, context);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, FunctionTypeConstants.JBXX_KEY_TYPE_12), orgInstruction);
    }

    /**
     * 11 基本信息 12 编制说明 13 特征
     * @param {*} unitId
     * @param {*} type
     * @returns
     */
    getDataMapKey(unitId, type) {
        if (ObjectUtils.isEmpty(unitId)) {
            unitId = "0";//保持key风格一致性
        }
        return "JBXX-" + unitId + "-" + type;
    }

    /**
     * 重新排序
     * @param array
     * @private
     */
    _reorder(array) {
        if (ObjectUtils.isEmpty(array)) {
            return;
        }
        array.forEach((item, index) => item.dispNo = index + 1)
    }

    /**
     * 保存列表
     * @param args
     */
    async saveList(args) {
        const {constructId, unitId, orgInstruction, type, operateType} = args;
        if (type === FunctionTypeConstants.JBXX_KEY_TYPE_12) {
            // 保存编制说明
            this.saveOrgInstructions(args);
        } else {
            let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId, type));
            let newOrg = new GljProjectOverview();
            ConvertUtil.setDstBySrc(orgInstruction, newOrg);
            if (operateType === "insert") {
                let orgIn = list.find(item => item.sequenceNbr === orgInstruction.sequenceNbr);
                let insertItem = new GljProjectOverview();
                insertItem.sequenceNbr = Snowflake.nextId();
                insertItem.addFlag = 1;
                insertItem.type = 0;
                insertItem.lockFlag = 0;
                insertItem.name = "";
                insertItem.parentId = orgIn.parentId;
                let dispNo = newOrg.dispNo;
                if (insertItem.parentId == "0") {
                    list.forEach(childItem => {
                        if (childItem.parentId === orgIn.sequenceNbr) {
                            dispNo = childItem.dispNo
                        }
                    });
                    insertItem.parentId = orgIn.sequenceNbr;
                }
                list.splice(dispNo, 0, insertItem)
            }
            if (operateType === "edit") {
                let newOrgInstruction = list.find(item => item.sequenceNbr == newOrg.sequenceNbr);
                if (newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '工程规模') {

                    let liandongDe = false;
                    let deUnitProject = null;
                    if (type === FunctionTypeConstants.JBXX_KEY_TYPE_13) {
                        let newUnitProjectModel = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId);
                        let oldAverage = newUnitProjectModel[0].average
                        newUnitProjectModel[0].average = newOrg.context;
                        liandongDe = oldAverage != newOrg.context;
                        deUnitProject = newUnitProjectModel[0];
                        // 更新费用代码工程规模
                        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                            constructId: constructId,
                            unitId: unitId,
                            qfMajorType: newUnitProjectModel.qfMajorType
                        });
                        //如果工程规模变动，联动定额使用工程规模表达式的工程量变化
                        if (liandongDe) {
                            //这里需要查询所有定额
                            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                            let functionDataMap = ProjectDomain.getDomain(constructId).functionDataMap;
                            let deGclMap = functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
                            if (ObjectUtils.isNotEmpty(deGclMap)) {
                                let deNotifySet = deGclMap.get(unitId);
                                if (ObjectUtils.isNotEmpty(deNotifySet)) {
                                    //校验quantity
                                    let priceCodes = await deDomain.getQuantityExpressionCodes(constructId, unitId, functionDataMap);
                                    for (let de of deNotifySet) {
                                        await deDomain.notifyQuantity(de, true, true, priceCodes);
                                        //联动计取安装费
                                        await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, de.deRowId, "delete");
                                    }
                                    await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
                                        unitId: unitId,
                                        singleId: null,
                                        constructId: constructId
                                    });
                                    // 更新费用代码工程规模
                                    await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                                        constructId: constructId,
                                        unitId: unitId,
                                        qfMajorType: newUnitProjectModel.qfMajorType
                                    });
                                }
                            }
                            // 同步计算工程量明细
                            await this.service.gongLiaoJiProject.gljQuantitiesService.recaculateQuantityByUnit(constructId, unitId, true);
                        }
                    } else {
                        // 修改工程项目层级的工程规模，不用联动修改单位的工程规模
                        let newUnitProjectModel = ProjectDomain.getDomain(constructId).getRoot();
                        newUnitProjectModel.average = newOrg.remark;
                    }
                }
                if (newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '工程规模单位') {
                    if (type === FunctionTypeConstants.JBXX_KEY_TYPE_13) {
                        let newUnitProjectModel = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId);
                        newUnitProjectModel[0].averageUnit = newOrg.context;
                    } else {
                        let newUnitProjectModel = ProjectDomain.getDomain(constructId).getRoot();
                        newUnitProjectModel.averageUnit = newOrg.remark;
                    }
                }
                if (newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '工程名称') {
                    let newUnitProjectModel = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId);
                    newUnitProjectModel[0].name = newOrg.remark;
                }
                if (newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '项目编号') {
                    let newUnitProjectModel = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === constructId);
                    newUnitProjectModel[0].code = newOrg.remark;
                }
                if (newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '项目名称') {
                    let newUnitProjectModel = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === constructId);
                    newUnitProjectModel[0].name = newOrg.remark;
                }
                newOrgInstruction.name = newOrg.name;
                newOrgInstruction.remark = newOrg.remark;
                newOrgInstruction.context = newOrg.context;
            }
            if (operateType === "copy") {
                newOrg.sequenceNbr = Snowflake.nextId();
                newOrg.addFlag = 1;
                let dispNo = newOrg.dispNo;
                if (newOrg.parentId === "0") {
                    list.forEach(childItem => {
                        if (childItem.parentId === newOrg.sequenceNbr) {
                            dispNo = childItem.dispNo
                        }
                    });
                }
                list.splice(dispNo, 0, newOrg)
            }
            this._reorder(list);
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, type), list);
        }
    }

    /**
     * 删除列表
     * @param args
     */
    async delete(args) {
        const {constructId, unitId, sequenceNbr, type} = args;
        let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId, type));
        list = list.filter(item => item.sequenceNbr !== sequenceNbr);
        this._reorder(list);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, type), list);
    }

    /**
     * 根据名称获取信息行
     * @param {*} constructId 项目id
     * @param {*} unitId 单位id
     * @param {*} type  FunctionTypeConstants.JBXX_KEY_TYPE_13 工程特征  FunctionTypeConstants.JBXX_KEY_TYPE_11 基本信息
     * @param {*} name  列名
     * @returns
     */
    async getByName(constructId, unitId, type, name) {
        let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId, type));
        let item = null;
        if (ObjectUtils.isNotEmpty(list)) {
            item = list.find(item => item.name === name);
        }
        return item;
    }

    /**
     * 获取信息列表
     * @param args
     */
    async getList(args) {
        const {constructId, unitId, type} = args;
        let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId, type));
        // 判断是否有数据，没有则初始化，有则获取并填工程规模数据
        if (type !== FunctionTypeConstants.JBXX_KEY_TYPE_12 && ObjectUtils.isEmpty(list)) {
            list = await this.initData(args);
        } else {
            if (type === FunctionTypeConstants.JBXX_KEY_TYPE_12) {
                list = list instanceof Map ? Object.fromEntries(list.entries()) : list;
            } else {

                let gcgmItem = null;
                // 兼容map对象处理
                if (list[0] instanceof Map) {
                    for (let index = 0; index < list.length; index++) {

                        list[index] = Object.fromEntries(list[index].entries())
                        if ('工程规模' === list[index].name) {
                            gcgmItem = list[index];
                        }
                    }
                } else {
                    for (let item of list) {
                        if ('工程规模' === item.name) {
                            gcgmItem = item;
                        }
                    }
                }
                if (gcgmItem && type === FunctionTypeConstants.JBXX_KEY_TYPE_11) {
                    let rootProject = ProjectDomain.getDomain(constructId).getRoot();
                    gcgmItem.remark = rootProject.average;
                } else if (gcgmItem && type === FunctionTypeConstants.JBXX_KEY_TYPE_13) {

                    // 获取单位工程
                    let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
                    if (unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE3.code || unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE7.code
                        || unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE4.code) {
                        // 详细专业行
                        let newList = new Array();
                        let xxzyValue = list.find(item => item.name === '详细专业').context;
                        if (ObjectUtils.isNotEmpty(xxzyValue) && xxzyValue !== '安装全专业') {
                            for (let item of list) {
                                let groupCodes = item.groupCode.split(',');
                                if (groupCodes.includes(xxzyValue)) {
                                    newList.push(item);
                                }
                            }
                            this._reorder(newList);
                            list = newList;
                        }
                    }
                    let rootProject = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId);
                    gcgmItem.context = rootProject[0].average;
                }
            }
        }
        return list;
    }

    /**
     * 初始化基本信息、工程特征
     * @param project
     * @returns {*[]}
     */
    initDataProject(project) {
        let list = [];
        let map = new Map();
        for (let i in gsjbxx) {
            gsjbxx[i].type = 0;
            gsjbxx[i].addFlag = 0;
            gsjbxx[i].lockFlag = 0;
            let listProjectOverviewXX = new GljProjectOverview();
            ConvertUtil.setDstBySrc(gsjbxx[i], listProjectOverviewXX);
            if ('项目编号' === gsjbxx[i].name) {
                listProjectOverviewXX.remark = project.code;
            }
            if ('项目名称' === gsjbxx[i].name) {
                listProjectOverviewXX.remark = project.name;
            }
            if ('工程规模' === gsjbxx[i].name) {
                listProjectOverviewXX.remark = project.average;
                if (project.average && project.average == '0') {
                    listProjectOverviewXX.remark = null;
                }
            }
            if ('工程规模单位' === gsjbxx[i].name) {
                if(ObjectUtils.isEmpty(gsjbxx[i].remark)){
                    listProjectOverviewXX.remark = "平方米";
                }
            }
            listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
            let parentId = map.get(listProjectOverviewXX.groupCode);
            if (ObjectUtils.isEmpty(parentId)) {
                listProjectOverviewXX.parentId = "0";
                map.set(listProjectOverviewXX.groupCode, listProjectOverviewXX.sequenceNbr);
            } else {
                listProjectOverviewXX.parentId = parentId;
            }
            list.push(listProjectOverviewXX);
        }
        return list;
    }

    /**
     * type 0 项目  1 单位信息
     * @param {*} type
     */
    async initData(args) {
        const {constructId, unitId, type} = args;
        let list = [];
        let project = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === constructId);
        // 初始化工程项目层级的基本工程信息
        if (ObjectUtils.isEmpty(unitId)) {
            list = this.initDataProject(project[0]);
        } else {

            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            if (type === FunctionTypeConstants.JBXX_KEY_TYPE_11) {
                for (let i in gsUnitJbxx) {
                    gsUnitJbxx[i].addFlag = 0;
                    gsUnitJbxx[i].type = 0;
                    gsUnitJbxx[i].lockFlag = 0;
                    let listProjectOverviewXX = new GljProjectOverview();
                    ConvertUtil.setDstBySrc(gsUnitJbxx[i], listProjectOverviewXX);
                    listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
                    if ('工程名称' === gsUnitJbxx[i].name) {
                        listProjectOverviewXX.remark = unitProject.name
                    }
                    if ('工程专业' === gsUnitJbxx[i].name) {
                        let deLibrary = await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getByLibraryCode(unitProject.constructMajorType);
                        listProjectOverviewXX.remark = deLibrary ? deLibrary.projectType : unitProject.constructMajorType;
                        gsUnitJbxx[i].lockFlag = 1;
                    }
                    if ('编制依据' === gsUnitJbxx[i].name) {
                        //获取取费专业数据,默认河北石家庄,
                        let costMajorList = await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getDeLibrariesByDirection('130000', '130100');
                        let deLibrary = costMajorList.find(costItem => costItem.libraryCode === unitProject.constructMajorType);
                        listProjectOverviewXX.remark = deLibrary ? deLibrary.libraryName : costMajorList[0].libraryName;
                        listProjectOverviewXX.jsonStr = costMajorList.map(item => item.libraryName).join(',');
                        gsUnitJbxx[i].lockFlag = 1;
                    }
                    list.push(listProjectOverviewXX);
                }
            }
            if (type === FunctionTypeConstants.JBXX_KEY_TYPE_13) {
                let gsUnitGclxInitList = gsUnitGclx;
                if (unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE3.code || unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE7.code) {
                    gsUnitGclxInitList = gsUnitGclxAz;
                }
                if (unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE4.code) {
                    gsUnitGclxInitList = gsUnitGclxSz;
                }
                if (unitProject.constructMajorType === ConstructMajorTypeEnum.TYPE5.code) {
                    gsUnitGclxInitList = gsUnitGclxLh;
                }
                for (let i in gsUnitGclxInitList) {
                    gsUnitGclxInitList[i].type = 1;
                    gsUnitGclxInitList[i].addFlag = 0;
                    gsUnitGclxInitList[i].lockFlag = 0;
                    let listProjectOverviewTz = new GljProjectOverview();
                    ConvertUtil.setDstBySrc(gsUnitGclxInitList[i], listProjectOverviewTz);

                    if ('工程规模' === gsUnitGclxInitList[i].name) {
                        listProjectOverviewTz.context = unitProject.average;
                        if (unitProject.average && unitProject.average == '0') {
                            listProjectOverviewTz.context = null;
                        }
                    }
                    if ('工程规模单位' === gsUnitGclxInitList[i].name) {
                        if(ObjectUtils.isEmpty(gsjbxx[i].context)){
                            listProjectOverviewTz.context = "平方米";
                        }
                    }
                    // 添加父级行，不可编辑标识
                    if ('强电工程主要材料及设备品牌' === gsUnitGclxInitList[i].name || '弱电工程主要材料及设备品牌' === gsUnitGclxInitList[i].name
                        || '燃气主要材料及设备' === gsUnitGclxInitList[i].name || '主要材料及设备' === gsUnitGclxInitList[i].name) {
                        listProjectOverviewTz.lockFlag = 1;
                    }

                    if (ObjectUtils.isNotEmpty(listProjectOverviewTz.parentId)) {
                        let find = gsUnitGclxInitList.find(p => p.sequenceNbr === listProjectOverviewTz.parentId);
                        let find1 = list.find(o => o.name === find.name);
                        listProjectOverviewTz.parentId = find1.sequenceNbr;
                    }
                    listProjectOverviewTz.sequenceNbr = Snowflake.nextId();
                    list.push(listProjectOverviewTz);
                }
            }
        }
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, type), list);
        return list;
    }


    /**
     * 上移下移基本信息，工程特征
     * @param args
     * @returns {*}
     */
    async moveUpAndDownOverview(args) {
        let {constructId, unitId, moveType, sequenceNbrArray, type, projectId} = args;
        constructId = projectId;
        //获取原始数据
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId, type));
        let otherProjectCostsLevel1 = xeUtils.toArrayTree(otherProjectCosts, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });

        sequenceNbrArray.forEach(sequenceNbr => {
            // 获取当前选定行的父节点，获取他的子集
            let projectCostParentNode = {}
            projectCostParentNode.children = otherProjectCosts;
            if (ObjectUtils.isNotEmpty(projectCostParentNode) && ObjectUtils.isNotEmpty(projectCostParentNode.children)) {
                // 遍历建设其他费，找到点击行
                let projectCostNode = projectCostParentNode.children.find(item => item.sequenceNbr === sequenceNbr);

                if (ObjectUtils.isEmpty(projectCostNode.parentId) || projectCostNode.parentId == "0") {
                    projectCostParentNode.children = otherProjectCostsLevel1;
                }

                // 将指定行，在集合中向上/向下移一个位置
                if (moveType === "up") {
                    this.moveItemUp(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                } else if (moveType === "down") {
                    this.moveItemDown(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                }
                if (ObjectUtils.isEmpty(projectCostNode.parentId) || projectCostNode.parentId == "0") {
                    otherProjectCosts = xeUtils.toTreeArray(otherProjectCostsLevel1);
                }
            }
        });

        let sortNo = 1;
        otherProjectCosts.forEach(o => {
            o.dispNo = sortNo++;
        });
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, type), otherProjectCosts);
        return ResponseData.success(otherProjectCosts);
    }


    /**
     * 向上移位
     * @param array
     * @param index
     */
    moveItemUp(array, index) {
        // 检查索引是否大于0，因为不能移动第一个元素到更前面去
        if (index > 0) {
            // 保存要移动的元素
            let item = array.splice(index, 1)[0];
            // 在当前位置之前插入元素
            array.splice(index - 1, 0, item);
        }
    }

    /**
     * 向下移位
     * @param array
     * @param index
     */
    moveItemDown(array, index) {
        // 检查index是否在数组的有效范围内并且不是最后一个元素
        if (index >= 0 && index < array.length - 1) {
            // 使用splice取出要移动的元素
            const element = array.splice(index, 1)[0];
            // 将取出的元素插入到其下方的位置
            array.splice(index + 1, 0, element);
        }
        // return array;
    }

    /**
     * 取工程基本信--建筑分类的二级联动
     */
    getJzType(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "工业建筑":
        //         return gljJbxxType.工业建筑;
        //     case "居民服务建筑":
        //         return gljJbxxType.居民服务建筑;
        //     case "高程调整系数":
        //         return gljJbxxType.高程调整系数;
        //     default:
        //         return null;
        // }
        return gljJbxxType;
    }
}

GljOverviewService.toString = () => '[class GljOverviewService]';
module.exports = GljOverviewService;