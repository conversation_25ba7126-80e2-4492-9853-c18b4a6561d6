<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-10-20 14:22:58
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-20 15:27:28
-->
<template>
  <div class="standard-type-table">
    <!-- {{tableData}} -->
    <!--    <vxe-table-->
    <!--      ref="vexTable"-->
    <!--      :class="[-->
    <!--        'standard-type-table table-edit-common',-->
    <!--        props.isSetStandard ? 'table-edit-common' : '',-->
    <!--      ]"-->
    <!--      border-->
    <!--      height="auto"-->
    <!--      :scroll-y="{ enabled: false }"-->
    <!--      :data="tableData"-->
    <!--      keep-source-->
    <!--      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"-->
    <!--      :column-config="{ resizable: true }"-->
    <!--      @current-change="currentChangeEvent"-->
    <!--      @edit-closed="editClosedEvent"-->
    <!--      :edit-config="{-->
    <!--        trigger: 'click',-->
    <!--        mode: 'cell',-->
    <!--        beforeEditMethod: cellBeforeEditMethod,-->
    <!--      }"-->
    <!--      :cell-class-name="selectedClassName"-->
    <!--      @cell-click="-->
    <!--        cellData => {-->
    <!--          useCellClickEvent(cellData, null, ['val']);-->
    <!--        }-->
    <!--      "-->
    <!--    >-->
    <!--      <vxe-column field="type" title="审核过程">-->
    <!--        <template #default="{ row,  $rowIndex }">-->
    <!--          {{  $rowIndex === 0 ? '送审' : '审定' }}-->
    <!--        </template>-->
    <!--      </vxe-column>-->
    <!--      <vxe-column :field="type=='1'?'bdCode':'fxCode'" title="项目编码"> </vxe-column>-->
    <!--      <vxe-column :field="type=='1'?'bdName':'name'" title="项目名称"> </vxe-column>-->
    <!--      <vxe-column-->
    <!--        field="projectAttr"-->
    <!--        title="项目特征"-->
    <!--        v-if="tableData.length>0&&tableData[1]?.kind === '03'"-->
    <!--      >-->
    <!--      </vxe-column>-->
    <!--      <vxe-column field="unit" title="单位" v-if="tableData.length>0&&tableData[1].kind === '04'">-->
    <!--      </vxe-column>-->
    <!--      <vxe-column field="quantity" title="工程量"> </vxe-column>-->
    <!--      <vxe-column-->
    <!--        field="zjfPrice"-->
    <!--        title="单价"-->
    <!--        :visible="tableData.length>0&&tableData[1]?.kind === '04'"-->
    <!--      >-->
    <!--      </vxe-column>-->
    <!--      <vxe-column-->
    <!--        field="zjfTotal"-->
    <!--        title="合价"-->
    <!--        :visible="tableData.length>0&&tableData[1]?.kind === '04'"-->
    <!--      >-->
    <!--      </vxe-column>-->
    <!--      <vxe-column field="price" title="综合单价"  > </vxe-column>-->
    <!--      <vxe-column field="total" title="综合合价"  > </vxe-column>-->
    <!--    </vxe-table>-->

    <comparison-page
      :currentInfo="props.currentInfo"
      :type="props.type"
      :pageType="
        props.currentInfo.kind === '03'
          ? 'quotaqd'
          : props.currentInfo.kind === '04'
          ? 'quotade'
          : ''
      "
      :needTitle="false"
    >
    </comparison-page>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();

const props = defineProps(['currentInfo', 'type']);
const emits = defineEmits(['saveData']);

let tableData = ref([]);
const projectStore = projectDetailStore();

const currentInfo = ref(props.currentInfo);
const vexTable = ref();

watch(
  () => props.currentInfo,
  val => {
    setTable(props.currentInfo);
  }
);
onMounted(() => {
  setTable(props.currentInfo);
});
const setTable = val => {
  tableData.value = [];
  if (props.currentInfo) {
    if (props.currentInfo.kind !== '04' && props.currentInfo.kind !== '03') {
      tableData.value = [];
      return;
    }
    let obj = {
      kind: val?.kind,
    };
    console.log(val, 'val.ysshSysj');
    Object.keys(val).forEach(key => {
      obj[key] = val[key];
    });
    tableData.value = [val.ysshSysj, obj];
    console.log(tableData.value, 'val.ysshSysj');
  } else {
    tableData.value = [];
  }
};
// 选中单条分部分项数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};
</script>

<style lang="scss" scoped>
.standard-type-table {
  width: 100%;
  height: 100%;
  :deep(.vxe-table) {
    width: 97% !important;
    height: 100%;
    .vxe-table--render-wrapper {
      height: 100%;
      .vxe-table--main-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        .vxe-table--body-wrapper {
          flex: 1;
          height: auto !important;
          min-height: auto !important;
        }
      }
    }
  }
  .table-edit-common {
    width: 100% !important;
  }
}
</style>
