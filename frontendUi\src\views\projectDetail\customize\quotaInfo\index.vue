<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-05-29 15:11:09
 * @LastEditors: liuxia
 * @LastEditTime: 2024-09-24 14:46:36
-->
<template>
  <div class="quota-info">
    <div class="head-action">
      <a-tabs
        v-model:activeKey="componentName"
        type="card"
        @change="tableChange"
        :hideAdd="true"
      >
        <a-tab-pane v-for="pane in tabList" :key="pane.key" :tab="pane.title">
          <!--          {{ pane.title }}-->
        </a-tab-pane>
      </a-tabs>
      <!--			<a-radio-group-->
      <!--				:value="componentName"-->
      <!--				@change="tableChange"-->
      <!--				button-style="solid"-->
      <!--				style="margin-right: 15px"-->
      <!--			>-->
      <!--			</a-radio-group>-->
    </div>
    <keep-alive>
      <div class="content">
        <component
          ref="materialRef"
          :is="components.get(componentName)"
          :tableData="tableData"
          :currentInfo="props.currentInfo"
          :currentMaterialInfo="currentMaterialInfo"
          :type="props.type"
          :interfaceData="interfaceData"
          :RefreshList="refreshList"
          :fatherLoading="fatherLoading"
          @dbClickFile="dbClickFile"
          @updateData="updateData"
          @cellDBLClickEvent="cellDBLClickEvent"
        ></component>
      </div>
    </keep-alive>
    <material-machine-index
      v-model:indexVisible="indexVisible"
      :currentMaterialInfo="currentMaterialInfo"
      :indexLoading="indexLoading"
      @currentRcjInfo="currentRcjInfo"
      @addChildrenRcjData="addChildrenRcjData"
      @currentInfoReplace="currentInfoReplace"
    ></material-machine-index>
  </div>
</template>

<script setup>
import { defineAsyncComponent, markRaw, nextTick, ref, watch } from 'vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import api from '../../../../api/projectDetail';
import csProject from '@/api/csProject.js';
import feePro from '@/api/feePro';
import { globalData } from './status.js';

import { message } from 'ant-design-vue';
import { projectDetailStore } from '../../../../store/projectDetail';

const props = defineProps([
  'currentInfo',
  'isAttrContent',
  'type',
  'isUpdateFile',
  'isUpdateQuantities',
  'isComplete',
  'isUpdate',
  'fatherLoading', // 父级数据请求loading状态
]);
const emits = defineEmits(['refreshCurrentInfo', 'tabClickBefore']);
const indexVisible = ref(false);

let tabList = ref([
  {
    title: '人材机明细',
    key: 'materialMachineTable',
  },
  {
    title: '单价构成',
    key: 'priceCompositionTable',
  },
  {
    title: '标准换算',
    key: 'standardConversionTable',
  },
  {
    title: '换算信息',
    key: 'conversionInfoTable',
  },
  {
    title: '特征及内容',
    key: 'contentsTable',
  },
  {
    title: '工程量明细',
    key: 'quantitiesTable',
  },
  // {
  //   title: '安文费明细',
  //   key: 'safeFeeInfoTable',
  // },
]);
let tableData = ref([]);
const projectStore = projectDetailStore();
let currentSequenceNbr = ref('');
let currentMaterialInfo = ref(null);
let indexLoading = ref(false); // 索引页面loading
let materialRef = ref(null);
let interfaceData = ref(null);
const checkList = projectStore.checkCgZsIdList;
watch(
  () => materialRef.value,
  val => {
    projectStore.materialRef = val;
  },
  { deep: true }
);

const components = markRaw(new Map());
components.set(
  'materialMachineTable',
  defineAsyncComponent(() => import('./materialMachineTable.vue'))
);
components.set(
  'priceCompositionTable',
  defineAsyncComponent(() => import('./priceCompositionTable.vue'))
);
components.set(
  'standardConversionTable',
  defineAsyncComponent(() => import('./standardConversionTable.vue'))
);
components.set(
  'conversionInfoTable',
  defineAsyncComponent(() => import('./conversionInfoTable.vue'))
);
components.set(
  'contentsTable',
  defineAsyncComponent(() => import('./contentsTable.vue'))
);
components.set(
  'quantitiesTable',
  defineAsyncComponent(() => import('./quantitiesTable.vue'))
);
components.set(
  'safeFeeInfoTable',
  defineAsyncComponent(() => import('./safeFeeInfo.vue'))
);

components.set(
  'groupSchemeTable',
  defineAsyncComponent(() => import('./groupSchemeTable.vue'))
);

let componentName = ref('materialMachineTable');
let newComponentName = ref();
watch(
  () => globalData.isEditStatus,
  (newVal, oldVal) => {
    if (!newVal && oldVal && newComponentName.value) {
      componentName.value = newComponentName.value;
      tableChange(componentName.value);
    }
  }
);
const tableChange = event => {
  if (event && !props.isComplete) {
    componentName.value = 'materialMachineTable';
    emits('tabClickBefore');
    return;
  }
  if (globalData.isEditStatus && event !== 'priceCompositionTable') {
    componentName.value = 'priceCompositionTable';
    newComponentName.value = event;
  } else {
    newComponentName.value = null;
  }
  if (componentName.value === 'materialMachineTable') {
    queryRcjDataByDeId();
  } else if (componentName.value === 'priceCompositionTable') {
    mathDePrice();
  } else if (componentName.value === 'standardConversionTable') {
    queryRule();
  } else if (componentName.value === 'conversionInfoTable') {
    queryRuleInfo();
  } else if (componentName.value === 'contentsTable') {
    qdFeature();
  } else if (componentName.value === 'quantitiesTable') {
    queryAll();
  } else if (componentName.value === 'safeFeeInfoTable') {
    querySafeFeeData();
  } else if (componentName.value === 'groupSchemeTable') {
    queryGroupSchemeData();
  }
};

watch(
  () => props.currentInfo,
  (newVal, oldVal) => {
    if (JSON.stringify(newVal) == JSON.stringify(oldVal)) {
      return;
    }

    if (newVal && newVal?.sequenceNbr !== oldVal?.sequenceNbr) {
      currentMaterialInfo.value = null;

      projectStore.SET_SUB_CURRENT_MATERIAL_INFO(null);
    }
    // isSupplement 是否未补充清单
    let { kind, standardId, zjcsClassCode, isSupplement, bdCode, fxCode } =
      props.currentInfo || {};
    console.log('🚀 ~ props.currentInfo:', props.currentInfo);
    //设置刷新选中行上下移动
    projectStore.SET_SUB_CURRENT_INFO(props.currentInfo);

    // 组价方案交互判断，
    // 组价方案，切换分部清单，刷新数据。
    // 如果单纯的双击组价方案左侧的话，不需要组价自身刷新数据
    if (
      newVal?.sequenceNbr !== oldVal?.sequenceNbr &&
      materialRef.value &&
      materialRef.value?.editRefresh
    ) {
      materialRef.value?.editRefresh();
    }

    let initTabList = tabList.value.filter(
      item => !['groupSchemeTable', 'safeFeeInfoTable'].includes(item.key)
    );
    if (props.currentInfo && Number(props.currentInfo.isCostDe) === 1) {
      initTabList.push({
        title: '安全生产、文明施工费明细',
        key: 'safeFeeInfoTable',
      });
    }

    // 仅选中清单行时，明细区有的组价方案页签展示；
    // zjcsClassCode "0" 安文费
    let hasGroupSchemeTableStatus =
      (bdCode || fxCode) &&
      ['03'].includes(kind) &&
      !checkList.includes(standardId) &&
      (zjcsClassCode === null ||
        zjcsClassCode === '' ||
        typeof zjcsClassCode == 'undefined') &&
      isSupplement === 0;

    console.log(
      '🚀 ~ projectStore.deType:',
      projectStore.deType,
      hasGroupSchemeTableStatus
    );
    if (hasGroupSchemeTableStatus) {
      initTabList.push({
        title: '组价方案',
        key: 'groupSchemeTable',
      });
    }

    // 1.如果上一次选中了组价方案页签，但当前行不是清单，就取消选中
    // 2. 如果上一次选中了安全文明费页签，但当前行不是1，就取消选中
    if (
      (componentName.value === 'groupSchemeTable' &&
        !hasGroupSchemeTableStatus) ||
      (componentName.value === 'safeFeeInfoTable' &&
        Number(props.currentInfo.isCostDe) !== 1)
    ) {
      componentName.value = 'materialMachineTable';
    }

    tabList.value = initTabList;

    // 组价方案刷新判断
    // 非组价方案的沿用以前的
    if (
      (componentName.value === 'groupSchemeTable' &&
        !isDeepEqual(newVal, oldVal)) ||
      componentName.value !== 'groupSchemeTable'
    ) {
      tableChange();
    }
  },
  { deep: true }
);
watch(
  () => props.isAttrContent,
  () => {
    if (props.isAttrContent) {
      componentName.value = 'contentsTable';
      qdFeature();
    }
  }
);
watch(
  () => props.isUpdateFile,
  () => {
    if (props.isUpdateFile) {
      if (componentName.value === 'priceCompositionTable') {
        mathDePrice();
      } else if (componentName.value === 'materialMachineTable') {
        queryRcjDataByDeId();
      }
    }
  }
);

watch(
  () => props.isUpdateQuantities,
  () => {
    if (props.isUpdateQuantities) {
      if (componentName.value === 'quantitiesTable') {
        clearAll();
      }
    }
  }
);

watch(
  () => props.isUpdate,
  () => {
    if (props.isUpdate) {
      tableChange();
    }
  }
);

const isDeepEqual = (obj1, obj2) => {
  if (obj1 === obj2) return true; // 如果引用相同，则直接返回true
  if (
    typeof obj1 !== 'object' ||
    obj1 === null ||
    typeof obj2 !== 'object' ||
    obj2 === null
  ) {
    return false; // 如果一方不是对象或者为null，则不相等
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false; // 属性数量不同则不相等
  }

  for (let key of keys1) {
    const val1 = obj1[key];
    const val2 = obj2[key];
    const areObjects = isObject(val1) && isObject(val2);

    if (
      (areObjects && !isDeepEqual(val1, val2)) ||
      (!areObjects && val1 !== val2)
    ) {
      return false; // 对象属性值不相等或类型不同则不相等
    }
  }

  return true;
};

// 辅助函数，检查值是否为对象
const isObject = value => {
  return value != null && typeof value === 'object';
};

const refreshList = (bol = false) => {
  emits('refreshCurrentInfo', bol);
};

const queryRcjDataByDeId = () => {
  // currentMaterialInfo.value = null;
  let apiData = {
    id: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    branchType: props.type,
  };
  if (!apiData.id) return;
  if ([94, 95].includes(props.currentInfo?.kind)) {
    tableData.value = [];
    return;
  }
  api.queryRcjDataByDeId(apiData).then(res => {
    console.log('定额明细数据', apiData, res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
      for (let i = 0; i < res.result?.length; ++i) {
        let strNum = '' + res.result[i].totalNumber;
        res.result[i].originalMaterialCode = res.result[i].materialCode;
        if (res.result[i]?.rcjDetailsDTOs) {
          res.result[i].rcjDetailsDTOs.forEach(child => {
            child.originalMaterialCode = child.materialCode;
          });
        }
      }
      // 默认选中第一条数据
      if (tableData.value.length && !currentMaterialInfo.value)
        currentMaterialInfo.value = tableData.value[0];

      projectStore.SET_SUB_CURRENT_MATERIAL_INFO(tableData.value[0]);
      // 插入之后反向定位到该条数据
      if (currentSequenceNbr.value) {
        currentMaterialInfo.value = { sequenceNbr: currentSequenceNbr.value };
        nextTick(() => {
          // 反向查询真实列表中的数据，替换只有id做定位高亮的数据
          currentMaterialInfo.value = materialRef.value?.vexTable?.getRowById(
            currentSequenceNbr.value
          );
          projectStore.SET_SUB_CURRENT_MATERIAL_INFO(currentMaterialInfo.value);
          console.log('currentMaterialInfo', currentMaterialInfo.value);
        });
      }
      getSortNo();
      setTimeout(() => {
        currentSequenceNbr.value = '';
      });
    } else {
      tableData.value = [];
    }
  });
};
const getSortNo = () => {
  if (tableData.value) {
    tableData.value.map((item, index) => {
      item.sortNo = index + 1 + '';
      item.rcjDetailsDTOs &&
        item.rcjDetailsDTOs.map((child, i) => {
          child.sortNo = `${item.sortNo}.${i + 1}`;
        });
    });
  }
};
const mathDePrice = () => {
  let apiData = {
    pointLineId: props.currentInfo?.sequenceNbr,
    unitWorkId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('单价构成参数', apiData);
  api.mathDePrice(apiData).then(res => {
    console.log('单价构成数据', res);
    if (res.status === 200) {
      res.result &&
        res.result.map(item => {
          if (item.type === '安全文明施工费') {
            item.name = '安全生产、文明施工费';
          }
          item.rate = item.rate + '';
        });
      tableData.value = res.result;
    }
  });
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const dbClickFile = v => {
  console.log('🚀 ~ dbClickScheme ~ v:', v);
  emits('onDbClickFile', {
    isRefresh: false,
    ...v,
  });
};

const updateData = column => {
  if (componentName.value === 'materialMachineTable') {
    queryRcjDataByDeId();
  } else if (componentName.value === 'standardConversionTable') {
    queryRule();
  } else if (componentName.value === 'conversionInfoTable') {
    queryRuleInfo();
  } else if (componentName.value === 'contentsTable') {
    qdFeature();
    if (column) {
      emits('refreshCurrentInfo');
    }
  } else if (componentName.value === 'quantitiesTable') {
    queryAll();
  }
  if (column) {
    emits('refreshCurrentInfo');
  }
};

const cellDBLClickEvent = row => {
  if (props.currentInfo?.kind === '04') {
    indexVisible.value = true;
  }
  console.log('currentMaterialInfo:', row);
  currentMaterialInfo.value = row;
  projectStore.SET_SUB_CURRENT_MATERIAL_INFO(row);
};

const queryRule = () => {
  let apiData = {
    standardDeId: props.currentInfo?.standardId,
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    libraryCode: props.currentInfo?.libraryCode,
  };
  console.log('标准换算列表参数2', apiData);
  api.queryRule(apiData).then(res => {
    console.log('标准换算列表数据', res);
    if (res.status === 200 && res.result) {
      let result = res.result;
      const group = getList(result);
      group.forEach(item => {
        if (item[0].relationGroupName) {
          item.forEach((child, index) => {
            if (index === 0) {
              child.rowSpan = item.length;
            } else {
              child.rowSpan = 0;
            }
            child.old_selected = child.selected || false;
            child.chapterStatus = false;
          });
        } else {
          item.forEach(child => {
            child.old_selected = child.selected || false;
            child.rowSpan = 1;
            child.chapterStatus = false;
          });
        }
      });
      group.forEach(item => {
        item.forEach((child, index) => {
          result.map(original => {
            original = child;
          });
        });
      });
      console.log('group', result);
      tableData.value = result;
      console.log('==========', tableData.value);
    } else {
      tableData.value = [];
    }
  });
};

const getList = list => {
  const map = new Map();
  list.forEach((item, index, arr) => {
    if (!map.has(item.relationGroupName)) {
      map.set(
        item.relationGroupName,
        arr.filter(a => a.relationGroupName === item.relationGroupName)
      );
    }
  });
  return Array.from(map).map(item => [...item[1]]);
};

const queryRuleInfo = () => {
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  if (!apiData.fbFxDeId) return;
  console.log('换算信息参数', apiData);
  api.queryRuleInfo(apiData).then(res => {
    console.log('换算信息数据', res);
    if (res.status === 200) {
      tableData.value = res.result;
      console.log('==========', tableData.value);
      tableData.value.map((item, index) => {
        item.sortNumber = index + 1 + '';
        item.children &&
          item.children.map((child, i) => {
            child.sortNumber = `${item.sortNumber}.${i + 1}`;
          });
      });
    }
  });
};
const querySafeFeeData = () => {
  // isCostDe判别是否是安文费
  console.log('props.currentInfo', props.currentInfo);
  if (props.currentInfo && Number(props.currentInfo.isCostDe) === 1) {
    let apiData = {
      deId: props.currentInfo?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
    };
    console.log('安文费明细参数', apiData);
    api.awfDetails(apiData).then(res => {
      console.log('安文费明细数据', res.result, typeof res.result);
      if (res.status === 200) {
        tableData.value = [res.result];
        console.log('==========', tableData.value);
      }
    });
  } else {
    tableData.value = [];
  }
};
const qdFeature = () => {
  if (props.currentInfo?.kind !== '03') {
    tableData.value = [];
    return;
  }
  let apiData = {
    fbFxQdId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.qdFeature(apiData).then(res => {
    console.log('特征及内容数据', res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
      console.log('==========', tableData.value);
    } else {
      tableData.value = [];
    }
  });
};

// 工程量明细查询所有数据
const queryAll = () => {
  if (props.currentInfo.kind !== '03' && props.currentInfo.kind !== '04') {
    tableData.value = [];
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    lineId: props.currentInfo?.sequenceNbr,
  };
  console.log('工程量明细参数', apiData);
  api.queryAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('工程量明细结果', res.result);

      tableData.value = res.result;
    } else {
      tableData.value = [];
    }
  });
};

// 工程量明细清空所有数据
const clearAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type,
    quotaListId: props.currentInfo?.sequenceNbr,
  };
  console.log('工程量清空数据参数', apiData);
  api.clearAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('工程量清空数据结果', res.result);
      queryAll();
    }
  });
};

let SchemeDataLoading = ref(false);

// 组价方案查询所有数据
const queryGroupSchemeData = async () => {
  const { result } = await feePro.isOnline();
  if (!result) {
    tableData.value = [];
    message.error('网络未连接!');
    return;
  }

  if (SchemeDataLoading.value) return;
  const postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: props.type == 1 ? 'fbfx' : 'csxm',
    qdId: props.currentInfo?.sequenceNbr,
  };
  interfaceData.value = postData;
  // tableData.value = [];
  SchemeDataLoading.value = true;
  csProject
    .qdMergePlanQuery(postData)
    .then(res => {
      console.log('组价方案查询所有数据');
      if (res.status === 200) {
        tableData.value = res.result || [];
      }
    })
    .finally(() => {
      SchemeDataLoading.value = false;
    });
};

const addOrReplaceIntercept = () => {};

const currentRcjInfo = row => {
  indexLoading.value = true;
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    baseRcjModel: JSON.parse(JSON.stringify(row)),
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('人材机增加参数', apiData);
  api.addRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('人材机增加结果', res.result);
      message.success('插入成功');
      currentSequenceNbr.value = res.result?.sequenceNbr;
      emits('refreshCurrentInfo');
      indexLoading.value = false;
      queryRcjDataByDeId();
    }
  });
};

const addChildrenRcjData = row => {
  indexLoading.value = true;
  row.isSupplement = 0;
  let apiData = {
    rcjDetail: JSON.parse(JSON.stringify(row)),
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    sequenceNbr: currentMaterialInfo.value?.parentId,
    pointLine: JSON.parse(JSON.stringify(currentMaterialInfo.value)),
    de: JSON.parse(JSON.stringify(props.currentInfo)),
  };
  console.log('人材机配比增加参数', apiData);
  api
    .addChildrenRcjData(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        message.success('插入成功');
        console.log('人材机插入结果', res.result);
        currentSequenceNbr.value = res.result?.sequenceNbr;
        emits('refreshCurrentInfo');
        indexLoading.value = false;
        queryRcjDataByDeId();
      }
    })
    .catch(err => {
      console.log(err);
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

const currentInfoReplace = row => {
  // if (props.type === 1) {
  //   replaceItemBillData(row);
  // } else {
  //   itemReplaceFromIndexPage(row);
  // }
  retailAreaRcjReplace(row);
};
/**
 * 新替换功能
 * @param {*} row
 */
const retailAreaRcjReplace = row => {
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    replaceLine: JSON.parse(JSON.stringify(row)),
    selectLine: JSON.parse(JSON.stringify(currentMaterialInfo.value)),
    conversionCoefficient: row.conversionCoefficient,
    de: JSON.parse(JSON.stringify(props.currentInfo)),
  };
  console.log('新替换', apiData);
  api.retailAreaRcjReplace(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('替换成功');
      emits('refreshCurrentInfo');
      indexLoading.value = false;
      currentSequenceNbr.value = res.result?.sequenceNbr;
      queryRcjDataByDeId();
    }
  });
};
// 分部分项替换功能
const replaceItemBillData = row => {
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentMaterialInfo.value?.sequenceNbr,
    type: 2,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    libraryCode: row.libraryCode,
  };
  console.log('替换', apiData);
  api.replaceItemBillData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('替换成功');
      emits('refreshCurrentInfo');
      indexLoading.value = false;
      currentSequenceNbr.value = res.result?.sequenceNbr;
      queryRcjDataByDeId();
    }
  });
};

// 措施项目替换功能
const itemReplaceFromIndexPage = row => {
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentMaterialInfo.value?.sequenceNbr,
    type: 2,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    libraryCode: row.libraryCode,
  };
  api.itemReplaceFromIndexPage(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('替换成功');
      emits('refreshCurrentInfo');
      indexLoading.value = false;
      currentSequenceNbr.value = res.result?.sequenceNbr;
      queryRcjDataByDeId();
    }
  });
};

// 外部切换tab
const manualTabChange = name => {
  console.log('切换', name);
  componentName.value = name;
  tableChange(null);
};
defineExpose({
  manualTabChange,
});
</script>

<style lang="scss" scoped>
.quota-info {
  height: 100%;
  .head-action {
    margin-bottom: 5px;
    height: 35px;
    background: #e7e7e7;
    flex: 1;
    :deep(.ant-tabs-tab) {
      height: 35px;
      background: transparent;
      border: none;
      color: #7c7c7c;
    }
    :deep(.ant-tabs-tab-active) {
      background: #ffffff;
      border-top: 2px solid #4786ff;
      .ant-tabs-tab-btn {
        color: #000000;
      }
    }
    button {
      float: right;
      margin-right: 15px;
    }
  }
  .content {
    height: calc(100% - 40px);
  }
}
</style>
