<!--
 * @Descripttion: 清单定额索引
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-05-28 16:16:57
-->
<template>
  <div class="inventory-dialog-wrap">
    <common-modal
      className="dialog-comm resizeClass"
      v-model:modelValue="props.indexVisible"
      :title="dialogTitle"
      :mask="false"
      :lockView="false"
      :lockScroll="false"
      width="1040"
      height="80vh"
      min-height="350"
      min-width="900"
      :resize="true"
      :show-zoom="true"
      @cancel="close"
      @close="close"
    >
      <div class="dialog-wrap">
        <div class="contentCenter">
          <div class="left">
            <p class="head">
              <a-radio-group
                v-model:value="value1"
                button-style="solid"
                @change="radioChange"
              >
                <a-radio-button value="dingE">定额</a-radio-button>
                <a-radio-button value="renCJ">人材机</a-radio-button>
              </a-radio-group>
            </p>
            <div class="search">
              <IndexSearch
                v-model:value="bdName"
                :type="value1"
                @search="onSearch"
              ></IndexSearch>
              <!-- <a-input-search
                v-model:value="bdName"
                :maxlength="50"
                placeholder="请输入编码或名称"
                style="width: 95%; margin: 10px 8px 10px"
                @search="onSearch" 
              /> -->
              <a-select
                v-model:value="selectValue"
                style="width: 95%; margin: 0 8px 10px"
                :options="selectOptions"
                placeholder="请选择"
                :field-names="fieldnames"
                @change="handleChange"
              ></a-select>
              <div class="tree">
                <a-tree
                  v-if="treeData && treeData.length"
                  v-model:expandedKeys="expandedKeys"
                  v-model:selectedKeys="selectedKeys"
                  :tree-data="treeData"
                  @select="selectChildren"
                  style="z-index: 10"
                  :field-names="{
                    title: value1 === 'dingE' ? 'name' : 'materialName',
                    children: 'childrenList',
                    key: 'key',
                  }"
                >
                  <template #icon="{ key }">
                    <template v-if="key !== '0-0'"></template>
                  </template>
                  <template
                    #title="{ bdCodeLevel04, bdName }"
                    v-if="value1 === 'qdzy'"
                  >
                    <span>{{ bdCodeLevel04 }} {{ bdName }}</span>
                  </template>
                </a-tree>
              </div>
            </div>
          </div>
          <div class="right">
            <p class="btns">
              <a-radio-group
                v-model:value="value2"
                button-style="solid"
                @change="changeRadio(value2)"
              >
                <a-radio-button value="list"
                  >{{
                    value1 === 'dingE' ? '定额' : '人材机'
                  }}列表</a-radio-button
                >
              </a-radio-group>
              <span>
                <a-button
                  type="primary"
                  class="btnNo1"
                  :disabled="
                    isInsertDisabled || tableData.length == 0 || isAddRe
                  "
                  @click="updateCurrentInfo(1)"
                  >插入</a-button
                >
                <a-button
                  type="primary"
                  :disabled="isDisabled || tableData.length == 0 || isAddRe"
                  @click="updateCurrentInfo(2)"
                  >替换</a-button
                >
              </span>
            </p>

            <div class="table table-content" ref="tabContentRef">
              <split
                v-if="value2 === 'list'"
                horizontal
                :ratio="splitRatio"
                :horizontalBottom="200"
                style="height: 100%"
                :minHorizontalTop="minHorizontalTop"
                mode="vertical"
                :only-part="value1 === 'renCJ' ? 'all' : 'Top'"
                @onDragHeight="dragHeight"
              >
                <template #one>
                  <vxe-table
                    ref="vexTable"
                    align="center"
                    :column-config="{ resizable: true }"
                    :data="tableData"
                    height="auto"
                    :row-config="{
                      isCurrent: true,
                      keyField: 'sequenceNbr',
                    }"
                    @current-change="currentChangeEvent"
                    @cell-dblclick="
                      cellData => {
                        useCellDBLClickEvent(
                          cellData,
                          vexTable,
                          'inventoryAndQuotaIndex',
                          cellDBLClickEvent,
                        );
                      }
                    "
                    :scroll-y="tableScrollY"
                    v-if="value2 === 'list'"
                    @scroll="getScroll"
                    show-overflow="title"
                  >
                    <vxe-column type="seq" width="50" title="序号">
                    </vxe-column>
                    <vxe-column
                      field="bdCode"
                      align="left"
                      width="80"
                      title="编码"
                    ></vxe-column>
                    <vxe-column
                      field="bdName"
                      width="auto"
                      align="left"
                      title="名称"
                    ></vxe-column>
                    <vxe-column
                      v-if="value1 === 'renCJ'"
                      field="specification"
                      width="80"
                      align="left"
                      title="规格型号"
                    ></vxe-column>
                    <vxe-column
                      field="unit"
                      width="70"
                      title="单位"
                    ></vxe-column>
                    <vxe-column
                      field="baseJournalPrice"
                      width="80"
                      v-if="store.taxMade == 1"
                      align="right"
                      :title="value1 === 'renCJ' ? '不含税基期价' : '单价'"
                    >
                      <template #default="{ row }">
                        {{
                          value1 === 'renCJ' && Number(row.isDataTaxRate) == 0
                            ? '-'
                            : DecimalUtils.toFixed(row.baseJournalPrice,2)
                        }}
                      </template>
                    </vxe-column>
                    <vxe-column
                      field="baseJournalTaxPrice"
                      width="80"
                      v-else
                      align="right"
                      :title="value1 === 'renCJ' ? '含税基期价' : '单价'"
                    >
                      <template #default="{ row }">
                        {{
                          value1 === 'renCJ' && Number(row.isDataTaxRate) == 0
                            ? '-'
                            : DecimalUtils.toFixed(row.baseJournalTaxPrice,2)
                        }}
                      </template>
                    </vxe-column>
                  </vxe-table>
                </template>
                <template v-if="value1 === 'renCJ'" #two>
                  <div style="position: relative; height: 100%">
                    <Teleport
                      v-if="tableData.length"
                      to=".table-content .resizer"
                      ><div class="contract-btn" @click.stop="toggleContract">
                        <DownOutlined v-if="isContract" /><UpOutlined
                          v-else
                        /></div
                    ></Teleport>
                    <vxe-table
                      v-if="sTableHeightBottom"
                      ref="vexTableRcj"
                      align="center"
                      :column-config="{ resizable: true }"
                      :data="rcjTableData"
                      height="auto"
                      :loading="rcjTableLoading"
                      :row-config="{
                        isCurrent: true,
                        keyField: 'sequenceNbr',
                      }"
                      @cell-dblclick="
                        cellData => {
                          useCellDBLClickEvent(
                            cellData,
                            vexTable,
                            'inventoryAndQuotaIndex',
                            insertRcjDe,
                          );
                        }
                      "
                      :scroll-y="{
                        enabled: true,
                        scrollToTopOnChange: true,
                      }"
                      show-overflow="title"
                    >
                      <vxe-column type="seq" width="50" title="序号">
                      </vxe-column>
                      <vxe-column
                        field="deCode"
                        align="left"
                        width="80"
                        title="编码"
                      ></vxe-column>
                      <vxe-column
                        field="deName"
                        width="auto"
                        align="left"
                        title="名称"
                      ></vxe-column>
                      <vxe-column
                        field="unit"
                        width="70"
                        title="单位"
                      ></vxe-column>
                      <vxe-column
                        field="baseJournalPrice"
                        width="80"
                        align="right"
                        title="单价"
                      >
                     <template #default="{ row }">
                        <div>{{  DecimalUtils.toFixed(row.baseJournalPrice,2) }}</div>
                      </template>
                      </vxe-column>
                    </vxe-table>
                  </div>
                </template>
              </split>

              <div v-if="value2 !== 'list'" style="width: 100%; height: 100%">
                <div style="width: 100%; height: 100%">
                  <iframe
                    :src="desHtml.ossUrl"
                    title="description"
                    width="100%"
                    height="100%"
                    v-if="desHtml.ossUrl"
                  ></iframe>
                  <p v-if="!desHtml.ossUrl" class="noData">暂无数据</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </common-modal>
    <div class="unit-radio" v-if="showUnitTooltip">
      <div class="title">选择单位</div>
      <a-radio-group v-model:value="selectUnit">
        <a-radio
          v-for="(unit, index) in currentInfo.unitList"
          :key="index"
          :value="unit"
        >
          {{ unit }}
        </a-radio>
      </a-radio-group>
      <a-button type="link" @click="selectHandler(currentInfo)">确定</a-button>
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  reactive,
  ref,
  watchEffect,
  watch,
  toRefs,
  toRaw,
  nextTick,
  shallowRef,
  getCurrentInstance,
} from 'vue';
import api from '@gongLiaoJi/api/projectDetail';
import csProject from '@/api/csProject';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellDBLClickEvent } from '@gongLiaoJi/hooks/useCellClick';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import XEUtils from 'xe-utils';
import deMapFun from '../deMap';
import IndexSearch from './IndexSearch.vue';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import DecimalUtils from '@gongLiaoJi/utils/decimalUtils.js';

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

const route = useRoute();
const value1 = ref('dingE');
const value2 = ref('list');
const props = defineProps([
  'indexVisible',
  'originInfo',
  'indexLoading',
  'isCsxm',
]);
const emits = defineEmits(['currentQdDeInfo', 'update:indexVisible']);
const store = projectDetailStore();
let searchKey = ref();
let selectValue = ref('');
let selectOptions = ref([]);
let expandedKeys = ref([]);
let queryForm = reactive({
  libraryCode: '',
  classlevel01: '',
  classlevel02: '',
  classlevel03: '',
  classlevel04: '',
  classlevel05: '',
  classlevel06: '',
  classlevel07: '',
  deName: '',
  standardId: '',
  bdName: '',
  deCode: '',
  page: 1,
  limit: 30,
});
let scrollBeforeTop = 0;
let { bdName } = { ...toRefs(queryForm) };

let renCJQueryForm = reactive({
  libraryCode: '',
  level1: '',
  level2: '',
  level3: '',
  level4: '',
  level5: '',
  level6: '',
  level7: '',
  materialName: '',
  startIndex: 1,
  pageNumber: 50,
});
let desHtml = reactive({ ossUrl: '' });
const selectedKeys = ref([]);
let currentTreeInfo = ref();
let treeData = shallowRef([]);
let currentInfo = ref();
const showUnitTooltip = ref(false);
let vexTable = ref();
let scrollSwitch = ref(false);
let dialogTitle = ref('定额索引');
let conversionCoefficient = ref('');
let unitVisible = ref(false);
const isAddRe = ref(false);
let isSearch = ref(false); // 是否为点击搜索按钮查询数据
let selectUnit = ref(); // 多单位时选择单位
let optionType = ref(1); // 判断当前操作类型 1 插入 2 替换
let fieldnames = ref({
  label: 'libraryName',
  value: 'libraryCode',
});
watch(
  () => props.indexVisible,
  () => {
    if (props.indexVisible) {
      isInsertDisabledNext.value = true;
      fieldnames.value = {
        label: 'libraryName',
        value: 'libraryCode',
      };
      tableData.value = [];
      console.log('🚀 ~ props.originInfo:', props.originInfo);
      value1.value = ['06', '09', '05'].includes(props.originInfo.kind)
        ? 'renCJ'
        : 'dingE';
      if (['04', '08'].includes(props.originInfo.kind)) {
        console.log('02');
        queryDeLibrary(value1.value === 'dingE');
      } else {
        console.log('03');
        queryDeLibrary(value1.value === 'dingE');
      }
      selectValue.value = store.currentTreeInfo.constructMajorType;
      // if (props.originInfo.standardId) {
      //   queryCurrentData();
      // }
    }
    if (!props.indexVisible) {
      showUnitTooltip.value = false;
    }
  },
);
watch(
  () => selectedKeys.value,
  () => {
    nextTick(() => {
      const selectedNode = document.querySelector(
        '.dialog-wrap .ant-tree-treenode-selected'
      );
      let distanceFromTop = selectedNode?.offsetTop;
      const scrollableDiv = document.querySelector('.dialog-wrap .tree');
      scrollableDiv.scrollTop = distanceFromTop;
    });
  },
);
const isInsertDisabledNext = ref(true);
watchEffect(() => {
  if (currentInfo.value && ['00', '01', '02'].includes(props.originInfo.kind)) {
    nextTick(async () => {
      //查询子节点
      let res = await api.queryDeChildType({
        constructId: store.currentTreeGroupInfo?.constructId,
        unitId: store.currentTreeInfo?.id,
        singleId: '',
        deRowId: props.originInfo.deRowId,
      });
      isInsertDisabledNext.value = res.result.some(item =>
        ['01', '02'].includes(item)
      );
    });
  }
});
const isInsertDisabled = computed({
  get: () => {
    if (props.isCsxm) {
      if (
        currentInfo.value &&
        props.originInfo.kind !== '00' &&
        props.originInfo.kind !== '01' &&
        props.originInfo.kind !== '02' &&
        ![1, 3].includes(props.originInfo.pricingMethod) && //bug22384子措施组件跟计算公式组件不允许插入定额
        !props.indexLoading
      ) {
        return false;
      } else {
        return true;
      }
    } else {
      if (
        currentInfo.value &&
        value1.value !== 'qingDan' &&
        deMapFun.isDe(props.originInfo.kind) &&
        !props.indexLoading
      ) {
        return false;
      }
      return isInsertDisabledNext.value;
    }
  },
  set: val => {
    return val;
  },
});
const isDisabled = computed(() => {
  if (
    currentInfo.value &&
    value1.value === 'dingE' &&
    deMapFun.isDe(props.originInfo.kind) &&
    !props.indexLoading
  ) {
    return false;
  } else if (
    currentInfo.value &&
    value1.value === 'renCJ' &&
    deMapFun.isDe(props.originInfo.kind) &&
    !props.indexLoading
  ) {
    return false;
  }
  return true;
});
const changeRadio = value => {
  value2.value = value;
  if (value1.value === 'dingE') {
    queryListByClassify();
  } else {
    likeQdByCodeOrName();
  }
};
/**
 * 点击子节点
 * 选择左侧树对应右侧表格数据发生变化
 * @param {Array} selectedKeys - 选中的key
 * @param {Object} { node, event } - 选中的节点和事件
 */
const selectChildren = (data, e) => {
  scrollBeforeTop = 0;

  if (!e.selected) {
    selectedKeys.value = [e.node.key];
    return;
  }
  currentTreeInfo.value = e.node;
  if (value1.value == 'qdzy') {
    qdzyDeList.value = [];
    qdGuideDeList();
    console.log('a');
    return;
  }

  if (value1.value === 'renCJ') {
    console.log('b');
    initRCJQuery();
  } else {
    console.log('c');
    initQuery();
  }
  let pathList = e.node.dataRef.path.split('/');
  queryForm.libraryCode = selectValue.value;
  renCJQueryForm.libraryCode = selectValue.value;
  pathList.forEach((item, index) => {
    if (index === 0 && value1.value === 'dingE') return;
    if (value1.value === 'qingDan') {
      queryForm[`classlevel0${index + 1}`] = item;
    } else if (value1.value === 'dingE') {
      queryForm[`classlevel0${index}`] = item;
    } else {
      renCJQueryForm[`level${index + 1}`] = item;
    }
  });
  if (pathList.length != 6) {
    for (let i = 0; i < 6; i++) {
      if (i > pathList.length) {
        queryForm[`classlevel0${i}`] = '';
      }
    }
  }
  currentInfo.value = null;
  console.log('定额索引左侧树点击传参', queryForm);
  if (value1.value === 'dingE') {
    queryListByClassify();
  } else if (value1.value === 'qingDan') {
    likeQdByCodeOrName();
  } else {
    isSearch.value = false;
    queryBaseRcjLikeName();
  }
};
const getChapterDate = () => {
  let apiData = {
    kind: value1.value === 'qingDan' ? '03' : '04',
    dataType: value2.value === 'des' ? 1 : value2.value === 'compute' ? 2 : 3,
    libraryCode: queryForm.libraryCode,
    selectedName: currentTreeInfo.value?.name,
  };
  console.log('章节说明参数', apiData);
  api.queryChapterDate(apiData).then(res => {
    if (res.status === 200) {
      console.log('--------------', res);
      const filePath = res.result.absolutePath
        .split('public')[1]
        ?.replace(/\\/g, '/');
      desHtml.ossUrl = '';
      fetch(filePath)
        .then(response => {
          if (!response.ok) {
            // HTTP status code is not 200-299
            if (response.status === 404) {
              console.error('The iframe source URL returned a 404 status.');
            }
          } else {
            desHtml.ossUrl = response.url;
          }
        })
        .catch(error => {
          console.error(
            'There was an error fetching the iframe source:',
            error
          );
        });
    }
  });
};

const handleChange = value => {
  queryForm.libraryCode = value;
  currentInfo.value = null;
  tableData.value = [];
  for (let i = 0; i < 8; i++) {
      if (i > 1) {
        queryForm[`classlevel0${i}`] = '';
      }
    }
  switch (value1.value) {
    case 'dingE':
      deListByTree();
      break;
    default:
      initRCJQuery();
      getRcjTreeByCode();
      break;
  }
};
const tableData = shallowRef();

const onSearch = () => {
  if ((bdName.value ?? '') === '') {
    let parentNode = findPathToRoot(treeData.value, selectedKeys.value[0]);
    if (parentNode.length > 0) {
      let currentNode = parentNode[parentNode.length - 1];
      scrollBeforeTop = 0;
      currentTreeInfo.value = currentNode;
      if (value1.value == 'qdzy') {
        qdzyDeList.value = [];
        qdGuideDeList();
        console.log('a');
        return;
      }

      if (value1.value === 'renCJ') {
        console.log('b');
        initRCJQuery();
      } else {
        console.log('c');
        initQuery();
      }
      let pathList = currentNode.path.split('/');
      queryForm.libraryCode = selectValue.value;
      renCJQueryForm.libraryCode = selectValue.value;
      pathList.forEach((item, index) => {
        if (index === 0 && value1.value === 'dingE') return;
        if (value1.value === 'qingDan') {
          queryForm[`classlevel0${index + 1}`] = item;
        } else if (value1.value === 'dingE') {
          queryForm[`classlevel0${index}`] = item;
        } else {
          renCJQueryForm[`level${index + 1}`] = item;
        }
      });
      if (pathList.length != 6) {
        for (let i = 0; i < 6; i++) {
          if (i > pathList.length) {
            queryForm[`classlevel0${i}`] = '';
          }
        }
      }
      currentInfo.value = null;
      console.log('定额索引左侧树点击传参', queryForm);
      if (value1.value === 'dingE') {
        queryListByClassify();
      } else if (value1.value === 'qingDan') {
        likeQdByCodeOrName();
      } else {
        isSearch.value = false;
        queryBaseRcjLikeName();
      }
    }
  } else {
    tableData.value = [];
    if (value1.value === 'qdzy') {
      qdzyGetTree();
      return;
    }

    if (value1.value === 'renCJ') {
      initRCJQuery();
    } else {
      initQuery();
    }
    queryForm.libraryCode = selectValue.value;
    renCJQueryForm.libraryCode = selectValue.value;
    renCJQueryForm.materialName = queryForm.bdName;
    if (value1.value === 'dingE') {
      queryListByClassify();
    } else if (value1.value === 'qingDan') {
      likeQdByCodeOrName();
    } else {
      isSearch.value = true;
      queryAllBaseRcjLikeName();
    }
  }
};

const findPathToRoot = (tree, targetKey) => {
  let path = [];

  function dfs(node, currentPath) {
    if (node.key === targetKey) {
      path = [...currentPath, node]; // 找到目标，记录路径
      return true; // 终止搜索
    }

    if (node.childrenList && node.childrenList.length > 0) {
      for (const child of node.childrenList) {
        if (dfs(child, [...currentPath, node])) {
          return true; // 如果子节点找到，提前终止
        }
      }
    }
    return false;
  }

  // 遍历根节点
  for (const rootNode of tree) {
    if (dfs(rootNode, [])) {
      break;
    }
  }
  return path;
};

const initQuery = () => {
  scrollBeforeTop = 0;
  queryForm = Object.assign(queryForm, {
    classlevel01: '',
    classlevel02: '',
    classlevel03: '',
    classlevel04: '',
    classlevel05: '',
    classlevel06: '',
    classlevel07: '',
    deName: '',
    standardId: '',
    deCode: '',
    page: 1,
    limit: 30,
  });

  qdzyDeList.value = [];
};

const initRCJQuery = () => {
  scrollBeforeTop = 0;
  renCJQueryForm = Object.assign(renCJQueryForm, {
    level1: '',
    level2: '',
    level3: '',
    level4: '',
    level5: '',
    level6: '',
    level7: '',
    startIndex: 1,
    materialName: '',
  });
  rcjTableData.value = [];
};

let qdzyDeList = shallowRef([]);
// 清单指引获取树
const qdzyGetTree = async (type = false) => {
  let postData = {
    qdCodeOrName: bdName.value,
    libraryCode: guideLibraryModel.value.qdLibraryCode,
  };
  const res = await csProject.qdLevelTree(postData);
  console.log('🚀清单指引获取树', res);
  qdzyDeList.value = [];
  qdzyTableData.value = [];
  treeData.value = res.result.map(i => {
    if (!i.bdName) {
      i.bdName = i.bdNameLevel04;
    }
    return i;
  });

  if (type) {
    nextTick(() => {
      const expandedKey = findParentIds(
        treeData.value,
        currentTreeInfo.value.sequenceNbr,
      );
      expandedKeys.value = expandedKey;
      console.log('🚀 ~ nextTick ~ expandedKeys.value:', expandedKeys.value);
      selectedKeys.value = [currentTreeInfo.value.sequenceNbr];
    });
  } else {
    currentTreeInfo.value = res.result[0];
    if (res.result[0]?.childrenList && res.result[0]?.childrenList.length) {
      expandedKeys.value = [
        res.result[0]?.sequenceNbr,
        res.result[0]?.childrenList[0].sequenceNbr,
      ];
    } else {
      expandedKeys.value = [];
      selectedKeys.value = [currentTreeInfo.value.sequenceNbr];
    }
  }

  if (res.result?.length) {
    qdGuideDeList();
  } else {
    qdzyTableData.value = [];
  }
};

let qdzyTableData = ref([]);
// 获取左侧清单指引列表数据
const qdGuideDeList = async () => {
  const guide = { ...toRaw(guideLibraryModel.value) };
  delete guide.dataRef;
  delete guide.parent;

  let postData = {
    guideLibraryModel: { ...guide },
    baseListModel: { ...toRaw(currentTreeInfo.value) },
  };

  const res = await csProject.qdGuideDeList(postData);
  console.log('🚀 ~ qdGuideDeList ~ res:', res);
  const list = addIdAndParentId(res.result, 1);
  qdzyTableData.value = list.map(i => {
    i.displaySign = 1;
    return i;
  });
  nextTick(() => {
    vexTable.value.setAllTreeExpand(true);
  });
};

const addIdAndParentId = (data, parentId = null) => {
  return data.map((node, index) => ({
    id: `${parentId ? parentId + '-' : ''}${index + 1}`, // 构造唯一的id
    parentId,
    ...node,
    children:
      node.children && node.children.length > 0
        ? addIdAndParentId(
            node.children,
            `${parentId ? parentId + '-' : ''}${index + 1}`
          )
        : [],
  }));
};

/**
 * isDeDw 是否定额定位
 */
const queryDeLibrary = (isDeDw = false) => {
  console.log('constructConfigInfo', store.currentTreeInfo.libraryCode);
  api.getByLibraryAll(props.originInfo.standardId).then(res => {
    if (res.result) {
      selectOptions.value = res.result;
      console.log(
        'getByLibraryAll',
        res.result,
        props.originInfo,
        value1.value
      );
      console.log(
        '🚀 ~ api.queryDeLibrary ~ queryRcjById:',
        props.originInfo.standardId && value1.value === 'renCJ'
      );
      if (
        (props.originInfo.standardId &&
          ['04', '08'].includes(props.originInfo.kind) &&
          props.originInfo.isDeResource === 0 &&
          props.originInfo.isCostDe !== 1 &&
          value1.value === 'dingE') ||
        isDeDw
      ) {
        console.log('🚀 ~ api.queryDeLibrary ~ queryDeById:');
        queryDeById();
      } else if (
        props.originInfo.standardId &&
        (props.originInfo.kind === '06' || props.originInfo.kind === '09') &&
        value1.value === 'renCJ' &&
        props.originInfo.isDeResource === 1
      ) {
        console.log('🚀 ~ api.queryDeLibrary ~ queryRcjById:', value1.value);
        queryRcjById();
      } else {
        selectValue.value = store.currentTreeInfo.constructMajorType;
        console.log('store.currentTreeInfo.libraryCode=', selectValue.value);
        if (value1.value === 'dingE') {
          deListByTree();
        } else {
          getRcjTreeByCode();
        }
      }
    }
  });
};

const deListByTree = () => {
  api.gsDeListByTree({ libraryCode: selectValue.value }).then(res => {
    console.log(
      '定额数据机构',
      res,
      props.originInfo.standardId &&
        deMapFun.isDe(props.originInfo.kind) &&
        value1.value === 'dingE'
    );
    if (res.result) {
      treeData.value = getDeInitData([res.result]);
      if (props.isCsxm && ['03'].includes(props.originInfo.kind) && currentInfo.value) {
        console.log('7777777777888888888888888', currentInfo.value);
        findDeAndLevel(treeData.value, currentInfo.value);
        queryListByClassify();
      } else if (
        (props.originInfo.standardId &&
          deMapFun.isDe(props.originInfo.kind) &&
          currentInfo.value &&
          !(props.originInfo.kind === '06' || props.originInfo.kind === '09') &&
          value1.value === 'dingE') ||
        props.originInfo.kind === '08'
      ) {
        findDeAndLevel(treeData.value, currentInfo.value);
      } else {
        console.log('selectedKeys', treeData.value[0].key);
        selectedKeys.value = [treeData.value[0].key];
        queryForm.libraryCode = selectValue.value;
        queryForm.page = 1;
        treeData.value.forEach(item => {
          // queryForm.classifyLevel0 = item.name;
          selectedKeys.value = [item.key];
          expandedKeys.value = [item.key];
          item.childrenList.forEach((child,key) => {
            // if (
            //   child.name.includes(
            //     store.currentTreeInfo.name
            //   )
            // ) {
            if (key == 0) {
              queryForm.classlevel01 = child.name;
              selectedKeys.value = [child.key];
              expandedKeys.value.push(child.key);
            }
            // }
          });
        });
      }
      nextTick(() => {
        if (props.isCsxm && ['03'].includes(props.originInfo.kind)) {
          queryListByClassify();
        } else {
          queryListByClassify();
        }
      });
    }
  });
};

const getRcjTreeByCode = () => {
  api.getGsRcjTreeByCode({ libraryCode: selectValue.value }).then(res => {
    console.log('rcj----', res.result, props.originInfo, currentInfo.value);
    if (res.status === 200) {
      treeData.value = getRCJInitData(res.result);
      if (
        props.originInfo.isDeResource !== 1 ||
        props.originInfo.kind == '09' ||
        currentInfo.value == null
      ) {
        selectedKeys.value = [res.result[0].key];
        renCJQueryForm.libraryCode = selectValue.value;
      } else {
        findRCJAndLevel(treeData.value, currentInfo.value);
        getRcjData(currentInfo.value);
      }
      queryBaseRcjLikeName();
    }
  });
};

const getScroll = type => {
  if (value1.value === 'qingDan') return;
  if (
    Math.ceil(type.scrollTop + type.$event.target.clientHeight) >=
    type.scrollHeight
  ) {
    if (scrollSwitch.value) {
      const { scrollTop } = vexTable.value.getScroll();
      scrollBeforeTop = scrollTop;
      if (value1.value === 'dingE') {
        console.log('不进来么定额分页');
        queryForm.page++;
        queryListByClassify();
      } else {
        renCJQueryForm.startIndex++;
        if (isSearch.value) {
          queryAllBaseRcjLikeName();
        } else {
          queryBaseRcjLikeName();
        }
      }
    }
  }
};

const likeQdByCodeOrName = () => {
  if (value2.value === 'list') {
    queryForm.bdName = bdName.value;
    api.likeQdByCodeOrName(JSON.parse(JSON.stringify(queryForm))).then(res => {
      if (res.result) {
        tableData.value = res.result;
        if (props.originInfo.standardId) {
          let currentObj = tableData.value.filter(
            x =>
              x.bdName === currentInfo.value?.bdNameLevel04 &&
              x.bdCode === currentInfo.value?.bdCodeLevel04,
          )[0];
          vexTable.value.setCurrentRow(currentObj);
        } else {
          let obj = tableData.value.filter(
            x => x.sequenceNbr === currentInfo.value?.sequenceNbr
          )[0];
          if (!obj) {
            currentInfo.value = null;
            vexTable.value.clearCurrentRow();
          }
        }
      }
    });
  } else if (value2.value === 'des') {
    desHtml.ossUrl = '';
  }
};

const queryListByClassify = () => {
  if (value2.value === 'list') {
    queryForm.bdName = bdName.value;
    if (queryForm.classlevel02 === '十八章 补充定额') {
      queryForm.limit = 100;
    } else {
      queryForm.limit = 30;
    }
    let postData = {
      ...toRaw(queryForm),
    };
    if (store.currentTreeInfo) {
      postData.constructId = route.query?.constructSequenceNbr;
      postData.spId = store.currentTreeInfo?.parentId;
      postData.upId = store.currentTreeInfo.id;
    }
    if (props.originInfo.kind == '08') {
      postData['deCode'] = '';
      postData['deName'] = '';
      postData['classlevel04'] = '';
    }
    console.log('postData', postData);
    api.queryListByClassify(postData).then(res => {
      console.log('定额列表数据', res);
      if (queryForm.deName || queryForm.deCode || queryForm.standardId) {
        queryForm.deName = '';
        queryForm.deCode = '';
        queryForm.standardId = '';
      }
      if (res.status === 200) {
        if (postData.page === 1) {
          tableData.value = [];
        }
        if (res.result.page) {
          queryForm.page = res.result.page;
        }
        let tempList = res.result.data.map(item => {
          return {
            ...item,
            bdName: item.deName,
            bdCode: item.deCode,
          };
        });
        console.log(
          'tableData.value.concat(tempList);',
          tableData.value.concat(tempList)
        );
        tableData.value = tableData.value.concat(tempList);
        setScrollTop();
        if (props.originInfo.standardId) {
          let currentObj = tableData.value.filter(
            x =>
              x.bdName === currentInfo.value?.deName &&
              x.bdCode === currentInfo.value?.deCode,
          )[0];
          console.log('currentObj', currentObj);
          setTimeout(() => {
            if (!scrollBeforeTop) {
              vexTable.value.scrollToRow(currentObj);
              vexTable.value.setCurrentRow(currentObj);
            } else {
              setScrollTop();
            }
          }, 10);
        } else {
          let obj = tableData.value.filter(
            x => x.sequenceNbr === currentInfo.value?.sequenceNbr
          )[0];
          if (!obj) {
            currentInfo.value = null;
            vexTable.value.clearCurrentRow();
          }
        }
        if (Math.ceil(res.result.total / queryForm.limit) > queryForm.page) {
          scrollSwitch.value = true;
        } else {
          scrollSwitch.value = false;
        }
      }
    });
  } else if (value2.value !== 'list') {
    getChapterDate();
  }
};

const queryBaseRcjLikeName = () => {
  scrollSwitch.value = false;
  renCJQueryForm.materialName = bdName.value;
  if (!renCJQueryForm.level1) {
    renCJQueryForm.level1 = '人工';
  }
  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(renCJQueryForm)),
    page: renCJQueryForm.startIndex,
    limit: renCJQueryForm.pageNumber,
    baseRcjInfo: { ...toRaw(currentInfo.value) }, //修复bug25358预算书/措施项目：园林绿化工程，插入主材Z00080，作为人材机定额，再次双击，在人材机索引栏中定位不到
  };
  console.log('queryBaseRcjLikeName', apiData);
  api.queryBaseRcjLikeName(apiData).then(res => {
    if (res.status === 200) {
      if (renCJQueryForm.startIndex === 1) {
        tableData.value = [];
      }
      let inventoryList = res.result.list.map(item => {
        return {
          ...item,
          bdName: item.materialName,
          bdCode: item.materialCode,
        };
      });


      let tempList = sortInventoryList(inventoryList);

      tableData.value = tableData.value.concat(tempList);
      console.log('tableData.value', tableData.value);
      setScrollTop();
      if (props.originInfo.standardId) {
        let currentObj = tableData.value.filter(
          x =>
            x.bdName === currentInfo.value?.materialName &&
            x.bdCode === currentInfo.value?.materialCode,
        )[0];
        setTimeout(() => {
          if (!scrollBeforeTop) {
            vexTable.value.scrollToRow(currentObj);
            vexTable.value.setCurrentRow(currentObj);
          } else {
            setScrollTop();
          }
        }, 100);
      } else {
        let obj = tableData.value.filter(
          x => x.sequenceNbr === currentInfo.value?.sequenceNbr
        )[0];
        if (!obj) {
          currentInfo.value = null;
          vexTable.value.clearCurrentRow();
        }
      }
      if (
        Math.ceil(res.result.total / renCJQueryForm.pageNumber) >
        renCJQueryForm.startIndex
      ) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};


/**
 * 列表排序,先根据bdName名称一样或者相近的放在一块，然后每块再根据bdCode排序,
 * 只有新三本定额册排序
 * @param {*} list
 */
function sortInventoryList(data) {
    if(!data || !data.length || ['2025-FGJZ-DEG','2025-GJXSGC-DEG','2025-SZSS-DEX'].includes(selectValue.value)){
      return data;
    }
    function isSimilar(nameA, nameB) {
        return  nameA == nameB || nameA.substring(0, 2) == nameB.substring(0, 2);
    }

    const groups = [];

    for (const item of data) {
        let grouped = false;
        for (const group of groups) {
            if (isSimilar(item.bdName, group[0].bdName)) {
                group.push(item);
                grouped = true;
                break;
            }
        }
        if (!grouped) {
            groups.push([item]);
        }
    }

    const sortedGroups = groups.map(group => {
        return group.sort((a, b) => a.bdCode.localeCompare(b.bdCode));
    });

    return sortedGroups.flat();
}

// 设置滚动条位置
const setScrollTop = () => {
  if (!scrollBeforeTop) return;
  setTimeout(() => {
    console.log('滚动到', scrollBeforeTop);
    vexTable.value.scrollTo(0, scrollBeforeTop);
  }, 10);
};
const queryAllBaseRcjLikeName = () => {
  scrollSwitch.value = false;
  renCJQueryForm.materialName = bdName.value;

  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(renCJQueryForm)),
    page: renCJQueryForm.startIndex,
    limit: renCJQueryForm.pageNumber,
  };
  api.queryAllBaseRcjLikeName(apiData).then(res => {
    console.log('搜索查询接口');
    if (res.status === 200) {
      if (renCJQueryForm.startIndex === 1) {
        tableData.value = [];
      }
      let inventoryList  = res.result.list.map(item => {
        return {
          ...item,
          bdName: item.materialName,
          bdCode: item.materialCode,
        };
      });
      let tempList = sortInventoryList(inventoryList);


      tableData.value = tableData.value.concat(tempList);
      setScrollTop();
      let obj = tableData.value.filter(
        x => x.sequenceNbr === currentInfo.value?.sequenceNbr
      )[0];
      if (!obj) {
        currentInfo.value = null;
        vexTable.value.clearCurrentRow();
      }
      if (
        Math.ceil(res.result.total / renCJQueryForm.pageNumber) >
        renCJQueryForm.startIndex
      ) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};

const getQdInitData = tree => {
  return tree.map(item => {
    item.key = item.bdName + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.bdName;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getQdInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.bdName + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.bdName) + '/' + n.bdName,
          }))
        )
      : null;
    return item;
  });
};

const getDeInitData = tree => {
  return tree.map(item => {
    item.key = item.name + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.name;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getDeInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.name + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.name) + '/' + n.name,
          }))
        )
      : null;
    return item;
  });
};

const getRCJInitData = tree => {
  return tree.map(item => {
    item.key = item.materialName + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.materialName;
    if (!item.path || item.path.split('/').length < 2) {
      // expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getRCJInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.materialName + Math.ceil(Math.random() * 10000 + 1),
            path:
              (item.path ? item.path : item.materialName) +
              '/' +
              n.materialName,
          }))
        )
      : null;
    return item;
  });
};

// 反向查找清单指定数据并展开选中
const findQDAndLevel = (tree, targetData, level = 1) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (
      targetData &&
      targetData[`bdCodeLevel0${level}`] &&
      node.bdCode === targetData[`bdCodeLevel0${level}`]
    ) {
      expandedKeys.value.push(node.sequenceNbr);
      selectedKeys.value = [node.sequenceNbr];
    }

    if (node.childrenList) {
      const result = findQDAndLevel(
        node.childrenList,
        currentInfo.value,
        level + 1
      );
      if (result) {
        return result;
      }
    }
  }

  return null;
};

const findParentIds = (tree, sequenceNbr) => {
  let result = [];
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].sequenceNbr === sequenceNbr) {
      result.push(tree[i].sequenceNbr);
      break;
    }
    if (tree[i].childrenList) {
      result = result.concat(findParentIds(tree[i].childrenList, sequenceNbr));
      if (result.length > 0) {
        result.push(tree[i].sequenceNbr);
        break;
      }
    }
  }
  return result;
};

// 反向查找定额指定数据并展开选中
const findDeAndLevel = (tree, targetData, level = 0) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (targetData && targetData[`classlevel0${level}`]) {
      let result = true;
      for (let j = 1; j <= level; j++) {
        if (
          result &&
          node[`classlevel0${j}`] === targetData[`classlevel0${j}`]
        ) {
          result = true;
        } else {
          result = false;
        }
      }
      if (result) {
        expandedKeys.value.push(node.key);
        selectedKeys.value = [node.key];
      }
    }
    if (node.childrenList) {
      const result = findDeAndLevel(
        node.childrenList,
        currentInfo.value,
        level + 1
      );
      if (result) {
        console.log('反向定位数据', result);
        return result;
      }
    }
  }

  return null;
};

// 反向查找人材机指定数据并展开选中
const findRCJAndLevel = (tree, targetData, level = 1) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (
      targetData &&
      targetData[`level${level}`] &&
      node.materialName === targetData[`level${level}`]
    ) {
      expandedKeys.value.push(node.key);
      selectedKeys.value = [node.key];
    }
    if (node.childrenList) {
      const result = findRCJAndLevel(
        node.childrenList,
        currentInfo.value,
        level + 1
      );
      if (result) {
        console.log('反向定位数据', result);
        return result;
      }
    }
  }

  return null;
};

let likeQdByCodeTimer = ref(null);
const radioChange = XEUtils.debounce(e => {
  bdName.value = '';
  isAddRe.value = true;
  rcjTableData.value = [];
  for (let i = 0; i < 8; i++) {
    if (i > 1) {
      queryForm[`classlevel0${i}`] = '';
    }
  }
  initQuery();
  let fieldNames = {
    label: 'libraryName',
    value: 'libraryCode',
  };
  clearTimeout(likeQdByCodeTimer.value);
  likeQdByCodeTimer.value = null;
  console.log('target', e.target.value);
  switch (e.target.value) {
    default:
      queryDeLibrary();
      break;
  }
  fieldnames.value = fieldNames;
}, 200);

// 列表配置虚拟滚动，不影响原有逻辑，所以enabled动态
const tableScrollY = computed(() => {
  return {
    scrollToTopOnChange: true,
    enabled: value1.value === 'qingDan' ? true : false,
  };
});

// 选中单条清单定额数据
const currentChangeEvent = ({ row }) => {
  isAddRe.value = false;
  currentInfo.value = { ...row, ...{ isDe: value1.value === 'dingE' ? 1 : 0 } };
  if (showUnitTooltip.value) {
    showUnitTooltip.value = false;
  }

  if (value1.value === 'renCJ') {
    getRcjData(currentInfo.value);
  }
};

const cellDBLClickEvent = async ({ row }) => {
  if (value1 === 'qdzy') return;
  currentInfo.value = { ...row, ...{ isDe: value1.value === 'dingE' ? 1 : 0 } };
  if (!isInsertDisabled.value && !props.indexLoading) {
    updateCurrentInfo(1);
  }
};

const insertRcjDe = ({ row }) => {
  const type = 1
   let isReplace =
    (!props.isCsxm &&
      ((!['00', '01', '02'].includes(props.originInfo.kind) &&
        props.originInfo.deCode == undefined) ||
        (props.originInfo.deCode != undefined && type == 2))) ||
    (props.isCsxm &&
      ((!['00', '01', '02', '03'].includes(props.originInfo.kind) &&
        props.originInfo.deCode == undefined) ||
        (props.originInfo.deCode != undefined && type == 2)));

  emits('currentQdDeInfo', { ...row,kind:'04',isDeResource:0 }, 1,isReplace ? 'replace' : 'add');
};
/**
 *
 * @param {*} type 调用接口的类型
 * @param {*} status 插入的 类型，zm:'插入子目'， qd:'插入清单' th: '替换清单'
 */
const updateCurrentInfo = type => {
  optionType.value = type;
  // 如果是预算书页面：单位工程行和分部行都是新增，定额行如果没有编码则是替换否则是新增，定额行如果有编码但是点击的替换则也是替换
  let isReplace =
    (!props.isCsxm &&
      ((!['00', '01', '02'].includes(props.originInfo.kind) &&
        props.originInfo.deCode == undefined) ||
        (props.originInfo.deCode != undefined && type == 2))) ||
    (props.isCsxm &&
      ((!['00', '01', '02', '03'].includes(props.originInfo.kind) &&
        props.originInfo.deCode == undefined) ||
        (props.originInfo.deCode != undefined && type == 2)));
  currentInfo.value.kind = '04';
  if (value1.value === 'dingE') {
    currentInfo.value.isDeResource = 0;
  } else {
    currentInfo.value.isDeResource = 1;
  }
  if (type === 2 && value1.value === 'renCJ') {
    if (currentInfo.value.unit !== props.originInfo.unit) {
      handleOk();
      return;
    }
  }

  if (
    (props.isCsxm &&
      type === 1 &&
      (props.originInfo.kind == '03' ||
        (props.originInfo.kind != '03' &&
          props.originInfo.deCode != undefined))) ||
    (!props.isCsxm &&
      props.originInfo.kind != '-1' &&
      props.originInfo.deCode != undefined)
  ) {
    let handleCurrentData = { ...currentInfo.value };
    emits(
      'currentQdDeInfo',
      handleCurrentData,
      value1.value === 'dingE' ? 1 : 0,
      isReplace ? 'replace' : 'add'
    );
  } else {
    emits(
      'currentInfoReplace',
      currentInfo.value,
      value1.value === 'dingE' ? 1 : 0,
      isReplace ? 'replace' : 'add'
    );
  }
};

// 获取人材机定额
let rcjTableData = ref([]);
let rcjTableLoading = ref(false);
const getRcjData = record => {
  console.log('💡 ~ record:', record);
  const params = {
    constructId: route.query?.constructSequenceNbr,
    libraryCode: record.libraryCode,
    baseRcjId: record.sequenceNbr,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
    page: 1,
    pageSize: 300000,
  };
  rcjTableLoading.value = true;
  api
    .queryDeByRcj(params)
    .then(res => {
      if (res.code === 200) {
        rcjTableData.value = res.result.data;
      }
    })
    .finally(() => {
      rcjTableLoading.value = false;
    });
};

let minHorizontalTop = ref(100);
let isContract = ref(true);
let sTableHeight = ref(300);
let sTableHeightBottom = ref(200);
let rightTotalHeight = ref(400);

const toggleContract = () => {
  isContract.value = !isContract.value;
  minHorizontalTop.value = isContract.value ? 100 : 150;
  setTimeout(() => {
    sTableHeightBottom.value = isContract.value
      ? rightTotalHeight.value / 2
      : 0;
    sTableHeight.value = !isContract.value
      ? rightTotalHeight.value - sTableHeightBottom.value
      : rightTotalHeight.value;
  }, 10);
};
const splitRatio = computed(() => {
  if (!isContract.value) {
    return '32/1';
  }
  return value1.value === 'renCJ' ? '4/3' : '1/1';
});

const close = () => {
  allInit();
  emits('update:indexVisible', false);
  bus.emit('focusTableData');
};
const selectHandler = dataRef => {
  if (!selectUnit.value) {
    return message.warning('请选择单位');
  }
  showUnitTooltip.value = false;
  const obj = {
    ...dataRef,
    unit: selectUnit.value,
  };
  // optionType.value 1 插入  2 替换
  if (optionType.value === 1) {
    emits('currentQdDeInfo', obj);
  } else {
    emits('currentInfoReplace', obj);
  }
};
const allInit = () => {
  initQuery();
  initRCJQuery();
  bdName.value = '';
  treeData.value = [];
  value1.value = 'dingE';
  value2.value = 'list';
  tableData.value = [];
  expandedKeys.value = [];
  selectedKeys.value = [];
  currentTreeInfo.value = null;
  currentInfo.value = null;
  conversionCoefficient.value = '';
  queryForm.page = 1;
};

// 查询当前定额数据用于反向定位
const queryDeById = () => {
  console.log('props.originInfo', props.originInfo);
  if (props.isCsxm && ['03'].includes(props.originInfo.kind)) {
    const postData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      unitId: store.currentTreeInfo?.id,
      deName: props.originInfo.deName,
    };
    console.log('🚀 ~ queryDeById ~ postData:', postData);
    api.getDeCsxMappingByLibraryCode(postData).then(res => {
      console.log('🚀 询当前定额数据用于反向定位:', res, selectOptions.value);
      if (res.status === 200) {
        if (res.result && res.result.length > 0) {
          if (+store.deType === 22 && !res.result[0].libraryCode) {
            // 空定额，并且是22的
            selectValue.value = selectOptions.value[0]?.libraryCode;
            currentInfo.value = res.result[0];
          } else {
            selectValue.value = res.result[0].libraryCodeMapping;
            currentInfo.value = {
              ...res.result[0],
              libraryCode: res.result[0].libraryCodeMapping,
            };
          }
          queryForm = {
            ...queryForm,
            libraryCode: res.result[0].libraryCodeMapping,
            classlevel01: res.result[0].classlevel01,
            classlevel02: res.result[0].classlevel02,
            classlevel03: res.result[0].classlevel03,
            classlevel04: res.result[0].classlevel04,
            classlevel05: res.result[0].classlevel05,
            classlevel06: res.result[0].classlevel06,
            classlevel07: res.result[0].classlevel07,
          };
          console.log(
            '🚀 ~ queryDeById------- ~ queryForm:',
            JSON.parse(JSON.stringify(queryForm))
          );
        }
        deListByTree();
      }
    });
  } else {
    const postData = {
      standardId: props.originInfo.standardId,
      libraryCode: props.originInfo.libraryCode,
      deCode: props.originInfo.deCode,
      sequenceNbr: props.originInfo.sequenceNbr,
      constructId: store.currentTreeGroupInfo?.constructId,
    };
    console.log('🚀 ~ queryDeById ~ postData:', postData);
    api.queryDeById(postData).then(res => {
      console.log('🚀 询当前定额数据用于反向定位:', res);
      if (res.status === 200) {
        if (res.result && res.result.libraryCode) {
          console.log(
            '🚀 ~ queryDeById ~ selectValue.value:',
            selectOptions.value
          );

          if (+store.deType === 22 && !props.originInfo.standardId) {
            // 空定额，并且是22的
            selectValue.value = selectOptions.value[0]?.libraryCode;
          } else {
            selectValue.value = res.result.libraryCode;
          }
          currentInfo.value = res.result;
          queryForm = {
            ...queryForm,
            libraryCode: res.result.libraryCode,
            classlevel01: res.result.classlevel01,
            classlevel02: res.result.classlevel02,
            classlevel03: res.result.classlevel03,
            classlevel04: res.result.classlevel04,
            classlevel05: res.result.classlevel05,
            classlevel06: res.result.classlevel06,
            classlevel07: res.result.classlevel07,
            deCode: res.result.deCode,
            deName: res.result.deName,
            standardId: props.originInfo.standardId,
          };
          console.log('🚀 ~ queryDeById ~ queryForm:', queryForm);
        }
        deListByTree();
      }
    });
  }
};

// 查询当前人材机数据用于反向定位
const queryRcjById = () => {
  console.log('queryRcjById', {
    standardId: props.originInfo.standardId,
    libraryCode: props.originInfo.libraryCode,
  });
  api
    .queryRcjById({
      standardId: props.originInfo.standardId,
      libraryCode: props.originInfo.libraryCode,
    })
    .then(res => {
      console.log('查询当前人材机数据用于反向定位', props.originInfo, res);
      if (res.status === 200) {
        if (res.result) {
          selectValue.value = res.result?.libraryCode;
          currentInfo.value = res.result;
          renCJQueryForm = {
            ...renCJQueryForm,
            libraryCode: res.result.libraryCode,
            level1: res.result.level1,
            level2: res.result.level2 || '',
            level3: res.result.level3 || '',
            level4: res.result.level4 || '',
            level5: res.result.level5 || '',
            level6: res.result.level6 || '',
            level7: res.result.level7 || '',
          };
        }

        getRcjTreeByCode();
      }
    });
};

const handleOk = () => {
  unitVisible.value = false;
  emits('currentInfoReplace', currentInfo.value, 0, 'replace');
};
</script>

<style lang="scss" scoped>
.table-content {
  :deep(.resizer) {
    position: relative;
    background: #c3ddf7;
    &:hover {
      background: #287cfa;
    }
  }
}
.contract-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 7px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 6;
  width: 32px;
  height: 9px;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;
  background: #287cfa;
}
.qdzy-wrap {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  .qdzy-btns {
    margin-left: 15px;
  }
}

.dialog-wrap {
  // width: 1000px;
  width: 100%;
  height: 100%;
}
.contentCenter {
  display: flex;
  justify-content: space-around;
  color: #b9b9b9;
  margin-top: 10px;
  height: calc(100% - 10px);
  .left {
    width: 35%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .search {
      width: 100%;
      flex: 1;
      border: 1px solid #b9b9b9;
      overflow: hidden;
      //overflow-y: scroll; // 解决闪烁问题
      //height: 100%;
      .tree {
        width: calc(100% - 10px);
        height: calc(100% - 95px);
        // border: 1px solid #dcdfe6;
        margin-left: 10px;
        //overflow: hidden;
        overflow-y: scroll; // 解决闪烁问题
        :deep(.ant-tree) {
          height: 100%;
        }
        &:hover {
          overflow: auto;
        }
      }
    }
  }
  .right {
    width: 60%;
    display: flex;
    flex-direction: column;
    height: 100%;
    .btns {
      display: flex;
      width: 100%;
      justify-content: space-between;
      .btnNo1 {
        margin: 0 15px;
      }
    }
    .table {
      flex: 1;
      height: 86%;
    }
  }
}
.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.dialog-content {
  padding: 46px;
  text-align: center;
}
.init-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  .icon {
    margin-right: 5px;
  }
  span {
    color: #1e90ff;
    margin: 0 2px;
  }
  .ant-input {
    width: 105px;
    margin-left: 5px;
  }
}
.init-text:nth-last-of-type(1) {
  margin-top: 25px;
}
.footer-btn-list {
  padding-bottom: 20px;
  margin-top: 20px;
}
</style>
<style lang="scss">
.qdzy-table {
  border: 1px solid rgba(185, 185, 185, 1);
  overflow: hidden;
  .tree-title {
    font-weight: 800;
    color: #000;
  }
  .vxe-table--render-default
    .vxe-body--column.col--ellipsis
    > .vxe-cell
    .vxe-tree-cell {
    display: flex;
    justify-content: flex-end;
    // .vxe-checkbox--icon{
    //   color:rgba(185, 185, 185, 1);
    // }
  }
  .row-tree-title .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    display: flex;
    justify-content: flex-start;
  }
  .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    color: rgba(185, 185, 185, 1);
  }
  .vxe-table--render-default
    .is--checked.vxe-cell--checkbox
    .vxe-checkbox--icon {
    color: var(--vxe-primary-color);
  }
  .vxe-table {
    .vxe-tree--btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      left: -4px;
    }
    .row-tree-title {
      background-color: #f5f5f5;
    }
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }

    .vxe-cell--tree-node {
      .rotate90 {
        transform: rotate(0);
      }
      .vxe-icon-caret-right:before {
        content: '+';
        display: contents;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(50%, 50%);
      }
      &.is--active {
        .vxe-icon-caret-right:before {
          content: '-';
        }
      }
    }

    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image:
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1)),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image:
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1)),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--body {
    // border-collapse: collapse;
  }
}
.vxe-modal--content {
  overflow: hidden !important;
}
</style>
