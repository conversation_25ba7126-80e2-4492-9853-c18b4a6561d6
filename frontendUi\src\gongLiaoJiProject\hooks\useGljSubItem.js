/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-02-02 10:35:04
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-07-12 11:54:46
 */
import infoMode from '@/plugins/infoMode';
import gljdetailApi from '@gongLiaoJi/api/projectDetail.js';
import csProject from '@gongLiaoJi/api/csProject';
import detailApi from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useVirtualList } from '@/hooks/useVirtualList';
import { message } from 'ant-design-vue';
import { ref, nextTick, toRaw, reactive } from 'vue';
import {
  quantityExpressionHandler,
  removeSpecialCharsFromPrice,
  everyNumericHandler,
} from '@/utils/index';
import xeUtils from 'xe-utils';
import deMapFun from '@/gongLiaoJiProject/views/projectDetail/customize/deMap';

import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
import { validateExpression } from '@/utils/index';
import { validateQuantityExpression } from '@gongLiaoJi/utils/index';

let {
  // gljCheckTab,
} = recordProjectData();
// vexTable.value: 表格ref
// codeField：编码字段名
// nameField: 名称字段名
// operateList: 操作列表
// resetCellData 重置当前单元格方法
// 小数位数设置
export const decimalLimitationPathArray = (row, fieldArray = []) => {
  let fieldPathArray = [];
  if (row.type === '05') {
    // 定额下主材/设备
    fieldPathArray = [
      { property: 'baseJournalPrice', pathKey: 'EDIT_DEXZS_PRICE_PATH' }, // 单价
      { property: 'price', pathKey: 'EDIT_DEXZS_PRICE_PATH' }, // 单价
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_DEXZS_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_DEXZS_TOTALNUMBER_PATH' }, // 合价
      { property: 'ZSum', pathKey: 'EDIT_DEXZS_ZSUM_PATH' }, // 主材费单价
      { property: 'ZDSum', pathKey: 'EDIT_DEXZS_ZSUM_PATH' }, // 主材费单价
      { property: 'zdTotalSum', pathKey: 'EDIT_DEXZS_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_DEXZS_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'SSum', pathKey: 'EDIT_DEXZS_SSUM_PATH' }, // 设备费单价
      { property: 'SDSum', pathKey: 'EDIT_DEXZS_SSUM_PATH' }, // 设备费单价
      { property: 'sTotalSum', pathKey: 'EDIT_DEXZS_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_DEXZS_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else if (row.type === '06' || row.type === '09') {
    // 定额主材设备
    if (row.deResourceKind === 5 || row.deResourceKind === 4) {
      fieldPathArray = [
        { property: 'baseJournalPrice', pathKey: 'EDIT_DEZS_PRICE_PATH' }, // 单价
        { property: 'price', pathKey: 'EDIT_DEZS_PRICE_PATH' }, // 单价
        {
          property: 'baseJournalTotalNumber',
          pathKey: 'EDIT_DEZS_TOTALNUMBER_PATH',
        }, // 合价
        { property: 'totalNumber', pathKey: 'EDIT_DEZS_TOTALNUMBER_PATH' }, // 合价
        { property: 'ZSum', pathKey: 'EDIT_DEZS_ZSUM_PATH' }, // 主材费单价
        { property: 'ZDSum', pathKey: 'EDIT_DEZS_ZSUM_PATH' }, // 主材费单价
        { property: 'zdTotalSum', pathKey: 'EDIT_DEZS_ZTOTALSUM_PATH' }, // 主材费合价
        { property: 'zTotalSum', pathKey: 'EDIT_DEZS_ZTOTALSUM_PATH' }, // 主材费合价
        { property: 'SSum', pathKey: 'EDIT_DEZS_SSUM_PATH' }, // 设备费单价
        { property: 'SDSum', pathKey: 'EDIT_DEZS_SSUM_PATH' }, // 设备费单价
        { property: 'sTotalSum', pathKey: 'EDIT_DEZS_STOTALSUM_PATH' }, // 设备费合价
        { property: 'sdTotalSum', pathKey: 'EDIT_DEZS_STOTALSUM_PATH' }, // 设备费合价
      ];
    } else {
      // 定额人材机
      fieldPathArray = [
        { property: 'baseJournalPrice', pathKey: 'EDIT_DERCJ_PRICE_PATH' }, // 单价
        { property: 'price', pathKey: 'EDIT_DERCJ_PRICE_PATH' }, // 单价
        {
          property: 'baseJournalTotalNumber',
          pathKey: 'EDIT_DERCJ_TOTALNUMBER_PATH',
        }, // 合价
        { property: 'totalNumber', pathKey: 'EDIT_DERCJ_TOTALNUMBER_PATH' }, // 合价
        { property: 'RSum', pathKey: 'EDIT_DERCJ_RSUM_PATH' }, // 人工费单价
        { property: 'RDSum', pathKey: 'EDIT_DERCJ_RSUM_PATH' }, // 人工费单价
        { property: 'rTotalSum', pathKey: 'EDIT_DERCJ_RTOTALSUM_PATH' }, // 人工费合价
        { property: 'rdTotalSum', pathKey: 'EDIT_DERCJ_RTOTALSUM_PATH' }, // 人工费合价
        { property: 'CSum', pathKey: 'EDIT_DERCJ_CSUM_PATH' }, // 材料费单价
        { property: 'CDSum', pathKey: 'EDIT_DERCJ_CSUM_PATH' }, // 材料费单价
        { property: 'cTotalSum', pathKey: 'EDIT_DERCJ_CTOTALSUM_PATH' }, // 材料费合价
        { property: 'cdTotalSum', pathKey: 'EDIT_DERCJ_CTOTALSUM_PATH' }, // 材料费合价
        { property: 'JSum', pathKey: 'EDIT_DERCJ_JSUM_PATH' }, // 机械费单价
        { property: 'JDSum', pathKey: 'EDIT_DERCJ_JSUM_PATH' }, // 机械费单价
        { property: 'jTotalSum', pathKey: 'EDIT_DERCJ_JTOTALSUM_PATH' }, // 机械费合价
        { property: 'jdTotalSum', pathKey: 'EDIT_DERCJ_JTOTALSUM_PATH' }, // 机械费合价
      ];
    }
  } else if (row.type === '0') {
    fieldPathArray = [
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_UNITLINE_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_UNITLINE_TOTALNUMBER_PATH' }, // 合价
      { property: 'rTotalSum', pathKey: 'EDIT_UNITLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_UNITLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'cTotalSum', pathKey: 'EDIT_UNITLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_UNITLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'jTotalSum', pathKey: 'EDIT_UNITLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_UNITLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'zdTotalSum', pathKey: 'EDIT_UNITLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_UNITLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'sTotalSum', pathKey: 'EDIT_UNITLINE_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_UNITLINE_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else if (row.type === '01' || row.type === '02') {
    fieldPathArray = [
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_FBLINE_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_FBLINE_TOTALNUMBER_PATH' }, // 合价
      { property: 'rTotalSum', pathKey: 'EDIT_FBLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_FBLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'cTotalSum', pathKey: 'EDIT_FBLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_FBLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'jTotalSum', pathKey: 'EDIT_FBLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_FBLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'zdTotalSum', pathKey: 'EDIT_FBLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_FBLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'sTotalSum', pathKey: 'EDIT_FBLINE_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_FBLINE_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else {
    // 定额
    fieldPathArray = [
      { property: 'baseJournalPrice', pathKey: 'EDIT_DE_PRICE_PATH' }, // 单价
      { property: 'price', pathKey: 'EDIT_DE_PRICE_PATH' }, // 单价
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_DE_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_DE_TOTALNUMBER_PATH' }, // 合价
      { property: 'RSum', pathKey: 'EDIT_DE_RSUM_PATH' }, // 人工费单价
      { property: 'RDSum', pathKey: 'EDIT_DE_RSUM_PATH' }, // 人工费单价
      { property: 'rTotalSum', pathKey: 'EDIT_DE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_DE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'CSum', pathKey: 'EDIT_DE_CSUM_PATH' }, // 材料费单价
      { property: 'CDSum', pathKey: 'EDIT_DE_CSUM_PATH' }, // 材料费单价
      { property: 'cTotalSum', pathKey: 'EDIT_DE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_DE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'JSum', pathKey: 'EDIT_DE_JSUM_PATH' }, // 机械费单价
      { property: 'JDSum', pathKey: 'EDIT_DE_JSUM_PATH' }, // 机械费单价
      { property: 'jTotalSum', pathKey: 'EDIT_DE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_DE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'ZSum', pathKey: 'EDIT_DE_ZSUM_PATH' }, // 主材费单价
      { property: 'ZDSum', pathKey: 'EDIT_DE_ZSUM_PATH' }, // 主材费单价
      { property: 'zdTotalSum', pathKey: 'EDIT_DE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_DE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'SSum', pathKey: 'EDIT_DE_SSUM_PATH' }, // 设备费单价
      { property: 'SDSum', pathKey: 'EDIT_DE_SSUM_PATH' }, // 设备费单价
      { property: 'sTotalSum', pathKey: 'EDIT_DE_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_DE_STOTALSUM_PATH' }, // 设备费合价
    ];
  }
  if (fieldArray.length > 0) {
    fieldPathArray = [...fieldArray, ...fieldPathArray];
  }
  return fieldPathArray;
};

export const useSubItem = ({
  operateList,
  vexTable,
  codeField = 'deCode',
  nameField = 'deName',
  frameSelectRef = null,
  showColumns,
  pageType,
  resetCellData = () => {},
  focusTable = () => {},
  checkUnit = () => {},
  emits = () => {},
  listCallback = () => {}, // 列表请求回调
  api = {
    updateData: detailApi.updateFbData,
    gljUpdateData: gljdetailApi.updateFbData,
    getList: detailApi.queryBranchDataByFbIdV1,
    getGsList: gljdetailApi.getDeTree4Unit,
  },
}) => {
  const isFBFX = codeField === 'deCode'; // 是否分部分项
  const projectStore = projectDetailStore();
  let otherApi = gljdetailApi;
  console.log('otherApi', projectStore.$state.type, otherApi);
  let currentInfo = ref();
  let feeFileList = ref([]);
  let tableData = ref([]);
  let originalTableData = ref([]); // 表格原始数据
  let selectData = ref(null);
  let loading = ref(false);
  let page = ref(1);
  let limit = ref(300000);
  let lockFlag = ref(0); // 整体锁定状态 0 不锁定 1 锁定
  let addDataSequenceNbr = ref('');
  let isIndexAddInfo = ref(false); // 是否从索引页面添加数据
  let initDataListObject = {}; // 初始化数据列表
  //多单位------
  let addCurrentInfo = ref(); // 多单位选择时选中的清单数据
  let showUnitTooltip = ref(false); // 是否多单位选择
  let selectUnit = ref(); // 多单位时选择单位
  //end----------
  // 提示信息框------
  let infoVisible = ref(false); // 提示信息框是否显示
  let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
  let iconType = ref(''); // 提示信息框的图标
  let isSureModal = ref(false); // 提示信息框是否为确认提示框
  //end------
  let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
  let isUpdateFile = ref(false);
  const indexVisible = ref(false);
  // 编辑弹框相关
  let editKey = ref(''); // 单独编辑弹框记录得当前编辑字段
  let isShowModel = ref(false);

  let qdVisible = ref(false);
  let deVisible = ref(false);
  let bcDeRow = ref({});
  let rcjVisible = ref(false);
  let priceVisible = ref(false);
  let deCode = ref('');
  let isSortQdCode = ref(false); // 编码重复是否继续
  let isClearEdit = ref(false); // 是否手动清除编辑状态
  let ishasRCJList = ref(false); //人材机定额且有人材机明细数据---不可编辑单价

  let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
  let materialVisible = ref(false); // 是否设置主材市场价弹框
  let materialType = ref('');
  let rowType = ref('');
  let materialRow = ref({});
  let DJGCrefreshFeeFile = ref(false); //单价构成需要刷新取费文件列表
  let batchDeleteVisible = ref(false); // 批量删除弹框是否展示
  let batchALLDeleteVisible = ref(false); //批量删除所有子目
  let batchDataType = ref(1); // 批量删除类型 1 批量删除所有临时删除项  2 批量删除所有工程量为0项
  let openLevelCheckList = ref();
  let associateSubQuotasVisible = ref(false); // 关联子目弹框是否展示
  let zmAssociateData = reactive({
    libraryNameRelation: '',
    zmDeList: [],
    zmPointList: [],
    zmVariableRuleList: [],
  });
  let isReplaceRow = ref(null);
  let currentIndex = ref(0);
  const currentZmDe = ref(null);
  const addZmlistRes = reactive({
    deList: [],
    conversionList: [],
  }); // 新增定额关联的子目
  // const {
  //   initVirtual,
  //   getScroll,
  //   renderedList,
  //   init,
  //   EnterType,
  //   onDragHeight,
  //   scrollToPosition,
  // } = useVirtualList();

  /**
   * 首字母大写 originalBdCode\originalFxCode
   */
  const originalCode = `original${codeField
    .charAt(0)
    .toUpperCase()}${codeField.slice(1)}`;
  const originalName =
    nameField === 'name'
      ? 'originalFxName'
      : `original${nameField.charAt(0).toUpperCase()}${nameField.slice(1)}`;
  const isUpdateByRow = (row, field, newValue, oldValue) => {
    return row[field] !== oldValue;
  };
  /**
   * 编辑完成之后
   * @param {*} param0
   * @returns
   */
  const editClosedEvent = ({ row, column }, newValue, oldValue) => {
    console.log('--------feeFileList', feeFileList.value, column.field);
    if (newValue == oldValue) return;
    if (['deName'].includes(column.field) && (newValue ?? '') === '') {
      row[column.field] = oldValue;
      return;
    }
    if (['01', '02'].includes(row.kind)) {
      row[column.field] = newValue;
      updateFbData(row, column.field);
      return;
    }
    if (isClearEdit.value) return;
    if (row.kind === '05') {
      if (newValue === row[codeField]) return;
      editRcj({ row, column }, newValue, oldValue);
      return;
    }
    let field = column.field;
    if (field == 'quantityExpression') {
      if(!validateQuantityExpression(newValue)){
          row[field] = oldValue;
          return message.error('您输入的工程量表达式非法，请修改！');
      }
      field = 'originalQuantity';
      row.originalQuantity = row.quantityExpression;
    }

    const codeValue = row[codeField]; // deCode,fxCode
    if (field === 'deCode' && newValue !== codeValue && codeValue) {
      isReplaceRow.value = row;
    }
    console.log('row[field]', row[field], newValue, oldValue);
    // 判断单元格值是否被修改
    if (field === 'originalQuantity') {
      if (newValue == oldValue) {
        return;
      } else {
        if (['originalQuantity'].includes(field)) {
          console.log('originalQuantity', row[field]);

          function replaceChineseBrackets(str) {
            return str
              .replace(/[（（]/g, '(')
              .replace(/[））]/g, ')')
              .replace(/×/g, '*')
              .replace(/÷/g, '/');
          }

          let inputValue = replaceChineseBrackets(newValue);
          if (/[^0-9a-zA-Z_|\-|\*|\+|\/|\.|(|)|（|）]/g.test(inputValue)) {
            row[field] = oldValue;
            // vexTable.value.revertData(currentInfo.value, 'originalQuantity');
            return message.error('您输入的工程量表达式非法，请修改！');
          }
          const isNumber = /^-?\d+(\.\d+)?$/.test(inputValue);
          console.log('isNumber', isNumber);
          if (isNumber) {
            // 去掉前导零，保留小数
            inputValue = inputValue.replace(/^0+(?![$.])/, '');
            // inputValue = inputValue.replace(/(\.\d{8}).*$/, '$1');
          }
          row.originalQuantity = inputValue || 0;
          if (
            newValue?.indexOf('GCLMXHJ') == -1 &&
            (oldValue + '')?.indexOf('GCLMXHJ') > -1
          ) {
            infoMode.show({
              iconType: 'icon-qiangtixing',
              infoText:
                '已从工程量明细中引用，修改工程量将清空明细区数据，是否继续？',
              confirm: () => {
                updateGclData(row, field, oldValue);
                infoMode.hide();
              },
              close: () => {
                row.originalQuantity = oldValue;
                infoMode.hide();
                // vexTable.value.revertData(currentInfo.value, 'originalQuantity');
              },
            });
          } else {
            updateGclData(row, field, oldValue);
          }
        }
      }
    } else if (row[field] && newValue == oldValue && field != 'type') return;
    console.log(
      'aaa',
      newValue == oldValue,
      row[field] && newValue == oldValue && field != 'type',
    );

    if (
      field === codeField &&
      !newValue &&
      !['00', '01', '02', '-1'].includes(row.kind)
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '编码不可为空',
        confirm: () => {
          infoMode.hide();
          currentInfo.value[codeField] = currentInfo.value[originalCode];
        },
      });
      return;
    }

    nextTick(() => {
      console.log('222field---', field, row[field]);
      // 如果索引弹窗出现，则不出现补充弹窗
      if ([codeField].includes(column.field) && indexVisible.value) return;
      if (deMapFun.isDe(row.kind) && newValue) {
        isMainQuotaLibraryCode(
          field,
          newValue,
          JSON.parse(JSON.stringify(row)),
        );
      }
      if (
        [
          'price',
          'baseJournalPrice',
          'RDSum',
          'CDSum',
          'JDSum',
          'ZDSum',
          'SDSum',
          'RSum',
          'CSum',
          'JSum',
          'ZSum',
          'SSum',
        ].includes(field)
      ) {
        if (!validateExpression(newValue)) {
          row[field] = oldValue;
          return message.error('您输入的值非法，请修改!');
        }
      }

      if (
        [
          'resQty',
          'totalNumber',
          'price',
          'baseJournalPrice',
          'costMajorName',
          'unit',
        ].includes(field)
      ) {
        if (
          (['baseJournalPrice', 'price'].includes(field) &&
            row.CSum === 0 &&
            row.JSum === 0 &&
            row.RSum === 0 &&
            newValue != 0 &&
            ['03', '04'].includes(row.kind)) ||
          (['baseJournalPrice', 'price'].includes(field) &&
            ['08'].includes(row.kind))
        ) {
          row.originaPrice = oldValue;
          row[field] = newValue;
          priceVisible.value = true;
          return;
        } else if (
          ['baseJournalPrice', 'price'].includes(field) &&
          row.CSum === 0 &&
          row.JSum === 0 &&
          row.RSum === 0 &&
          newValue == 0 &&
          !['06'].includes(row.kind)
        ) {
          return;
        }
        // if (field === 'price' && newValue == 0) {
        //   row.price = Math.round(row.price * 100) / 100;
        //   if (Number(newValue) === 0) {
        //     infoVisible.value = true;
        //     isSureModal.value = true;
        //     infoText.value = '定额单价不能为0';
        //     iconType.value = 'icon-qiangtixing';
        //     row.price = oldValue;
        //     return;
        //   }
        // }
        if (
          !/^\d+(\.\d+)?$/.test(newValue) &&
          ['resQty', 'totalNumber'].includes(field)
        ) {
          row[field] = oldValue;
          return message.error('非法数据');
        }
        if (
          !/^(-?\d+(\.\d+)?)$/.test(newValue) &&
          ['baseJournalPrice', 'price'].includes(field)
        ) {
          row[field] = oldValue;
          return message.error('非法数据');
        }
        if (field == 'unit' && !row[field]) {
          return;
        }
        console.log('单价！！！！！！！');
        row[field] = newValue ?? oldValue;
        if (newValue !== '') priceAndTolalEditEvent(field, row, oldValue); //单价编辑
        return;
      }
      let rcjIds = [
        'RDSum',
        'RSum',
        'CSum',
        'JSum',
        'ZSum',
        'SSum',
        'CDSum',
        'JDSum',
        'SDSum',
        'ZDSum',
      ];
      if (rcjIds.includes(field)) {
        row[field] = newValue ?? oldValue;
        updateRCJPrice(row, field, oldValue);
        return;
      }
      if (
        ![
          codeField,
          'resQty',
          'originalQuantity',
          'totalNumber',
          'costMajorName',
          'unit',
          ...rcjIds,
        ].includes(field)
      ) {
        if (['type'].includes(field)) {
          row.deResourceKind = newValue;
        } else {
          row[field] = newValue;
        }
        updateFbData(row, field);
      }
    });
  };
  const editRcj = ({ row, column }, newValue, oldValue) => {
    if (newValue == oldValue) {
      return;
    }
    let field = column.field;
    if (!row.deCode) {
      currentInfo.value.deCode = currentInfo.value.originalDeCode;
      return;
    }
    let rcjRow = xeUtils.clone(row, true);
    if (field === 'deCode') {
      // field = 'materialCode';
      rcjRow.deCode = newValue;
    }
    if (field === 'deName') {
      field = 'materialName';
      rcjRow.materialName = newValue;
    }
    if (field === 'originalQuantity') {
      field = 'totalNumber';
      rcjRow.totalNumber = newValue;
    }
    if (field === 'price' || field === 'baseJournalPrice') {
      if (projectStore?.taxMade == '1') {
        field = 'marketPrice';
        rcjRow.marketPrice = newValue;
      } else {
        field = 'marketTaxPrice';
        rcjRow.marketTaxPrice = newValue;
      }
    }
    if (field === 'type') {
      rcjRow.deResourceKind = newValue;
    }
    if (field === 'resQty') {
      rcjRow.resQty = newValue;
    }
    if (field === 'unit') {
      rcjRow.unit = newValue;
    }
    if (field === 'specification') {
      rcjRow.specification = newValue;
    }
    console.log(
      'editRcj({ row, column }, newValue, oldValue);',
      field,
      rcjRow,
      newValue,
      oldValue,
    );
    updateConstructRcj(rcjRow, field);
    isRcjCodeMainQuotaLibrary(field, newValue, rcjRow);
  };
  /**
   * 修改定额内的单价
   * @param {Object} row - 当前行
   * @param {string} field -  column field
   */
  const updateRCJPrice = (row, field, oldValue) => {
    let idMap = {
      RDSum: 1,
      RSum: 1,
      CSum: 2,
      CDSum: 2,
      JSum: 3,
      JDSum: 3,
      ZSum: 5,
      ZDSum: 5,
      SSum: 4,
      SDSum: 4,
    };
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: row.sequenceNbr,
      price: row[field],
      type: idMap[field],
    };
    console.log('updateRCJPrice', apiData);
    otherApi.updateRCJPrice(apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      } else {
        row[field] = oldValue;
        message.error(res.message);
      }
    });
  };

  /**
   * 判断输入的定额编码是否为主定额库编码
   * @param {*} field
   * @param {*} deCode
   * @returns
   */
  const isMainQuotaLibraryCode = (field, deCode, row) => {
    console.log('判断是否为主定额册下的标准定额参数', field, codeField);
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: row.sequenceNbr,
      deCode,
    };
    console.log('判断是否为主定额册下的标准定额参数', apiData);
    otherApi.queryDeAndAppendDe(apiData).then(res => {
      console.log('判断是否为主定额册下的标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          console.log('isMainQuotaLibraryCode返回值', res.result);
          addDeInfo.value = res.result;
          queryBranchDataById('queryRule');
        } else {
          isStandardDe(deCode, row);
        }
      }
    });
  };

  /**
   * 判断输入的定额编码是否是标准定额
   * @param {*} code
   */
  const isStandardDe = (code, row) => {
    if (bcDeRow.value.kind) {
      return;
    }
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      libraryCode: projectStore.currentTreeInfo?.deLibrary,
    };

    otherApi.isStandardDe(apiData).then(res => {
      console.log('判断输入的定额编码是否是标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code, row);
        } else {
          bcDeRow.value = row;
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额库下未找到该子目，是否补充子目？',
            confirm: () => {
              deVisible.value = true;
              deCode.value = code;
              infoMode.hide();
            },
            close: () => {
              infoMode.hide();
              bcDeRow.value = {};
              row[codeField] = row[originalCode];
            },
          });
        }
        console.log('判断输入的定额编码是否为主定额库编码', res);
      }
    });
  };

  /**
   * 分部分项 措施项目 替换定额数据
   * @param {*} code
   */
  const updateDeReplaceData = (code, row) => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: row.sequenceNbr,
      code,
    };
    console.log('通过标准编码插入定额', apiData);
    otherApi.appendUserDe(apiData).then(res => {
      console.log('通过标准编码插入定额结果', res);
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = row.sequenceNbr;
        deVisible.value = false;
        message.success('定额替换成功');
        queryBranchDataById();
      }
    });
  };
  // 修改人材机定额
  const updateConstructRcj = (row, field) => {
    if (field === 'deCode') return;
    let value;
    if (field === 'type') {
      value = row.deResourceKind;
    }

    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deId: row.deRowId,
      rcjDetailId: row.sequenceNbr,
      constructRcj: {
        [field === 'type' ? 'kind' : field]:
          field === 'type' ? value : row[field],
      },
    };
    console.log('修改人材机明细数据参数', apiData, otherApi.updateConstructRcj);
    otherApi.updateConstructRcj(apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      }
    });
  };

  // 判断输入的材料编码是否标准人材机数据
  const replaceRcjByCodeData = (code, row) => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      deId: currentInfo.value.deRowId,
      deRowId: currentInfo.value.deRowId,
      rcjDetailId: currentInfo.value.sequenceNbr,
      rcjId: undefined,
    };
    console.log('=======人材机是否是标准数据', apiData);
    otherApi.replaceRcjByCodeData(apiData).then(res => {
      console.log('========人材机是否是标准数据', res);
      if (res.status === 200) {
        if (res.result) {
          rcjVisible.value = false;
          queryBranchDataById();
        } else if (res.result === 'unqualified') {
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-querenshanchu',
            infoText: '配合比材料下不允许增加配合比材料',
            confirm: () => {
              infoMode.hide();
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            },
          });
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '主定额库不存在该材料编码，是否补充人材机？',
            confirm: () => {
              rcjVisible.value = true;
              infoMode.hide();
              deCode.value = code;
            },
            close: () => {
              infoMode.hide();
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            },
          });
        }
      }
    });
  };

  /**
   * 判断输入的材料编码是否与主定额库编码相同
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isRcjCodeMainQuotaLibrary = (field, code, row) => {
    console.log('判断输入的材料编码是否与主定额库编码相同', field, codeField);
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    otherApi.isRcjCodeMainQuotaLibrary(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          // 输入的编码为主定额库编码
          updateBjqRcjReplaceData(code);
        } else {
          if (row.kind === '05') {
            replaceRcjByCodeData(code, row);
          } else {
            isStandardRcj(code);
          }
        }
      }
    });
  };

  /**
   * 分部分项 措施项目 替换编辑区的人材机数据
   * @param {*} code
   */
  const updateBjqRcjReplaceData = code => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      deId: currentInfo.value.deRowId,
      code: code,
      region: 0,
      rcjDetailId: currentInfo.value.sequenceNbr,
      rcjId: undefined,
    };
    console.log('明细区替换人材机===updateBjqRcjReplaceData', apiData);
    otherApi.updateBjqRcjReplaceData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        message.success('人材机替换成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否标准人材机数据
   * @param {*} code
   */
  const isStandardRcj = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    otherApi.isStandardRcj(apiData).then(res => {
      console.log('=============');
      if (res.status === 200) {
        if (res.result) {
          updateBjqRcjReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额下不存在该材料编码,是否补充人材机？',
            confirm: () => {
              rcjVisible.value = true;
              deCode.value = code;
              infoMode.hide();
            },
            close: () => {
              currentInfo.value.deCode = currentInfo.value.originalBdCode;
              infoMode.hide();
            },
          });
        }
      }
    });
  };

  /**
   * 判断是否是标准清单
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isStandQd = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    otherApi.isStandQd(apiData).then(res => {
      console.log('判断是否是标准清单', res);
      if (res.status === 200) {
        if (res.result) {
          const unit = res.result.unit;
          const unitArr = Array.isArray(unit) ? unit : unit?.split('/');
          res.result.unit = unitArr;
          addCurrentInfo.value = res.result;
          addCurrentInfo.value.bdCodeLevel04 = code;
          if (code.length === 9) {
            if (unitArr && unitArr.length > 1) {
              showUnitTooltip.value = true;
            } else {
              updateQdByCode(code, unitArr[0]);
            }
          } else {
            isQdCodeExist(code, res.result);
          }
        } else {
          isQdCodeExist(code, res.result);
        }
      }
    });
  };

  /**
   * 判断清单编码是否存在
   * @param {*} code
   * @param {*} obj
   */
  const isQdCodeExist = (code, obj) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    otherApi.isQdCodeExist(apiData).then(res => {
      console.log('判断清单编码是否存在', res, obj);
      // if (res.status === 200) {
      if (res) {
        // 若存在,则弹框提示是否继续
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: obj ? '' : '是否补充清单？',
          descText: obj
            ? '当前单位工程有相同清单编码，是否自动排序清单编码？'
            : '当前单位工程有相同清单编码，是否继续?',
          confirm: () => {
            if (!obj) {
              deCode.value = code;
              qdVisible.value = true;
              isSortQdCode.value = false;
            } else {
              isSortQdCode.value = true;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            if (obj) {
              isSortQdCode.value = false;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            } else {
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            }
          },
        });
      } else {
        // 根据是否为标准数据判断替换或补充
        if (!obj) {
          deCode.value = code;
          qdVisible.value = true;
        } else {
          if (obj.unit && obj.unit.length > 1) {
            showUnitTooltip.value = true;
          } else {
            updateQdByCode(code, obj.unit ? obj.unit[0] : null);
          }
        }
      }
      // }
    });
  };

  /**
   * 通过标准编码插入清单
   * @param {*} code
   * @param {*} unit
   */
  const updateQdByCode = (code, unit) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    console.log('==============标准清单编码插入api参数', apiData);
    otherApi.updateQdByCode(apiData).then(res => {
      console.log('标准清单编码插入', res);
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        message.success('清单插入成功');
        queryBranchDataById('noPosition');
      }
    });
  };

  const costMajorNameEditEvent = (field, row) => {
    if (field !== 'costMajorName') return;

    row.costFileCode = feeFileList.value.filter(
      x => x.qfName === row.costMajorName,
    )[0].qfCode;
  };
  const priceAndTolalEditEvent = (field, row, oldValue) => {
    console.log('priceAndTolalEditEvent', field, row);

    updateField(field, row, oldValue);
  };
  const updateField = (field, row, oldValue) => {
    console.log('updateField', currentInfo.value);
    let apiRoute = {
      price: 'updatePrice',
      baseJournalPrice: 'updatePrice',
      totalNumber: 'updateTotal',
      resQty: 'updateResQty',
      costMajorName: 'updateChargingDiscipline',
      unit: 'updateUnit',
    };
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deRowId: JSON.parse(JSON.stringify(row)).sequenceNbr,
      [field]: JSON.parse(JSON.stringify(row))[field],
    };
    if (field === 'baseJournalPrice') {
      apiData['price'] = apiData.baseJournalPrice;
    }
    if (field === 'costMajorName') {
      apiData.costFileCode = feeFileList.value.filter(
        x => x.qfName === apiData[field],
      )[0].qfCode;
    }
    console.log(
      'otherApi[apiRoute[field]]',
      otherApi[apiRoute[field]],
      apiData,
    );
    otherApi[apiRoute[field]](apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      } else {
        if (
          (field === 'baseJournalPrice' || field === 'price') &&
          res?.result === 'T001'
        ) {
          row.originaPrice = oldValue;
          priceVisible.value = true;
        } else {
          row[field] = oldValue;
          message.error(res.message);
        }
      }
    });
  };
  // 更新工程量数据
  const updateGclData = (row, field, oldValue) => {
    console.log('更新工程量数据');
    isUpdateFile.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deId: row.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      quantity: row.originalQuantity,
      type: 'yss',
    };
    console.log('apiDataupdateQuantity', apiData);
    otherApi.updateQuantity(apiData).then(res => {
      if (res.status === 200 && res.result && res.result.code !== 500) {
        message.success('修改成功');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById('noPosition');
      } else {
        row.originalQuantity = oldValue;
        message.error(res.result.message);
      }
    });
  };
  /**
   * 更新数据
   * @param {*} row
   * @param {*} field
   */
  const updateFbData = (row, field) => {
    if (projectStore.type === 'glj') {
      updateGlj(row, field);
    }
  };
  const updateGlj = (row, field) => {
    costMajorNameEditEvent(field, row);
    isUpdateFile.value = true;
    let apiData = {
      deRow: JSON.parse(JSON.stringify(row)),
    };
    console.log('概算更新数据参数', apiData);
    api.gljUpdateData(apiData).then(res => {
      console.log('gljUpdateData修改后', res);
      if (res.status === 200) {
        if (field !== 'seq') {
          message.success('修改成功');
          if (editKey.value) {
            isShowModel.value = false;
          }
          if (infoVisible.value) {
            isUpdateQuantities.value = true;
            infoVisible.value = false;
          }
        }
        if (
          (row.kind === '01' || row.kind === '02' || row.kind === '0') &&
          field === nameField
        ) {
          emits('updateMenuList');
        }
        if (
          field === 'costMajorName' ||
          field === 'zjfPrice' ||
          field === 'quantityExpression' ||
          field === 'quantity'
        ) {
          isUpdateFile.value = true;
        }
        if (currentInfo.value) {
          addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        }
        if (field == 'annotations' || field == 'isShowAnnotations') {
          queryBranchDataById('annotations');
        } else {
          queryBranchDataById();
        }
      }
    });
  };
  const initObject = row => {
    let obj = {};
    Object.keys(initDataListObject).forEach(key => {
      obj[key] = JSON.parse(JSON.stringify(row))[key];
    });
    return obj;
  };
  const addLevelToTree = (data, parentLevel = 0) => {
    return data.map(node => ({
      ...node,
      id: node.sequenceNbr,
      customLevel: parentLevel + 1,
      children:
        (node.children || []).length > 0
          ? addLevelToTree(node.children, parentLevel + 1)
          : [],
    }));
  };
  const getYsList = (EnterTypes, posId) => {
    if (isFBFX && !projectStore.asideMenuCurrentInfo?.sequenceNbr) return;
    if (!isFBFX && (!projectStore.currentTreeInfo?.id || loading.value)) return;
    checkUnit();
    loading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      pageSize: limit.value,
      pageNum: page.value,
      isAllFlag: !!posId,
    };

    api.getList(apiData).then(res => {
      if (res.status === 200) {
        if (!res.result) {
          loading.value = false;
          tableData.value = [];
          // 快速组价存的当前列表数据
          return;
        }

        let testTreeData = xeUtils.toArrayTree(res.result.data, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        res.result.data = xeUtils.toTreeArray(addLevelToTree(testTreeData));
        changeListHandler(res.result.data);
        tableData.value = res.result.data;
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));

        // virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
        });

        // frameSelectRef?.value.clearSelect();
        // lockFlagHandler();
        // addDataHandler(posId);
        loading.value = false;
        emits('getCurrentInfo', currentInfo.value);
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
        }, 500);
      }
    });
  };
  const getGsList = (EnterTypes, posId, deleteIndex) => {
    // if (isFBFX && !projectStore.asideMenuCurrentInfo?.sequenceNbr) return;
    // if (!isFBFX && (!projectStore.currentTreeInfo?.id || loading.value)) return;
    // checkUnit();
    // function countTreeNodes(treeData) {
    //   let count = 0;

    //   // 遍历数组中的每个元素
    //   for (const node of treeData) {
    //     count++; // 当前节点计数加1

    //     // 如果节点有子节点，递归计算子节点的数量
    //     if (node.children && Array.isArray(node.children)) {
    //       count += countTreeNodes(node.children);
    //     }
    //   }

    //   return count;
    // }
    console.log('getGsList-------EnterTypes', EnterTypes);
    loading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deRowId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      posId,
    };
    if (EnterTypes !== 'annotations') {
      apiData['isShowAnnotations'] = false;
    }
    // let res = {
    //   result: da,
    // };
    // tableData.value = res.result
    // console.log('apiData', tableData.value,countTreeNodes(tableData.value));
    // loading.value = false;
    console.info('获取概算项目参数！', apiData);
    api.getGsList(apiData).then(async res => {
      console.info('6666667776676', res);
      if (res.result[0]) {
        if (res.result[0].type == '0') {
          res.result[0].kind = '00';
          res.result[0].deName = res.result[0].deName || '单位工程';
        }
        let conversionRuleList = await conversionRuleListAll();
        const idMap = {};
        // 遍历数据，找到所有type为03的条目
        let testTreeData = xeUtils.toArrayTree(res.result, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        res.result = xeUtils.toTreeArray(
          addLevelToTree(
            testTreeData,
            projectStore.asideMenuCurrentInfo?.customLevel - 1,
          ),
        );
        res.result.forEach((item, index) => {
          item.annotationsVisible = item.isShowAnnotations;
          // 标准换算标识
          conversionRuleList.map(a => {
            if (a.sequenceNbr === item.sequenceNbr) {
              item.redArray = a.redArray;
              item.blackArray = a.blackArray;
            }
          });
          idMap[item.deRowId] = item;
          item.key = item.sequenceNbr;
          delete item.children;
          // item.dispNo = index
          if (item.type === '05') {
            item.parentKind = res.result.find(
              a => a.sequenceNbr === item.parentId,
            ).type;
            const parentCustom = res.result.find(
              a => a.sequenceNbr === item.parentId
            );
            if (parentCustom) {
              item.parentCustom = JSON.parse(JSON.stringify(parentCustom));
            }
          }
          // 判断是否可以上下移动
          let datas = res.result.filter(a => a.parentId == item.parentId);
          let rowIndex = datas.findIndex(
            a => a.sequenceNbr == item.sequenceNbr,
          );
          if (rowIndex === 0) {
            item.isFirst = true;
          }
          if (rowIndex === datas.length - 1) {
            item.isLast = true;
          }
          // // 判断是否可以升降级
          // if (item.type === '01') {
          //   item.isUp = true
          //   if(res.result[index+1]?.type !== '02'||index === res.result.length-1) {
          //     item.isDown = true
          //   }
          // }
          // if (item.type === '02') {
          //   if(res.result[index+1]?.type !== '02'||index === res.result.length-1) {
          //     item.isDown = true
          //   }
          //   if(res.result[index-1]?.type !== '02'&&res.result[index-1]?.type !== '01') {
          //     item.isUp = true
          //   }
          // }
          // 查找父级条目
          const parentId = item.parentId;
          const parentItem = parentId ? idMap[parentId] : null;
          // 检查父级type是否为0、01或02
          if (parentItem && ['0', '01', '02'].includes(parentItem.type)) {
            // 设置一个属性标识这是最父级type为03的数据
            item.isTopLevelType03 = true;
            item.isOriginalQuantity = true;
            if (item.type != '07') item.resQty = '';
          }
          // 如果父级是03定额并且工程量不为0则这条定额就可以编辑工程量
          if (parentItem && parentItem.originalQuantity != undefined) {
            if (parentItem.type == '03' && parentItem.originalQuantity == 0) {
              item.isOriginalQuantity = false;
            } else {
              item.isOriginalQuantity = true;
            }
          }
        });
        changeListHandler(res.result);
        tableData.value = res.result.map(a => {
          return { kind: a.type, optionMenu: [3, 4, 5], ...a };
        });
        console.log('tableData___', tableData.value);
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));
        console.log('gljtableData', tableData.value);
        // virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          let gljCheckTab = projectStore.gljCheckTab;
          let upSelRow = gljCheckTab[
            projectStore.currentTreeInfo.sequenceNbr
          ]?.tabList.find(a => a.tabName == '预算书');
          console.log('upSelRow', upSelRow);
          if (deleteIndex !== -1) {
            //处理删除数据重新定位到删除选中的那行
            if (tableData.value.length > deleteIndex) {
              currentInfo.value = tableData.value[deleteIndex];
            } else if (tableData.value.length > 0) {
              currentInfo.value = tableData.value[tableData.value.length - 1];
            }
          } else if (upSelRow && upSelRow.selRowId !== '') {
            let obj = tableData.value.find(
              a => a.sequenceNbr == upSelRow.selRowId,
            );
            if (!obj) {
              obj = tableData.value[0];
            }
            currentInfo.value = obj;
            setTimeout(() => {
              if (EnterTypes !== 'noPosition') {
                vexTable.value?.scrollTo({ rowKey: obj.key }, 'auto');
              }
            }, 100);
          } else if (!currentInfo.value && !posId) {
            currentInfo.value = tableData.value[0];
          } else {
            console.log(posId, 'listObj');
            currentInfo.value =
              tableData.value.find(
                a => a.sequenceNbr === currentInfo.value.sequenceNbr,
              ) || tableData.value[0];
            if (
              !tableData.value.find(
                a => a.sequenceNbr === currentInfo.value.sequenceNbr,
              )
            ) {
              message.error('未查询到当前数据！');
            }
          }
          if (currentInfo.value && !posId && EnterTypes === 'position') {
            setTimeout(() => {
              if (EnterTypes !== 'noPosition') {
                vexTable.value?.scrollTo(
                  { rowKey: currentInfo.value.key },
                  'auto',
                );
              }
              let key = vexTable.value.getSelectedRange()[0].columns[0].key;
              vexTable.value?.clearAllSelectedRange();
              vexTable.value.appendCellToSelectedRange({
                rowStartIndex: tableData.value.findIndex(
                  a => a.sequenceNbr === currentInfo.value.sequenceNbr,
                ),
                rowEndIndex: tableData.value.findIndex(
                  a => a.sequenceNbr === currentInfo.value.sequenceNbr,
                ),
                columnStartKey: key,
                columnEndKey: key,
              });
            }, 100);
          } else if (EnterTypes !== 'noPosition') {
            setTimeout(() => {
              if (currentInfo.value?.key) {
                vexTable.value?.scrollTo(
                  { rowKey: currentInfo.value.key },
                  'auto',
                );
              }
            }, 100);
          }
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
          if (posId) {
            let listObj = tableData.value.find(i => i.sequenceNbr == posId);
            console.log('listObj', posId, listObj);
            if (!listObj) {
              message.error('未查询到当前数据！');
            }
            vexTable?.value.scrollTo({ rowKey: currentInfo.value.key }, 'auto');
          }
          if (EnterTypes === 'queryRule') {
            queryRcjDataByDeId();
          }
          listCallback();
          // focusTable();
        });
        // addDataHandler(posId);
        // tableRef.value.scrollTo(posId,'auto');
        loading.value = false;
        emits('getCurrentInfo', currentInfo.value);
        queryFeeFileData(true);
        getSubItemProjectBtn();
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
          projectStore.isAutoPosition = false;
        }, 500);
        // let elements = document.querySelectorAll(".tableCellClass");
        // console.info(1111111111111111)
        // console.info(elements)
        // setTimeout(() => {
        //   nextTick(() => {
        //     let num=9999
        //     elements.forEach(function(element) {
        //       element.style.zIndex = num;
        //       num--
        //     });
        //   });
        // },1000)
      }
    });
  };
  // 分部分项，展开判断
  const getSubItemProjectBtn = () => {
    csProject
      .openLevelCheckList({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        unitId: projectStore.currentTreeInfo?.id,
      })
      .then(res => {
        openLevelCheckList.value = res.result;
        console.log('getSubItemProjectBtn', openLevelCheckList.value);
        operateList.value.forEach(item => {
          if (item.name == 'openData') {
            item.options.forEach(item => {
              item.isValid = res.result[item.kind]?.hasCheck;
            });
          }
        });
      });
  };
  const conversionRuleListAll = async materialCode => {
    return new Promise((resolve, reject) => {
      gljdetailApi
        .conversionRuleListAll({
          constructId: projectStore.currentTreeGroupInfo?.constructId,
          unitId: projectStore.currentTreeInfo?.id,
        })
        .then(res => {
          resolve(res.result);
        })
        .catch(error => {
          reject(error);
        });
    });
  };

  /**
   * 处理新的表格数据
   */
  const handleNewTable = async result => {
    let res = {
      result,
    };
    if (res.result[0]) {
      if (res.result[0].type == '0') {
        res.result[0].kind = '00';
        res.result[0].deName = res.result[0].deName || '单位工程';
      }
      let conversionRuleList = await conversionRuleListAll();
      const idMap = {};
      // 遍历数据，找到所有type为03的条目
      let testTreeData = xeUtils.toArrayTree(res.result, {
        key: 'sequenceNbr',
        parentKey: 'parentId',
      });
      res.result = xeUtils.toTreeArray(addLevelToTree(testTreeData));
      res.result.forEach((item, index) => {
        item.annotationsVisible = item.isShowAnnotations;
        // 标准换算标识
        conversionRuleList.map(a => {
          if (a.sequenceNbr === item.sequenceNbr) {
            item.redArray = a.redArray;
            item.blackArray = a.blackArray;
          }
        });
        idMap[item.deRowId] = item;
        item.key = item.sequenceNbr;
        item.displaySign = 0;
        delete item.children;
        // item.dispNo = index
        if (deMapFun.isDe(item.type)) {
          // 查找父级条目
          const parentId = item.parentId;
          const parentItem = parentId ? idMap[parentId] : null;
          // 检查父级type是否为0、01或02
          if (parentItem && ['0', '01', '02'].includes(parentItem.type)) {
            // 设置一个属性标识这是最父级type为03的数据
            item.isTopLevelType03 = true;
            if (item.type != '07') item.resQty = '';
          }
        }
      });
      changeListHandler(res.result);
      tableData.value = res.result.map(a => {
        return { kind: a.type, optionMenu: [3, 4, 5], ...a };
      });
      console.log('gljtableData', tableData.value);

      queryFeeFileData(true);
      setTimeout(() => {
        addDataSequenceNbr.value = '';
        isIndexAddInfo.value = false;
        addCurrentInfo.value = null;
      }, 500);
    }
  };

  /**
   * 获取列表信息
   * @param {*} EnterType //other 从其他页面需要初始化数据 ，Refresh, 修改了刷新数据
   */
  const queryBranchDataById = (
    EnterTypes = 'Refresh',
    posId = '',
    deleteIndex = -1,
  ) => {
    if (projectStore.type === 'glj') {
      getGsList(EnterTypes, posId, deleteIndex);
    }
  };

  /**
   * 虚拟滚动处理
   * @param {*} type
   * @param {*} posId
   */
  const virtualListHandler = (type, posId) => {
    EnterType.value = type;
    // const initList = init(tableData.value);
    setTimeout(() => {
      // initList();
      if (posId) {
        // scrollToPosition(posId, tableData.value);
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId,
        );
        // vexTable.value.setCurrentRow(currentInfo.value);
        projectStore.isAutoPosition = false;
      }
    }, 10);
  };

  /**
   * 插入数据逻辑处理
   * @returns
   */
  // const addDataHandler = posId => {
  //   if (addDataSequenceNbr.value) {
  //     tableData.value.forEach(item => {
  //       if (!isIndexAddInfo.value) {
  //         if (item.sequenceNbr === addDataSequenceNbr.value) {
  //           currentInfo.value = item;
  //           vexTable.value.setCurrentRow(item);
  //           vexTable.value.scrollToRow(item);
  //         }
  //       } else if (item.sequenceNbr === currentInfo.value.sequenceNbr) {
  //         currentInfo.value = item;
  //       }
  //     });
  //     nextTick(() => {
  //       frameSelectRef?.clearSelect();
  //       vexTable.value.setCurrentRow(currentInfo.value);
  //       resetCellData();
  //       console.log('nextTick', currentInfo.value);
  //     });
  //   } else if (!isIndexAddInfo.value) {
  //     if (posId) return;
  //     let hasCurrentInfo = true; // 有点击选中的数据
  //     if (!currentInfo.value?.sequenceNbr) {
  //       // 没有点击选中的，则直接替换成数据第一个
  //       hasCurrentInfo = false;
  //     } else {
  //       // 有选中数据，但是在列表中没有找到
  //       if (
  //         tableData.value.every(
  //           item => item.sequenceNbr !== currentInfo.value.sequenceNbr
  //         )
  //       ) {
  //         hasCurrentInfo = false;
  //       } else {
  //         currentInfo.value = tableData.value.find(
  //           item => item.sequenceNbr === currentInfo.value.sequenceNbr
  //         );
  //       }
  //     }

  //     let handleCurrentInfo = currentInfo.value; // 重新赋值，为了人才机等子页面能监听到变化掉借口
  //     if (!hasCurrentInfo) {
  //       // 没有选中的数据，默认第一个选中，并清除所有数据
  //       handleCurrentInfo = tableData.value[0];
  //     }
  //     currentInfo.value = '';
  //     nextTick(() => {
  //       currentInfo.value = handleCurrentInfo;
  //       vexTable.value.setCurrentRow(currentInfo.value);
  //     });
  //   }
  // };

  /**
   * 锁定处理
   */
  const lockFlagHandler = () => {
    lockFlag.value = tableData.value
      .filter(data => data.kind === '03')
      .some(item => item.isLocked);
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
  };

  /**
   * 组价方案匹配条件筛选
   * @param {*} val
   */
  const filterData = val => {
    let tempList = [];
    tableData.value = [];
    if (val.length === 0 || !val) {
      tableData.value = originalTableData.value;
    } else {
      originalTableData.value.forEach(item => {
        if (val.includes(item.matchStatus)) {
          tempList.push(item.sequenceNbr);
        }
      });
      for (let i = 0; i < originalTableData.value.length; i++) {
        if (
          tempList.includes(originalTableData.value[i].sequenceNbr) ||
          tempList.includes(originalTableData.value[i].parentId)
        ) {
          tableData.value.push(originalTableData.value[i]);
        }
      }
      tableData.value.forEach((item, index) => {
        item.index = (page.value - 1) * limit.value + (index + 1);
      });
    }
    // const initList = init(tableData.value);
    nextTick(() => {
      initList();
    });
  };

  /**
   * 对列表原数据做处理，对之前分开处理做合并一块处理，后续需要对数据做循环处理，统一在这里做处理
   * @param {} data
   */
  const changeListHandler = data => {
    for (let i = 0; i < data.length; ++i) {
      let item = data[i];
      if (item.defaultLine) {
        item.measureType = '';
      }
      if (item.appendType && item.appendType.length > 0) {
        if (item.appendType.includes('换')) {
          item.changeFlag = '换';
        }
        if (item.appendType.includes('借')) {
          item.borrowFlag = '借';
        }
      }
      item.index = (page.value - 1) * limit.value + (i + 1);
      item[originalCode] = item[codeField];
      item[originalName] = item[nameField];
      item.originalQuantityExpression = item.quantityExpression;
      // item.originalQuantity = item.quantity;
      // item.originalZjfPrice = item.zjfPrice;
    }
  };

  /**
   * 获取所有的取费文件列表
   */
  const queryFeeFileData = (isRefresh = false) => {
    otherApi.queryBaseFeeData().then(res => {
      if (res.status === 200 && res.result) {
        feeFileList.value = res.result;
        DJGCrefreshFeeFile.value = false;
      }
    });
  };

  let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
  /**
   * 获取人材机明细数据
   * @param {*} bol
   * @param {*} deItem
   * @returns
   */
  const queryRcjDataByDeId = (bol = true, deItem = null) => {
    let apiData = {
      deRowId: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      type: bol ? addDeInfo.value?.type : deItem.type,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    if (!apiData.deRowId) return;
    otherApi.getAllRcjDetail(apiData).then(res => {
      console.log('定额明细数据-查询标准换算', res, apiData);
      if (res.status === 200) {
        if (res.result?.length > 0) {
          ishasRCJList.value = true;
        }

        if (bol) {
          mainMaterialTableData.value = res.result.filter(
            x => x.kind === 5 || x.kind === 4,
          );
          console.log('cda', res.result, mainMaterialTableData.value);
          const showUnpricedPop =
            projectStore.convenienceSettings.get('UNPRICED')?.unPricedPop;
          if (mainMaterialTableData.value.length > 0 && showUnpricedPop) {
            materialVisible.value = true;
          } else {
            queryRule();
          }
        } else {
          queryRule();
        }
      }
    });
  };
  /**
   * 选中单条分部分项数据
   * @param {*} param0
   */
  const currentChangeEvent = ({ row }) => {
    // const $table = vexTable.value;
    // 判断单元格值是否被修改
    // if ($table.isEditByRow(currentInfo.value)) return;
    currentInfo.value = row;
    ishasRCJList.value = false;
    if (row.kind === '04' && row.rcjFlag === 1) {
      queryRcjDataByDeId(false, row);
    }
    projectStore.SET_SUB_CURRENT_INFO(row);
    // emits('getCurrentInfo', currentInfo.value);
  };

  let editContent = ref('');
  // 编辑内容保存事件
  const saveContent = () => {
    const valueType = typeof editContent.value;
    if (valueType !== 'string' || editContent.value.trim() === '') {
      if (editKey.value === 'quantityExpression') {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-qiangtixing',
          infoText: '请输入工程量表达式',
          confirm: () => {
            infoMode.hide();
          },
        });
      }
      return;
    }
    currentInfo.value[editKey.value] = editContent.value;

    if (editKey.value !== 'quantityExpression') {
      updateFbData(currentInfo.value, editKey.value);
    }
  };

  let showModelTitle = ref('名称编辑');
  /**
   * 打开编辑弹框方法
   * @param {*} field
   */
  const openEditDialog = field => {
    editKey.value = field;
    switch (field) {
      case nameField:
        showModelTitle.value = '名称编辑';
        break;
      case 'projectAttr':
        showModelTitle.value = '项目特征编辑';
        break;
      case 'quantityExpression':
        showModelTitle.value = '工程量表达式编辑';
        break;
    }
    isShowModel.value = true;
    editContent.value = currentInfo.value[field];
  };

  let standardVisible = ref(false);
  /**
   * 获取定额是否存在标准换算信息
   * @returns
   */
  const queryRule = () => {
    const standardPop = projectStore.convenienceSettings.get(
      'STANDARD_CONVERSION',
    );
    if (!addDeInfo.value?.standardId || !standardPop) {
      if (addDeInfo.value?.standardId) {
        // 标准换算弹窗隐藏，继续判断显示子目关联弹窗
        getGlgSettingData(addDeInfo.value?.standardId);
      }
      return;
    }
    let apiData = {
      standardDeId: addDeInfo.value?.standardId,
      fbFxDeId: addDeInfo.value?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
    };
    console.log('标准换算列表参数', apiData);
    otherApi.queryRule(apiData).then(res => {
      if (res.status === 200 && res.result && res.result.conversionList) {
        if (res.result.conversionList && res.result.conversionList.length > 0) {
          console.log('标准换算列表数据', res);
          standardVisible.value = true;
        } else {
          // 标准换算弹窗隐藏，继续判断显示子目关联弹窗
          getGlgSettingData(addDeInfo.value?.standardId);
        }
      }
    });
  };
  // 临时删除
  const updateDelTempStatusColl = selectData => {
    // let ids = [];
    // let deRowId = '';
    // if (row.kind === '05') {
    //   ids.push(row.sequenceNbr);
    //   deRowId = row.parentId;
    // } else if (selectData.value?.length > 1) {
    //   selectData.value.forEach(item => {
    //     ids.push(item.sequenceNbr);
    //   });
    // } else {
    //   ids.push(currentInfo.value.sequenceNbr);
    // }
    let deRowId = '';
    let ids = [];
    console.log('selectData', selectData);
    let deList = [];
    if (selectData && selectData.length > 0) {
      selectData.forEach(item => {
        ids.push(item.sequenceNbr);
        deList.push({
          id: item.sequenceNbr,
          type: item.type,
          parentId: item.parentId,
        });
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
      deList.push({
        id: currentInfo.value.sequenceNbr,
        type: currentInfo.value.type,
        parentId: currentInfo.value.parentId,
      });
    }
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
      deRowId,
      deList,
    };
    console.log('临时删除参数', apiData);
    gljdetailApi.tempRemoveDeRow(apiData).then(res => {
      console.log('res临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById('noPosition');
      }
    });
  };
  // 取消临时删除
  const updateCancelDelTempStatusColl = selectData => {
    // let ids = [];
    // let deRowId = '';
    // if (row.kind === '05') {
    //   ids.push(row.sequenceNbr);
    //   deRowId = row.parentId;
    // } else if (selectData.value?.length > 1) {
    //   selectData.value.forEach(item => {
    //     ids.push(item.sequenceNbr);
    //   });
    // } else {
    //   ids.push(currentInfo.value.sequenceNbr);
    // }
    let deRowId = '';
    let ids = [];
    let deList = [];
    if (selectData && selectData.length > 0) {
      selectData.forEach(item => {
        ids.push(item.sequenceNbr);
        deList.push({
          id: item.sequenceNbr,
          type: item.type,
          parentId: item.parentId,
        });
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
      deList.push({
        id: currentInfo.value.sequenceNbr,
        type: currentInfo.value.type,
        parentId: currentInfo.value.parentId,
      });
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
      deRowId,
      deList,
    };
    console.log('取消临时删除参数', apiData);
    gljdetailApi.cancelTempRemoveDeRow(apiData).then(res => {
      console.log('res取消临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById('noPosition');
      }
    });
  };
  // 取消临时删除
  const batchDeleteFun = () => {
    let ids = [];
    if (selectData.value?.length > 1) {
      selectData.value.forEach(item => {
        ids.push(item.sequenceNbr);
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
    };
    console.log('取消临时删除参数', apiData);
    gljdetailApi.cancelTempRemoveDeRow(apiData).then(res => {
      console.log('res取消临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById();
      }
    });
  };

  let areaStatus = ref(false);
  let areaVisibleType = ref('');
  let AnnotationsCurrent = ref(null);
  let AnnotationsRefList = ref({});
  // 鼠标批注右键操作
  const handleNoteClick = (item, row) => {
    // console.log('🚀 ~ handleNoteClick ~ row:', row);
    delete row.children;
    switch (item.code) {
      case 'edit-note':
      case 'add-note':
        editAnnotations(row);
        break;
      case 'del-note':
        updateFbData(
          { ...row, annotations: '', isShowAnnotations: false },
          'annotations',
        );
        break;
      case 'show-note':
        updateFbData({ ...row, isShowAnnotations: true }, 'isShowAnnotations');
        break;
      case 'hide-note':
        updateFbData({ ...row, isShowAnnotations: false }, 'isShowAnnotations');
        break;
      case 'del-all-note':
        areaStatus.value = true;
        areaVisibleType.value = 'note-all';
        break;
      default:
        break;
    }
  };

  const onFocusNode = row => {
    if (AnnotationsCurrent.value != row.sequenceNbr) {
      console.log('🚀 ~手动选中');
      editAnnotations(row);
    }
  };

  const editAnnotations = row => {
    let isDeName = showColumns.value.filter(a => a.field == 'deName');
    if (isDeName.length == 0) {
      return message.warning('名称列已隐藏，请展开后操作！');
    }
    row.noteEditVisible = true;
    row.annotationsVisible = true;
    tableData.value = [...tableData.value];
    nextTick(() => {
      AnnotationsCurrent.value = row.sequenceNbr;
      AnnotationsRefList.value[row.sequenceNbr]?.focusNode();
    });
  };
  // 关闭编辑的
  const closeAnnotations = (v, row) => {
    if (!row?.isShowAnnotations) {
      row.noteEditVisible = false;
      row.noteViewVisible = false;
      row.annotationsVisible = false;
    }
    if (v == row?.annotations) {
      return;
    }
    updateFbData({ ...row, annotations: v }, 'annotations');
  };

  const getAnnotationsRef = (el, row) => {
    if (el) {
      AnnotationsRefList.value[row.sequenceNbr] = el;
    } else {
      AnnotationsRefList.value[row.sequenceNbr] = null;
    }
  };
  const closeAreaModal = v => {
    areaStatus.value = false;
    if (v) {
      let apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        applyConstruct: v == '1' ? true : false,
      };
      gljdetailApi
        .deleteAllDeAnnotations(apiData)
        .then(res => {
          queryBranchDataById('other');
        })
        .finally(() => {
          areaVisibleType.value = '';
        });
    }
  };
  const getGlgSettingData = deId => {
    console.log('————————————————————————————调用getGlgSettingData');
    csProject
      .getGljSetUp({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {
        console.log('res.result', res.result, JSON.stringify(res.result));
        const canPopUp = res.result.get('RELATION_DE')?.relationDePop;
        console.log('是否显示子目关联弹窗', canPopUp);
        if (canPopUp) {
          hasAssociatedWithSubItem(deId);
        }
      });
  };
  const hasAssociatedWithSubItem = deId => {
    const apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deId: deId || addDeInfo.value?.standardId,
    };
    console.log('——————————————是否有子目接口参数', apiData);
    gljdetailApi.isAssociatedWithSubItem(apiData).then(res => {
      console.log('____________是否有子目', res);
      if (res.status === 200 && res.result) {
        associateSubQuotasVisible.value = true;
        getAssociatedQuota(apiData);
      } else {
        // 如果没有子目关联，重置。否则5-1 改 1-1 之后 isReplaceRow 会变为有值，再操作 新增 5-1， isReplaceRow 有值，会执行 zmCalculateVariable
        isReplaceRow.value = null;
      }
    });
  };
  const getAssociatedQuota = apiData => {
    console.log('____________addDeInfo.value?', addDeInfo.value);
    gljdetailApi.getAssociatedQuota(apiData).then(res => {
      console.log('zzzzzzzzzzzzzzz子目关联接口返回了', res);
      if (res.status === 200) {
        handleZmAssociateData(res.result, apiData.deId);
      }
    });
  };
  const handleZmAssociateData = (data, deId) => {
    if (data.zmPointList?.length) {
      data.zmPointList.unshift('显示所有');
    }
    let index = 1;
    // 递归调用为子项添加dispNo
    const assignDispNo = items => {
      items.forEach(item => {
        item.dispNo = index++;
        if (item.children?.length) {
          assignDispNo(item.children);
        }
      });
    };
    if (data.zmDeList?.length) {
      assignDispNo(data.zmDeList);
    }
    data.zmVariableRuleList.forEach((item, index) => {
      item.index = index + 1;
    });
    if (isReplaceRow.value) {
      console.log('_______替换行', isReplaceRow.value);
      const replacedRow = originalTableData.value.find(
        item => item.sequenceNbr === isReplaceRow.value.sequenceNbr,
      );
      if (replacedRow && !data.zmVariableRuleList[0].resultValue) {
        const num = replacedRow.quantity * extractNumber(replacedRow.unit);
        data.zmVariableRuleList[0].resultValue = num;
        isReplaceRow.value = null;
        console.log('带回的值', num);
        zmCalculateVariable(data.zmVariableRuleList, deId);
      }
    }
    Object.assign(zmAssociateData, data);
  };

  const zmCalculateVariable = (zmVariableRuleList, deId) => {
    const params = {
      zmVariableRuleList,
      standardId: deId,
    };
    console.log('重新计算子目规则计算参数', params);
    const apiParams = JSON.parse(JSON.stringify(params));
    gljdetailApi.zmCalculateVariable(apiParams).then(res => {
      console.log('子目规则计算返回______', res);
      if (res.status === 200) {
        emits('updateZmData', res.result);
        handleZmAssociateData(res.result);
      }
    });
  };

  const extractNumber = str => {
    const num = parseFloat(str);
    const result = isNaN(num) ? 1 : num;
    console.log('__单位数字', result);
    return result;
  };

  const updateZmAssociateData = data => {
    handleZmAssociateData(data);
  };

  const updateIsReplaceRow = value => {
    isReplaceRow.value = value;
  };
  return {
    queryBranchDataById,
    getSubItemProjectBtn,
    openLevelCheckList,
    queryFeeFileData,
    editClosedEvent,
    currentChangeEvent,
    updateFbData,
    loading,
    mainMaterialTableData,

    saveContent,
    openEditDialog,
    showModelTitle,

    currentInfo,
    isShowModel,
    editContent,
    editKey,
    infoVisible,
    infoText,
    iconType,
    isSureModal,

    ishasRCJList,
    isClearEdit,
    isSortQdCode,
    deCode,
    rcjVisible,
    deVisible,
    bcDeRow,
    qdVisible,
    DJGCrefreshFeeFile,
    isUpdateFile,
    indexVisible,
    isUpdateQuantities,
    selectUnit,
    showUnitTooltip,
    addCurrentInfo,
    isIndexAddInfo,
    addDataSequenceNbr,
    lockFlag,
    feeFileList,
    tableData,
    originalTableData,
    materialVisible,
    materialType,
    materialRow,
    rowType,
    updateDelTempStatusColl,
    updateCancelDelTempStatusColl,
    batchDeleteFun,
    selectData,
    batchDeleteVisible,
    batchALLDeleteVisible,
    batchDataType,
    handleNoteClick,
    areaStatus,
    areaVisibleType,
    onFocusNode,
    closeAnnotations,
    getAnnotationsRef,
    AnnotationsRefList,
    closeAreaModal,
    priceVisible,
    handleNewTable,
    standardVisible,
    addDeInfo,
    zmAssociateData,
    associateSubQuotasVisible,
    hasAssociatedWithSubItem,
    getAssociatedQuota,
    updateZmAssociateData,
    getGlgSettingData,
    updateIsReplaceRow,
    currentIndex,
    queryRule,
    currentZmDe,
    addZmlistRes,
  };
};
