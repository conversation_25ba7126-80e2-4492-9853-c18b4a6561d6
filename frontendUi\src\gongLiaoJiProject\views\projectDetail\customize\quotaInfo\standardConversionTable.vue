<!--
 * @Descripttion: 标准换算
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-08 10:41:29
-->
<template>
  <div class="standard-conversion-table">
    <!-- props.tableData.length > 0&& -->
    <div
      class="head"
      v-show="
        props.currentInfo?.kind !== '00' &&
        props.currentInfo?.kind !== '01' &&
        props.currentInfo?.kind !== '02' &&
        !props.isSetStandard &&
        ![11].includes(props.currentInfo?.isCostDe)
      "
    >
      <!-- :disabled="!props.gsListVal?.isConversion" -->
      <a-button
        type="text"
        @click="reset"
        :disabled="standardConvertMod == 1 && props.tableData.length == 0"
      >
        <icon-font type="icon-biaodan-charu"></icon-font>清空换算
      </a-button>
      <a-button
        v-if='false'
        @click="move(1)"
        type="text"
        :disabled="
          !props.tableData?.length ||
          !currentInfo ||
          currentInfo?.index === 0 ||
          loading
        "
        ><icon-font type="icon-biaodan-charu"></icon-font>上移</a-button
      >

      <a-button
        v-if='false'
        type="text"
        @click="move(2)"
        :disabled="
          !props.tableData?.length ||
          !currentInfo ||
          currentInfo?.index + 1 === props.tableData.length ||
          loading
        "
        ><icon-font type="icon-biaodan-charu"></icon-font>下移</a-button
      >
      <span class="text">执行规则：</span>
      <vxe-select
        v-model="standardConvertMod"
        transfer
        @change="switchConversionMethods"
      >
        <vxe-option
          v-for="(item, index) in standardConvertList"
          :key="index"
          :value="item.code"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
      <a-checkbox
        style="margin-left: 15px"
        v-model:checked="mainMatConvertMod"
        @change="switchConversionMainMatMod"
        >主材设备受系数调整影响</a-checkbox
      >
    </div>
    <div class="content" :class="autoHeight">
      <vxe-table
        ref="vexTable"
        :class="[
          'standard-conversion-table table-edit-common',
          props.isSetStandard ? 'table-edit-common' : '',
        ]"
        keep-source
        border
        height="auto"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :column-config="{ resizable: true }"
        :scroll-y="{ enabled: false }"
        :span-method="mergeRowMethod"
        :data="props.tableData"
        :row-class-name="rowClassName"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
        }"
        :cell-class-name="selectedClassName"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, tableCellClick, ['selectedRule']);
          }
        "
        @edit-closed="editClosedEvent"
        @current-change="currentChangeEvent"
      >
        <vxe-column :width="columnWidth(60)" columnWidth  title="序号">
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column field="relationGroupName" title="换算名称" align="left" :min-width="columnWidth(120)">
          <template #default="{ row }">
            <span>{{ row.relationGroupName || row.relation }}</span>
            <a-tooltip
              placement="right"
              v-if="row && row.ruleFile && row.ruleFile.fileDetails"
            >
              <template #title>
                <span> {{ row.ruleFile.fileDetails }}</span>
              </template>
              <span class="relation-logo"><question-circle-outlined /></span>
            </a-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="relation" title="" align="left" :min-width="columnWidth(120)">
          <template #default="{ row }">
            <span v-if="row.relationGroupName">{{ row.relation }}</span>
          </template>
        </vxe-column>
        <vxe-column field="defaultValue" title="默认值" :min-width="columnWidth(120)"></vxe-column>
        <vxe-column
          field="selectedRule"
          title="换算处理"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
          :min-width="columnWidth(120)"
        >
          <template #default="{ row }">
            <a-checkbox
              v-if="row.kind === '1'"
              v-model:checked="row.selected"
              v-model:groupName="row.relationGroupName"
              ref="myCheckBox"
              :disabled="row.beGray"
            ></a-checkbox>
            <div v-if="row.kind === '2'">
              <span style="margin-right: 10px; display: inline-block">{{
                  row.currentRcjCode + row.ruleInfo
              }}</span>
              <DownOutlined
                :style="{
                  fontSize: '12px',
                  color: 'rgba(0, 0, 0, 0.25)',
                }"
              />
            </div>
            <div v-if="row.kind === '3'">
              {{ row.selectedRule }}
            </div>
          </template>
          <template #edit="{ row }">
            <a-checkbox
              v-if="row.kind === '1'"
              v-model:checked="row.selected"
              v-model:groupName="row.relationGroupName"
              ref="myCheckBox"
              @change="updeteRule(row)"
              :disabled="row.beGray"
            ></a-checkbox>
            <div v-if="row.kind === '2'">
              <chapter-info
                v-model:filedValue="row.selectedRuleGroup"
                :groupTypeList="groupTypeList"
                :ruleInfo="row.ruleInfo"
                :ruleRcjCode="row.currentRcjCode"
                :currentRcjLibraryCode="row.currentRcjLibraryCode"
                :placement="props.placement || 'top'"
                @selectChange="ruleMixProportion"
                @selectInfo="selectInfo"
                @showChapter="showChapter"
                @cancel="cancel"
                :treeData="treeData"
              ></chapter-info>

              <!-- <vxe-input
                v-model.trim="row.ruleInfo"
                placeholder="请输入"
                @click="showChapter(row)"
                :disabled="row.beGray"
              >
                <template #suffix>
                  <DownOutlined
                    :style="{
                      fontSize: '12px',
                      color: 'rgba(0, 0, 0, 0.25)',
                    }"
                  />
                </template>
              </vxe-input> -->
            </div>
            <vxe-input
              v-if="row.kind === '3' && isEditDe"
              v-model="row.selectedRule"
              type="text"
              placeholder="请输入值"
              @keyup="
                row.selectedRule = (row.selectedRule.match(
                  /\d{0,8}(\.\d{0,10}|100)?/
                ) || [''])[0]
              "
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-table>
      <standard-type-table
        class="rightTable"
        v-if="
          !props.isSetStandard &&
          props.currentInfo.kind !== '05' &&
          props.currentInfo.kind !== '09' &&
          ![11].includes(props.currentInfo?.isCostDe)
        "
        :currentInfo="props.currentInfo"
        @updateData="updateData"
      ></standard-type-table>

      <select-chapter
        v-model:filedValue="selectedRuleGroup"
        :groupTypeList="groupTypeList"
        v-model:visible="chapterStatus"
        @selectChange="ruleMixProportion"
        @selectInfo="selectInfo"
        :treeData="treeData"
        :isDeType="0"
      ></select-chapter>
    </div>
  </div>
</template>

<script setup>
import api from '@gongLiaoJi/api/projectDetail';
import { message } from 'ant-design-vue';
const props = defineProps([
  'tableData',
  'currentInfo',
  'type',
  'isSetStandard',
  'gsListVal',
]);
import { QuestionCircleOutlined, DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import {
  defineAsyncComponent,
  nextTick,
  onActivated,
  onMounted,
  reactive,
  ref,
  toRaw,
  watch,
  computed,
} from 'vue';
import StandardTypeTable from './standardTypeTable.vue';
import infoMode from '@/plugins/infoMode.js';
import ChapterInfo from './chapterInfo.vue';
import deMapFun from '../deMap';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();

const emits = defineEmits([
  'updateData',
  'currentInfo',
  'type',
  'batchConversionRule',
  'gsListVal',
]);
const projectStore = projectDetailStore();
const groupTypeList = ref([]); // 配合比类型下拉框数据
const treeData = ref([]);
const currentInfo = ref(null);
const vexTable = ref();

let selectedRuleGroup = ref(null);
let chapterStatus = ref(false);
let standardConvertMod = ref(2);
let mainMatConvertMod = ref(); // 主材设备是否受系数调整影响
let standardConvertList = reactive([
  {
    code: 1,
    label: '以标准人材机数据执行',
  },
  {
    code: 2,
    label: '以当前数据执行计算',
  },
]);

const autoHeight = computed(() => {
  if( props.currentInfo?.kind !== '00' &&  props.currentInfo?.kind !== '01' && props.currentInfo?.kind !== '02' && !props.isSetStandard &&
      ![11].includes(props.currentInfo?.isCostDe)){
    return 'glj-h40-detailed-area';
  }else{
    return 'glj-h100-detailed-area';
  }
})

const selectChapter = defineAsyncComponent(() =>
  import('@/components/SelectChapter/index.vue')
);

watch(
  () => props.tableData,
  () => {
    if (props.tableData) {
      emits('batchConversionRule', props.tableData);
      standardConvertMod.value = props.gsListVal?.standardConvertMod || 2;
      mainMatConvertMod.value = props.gsListVal?.mainMatConvertMod || false;
      props.tableData.forEach(item => {
        if (item.sequenceNbr === currentInfo.value?.sequenceNbr) {
          currentInfo.value = item;
        }
      });
      if (!currentInfo.value) {
        currentInfo.value = props.tableData[0];
      }
      vexTable.value.setCurrentRow(currentInfo.value);
    }
  }
);

watch(
  () => props.currentInfo,
  (newValue, oldValue) => {
    nextTick(() => {
      standardConvertMod.value = props.gsListVal?.standardConvertMod || 2;
      mainMatConvertMod.value = props.gsListVal?.mainMatConvertMod || false;
      if (!newValue) return;
      if (newValue?.sequenceNbr !== oldValue?.sequenceNbr) {
        currentInfo.value = null;
      }
    });
  }
);

const updeteRule = (row, clpb) => {
  console.log('row', row);
  // 找到这个a-chekbox 兄弟
  if (row.selected) {
    let rowItem = toRaw(row);
    props.tableData.forEach(item => {
      let cLine = toRaw(item);
      if (
        rowItem.relationGroupName &&
        item.selected &&
        rowItem.relationGroupName === cLine.relationGroupName &&
        rowItem.sequenceNbr !== cLine.sequenceNbr
      ) {
        item.selected = false;
        updeteRule(item, clpb);
      }
    });
  }
  if (clpb) {
    row.clpb = clpb;
  }
  row.nowChange = true;
  row.selected = row.selected ? 1 : 0;
  if (row.kind === '3') {
    const group = getList(props.tableData);
    group.forEach(item => {
      if (item[0].kind === '3' && item[0].type === 'e2') {
        let sum = 0;
        let updateIndex = item.findIndex(
          child => child.sequenceNbr === row.sequenceNbr
        );
        console.log('updateIndex', updateIndex);
        item.forEach((child, index) => {
          if (updateIndex !== item.length - 1) {
            if (index !== item.length - 1) {
              if (sum < 100) {
                sum += Number(child.selectedRule);
                if (sum > 100) {
                  sum = sum - item[index].selectedRule;
                  item[index].selectedRule = 0;
                  item[index].nowChange = true;
                }
              } else {
                child.selectedRule = 0;
                child.nowChange = true;
              }
            }
          } else if (updateIndex === item.length - 1) {
            if (index !== 0) {
              if (sum < 100) {
                sum += Number(child.selectedRule);
                if (index === updateIndex && sum > 100) {
                  sum = sum - item[index - 1].selectedRule;
                  item[index - 1].selectedRule = 0;
                  item[index - 1].nowChange = true;
                }
              } else {
                child.selectedRule = 0;
                child.nowChange = true;
              }
            }
          }
        });
        console.log('sum', sum);
        if (updateIndex !== item.length - 1) {
          item[item.length - 1].selectedRule = Number(100 - sum).toFixed(2);
          item[item.length - 1].nowChange = true;
        } else if (updateIndex === item.length - 1) {
          item[0].selectedRule = Number(100 - sum).toFixed(2);
          item[0].nowChange = true;
        }
      }

      item.forEach(i => {
        if (i.kind === '3' && i.relationGroupCnt >= 2 && i.sequenceNbr !== row.sequenceNbr) {
          i.selectedRule = i.defaultValue;
        }
      });
    });
  }
  if (props.isSetStandard) {
    emits('batchConversionRule', props.tableData);
    return;
  }
  // 接口调用
  console.log('row', row.selected);
  batchOperationalConversionRule();
};
const batchOperationalConversionRule = () => {
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    rules: JSON.parse(JSON.stringify(props.tableData)),
  };
  console.log('标准换算勾选参数', apiData);
  api
    .batchOperationalConversionRule(JSON.parse(JSON.stringify(apiData)))
    .then(res => {
      console.log('标准换算勾选数据', res);
      if (res.status === 200 && res.result) {
        message.success('修改成功');
        emits('updateData', 1);
      }
    });
};
const getList = list => {
  const map = new Map();
  list.forEach((item, index, arr) => {
    if (!map.has(item.relationGroupName)) {
      map.set(
        item.relationGroupName,
        arr.filter(a => a.relationGroupName === item.relationGroupName)
      );
    }
  });
  return Array.from(map).map(item => [...item[1]]);
};

// 清空换算信息
const reset = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    fbFxDeId: props.currentInfo.sequenceNbr,
    standardDeId: props.currentInfo.standardId,
  };
  api.reset(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('换算已清空');
      emits('updateData', 1);
    }
  });
};

const updateData = () => {
  emits('updateData', 1);
};

// const splitComparison=(str,num)=> {
//   // 定义一个正则表达式，匹配比较操作符和随后的数字
//   // 这个正则可能需要根据具体的需求进行调整，比如处理浮点数、负数等
//   const regex = /([≥>])(\d+)/;
//   // 执行匹配
//   const matchVal = str.match(regex);
//   // 如果匹配成功，返回操作符和值（如果操作符不存在，则为undefined）
//   if (matchVal) {
//     let operator=matchVal[1] // 操作符
//     let value=parseInt(matchVal[2], 10)// 转换为整数
//     switch (operator) {
//       case '>':
//         return num > value;
//       case '≥':
//         return num >= value;
//     }
//   }
// }
const splitComparison = (str, num) => {
  // 定义一个正则表达式，匹配比较操作符和随后的数字
  // 这个正则可能需要根据具体的需求进行调整，比如处理浮点数、负数等
  const regex = /([≥>])(\d+)/;
  // 执行匹配
  const matchVal = (str || '').match(regex);
  // 如果匹配成功，返回操作符和值（如果操作符不存在，则为undefined）
  if (matchVal) {
    let operator = matchVal[1]; // 操作符
    let value = parseInt(matchVal[2], 10); // 转换为整数
    switch (operator) {
      case '>':
        return num > value;
      case '≥':
        return num >= value;
    }
  } else {
    return true;
  }
};

// 单元格退出编辑事件
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  if (['selectedRule'].includes(field)) {
    let ruleRange = splitComparison(row.ruleRange, +row[field]);
    if (row.kind === '3') {
      if (ruleRange) {
        row[field] = +row[field];
      } else {
        message.warning('输入值应' + row.ruleRange);
        $table.revertData(row, field);
      }
    }
  }
  if ($table.isUpdateByRow(row, field)) {
    if (row.kind !== '3') {
      return;
    }
    console.log('🚀 ~ editClosedEvent ~ row:', row);
    ruleInfo(row);
  }
};
onMounted(() => {
  nextTick(() => {
    let document1 = document.querySelector(
      '.standard-conversion-table .vxe-header--row'
    );
    document1.children[1].setAttribute('colspan', '2');
    standardConvertMod.value = props.gsListVal?.standardConvertMod || 2;
    mainMatConvertMod.value = props.gsListVal?.mainMatConvertMod || false;
    groupTypeSelect();
  });
});

// 通用行合并函数（将相同多列数据合并为一行）
const mergeRowMethod = ({
  row,
  _rowIndex,
  column,
  visibleData,
  _columnIndex,
}) => {
  const fields = ['relationGroupName'];
  const cellValue = row[column.field];
  if (cellValue && fields.includes(column.field)) {
    const prevRow = visibleData[_rowIndex - 1];
    let nextRow = visibleData[_rowIndex + 1];

    if (prevRow && prevRow[column.field] === cellValue) {
      return { rowspan: 0, colspan: 0 };
    } else {
      let countRowspan = 1;
      while (nextRow && nextRow[column.field] === cellValue) {
        nextRow = visibleData[++countRowspan + _rowIndex];
      }
      if (countRowspan > 1) {
        return { rowspan: countRowspan, colspan: 1 };
      }
    }
  } else if (!row.relationGroupName && _columnIndex === 1) {
    return { rowspan: 1, colspan: 2 };
  } else if (!row.relationGroupName && _columnIndex === 2) {
    return { rowspan: 0, colspan: 0 };
  }
};

// 章节点击展示弹窗
const showChapter = row => {
  console.log('🚀 ~ showChapter ~ row:', row);
  selectedRuleGroup.value = currentInfo.value.selectedRuleGroup;
  ruleMixProportion(currentInfo.value.selectedRuleGroup);
};

// 配比类型下拉框
const groupTypeSelect = () => {
  const postData = {
    libraryCode: props.currentInfo?.libraryCode,
  };
  console.log('配比类型下拉框', postData);
  api.groupTypeSelect(postData).then(res => {
    console.log('groupTypeSelect', res);
    if (res.status === 200 && res.result) {
      groupTypeList.value = res.result;
      groupTypeList.value = groupTypeList.value.map(item => {
        return {
          libraryName: item.groupName,
          libraryCode: item.groupName,
        };
      });
    }
  });
};

const ruleMixProportion = value => {
  let apiData = {
    topGroupType: currentInfo.value.topGroupType,
    groupName: value,
    libraryCode: props.currentInfo?.libraryCode,
  };
  console.log('🚀 ~ ruleMixProportion ~ apiData:', apiData);
  api.ruleMixProportion(apiData).then(res => {
    console.log('222222222222', res);
    if (res.status === 200 && res.result) {
      let title = Object.keys(res.result)[0];
      let obj = {
        details: value,
        detailsCode: 1,
        childrenList: res.result,
      };
      treeData.value = [obj];
      console.log('treeData', treeData.value);
    }
  });
};

const cancel = () => {
  vexTable.value.clearEdit();
};

const selectInfo = item => {
  // currentInfo.value.selected = true;
  // currentInfo.value.ruleInfo = item.details;

  // updeteRule(currentInfo.value, item);

  currentInfo.value.selected = true;
  if (item.specification && item.details.indexOf(item.specification) !== -1) {
    currentInfo.value.ruleInfo = item.details;
  } else {
    currentInfo.value.ruleInfo =
      item.details + ' ' + (item.specification || '');
  }
  console.log(currentInfo.value.ruleInfo);
  updeteRule(currentInfo.value, item);
};

// 设置行样式
const rowClassName = ({ row }) => {
  if (row.beGray) {
    return 'row-disabled';
  }
  return null;
};

const ruleInfo = row => {
  if (!Number(row.defaultValueMax) && Number(row.defaultValueMin)) {
    if (Number(row.selectedRule) < Number(row.defaultValueMin)) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '请输入的值大于' + Number(row.defaultValueMin),
        confirm: () => {
          infoMode.hide();
          let $table = vexTable.value;
          $table.revertData(row);
        },
      });
      return;
    }
  } else if (Number(row.defaultValueMax) && !Number(row.defaultValueMin)) {
    if (Number(row.selectedRule) > Number(row.defaultValueMax)) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '请输入的值小于' + Number(row.defaultValueMax),
        confirm: () => {
          infoMode.hide();
          let $table = vexTable.value;
          $table.revertData(row);
        },
      });
      return;
    }
  } else if (!!Number(row.defaultValueMax) && !!Number(row.defaultValueMin)) {
    if (
      Number(row.selectedRule) < Number(row.defaultValueMin) ||
      Number(row.selectedRule) > Number(row.defaultValueMax)
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText:
          '请输入的值在' +
          Number(row.defaultValueMin) +
          '到' +
          Number(row.defaultValueMax) +
          '之间',
        confirm: () => {
          infoMode.hide();
          let $table = vexTable.value;
          $table.revertData(row);
        },
      });
      return;
    }
  }
  updeteRule(row);
};
const move = val => {
  let index = currentInfo.value.index;
  let list = props.tableData;
  if (val === 1) {
    if (currentInfo.value.rowSpan === 1) {
      if (list[index - 1].rowSpan === 1) {
        const temp = props.tableData[index];
        list[index] = list[index - 1];
        list[index - 1] = temp;
      } else {
        let startIndex = list.findIndex(
          x => x.relationGroupName === list[index - 1].relationGroupName
        );
        list.splice(index, 1);
        list.splice(startIndex, 0, currentInfo.value);
      }
    } else {
      let groupStartIndex = list.findIndex(
        x => x.relationGroupName === currentInfo.value.relationGroupName
      );
      if (list[groupStartIndex - 1].rowSpan === 1) {
        let temp = list[groupStartIndex - 1];
        list.splice(groupStartIndex - 1, 1);
        let addIndex = list.findLastIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex + 1, 0, temp);
      } else {
        let beforeList = list.filter(
          x =>
            x.relationGroupName === list[groupStartIndex - 1].relationGroupName
        );
        list = removeElements(list, beforeList);
        let addIndex = list.findLastIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex + 1, 0, ...beforeList);
      }
    }
  } else {
    if (currentInfo.value.rowSpan === 1) {
      if (list[index + 1].rowSpan === 1) {
        const temp = props.tableData[index];
        list[index] = list[index + 1];
        list[index + 1] = temp;
      } else {
        let startIndex = list.findLastIndex(
          x => x.relationGroupName === list[index + 1].relationGroupName
        );
        list.splice(index, 1);
        list.splice(startIndex, 0, currentInfo.value);
      }
    } else {
      let groupEndIndex = list.findLastIndex(
        x => x.relationGroupName === currentInfo.value.relationGroupName
      );
      let startIndex = list.findLastIndex(
        x => x.relationGroupName === list[groupEndIndex + 1].relationGroupName
      );
      console.log('groupEndIndex', groupEndIndex, startIndex);
      if (list[groupEndIndex + 1].rowSpan === 1) {
        let temp = list[groupEndIndex + 1];
        list.splice(groupEndIndex + 1, 1);
        let addIndex = list.findIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex, 0, temp);
      } else {
        let afterList = list.filter(
          x => x.relationGroupName === list[groupEndIndex + 1].relationGroupName
        );
        console.log('afterList', afterList);
        list = removeElements(list, afterList);
        let addIndex = list.findIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex, 0, ...afterList);
      }
    }
  }
  list.forEach((item, index) => {
    item.index = index;
  });
  console.log('移动数据', props.tableData);
  batchOperationalConversionRule();
};
const switchConversionMethods = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    standardConvertMod: standardConvertMod.value,
  };
  console.log('apiDAta', apiData);
  api.switchConversionMod(apiData).then(res => {
    if (res.status === 200 && res.result) {
      emits('updateData', 1);
    }
  });
};

const switchConversionMainMatMod = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    mainMatConvertMod: mainMatConvertMod.value,
  };
  console.log('换算ApiData', apiData);
  api.switchConversionMainMatMod(apiData).then(res => {
    if (res.status === 200 && res.result) {
      emits('updateData', 1);
    }
  });
};
const tableCellClick = ({ row }) => {
  if (props.currentInfo?.tempDeleteFlag) return false;
  return true;
};
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};

// 可以编辑的定额吓得人材机普通专业的装饰超高、垂直运输定额下人材机可自由编辑
const isEditDe = computed(() => {
  return deMapFun.isEditRcj(props.currentInfo);
});
</script>

<style lang="scss" scoped>
.standard-conversion-table {
  width: 100%;
  height: 100%;
  .content {
    // display: block;
    display: flex;
    justify-content: space-between;
    width: 100%;
    //height: calc(100% - 40px);
    :deep(.vxe-table) {
      min-width: 65%;
      //height: calc(100% - 40px);
      .vxe-table--render-wrapper {
        height: 100%;
        .vxe-table--main-wrapper {
          height: 100%;
          display: flex;
          flex-direction: column;
          .vxe-table--body-wrapper {
            flex: 1;
            height: auto !important;
            min-height: auto !important;
          }
        }
      }
    }
    .table-edit-common {
      min-width: 65%;
      height: 100%;
    }
    .rightTable {
      width: 33%;
      height: 100%;
    }
  }
}
.relation-logo {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  right: 10px;
}
::v-deep(.standard-conversion-table .vxe-header--row) {
  th:nth-of-type(3) {
    display: none;
  }
}
::v-deep(.vxe-table .row-disabled) {
  background: #ececec;
}
</style>
