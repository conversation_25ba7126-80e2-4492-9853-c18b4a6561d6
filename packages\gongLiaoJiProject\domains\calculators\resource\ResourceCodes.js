const rcjBaseFn = {
  "quantity": () => {
    return {
      "type": "DE",
      "column": "quantity"}
  },
  "baseJournalPrice": () => {
    return {
      "type": "item",
      "column":"baseJournalPrice"
    };
  },
  "baseJournalTaxPrice": () => {
    return {
      "type": "item",
      "column":"baseJournalTaxPrice"
    };
  },
  "marketPrice": () => {
    return {
      "type": "item",
      "column":"marketPrice"
    };
  },
  "marketTaxPrice": () => {
    return {
      "type": "item",
      "column":"marketTaxPrice"
    };
  },
  "resQty": () => {
    return {
      "type": "item",
      "column": "resQty"
      //   (item)=>{ //如果是配比 则为resqty , RCJ的话则是qts
      //      return item.resQty;
      // }
    };
  },
  "totalNumber": () => {
    return {
      "type": "item",
      "column":"totalNumber"
    };
  },
  "price": () => {
    return {
      "type": "item",
      "column":"price"
    };
  },
  "transferFactor": () => {
    return {
      "type": "item",
      "column":(item)=>{
        let res = item.transferFactor;
        if(res == undefined || res == ''){
          res = 1;
        }
        return res;
      }
    };
  }

}


//父级规则
const RJCRules = {
  "marketPrice":{
    "name":"人材机市场价",
    "mathFormula":"marketPrice",//定额工程量 = 定额工程量
  },
  "marketTaxPrice":{
    "name":"人材机含税市场价",
    "mathFormula":"marketPrice",//定额工程量 = 定额工程量
  },
  "baseJournalPrice":{
    "name":"基期价",
    "mathFormula":"baseJournalPrice",
  },
  "baseJournalTaxPrice":{
    "name":"含税基期价",
    "mathFormula":"baseJournalTaxPrice",
  },
  "resQty":{
    "name":"人材机消耗量",
    "mathFormula":"resQty",//定额工程量 = 定额工程量
  },
  "total":{
    "name":"合价",
    "mathFormula":"totalNumber*marketPrice",//合价（市场价合价）= 合计数量 * 市场价
  },
  "totalTax":{
    "name":"合价",
    "mathFormula":"totalNumber*marketTaxPrice",//合价（市场价合价）= 合计数量 * 市场价
  },
  "baseJournalTotal":{
    "name":"合价",
    "mathFormula":"totalNumber*baseJournalPrice",//合价（市场价合价）= 合计数量 * 市场价
  },
  "baseJournalTotalTax":{
    "name":"合价",
    "mathFormula":"totalNumber*baseJournalTaxPrice",//合价（市场价合价）= 合计数量 * 市场价
  },
  "totalNumber":{
    "name":"合计数量",
    "mathFormula":"quantity*resQty",//合计数量 = 定额工程量*消耗量*计算基数
  },
  "quantity":{
    "name":"市场价",
    "mathFormula":"quantity",//父级市场价 Σ子级（市场价*消耗量）
  },
  "price":{
    "name":"定额价",
    "mathFormula":"price",//定额价 = 定额价
  },
  "transferFactor":{
    "name":"三材系数",
    "mathFormula":"transferFactor",//三材系数
  },
  "scCount":{
    "name":"三材量",
    "mathFormula":"scCount",//=合计数量*三材系数
  },
}


//子级规则
const PBRules = {
  ...RJCRules,

  "totalNumber":{
    "name":"合计数量",
    "mathFormula":"quantity*resQty",//子级合计数量 = 父级合计数量*子级对应消耗量
  },
  "price":{
    "name":"单价",
    "mathFormula":"dePrice*resQty",//父级定额价 Σ子级（定额价*消耗量）
  },
  "baseJournalPrice":{
    "name":"基期价",
    "mathFormula":"baseJournalPrice",//父级基期价 Σ子级（市场价*消耗量）
  },
  "marketPrice":{
    "name":"市场价",
    "mathFormula":"marketPrice",//父级市场价 Σ子级（市场价*消耗量）
  },
  "baseJournalTaxPrice":{
    "name":"含税基期价",
    "mathFormula":"baseJournalTaxPrice",//父级含税基期价 Σ子级（市场价*消耗量）
  },
  "marketTaxPrice":{
    "name":"含税市场价",
    "mathFormula":"marketTaxPrice",//父级市场价 Σ子级（市场价*消耗量）
  },
  "quantity":{
    "name":"市场价",
    "mathFormula":"quantity",//父级市场价 Σ子级（市场价*消耗量）
  },
  "resQty":{
    "name":"人材机消耗量",
    "mathFormula":"resQty",//定额工程量 = 定额工程量
  },
  "transferFactor":{
    "name":"三材系数",
    "mathFormula":"transferFactor",//三材系数
  },
  "scCount":{
    "name":"三材量",
    "mathFormula":"scCount",//=合计数量*三材系数
  },

}



module.exports = {RJCRules,PBRules,rcjBaseFn}
