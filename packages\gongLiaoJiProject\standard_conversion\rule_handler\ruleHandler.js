const RCJPriceMathHandler = require("../math_item_handler/RCJPriceMathHandler");
const Hxxx$yyy$VMathHandler = require("../math_item_handler/Hxxx$yyy$VMathHandler");
const Hxxx$yyyMathHandler = require("../math_item_handler/Hxxx$yyyMathHandler");
const H$xxx$VMathHandler = require("../math_item_handler/H$xxx$VMathHandler");
const XXX$VMathHandler = require("../math_item_handler/XXX$VMathHandler");
const A$$VMathHandler = require("../math_item_handler/A$$VMathHandler");
const {ConversionInfoUtil} = require("../util/ConversionInfoUtil");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const ProjectDomain = require("../../domains/ProjectDomain");
const WildcardMap = require("../../core/container/WildcardMap");
const {BaseDe2022} = require("../../models/BaseDe2022");
const DeCommonConstants = require("../../constants/DeCommonConstants");
const CommonConstants = require("../../constants/CommonConstants");
const { NumberUtil } = require("../../../../common/NumberUtil");

class RuleHandler {
    constructor(strategyCtx, rule) {
        this.ctx = strategyCtx;
        this.app = this.ctx.app;
        this.rule = rule;
        this.HSCL_TEXT = "换算材料：";
        this.deRcjs = this.getDeRcjs();
        this.notStandardActiveRcjCodes = [];
        this.tcRCJResQty = {
            R: 0,
            C: 0,
            J: 0
        }
        this.jd = this.ctx.jd
    }

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare(){
        this.effectDeRCJ = await this.initEffectRCJ();
        this.rule.mathHandlers = this.analysisRule();
    }

    async initEffectRCJ(){
        //return this.ctx.deInitialRcjs;
         let deRcjs = this.getDeRcjs();
         return deRcjs;
         // return FindRcjHandler.sortRcj(deRcjs)
    }

    /**
     * 逐条执行换算规则
     */
    async execute(){
        await this.prepare();
        for(let handler of this.rule.mathHandlers){
            await handler.execute();
        }

        await this.after();
    }

    async after(){
        await this._dealTcRcj();
        this.rule.conversionString = this._dealNotStandardRcjRelation(this.rule.conversionString);
        this.rule.conversionExplain = this._dealNotStandardRcjRelation(this.rule.conversionExplain);
        //this.addConversionInfo();
        this.addDeUpdateInfo();
        delete this.rule.mathHandlers;
    }

    /**
     * 调差人材机处理
     */
    async _dealTcRcj(){
        let baseDe = this.ctx.deLine;


        this.tcRCJResQty.R = NumberUtil.numberScale(this.tcRCJResQty.R, this.jd.resQty);
        this.tcRCJResQty.C = NumberUtil.numberScale(this.tcRCJResQty.C, this.jd.resQty);
        this.tcRCJResQty.J = NumberUtil.numberScale(this.tcRCJResQty.J, this.jd.resQty);

        this.tcRCJResQty.R = this._tzRCJUpdateResQty(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ, this.tcRCJResQty.R);
        this.tcRCJResQty.C = this._tzRCJUpdateResQty(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ, this.tcRCJResQty.C);
        this.tcRCJResQty.J = this._tzRCJUpdateResQty(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ, this.tcRCJResQty.J);

        if(this.tcRCJResQty.R == 0 && this.tcRCJResQty.C == 0 && this.tcRCJResQty.J == 0){
            return ;
        }
        baseDe.resourceTZ = CommonConstants.COMMON_YES;//调整了人材机，用于单价调整合并人材机
        //
        baseDe.compensation = {
            rgfCompensation: this.tcRCJResQty.R,
            clfCompensation: this.tcRCJResQty.C,
            jxfCompensation: this.tcRCJResQty.J,
        };

        let {
            differenceResourceR,
            differenceResourceC,
            differenceResourceJ
        } = await this.ctx.deOrCsxmDomain.createDeTcResourceForConversion(this.ctx.constructId, this.ctx.unitId,baseDe,this.tcRCJResQty.R,this.tcRCJResQty.C,this.tcRCJResQty.J)

        delete baseDe.compensation;

        if(ObjectUtil.isNotEmpty(differenceResourceR)){
            differenceResourceR.price = 1;
            this.ctx.dealLockNumberOneRcj(differenceResourceR);
        }

        if(ObjectUtil.isNotEmpty(differenceResourceC)){
            differenceResourceC.price = 1;
            this.ctx.dealLockNumberOneRcj(differenceResourceC);
        }

        if(ObjectUtil.isNotEmpty(differenceResourceJ)){
            differenceResourceJ.price = 1;
            this.ctx.dealLockNumberOneRcj(differenceResourceJ);
        }
    }

    _tzRCJUpdateResQty(tzRcjCode, addResQty){
        let addResQtyTmp = addResQty;
        if(addResQtyTmp != 0){
            let tzRcj = this.deRcjs.find((rcj) => rcj.materialCode === tzRcjCode);
            if(ObjectUtil.isNotEmpty(tzRcj)){
                if(tzRcj.isNumLock){
                    tzRcj.resQtyConversionLock = tzRcj.resQtyConversionLock || 0;
                    tzRcj.resQtyConversionLock += addResQtyTmp;
                }else{
                    this.ctx.conversionService.addTempRemoveRCJResQty(tzRcj, addResQtyTmp)
                }

                addResQtyTmp = 0;
            }
        }
        return addResQtyTmp;
    }


    addConversionInfo() {
        let conversionInfoItem = this.initConversionInfo(this.ctx.deUpDateObj.conversionInfo.length);
        this.dealConversionInfo(conversionInfoItem)
        this.ctx.deUpDateObj.conversionInfo.push(conversionInfoItem);
    }

    addDeUpdateInfo(){
        let rule = this.rule;

        let {redStr, blackStr} = this.deCodeUpdateInfo();
        redStr = this._dealNotStandardRcjRelation(redStr);


        let nameSuffix = this.deNameUpdateInfo(rule);
        nameSuffix = this._dealNotStandardRcjRelation(nameSuffix);

        if(ObjectUtil.isNotEmpty(redStr)){
            this.ctx.deUpDateObj.redArray.push(redStr);
        }

        if(ObjectUtil.isNotEmpty(blackStr)){
            this.ctx.deUpDateObj.blackArray.push(blackStr);
        }

        this.ctx.deUpDateObj.nameSuffixArray.push(nameSuffix);

        this.deTypeUpdateInfo();
    }

    _dealNotStandardRcjRelation(src){
        if(ObjectUtil.isEmpty(src) || ObjectUtil.isEmpty(this.notStandardActiveRcjCodes)){
            return src;
        }

        let result = src;

        for(let codeKV of this.notStandardActiveRcjCodes){
            let regex = new RegExp(`\\b${codeKV[0]}\\b`, "g");
            let regexH = new RegExp(`\\bH${codeKV[0]}\\b`, "g");
            result = result.replace(regex, codeKV[1]).replace(regexH, `H${codeKV[1]}`)
        }

        return result;
    }

    deCodeUpdateInfo() {
        throw new Error("dealConversionInfo 由子类实现");
    }

    initConversionInfo(sortNo = null){
        return ConversionInfoUtil.initConversionInfo(
            this.rule,
            this.rule.isUniteRule ? ConversionInfoUtil.UNITE_CONVERSION_SOURCE : ConversionInfoUtil.STARDARD_CONVERSION_SOURCE,
            this.rule.math,
            sortNo);
        // return {
        //     ruleId: this.rule.sequenceNbr,
        //     source: this.rule.isUniteRule ? "统一换算" : "标准换算",
        //     conversionExplain: null,
        //     conversionString: this.rule.math,
        //     sortNo
        // };
    }


    dealConversionInfo(conversionInfoItem) {
        throw new Error("dealConversionInfo 由子类实现");
    }


    deNameUpdateInfo(rule){
         throw new Error("deNameUpdateInfo 由子类实现");
    }

    deTypeUpdateInfo(rule){
        if(!this.ctx.deUpDateObj.deTypes.includes("换")){
            this.ctx.deUpDateObj.deTypes.push("换")
        }
    }

    formatConversionExplain(explain){
        const typeMaps = new Map([
            ["R", "人工"],
            ["J", "机械"],
            ["C", "材料"],
            ["", "单价"]
        ]);

        let tmpExplain = explain.trim();

        let firstCharacter = tmpExplain.charAt(0);
        if("RCJ".includes(firstCharacter)){
            return typeMaps.get(firstCharacter) + tmpExplain.substring(1);
        }

        if("+-*/".includes(firstCharacter)){
            return typeMaps.get("") + tmpExplain;
        }
    }

    getDeRcjs(){
        return ProjectDomain.getDomain(this.ctx.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.ctx.unitId, this.ctx.de.sequenceNbr) + WildcardMap.WILDCARD);

        // return this.ctx.unitProject.constructProjectRcjs.filter((v) => v.deId == this.ctx.de.sequenceNbr);
    }

    /**
     * 解析规则：规则分类、输入/选择值、公式
     */
    analysisRule(){
        let rule = this.rule;

        if(ObjectUtil.isEmpty(rule.math)){
            return [];
        }

        let standardMath = this.formulaStandardizationConversion(rule.math);
        // let mathSubs = standardMath.split(",").map((item) => item.trim());
        let mathSubs = this.splitWithSpecial(standardMath).map((item) => item.trim());

        let mathHandlers = [];
        /**
         * 单条规则处理math，包含以下情况（运算符以*为例子）：
         *    1. *n 整体乘
         *    2. R/C/J*n 对应R/C/J 乘
         *    3. HXXXX XXXX *n 对应材料编码 乘
         *    4. HXXXX XXXX n 对应材料编码 重置为 n
         *    5. HXXXX YYYY 对应材料编码 替换为 YYYY 消耗量不变
         *    6. H XXXX n 新增材料，其消耗量为n
         *    7. XXXX *n  材料消耗量 乘
         *    8. 新增定额
         *    9. 新关联定额人材机
         */
        for(let oneMath of mathSubs){
            if(/^\$\{[\s\S]*\}\$$/.test(oneMath)){
                mathHandlers.push(new A$$VMathHandler(this, oneMath));
            }else if (/^H\S{5,}\s+\S{5,}\s+\S+$/.test(oneMath)) {
                mathHandlers.push(new Hxxx$yyy$VMathHandler(this, oneMath));
            }else if(/^H\S{5,}\s+\S{5,}$/.test(oneMath)){
                mathHandlers.push(new Hxxx$yyyMathHandler(this, oneMath));
            }else if(/^H\s+\S{5,}\s+\S+$/.test(oneMath)){
                mathHandlers.push(new H$xxx$VMathHandler(this, oneMath));
            }else if(/^\S{5,}\s+\S+$/.test(oneMath)){
                mathHandlers.push(new XXX$VMathHandler(this, oneMath));
            }else {
                mathHandlers.push(new RCJPriceMathHandler(this, oneMath));
            }
        }

        // for(let oneMath of mathSubs){
        //     if (/^H[A-Z0-9#_-]{5,}\s+[A-Z0-9#_-]{5,}\s+\S+$/.test(oneMath)) {
        //         mathHandlers.push(new Hxxx$yyy$VMathHandler(this, oneMath));
        //     }else if(/^H[A-Z0-9#_-]{5,}\s+[A-Z0-9#_-]{5,}$/.test(oneMath)){
        //         mathHandlers.push(new Hxxx$yyyMathHandler(this, oneMath));
        //     }else if(/^H\s+[A-Z0-9#_-]{5,}\s+\S+$/.test(oneMath)){
        //         mathHandlers.push(new H$xxx$VMathHandler(this, oneMath));
        //     }else if(/^[A-Z0-9#_-]{5,}\s+\S+$/.test(oneMath)){
        //         mathHandlers.push(new XXX$VMathHandler(this, oneMath));
        //     }else {
        //         mathHandlers.push(new RCJPriceMathHandler(this, oneMath));
        //     }
        // }

        return  mathHandlers;
    }

    async getRelationDe(libraryCode, relationDeId){
        return await this.app.gljAppDataSource
            .getRepository(
                BaseDe2022
            )
            .findOneBy({
                sequenceNbr: relationDeId
            });
    }


    /**
     * 公式标准化转换
     * @param math
     * @return {*}
     * @private
     */
    formulaStandardizationConversion(math){
        let tmpMath = "";
        tmpMath = math.replace(/，/g, ",").trim();
        // 正则表达式匹配 n= 后面的内容
        const nMatch = tmpMath.match(/n=(.*?)($|,)/);
        // 如果找到了匹配的内容
        if (nMatch && nMatch[1]) {
            // 用 n= 后面的内容替换公式中的 n
            const replacement = nMatch[1];
            tmpMath = tmpMath
                .replace(`n=${nMatch[1]}`, "")
                .replace("n", replacement);
        }
        if (tmpMath.endsWith(",")) {
            tmpMath = tmpMath.slice(0, -1);
        }
        return tmpMath;
    }

    splitWithSpecial(str) {
        let result = [];
        let idx = 0;
        while (idx < str.length) {
            const found = this.findFirstSpecial(str, idx);
            if (!found) {
                // 没有特殊结构，剩下的用 , 分割并过滤空字符串
                result.push(...str.slice(idx).split(',').map(s => s.trim()).filter(s => s));
                break;
            }
            // 处理特殊结构前的普通内容
            if (found.minIdx > idx) {
                result.push(...str.slice(idx, found.minIdx).split(',').map(s => s.trim()).filter(s => s));
            }
            // 处理特殊结构本身
            result.push(str.slice(found.minIdx, found.endIdx));
            idx = found.endIdx;
        }
        return result;
    }

    findFirstSpecial(str, startIdx) {
        let minIdx = -1, typeIdx = -1, endIdx = -1;
        for (let i = 0; i < this.specialTypes.length; i++) {
            const { start, end } = this.specialTypes[i];
            const sIdx = str.indexOf(start, startIdx);
            if (sIdx !== -1) {
                const eIdx = str.indexOf(end, sIdx + start.length);
                if (eIdx !== -1) {
                    if (minIdx === -1 || sIdx < minIdx) {
                        minIdx = sIdx;
                        typeIdx = i;
                        endIdx = eIdx + end.length;
                    }
                }
            }
        }
        return minIdx === -1 ? null : { typeIdx, minIdx, endIdx };
    }

    // 定义所有特殊结构的起止标记
    specialTypes = [
        { start: '${', end: '}$' },
        // { start: '#', end: '#' },
        // { start: '@@', end: '@@' }
    ];

}

module.exports = RuleHandler;
