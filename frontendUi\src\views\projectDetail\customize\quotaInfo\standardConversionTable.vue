<!--
 * @Descripttion: 标准换算
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-04 14:20:13
-->
<template>
  <div class="standard-conversion-table">
    <div
      class="head"
      v-show="
        props.currentInfo?.kind !== '0' &&
        props.currentInfo?.kind !== '01' &&
        props.currentInfo?.kind !== '02' &&
        props.currentInfo?.kind !== '03' &&
        !props.isSetStandard &&
        !props.currentInfo?.tempDeleteFlag
      "
    >
      <a-button
        type="text"
        @click="reset"
      ><icon-font type="icon-biaodan-charu"></icon-font>清空换算</a-button>
      <a-button
        @click="move(1)"
        type="text"
        :disabled="!currentInfo || currentInfo?.index === 0 || loading"
      ><icon-font type="icon-biaodan-charu"></icon-font>上移</a-button>

      <a-button
        type="text"
        @click="move(2)"
        :disabled="
          !currentInfo ||
          currentInfo?.index + 1 === props.tableData.length ||
          loading
        "
      ><icon-font type="icon-biaodan-charu"></icon-font>下移</a-button>
      <span class="text">执行规则：</span>
      <vxe-select
        v-model="standardConvertMod"
        transfer
        @change="switchConversionMethods"
      >
        <vxe-option
          v-for="(item, index) in standardConvertList"
          :key="index"
          :value="item.code"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
      <a-checkbox
        v-model:checked="mainMatConvertMod"
        @change="switchConversionMainMatMod"
      >主材设备受系数调整影响</a-checkbox>
    </div>
    <div :class="['content', !props.isSetStandard ? 'content-detail' : '']">
      <vxe-table
        ref="vexTable"
        :class="[
          'standard-conversion-table table-edit-common',
          props.isSetStandard ? 'table-edit-common' : '',
        ]"
        keep-source
        border
        height="auto"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :column-config="{ resizable: true }"
        :scroll-y="{ enabled: false }"
        :span-method="mergeRowMethod"
        :data="props.tableData"
        :row-class-name="rowClassName"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
        }"
        :cell-class-name="selectedClassName"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, tableCellClick, ['selectedRule']);
          }
        "
        @edit-closed="editClosedEvent"
        @current-change="currentChangeEvent"
      >
        <vxe-column
          width="60"
          title="序号"
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          field="relationGroupName"
          title="换算名称"
          align="left"
        >
          <template #default="{ row }">
            <span>{{ row.relationGroupName || row.relation }}</span>
            <a-tooltip
              placement="right"
              v-if="row && row.ruleFile && row.ruleFile.fileDetails"
            >
              <template #title>
                <span> {{ row.ruleFile.fileDetails }}</span>
              </template>
              <span class="relation-logo"><question-circle-outlined /></span>
            </a-tooltip>
          </template>
        </vxe-column>
        <vxe-column
          field="relation"
          title=""
          align="left"
        >
          <template #default="{ row }">
            <span v-if="row.relationGroupName">{{ row.relation }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="defaultValue"
          title="默认值"
        ></vxe-column>
        <vxe-column
          field="selectedRule"
          title="换算处理"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <a-checkbox
              v-if="row.kind === '1'"
              v-model:checked="row.selected"
              v-model:groupName="row.relationGroupName"
              ref="myCheckBox"
              :disabled="row.beGray || props.currentInfo?.tempDeleteFlag"
            ></a-checkbox>
            <div v-if="row.kind === '2'">
              <span style="margin-right: 10px; display: inline-block">{{
                row.ruleInfo
              }}</span>
              <DownOutlined :style="{
                  fontSize: '12px',
                  color: 'rgba(0, 0, 0, 0.25)',
                }" />
            </div>
            <div v-if="row.kind === '3'">
              {{ row.selectedRule }}
            </div>
          </template>
          <template #edit="{ row }">
            <a-checkbox
              v-if="row.kind === '1'"
              v-model:checked="row.selected"
              v-model:groupName="row.relationGroupName"
              ref="myCheckBox"
              @change="updeteRule(row)"
              :disabled="row.beGray || props.currentInfo?.tempDeleteFlag"
            ></a-checkbox>
            <div v-if="row.kind === '2'">
              <chapter-info
                v-model:filedValue="row.selectedRuleGroup"
                :groupTypeList="groupTypeList"
                :ruleInfo="row.ruleInfo"
                :ruleRcjCode="row.currentRcjCode"
                :currentRcjLibraryCode="row.currentRcjLibraryCode"
                :placement="props.placement || 'top'"
                @selectChange="ruleMixProportion"
                @selectInfo="selectInfo"
                @showChapter="showChapter"
                @cancel="cancel"
                :treeData="treeData"
              ></chapter-info>
            </div>
            <vxe-input
              v-if="row.kind === '3'"
              v-model="row.selectedRule"
              type="text"
              placeholder="请输入值"
              :disabled="props.currentInfo?.tempDeleteFlag"
              @keyup="
                row.selectedRule = (row.selectedRule.match(
                  /\d{0,8}(\.\d{0,2}|100)?/
                ) || [''])[0]
              "
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-table>
      <standard-type-table
        class="rightTable"
        v-if="!props.isSetStandard"
        :currentInfo="props.currentInfo"
        @updateData="updateData"
      ></standard-type-table>
    </div>
  </div>
</template>

<script setup>
import api from '@/api/projectDetail';
import { message } from 'ant-design-vue';
const props = defineProps([
  'tableData',
  'currentInfo',
  'type',
  'isSetStandard',
  'placement',
]);
import { QuestionCircleOutlined, DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import {
  defineAsyncComponent,
  nextTick,
  onActivated,
  onMounted,
  reactive,
  ref,
  toRaw,
  watch,
} from 'vue';
import StandardTypeTable from './standardTypeTable.vue';
import ChapterInfo from './chapterInfo.vue';
import infoMode from '@/plugins/infoMode.js';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();

const emits = defineEmits([
  'updateData',
  'currentInfo',
  'type',
  'batchConversionRule',
]);
const projectStore = projectDetailStore();
const groupTypeList = ref([]); // 配合比类型下拉框数据
const treeData = ref([]);
const currentInfo = ref(null);
const vexTable = ref();
const loading = ref(false);

let selectedRuleGroup = ref(null);
let chapterStatus = ref(false);
let standardConvertMod = ref(1);
let mainMatConvertMod = ref(); // 主材设备是否受系数调整影响
let standardConvertList = reactive([
  {
    code: 1,
    label: '以标准人材机数据执行',
  },
  {
    code: 2,
    label: '以当前数据执行计算',
  },
]);
let isEdit = ref(false); // 判断是否有编辑报错  使用场景，kind=3输入一个超出默认值的值，然后直接点击kind=1的多选框

const selectChapter = defineAsyncComponent(() =>
  import('@/components/selectChapter/index.vue')
);

watch(
  () => props.tableData,
  () => {
    if (props.tableData) {
      emits('batchConversionRule', props.tableData);
      standardConvertMod.value = props.currentInfo?.standardConvertMod || 2;
      mainMatConvertMod.value = props.currentInfo?.mainMatConvertMod || false;
      props.tableData.forEach(item => {
        if (item.sequenceNbr === currentInfo.value?.sequenceNbr) {
          currentInfo.value = item;
        }
      });
      if (!currentInfo.value) {
        currentInfo.value = props.tableData[0];
      }
      vexTable.value.setCurrentRow(currentInfo.value);
    }
  }
);

watch(
  () => props.currentInfo,
  (newValue, oldValue) => {
    nextTick(() => {
      standardConvertMod.value = props.currentInfo?.standardConvertMod || 2;
      mainMatConvertMod.value = props.currentInfo?.mainMatConvertMod || false;
      if (!newValue) return;
      if (newValue?.sequenceNbr !== oldValue?.sequenceNbr) {
        currentInfo.value = null;
      }
    });
  }
);

const cancel = () => {
  vexTable.value.clearEdit();
};

const tableCellClick = ({ row }) => {
  if (props.currentInfo?.tempDeleteFlag) return false;
  return true;
};

const updeteRule = (row, clpb) => {
  console.log('row', row);
  if (isEdit.value) {
    vexTable.value.revertData(row);
    return;
  }
  // 找到这个a-chekbox 兄弟
  if (row.selected) {
    let rowItem = toRaw(row);
    props.tableData.forEach(item => {
      let cLine = toRaw(item);
      if (
        rowItem.relationGroupName &&
        item.selected &&
        rowItem.relationGroupName === cLine.relationGroupName &&
        rowItem.sequenceNbr !== cLine.sequenceNbr
      ) {
        item.selected = false;
        updeteRule(item, clpb);
      }
    });
  }
  if (clpb) {
    row.clpb = clpb;
  }
  row.nowChange = true;
  row.selected = row.selected ? 1 : 0;
  if (row.kind === '3') {
    const group = getList(props.tableData);
    group.forEach(item => {
      if (item[0].kind === '3' && item[0].type === 'e2') {
        let sum = 0;
        let updateIndex = item.findIndex(
          child => child.sequenceNbr === row.sequenceNbr
        );
        console.log('updateIndex', updateIndex);
        item.forEach((child, index) => {
          if (updateIndex !== item.length - 1) {
            if (index !== item.length - 1) {
              if (sum < 100) {
                sum += Number(child.selectedRule);
                if (sum > 100) {
                  sum = sum - item[index].selectedRule;
                  item[index].selectedRule = 0;
                  item[index].nowChange = true;
                }
              } else {
                child.selectedRule = 0;
                child.nowChange = true;
              }
            }
          } else if (updateIndex === item.length - 1) {
            if (index !== 0) {
              if (sum < 100) {
                sum += Number(child.selectedRule);
                if (index === updateIndex && sum > 100) {
                  sum = sum - item[index - 1].selectedRule;
                  item[index - 1].selectedRule = 0;
                  item[index - 1].nowChange = true;
                }
              } else {
                child.selectedRule = 0;
                child.nowChange = true;
              }
            }
          }
        });
        console.log('sum', sum);
        if (updateIndex !== item.length - 1) {
          item[item.length - 1].selectedRule = Number(100 - sum).toFixed(2);
          item[item.length - 1].nowChange = true;
        } else if (updateIndex === item.length - 1) {
          item[0].selectedRule = Number(100 - sum).toFixed(2);
          item[0].nowChange = true;
        }
      }
    });
    console.log('group333333333333', group);
    console.log('444444444444444', props.tableData);
  }
  if (props.isSetStandard) {
    emits('batchConversionRule', props.tableData);
    return;
  }
  // 接口调用
  console.log('row', row.selected);
  batchOperationalConversionRule();
};

const batchOperationalConversionRule = () => {
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    rules: JSON.parse(JSON.stringify(props.tableData)),
  };
  console.log('标准换算勾选参数', apiData);
  api
    .batchOperationalConversionRule(JSON.parse(JSON.stringify(apiData)))
    .then(res => {
      console.log('标准换算勾选数据', res);
      if (res.status === 200 && res.result) {
        message.success('修改成功');
        emits('updateData', 1);
      }
    });
};
const getList = list => {
  const map = new Map();
  list.forEach((item, index, arr) => {
    if (!map.has(item.relationGroupName)) {
      map.set(
        item.relationGroupName,
        arr.filter(a => a.relationGroupName === item.relationGroupName)
      );
    }
  });
  return Array.from(map).map(item => [...item[1]]);
};

// 清空换算信息
const reset = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    fbFxDeId: props.currentInfo?.sequenceNbr,
    standardDeId: props.currentInfo?.standardId,
  };
  api.reset(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('标准换算已清空');
      emits('updateData', 1);
    }
  });
};

const updateData = () => {
  emits('updateData', 1);
};
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};
// 单元格退出编辑事件
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;

  if (['selectedRule'].includes(field) && row.selectedRule) {
    row[field] = +row[field];
  }
  if ($table.isUpdateByRow(row, field)) {
    if (row.kind !== '3') {
      return;
    }
    console.log('🚀 ~ editClosedEvent ~ row:', row);
    ruleInfo(row);
  }
};

onMounted(() => {
  nextTick(() => {
    let document1 = document.querySelector(
      '.standard-conversion-table .vxe-header--row'
    );
    document1.children[1].setAttribute('colspan', '2');
    console.log('document');
    groupTypeSelect();
  });
});

// 通用行合并函数（将相同多列数据合并为一行）
const mergeRowMethod = ({
  row,
  _rowIndex,
  column,
  visibleData,
  _columnIndex,
}) => {
  const fields = ['relationGroupName'];
  const cellValue = row[column.field];
  if (cellValue && fields.includes(column.field)) {
    const prevRow = visibleData[_rowIndex - 1];
    let nextRow = visibleData[_rowIndex + 1];

    if (prevRow && prevRow[column.field] === cellValue) {
      return { rowspan: 0, colspan: 0 };
    } else {
      let countRowspan = 1;
      while (nextRow && nextRow[column.field] === cellValue) {
        nextRow = visibleData[++countRowspan + _rowIndex];
      }
      if (countRowspan > 1) {
        return { rowspan: countRowspan, colspan: 1 };
      }
    }
  } else if (!row.relationGroupName && _columnIndex === 1) {
    return { rowspan: 1, colspan: 2 };
  } else if (!row.relationGroupName && _columnIndex === 2) {
    return { rowspan: 0, colspan: 0 };
  }
};

// 章节点击展示弹窗
const showChapter = () => {
  // console.log('🚀 ~ showChapter ~ row:', row);
  //  row.chapterStatus = true;
  //  currentInfo.value = row;
  //
  //  chapterStatus.value = true;
  selectedRuleGroup.value = currentInfo.value.selectedRuleGroup;
  ruleMixProportion(currentInfo.value.selectedRuleGroup);
};

// 配比类型下拉框
const groupTypeSelect = () => {
  const postData = {
    libraryCode: props.currentInfo?.libraryCode,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('配比类型下拉框', postData);
  api.groupTypeSelect(postData).then(res => {
    console.log('groupTypeSelect', res);
    if (res.status === 200 && res.result) {
      groupTypeList.value = res.result;
      groupTypeList.value = groupTypeList.value.map(item => {
        return {
          libraryName: item.groupName,
          libraryCode: item.groupName,
        };
      });
    }
  });
};

const ruleMixProportion = value => {
  if (!value) {
    treeData.value = [];
    return;
  }
  let apiData = {
    topGroupType: currentInfo.value.topGroupType,
    groupName: value,
    libraryCode: props.currentInfo?.libraryCode,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  console.log('🚀 ~ ruleMixProportion ~ apiData:', apiData);
  api.ruleMixProportion(apiData).then(res => {
    console.log('数据', res);
    if (res.status === 200 && res.result) {
      let title = Object.keys(res.result)[0];
      let obj = {
        details: value,
        detailsCode: 1,
        childrenList: res.result,
      };
      treeData.value = [obj];
      console.log('treeData', treeData.value);
    }
  });
};

const selectInfo = item => {
  currentInfo.value.selected = true;
  currentInfo.value.ruleInfo = item.details + ' ' + (item.specification || '');

  updeteRule(currentInfo.value, item);
};

// 设置行样式
const rowClassName = ({ row }) => {
  if (row.beGray) {
    return 'row-disabled';
  }
  return null;
};

const check = (suanShi, value, defaultValue) => {
  //debugger
  let newSuanShi = suanShi
    .toLowerCase()
    .replaceAll('＞', '>')
    .replaceAll('＜', '<');

  if (Number(value) === Number(defaultValue)) {
    return true;
  }
  if (!newSuanShi.includes('v')) {
    newSuanShi = 'v' + newSuanShi;
  } else {
    let arr = newSuanShi.split(/≤|<|≥|>/);
    if (arr.length > 2) {
      newSuanShi = newSuanShi.replace('v', 'v&&v');
    }
  }

  newSuanShi = newSuanShi
    .replaceAll('v', value)
    .replaceAll('≤', '<=')
    .replaceAll('≥', '>=');

  if (suanShi === '正负无穷大') {
    return true;
  }
  return eval(newSuanShi);
};

const ruleInfo = row => {
  if (check(row.ruleRange, row.selectedRule, row.defaultValue)) {
    updeteRule(row);
  } else {
    isEdit.value = true;
    let text = checkRule(row.ruleRange);
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: '请输入的值' + text,
      confirm: () => {
        infoMode.hide();
        let $table = vexTable.value;
        $table.revertData(row);
        isEdit.value = false;
      },
    });
  }
};

const formatInfo = ruleRange => {
  let formatMap = new Map();
  formatMap.set('≤', '小于等于');
  formatMap.set('<', '小于');
  formatMap.set('≥', '大于等于');
  formatMap.set('>', '大于');

  let formulaMap = new Map();
  formulaMap.set('≤', '≥');
  formulaMap.set('<', '>');
  formulaMap.set('≥', '≤');
  formulaMap.set('>', '<');

  if (/^[≤<≥>]/.test(ruleRange)) {
    let first = ruleRange.substring(0, 1);
    return ruleRange.replace(first, formatMap.get(first));
  } else {
    let sign = ruleRange.replaceAll(/\d+|\./g, '');
    return (
      formatMap.get(formulaMap.get(sign)) + ruleRange.replaceAll(/[≤<≥>]/g, '')
    );
  }

  return ruleRange;
};

function checkRule(ruleRange) {
  let newSuanShi = ruleRange
    .toLowerCase()
    .replaceAll('＞', '>')
    .replaceAll('＜', '<');
  if (!newSuanShi.includes('v')) {
    return formatInfo(newSuanShi);
  } else {
    let arr = newSuanShi.split(/≤|<|≥|>/);
    if (arr.length > 2) {
      let arrRuleRange = newSuanShi.split('v');
      return formatInfo(arrRuleRange[0]) + '且' + formatInfo(arrRuleRange[1]);
    } else {
      return formatInfo(newSuanShi.replace('v', ''));
    }
  }
}

const move = val => {
  let index = currentInfo.value.index;
  let list = props.tableData;
  if (val === 1) {
    if (currentInfo.value.rowSpan === 1) {
      if (list[index - 1].rowSpan === 1) {
        const temp = props.tableData[index];
        list[index] = list[index - 1];
        list[index - 1] = temp;
      } else {
        let startIndex = list.findIndex(
          x => x.relationGroupName === list[index - 1].relationGroupName
        );
        list.splice(index, 1);
        list.splice(startIndex, 0, currentInfo.value);
      }
    } else {
      let groupStartIndex = list.findIndex(
        x => x.relationGroupName === currentInfo.value.relationGroupName
      );
      if (list[groupStartIndex - 1].rowSpan === 1) {
        let temp = list[groupStartIndex - 1];
        list.splice(groupStartIndex - 1, 1);
        let addIndex = list.findLastIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex + 1, 0, temp);
      } else {
        let beforeList = list.filter(
          x =>
            x.relationGroupName === list[groupStartIndex - 1].relationGroupName
        );
        list = removeElements(list, beforeList);
        let addIndex = list.findLastIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex + 1, 0, ...beforeList);
      }
    }
  } else {
    if (currentInfo.value.rowSpan === 1) {
      if (list[index + 1].rowSpan === 1) {
        const temp = props.tableData[index];
        list[index] = list[index + 1];
        list[index + 1] = temp;
      } else {
        let startIndex = list.findLastIndex(
          x => x.relationGroupName === list[index + 1].relationGroupName
        );
        list.splice(index, 1);
        list.splice(startIndex, 0, currentInfo.value);
      }
    } else {
      let groupEndIndex = list.findLastIndex(
        x => x.relationGroupName === currentInfo.value.relationGroupName
      );
      let startIndex = list.findLastIndex(
        x => x.relationGroupName === list[groupEndIndex + 1].relationGroupName
      );
      console.log('groupEndIndex', groupEndIndex, startIndex);
      if (list[groupEndIndex + 1].rowSpan === 1) {
        let temp = list[groupEndIndex + 1];
        list.splice(groupEndIndex + 1, 1);
        let addIndex = list.findIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex, 0, temp);
      } else {
        let afterList = list.filter(
          x => x.relationGroupName === list[groupEndIndex + 1].relationGroupName
        );
        console.log('afterList', afterList);
        list = removeElements(list, afterList);
        let addIndex = list.findIndex(
          x => x.relationGroupName === currentInfo.value.relationGroupName
        );
        list.splice(addIndex, 0, ...afterList);
      }
    }
  }
  list.forEach((item, index) => {
    item.index = index;
  });
  console.log('移动数据', props.tableData);
  batchOperationalConversionRule();
};

const removeElements = (arr, elements) => {
  elements.forEach(item => {
    const index = arr.indexOf(item);
    if (index > -1) {
      arr.splice(index, 1);
    }
  });
  return arr;
};

const switchConversionMethods = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    standardConvertMod: standardConvertMod.value,
  };
  console.log('apiDAta', apiData);
  api.switchConversionMod(apiData).then(res => {
    if (res.status === 200 && res.result) {
      emits('updateData', 1);
    }
  });
};

const switchConversionMainMatMod = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    mainMatConvertMod: mainMatConvertMod.value,
  };
  console.log('apiDAta', apiData);
  api.switchConversionMainMatMod(apiData).then(res => {
    if (res.status === 200 && res.result) {
      emits('updateData', 1);
    }
  });
};
</script>

<style lang="scss" scoped>
.standard-conversion-table {
  width: 100%;
  height: 100%;
  .content-detail {
    height: calc(100% - 40px) !important;
  }
  .content {
    // display: block;
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    :deep(.vxe-table) {
      width: 65%;
      height: 100%;
      .vxe-table--render-wrapper {
        height: 100%;
        .vxe-table--main-wrapper {
          height: 100%;
          display: flex;
          flex-direction: column;
          .vxe-table--body-wrapper {
            flex: 1;
            height: auto !important;
            min-height: auto !important;
          }
        }
      }
    }
    .table-edit-common {
      width: 100% !important;
      height: 100%;
    }
    .rightTable {
      width: 33%;
    }
  }
}
.relation-logo {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  right: 10px;
}
::v-deep(.standard-conversion-table .vxe-header--row) {
  th:nth-of-type(3) {
    display: none;
  }
}
::v-deep(.vxe-table .row-disabled) {
  background: #ececec;
}
.head {
  .text {
    margin-left: 10px;
  }
  .ant-checkbox-wrapper {
    margin-left: 20px;
  }
}
</style>
