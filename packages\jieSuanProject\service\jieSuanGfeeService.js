const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const DePropertyTypeConstant = require("../../../electron/enum/DePropertyTypeConstant");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {ResponseData} = require("../../../common/ResponseData");
const JieSuanFeeSetEnum = require("../enum/JieSuanFeeSetEnum");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {Gfee} = require("../../../electron/model/Gfee");
const {ArrayUtil} = require("../../../electron/utils/ArrayUtil");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Service} = require('../../../core');
class jieSuanGfeeService extends Service{

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    async getJcGfeeFee(args){
        let constructId = args.constructId;
        let singleId = args.singleId;
        let unitId = args.unitId;

        await this.countJcGfees(constructId, singleId, unitId);
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        return unit.jcGfees;
    }

    /**
     * 计算规费明细
     * @param args
     */
    async countJcGfees(constructId, singleId, unitId) {
        let unit =await PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // 获取单位工程下所有的定额
        let deList =await this.service.jieSuanProject.jieSuanSafeFeeService.getDeList(constructId, singleId, unitId);
        let copyDeList = ConvertUtil.deepCopy(deList);
        // 定额按照取费专业分组
        let costMajorNameGroup = ArrayUtil.group(copyDeList,'costMajorName');
        let rcjListAll = await this.service.rcjProcess.getCountRcjList(constructId, singleId, unitId) === null ? new Array() : this.service.rcjProcess.getCountRcjList(constructId, singleId, unitId);
        let args ={
            constructId:constructId,
            singleId:singleId,
            unitId:unitId,
            rcjList:rcjListAll
        }
        //调用计算价差后的人材机
        let res = await this.service.jieSuanProject.jieSuanRcjStageService.jieSuanRcjListTongYiTiaoCha(args);


        // 遍历分组后的对象
        let group = new Array();
        for ( let [key, value] of costMajorNameGroup.entries()) {
            let item = {};
            item.costMajorName = key;
            // 该取费专业的所有人材机
            let rcjLists = [];
            for (let j = 0; j < value.length; j++) {
                let de = value[j];
                // 获取该定额的人材机
                let rcjList = res.filter(i => i.deId === de.sequenceNbr)
                let rcjListNew = rcjList.filter(rcj => rcj.jieSuanFee == JieSuanFeeSetEnum.METHOD2.code);
                rcjLists.push(...rcjListNew);
            }

            //定额下记取安文费的价差合计
            let priceDifferencSum_de = rcjLists.reduce((accumulator, constructProjectRcj) => {
                return NumberUtil.add(accumulator , constructProjectRcj.jieSuanPriceDifferencSum);
            }, 0)
            item.formula =priceDifferencSum_de;
            group.push(item);
        }
        let jcGfees = new Array();
        let feeFiles = unit.feeFiles;
        if(!ObjectUtils.isObject(group)){

            for (let i = 0; i < group.length; i++) {
                let item = group[i];
                // 当前定额取费专业
                let feeFile = feeFiles.find(obj => obj.feeFileName === item.costMajorName);
                let jcGfee = new Gfee();
                jcGfee.sequenceNbr = Snowflake.nextId();
                jcGfee.unitId = unit.sequenceNbr;
                jcGfee.costMajorName = item.costMajorName;
                jcGfee.costFeeBase = ObjectUtils.isEmpty(item.formula)?item.formula:NumberUtil.numberScale2(item.formula) ;
                jcGfee.gfeeRate =feeFile.fees;
                jcGfee.priceDifferencFeeAmount =NumberUtil.numberScale2(NumberUtil.multiply(item.formula,feeFile.fees/100)) ;
                jcGfees.push(jcGfee);
            }

        }

        unit.jcGfees = jcGfees;
    }



}
jieSuanGfeeService.toString = () => '[class jieSuanGfeeService]';
module.exports = jieSuanGfeeService;