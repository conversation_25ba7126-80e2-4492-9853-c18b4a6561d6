<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2023-09-05 14:17:35
-->
<template>
  <div>
    <div class="head">
      <span v-for="item in tabList" :key="item.id">
        <a-button
          type="text"
          :disabled="item.isDisabled"
          @click="clickEvent(item.title)"
          v-if="item.show"
          ><img :src="item.url" :alt="item.title" />{{ item.title }}</a-button
        >
      </span>
    </div>
    <div class="table-content"></div>
  </div>
</template>
<script setup>
import { reactive, watch, ref, onMounted, defineEmits } from 'vue';
import { getUrl } from '@/utils/index';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
import { message } from 'ant-design-vue';
const emit = defineEmits(['isUse']);
const tabList = reactive([
  {
    id: 1,
    title: '载价',
    url: getUrl('detailImg/zaijia.png'),
    isDisabled: false,
    show: true,
  },
  {
    id: 2,
    title: '调整市场价系数',
    url: getUrl('detailImg/shichangjia.png'),
    isDisabled: false,
    show: true,
  },
  {
    id: 3,
    title: '统一应用',
    url: getUrl('detailImg/tongyi.png'),
    isDisabled: true,
    show: true,
  },
  {
    id: 3,
    title: '导出',
    url: getUrl('detailImg/daochu.png'),
    isDisabled: false,
    show: true,
  },
]);
watch(
  () => store.asideMenuCurrentInfo,
  () => {
    if (store.tabSelectName === '人材机汇总') {
      tabList[2].show = store.currentTreeInfo.levelType === 1 ? true : false;
    }
  }
);
watch(
  () => store.humanUpdataData,
  () => {
    if (store.humanUpdataData && store.humanUpdataData.isEdit) {
      tabList[2].isDisabled = false;
    } else {
      tabList[2].isDisabled = true;
    }
  }
);
onMounted(() => {
  tabList[2].show = store.currentTreeInfo.levelType === 1 ? true : false;
});
const clickEvent = type => {
  switch (type) {
    case '统一应用':
      //此处写统一应用接口代码
      emit('isUse'); //修改费率总览统一应用按钮也可点击
      break;
    case '载价':
    case '调整市场价系数':
    case '导出':
      message.info('功能建设中...');
      break;
  }
};
</script>
<style lang="scss" scoped>
.head {
  // display: flex;
  // align-items: center;
  // height: 40px;
  // padding: 0 10px;
  span img {
    margin: 0 0 0 5px;
    margin-bottom: 3px;
  }
}
</style>
