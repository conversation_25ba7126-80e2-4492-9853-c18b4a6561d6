/*
 * @Descripttion: 系统配置信息
 * @Author:
 * @Date: 2025-01-23 14:13:49
 * @LastEditors: sunchen
 * @LastEditTime: 2025-04-07 11:22:39
 */
import { defineStore } from 'pinia';
import { setCssProperty } from '@/utils/index';
import {
  RCJ_DETAIL_AMOUNT,
  RCJ_SUMMARY_AMOUNT,
  QD_DE_AMOUNT,
  COST_PRICE,
  RATE,
} from '@/enum/decimalPoint.js';

export const systemConfigStore = defineStore('systemConfig', {
  state: () => ({
    functionalArea: {
      //  功能区配置信息
      isExpand: true,
      height: '54px',
    },
    tableConfig: {
      defaultFontSize: 12, // 默认字体大小
      scale: 100, // 缩放比例
      maxScale: 200,
      minScale: 50,
    },
    decimalConfig: {
      rcjDetailAmount: RCJ_DETAIL_AMOUNT,
      rcjSummaryAmount: RCJ_SUMMARY_AMOUNT,
      qdDeAmount: QD_DE_AMOUNT,
      costPrice: COST_PRICE,
      rate: RATE,
    },
    gljDecimalConfig: {},
  }),
  actions: {
    setFunctionalArea(info) {
      setCssProperty('--project-detail-functional-area-height', info.height);
      this.functionalArea = info;
    },
    setTableConfig(info) {
      let { scale, defaultFontSize, maxScale, minScale } = info;
      if (scale > maxScale) {
        info.scale = maxScale; // 最大缩放比例为2倍
      }
      if (scale < minScale) {
        info.scale = minScale; // 最小缩放比例为0.5倍
      }
      const size = (defaultFontSize * info.scale) / 100;
      setCssProperty('--project-detail-table-font-size', size + 'px');
      setCssProperty('--vxe-font-size-mini', size + 'px');
      this.tableConfig = info;
    },
  },
  getters: {
    tableScaleRatio() {
      return this.tableConfig.scale / 100;
    },
  },
});
