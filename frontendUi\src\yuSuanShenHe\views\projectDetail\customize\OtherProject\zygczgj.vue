<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-03-25 11:12:18
-->
<template>
  <div class="table-content">
    <child-page-table
      :pageType="'zygczgj'"
      :columnList="columnList"
    ></child-page-table>
  </div>
</template>
<script setup>
import ChildPageTable from './childPageTable.vue';
const columnList = [
  {
    field: '',
    title: '',
    minWidth: 60,
    slots: {default:'changeIdentification'},
  },
  {
    title: '送审',
    children:[
      {
        field: 'dispNo',
        title: '序号',
        minWidth: 60,
        type: 'text',
        slots: { default:'dispNo_default'},
      },
      {
        field: 'name',
        title: '工程名称',
        minWidth: 180,
        slots: { default:'name_default' },
      },
      {
        field: 'content',
        title: '工程内容',
        minWidth: 180,
        slots: { default:'content_default' },
      },
      {
        field: 'unit',
        title: '单位',
        minWidth: 100,
        slots: { default:'unit_default' },
      },
      {
        field: 'amount',
        title: '数量',
        minWidth: 80,
        slots: { default:'amount_default' },
      },
      {
        field: 'price',
        title: '单价',
        minWidth: 100,
        slots: { default:'price_default' },
      },
      {
        field: 'total',
        minWidth: 100,
        title: '金额',
        slots: { default:'total_default' },
      },
    ]
  },
  {
    title: '审定',
    children:[
      {
        field: 'dispNo',
        title: '序号',
        minWidth: 60,
        type: 'text',
      },
      {
        field: 'name',
        title: '工程名称',
        minWidth: 180,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'name_edit' },
      },
      {
        field: 'content',
        title: '工程内容',
        minWidth: 180,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'content_edit' },
      },
      {
        field: 'unit',
        title: '单位',
        minWidth: 100,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'unit_edit' },
      },
      {
        field: 'amount',
        title: '数量',
        minWidth: 80,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'amount_edit' },
      },
      {
        field: 'price',
        title: '单价',
        minWidth: 100,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'price_edit' },
      },
      {
        field: 'total',
        minWidth: 100,
        title: '金额',
      },
    ]
  },
  {
    field: 'changeTotal',
    minWidth: 100,
    title: '增减金额',
    slots: {default:'changeTotal_default' },
  },
  {
    field: 'ysshSysj.changeExplain',
    title: '增减说明',
    editRender: { autofocus: '.vxe-input--inner' },
    minWidth: 100,
    slots: {default:'changeExplain_default',edit: 'changeExplain_edit'},
  },
];
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
