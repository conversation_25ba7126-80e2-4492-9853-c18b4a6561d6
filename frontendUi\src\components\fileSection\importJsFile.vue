<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-25 17:43:51
-->
<template>
  <common-modal
    @close="cancel()"
    className="dialog-comm tree-dialog"
    :showClose="!loading"
    :loading="loading"
    width="auto"
    v-model:modelValue="dialogVisible"
    title="导入项目"
  >
    <div class="tree-content-wrap">
      <div class="tree-list">
        <div class="dialog-content">
          <div class="title">当前项目</div>
          <div
            class="list"
            v-if="currentTreeData"
          >
            <a-radio-group v-model:value="currentSelected.keys">
              <a-tree
                :defaultExpandAll="true"
                show-line
                selectable
                :tree-data="currentTreeData"
                :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                @select="currentSelect"
              >
                <template #switcherIcon="{ switcherCls, children }">
                  <down-outlined :class="switcherCls" />
                </template>
                <template #title="{ levelType, id, name, whetherNew,whetherReplace, parentId }">
                  <icon-font
                    class="mIcon del-icon"
                    :class="`delicon${levelType}`"
                    type="icon-shanchu1"
                    v-show="whetherNew||whetherReplace"
                    @click="delImportItem(id,name,parentId,levelType)"
                  />
                  <span class="check-labels">{{ name }}</span>
                </template>
              </a-tree>
            </a-radio-group>
          </div>
        </div>

        <a-button
          type="primary"
          @click="move"
        >
          <template #icon><left-outlined /></template>
          导入
        </a-button>

        <div class="dialog-content">
          <div class="title">
            <span>导入项目</span>
            <div class="checkhandle">
              <a-radio-group
                v-model:value="checkStatus"
                @change="changeStatus"
              >
                <a-radio value="all">全部</a-radio>
                <a-radio value="part">取消全选</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div
            class="list"
            v-if="importTreeData"
          >
            <a-tree
              :defaultExpandAll="true"
              checkable
              show-line
              multiple
              :tree-data="importTreeData"
              @check="importSelect"
              :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
              :checkedKeys="importCheckedKeys"
            >
              <template #switcherIcon="{ switcherCls, children }">
                <down-outlined :class="switcherCls" />
              </template>
            </a-tree>
          </div>
        </div>
      </div>
      <!-- <div class="group-list">
        <a-radio-group v-model:value="dataStatus">
          <a-radio value="all">以当前项目费率为准</a-radio>
          <a-radio value="part">以导入项目标准率为准</a-radio>
        </a-radio-group>
      </div> -->
      <div style="position: absolute;bottom: 30px;">
        <a-radio-group
          v-model:value="radioAorT"
          :disabled="currentSelected.node ? currentSelected.node.originalFlag : true"
        >
          <a-radio value="1">合同外追加导入</a-radio>
          <a-radio value="2">合同外替换导入</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel()">取消</a-button>
        <a-button
          type="primary"
          @click="handleOk"
          :title="handleTip()"
          :loading="submitLoading"
        >确定</a-button>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  nextTick,
  toRaw,
  defineExpose,
  watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/jiesuanApi';
import { getJsAsideTreeList } from '@/api/jiesuanApi';
import { ConstructMenuOperator } from '@/components/editJsProjectStructure/ConstructMenuOperator';
const menuOperator = new ConstructMenuOperator();
import { DownOutlined, LeftOutlined } from '@ant-design/icons-vue';
import xeUtils from 'xe-utils';
const emits = defineEmits(['closeDialog']);
const route = useRoute();
const submitLoading = ref(false);
const dialogVisible = ref(false);
const currentTreeData = ref(null);
const copyTreeData = ref(null);
const importTreeData = ref(null);
const importCheckedKeys = ref([]);
const checkStatus = ref(null); //全选，取消全选
const hasSingle = ref(false); //当前项目，是否有单项结构
const importSingleList = ref([]); // 导入项目，所有的单项结构
const loading = ref(false);
const currentSelectKeys = ref([]); //当前选中项目
const expandedKeys = ref([]); //当前项目选中
const radioAorT = ref('');
const currentSelected = reactive({
  initData: [],
  keys: '',
  node: null,
  parentObj: [], //选中项单位的父级列表
});
const importSelected = reactive({
  historyKeys: [],
  handleKeys: [], //点击了全选
});
const dataStatus = ref(null);
const importYsf = ref(null);

const props = defineProps(['importJsList']);

// 处理确定的提示语
const handleTip = () => {
  let msg = '';
  if (!importSelected.historyKeys.length) {
    msg = '请选择导入项目!';
  }

  // else if(!dataStatus.value){
  //   msg="请选择费率标准!"
  // }
  return msg;
};

const currentSelect = (
  selectedKeys,
  { selected, selectedNodes, node, event }
) => {
  // expandedKeys.value----当前选中项直级父级
  let target = currentTreeTile.find(i => i.id === selectedKeys[0]);
  if (target?.levelType === 3) {
    let parent = menuOperator.getSingleNew(target, currentTreeTile);
    expandedKeys.value = target.parentId;
  } else {
    expandedKeys.value = selectedKeys[0];
  }
  let nodeData = node.dataRef;
  currentSelected.keys = selectedKeys[0]; //当前选中的key
  currentSelected.node = nodeData; //当前选中的node
  let parentList = getParentId(currentTreeData.value, currentSelected.keys);
  currentSelected.parentObj =
    parentList &&
    parentList.filter(i => i.levelType !== 1 && i.levelType !== 3);
  console.log(
    'currentSelected',
    currentSelected,
    'expandedKeys.value',
    expandedKeys.value
  );
  //   // 如果选择的为合同内项目则下方切换设置为空
  if (nodeData.originalFlag) {
    radioAorT.value = '';
  } else {
    radioAorT.value = '1';
  }
};

const importSelect = (checkedKeys, { checked, checkedNodes, node, event }) => {
  importCheckedKeys.value = checkedKeys;
};
let unitImportList = ref([]);
const getImportUnitList = () => {
  unitImportList.value = importTreeTile.filter(
    i => i.levelType === 3 && importCheckedKeys.value.includes(i.id)
  );
};

// 修改导入项目选择
const changeStatus = async () => {
  importSelected.handleKeys = [];
  await flattenTree(importTreeData.value, true);
  importCheckedKeys.value =
    checkStatus.value === 'all'
      ? [...importSelected.handleKeys, ...importSelected.historyKeys]
      : importSelected.historyKeys;
};

//平铺结构的导入树及导出树
let currentTreeTile = reactive([]); //当前平铺树
let importTreeTile = reactive([]); //导入平铺树
const setOriginalFlag = parent => {
  if (parent.levelType !== 1 && parent.children.length > 0) {
    parent.children.map(a => {
      a.originalFlag = parent.originalFlag;
      setOriginalFlag(a);
    });
  } else if (parent.levelType === 1 && parent.children.length > 0) {
    parent.children.map(a => {
      setOriginalFlag(a);
    });
  }
};
const tileTreeList = () => {
  setOriginalFlag(currentTreeData.value[0]);
  setOriginalFlag(importTreeData.value[0]);
  currentTreeTile = xeUtils.toTreeArray(currentTreeData.value);
  importTreeTile = xeUtils.toTreeArray(importTreeData.value);
};
// 点击导入
let impostMoveNodes = ref([]);
const move = async () => {
  if (!currentTreeData.value.length) {
    message.error('当前项目错误！');
    return;
  }

  // 查找要复制过去的元素
  const elNodes = importCheckedKeys.value
    .filter(i => !importSelected.historyKeys.includes(i))
    .map(i => toRaw(findElement(i, importTreeData.value)));
  const unitelNodes = elNodes.filter(i => i.levelType === 3);
  impostMoveNodes.value = [...elNodes];
  console.log('elNodes', elNodes, unitelNodes);
  const tree = xeUtils.toArrayTree(unitelNodes, { key: 'id' });

  // 设置whetherNew
  recursionTree(tree, 'addNew');
  // 向左侧插入数据前置校验
  const res = await appendAfterCheck(unitelNodes, tree, unitelNodes);
  if (!res.status) {
    message.error(res.msg);
    return;
  }

  //插入数据
  appendTree(unitelNodes);

  //  导入项目，禁用已经导入的
  disableItem();
};

// 开始向左侧插入数据前置校验
const appendAfterCheck = (elNodes, tree, importNodes) => {
  return new Promise(async (resolve, reject) => {
    if (!tree.length) {
      resolve({ status: false, msg: '请选择导入层级' });
      return;
    }
    if (!currentSelected.keys) {
      resolve({ status: false, msg: '请选择当前项目' });
      return;
    }
    if (importCheckedKeys.value.length === 0) {
      resolve({ status: false, msg: '请选择要导入项目' });
      return;
    }
    const target = currentTreeTile.find(i => i.id === currentSelected.keys);
    const importTar = [...importNodes];
    if (importTar.find(i => i.originalFlag !== target.originalFlag)) {
      resolve({ status: false, msg: '合同内项目与合同外项目不可交叉导入' });
      return;
    }
    let targetChildList = menuOperator.getChildList(currentSelected.node, []); //选中目标项子级
    // 合同内选择必须层级一样
    let leveLimit = true; //导入与当前选中层级相同
    if (target.originalFlag) {
      importTar.map(i => {
        let imParentList = getParentId(importTreeData.value, i.id); //获取当前选中项的层级结构
        let curSameList = targetChildList.filter(
          cur =>
            cur.name === i.name &&
            cur.originalFlag === i.originalFlag &&
            cur.levelType === i.levelType
        );
        if (curSameList.length === 0) {
          leveLimit = false;
          return;
        } else {
          leveLimit = curSameList.some(cur => {
            const curParentObj = getParentId(currentTreeData.value, cur.id);
            if (curParentObj.length === imParentList.length) {
              let flag = curParentObj.every(
                (a, idx) =>
                  a.name === imParentList[idx].name &&
                  a.levelType === imParentList[idx].levelType
              );
              if (flag) return true;
            }
            return false;
          });
        }
      });
    }
    if (!leveLimit) {
      resolve({ status: false, msg: '请选择正确的层级结构进行导入' });
      return;
    }
    resolve({ status: true });
  });
};
const compareSameLevel = (impList, importItem) => {
  //比较两个列表层级是否相同
  let leveLimit = true; //导入与当前选中层级相同
  let targetChildList = menuOperator.getChildList(currentSelected.node, []);
  //单项层级是否相同
  if (
    !targetChildList.find(a => a.levelType === 3) &&
    targetChildList.find(a => a.name === impList[0].name)
  ) {
    importItem = impList[0];
  }
  let curSameList = targetChildList.filter(
    cur =>
      cur.name === importItem.name &&
      cur.originalFlag === importItem.originalFlag &&
      cur.levelType === importItem.levelType
  );
  if (curSameList.length === 0) {
    return false;
  } else {
    leveLimit = curSameList.some(cur => {
      let curParentObj = getParentId(currentTreeData.value, cur.id);
      curParentObj = curParentObj.filter(d => [2].includes(d.levelType));
      if (curParentObj.length === impList.length) {
        console.log('impList', impList);
        let flag = curParentObj.every(
          (a, idx) =>
            a.name === impList[idx].name &&
            a.levelType === impList[idx].levelType
        );
        if (flag) return true;
      }
      return false;
    });
    return leveLimit;
  }
};
const compareLevel = (curList, impList) => {
  //比较两个列表层级是否相同
  if (impList.length !== curList.length) {
    return false;
  } else {
    impList.map((item, idx) => {
      if (curList[idx].name !== item.name) {
        return false;
      }
    });
  }
  return true;
};
/**
 * 处理当前项目的打开节点
 * @param {*} tree
 * @param {*} isAdd
 */
const handleExpandedKeys = (tree, isAdd = true) => {
  for (let i = 0; i < tree.length; i++) {
    if (
      [2].includes(tree[i].levelType) &&
      !expandedKeys.value.includes(tree[i].id)
    ) {
      expandedKeys.value.push(tree[i].id);
    }
  }
};

// 插入数据
const appendTree = tree => {
  let appendId = currentSelected.keys;
  const target = currentTreeTile.find(i => i.id === expandedKeys.value);
  // handleExpandedKeys(tree);
  // 插入数据
  currentTreeData.value = appendNodeInTree(
    target,
    currentTreeData.value,
    toRaw(tree)
  );
  console.log(
    '插入操作后当前树结构数据',
    currentTreeData.value,
    currentTreeTile,
    importTreeTile
  );
};

const delListId = ref([]); // 左侧点击删除的数据
const delParentIdList = ref([]);
const delImportItem = async (id, name, parentId, levelType) => {
  // 左侧的删除数据
  let parent = null;
  if (levelType === 3) {
    //删除单位的话连带父级一起删除
    parent = currentTreeTile.find(
      a => a.id === parentId && (a.whetherNew || a.whetherReplace)
    );
  }
  // debugger;
  if (parent) {
    await removeNodeInTree(
      currentTreeData.value,
      parent.id,
      parent.name,
      parent.parentId,
      parent.levelType
    );
  } else {
    await removeNodeInTree(
      currentTreeData.value,
      id,
      name,
      parentId,
      levelType
    );
  }
  console.log(delListId.value);
  // handleleftTree();
  console.log(
    'importSelected.historyKeys',
    importSelected.historyKeys,
    importCheckedKeys.value,
    delListId.value
  );
  let patentIdList = [];
  delListId.value.map(i => {
    let list = getParentId(importTreeData.value, i);
    console.log(i, importTreeTile);
    list &&
      list.map(a => {
        if (!patentIdList.includes(a.id)) {
          patentIdList.push(a.id);
        }
      });
  });
  delParentIdList.value = patentIdList;
  console.log('patentIdList', patentIdList);
  importSelected.historyKeys = toRaw(importSelected.historyKeys).filter(i => {
    return !patentIdList.includes(i);
  });

  importCheckedKeys.value = toRaw(importSelected.historyKeys).filter(i => {
    return !patentIdList.includes(i);
  });
  console.log(
    'patentIdList',
    patentIdList,
    importCheckedKeys.value,
    importSelected.historyKeys
  );
  currentTreeTile = xeUtils.toTreeArray(currentTreeData.value);
  // 解除禁用操作
  recursionTree(importTreeData.value, 'delDisable');
};

// 导入的数据，将左侧数据禁用
const disableItem = async () => {
  // 存储历史上一步操作的数据
  importSelected.historyKeys = xeUtils.clone(importCheckedKeys.value, true);

  recursionTree(importTreeData.value, 'addDisable');
};

// 在树中查找匹配的元素
const findElement = (key, nodes) => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.id === key) {
      const list = xeUtils.clone(node, true);
      delete list.children;
      return list;
    }
    if (node.children) {
      const result = findElement(key, node.children);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

// 扁平化树结构
const flattenTree = (tree, isLog = false, parent = null, result = []) => {
  for (const node of tree) {
    const { children, ...data } = node;
    result.push({ ...data });
    if (!node.disabled && isLog && !node.initDisable) {
      importSelected.handleKeys.push(node.id);
    }
    if (children && children.length > 0) {
      flattenTree(children, isLog, node.parentId, result);
    }
  }
  return result;
};
// 树结构替换子数据
const replaceChildrenInTreeFun = (treeData, parentId, newChildren) => {
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    if (node.id === parentId && node.levelType !== 3) {
      node.children = newChildren;
      return;
    }
    if (Array.isArray(node.children)) {
      replaceChildrenInTreeFun(node.children, parentId, newChildren);
    }
  }
};
// 树结构插入数据
const appendInTreeFun = (id, tree, obj) => {
  tree.forEach(ele => {
    if (ele.id === id) {
      //判断名称是否重复  重复+-1
      if (ele.children) {
        obj.checkName = obj.name;
        const newObj = handleName(ele.children, obj);
        // debugger;
        ele.children = [...ele.children, newObj];
      } else {
        ele.children = [obj];
      }
    } else if (ele.children) {
      appendInTreeFun(id, ele.children, obj);
    }
  });
  return tree;
};
const getTHNewChild = (parent, obj, target) => {
  //替换操作获取新的子级
  let children = [...parent.children];
  let newChildren = [];
  children.map(a => {
    if (a.id === target.id) {
      a = { ...a, ...obj };
    }
    newChildren.push(a);
  });
  return newChildren;
};
const getNewTHObj = (obj, oldObj) => {
  let newObj = { ...oldObj, ...obj }; //获取新的对象
  //替换
  newObj.whetherReplace = true;
  newObj.whetherNew = false;
  newObj.oldId = oldObj.id;
  return newObj;
};
const getNewZJObj = (obj, parent) => {
  let newObj = { ...obj }; //获取新的对象
  //追加
  newObj.whetherReplace = false;
  newObj.whetherNew = true;
  newObj.parentId = parent.id;
  return newObj;
};
const appendFun = (targetParent, obj, queryParent, target, tree) => {
  //追加操作中的一种条件
  // targetParent--左侧树选中最外层父级单项  obj--导入项  queryParent--导入项直接父级单项
  let targetList = menuOperator.getChildList(targetParent, []);
  const currentHasSingle = targetList.find(
    a => (a.levelType == 2 && a.id !== targetParent.id) || a.levelType == 3
  ); //查找有无子单项或单位工程
  const currentHasNameSingle = targetList.find(
    a =>
      a.levelType == 2 &&
      a.name === queryParent.name &&
      a.id !== targetParent.id
  ); //查找有无同名子单项
  obj.whetherReplace = false;
  obj.whetherNew = true;
  if (currentHasNameSingle) {
    //有同名单项追加至同名单项下
    let tar = menuOperator.getChildSingle(currentHasNameSingle);
    let newObj = { ...obj };
    newObj.parentId = tar.id;
    appendInTreeFun(tar.id, tree, newObj);
  } else if (currentHasSingle && !currentHasNameSingle) {
    //无同名子单项追加至第一个最里面
    let currentUnitParent;
    if (currentHasSingle.levelType === 3) {
      currentUnitParent = targetList.find(
        a => a.id === currentHasSingle.parentId
      );
      let newObj = { ...obj };
      newObj.parentId = currentUnitParent.id;
      appendInTreeFun(currentUnitParent.id, tree, newObj);
    } else {
      let tar = menuOperator.getChildSingle(currentHasSingle); //最里层单项
      let newObj = { ...obj };
      newObj.parentId = tar.id;
      appendInTreeFun(tar.id, tree, newObj);
    }
  } else if (!currentHasSingle) {
    //当前目标下无子单项--连带导入目标的单项父级一起追加过来
    //替换的话只追加单位
    let newObj = { ...obj };
    newObj.whetherNew = true;
    newObj.whetherReplace = false;
    if (radioAorT.value === '2') {
      newObj.parentId = target.id;
      appendInTreeFun(target.id, tree, newObj);
    } else {
      let newChildrenItem = { ...queryParent };
      newChildrenItem.children = [obj];
      newChildrenItem.whetherNew = true;
      newChildrenItem.whetherReplace = false;
      newChildrenItem.parentId = target.id;
      appendInTreeFun(target.id, tree, newChildrenItem);
    }
  }
};
// 树结构插入数据
const appendNodeInTree = (target, tree, obj) => {
  let id = target.id;
  let targetChildList = menuOperator.getChildList(target, []); //当前选中项的所有子级
  let targetParent =
    currentSelected.parentObj[currentSelected.parentObj.length - 1]; //左侧树选中最外层单项父级
  tree.forEach(async ele => {
    console.info(88888888);
    console.info(obj);
    for (let i in obj) {
      // 获取单项工程数组
      let leftData = ele.children;
      // 是否匹配到单项工程名称相同的数据
      let noEqual = true;
      let currentIsSame = false;
      let currentSingleIsSame = false;
      // 循环左侧树结构
      // 获取从当前点击的数据一直获取到最顶级树结构
      let list = getParentId(importTreeData.value, obj[i].id); //第一项当前选中项，最后一项为最外层子级单项
      let parentObj = list.filter(i => i.levelType !== 1 && i.levelType !== 3); //过滤工程项目级别
      // 单项工程级别对象
      let queryParent = parentObj[0];
      // 如果为合同内单项或者合同外选择替换模式则进行替换操作
      if (obj[i].originalFlag || radioAorT.value === '2') {
        if (
          (!queryParent.originalFlag && target.type === queryParent.type) ||
          queryParent.originalFlag
        ) {
          // 匹配两方单项工程名称是否相同--合同内单项名称相同  合同外标签+名称相同（此外还需要考虑层级）
          // currentIsSame = compareLevel(currentSelected.parentObj, parentObj);
          currentIsSame = compareSameLevel(parentObj, obj[i]);
          //当前选中项与目标项单项工程标签与名称及层级是否一致
          if (!currentIsSame) {
            currentSingleIsSame =
              targetParent.name === parentObj[parentObj.length - 1].name &&
              targetParent.type === parentObj[parentObj.length - 1].type; //当前选中项最外层单项与目标项最外层单项工程标签与名称是否一致
          }
          if (
            currentIsSame ||
            currentSingleIsSame ||
            target.type === queryParent.type
          )
            noEqual = false;
        }
        if (currentIsSame) {
          //当前选中项单项与导入项单项标签名称同层级相同
          //合同外有同名单位替换  无的话追加
          console.log('targetChildList', targetChildList, obj[i]);
          const flag = targetChildList.find(
            a =>
              a.name === obj[i].name &&
              a.levelType === obj[i].levelType &&
              !a.whetherNew &&
              !a.whetherReplace
          );
          if (flag) {
            //找到的话进行替换
            let newObj = getNewTHObj(obj[i], flag);
            let flagParent = currentTreeTile.find(a => a.id === flag.parentId);
            let newChildren = getTHNewChild(flagParent, newObj, flag);
            newChildren.map(a => (a.parentId = target.id));
            replaceChildrenInTreeFun(tree, flagParent.id, newChildren);
            console.log(tree, target.id, newChildren);
          } else {
            //找不到的话进行追加--只追加单位
            let newObj = getNewZJObj(obj[i], target);
            appendInTreeFun(target.id, tree, newObj);
          }
        } else if (!currentIsSame && currentSingleIsSame) {
          //替换需要保证单位名称一致，增加至最子级单项中优先追加至同名子单项中，否则就是最里面子单项
          let targetList = menuOperator.getChildList(targetParent, []);
          const hasSameNameUnit = targetList.find(
            a =>
              a.name === obj[i].name &&
              a.levelType === obj[i].levelType &&
              !a.whetherReplace &&
              !a.whetherNew
          ); //判断是否有同名单位
          if (hasSameNameUnit) {
            //有同名单位执行替换操作
            let parentTar = currentTreeTile.find(
              a => a.id === hasSameNameUnit.parentId
            );
            let newObj = getNewTHObj(obj[i], hasSameNameUnit);
            let newChildren = getTHNewChild(parentTar, newObj, hasSameNameUnit);
            newChildren.map(a => (a.parentId = target.id));
            replaceChildrenInTreeFun(tree, parentTar.id, newChildren);
          } else {
            //无同名单项最子级单项执行追加操作
            //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
            appendFun(targetParent, obj[i], queryParent, target, tree);
          }
        } else if (target.type === queryParent.type) {
          //只有标签相同的话  不管如何均执行追加操作---同上面相同
          //无同名子单项执行追加单位
          //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
          appendFun(targetParent, obj[i], queryParent, target, tree);
        }
        if (noEqual) {
          const brother = impostMoveNodes.value.find(
            a => a.parentId === obj[i].parentId && a.id !== obj[i].id
          ); //有同父级单位已经被移动过去
          const hasMove = brother
            ? currentTreeTile.find(a => a.id === brother.id)
            : null;
          if (hasMove) {
            // 移动新增到brother同级
            const brotherParent = currentTreeTile.find(
              a => a.id === brother.parentId
            );
            let newObj = getNewZJObj(obj[i], brotherParent);
            appendInTreeFun(brotherParent.id, tree, newObj);
          } else {
            let tar = currentTreeTile[0];
            let newChildrenItem = { ...queryParent };
            let newObj = obj[i];
            newObj.whetherNew = true;
            newObj.whetherReplace = false;
            newChildrenItem.children = [newObj];
            newChildrenItem.whetherNew = true;
            newChildrenItem.whetherReplace = false;
            newChildrenItem.parentId = tar.id;
            appendInTreeFun(tar.id, tree, newChildrenItem);
          }
        }
        // 追加操作
      } else {
        //合同外追加
        if (
          (!queryParent.originalFlag && target.type === queryParent.type) ||
          queryParent.originalFlag
        ) {
          // 匹配两方单项工程名称是否相同--合同内单项名称相同  合同外标签+名称相同（此外还需要考虑层级）
          // currentIsSame = compareLevel(currentSelected.parentObj, parentObj);
          currentIsSame = compareSameLevel(parentObj, obj[i]);
          //当前选中项与目标项单项工程标签与名称及层级是否一致
          if (!currentIsSame) {
            console.log(
              'targetParent',
              targetParent,
              parentObj[parentObj.length - 1]
            );
            currentSingleIsSame =
              targetParent.name === parentObj[parentObj.length - 1].name &&
              targetParent.type === parentObj[parentObj.length - 1].type; //当前选中项最外层单项与目标项最外层单项工程标签与名称是否一致
          }
          if (
            currentIsSame ||
            currentSingleIsSame ||
            target.type === queryParent.type
          )
            noEqual = false;
        }
        if (currentIsSame) {
          //当前选中项单项与导入项单项标签名称同层级相同
          //合同外有同名单位替换  无的话追加
          console.log('targetChildList', targetChildList, obj[i]);
          //只追加单位
          let newObj = { ...obj[i] };
          newObj.whetherNew = true;
          newObj.whetherReplace = true;
          newObj.parentId = target.id;
          appendInTreeFun(target.id, tree, newObj);
        } else if (!currentIsSame && currentSingleIsSame) {
          //无同名单项最子级单项执行追加操作
          //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
          appendFun(targetParent, obj[i], queryParent, target, tree);
        } else if (target.type === queryParent.type) {
          //只有标签相同的话  不管如何均执行追加操作---同上面相同
          //无同名子单项执行追加单位
          //当前文件下是否有子级单位  有的话只追加单位工程无得话连带导入文件的单项一起追加
          appendFun(targetParent, obj[i], queryParent, target, tree);
        }
        if (noEqual) {
          const brother = impostMoveNodes.value.find(
            a => a.parentId === obj[i].parentId && a.id !== obj[i].id
          ); //有同父级单位已经被移动过去
          const hasMove = brother
            ? currentTreeTile.find(a => a.id === brother.id)
            : null;
          if (hasMove) {
            // 移动新增到brother同级
            const brotherParent = currentTreeTile.find(
              a => a.id === brother.parentId
            );
            let newObj = getNewZJObj(obj[i], brotherParent);
            appendInTreeFun(brotherParent.id, tree, newObj);
          } else {
            let tar = currentTreeTile[0];
            let newChildrenItem = { ...queryParent };
            let newObj = obj[i];
            newObj.whetherNew = true;
            newObj.whetherReplace = false;
            newChildrenItem.children = [newObj];
            newChildrenItem.whetherNew = true;
            newChildrenItem.whetherReplace = false;
            newChildrenItem.parentId = tar.id;
            appendInTreeFun(tar.id, tree, newChildrenItem);
          }
        }
      }
      currentTreeTile = xeUtils.toTreeArray(tree);
    }
  });
  return tree;
};

// 处理单位工程名称是否有相同如果有则自增一
const handleName = (initList, current) => {
  //initList---同级列表数据   current--增加的名称数据
  function getNameCount(namesMap, name) {
    return (namesMap.get(name) || 0) + 1;
  }

  function setName(item) {
    let NAME = '';
    let count = 0;
    let nameList = [];
    for (let [k, v] of namesMap) {
      nameList.push(k.trim());
    }

    for (let [index, name] of nameList.entries()) {
      let lastName = index > 0 ? `${item.name}_${index + 1}` : `${item.name}`;
      let currentName = index > 0 ? `${item.name}_${index}` : `${item.name}`;
      if (
        !nameList.includes(lastName.trim()) &&
        nameList.includes(currentName.trim())
      ) {
        NAME = lastName.trim();
        namesMap.set(lastName.trim(), 0);
        break;
      } else if (
        nameList.includes(lastName.trim()) &&
        !nameList.includes(currentName.trim())
      ) {
        NAME = currentName.trim();
        namesMap.set(currentName.trim(), 0);
        break;
      }
    }

    if (namesMap.has(item.checkName)) {
      NAME = NAME || `${item.name}_${nameList.length}`;
    } else {
      NAME = item.name;
    }

    return NAME;
  }

  // 初始化一个映射存储每个名称出现的次数
  let namesMap = new Map();

  initList.forEach(item => {
    const count = getNameCount(namesMap, item.name);
    namesMap.set(item.name, count);
  });
  const currentList = [current];
  const renamedItems = currentList.reduce((acc, item, index) => {
    const count = getNameCount(namesMap, item.checkName);

    const newName =
      count > 1 ? `${item.checkName}_${count - 1}` : `${item.checkName}`;

    let newItem = { ...item, name: newName, num: count };
    if (namesMap.has(newName)) {
      const handleName = setName({ ...item });
      newItem = { ...item, name: handleName, num: count };
    }

    namesMap.set(item.checkName, count);
    acc.push(newItem);

    return acc;
  }, []);

  for (let i of renamedItems) {
    const count = getNameCount(namesMap, i.checkName);
    i.num = count;
  }
  return renamedItems[0];
};
// 通过id递归获取父节点
const getParentId = (list, id) => {
  for (let i in list) {
    if (list[i].id == id) {
      return [list[i]];
    }
    if (list[i].children) {
      let node = getParentId(list[i].children, id);
      if (node !== undefined) {
        return node.concat(list[i]);
      }
    }
  }
};
// 树结构删除数据
const removeNodeInTree = async (treeList, id, name, parentId, levelType) => {
  if (!treeList || !treeList.length) {
    return;
  }
  let copyTree = JSON.parse(JSON.stringify(copyTreeData.value));
  const copyTreeTile = xeUtils.toTreeArray(copyTree);
  for (let i = 0; i < treeList.length; i++) {
    if (
      treeList[i].id === id &&
      (treeList[i].whetherNew || treeList[i].whetherReplace)
    ) {
      //只能移除被替换或者追加的元素
      const delId = [id];
      // 如果包含子级，则将子级所有单位工程全部移除
      if (
        levelType === 2 &&
        treeList[i].children &&
        treeList[i].children.length
      ) {
        treeList[i].children.map(i => {
          delId.push(i.id);
        });
      }
      if (levelType === 3) {
        //移除单位父级也是新增且只有这一个单位的话也移除
        let parentIsRemove = currentTreeTile.filter(
          a =>
            a.id === parentId &&
            a.whetherNew &&
            a.children.length === 1 &&
            a.children[0].whetherNew
        ); //父级是否满足移除条件
        delId.push(parentId);
      }
      // 获取右侧树结构
      delListId.value = delId;
      let unitObj = {};
      // 如果是单位工程
      if (treeList[i].whetherNew) {
        unitObj = {};
      } else if (treeList[i].whetherReplace) {
        let obj = copyTreeTile.find(a => a.id === treeList[i].oldId);
        unitObj = { ...obj };
      }
      treeList.splice(i, 1);
      if (Object.keys(unitObj).length !== 0) {
        treeList.splice(i, 0, unitObj);
      }
      break;
    }
    removeNodeInTree(treeList[i].children, id, name, treeList[i].id, levelType);
  }
};
// 处理左侧树删除操作
const handleleftTree = async () => {
  let leftTree = currentTreeData.value;
  let letfChild = leftTree[0].children;
  for (let i in letfChild) {
    let rightNum = 0;
    let unitChild = letfChild[i].children;
    for (let n in unitChild) {
      // 如果是右侧导入过来的单位工程
      if (unitChild[n].whetherNew || unitChild[n].whetherReplace) {
        rightNum++;
      }
    }
    // 如果单项工程下不是全部为右侧导入的单位工程，则单项工程删除从右侧导入进来的属性
    if (rightNum !== unitChild.length) {
      delete letfChild[i].constructMajorType;
      delete letfChild[i].disableCheckbox;
      delete letfChild[i].disabled;
      delete letfChild[i].whetherNew;
      delete letfChild[i].whetherReplace;
    }
  }
};

// 循环，处理树
/*
**type addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
       addNew      添加new
*/
const recursionTree = (treeList, type) => {
  treeList.forEach(node => {
    setValue(node, type);
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        setValue(child, type);
      });
      // addDisable  添加disable， 如果所有的子级勾选了，父级默认给勾选上
      if (
        ['addDisable', 'delDisable'].includes(type) &&
        [2].includes(node.levelType)
      ) {
        let isAllUse = node.children.every(i => {
          return importSelected.historyKeys.includes(i.id);
        });
        // console.info(33333333)
        // console.info(isAllUse)
        // console.info(node)
        switch (type) {
          case 'addDisable':
            if (isAllUse) {
              node.disabled = true;
            } else {
              node.disabled = false;
            }
            break;
          case 'delDisable':
            node.disabled =
              isAllUse && !importSelected.historyKeys.includes(node.id);
            break;
          default:
            break;
        }
      }

      recursionTree(node.children, type);
    }
  });
};

//设置值
const setValue = (data, type) => {
  if (!data.initDisable) {
    // 过滤最原始就不能编辑的值
    switch (type) {
      case 'addDisable':
        data.disableCheckbox = importCheckedKeys.value.includes(data.id);
        data.disabled = importCheckedKeys.value.includes(data.id);
        break;
      case 'addNew':
        data.whetherNew = importCheckedKeys.value.includes(data.id);
        break;
      case 'delDisable':
        if (delParentIdList.value.includes(data.id)) {
          data.disableCheckbox = false;
          data.disabled = false;
        }
        break;
      default:
        break;
    }
  }
};
// 当前项目
const getTreeList = () => {
  expandedKeys.value = [];
  getJsAsideTreeList(route.query.constructSequenceNbr).then(res => {
    if (res.code === 200) {
      const data = xeUtils.toArrayTree(
        Array.isArray(res.result) ? res.result : [res.result],
        {
          children: 'children',
          id: 'id',
          pid: 'parentId',
          sort: 'sort',
        }
      );

      currentTreeData.value = data;
      copyTreeData.value = JSON.parse(JSON.stringify(data));
      currentSelected.initData = data;
      // 获取导入项目
      getImportTreeList();
    }
  });
};

// 导入项目列表
const getImportTreeList = async () => {
  const data = xeUtils.toArrayTree(
    Array.isArray(importYsf.value) ? importYsf.value : [importYsf.value],
    {
      children: 'children',
      id: 'id',
      pid: 'parentId',
      sort: 'sort',
    }
  );
  const list = await flattenTree(toRaw(currentTreeData.value));
  // 判断当前项目有没有单项
  hasSingle.value = list.some(i => {
    return [2].includes(i.levelType);
  });
  handleImportTree(data, hasSingle.value);
  importTreeData.value = data;
  tileTreeList();
  nextTick(() => {
    loading.value = false;
  });
};
// status true， 保存成功
const cancel = (status = false) => {
  dialogVisible.value = false;
  if (!status) {
    csProject.deleteImportProject(route.query.constructSequenceNbr);
  }
  emits('closeDialog', { status });
};

// 点击确定按钮
const handleOk = async () => {
  const status = await handleTip();
  if (status) {
    message.error(status);
    return;
  }
  if (submitLoading.value) return;
  submitLoading.value = true;
  const postData = {
    constructId: route.query.constructSequenceNbr,
    projectStructureTree: JSON.parse(JSON.stringify(currentTreeData.value[0])),
  };
  // debugger;
  console.info(11111111);
  console.info(postData);
  csProject
    .saveJsOutImportProject(postData)
    .then(res => {
      if (res.status === 200) {
        message.success('导入成功');
        setTimeout(() => {
          location.reload();
        }, 1000);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

// 初始化，处理导入项目
const handleImportTree = (tree, hasSingle, parentId = null) => {
  tree.forEach(node => {
    // 空的单项禁用，工程项目禁用, hasSingle,当前项目没有单项的，导入项目单项也禁用
    const DisableStatus =
      [1].includes(node.levelType) ||
      ([2].includes(node.levelType) &&
        (!node.children || !node.children.length || !hasSingle));
    // 测试id不同
    // node.id = node.id + 2 //测试的
    node.whetherNew = false;
    node.disableCheckbox = DisableStatus;
    node.disabled = DisableStatus;
    if (DisableStatus) {
      // 设置最原始的值，默认就是不能编辑
      node.initDisable = true;
    }

    // 判断导入项目有没有单项
    if ([2].includes(node.levelType)) {
      importSingleList.value.push(node.id);
    }

    if (node.children && node.children.length > 0) {
      handleImportTree(node.children, hasSingle, node.id);
    }
  });
};

/**
 *
 * @param {*} ossPath 导入文件的路径
 */
const openDialog = data => {
  if (data) {
    loading.value = true;
    importYsf.value = data;
    dialogVisible.value = true;
    getTreeList();
  }
};

watchEffect(() => {
  if (props.importJsList) {
    openDialog(JSON.parse(JSON.stringify(props.importJsList)));
  }
});
</script>
<style lang="scss" scoped>
.del-icon {
  font-size: 17px;
}

.mIcon {
  margin-right: 10px;
}

.tree-content-wrap {
  width: 70vw;
  height: 100%;
  max-width: 800px;
  min-width: 700px;
  display: flex;
  flex-direction: column;
}

.tree-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  height: 50vh;
  width: 43%;
  overflow: hidden;

  &:hover {
    overflow: auto;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 7px 13px;
    background-color: #eaeaea;

    span {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }

    .checkhandle {
      display: flex;
      align-items: center;
    }
  }

  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    ::v-deep .ant-tree {
      background-color: #fafafa;

      .ant-tree-switcher-noop {
        opacity: 0;
      }

      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 12px 0 30px;
  padding: 9px 18px;
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  border-radius: 2px;
}

.footer-btn-list {
  margin-top: 30px;
}
</style>
