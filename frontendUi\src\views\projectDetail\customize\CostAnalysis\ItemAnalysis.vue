<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-21 17:40:32
-->
<template>
  <vxe-table
    border
    align="center"
    header-align="center"
    auto-resize
    height="auto"
    :column-config="{ resizable: true }"
    :row-config="{ isHover: true, keyField: 'sequenceNbr' }"
    :tree-config="{
      rowField: 'dispNo',
      children: 'childrenList',
      expandAll: false,
      reserve: true,
    }"
    :data="tableData"
    :menu-config="menuConfig"
    @menu-click="contextMenuClickEvent"
    ref="itemTable"
    keep-source
    @edit-closed="editClosedEvent"
    class="table-edit-common"
    @cell-click="
      cellData => {
        useCellClickEvent(cellData);
      }
    "
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      beforeEditMethod({ rowIndex }) {
        if (rowIndex === tableData.length - 1) {
          //最后一行不可编辑
          return false;
        }
        return true;
      },
    }"
    :cell-class-name="selectedClassName"
    :row-style="rowStyle"
    :header-cell-class-name="setHeaderCellClassName"
  >
    <template v-for="columns of showColumns">
      <vxe-column
        v-if="columns.children.length === 0"
        v-bind="columns"
        :width="columns.width"
        :align="columns.align"
      >
        <template
          v-if="columns.field !== 'dispNo'"
          #header="{ column }"
        >
          <span class="custom-header-close">
            <span>{{ column.title }}</span>
            <CloseOutlined
              class="icon-close"
              @click="closeColumn({ column })"
            />
          </span>
        </template>
        <template #default="{ column, row, $columnIndex }">
          <template v-if="
              columns.field !== 'dispNo' &&
              !['totalLast', 'AWF'].includes(row?.typeInfo)
            ">
            {{ row[column.field] || 0 }}
          </template>
          <template v-else>{{ row[column.field] }}</template>
        </template>
        <template #edit="{ column, row, $columnIndex }">
          <template v-if="columns.slot && column.field == 'average'">
            <vxe-input
              :clearable="false"
              v-model="row.average"
              @blur="(row.average = pureNumber(row.average, 2)), clear()"
              :disabled="row.projectName.indexOf('合计') !== -1 ? true : false"
            ></vxe-input>
          </template>
          <template v-else>
            {{ row[column.field] }}
          </template>
        </template>
      </vxe-column>
      <vxe-colgroup
        v-else
        v-bind="columns"
        width="100"
        align="right"
      >
        <template #header="{ column }">
          <span class="custom-header-close">
            <span>{{ column.title }}</span>
            <CloseOutlined
              class="icon-close"
              @click="closeColumn({ column })"
            />
          </span>
        </template>
        <vxe-column
          v-for="columnsChild of columns.children"
          v-bind="columnsChild"
          width="100"
          align="right"
        >
          <template #header="{ column }">
            <span class="custom-header-close">
              <span>{{ column.title }}</span>
              <CloseOutlined
                class="icon-close"
                @click="closeColumn({ column, columns })"
              />
            </span>
          </template>
        </vxe-column>
      </vxe-colgroup>
    </template>
  </vxe-table>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
</template>
<script setup>
import { watch, ref, onMounted, reactive } from 'vue';
import { Modal } from 'ant-design-vue';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { CloseOutlined } from '@ant-design/icons-vue';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: 'dispNo' });
import { tableColumns } from './columns.js';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import xeUtils from 'xe-utils';
import { pureNumber } from '@/utils/index';
const store = projectDetailStore();
const itemTable = ref();
let tableData = ref([]);
let getData = [];
let averageRow = ref();
let average = ref();
let taxMode = ref(); //1-一般计税，0-简易计税
let flag = ref(false); //修改单项建筑面积是否联动修改单位建筑面积
const {
  handlerColumns,
  showColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'ys',
  initCallback: () => {},
  initColumnsCallback: () => {
    initColumns({
      columns: tableColumns.value,
      pageName: 'zjfx',
    });
  },
});
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'pageSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, column, columnIndex, row, rowIndex);
    return true;
  },
});
// 点击右击菜单
const contextMenuClickEvent = ({ menu, row }) => {
  // 如果点击页面显示列设置
  if (menu.code === 'pageSetting') {
    showPageColumnSetting();
  }
};
const getTableData = () => {
  taxMode.value = Number(store.taxMade);
  getData = [];
  let apiData = {
    levelType: store.currentTreeInfo?.levelType,
  };
  apiData.constructId = store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo?.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id;
  }
  if (!apiData.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  // debugger;
  apiData.levelType &&
    feePro.getCostAnalysisData(apiData).then(res => {
      if (res.status === 200) {
        console.log('获取造价分析接口获取到的数据', res);
        if (res.result.costAnalysisConstructVOList) {
          res.result.costAnalysisConstructVOList.map(i => {
            if (
              i.projectName.indexOf('安全生产、文明施工费') !== -1 &&
              !i.children
            )
              i.typeInfo = 'AWF';
          });
        }
        if (store.currentTreeInfo?.levelType === 1) {
          getData = res.result.costAnalysisConstructVOList;
          // gczj(getData); //自己实验
        } else if (store.currentTreeInfo?.levelType === 2) {
          getData = res.result.costAnalysisSingleVOList;
          //自己实验
          // getData.gczj = '8888';
          // gczj(getData.childrenList);
        }
        let data = getTotal(getData);
        getDsposeData(data);
        // tableData.value = data;
        console.log('获取造价分析返回数据', apiData, tableData.value);
      }
    });
};
const gczj = data => {
  data.forEach(item => {
    item.gczj = '8888';
    item.childrenList ? gczj(item.childrenList) : '';
  });
};
const getDsposeData = data => {
  if (!data) {
    data = [];
  }
  console.log(xeUtils.toTreeArray([store.currentTreeInfo], { key: 'id' }));
  let asideTreeList = xeUtils.toTreeArray([store.currentTreeInfo], {
    key: 'id',
  });
  // debugger;
  data &&
    data.map(item => {
      item.average =
        Number(item.average) >= 0 ? Number(item.average).toFixed(2) : '0.00';
      item.averageOld = item.average;
      item.unitcost =
        Number(item.unitcost) > 0 ? Number(item.unitcost).toFixed(2) : '0.00';
      if (
        item.projectName === '合计' &&
        store.currentTreeInfo?.levelType === 2
      ) {
        item.average = '/';
        item.unitcost = '/';
      }
      if (Number(store.deType) === 22) {
        if (
          item.levelType === 3 &&
          !['AWF', 'totalLast'].includes(item.typeInfo)
        ) {
          let tar = asideTreeList.find(
            a => a.id === item.sequenceNbr && a.levelType === 3
          );
          //此处应该是22单位行
          item.gfee = tar.deStandardReleaseYear === '22' ? '/' : item.gfee;
        } else if (item.levelType === 2) {
          let tar = asideTreeList.find(
            a => a.id === item.sequenceNbr && a.levelType === 2
          );
          let has12 = tar.children?.some(a => a.deStandardReleaseYear === '12');
          if (!has12) item.gfee = '/';
        }
      }
      if (item.childrenList && item.childrenList.length === 0) return;
      item.childrenList && item.childrenList.length > 0
        ? getDsposeData(item.childrenList)
        : '';
    });
  tableData.value = data;
};
const getEveryUnitCost = data => {
  data.map(item => {
    item.average =
      Number(item.average) >= 0 ? Number(item.average).toFixed(2) : '0.00';
    item.averageOld = item.average;
    item.unitcost =
      Number(item.average) > 0
        ? Number(item.gczj / item.average).toFixed(2)
        : '0.00';
    if (item.projectName === '合计' && store.currentTreeInfo?.levelType === 2) {
      item.average = '/';
      item.unitcost = '/';
    }
    if (item.childrenList && item.childrenList.length === 0) return;
    item.childrenList && item.childrenList.length > 0
      ? getEveryUnitCost(item.childrenList)
      : '';
  });
  tableData.value = data;
};

const getTotal = list => {
  //合计数据行
  let totalLast = {
    average: 0,
    unitcost: 0,
    gczj: 0,
    fbfxhj: 0,
    fbfxrgf: 0,
    fbfxclf: 0,
    fbfxjxf: 0,
    fbfxzcf: 0,
    fbfxglf: 0,
    fbfxlr: 0,
    fbfxzgj: 0,
    djcsxhj: 0,
    djcsxrgf: 0,
    djcsxclf: 0,
    djcsxjxf: 0,
    djcsxzcf: 0,
    djcsxglf: 0,
    djcsxlr: 0,
    zjcsxhj: 0,
    zjcsxrgf: 0,
    zjcsxclf: 0,
    zjcsxjxf: 0,
    zjcsxzcf: 0,
    zjcsxglf: 0,
    zjcsxlr: 0,
    qtxmzlje: 0,
    qtxmzygczgj: 0,
    qtxmzcbfwf: 0,
    qtxmjrg: 0,
    csxhj: 0,
    qtxmhj: 0,
    gfee: 0,
    safeFee: 0,
    jxse: 0,
    xxse: 0,
    zzsynse: 0,
    fjse: 0,
    sj: 0,
    sbfsj: 0,
  };
  if (!list) return;
  if (store.currentTreeInfo?.levelType === 2) {
    for (let key in totalLast) {
      if (key !== 'average' && key !== 'unitcost') totalLast[key] = list[key];
      totalLast[key] = Number(totalLast[key]).toFixed(2);
    }
    totalLast.average = '/';
    totalLast.unitcost = '/';
  } else if (store.currentTreeInfo?.levelType === 1) {
    for (let key in totalLast) {
      list.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(totalLast, key)) {
          totalLast[key] =
            item[key] && Number(item[key])
              ? floatAdd(totalLast[key], item[key])
              : totalLast[key];
          totalLast[key] = Number(totalLast[key]).toFixed(2);
        }
      });
    }
  }
  //此处处理13867bug---工程项目级别合计需要计算含设备费及其税金
  totalLast.projectName =
    store.currentTreeInfo?.levelType === 2 ? '合计' : '合计（含设备费及其税金)';
  totalLast.typeInfo = 'totalLast';
  totalLast.dispNo = '';
  if (store.currentTreeInfo?.levelType === 1) {
    let computedSum = {
      fbfxhj: totalLast.fbfxhj,
      csxhj: totalLast.csxhj,
      qtxmhj: totalLast.qtxmhj,
      gfee: totalLast.gfee || 0,
      safeFee: totalLast.safeFee,
      sj: totalLast.sj,
      sbfsj: totalLast.sbfsj,
    };
    totalLast.gczj = Object.values(computedSum)
      .reduce((sum, value) => Number(sum) + Number(value), 0)
      .toFixed(2);
    // totalLast.gczj = (
    //   Number(totalLast.gczj) +
    //   Number(totalLast.sbfsj) +
    //   Number(totalLast.safeFee)
    // ).toFixed(2);
  }
  if (list instanceof Array) {
    return [...list, totalLast];
  } else {
    return [list, totalLast];
  }
};
const floatAdd = (arg1, arg2) => {
  var r1, r2, m;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
};
watch(
  () => store.tabSelectName,
  async () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.levelType !== 3
    ) {
      await initColumns({
        columns: tableColumns.value,
        pageName: 'zjfx',
      });
      // debugger;
      getTableData();
    }
  }
);
watch(
  () => store.currentTreeInfo,
  async () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.levelType !== 3
    ) {
      await initColumns({
        columns: tableColumns.value,
        pageName: 'zjfx',
      });
      getTableData();
    }
  }
);
onMounted(async () => {
  if (
    store.tabSelectName === '造价分析' &&
    store.currentTreeInfo?.levelType !== 3
  ) {
    await initColumns({
      columns: tableColumns.value,
      pageName: 'zjfx',
    });
    getTableData();
  }
});
const clear = () => {
  //清除编辑状态
  const $table = itemTable.value;
  $table.clearEdit();
};
let parent = reactive(null);
const findParent = (list, tar = null) => {
  list.some(i => {
    if (i.sequenceNbr === averageRow.value.sequenceNbr) {
      parent = tar;
      return true;
    } else if (!parent && i.sequenceNbr !== averageRow.value.sequenceNbr) {
      i.childrenList ? findParent(i.childrenList, i) : '';
    }
    return true;
  });
};
const upDateAverage = () => {
  // getDsposeData(tableData.value);
  // debugger;
  const $table = itemTable.value;
  let apiData = {
    levelType: averageRow.value.levelType,
    average: average.value ? Number(average.value) : 0.0,
    // unitcost: averageRow.value.unitcost ? averageRow.value.unitcost : 0,
    constructId: store.currentTreeGroupInfo?.constructId,
    flag: flag.value,
  };
  // if (store.currentTreeInfo?.levelType === 1) {
  if (averageRow.value.levelType === 2) {
    apiData.singleId = averageRow.value.sequenceNbr;
  } else if (averageRow.value.levelType === 3) {
    findParent(tableData.value);
    apiData.singleId = parent?.sequenceNbr;
    apiData.unitId = averageRow.value.sequenceNbr;
    console.log('修改成功造价分析', apiData, parent);
  }
  feePro.updateCostAnalysis(apiData).then(res => {
    if (res.status === 200) {
      console.log('修改成功造价分析', apiData, res, averageRow.value);
      averageRow.value.averageOld = averageRow.value.average;
      getTableData();
    }
  });
};
let expandedList = ref();
const getExpandList = list => {
  list.map(item => {
    expandedList.value.push(item);
    item.childrenList ? getExpandList(item.childrenList) : '';
  });
};
const editClosedEvent = ({ row, column }) => {
  const $table = itemTable.value;
  const field = column.field;
  averageRow.value = row;
  average.value = row[field];
  if (!row[field]) {
    row[field] = 0;
    average.value = 0;
  }
  if (Number(row.averageOld) === Number(row.average)) {
    row.average = Number(row.average).toFixed(2);
  }
  // 判断单元格值是否被修改
  if (
    $table.isUpdateByRow(row, field) &&
    Number(row.averageOld) !== Number(row.average)
  ) {
    flag.value = false;
    const hasChildren =
      row.childrenList && row.childrenList.length > 0 ? true : false;

    averageRow.value.levelType === 2 && hasChildren
      ? Modal.confirm({
          title: '是否联动修改其下所有项目层级建筑面积数据？',
          zIndex: '99999',
          onOk() {
            flag.value = true;
            expandedList.value = [row];
            getExpandList(row.childrenList);
            itemTable.value.setTreeExpand(expandedList.value, true);
            // itemTable.value.setTreeExpand(row, true);
            upDateAverage();
          },
          onCancel() {
            upDateAverage();
          },
        })
      : upDateAverage();
  }
};
const rowStyle = ({ row }) => {
  console.log(row);
  return {
    backgroundColor: '#fff7f2',
  };
};
defineExpose({ getTableData });
</script>
<style lang="scss" scoped>
.table-content {
  max-width: 95%;
  // width: calc(100% - 250px);
  max-height: calc(100% - 40px);
}
::v-deep .vxe-header--column {
  .custom-header-close {
    .icon-close {
      position: absolute;
      top: 15px;
      transform: translateY(-50%);
      right: 3px;
      display: none;
      background-color: #ebebeb;
      // display: block;
    }
  }
  &:hover .icon-close {
    display: block;
  }
}
::v-deep(.vxe-body--column.col--actived) {
  box-shadow: inset -1px -1px 0px 0px #b9b9b9;
  background: #fff !important;
}
</style>
