<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2024-03-04 16:11:58
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-02 17:36:38
-->
<template>
  <div class="contentbox">
    <div v-show="activeKey === 3">
      <!-- {{ otherActive }} -->
      <menuList v-model:otherActive="otherActive" />
    </div>
    <splitModel
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      :style="{ width: activeKey === 3 ? 'calc(100% - 140px)' : '100%' }"
      mode="vertical"
    >
      <template #one>
        <component
          @getRow="getShRow"
          :is="comId"
          :otherActive="otherActive"
          :key="activeKey"
          :sdList="sdList"
        ></component>
      </template>
      <template #two>
        <!--         {{ activeKey }} -->
        <matching
          @getShRow="getSsRow"
          :ssList="ssList"
          :otherActive="otherActive"
        />
      </template>
    </splitModel>
    <prompted
      v-model:visible="isShowModel"
      title="系统提示"
      :descTextLeft="shRow.kind === '03' ? '清单匹配：' : '定额匹配：'"
      descTextRight="当前审定已经匹配，是否要取消原匹配重新执行？"
      :isCancel="true"
      @determine="pushShChangeDetailAll"
      @cancel="isShowModel = false"
    ></prompted>
  </div>
</template>
<script setup>
import {
  ref,
  onMounted,
  markRaw,
  shallowRef,
  inject,
  watch,
  nextTick,
} from 'vue';
import splitModel from '@/components/split/index.vue';
import subItems from './table/subitems.vue';
import measureItems from './table/measureItems.vue';
import machine from './table/machine.vue';
import ortherItems from './table/ortherItems.vue';
import cost from './table/cost.vue';
import matching from './table/matching.vue';
import menuList from './menu.vue';
import prompted from '@/components/SelfModel/prompted.vue';
import feePro from '@/api/feePro';
import shApi from '@/api/shApi';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import budgetReviewApi from '@/api/budgetReview.js';
const projectStore = projectDetailStore();
const comId = shallowRef(subItems);
const activeKey = inject('activeKey');
const otherActive = ref(3);
const ssList = ref([]);
const sdList = ref([]);
const isShowModel = ref(false);
const comList = {
  table: [
    {
      activeKey: 1,
      component: markRaw(subItems),
    },
    {
      activeKey: 2,
      component: markRaw(measureItems),
    },
    {
      activeKey: 3,
      component: markRaw(ortherItems),
    },
    {
      activeKey: 4,
      component: markRaw(machine),
    },
    {
      activeKey: 5,
      component: markRaw(cost),
    },
  ],
};
const mateList = {};
const props = defineProps({
  isInner: {
    type: Boolean,
    default: false,
  },
  innerConstructId: {
    type: String,
    default: '',
  },
});
watch(
  () => activeKey.value,
  val => {
    console.log(val, 'activeKey');
    comId.value = comList.table.find(a => a.activeKey === val)?.component;
    if (val !== 3) otherActive.value = 3;
    init();
  }
);
watch(
  () => otherActive.value,
  val => {
    init();
  }
);
watch(
  () => projectStore.currentTreeInfo,
  () => {
    console.log(projectStore, '222222222树点击更新！！！！！！！！！！！！！');
    if (projectStore.currentTreeInfo) {
      init();
    }
  }
);
onMounted(() => {
  comId.value = comList.table.find(
    a => a.activeKey === activeKey.value
  )?.component;
  init();
});
const init = () => {
  if (activeKey.value !== 3) {
    getMatchingList();
    getShQueryDetail();
  } else {
    getMatchingList(1);
    getShQueryDetail(1);
  }
  ssRow = {};
  shRow = {};
};
const getShQueryDetail = (type = null) => {
  console.log(projectStore.currentTreeInfo, 'projectStore.currentTreeInfo');
  if (type) {
    if (activeKey.value === 3) {
      getOtherProjectList();
    }
  } else {
    if (activeKey.value === 1) {
      fbfxDataPiPeiColl();
    } else if (activeKey.value === 2) {
      csxmListSearch();
    } else if (activeKey.value === 4) {
      ysshRcjCollectComparison();
    } else if (activeKey.value === 5) {
      getUnitCostSummary();
    }
  }
};

// 分部分项审定数据
const fbfxDataPiPeiColl = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: null,
    pageNum: 1,
    pageSize: 300000,
    isAllFlag: true,
  };
  console.log('apiData分部分项审定数据', apiData);
  shApi.fbfxDataPiPeiColl(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200 && res.result) {
      sdList.value = res.result.data;
    }
  });
};

const csxmListSearch = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: null,
    pageNum: 1,
    pageSize: 300000,
    isAllFlag: true,
  };
  console.log('apiData措施项目审定数据', apiData);
  shApi.csxmListSearch(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200 && res.result) {
      sdList.value = res.result.data;
    }
  });
};

const getOtherAllProjectList = async () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  let resData;
  console.log('activeKey', otherActive.value);
  switch (otherActive.value) {
    case 3:
      resData = await shApi.getOtherProjectList(apiData);
      break;
    case 31:
      resData = await shApi.getOtherProjectZljeList(apiData);
      break;
    case 32:
      resData = await shApi.getOtherProjectZygcZgjList(apiData);
      break;
    case 33:
      resData = await shApi.getOtherProjectZcbfwfList(apiData);
      break;
    case 34:
      resData = await shApi.getOtherProjectJrgList(apiData);
      break;
  }
  return resData;
};
// 其他项目审定数据
const getOtherProjectList = async () => {
  let resData = await getOtherAllProjectList(); //获取子页面表格数据
  if (resData.status === 200) {
    sdList.value = resData.result;
    console.log('********getOtherProjectList', resData.result);
  }
};

const ysshRcjCollectComparison = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  shApi.ysshRcjCollectComparison(apiData).then(res => {
    if (res.status === 200) {
      sdList.value = res.result;
      console.log('********人材机汇总', res.result);
    }
  });
};

// 费用汇总审定数据
const getUnitCostSummary = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  shApi.getUnitCostSummary(apiData).then(res => {
    console.log('费用汇总', res);
    if (res.status === 200 && res.result) {
      sdList.value = res.result;
    }
  });
};

const getMatchingList = type => {
  let apiData = {
    type: type ? otherActive.value : activeKey.value,
    ysshConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ysshSpId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ysshUnitId: projectStore.currentTreeInfo?.ysshUnitId,
  };
  console.log('getMatchingList', apiData);
  budgetReviewApi.shQuerySSDetail(apiData).then(res => {
    console.log(res, '送审数据');
    ssList.value = res.code === 500 ? [] : res.result;
  });
};
let ssRow = {};
let shRow = {};
const pushShChangeDetail = type => {
  let apiData = {
    matchingId: ssRow.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
  };
  console.log(apiData, 'pushShChangeDetailapiData');
  budgetReviewApi.changeFbfxGLGuanXi(apiData).then(res => {
    console.log(res, 'resresresres');
    if (res.code === 200) {
      message.success('匹配成功');
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const changeMeasureRelation = () => {
  let apiData = {
    matchingId: ssRow.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
  };
  console.log(apiData, 'changeMeasureRelation');
  shApi.changeMeasureRelation(apiData).then(res => {
    console.log(res, 'resresresres');
    if (res.code === 200) {
      message.success('匹配成功');
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const changeCostSummaryRelation = () => {
  let apiData = {
    matchingId: ssRow.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
  };
  console.log(apiData, 'changeCostSummaryRelation');
  shApi.changeCostSummaryRelation(apiData).then(res => {
    console.log(res, 'resresresres');
    if (res.code === 200) {
      message.success('匹配成功');
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const updateMatch = bizType => {
  let apiData = {
    ssSequenceNbr: ssRow.sequenceNbr,
    sequenceNbr: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bizType: bizType,
  };
  shApi.updateMatch(apiData).then(res => {
    if (res.code === 200) {
      message.success('匹配成功');
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const unitRcjChangeGL = () => {
  let apiData = {
    ssMaterialCode: ssRow.materialCode,
    materialCode: shRow.materialCode,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log(apiData, 'unitRcjChangeGL');
  shApi.unitRcjChangeGL(apiData).then(res => {
    console.log(res, 'resresresres');
    if (res.code === 200) {
      message.success('匹配成功');
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
const getShRow = row => {
  shRow = row;
};
const getSsRow = row => {
  ssRow = row;
  console.log('ssRow', 'sHRow', ssRow, shRow, shRow.sequenceNbr);
  if (!shRow.sequenceNbr) return;
  if (activeKey.value === 3 && otherActive.value === 3) return;
  console.log('77777777777777777777', shRow?.kind, ssRow?.kind, shRow?.rcjFlag, ssRow?.rcjFlag)
  if (shRow?.kind !== ssRow?.kind || shRow?.rcjFlag !== ssRow?.rcjFlag) return;
  if (activeKey.value === 1 || activeKey.value === 2) {
    if (shRow.kind === '03' || shRow.kind === '04') {
      if (shRow.ysshSysj?.change === 2) {
        return message.error('审删项不可匹配');
      } else if (shRow.ysshSysj?.change !== 1) {
        if (shRow.kind !== shRow.kind) return;
        if (ssRow.sequenceNbr === shRow.ysshSysj?.sequenceNbr) {
          return;
        } else {
          return (isShowModel.value = true);
        }
      }
    } else {
      return message.error('只可选择清单定额项');
    }
    if (ssRow.kind !== shRow.kind) return message.error('不可跨层级匹配');
  } else {
    if (shRow.ysshSysj?.change === 2) {
      return message.error('审删项不可匹配');
    } else if (shRow.ysshSysj?.change !== 1) {
      if (ssRow.sequenceNbr === shRow.ysshSysj?.sequenceNbr) {
        return;
      } else {
        return (isShowModel.value = true);
      }
    }
  }

  if (shRow.hasOwnProperty('sequenceNbr')) pushShChangeDetailAll();
};
const pushShChangeDetailAll = () => {
  if (activeKey.value === 5) {
    changeCostSummaryRelation();
  } else if (activeKey.value === 3) {
    let bizType;
    switch (otherActive.value) {
      case 31:
        bizType = 5;
        break;
      case 32:
        bizType = 6;
        break;
      case 33:
        bizType = 7;
        break;
      case 34:
        bizType = 8;
        break;
    }
    updateMatch(bizType);
  } else if (activeKey.value === 4) {
    unitRcjChangeGL();
  } else if (activeKey.value === 2) {
    changeMeasureRelation();
  } else {
    pushShChangeDetail();
  }
};
</script>
<style lang="scss" scoped>
.contentbox {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: row;
}
</style>
