<!--
 * @Descripttion: 人材机汇总
-->
<template>
  <div class="table-content" id="humanTable">
    <vxe-table
      align="center"
      ref="humanTable"
      :loading="loading"
      height="auto"
      :menu-config="menuConfig"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
      }"
      :data="tableData"
      :cell-style="
        projectStore.currentTreeInfo.levelType === 3
          ? cellStyle
          : cellTableStyle
      "
      :row-style="rowStyle"
      @edit-closed="editClosedEvent"
      keep-source
      @menu-click="contextMenuClickEvent"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData, null, ['ifDonorMaterial']);
        }
      "
      class="table-edit-common"
      :cell-class-name="selectedClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :scroll-y="{ enabled: true, gt: 30 }"
      @current-change="currentChange"
      :export-config="{}"
      show-overflow
    >
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="dispNo" width="50" title="序号"></vxe-column>
      <vxe-column
        field="materialCode"
        width="120"
        title="材料编码"
      ></vxe-column>
      <vxe-column
        field="type"
        width="100"
        title="类型"
        :edit-render="{
          enabled: projectStore.currentTreeInfo.levelType === 1 ? false : true,
        }"
      >
        <template #default="{ row }">
          <span>{{ row.type }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select
            v-model="row.type"
            :clearable="false"
            transfer
            v-if="
              (row.type === '主材费' ||
                row.type === '材料费' ||
                row.type === '设备费') &&
              !(row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) &&
              Number(row.edit) !== 1 &&
              !isChangeAva(row)
            "
          >
            <vxe-option
              v-for="item in selectOptions"
              :key="item.type"
              :value="item.type"
              :label="item.type"
            ></vxe-option>
          </vxe-select>
          <span v-else>{{ row.type }} </span>
        </template>
      </vxe-column>
      <vxe-column
        field="materialName"
        width="200"
        title="名称"
        :edit-render="{
          enabled: projectStore.currentTreeInfo.levelType === 1 ? false : true,
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #default="{ row }">
          <span>{{ row.materialName }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-if="Number(row.edit) !== 1"
            :clearable="false"
            v-model.trim="row.materialName"
            type="text"
            @blur="clear()"
          ></vxe-input>
          <span v-else>{{ row.materialName }}</span>
        </template></vxe-column
      >
      <vxe-column
        field="specification"
        width="100"
        title="规格型号"
        :edit-render="{
          enabled: projectStore.currentTreeInfo.levelType === 1 ? false : true,
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #default="{ row }">
          <span>{{ row.specification }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.specification"
            type="text"
            @blur="clear()"
            v-if="Number(row.edit) !== 1"
          ></vxe-input>
          <span v-else>{{ row.specification }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="unit"
        width="100"
        title="单位"
        :edit-render="{
          enabled: projectStore.currentTreeInfo.levelType === 1 ? false : true,
          autofocus: '.vxe-input--inner',
        }"
      >
        <template #default="{ row }">
          <span>{{ row.unit }}</span>
        </template>
        <template #edit="{ row }">
          <vxeTableEditSelect
            v-if="Number(row.edit) !== 1"
            :filedValue="row.unit"
            :list="projectStore.unitListString"
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'unit', $rowIndex);
              }
            "
          ></vxeTableEditSelect>
          <span v-else>{{ row.unit }}</span>
        </template>
      </vxe-column>
      <vxe-column field="totalNumber" width="100" title="数量"></vxe-column>
      <!-- showDePrice市场价字段更换为dePrice -->
      <vxe-column
        field="dePrice"
        width="110"
        :title="
          projectStore.deType === '12'
            ? '定额价'
            : projectStore.taxMade === 1
            ? '不含税基期价'
            : '含税基期价'
        "
      >
        <template #default="{ row, column }">
          <span>{{ isChangeAva(row) ? '-' : row.dePrice }}</span>
        </template>
      </vxe-column>
      <!-- showMarketPrice市场价字段更换为marketPrice -->
      <vxe-column
        field="marketPrice"
        width="110"
        :title="
          projectStore.deType === '12'
            ? '市场价'
            : projectStore.taxMade === 1
            ? '不含税市场价'
            : '含税市场价'
        "
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row, column }">
          <span>{{ isChangeAva(row) ? '-' : row.marketPrice }}</span>
        </template>
        <template #edit="{ row, column }">
          <vxe-input
            v-if="
              row.ifLockStandardPrice !== 1 &&
              isPartEdit &&
              !(row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) &&
              Number(row.edit) !== 1 &&
              !isChangeAva(row)
            "
            :clearable="false"
            v-model.trim="row.marketPrice"
            type="text"
            @blur="
              row.marketPrice = pureNumber(row.marketPrice, 2);
              clear();
            "
          ></vxe-input>
          <span v-else>{{ isChangeAva(row) ? '-' : row.marketPrice }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="total"
        width="110"
        :title="
          projectStore.deType === '12'
            ? '市场价合计'
            : projectStore.taxMade === 1
            ? '不含税市场价合计'
            : '含税市场价合计'
        "
      >
        <template #default="{ row, column }">
          <span>{{ isChangeAva(row) ? '-' : row.total }}</span>
        </template>
      </vxe-column>
      <vxe-column field="sourcePrice" width="150" title="价格来源"></vxe-column>
      <vxe-column
        field="markSum"
        width="130"
        title="是否汇总(二次分析)"
        :cell-render="{}"
        :export-method="
          ({ row }) => {
            return row.markSum && [1, 2].includes(Number(row.levelMark))
              ? '是'
              : '';
          }
        "
      >
        <template #default="{ row }">
          <vxe-checkbox
            v-model="row.markSum"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="CheckboxChange(row, 'markSum')"
            v-if="[1, 2].includes(Number(row.levelMark))"
          ></vxe-checkbox>
        </template>
      </vxe-column>
      <vxe-column
        field="taxRemoval"
        width="100"
        title="除税系数(%)"
        v-if="
          projectStore.deType !== '22' && Number(projectStore.taxMade) !== 0
        "
      ></vxe-column>
      <vxe-column
        field="jxTotal"
        width="100"
        title="进项税额"
        v-if="
          projectStore.deType !== '22' && Number(projectStore.taxMade) !== 0
        "
      ></vxe-column>
      <vxe-column field="priceDifferenc" width="100" title="价差"></vxe-column>
      <vxe-column
        field="priceDifferencSum"
        width="100"
        title="价差合计"
      ></vxe-column>
      <vxe-column
        field="ifDonorMaterial"
        width="100"
        title="供货方式"
        :fixed="!projectStore.standardGroupOpenInfo.isOpen ? 'right' : ''"
        :edit-render="{
          enabled:
            projectStore.currentTreeInfo.levelType === 1 ||
            projectStore.standardGroupOpenInfo.isOpen
              ? false
              : true,
        }"
        :export-method="
          ({ row }) => {
            return getDonorMaterialText(row.ifDonorMaterial);
          }
        "
      >
        <template #default="{ row }">
          {{ getDonorMaterialText(row.ifDonorMaterial) }}
        </template>
        <template #edit="{ row }">
          <vxe-select
            v-if="row.checkIsShow"
            v-model="row.ifDonorMaterial"
            @change="CheckboxChange(row, 'ifDonorMaterial')"
            transfer
          >
            <vxe-option
              v-for="item in donorMaterialList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
          <span v-else>{{ getDonorMaterialText(row.ifDonorMaterial) }}</span>
        </template></vxe-column
      >
      <vxe-column
        field="donorMaterialNumber"
        width="100"
        title="甲供数量"
        :edit-render="{
          enabled:
            projectStore.currentTreeInfo.levelType === 1 ||
            projectStore.standardGroupOpenInfo.isOpen
              ? false
              : true,
          autofocus: '.vxe-input--inner',
        }"
        :class-name="
          ({ row }) => {
            return row.donorMaterialNumber > row.totalNumber
              ? 'background-red'
              : '';
          }
        "
      >
        <template #default="{ row }">
          <span v-if="row.checkIsShow">{{ row.donorMaterialNumber }}</span>
          <span v-else></span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-if="row.checkIsShow"
            :clearable="false"
            v-model.trim="row.donorMaterialNumber"
            type="text"
            @blur="
              (row.donorMaterialNumber = row.donorMaterialNumber * 1 + ''),
                clear()
            "
            @keyup="
              row.donorMaterialNumber = row.donorMaterialNumber.replace(
                /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                '$1$2.$3'
              )
            "
          ></vxe-input>
        </template>
      </vxe-column>

      <vxe-column
        field="ifProvisionalEstimate"
        width="100"
        title="是否暂估"
        fixed="right"
        :export-method="
          ({ row }) => {
            return row.ifProvisionalEstimate && row.checkIsShow ? '是' : '';
          }
        "
      >
        <template #default="{ row }">
          <vxe-checkbox
            v-model="row.ifProvisionalEstimate"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            :disabled="isChangeAva(row)"
            @change="CheckboxChange(row, 'ifProvisionalEstimate')"
            v-if="row.checkIsShow"
          ></vxe-checkbox>
        </template>
      </vxe-column>
      <vxe-column
        field="ifLockStandardPrice"
        width="100"
        title="市场价锁定"
        fixed="right"
        :export-method="
          ({ row }) => {
            return row.ifLockStandardPrice && row.checkIsShow ? '是' : '';
          }
        "
      >
        <template #default="{ row }">
          <vxe-checkbox
            v-model="row.ifLockStandardPrice"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="CheckboxChange(row, 'ifLockStandardPrice')"
            v-if="row.checkIsShow"
            :disabled="Number(row.edit) === 1"
          ></vxe-checkbox>
        </template>
      </vxe-column>
      <vxe-column
        v-if="projectStore.currentTreeInfo.levelType === 3"
        field="producer"
        width="100"
        title="产地"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.producer"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        v-if="projectStore.currentTreeInfo.levelType === 3"
        field="manufactor"
        width="100"
        title="厂家"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.manufactor"
            type="text"
            @blur="clear()"
          ></vxe-input> </template
      ></vxe-column>
      <vxe-column
        v-if="projectStore.currentTreeInfo.levelType === 3"
        field="brand"
        width="100"
        title="品牌"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.brand"
            type="text"
            @blur="clear()"
          ></vxe-input> </template
      ></vxe-column>
      <vxe-column
        v-if="projectStore.currentTreeInfo.levelType === 3"
        field="deliveryLocation"
        width="100"
        title="送达地点"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.deliveryLocation"
            type="text"
            @blur="clear()"
          ></vxe-input> </template
      ></vxe-column>
      <vxe-column
        v-if="projectStore.currentTreeInfo.levelType === 3"
        field="qualityGrade"
        width="100"
        title="质量等级"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.qualityGrade"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
      </vxe-column>

      <vxe-column
        v-if="projectStore.asideMenuCurrentInfo?.key === '7'"
        field="output"
        width="100"
        title="是否输出"
        :cell-render="{}"
      >
        <template #default="{ row }">
          <vxe-checkbox
            v-model="row.output"
            size="small"
            content=""
            :checked-value="1"
            :unchecked-value="0"
            @change="CheckboxChange(row, 'output')"
          ></vxe-checkbox>
        </template>
      </vxe-column>
      <template #empty>
        <span
          style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          "
        >
          <img :src="getUrl('newCsProject/none.png')" />
        </span>
      </template>
    </vxe-table>
  </div>
  <common-modal
    className="dialog-comm"
    :title="typeModal"
    width="1020"
    :height="
      typeModal === '载价编辑' ? 560 : typeModal === '载价报告' ? 500 : 530
    "
    v-model:modelValue="reportModel"
    @cancel="cancel"
    @close="reportModel = false"
    :mask="true"
    :lockView="true"
  >
    <!--     :mask="typeModal === '载价编辑' ? false : true"
    :lockView="typeModal === '载价编辑' ? false : true" -->
    <batch-load-price
      v-if="typeModal === '批量载价'"
      @close="close"
    ></batch-load-price>
    <edit-load-price
      v-if="typeModal === '载价编辑'"
      :propsData="propsData"
      @close="close"
    ></edit-load-price>
    <report-load-price v-if="typeModal === '载价报告'"></report-load-price>
    <!-- 载价报告 -->
  </common-modal>

  <!-- 关联定额弹窗 -->
  <quota-popup
    v-if="quotaPopupVisible"
    :levelType="projectStore.currentTreeInfo.levelType"
    :HeaderData="quotaHeaderData"
    @closeDialog="quotaPopupVisible = false"
  ></quota-popup>
  <material-machine-index
    pageFr="rcjSummary"
    v-model:indexVisible="indexVisible"
    :currentMaterialInfo="currentInfo"
    :indexLoading="indexLoading"
    @addChildrenRcjData="() => {}"
    @currentInfoReplace="currentInfoReplace"
  ></material-machine-index>
  <SetMainMaterials
    :tableData="allRCJTableData"
    @successCallback="getHumanMachineData"
    v-model:materialVisible="materialVisible"
  />
  <LookupFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
  />
</template>

<script setup>
import {
  onMounted,
  onUpdated,
  ref,
  toRaw,
  watch,
  getCurrentInstance,
  provide,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  watchEffect,
  onActivated,
  onDeactivated,
} from 'vue';
// import * as XLSX from 'XLSX'
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '../../../../store/projectDetail';
import feePro from '@/api/feePro';
import loadApi from '../../../../api/loadPrice';
import infoMode from '@/plugins/infoMode.js';
import { disposeDeTypeData } from '@/hooks/publicApiData';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail';
import { getUrl, pureNumber } from '@/utils/index';
import HumanHeader from './HumanHeader.vue';
import { insetBus } from '@/hooks/insetBus';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
import operateList from '../operate';
import BatchLoadPrice from './BatchLoadPrice.vue';
import EditLoadPrice from './EditLoadPrice.vue';
import ReportLoadPrice from './ReportLoadPrice.vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import { setGlobalLoading } from '@/hooks/publicApiData';

const projectStore = projectDetailStore();
// 设置主要材料
const SetMainMaterials = defineAsyncComponent(() =>
  import('./SetMainMaterials.vue')
);
// 一、二、三类工
const renGongCodeList = [
  '10000001',
  '10000002',
  '10000003',
  'JXPB-005',
  'R00001',
];
/**
 * 获取是否勾选政策文件
 */
const isSelectFeePolicyDoc = ref(false);
const getFeePolicyDocData = () => {
  const isProject = projectStore.currentTreeInfo.levelType === 1;
  let apiData = {
    type: isProject ? 1 : 2,
    constructId: isProject
      ? projectStore.currentTreeInfo?.id
      : projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  feePro.getPolicyDocument(apiData).then(res => {
    console.log('政策文件', apiData, res);
    if (res.status === 200) {
      const { rgfId, awfId, gfId } = res.result;
      isSelectFeePolicyDoc.value = rgfId;
    }
  });
};
const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];
const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
let currentInfo = ref(null);
let reportModel = ref(false);
let typeModal = ref(null); //弹框类型
let propsData = ref(); //载价编辑的优先级

import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();

const quotaPopup = defineAsyncComponent(() =>
  import('@/components/SummaryPopup/index.vue')
);

let unifyData = operateList.value.find(
  item => item.name === 'unify-humanMachineSummary'
);
let isLoad = operateList.value.find(item => item.name === 'batch-loadprice');
// let isSeeReport = operateList.value.find(
//   item => item.name === 'loadprice-report'
// );
let quotaHeaderData = ref(null);
let isCurrent = ref(null);
let colorUnitList = ref([]);
let oldData = ref([]); //工程项目级别人材机汇总旧数据
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});

// 设置主要材料
let materialVisible = ref(false);
let allRCJTableData = ref([]);
const openSetMainMaterial = () => {
  materialVisible.value = true;
};
const closeSetMainMaterial = () => {
  materialVisible.value = false;
};

// 人材机索引
let indexVisible = ref(false);
// 人材机索引数据loading
let indexLoading = ref(false);
/**
 * 菜单右键替换数据处理
 */
const menuReplaceHandler = () => {
  indexVisible.value = true;
};
/**
 * 关闭替换人材机索引
 */
const closeReplaceRCJ = () => {
  indexVisible.value = false;
};

/**
 * 数据替换
 * replaceRcj 被替换的人材机
 * targetRcj 目标人材机
 */
const currentInfoReplace = targetInfo => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    replaceRcj: JSON.parse(JSON.stringify(currentInfo.value)),
    targetRcj: JSON.parse(JSON.stringify(targetInfo)),
  };
  api.replaceRcjToUnit(params).then(res => {
    console.log('人材机数据替换', params, res);
    if (res.status === 200) {
      message.success('替换成功!');
      getHumanMachineData();
      closeReplaceRCJ();
    }
  });
};

let humanTable = ref();
let loading = ref(false);
let tableData = ref([]);
let upDateRow = ref();
const selectOptions = [
  { type: '主材费' },
  { type: '材料费' },
  { type: '设备费' },
];

// 基期价、市场价为“-
const isChangeAva = row => {
  return projectStore.deType === '22' && Number(row.isChangeAva) === 0;
};
const quotaPopupVisible = ref(false); // 关联定额弹窗
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'search',
          name: '查询关联定额',
          visible: true,
          disabled: false,
        },
        {
          code: 'replace',
          name: '替换数据',
          visible: true,
          disabled: false,
        },
        {
          code: 'remove',
          name: '清除载价',
          visible: true,
          disabled: false,
        },
        {
          code: 'export',
          name: '导出excel',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, row);
    if (!row) return;

    options[0].find(item => item.code === 'remove').disabled =
      !row.highlight || row.sourcePrice == '自行载价';

    if (row.libraryCode?.startsWith('2022')) {
      options[0].find(item => item.code === 'remove').visible = false;
      // console.log('options[0][1]', options[0][1]);
    }
    let replaceInfo = options[0].find(item => item.code === 'replace');
    replaceInfo.visible = !!projectStore.currentTreeGroupInfo?.singleId;
    replaceInfo.disabled = !!row.rcjmx || row.isFyrcj === 0;
    return true;
  },
});

// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (!row) return;
  humanTable.value.setCurrentRow(row);
  currentInfo.value = row;
  switch (menu.code) {
    case 'search':
      quotaHeaderData.value = row;
      quotaPopupVisible.value = true;
      break;
    case 'replace':
      menuReplaceHandler();
      break;
    case 'export':
      exportExcel('all');
      break;
    case 'remove':
      // 掉接口恢复系统默认值
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '该条材料市场价已被锁定，'
            : '是否确定清除选中数据的载价数据？',
        descText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '请取消勾选后再进行清除载价操作'
            : '删除后无法撤销恢复',
        isFunction: false,
        confirm: () => {
          if (!row.ifLockStandardPrice || !row.cusTomIfLockStandardPrice) {
            clearZaijia(row);
          }
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
      break;
  }
};

// 清除载价格
const clearZaijia = data => {
  let postData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    rcj: { ...data },
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: data.sequenceNbr,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  console.log(
    '🚀 ~ file: index.vue:768 ~ csProject.clearLoadPriceUse ~ postData:',
    postData
  );
  csProject.clearLoadPriceUse(postData).then(res => {
    console.log(
      '🚀 ~ file: index.vue:760 ~ csProject.clearLoadPriceUse ~ res:',
      res
    );
    if (res.result) {
      message.success('清除成功');
      if (projectStore.currentTreeInfo.levelType === 1) {
        data.isChange = true; //标识编辑行
        // getSameUnit();
        // let upDateList = getPropData();
        // console.log('upDateList', upDateList);
        // if (upDateList && upDateList.length > 0) {
        projectStore.SET_HUMAN_UPDATA_DATA({
          isEdit: true,
          name: 'unify-humanMachineSummary',
          // updataData: upDateList,
          updataData: [],
        });
        // }
        unifyData.disabled = false;
      }
      getHumanMachineData();
    }
  });
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};
const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table.clearEdit();
};

const editClosedEvent = async e => {
  const { $table, row, column } = e;
  const field = column.field;
  // 选择重复调用处理
  if (
    [
      'markSum',
      'ifDonorMaterial',
      'ifProvisionalEstimate',
      'ifLockStandardPrice',
    ].includes(field)
  ) {
    return;
  }

  // 市场价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (['marketPrice'].includes(field)) {
    row[field] = +row[field];
  }

  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }

  switch (field) {
    case 'unit':
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  if (field === 'marketPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field === 'marketPrice' &&
    value > 0 &&
    row.marketPrice.length > 20
  ) {
    row.marketPrice = value.slice(0, 20);
  }
  if (
    field === 'marketPrice' &&
    value > 0 &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (
      Number(row.marketPrice) - Number(row.dePrice)
    ).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (field === 'donorMaterialNumber') {
    if (row.ifDonorMaterial === 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (row.ifDonorMaterial !== 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (
      row.ifDonorMaterial !== 1 &&
      value <= row.totalNumber &&
      value > 0
    ) {
      row.ifDonorMaterial = 1;
    } else if (row.ifDonorMaterial === 1 && (value <= 0 || value === '')) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = '';
    }
  }
  if (!(await isEditRenGongMarketPrice(field, row))) {
    $table.revertData(row, field);
    return;
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    console.log('🚀 ~ filnt ~ upDate:', row);
    upDate(row, field);
  } else {
    row.isChange = true; //标识编辑行
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: 'unify-humanMachineSummary',
        updataData: upDateList,
      });
    }
  }
};
// 是否修改人工1、2、3类工市场价
const isEditRenGongMarketPrice = (field, row) => {
  return new Promise(resolve => {
    if (
      ['marketPrice'].includes(field) &&
      isSelectFeePolicyDoc.value &&
      renGongCodeList.includes(row.materialCode)
    ) {
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText: '该市场价已受政策文件调整，是否确认修改',
        isFunction: false,
        confirm: () => {
          resolve(true);
          infoMode.hide();
        },
        close: () => {
          resolve(false);
          infoMode.hide();
        },
      });
    } else {
      resolve(true);
    }
  });
};
const getPropData = () => {
  let constructProjectRcjList = [];
  let upDateList = tableData.value.filter(item => item.isChange === true);
  upDateList.map(item => {
    let obj = {};
    let same = oldData.value.filter(l => l.sequenceNbr === item.sequenceNbr)[0];
    if (item.marketPrice !== same.marketPrice) {
      obj.marketPrice = item.marketPrice;
    }
    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial === 1) {
        obj.donorMaterialNumber = item.totalNumber;
      } else {
        obj.donorMaterialNumber = '';
      }
    }
    if (item.ifProvisionalEstimate != same.ifProvisionalEstimate) {
      obj.ifProvisionalEstimate = item.ifProvisionalEstimate;
    }
    if (item.ifLockStandardPrice != same.ifLockStandardPrice) {
      obj.ifLockStandardPrice = item.ifLockStandardPrice;
    }
    if (
      obj.hasOwnProperty('ifDonorMaterial') &&
      !obj.hasOwnProperty('donorMaterialNumber')
    ) {
      obj.donorMaterialNumber =
        obj.ifDonorMaterial === 1 ? item.totalNumber : '';
    }
    obj.sequenceNbr = item.sequenceNbr;
    obj.libraryCode = item.libraryCode;
    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const isUse = () => {
  //点击统一应用按钮
  if (!projectStore.humanUpdataData) {
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(projectStore.humanUpdataData.updataData)
    ),
  };
  // let tar = apiData.constructProjectRcjList[0];
  // if (
  //   Object.keys(tar).length === 1 &&
  //   apiData.constructProjectRcjList.length === 1
  // ) {
  //   apiData.constructProjectRcjList = [];
  // }
  //只是清除载价就传空值，清除载价+改市场价传修改数据
  console.log('统一应用接口传的参数', apiData);
  setGlobalLoading(true, '统一应用中，请稍后...');
  csProject
    .changeRcjConstructProject(apiData)
    .then(res => {
      console.log('统一应用接口返回结果', res);
      if (res.status === 200) {
        message.success('应用成功!');
        projectStore.SET_HUMAN_UPDATA_DATA(null);
        getHumanMachineData();
        unifyData.disabled = true;
      }
    })
    .finally(() => {
      setGlobalLoading(false);
    });
};
const upDate = (row, field) => {
  let apiData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    constructProjectRcj: {},
  };
  if (
    field === 'marketPrice' ||
    field === 'materialName' ||
    field === 'specification' ||
    field === 'unit' ||
    field === 'ifProvisionalEstimate' ||
    // field === 'ifDonorMaterial' ||
    field === 'ifLockStandardPrice' ||
    field === 'markSum' ||
    field === 'donorMaterialNumber' ||
    field === 'producer' ||
    field === 'manufactor' ||
    field === 'brand' ||
    field === 'deliveryLocation' ||
    field === 'qualityGrade' ||
    field === 'output'
  ) {
    apiData.constructProjectRcj[field] = row[field];
  } else if (field === 'type') {
    apiData.constructProjectRcj.kind = getKind(row.type);
  } else if (field === 'ifDonorMaterial') {
    apiData.constructProjectRcj.ifDonorMaterial = row[field];
    // apiData.constructProjectRcj.donorMaterialNumber = row.totalNumber;
  }
  apiData.libraryCode = row.libraryCode;
  console.log('修改人材机数据', apiData);
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  setGlobalLoading(true);
  csProject
    .changeRcj(apiData)
    .then(res => {
      if (res.status === 200) {
        console.log('修改人材机数据返回结果', res);
        isCurrent.value = row.sequenceNbr;
        getHumanMachineData();
      }
    })
    .finally(() => {
      setGlobalLoading(false);
    });
};

// =====================查找逻辑
let lookupVisible = ref(false);
const openLookup = event => {
  console.log(event);
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};
let originalTableData = []; // 查找之前的源数据
const lookupConfig = reactive({
  columns: [
    {
      field: 'materialName',
      label: '名称',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'specification',
      label: '规格',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'materialCode',
      label: '编码',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'marketPrice',
      label: '市场价',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'totalNumber',
      label: '数量',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
  ],
  conditionType: '&&',
  tableData: tableData,
});
const lookupCallback = rows => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData, true);
  } else {
    tableData.value = rows;
  }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    humanTable.value.setCurrentRow(info);
    currentInfo.value = info;
  });
};

const changeCurrentInfo = row => {
  if (row) {
    humanTable.value.setCurrentRow(row);
    currentInfo.value = row;
  }
};

const exportExcel = (dataType = '') => {
  const $table = humanTable.value;
  if (dataType !== 'all' && $table.getCheckboxRecords().length === 0) {
    message.info('请选择导出数据');
    return;
  }
  $table.exportData({
    filename: '人材机汇总导出报表',
    sheetName: 'Sheet1',
    type: 'xlsx',
    // types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
    // sheetMethod: sheetMethod, // 配置导出样式
    useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
    isFooter: true, //是否导出表尾（比如合计）
    data: dataType === 'all' ? tableData.value : $table.getCheckboxRecords(),
    columnFilterMethod({ column, $columnIndex }) {
      return !($columnIndex === 0);
    },
  });
};
const loadPrice = type => {
  reportModel.value = false;
  switch (type) {
    case 'batch-loadprice':
      typeModal.value = '批量载价';
      break;
    case 'loadprice-report':
      typeModal.value = '载价报告';
      break;
  }
  reportModel.value = true;
  console.log('执行loadPrice', reportModel.value);
};
const close = bol => {
  reportModel.value = false;
  console.log('执行close', reportModel.value);
};
const nextEdit = data => {
  propsData.value = data;
  typeModal.value = data.nextType;
  reportModel.value = true;
  if (typeModal.value === '载价报告') {
    getHumanMachineData();
  }
  console.log('执行nextEdit', reportModel.value);
};
provide('nextStep', nextEdit);
const jsz = () => {
  message.info('功能建设中...');
};
/**
 * 设置主要材料显示隐藏
 */
const showSetMainMaterial = () => {
  let mainMaterial = operateList.value.find(
    item => item.name === 'set-main-materials'
  );
  mainMaterial.levelType =
    Number(projectStore.asideMenuCurrentInfo.key) === 7 ? [3] : [];
};
watch(
  () => [projectStore.asideMenuCurrentInfo, projectStore.levelType],
  () => {
    if (
      projectStore.currentTreeInfo.levelType !== 2 &&
      projectStore.tabSelectName === '人材机汇总'
    ) {
      //侧边栏数据变化重新更新
      getHumanMachineData();
      getLoadStatus();
      showSetMainMaterial();
    }
  }
);
watch(
  () => projectStore.humanUpdataData,
  () => {
    if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
      unifyData.disabled = false;
    }
  }
);

const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});

// watchEffect(() => {
//   console.log('当前的', currentInfo.value);
// });

onMounted(() => {
  if (
    projectStore.currentTreeInfo.levelType !== 2 &&
    projectStore.tabSelectName === '人材机汇总' &&
    projectStore.asideMenuCurrentInfo?.key === '0'
  ) {
    // tableData.value = [];
    getHumanMachineData();
  }
  getLoadStatus();
});
onActivated(() => {
  console.log('onActivated');
  insetBus(bus, projectStore.componentId, 'humanMachineSummary', async data => {
    if (data.name === 'batch-loadprice')
      console.log('执行载价'), loadPrice(data.name);
    if (data.name === 'loadprice-report')
      console.log('载价报告'), loadPrice(data.name);
    if (data.name === 'market-price') console.log('调整市场价系数'), jsz();
    if (data.name === 'unify-humanMachineSummary') {
      isUse();
    }
    if (data.name === 'export-table') console.log('导出报表'), exportExcel();
    if (data.name === 'set-main-materials') openSetMainMaterial();
    if (data.name === 'lookup') openLookup();
    console.log(data);
  });
  getFeePolicyDocData();
  window.addEventListener('keydown', openLookup);
});
onDeactivated(() => {
  console.log('onDeactivated');
  lookupVisible.value = false;
  window.removeEventListener('keydown', openLookup);
});
const getLoadStatus = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }
  loadApi.loadPriceStatus(apiData).then(res => {
    console.log('++++++++++++++', res);
    // isSeeReport.disabled = false;
    if (res.result && res.result.includes(5)) {
      isLoad.disabled = true;
    } else if (res.result && res.result.includes(3)) {
      // isSeeReport.disabled = true;
    } else if (res.result && res.result.includes(4)) {
      feePro.isOnline().then(res => {
        console.log('判断是否有网------', res);
        if (res.result) {
          isLoad.disabled = false;
        } else {
          message.error('请连接网络后使用！');
        }
      });
    }
  });
};
const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.map(item => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum === 1 && [1, 2].includes(Number(item.levelMark))) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};
const getKind = type => {
  let value;
  switch (type) {
    case '其他费':
      value = 0;
      break;
    case '人工费':
      value = 1;
      break;
    case '材料费':
      value = 2;
      break;
    case '机械费':
      value = 3;
      break;
    case '设备费':
      value = 4;
      break;
    case '主材费':
      value = 5;
      break;
    case '商砼':
      value = 6;
      break;
    case '砼':
      value = 7;
      break;
    case '浆':
      value = 8;
      break;
    case '商浆':
      value = 9;
      break;
    case '配比':
      value = 10;
      break;
  }
  return value;
};
const getType = type => {
  let value = '';
  switch (type) {
    case 0:
      value = '其他费';
      break;
    case 1:
      value = '人工费';
      break;
    case 2:
      value = '材料费';
      break;
    case 3:
      value = '机械费';
      break;
    case 4:
      value = '设备费';
      break;
    case 5:
      value = '主材费';
      break;
    case 6:
      value = '商砼';
      break;
    case 7:
      value = '砼';
      break;
    case 8:
      value = '浆';
      break;
    case 9:
      value = '商浆';
      break;
    case 10:
      value = '配比';
      break;
  }
  return value;
};
const getOldData = () => {
  tableData.value &&
    tableData.value.map(item => {
      oldData.value.push({
        sequenceNbr: item.sequenceNbr,
        marketPrice: item.marketPrice,
        ifDonorMaterial: item.ifDonorMaterial,
        donorMaterialNumber: item.donorMaterialNumber,
        ifProvisionalEstimate: item.ifProvisionalEstimate,
        ifLockStandardPrice: item.ifLockStandardPrice,
      });
    });
  console.log('getOldData', oldData.value);
};
const getHumanMachineData = () => {
  loading.value = true;
  const kind = Number(projectStore.asideMenuCurrentInfo?.key);
  let formData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    kind,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    formData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    formData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  console.log(formData);
  feePro
    .queryConstructRcjByDeId(formData)
    .then(res => {
      if (res.status === 200 && res.result && res.result.length > 0) {
        let num = 1;
        res.result &&
          res.result.map((item, index) => {
            item.dispNo = num++;
            item.type = getType(item.kind);
            item.donorMaterialNumber =
              Number(item.donorMaterialNumber) === 0
                ? ''
                : item.donorMaterialNumber;
            item.origindonorMaterialNum = item.donorMaterialNumber
              ? item.donorMaterialNumber
              : '0';
          });
        // tableData.value = res.result;
        tableData.value = disposeDeTypeData(res.result, true, true);
        console.log(tableData.value);
        nextTick(() => {
          if (isCurrent.value && projectStore.currentTreeInfo.levelType === 3) {
            let isCurrentList = tableData.value.filter(
              item => isCurrent.value === item.sequenceNbr
            );
            humanTable.value.setCurrentRow(isCurrentList[0]);
          } else {
            humanTable.value.setCurrentRow(
              tableData.value && tableData.value[0]
            );
          }
        });

        checkBoxIsShow();
        if (projectStore.currentTreeInfo.levelType === 1) {
          getOldData();
          getSameUnit();
        }
        console.log('人材机汇总表格数据', tableData.value);
        currentInfo.value = tableData.value[0];
      } else {
        tableData.value = [];
      }
      originalTableData = xeUtils.clone(tableData.value, true);
      if (kind === 0) {
        // 记录所有人材机数据
        allRCJTableData.value = xeUtils.clone(tableData.value, true);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  tableData.value &&
    tableData.value.map(item => {
      let otherSameUnit = tableData.value.filter(
        unit =>
          unit.materialCode === item.materialCode &&
          unit.materialName === item.materialName &&
          // unit.unitId === item.unitId &&
          unit.unit === item.unit &&
          unit.specification === item.specification &&
          Number(unit.dePrice) === Number(item.dePrice) &&
          unit.ifDonorMaterial == item.ifDonorMaterial &&
          unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
          unit.ifLockStandardPrice == item.ifLockStandardPrice &&
          unit.sequenceNbr !== item.sequenceNbr &&
          Number(unit.marketPrice) !== Number(item.marketPrice)
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  tableData.value &&
    tableData.value.map(
      item =>
        (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );
  console.log('addColorList', addColorList);
};
const cellTableStyle = ({ row, column }) => {
  if (column.field === 'marketPrice') {
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      return {
        color: '#059421',
        backgroundColor: 'rgba(22, 225, 83, 0.4)',
      };
    }
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
const cellStyle = ({ row, column }) => {
  if (column.field === 'marketPrice') {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
const rowStyle = ({ row }) => {
  if (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) {
    if (row.field !== 'dispNo') {
      return {
        color: '#ACACAC',
      };
    }
  }
  if (row.highlight) {
    return {
      backgroundColor: '#FCF8EF',
    };
  }
};
const CheckboxChange = (row, type) => {
  switch (type) {
    case 'markSum':
      row.checkIsShow = row.markSum === 1 ? false : true;
      break;
    case 'ifDonorMaterial':
      break;
    case 'ifProvisionalEstimate':
      break;
    case 'ifLockStandardPrice':
      row.cusTomIfLockStandardPrice = row.ifLockStandardPrice;
      break;
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    upDate(row, type);
  } else if (projectStore.currentTreeInfo.levelType === 1) {
    //工程项目级别
    row.isChange = true; //标识编辑行
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: 'unify-humanMachineSummary',
        updataData: upDateList,
      });
    }
    if (type === 'ifDonorMaterial') {
      row.donorMaterialNumber =
        row.ifDonorMaterial === 1 ? row.totalNumber : '';
      humanTable.value.reloadRow(row, {});
    }
  }
};

const getCurrentIndex = (item, type) => {
  console.log('item', item, tableData.value);
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = n;
      }
    });
  } else {
    isCurrent.value = tableData.value[0];
  }
  humanTable.value.setCurrentRow(isCurrent.value);
  console.log('==============', isCurrent);
};

const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
  if (projectStore.currentTreeInfo.levelType === 1) {
    getCurrentIndex(row);
  }
};

// 定位方法
const posRow = sequenceNbr => {
  console.log('人材机汇总定位', sequenceNbr);
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  getHumanMachineData();
  setTimeout(() => {
    getCurrentIndex({ sequenceNbr });
  }, 800);
  // currentInfo.value = { sequenceNbr };
};

defineExpose({
  posRow,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}
.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
  :deep(.background-red) {
    color: #2a2a2a;
    background: #de3f3f;
  }
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
</style>
