<!--
 * @Descripttion: 设置汇总范围
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-26 16:06:37
-->
<template>
  <common-modal
    className="dialog-comm setAggreScope-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="设置汇总范围"
    width="550px"
    height="550px"
    :mask="true"
    show-zoom
    resize
    :lock-view="false"
    destroy-on-close
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div class="group-content-wrap">
      <vxe-table
        ref="vexTable"
        :column-config="{ resizable: true }"
        :data="TableData"
        height="80%"
        :row-config="{
            isCurrent: true,
            keyField: 'id',
          }"
        :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
        :tree-config="{
            transform: true,
            rowField: 'id',
            parentField: 'parentId',
            line: true, 
            expandAll: true,
            iconOpen: 'vxe-icon-square-minus',
            iconClose: 'vxe-icon-square-plus'
          }"
        :show-overflow="true"
      >
        <vxe-column
          field="sort"
          width="40"
          title=" "
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          field="name"
          min-width="300"
          title="工程名称"
          align="left"
          tree-node
        >
          <template #default="{ row, rowIndex }">
            <span style="margin-left:8px">{{ row.name }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="merge"
          min-width="60"
          title="调整"
          :cell-render="{}"
        >
          <template #default="{ row, rowIndex }">
            <vxe-checkbox
              v-model="row.cyhb"
              name="调整"
              @change="handleClearMergeMer(row, rowIndex)"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>

      <p class="footer-btnList">
        <a-button
          v-for="i in btnList"
          @click="btnFun(i)"
          size="small"
        >{{i.label}}</a-button>
      </p>
      <p class="footer-box">
        <a-button
          type="primary"
          ghost
          @click="cancel()"
        >取消</a-button>
        <a-button
          type="primary"
          style="margin-left: 14px"
          @click="handleOk()"
        >确定</a-button>
      </p>

    </div>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, reactive } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail.js';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
const route = useRoute();
const emits = defineEmits(['closeDialog', 'refresh', 'selfTestLocateTable']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);
let loading = ref(false);
const btnList = reactive([
  {
    label: '选择同名工程',
    id: 'selectSameName',
  },
  {
    label: '取消同名工程',
    id: 'cancelSameName',
  },
  {
    label: '选择同专业工程',
    id: 'selectSameMajor',
  },
  {
    label: '取消同专业工程',
    id: 'cancelSameMajor',
  },
]);
const cancel = (refresh = false) => {
  if (refresh) {
    emits('refresh');
  } else {
    // emits('closeDialog');
    dialogVisible.value = false;
  }
};

const open = k => {
  dialogVisible.value = true;
  getTableList();
};
let initTreeData = ref([]);
const getTableList = () => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      initTreeData.value = xeUtils.toArrayTree(res.result, {
        parentKey: 'parentId',
        childrenKey: 'children',
      });
      TableData.value = res.result;
      console.log(
        '设置汇总范围-treeData.value',
        TableData.value,
        res.result,
        initTreeData.value
      );
      nextTick(() => {
        vexTable.value.setAllTreeExpand(true);
      });
    }
  );
};

const handleClearMergeMer = (row, index) => {
  if (row.mergeTarget === null && !row.cyhb) {
    row.mergeTarget = false;
  }
};

// 双击事件
const cellDBLClickEvent = async ({ row }) => {
  selfTestLocate(row);
};
const selfTestLocate = row => {
  emits('selfTestLocateTable', row);
};

// 确认
const handleOk = () => {};
defineExpose({
  open,
});
</script>

<style lang="scss">
.setAggreScope-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .content-table {
    height: 80%;
    padding: 0 0px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .footer-btnList {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;
  }
  .footer-box {
    width: 150px;
    margin-left: calc(100% - 150px);
  }
  .vxe-table .index-bg {
    background-color: #ffffff;
  }
  .vxe-table--body {
    border-collapse: collapse;
    // border: 2px solid #ffffff;
  }
}
</style>
