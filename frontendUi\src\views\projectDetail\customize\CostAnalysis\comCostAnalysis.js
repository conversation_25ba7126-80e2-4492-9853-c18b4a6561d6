/*
 * @Descripttion:项目概况公共代码提取
 * @Author: wangru
 * @Date: 2024-01-30 10:12:01
 * @LastEditors: wangru
 * @LastEditTime: 2024-10-14 11:33:55
 */

import { ref, toRaw, reactive, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import ysshcsProject from '@/api/shApi';
import { message, Modal } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
export const comCostAnalysisJs = ({
  tableData,
  unitTable,
  editIndex,
  currentChange,
  average,
}) => {
  let operate = {
    ys: feePro,
    yssh: feePro,
  };
  const projectStore = projectDetailStore();
  let dafaultParams = reactive({});
  onMounted(() => {
    refreshParams();
  });
  const refreshParams = () => {
    dafaultParams = {
      levelType: projectStore.currentTreeInfo?.levelType,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId:
        projectStore.currentTreeInfo?.levelType === 3
          ? projectStore.currentTreeGroupInfo?.singleId
          : null,
      unitId:
        projectStore.currentTreeInfo?.levelType === 3
          ? projectStore.currentTreeInfo?.id
          : null,
    };
  };
  const getPageData = async () => {
    refreshParams();
    let resData = await operate[projectStore.type].getCostAnalysisData(
      dafaultParams
    );
    if (resData.status === 200) {
      let averge;
      let unitCost;
      let total;
      let list = resData.result.costAnalysisUnitVOList;
      list.map((item, index) => {
        if (item.name.includes('建筑面积')) {
          editIndex.value = index;
          item.context = item.context
            ? parseFloat(item.context).toFixed(2)
            : '0.00';
          averge = item.context;
          average.value = item.context;
        }
        if (item.name.includes('工程总造价(不含设备费及其税金 小写)')) {
          item.context = item.context ? parseFloat(item.context) : '';
          total = item.context;
        }
        if (item.name.includes('单方造价')) {
          item.context = item.context ? item.context.toFixed(2) : '0.00';
        }
      });
      tableData.value = list;
      setTimeout(() => {
        const $table = unitTable.value;
        if ($table) {
          // $table.setAllTreeExpand(true);
          if (currentChange.value) {
            let row = tableData.value.find(
              item => item.name === '建筑面积(m、㎡)'
            );
            $table.scrollToRow(row);
            $table.setCurrentRow(row);
          }
        }
      }, 0);
    }
    console.log('造价分析', dafaultParams, resData);
    return resData;
  };

  const updateOperate = async (average, unitcost = null) => {
    let apiData = {
      ...dafaultParams,
      average: average ? Number(average) : 0,
      flag: false,
    };
    let resData = await operate[projectStore.type].updateCostAnalysis(apiData);
    console.log('造价分析---saveAndUpdateOperate', resData, apiData);
    return resData;
  };
  return {
    getPageData,
    updateOperate,
  };
};
