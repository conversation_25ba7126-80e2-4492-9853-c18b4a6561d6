<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-24 09:21:50
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-08 20:19:14
-->
<template>
  <div class="operate">
    <div class="operate-scroll">
      <!-- @click="emit('executeCommand',item)" -->
      <!-- {{projectStore.componentId}} -->
      <template
        v-for="(item, index) in operateList"
        :key="index"
      >
        <div
          class="operate-item"
          :class="{ disabled: item.disabled }"
          v-show="
            item.levelType.includes(projectStore.currentTreeInfo?.levelType) &&
            item.components.includes(projectStore.componentId) &&
            !(noShowList.includes(item.name) && projectStore.deStandardReleaseYear === '22') &&
            !(
              noShowList12.includes(item.name) && projectStore.deStandardReleaseYear === '12'
            ) &&
            item.windows.includes(projectStore.standardGroupOpenInfo?.type) &&
            !item.hidden
          "
          v-if="isShowRender(item)"
          @click="!item.disabled && !item.type ? setEmit(item) : ''"
        >
          <a-tooltip placement="bottom" v-model:visible="item.decVisible" @visibleChange="val => infoVisibleChange(val, item)">
            <!-- :color="'white'" -->
            <template #title>
              <span style="font-size:12px;text-decoration: underline;">{{ item.label }}</span>
              <p
                v-if="item.infoDec"
                style="font-size:10px;"
              >
                {{ item.infoDec }}
              </p>
            </template>

            <template v-if="['select', 'selectRadio'].includes(item.type)">
              <a-dropdown
                @visibleChange="setEmit(item)"
                trigger="click"
              >
                <div
                  class="select-radio"
                  v-if="['selectRadio'].includes(item.type)"
                >
                  <div class="select-head">
                    <icon-font
                      :type="item.iconType"
                      class="iconType"
                      :style="item.iconStyle ?? {}"
                    />
                    <div
                      class="label"
                      :style="item.labelStyle ?? {}"
                    >
                      {{ item.label }}
                    </div>
                    <icon-font
                      type="icon-xiala"
                      style="color: rgba(51, 51, 51, 0.39)"
                    />
                  </div>
                  <div class="sub-name">
                    {{
                    item.options.find(opt => opt.kind === item.value)?.name ||
                    ''
                  }}
                  </div>
                </div>
                <div v-else>
                  <!-- <div class="icon" :style="item.iconStyle ?? {}">
                  <img :src="item.icon" alt="" />
                </div> -->
					<icon-font
					  :type="item.iconType"
					  class="iconType"
					  :style="item.iconStyle ?? {}"
					/>
					<div
					  class="label"
					  :style="item.labelStyle ?? {}"
					>
					  {{ item.label }}										
					</div>				 
                </div>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-for="selectitem in item.options"
                      :key="selectitem.kind"
                      :disabled="!selectitem.isValid"
                      @click="setSelectEmit(selectitem, item)"
                    >
                      <a-checkbox
                        v-if="['selectCheck'].includes(item.type)"
                        :checked="selectitem.kind == checkedIndex"
                      ></a-checkbox>
                      <a-radio
                        v-if="['selectRadio'].includes(item.type)"
                        :checked="selectitem.kind == item.value"
                      ></a-radio>
                      <span
                        v-if="['select-color'].includes(item.name)"
                        class="color-border"
                        :class="`${selectitem.kind}`"
                      ></span>
                      {{ selectitem.name }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
            <template v-else-if="['selectCheck'].includes(item.type)">
              <a-dropdown
                @visibleChange="setEmit(item)"
                trigger="click"
                v-model:visible="visible"
              >
                <div>
                  <!-- <div class="icon" :style="item.iconStyle ?? {}">
                  <img :src="item.icon" alt="" />
                </div> -->
				<template v-if="'filter-list' == item.name">
				  <icon-font :type="filteringStates ? 'icon-guolv-xiaoxi' : item.iconType"  class="iconType" :style="item.iconStyle ?? {}" />
				</template>
                <template v-else>
                  <icon-font :type="item.iconType"  class="iconType" :style="item.iconStyle ?? {}" />
                </template> 
                  <div
                    class="label"
                    :style="item.labelStyle ?? {}"
                  >
                    {{ item.label }}
                  </div>
                </div>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-for="selectitem in item.options"
                      :key="selectitem.kind"
                      :disabled="!selectitem.isValid"
                      @click="setSelectEmit(selectitem, item)"
                    >
                      <a-checkbox
                        v-if="['selectCheck'].includes(item.type)"
                        :checked="selectitem.kind == checkedIndex"
                      ></a-checkbox>
                      <span
                        v-if="['select-color'].includes(item.name)"
                        class="color-border"
                        :class="`${selectitem.kind}`"
                      ></span>
                      {{ selectitem.name }}
                    </a-menu-item>
                    <a-menu-item class="filter-color" v-if="['selectCheck'].includes(item.type)">
						<span class="title">按颜色过滤</span>
						<div style="display: flex;flex-direction: column;">
							<a-checkbox v-if="['selectCheck'].includes(item.type)" :indeterminate="state.indeterminate"
								v-model:checked="state.checkAll" 
								@change="onCheckAllChange">全部</a-checkbox>
							<a-checkbox-group v-model:value="checkColorList">
								<div v-for="(color, index) in colorList" :key="index">
									<a-checkbox  v-model:checked="state.checkAll"  :value="color">
										<span class="color-border" :class="`${color}`"></span>
									</a-checkbox>
								</div>
							</a-checkbox-group>
						</div>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
            <template v-else>
              <!-- <div class="icon" :style="item.iconStyle ?? {}">
              <img :src="item.icon" alt="" />
            </div> -->
              <div>
                <a-badge :dot="item.badgeDot">
                  <icon-font
                    :type="item.iconType"
                    class="iconType"
                    :style="item.iconStyle ?? {}"
                  />
                  <div
                    class="label"
                    :style="item.labelStyle ?? {}"
                  >
                    {{ item.label }}
                  </div>
                </a-badge>
              </div>
            </template>
          </a-tooltip>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
import {
  getCurrentInstance,
  watch,
  ref,
  computed,
  reactive,
  onMounted,
} from 'vue';
import operateList, { updateOperateByName } from './operate';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import apiObj from '@/api/projectDetail.js';
import jieSuanApi from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
const projectStore = projectDetailStore();
const props = defineProps(['projectComponentType']);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const noShowList = ['fees']; //22定额标准  组价方案匹配按钮,规费明细 记取水、电费不展示
const noShowList12 = ['taxation']; //12定额标准  计税方式
const emit = defineEmits(['showLoadMould']);
const route = useRoute();
const value = ref([]);
const checked = ref(true); // 已选中的选项
let checkColorList = ref([]); // 颜色筛选项
let colorList = ref([]); // 已有的颜色列表
let visible = ref(false); // 过滤下拉菜单判断是否关闭


const checkedIndex = computed(() => {
  return projectStore.currentTreeInfo?.screenCondition;
});
// 处理全选的逻辑
const state = reactive({
  indeterminate: true,
  checkAll: false
});

const onCheckAllChange = (e) => {
	checkColorList.value = e.target.checked ? colorList.value : [];
	state.checkAll =  e.target.checked;
	state.indeterminate =  false;
};
watch( checkColorList,
  val => {
    state.indeterminate = !!val.length && val.length < colorList.value.length;
    state.checkAll = val.length === colorList.value.length;
  },
);

// 处理界面是否显示过滤标识
const filteringStates = computed(() => {
	const { currentTreeInfo } = projectStore;
	if(currentTreeInfo?.screenCondition != null && currentTreeInfo?.screenCondition != undefined && currentTreeInfo?.screenCondition != 0 ){
		return true;
	}
	if(currentTreeInfo?.checkColorList) {
		if (projectStore.componentId === 'subItemProject') {
		  const { fbfx = [] } = currentTreeInfo.checkColorList;
		  return fbfx.filter(element => element != null).length > 0;
		}
		if (projectStore.componentId === 'measuresItem') {
		  const { csxm = [] } = currentTreeInfo.checkColorList;
		  return csxm.filter(element => element != null).length > 0;
		}
	}
	return false;
});




watch(
  () => visible.value,
  val => {
    if (val) {
      getInitColorList();
    }
  }
);
const infoVisibleChange = (val, item) => {
  if (val) {
    item.decVisible = true;
  } else {
    item.decVisible = false;
  }
};

const isShowRender = item => {
  const type = props.projectComponentType || projectStore.type;

  if (type === 'ys') {
    if (
      projectStore.updateSS &&
      [
        'unify-humanMachineSummary',
        'load-on-mould',
        'selfCheck',
        'component-matching',
        'reuse-group-price',
        'qd-group-price',
        'standard-group-price',
        'dataReplacement',
        'save-on-mould',
        'load-on-mould',
        'lock-subItem',
        'market-price',
      ].includes(item.name)
    ) {
      // 修改送审，隐藏人材机汇总里面的统一应用,调整市场价系数,  载入模板 保存模板
      // 费用汇总：载入模板、项目自检
      //分部分项+措施项目：组价方案匹配 复用组价  清单快速组价 标准组价  清单锁定
      return false;
    }

    // 预算没值或者有值存在ys显示
    return !item.showProjectType || item.showProjectType?.includes('ys');
  }
  if (type === 'yssh') {
    // 结算显示没值或者有值存在yssh显示
    return !item.showProjectType || item.showProjectType?.includes('yssh');
  }
  const show = !item.showProjectType || item.showProjectType?.includes(type);
  if (type === 'jieSuan') {
    if (item.label === '固定安文费') {
      return AWF.value;
    }
    return show && jieSuanOperateShowRender(item.label);
  }
  return true;
};
let AWF = ref(true);
const jieSuanOperateShowRender = name => {
  if (!projectStore.currentTreeInfo?.originalFlag) {
    const excludeNameObj = {
      subItemProject: ['工程量量差设置', '人材机分期调整'],
      measuresItem: ['人材机分期调整', '结算方式'],
    };
    const obj = excludeNameObj[projectStore.componentId];
    return obj ? !obj.includes(name) : true;
  }
  // 对应模块合同外显示操作
  const nameObj = {
    subItemProject: [
      '插入',
      '补充',
      '删除',
      '人材机分期调整',
      '工程量量差设置',
      '展开到',
      '工程量批量乘以系数',
    ],
    measuresItem: ['人材机分期调整', '结算方式', '展开到'],
    qtxmStatistics: [],
    summaryExpense: [
      '插入',
      '费用代码明细',
      '安全生产、文明施工费明细',
      '规费明细',
      '价差规费明细',
      '价差安、文费明细',
      '记取水、电费',
    ],
  };
  const obj = nameObj[projectStore.componentId];
  return obj ? obj.includes(name) : true;
};

watch(
  () => projectStore.componentId,
  val => {
    if (val) {
      operateList.value.sort((a, b) => {
        const aSetIndex = a.setIndex?.[val] || 0;
        const bSetIndex = b.setIndex?.[val] || 0;
        return aSetIndex - bSetIndex;
      });
      // projectStore.currentTreeInfo?.colorList?.forEach(item => {
      //   console.log('item', item)
      //   // colorList.value.push({
      //   //   value: item,
      //   // });
      // });
	 
      getInitColorList();
    }
  }
);

const getInitColorList = () => {
  const { fbfx = [], csxm = [] } =
    projectStore.currentTreeInfo?.colorList || {};
  if (projectStore.componentId === 'subItemProject') {
    colorList.value = ['none', ...fbfx];
  }
  if (projectStore.componentId === 'measuresItem') {
    colorList.value = ['none', ...csxm];
  }
  const { fbfx: cfbfx = [], csxm: ccsxm = [] } =
    projectStore.currentTreeInfo?.checkColorList || {};
  if (projectStore.componentId === 'subItemProject') {
    checkColorList.value = cfbfx;
  }
  if (projectStore.componentId === 'measuresItem') {
    checkColorList.value = ccsxm;
  }
  // if (cfbfx.length || ccsxm.length) {
    projectStore.SET_CHECK_COLOR_LIST(checkColorList.value);
  // }
};

watch(
  () => projectStore.currentTreeInfo,
  val => {
    if (val) {
	  getInitColorList();
      updateOperateByName('filter-list', item => {
        item.value = projectStore.currentTreeInfo?.screenCondition;
      });
    }
  },
  { deep: true }
);
onMounted(async () => {
  AWF.value = await jieSuanApi.selectSecurityFee({
    constructId: route.query.constructSequenceNbr,
  });
});

const setEmit = item => {
  item.decVisible = false;
  if (item.name === 'load-on-mould') {
    // 显示加载模板，后续多个业务都要用
    if (
      projectStore.type === 'jieSuan' &&
      projectStore.currentTreeInfo.originalFlag &&
      ['其他项目', '费用汇总'].includes(projectStore.tabSelectName)
    ) {
      message.info('合同内暂不支持该功能');
      return;
    } else {
      emit('showLoadMould');
    }
  } else if (item.name === 'save-on-mould') {
    // 显示保存模板，后续多个业务都要用
    message.info('功能建设中...');

    // emit('showLoadMould');
  } else if (item.name === 'return-proEdit') {
    //返回项目编辑
    console.log('projectStore', projectStore);
    let postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    csProject.standardMergeBack(postData).then(res => {
      console.log('返回项目编辑', res, postData);
      if (res.status === 200) {
        let { selectProjectId } = projectStore.standardGroupOpenInfo;
        setTab(selectProjectId);
        projectStore.SET_CURRENT_TREE_INFO({
          ...projectStore.standardGroupOpenInfo.oldTreeInfo.currentTreeInfo,
        });
        projectStore.SET_CURRENT_TREE_GROUP_INFO({
          ...projectStore.standardGroupOpenInfo.oldTreeInfo
            .currentTreeGroupInfo,
        });
        projectStore.SET_STANDARD_GROUP_OPEN_INFO({
          isOpen: false,
          info: null,
          selectProjectId: selectProjectId,
          type: 'parentPage', //子窗口
          modalTip: null,
          treeGroup: {},
          oldTreeInfo: {
            currentTreeInfo: null,
            currentTreeGroupInfo: null,
          },
        });
      }
    });
    // let { selectProjectId } = projectStore.standardGroupOpenInfo;
    // setTab(selectProjectId);
    // projectStore.SET_STANDARD_GROUP_OPEN_INFO({
    //   isOpen: false,
    //   info: null,
    //   selectProjectId: selectProjectId,
    //   type: 'parentPage', //子窗口
    // });
  } else if (item.name === 'filter-list') {
    console.log('2322222222', visible.value);
    if (!visible.value) {
      updateUnitColorColl();
    }
  } else {
    bus.emit(item.name, item);
  }
};
const setTab = id => {
  //设置切换主窗体tab栏定位到分部分项
  let list = [...projectStore.proCheckTab];
  list.map(i => {
    if (i.id === id) {
      i.clickTab = '分部分项';
    }
  });
  projectStore.SET_PRO_CHECK_TAB(list); //此处初始化tab的点击记忆
};
const setSelectEmit = (item, data) => {
  if (['selectRadio', 'selectCheck']?.includes(data.type)) {
    data.value = item.kind;
  }
  if (data.type === 'selectCheck' && data.name === 'filter-list') {
    console.log('🚀 ~ setSelectEmit ~ item:', item.kind);
    visible.value = false;
    // bus.emit(data.name + item.kind, data);
    // let screenCondition = item.kind;
    if (projectStore.currentTreeInfo?.screenCondition === item.kind) {
      data.value = 0;
    }
    let postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      screenCondition: data.value,
    };
    csProject.updateUnitScreenCondition(postData).then(res => {
      bus.emit(data.name + item.kind, data);
      if (projectStore.updateSS) {
        setUnitInfo();
        refreshList();
      } else {
        projectStore.isRefreshProjectTree = true;
      }
    });
    return;
  }
  visible.value = false;
  bus.emit(data.name + item.kind, data);
};

const refreshList = () => {
  projectStore.$state.mainContentComponentRefresh = false;
  setTimeout(() => {
    projectStore.$state.mainContentComponentRefresh = true;
  }, 10);
};
const setUnitInfo = () => {
  // 修改送审，过滤之后screenCondition左侧树未同步，改接口拿值重新复值
  if (!projectStore.updateSS) return; // 不是修改送审
  shApi
    .getUnitProject({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
    })
    .then(res => {
      if (!res) return;
      console.log(res, 'screenCondition左侧树未同步');
      const { screenCondition } = res;
      projectStore.$state.currentTreeInfo.screenCondition = screenCondition;
    });
};
const updateUnitColorColl = () => {
  if (checkColorList.value.filter(x => x == null).length > 0) {
    checkColorList.value = [];
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    checkColorList: JSON.parse(JSON.stringify(checkColorList.value)),
    type:
      projectStore.componentId === 'subItemProject'
        ? 'fbfx'
        : projectStore.componentId === 'measuresItem'
        ? 'csxm'
        : '',
  };
  console.log('过滤参数', apiData);
  apiObj.updateUnitColorColl(apiData).then(res => {
    console.log('颜色过滤', res);
    if (res.status === 200 && res.result) {
      projectStore.isRefreshProjectTree = true;
      projectStore.SET_CHECK_COLOR_LIST(checkColorList.value);
    }
  });
};
</script>
<style lang="scss" scoped>
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.select-radio {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  .select-head {
    display: flex;
    align-items: center;
    font-size: 14px;
  }
  .sub-name {
    margin-top: 4px;
    width: 100%;
    font-size: 12px;
    text-align: center;
  }
}
.operate {
  // flex-direction: row;
  box-sizing: border-box;
  border-bottom: 1px solid #d6d6d6;
  background: #f3f6f9;
  width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  height: 54px;
  line-height: 54px;
  user-select: none;
  &:hover {
    overflow-x: auto;
  }
  &-scroll {
    display: flex;
    min-width: fit-content; /* 设置最小宽度为子元素的总宽度 */
    padding: 0 5px;
  }
  &-item {
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    //align-items: center; /* 水平居中 */
    min-width: 50px;
    padding: 0 10px;
    text-align: center;
    height: 54px;
    cursor: pointer;
    div {
      height: auto;
      line-height: initial;
      text-align: center;
    }
    .iconType {
      font-size: 26px;
    }
    .icon {
      width: 28px;
      img {
        width: 100%;
      }
    }
    .label {
      font-size: 12px;
      margin-top: 2px;
    }
  }
}
.color-border {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 1px solid #eeeeee;
  border-radius: 3px;
  margin-right: 5px;
}
.none {
  background: none;
}
.red {
  background: #ef7c77 !important;
}
.green {
  background: #e3fada !important;
}
.orange {
  background: #e59665 !important;
}
.yellow {
  background: #fdfdac !important;
}
.blue {
  background: #8fa9fa !important;
}
.purple {
  background: #cfaadd !important;
}
.lightBlue {
  background: #a5d7f0 !important;
}
.deepYellow {
  background: #fbdf89 !important;
}
.filter-color {
  text-align: center;
  .color-list {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .color-border {
    margin-left: 10px;
    width: 16px;
    height: 16px;
  }
  .color-all {
    margin-left: 10px;
  }
  .title {
    display: block;
  }
  .ant-checkbox-group {
    ::v-deep(.ant-checkbox-group-item) {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
