<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-01 16:26:54
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-24 10:35:42
 * @Introduce:
-->

<template>
  <div class="content">
    <div>
      <a-form
        ref="form"
        :model="inputData"
        :label-col="colStyle.labelCol"
        :wrapper-col="colStyle.wrapperCol"
        @finish="onSubmit"
        class="newClass"
      >
        <a-form-item
          label="请选择送审文件"
          name="submissionDocuments"
          v-if="showType.projectType == 'yusuan'"
          :rules="[
            {
              required: true,
              message: '请选择送审文件!',
            },
          ]"
        >
          <div class="ys-flex">
            <a-input
              v-model:value="inputData.submissionDocuments"
              placeholder="请选择送审文件"
              @keyup="inputData.projectName = projectName('ss')"
              @input="
                () => {
                  inputData.submissionDocuments = YSremoveSpecialChars(
                    inputData.submissionDocuments
                  );
                }
              "
            />
            <a-button
              style="margin-left: 10px"
              type="primary"
              size="small"
              @click="upFile(1)"
              >选择送审</a-button
            >
          </div>
        </a-form-item>
        <a-form-item
          label="请选择审定文件"
          name="approvedDocuments"
          v-if="showType.projectType == 'yusuan'"
        >
          <div class="ys-flex">
            <a-input
              v-model:value="inputData.approvedDocuments"
              placeholder="请选择审定文件"
              @input="
                () => {
                  inputData.approvedDocuments = YSremoveSpecialChars(
                    inputData.approvedDocuments
                  );
                }
              "
              @keyup="inputData.projectName = projectName('sd')"
            />
            <a-button
              style="margin-left: 10px"
              type="primary"
              @click="upFile(2)"
              size="small"
              >选择审定</a-button
            >
          </div>
        </a-form-item>
        <a-form-item
          label="工程名称"
          name="projectName"
          v-if="showType.projectType == 'yusuan'"
          :rules="[
            {
              required: true,
              message: '请输入工程名称!',
            },
          ]"
        >
          <a-input
            v-model:value="inputData.projectName"
            placeholder="工程名称"
            @input="
              () => {
                inputData.projectName = YSremoveSpecialChars(
                  inputData.projectName
                );
              }
            "
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 21 }">
          <a-button
            type="primary"
            :loading="spinning || loading"
            html-type="submit"
            :disabled="!inputData.submissionDocuments || !inputData.projectName"
            >立即新建</a-button
          >
          <a-button
            style="margin-left: 10px"
            type="link"
            @click="comparisonSettings"
            :disabled="
              !inputData.submissionDocuments || !inputData.approvedDocuments
            "
            >对比匹配设置</a-button
          >
        </a-form-item>
      </a-form>
    </div>
    <!--    <div class="recentlyUsedProjects">-->
    <!--      <a-row>-->
    <!--        <a-col class="gutter-row" :span="7">      -->
    <!--          <label for="">最近使用项目：</label>-->
    <!--        </a-col>-->
    <!--      </a-row>-->
    <!--      <a-list-->
    <!--            class="demo-loadmore-list"-->
    <!--            style="margin:0px 30px"-->
    <!--            :loading="initLoading"-->
    <!--            item-layout="horizontal"-->
    <!--            :data-source="recentlyUsedList"-->
    <!--          >-->
    <!--           &lt;!&ndash; <template #loadMore>-->
    <!--            <div-->
    <!--              v-if="!initLoading"-->
    <!--              :style="{ textAlign: 'center', marginTop: '12px', height: '32px', lineHeight: '32px' }"-->
    <!--            >-->
    <!--            </div>-->
    <!--          </template> &ndash;&gt;-->
    <!--          <template #renderItem="{ item }">-->
    <!--            <a-list-item>-->
    <!--              <template #actions>-->
    <!--                <a key="list-loadmore-edit" @click="opnefile(item)">打开</a>-->
    <!--              </template>-->
    <!--              &lt;!&ndash; <a-skeleton :title="false" active> &ndash;&gt;-->
    <!--                <a-list-item-meta-->
    <!--                >-->
    <!--                  <template #title>-->
    <!--                    {{ item.constructName+".YSH" }}-->
    <!--                  </template>-->
    <!--                </a-list-item-meta>-->
    <!--                &lt;!&ndash; <div>content</div> &ndash;&gt;-->
    <!--              &lt;!&ndash; </a-skeleton> &ndash;&gt;-->
    <!--            </a-list-item>-->
    <!--          </template>-->
    <!--        </a-list>-->
    <!--    </div>-->
  </div>
  <prompted
    v-model:visible="isShowModel"
    title="系统提示"
    descTextLeft="创建失败："
    :descTextRight="descTextRight"
    @determine="determine"
    @cancel="cancel('xtts')"
  ></prompted>
  <!-- 对比匹配设置 -->
  <common-modal
    v-model:modelValue="comparisonSettingsModel"
    className="dialog-comm"
    title="对比匹配设置"
    width="350"
    height="300"
  >
    <div class="comparisonSettings">
      <div class="matchingRules">匹配规则：</div>
      <a-checkbox-group
        name="checkboxgroup"
        v-model:value="state.matchingSettings"
        :options="plainOptions"
      />
      <div class="footer">
        <a-button type="primary" @click="csdetermine">确定</a-button>
        <a-button type="info" style="margin-left: 10px" @click="cancel('dbpp')"
          >取消</a-button
        >
      </div>
    </div>
  </common-modal>
  <project-matching-model
    v-if="projectMatchingVisible"
    v-model:pmVisible="projectMatchingVisible"
    @pmSubmitData="pmSubmitData"
    :constructId="constructId"
    :matchingSettings="state.matchingSettings"
  ></project-matching-model>
  <a-upload
    class="file-uploadFile-audit"
    name="file"
    :maxCount="1"
    :showUploadList="false"
    :customRequest="uploadFile"
    :before-upload="beforeUpload"
    accept=".YSF"
    v-show="false"
  >
  </a-upload>
</template>

<script setup>
import projectMatchingModel from './projectMatchingModel.vue';
import prompted from './prompted.vue';
import budgetReview from '../../api/budgetReview';
import {
  getCurrentInstance,
  onMounted,
  reactive,
  toRaw,
  watch,
  ref,
  useAttrs,
} from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { ipcApiRoute } from '@/api/main';
import { YSremoveSpecialChars } from '@/utils/index';
import { proModelStore } from '@/store/proModel';
const store = proModelStore();
const plainOptions = [
  { label: '清单12位编码', value: '01' },
  { label: '清单前9位编码', value: '02' },
  { label: '清单名称', value: '03' },
  { label: '清单项目特征', value: '04' },
];
const state = reactive({
  matchingSettings: ['02', '03'],
});
const attrs = useAttrs();
const emits = defineEmits(['closeDialog', 'comparativeMatching']);
const form = ref();
const descTextRight = ref('');
const isShowModel = ref(false);
const projectMatchingVisible = ref(false);
const comparisonSettingsModel = ref(false);

const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;

const router = useRouter();
const showType = defineProps({
  projectType: {
    type: String, //类型字符串
    default: 'yusuan',
  },
});
const colStyle = reactive({
  colSize: null,
  labelCol: {
    span: 7,
  },
  wrapperCol: {
    span: 15,
  },
});
const loading = ref(false);
const initLoading = ref(false);
const spinning = ref(false);
const recentlyUsedList = ref([]);
const constructId = ref(null);
let type = ref(0); // 文件选择类型
const inputData = reactive({
  ssFilePath: '', // 送审文件地址
  submissionDocuments: '', // 送审文件名称
  approvedDocuments: '', // 审定文件名称
  sdFilePath: '', // 审定文件地址
  projectName: '', // 工程名称
});

const onSubmit = () => {
  console.log('是否进来提交。。。。。', !verify());
  //此处应该将vueX里面的弹框数据置为true
  if (!verify()) {
    return false;
  }
  console.log('11111111111111');
  const iptData = toRaw(inputData);
  console.log('22222222222', iptData);
  let formData = {
    matchingSettings: state.matchingSettings.join(','),
    ...iptData,
  };
  console.log(formData, 'formData11111');
  spinning.value = true;
  budgetReview['shNewProject'](formData)
    .then(function (response) {
      console.log(response, '立即新建');
      if (response.status === 200) {
        // reset();
        // emits('closeDialog');
        if (response.result.type === 'open') {
          // 打开项目匹配
          constructId.value = response.result.constructId;
          projectMatchingVisible.value = true;
        } else if (response.result.type === 'skip') {
          constructId.value = response.result.constructId;
          pmSubmitData(response.result.constructId);
        } else if (response.result.type === 'TypeError') {
          // 弹框提示
          descTextRight.value = '送审和审定不是同类文件，请重新上传。';
          isShowModel.value = true;
        } else {
          // 关闭弹框
          emits('closeDialog');
        }
        store.SET_Refresh(true);
      } else {
        message.error(response.message);
      }
    })
    .finally(err => {
      console.log(err, 'err');
      spinning.value = false;
    });
};
// 项目匹配确认按钮
const pmSubmitData = val => {
  console.log(val, '点击确认时的constructId');
  projectMatchingVisible.value = false;
  emits('closeDialog');
  emits('comparativeMatchingModel', val);
};
watch(
  () => showType.projectType,
  oldVal => {
    loading.value = false;
    spinning.value = false;
    form.value.clearValidate();
    reset();
  }
);
//submit提交内容校验
const verify = () => {
  if (!inputData.submissionDocuments) {
    message.error('请选择送审文件！');
    return false;
  }
  if (!inputData.projectName) {
    message.error('请输入工程名称！');
    return false;
  }
  return true;
};
// input框输入值置为空
const reset = () => {
  for (let key in inputData) {
    // 定额和清单不清空
    // if (!['mainfest', 'ration'].includes(key)) {
    inputData[key] = null;
    // }
  }
};

const opnefile = val => {
  let formData = {
    constructName: val.constructName,
    path: val.path,
  };
  budgetReview['shOpenProject'](formData)
    .then(function (response) {
      if (response.code !== 200) {
        descTextRight.value = '工程审核文件未找到！';
        isShowModel.value = true;
        getRecentlyUsedProjects();
      }
    })
    .finally(err => {
      console.log(err, 'err');
    });
  // isShowModel.value=true
};
const comparisonSettings = () => {
  comparisonSettingsModel.value = true;
};

// 上传前置判断
const beforeUpload = file => {
  console.log('file', file);
  const fileType = file.name
    .substring(file.name.lastIndexOf('.') + 1)
    .toUpperCase();
  const suffix = file.name.split('.').slice(0, -1).join('.');
  console.log('filetype', fileType);
  if (!['YSF'].includes(fileType)) {
    message.error('上传文件格式不正确!');
    return false;
  }
  if (type.value === 1) {
    inputData.ssFilePath = file.path;
    inputData.submissionDocuments = suffix;
  } else {
    inputData.sdFilePath = file.path;
    inputData.approvedDocuments = suffix;
  }
  inputData.projectName = projectName();
  return true;
};

// type 1 送审文件 2 审定文件
const upFile = val => {
  if (loading.value || spinning.value) return;
  type.value = val;
  document.querySelector('.file-uploadFile-audit .ant-upload input').click();
};

/**
 * @description: 文件上传
 * @param {*} params
 * @return {*}
 */
const uploadFile = async params => {
  loading.value = true;
  const { file } = params;
  const name = file.name.substring(0, file.name.lastIndexOf('.'));
  loading.value = false;
  inputData.importUrl = file.path;
  if (!inputData.constructName) {
    inputData.constructName = name;
    form.value?.clearValidate('constructName');
    form.value?.validateFields('constructName');
  }
};

const projectName = type => {
  if (type === 'ss' && inputData.approvedDocuments) {
    return inputData.projectName;
  }
  // 审定文件
  if (inputData.approvedDocuments) {
    return inputData.approvedDocuments.substring(0, 50) + '(审核)';
  }
  // 送审文件
  if (inputData.submissionDocuments) {
    return inputData.submissionDocuments.substring(0, 50) + '(审核)';
  }
  return '';
};
const determine = () => {
  isShowModel.value = false;
};
// 对比设置确认按钮
const csdetermine = () => {
  comparisonSettingsModel.value = false;
};
const cancel = val => {
  switch (val) {
    case 'xtts':
      isShowModel.value = false;
      break;
    case 'dbpp':
      comparisonSettingsModel.value = false;
      break;
    default:
      break;
  }
};
</script>
<style lang="scss" scoped>
::v-deep .ant-button {
  margin: auto !important;
}
::v-deep .ant-select {
  text-align: initial !important;
}
// 增加计税方式后的样式
.newClass {
  ::v-deep .ant-form-item {
    margin: 0 0 8px 20px;
  }
}

::v-deep .ant-form-item .ant-upload {
  width: 100% !important;
  button {
    width: 100% !important;
    text-align: left !important;
    color: #c0c0c0;
  }
}

.content {
  padding-top: 30px;
  font-size: 14px;
  text-align: center;
  height: 95%;
  position: relative;
  // padding-left: 25px;
}
.ys-flex {
  display: flex;
  align-items: center;
}
.recentlyUsedProjects {
  margin-top: 20px;
}
.comparisonSettings {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .matchingRules {
    font-size: 16px;
    font-weight: bold;
  }
  .footer {
    text-align: end;
  }
  ::v-deep .ant-checkbox-wrapper {
    display: flex;
    margin-top: 10px;
  }
  ::v-deep .ant-checkbox-group {
    margin-left: 20px;
  }
}
</style>
