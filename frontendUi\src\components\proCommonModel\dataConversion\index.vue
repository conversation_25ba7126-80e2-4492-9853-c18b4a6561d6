<!--
 * @Descripttion:
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2024-03-18 19:16:28
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-01 16:09:39
-->
<template>
  <common-modal
    v-model:modelValue="show"
    className="dialog-comm"
    title="数据转换"
    :width="1000"
    height="auto"
    @close="close"
  >
    <div class="dataConversion">
      <div class="dataConversion-table">
        <vxe-table
          border
          height="500"
          stripe
          :loading="btnLoading"
          ref="vexTable"
          class="table-scrollbar table-edit-common"
          :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
          :column-config="{ resizable: true }"
          :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parentId',
            line: true,
            showIcon: false,
            expandAll: true,
          }"
          :row-class-name="rowClassName"
          :cell-class-name="cellClassName"
          :data="tableData"
        >
          <vxe-column type="checkbox" width="60">
            <template #header="{ checked, indeterminate }">
              <span
                class="custom-checkbox"
                @click.stop="toggleAllCheckboxEvent"
              >
                <i v-if="indeterminate" class="vxe-icon-square-minus-fill"></i>
                <i v-else-if="checked" class="vxe-icon-square-checked-fill"></i>
                <i v-else class="vxe-icon-checkbox-unchecked"></i>
              </span>
            </template>
            <template #checkbox="{ row, checked, indeterminate }">
              <span
                v-if="row.kind != '0'"
                class="custom-checkbox"
                @click.stop="toggleCheckboxEvent(row)"
              >
                <i v-if="indeterminate" class="vxe-icon-square-minus-fill"></i>
                <i v-else-if="checked" class="vxe-icon-square-checked-fill"></i>
                <i v-else class="vxe-icon-checkbox-unchecked"></i>
              </span>
            </template>
          </vxe-column>
          <vxe-column field="change" width="50" title="">
            <template #default="{ row }">
              <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
                shChangeLabel(row.ysshSysj?.change).label
              }}</span>
            </template>
          </vxe-column>
          <vxe-colgroup title="送审">
            <vxe-column
              field="ysshSysj.bdCode"
              tree-node
              title="项目编码"
              width="160"
              align="left"
              headerAlign="center"
            >
              <template #default="{ row }">
                <span class="code">{{ row.ysshSysj?.bdCode }} </span>
              </template>
            </vxe-column>
            <vxe-column field="ysshSysj.name" title="项目名称" width="260">
              <template #default="{ column, row, $columnIndex }">
                <div>
                  {{ row.ysshSysj?.name }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="ysshSysj.unit" title="单位" width="60">
              <template #default="{ column, row, $columnIndex }">
                <div>
                  {{ row.ysshSysj?.unit }}
                </div>
              </template>
            </vxe-column>
            <vxe-column
              field="ysshSysj.projectAttr"
              title="项目特征"
              width="300"
            >
              <template #default="{ column, row, $columnIndex }">
                <div class="project-attr">
                  {{ row.ysshSysj?.projectAttr }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="ysshSysj.quantity" title="工程量" width="120">
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.quantity }}</span>
              </template>
            </vxe-column>
            <vxe-column field="ysshSysj.price" title="综合单价" width="120">
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.price }}</span>
              </template>
            </vxe-column>
            <vxe-column field="ysshSysj.total" title="综合合价" width="120">
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.total }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup title="审定">
            <vxe-column
              field="bdCode"
              title="项目编码"
              width="160"
              align="left"
              headerAlign="center"
            >
            </vxe-column>
            <vxe-column field="name" title="项目名称" width="260"> </vxe-column>
            <vxe-column field="unit" title="单位" width="60"></vxe-column>
            <vxe-column field="projectAttr" title="项目特征" width="300">
            </vxe-column>
            <vxe-column field="quantity" title="工程量" width="120">
            </vxe-column>
            <vxe-column field="price" title="综合单价" width="120"></vxe-column>
            <vxe-column field="total" title="综合合价" width="120"></vxe-column>
          </vxe-colgroup>
        </vxe-table>
      </div>
    </div>
    <div class="footer">
      <a-button
        type="primary"
        :loading="btnLoading"
        :disabled="!isCheckedTableData"
        @click="saveData"
        >确定</a-button
      >
      <a-button @click="close">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch, nextTick, toRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import shApi from '@/api/shApi';
import { useRoute } from 'vue-router';
import { shChangeLabel } from '@/utils/index';

import { useCellClick } from '@/hooks/useCellClick';

const { useCellClickEvent, selectedClassName } = useCellClick();
const { ipcRenderer, webFrame } = require('electron');
const route = useRoute();
import { message } from 'ant-design-vue';
const store = projectDetailStore();
const btnLoading = ref(false);
const tableData = ref([]);
const isCheckedTableData = ref(false);
const vexTable = ref();

const props = defineProps({
  visible: {
    type: Boolean,
  },
  convertToType: {
    type: String,
  },
});
const emit = defineEmits(['update:visible']);
const show = computed({
  get: () => props.visible,
  set: val => {
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      init();
    }
  }
);
const init = () => {
  getListData();
};
const isFbfx = () => {
  return store.tabSelectName === '分部分项';
};
const isCsxm = () => {
  return store.tabSelectName === '措施项目';
};
const getListData = () => {
  const { constructId, singleId, ssConstructId, ssSingleId } =
    store.currentTreeGroupInfo;
  const { id, ysshUnitId } = store.currentTreeInfo;
  let apiData = {
    constructId,
    singleId,
    unitId: id,
    sequenceNbr: store.asideMenuCurrentInfo?.sequenceNbr,
    pageSize: 300000,
    pageNum: 1,
    ssConstructId,
    ssSingleId,
    ssUnitId: ysshUnitId,
    isDisplayAllData: true,
  };
  let apiFun = isCsxm() ? shApi.csxmListSearch : shApi.fbfxDataPiPeiColl;
  console.log(apiFun);
  apiFun(apiData).then(res => {
    console.log('数据转化：', res.result);
    if (res.status === 200 && res.result) {
      tableData.value = res.result.data?.filter(item => {
        if (props.convertToType === '01' && item.ysshSysj?.change !== 1) {
          return true;
        }
        if (props.convertToType === '02' && item.ysshSysj?.change !== 2) {
          return true;
        }
        return false;
      });

      nextTick(() => {
        vexTable.value?.setAllTreeExpand(true);
      });
    }
  });
};
const rowClassName = ({ row }) => {
  if (row.kind === '0') {
    return 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    return 'row-sub';
  } else if (row.kind === '03') {
    return 'row-qd';
  }
  return null;
};
const cellClassName = ({ column, row, $columnIndex }) => {
  let className = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'bdCode') {
    className += ' code-color';
  } else if (column.field === 'index') {
    className += ' index-bg';
  }
  return className;
};
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log(row, 'row');
  return true;
};
const close = () => {
  show.value = false;
};
const refreshParentList = () => {
  store.$state.mainContentRefresh = false;
  setTimeout(() => {
    store.$state.mainContentRefresh = true;
  }, 10);
  // if (store.componentId === 'subItemProject') {
  //   store.subItemProjectAutoPosition?.posRow('', 'Refresh');
  // }
  // if (store.componentId === 'measuresItem') {
  //   store.measuresItemProjectAutoPosition?.posRow('', 'Refresh');
  // }
};
const saveData = () => {
  btnLoading.value = true;
  let tableData = vexTable.value.getCheckboxRecords();
  const { constructId, singleId, ssConstructId, ssSingleId } =
    store.currentTreeGroupInfo;
  const { id, ysshUnitId } = store.currentTreeInfo;
  const replaceIds = tableData.map(item => item.sequenceNbr);
  let apiFun =
    props.convertToType === '01'
      ? shApi.ssToSdDataConvert
      : shApi.sdToSsDataConvert;
  const params = {
    constructId,
    singleId,
    unitId: id,
    ssConstructId,
    ssSingleId,
    ssUnitId: ysshUnitId,
    type: isCsxm() ? 'csxm' : isFbfx() ? 'fbfx' : '',
    replaceIds,
  };
  console.log(tableData, props.convertToType, params, apiFun);
  apiFun(params).then(res => {
    console.log(res);
    if (res.code === 200) {
      btnLoading.value = false;
      refreshParentList();
      message.success('操作成功');
      close();
    }
  });
};

const toggleAllCheckboxEvent = () => {
  const $table = vexTable.value;
  if ($table) {
    $table.toggleAllCheckboxRow();
  }
  isCheckedTableData.value =
    vexTable.value.getCheckboxRecords().length > 0 ? true : false;
};

const toggleCheckboxEvent = row => {
  const $table = vexTable.value;
  if ($table) {
    $table.toggleCheckboxRow(row);
  }
  isCheckedTableData.value =
    vexTable.value.getCheckboxRecords().length > 0 ? true : false;
};
</script>
<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: flex-end;
  position: relative;
  top: 15px;
  :deep(.ant-btn) {
    margin-right: 15px;
  }
}
.dataConversion-table {
  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  :deep(.vxe-table) {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -8px;
      left: 20px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .rotate90:before {
      content: '-';
    }
    .rotate90 {
      transform: rotate(0deg);
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }
}
</style>
