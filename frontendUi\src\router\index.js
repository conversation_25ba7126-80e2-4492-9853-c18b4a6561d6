/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-05-17 20:50:48
 * @LastEditors: sunchen
 * @LastEditTime: 2024-11-29 15:20:19
 */
/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-05-05 15:14:41
 * @LastEditors: k<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-25 00:25:50
 * @LastEditors: sunchen
 * @LastEditTime: 2024-06-19 14:49:37
 */
import { createRouter, createWebHashHistory } from 'vue-router';

const gsProjectDetailLayout = () => import('@/layouts/gsProjectDetail.vue');
const ProjectDetailLayout = () => import('@/layouts/ProjectDetail.vue');
const gljProjectDetailLayout = () => import('@/layouts/gljProjectDetail.vue');
const routes = [
  {
    path: '/',
    name: 'root',
    redirect: '/csProject',
    component: import('@/views/csProject/index.vue'),
    children: [
      {
        path: 'csProject',
        name: 'home',
        component: () =>
          import(/* webpackChunkName: "home" */ '@/views/csProject/index.vue'),
        meta: { title: '工作台' },
      },
    ],
  },
  {
    path: '/csProject',
    name: 'csProject',
    component: () => import('@/views/csProject/index.vue'),
  },
  {
    path: '/index',
    name: 'home',
    component: () => import('/src/views/home/<USER>'),
  },
  {
    path: '/projectDetail',
    name: 'projectDetail',
    component: ProjectDetailLayout,
    redirect: '/projectDetail/customize',
    children: [
      {
        path: 'customize',
        name: 'customize',
        component: () => import('@/views/projectDetail/customize/index.vue'),
      },
      {
        path: 'reportForm',
        name: 'reportForm',
        component: () => import('@/views/projectDetail/reportForm/index.vue'),
      },
      {
        path: 'testtable',
        name: 'testtable',
        component: () => import('@/views/projectDetail/testtable.vue'),
      },
    ],
  },
  /* 概算 */
  {
    path: '/gsProjectDetail',
    name: 'gsProjectDetail',
    component: gsProjectDetailLayout,
    redirect: '/gsProjectDetail/customize',
    children: [
      {
        path: 'customize',
        name: 'gscustomize',
        component: () =>
          import('@/gaiSuanProject/views/projectDetail/customize/index.vue'),
      },
      {
        path: 'reportForm',
        name: 'gsreportForm',
        component: () =>
          import('@/gaiSuanProject/views/projectDetail/reportForm/index.vue'),
      },
      {
        path: 'testtable',
        name: 'testtable',
        component: () => import('@/views/projectDetail/testtable.vue'),
      },
      {
        path: 'testtableTree',
        name: 'testtableTree',
        component: () => import('@/views/projectDetail/testtabletree.vue'),
      },
    ],
  },
  /* 概算 */
  {
    path: '/gljProjectDetail',
    name: 'gljProjectDetail',
    component: gljProjectDetailLayout,
    redirect: '/gljProjectDetail/customize',
    children: [
      {
        path: 'customize',
        name: 'gljcustomize',
        component: () =>
          import('@/gongLiaoJiProject/views/projectDetail/customize/index.vue'),
      },
      {
        path: 'reportForm',
        name: 'gljreportForm',
        component: () =>
          import(
            '@/gongLiaoJiProject/views/projectDetail/reportForm/index.vue'
          ),
      },
      {
        path: 'testtable',
        name: 'testtable',
        component: () => import('@/views/projectDetail/testtable.vue'),
      },
      {
        path: 'testtableTree',
        name: 'testtableTree',
        component: () => import('@/views/projectDetail/testtabletree.vue'),
      },
    ],
  },
  {
    path: '/demo',
    name: 'demo',
    component: () => import('/src/views/base/index.vue'),
  },
  // {
  //     path: '/:catchAll(.*)',
  //     name: '404',
  //     component: () => import('/src/views/error/404.vue')
  // }
  {
    path: '/',
    name: 'root',
    redirect: '/csProject',
    component: import('@/views/csProject/index.vue'),
    children: [
      {
        path: 'csProject',
        name: 'home',
        component: () =>
          import(/* webpackChunkName: "home" */ '@/views/csProject/index.vue'),
        meta: { title: '工作台' },
      },
    ],
  },
  {
    path: '/csProject',
    name: 'csProject',
    component: () => import('@/views/csProject/index.vue'),
  },
  {
    path: '/index',
    name: 'home',
    component: () => import('/src/views/home/<USER>'),
  },
  {
    path: '/projectDetail',
    name: 'projectDetail',
    component: ProjectDetailLayout,
    redirect: '/projectDetail/customize',
    children: [
      {
        path: 'customize',
        name: 'customize',
        component: () => import('@/views/projectDetail/customize/index.vue'),
      },
      {
        path: 'reportForm',
        name: 'reportForm',
        component: () => import('@/views/projectDetail/reportForm/index.vue'),
      },
      {
        path: 'analysisAndReporting',
        name: 'analysisAndReporting',
        component: () =>
          import(
            '@/yuSuanShenHe/views/projectDetail/reportForm/analysisAndReporting.vue'
          ),
      },
      {
        path: 'reportFormSh',
        name: 'reportFormSh',
        component: () =>
          import('@/yuSuanShenHe/views/projectDetail/reportForm/index.vue'),
      },
      {
        path: 'reportFormJieSuan',
        name: 'reportFormJieSuan',
        component: () =>
          import('@/jieSuan/views/projectDetail/reportForm/index.vue'),
      },
    ],
  },
  {
    path: '/demo',
    name: 'demo',
    component: () => import('/src/views/base/index.vue'),
  },
  // {
  //     path: '/:catchAll(.*)',
  //     name: '404',
  //     component: () => import('/src/views/error/404.vue')
  // }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
