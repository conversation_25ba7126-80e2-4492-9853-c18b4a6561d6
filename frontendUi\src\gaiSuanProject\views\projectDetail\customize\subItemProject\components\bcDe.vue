<template>
  <common-modal
    className="dialog-comm"
    title="补充定额"
    width="auto"
    v-model:modelValue="props.visible"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="form-wrap">
      <div class="form-content">
        <a-spin tip="解析中..." :spinning="spinning">
          <a-form :model="inputData" ref="form" @finish="onSubmit">
            <a-form-item
              label="定额编码"
              name="deCode"
              class="form-item-two"
              :rules="[
                {
                  required: true,
                  message: '请输入定额编码！',
                },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.deCode"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="单位"
              class="form-item-two"
              name="unit"
              :rules="[{ required: true, message: '请选择或输入单位！' }]"
            >
              <vxeTableEditSelect
                :filedValue="inputData.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    saveCustomInput(newValue, inputData, 'unit', $rowIndex);
                  }
                "
              ></vxeTableEditSelect>
            </a-form-item>
            <a-form-item
              label="定额名称"
              name="deName"
              class="form-item-one"
              :rules="[{ required: true, message: '请输入定额名称！' }]"
            >
              <a-input
                v-model:value.trim="inputData.deName"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="所属章节"
              name="path"
              class="form-item-one"
              :rules="[{ required: true, message: '请选择所属章节！' }]"
            >
              <a-input
                v-model:value.trim="inputData.path"
                @click="showChapter"
                placeholder="请输入"
              >
                <template #suffix>
                  <DownOutlined
                    :style="{
                      fontSize: '12px',
                      color: 'rgba(0, 0, 0, 0.25)',
                    }"
                  />
                </template>
              </a-input>
            </a-form-item>
            <select-chapter
                v-model:filedValue="inputData.libraryCode"
                v-model:visible="chapterStatus"
                :treeData="treeData"
                :groupTypeList="groupTypeList"
                :isDeType="1"
                @selectChange="selectChange"
                @selectInfo="selectInfo"
              ></select-chapter>
            <a-form-item label="人工费(元)" name="rfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.rfee"
                @blur="
                  inputData.rfee = removeSpecialCharsFromPrice(inputData.rfee)|| 0
                "
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item label="材料费(元)" name="cfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.cfee"
                placeholder="请输入"
                @blur="
                  inputData.cfee = removeSpecialCharsFromPrice(inputData.cfee)|| 0
                "
              />
            </a-form-item>
            <a-form-item label="机械费(元)" name="jfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.jfee"
                placeholder="请输入"
                @blur="
                  inputData.jfee = removeSpecialCharsFromPrice(inputData.jfee)|| 0
                "
              />
            </a-form-item>

            <a-form-item label="主材费(元)" name="zcfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.zcfee"
                placeholder="请输入"
                @blur="
                  inputData.zcfee = removeSpecialCharsFromPrice(inputData.zcfee)|| 0
                "
              />
            </a-form-item>

            <a-form-item label="设备费(元)" name="sbfee" class="form-item-one">
              <a-input
                v-model:value.trim="inputData.sbfee"
                placeholder="请输入"
                @blur="
                  inputData.sbfee = removeSpecialCharsFromPrice(inputData.sbfee)|| 0
                "
              />
            </a-form-item>
            <a-form-item class="form-item-one">
              <div class="footer-btn-list">
                <a-button @click="cancel">取消</a-button>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="spinning || loading"
                  >新建</a-button
                >
              </div>
            </a-form-item>
          </a-form>
        </a-spin>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import {
  getCurrentInstance,
  ref,
  reactive,
  watch,
  onMounted,
  toRaw,
  toRefs,
  defineAsyncComponent,
} from 'vue';
import {
  isNumericExpression,
  everyNumericHandler,
  quantityExpressionHandler,
} from '@/utils/index';
import api from '@/gaiSuanProject/api/projectDetail';
import { DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import infoMode from '@/plugins/infoMode';
import { removeSpecialCharsFromPrice } from '@/utils/index';

const selectChapter = defineAsyncComponent(() =>
  import('@/components/gsSelectChapter/index.vue')
);

const projectStore = projectDetailStore();

const checkQuantityExpression = (rule, value) => {
  if (value === null || !value.length) return Promise.resolve();
  const [isSuccess, msg] = quantityExpressionHandler(inputData);
  if (isSuccess) {
    return Promise.reject(msg);
  }
  return Promise.resolve();
};

const expressionBlur = () => {
  inputData.quantityExpression = everyNumericHandler(
    inputData.quantityExpression
  );
};

const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emit = defineEmits(['update:visible', 'onSuccess', 'deSaveData']);
const store = projectDetailStore();

let chapterStatus = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  code: {
    type: String,
    default: '',
  },
  currentInfo: {
    type: Object,
    default: null,
  },
  type: {
    type: Number,
    default: 1,
  },
});

const colStyle = reactive({
  colSize: null,
});

const inputData = reactive({
  deCode: null, //项目编码
  deName: null, //项目名称
  quantityExpression: null, // 工程量表达式
  unit: null, // 单位
  classifyLevel1: null, // 定额章节1
  classifyLevel2: null, // 定额章节2
  classifyLevel3: null, // 定额章节3
  classifyLevel4: null, // 定额章节4
  rfee: null, // 人工费
  cfee: null, // 材料费
  jfee: null, // 机械费
  zcfee: null, // 主材费
  sbfee: null, // 设备费
  path: null, // 选择的定额章节
});
let groupTypeList = ref([]);
let treeData = ref([]);
let expandedKeys = ref([]);

onMounted(() => {});

// 章节点击展示弹窗
const showChapter = () => {
  chapterStatus.value = true;
};
// input框输入值置为空
const reset = () => {
  loading.value = false;
  for (let key in inputData) {
    inputData[key] = null;
  }
};

const cancel = () => {
  emit('bcCancel', 2);
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

watch(
  () => props.visible,
  (val, oldVal) => {
    if (val) {
      form.value?.resetFields();
      reset();
      if(props.code){
        inputData.deCode = props.code;
      }else{
        getDefaultCode()
      }
      inputData.libraryCode = store.currentTreeInfo.constructMajorType;
      queryDeLibrary();
    }
  }
);

const getDefaultCode = () => {
  api
    .getUserDeDefaultCode({
      constructId: store.currentTreeGroupInfo?.constructId,
      prefix: '补充子目',
      unitId: store.currentTreeInfo?.id,
    })
    .then(res => {
      console.log('补充子目' , res.result)
      inputData.deCode = '补充子目' + res.result;
    });
};
/**
 * 获取定额树数据
 */
const getDeList = code => {
  api.deListByTree({libraryCode:code}).then(res => {
    console.log('获取定额树结构', res,code,store.currentTreeInfo);
    treeData.value = getDeInitData([res.result]);
    const firstChildOfFirstDirectory = findFirstChild(
      treeData.value[0]?.childrenList
    );
    console.log('firstChildOfFirstDirectory', firstChildOfFirstDirectory);
    inputData.path = firstChildOfFirstDirectory.path;
    selectInfo(firstChildOfFirstDirectory);
  });
};

const getDeInitData = tree => {
  return tree.map(item => {
    item.key = item.name + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.name;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getDeInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.name + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.name) + '/' + n.name,
          }))
        )
      : null;
    return item;
  });
};

const findFirstChild = tree => {
  if (tree && tree.length > 0) {
    let item = tree.filter(
      x =>
        store.currentTreeInfo.secondInstallationProjectName &&
        x.name.includes(store.currentTreeInfo.secondInstallationProjectName)
    )[0];
    for (const node of tree) {
      if (item) {
        return findFirstChild(item.childrenList);
      } else {
        if (node.childrenList && node.childrenList.length > 0) {
          return findFirstChild(node.childrenList);
        }
      }
    }
    return tree[0]; // 找到第一个目录下的第一个子集
  }
  return null; // 没有子节点或者树为空
};

const queryDeLibrary = () => {
  api.deListByTree({libraryCode:store.currentTreeInfo.constructMajorType}).then(res => {
    console.log('queryDeLibrary',res)
    if (res.status === 200) {
      groupTypeList.value = [res.result].map(a=>{return {...a,libraryName:a.name,libraryCode:store.currentTreeInfo.constructMajorType}});
      getDeList(store.currentTreeInfo.constructMajorType);
    }
  });
};

const selectChange = value => {
  console.log('selectChange', value);
  getDeList(value);
};

const selectInfo = obj => {
  console.log('obj', obj);
  let pathList = obj.path.split('/');
  pathList.forEach((item, index) => {
    inputData[`classifyLevel${index}`] = item;
  });
  inputData.path = obj.path;
};

const onSubmit = () => {
  loading.value = true;
  console.log('inputDAta', inputData);
  isStandardDe();
};

// 判断输入的定额编码是否为主定额库编码
const isStandardDe = () => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    code: inputData.deCode,
  };
  api.isStandardDe(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '定额编码不可与标准定额编码相同，请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      } else {
        emit('deSaveData', inputData);
      }
    }
  });
};
</script>
<style lang="scss" scoped>
@import './style.scss';
</style>
