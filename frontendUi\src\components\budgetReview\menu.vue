<!--
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-20 10:57:49
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-14 10:29:14
-->
<template>
  <ul class="menu-list">
    <li
      v-for="(item, index) in menuList"
      :key="item.name"
      @click="handleSelect(item, index)"
      :class="{ on: item.key === otherActive, child: item.key !== 3 }"
    >
      <a-tooltip placement="right" :title="item.name">
        <span class="name-content">
          <!-- 设置取费文件标示 -->
          {{ item.name }}</span
        >
      </a-tooltip>
    </li>
  </ul>
</template>
<script setup>
import { ref } from 'vue';
// 31 暂列金 32 专业暂估价 33 总承包费用 34 计日工,
const categoryIndex = ref(0);
const props = defineProps({
  otherActive: {
    type: Number, //类型字符串
  },
});
const emit = defineEmits(['update:otherActive']);
const menuList = [
  {
    name: '其他项目',
    key: 3,
  },
  {
    name: '暂列金额',
    key: 31,
  },
  {
    name: '专业工程暂估价',
    key: 32,
  },
  {
    name: '总承包服务费',
    key: 33,
  },
  {
    name: '计日工',
    key: 34,
  },
];
const handleSelect = (item, index) => {
  categoryIndex.value = index;
  emit('update:otherActive', item.key);
};
</script>
<style lang="scss" scoped>
.menu-list {
  width: 140px;
  list-style: none;
  padding: 1px 0px 9px 0px;
  li {
    text-align: left;
    // margin: 0 0 6px 6px;
    margin: 0 0 1px 6px;

    cursor: pointer;
    .name-content {
      display: block;
      padding: 0 20px;

      line-height: 1.6;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }
    &:hover {
      background-color: #dae7f4;
    }
  }
}
.child {
  margin-left: 20px !important;
}
.on {
  background-color: #deeaff;
}
</style>
