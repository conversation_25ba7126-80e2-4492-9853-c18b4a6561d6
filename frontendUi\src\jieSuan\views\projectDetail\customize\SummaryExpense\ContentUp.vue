<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-13 14:47:43
-->
<template>
  <div class="table-content">
    <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="tableData"
      height="auto"
      ref="upTable"
      border="full"
      keep-source
      @edit-closed="editClosedEvent"
      @current-change="currentChangeEvent"
      :menu-config="menuConfig"
      :row-class-name="rowClassName"
      @menu-click="contextMenuClickEvent"
      :cell-class-name="cellClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      class="table-edit-common trends-table-column"
      @cell-click="useCellClickEvent"
      :header-cell-class-name="setHeaderCellClassName"
    >
      <vxe-column
        v-for="columns of handlerColumns"
        v-bind="columns"
        :key="columns.id"
      >
        <template #header="{ column }">
          <span class="custom-header">
            <span>{{ column.title }}</span>
            <CloseOutlined
              class="icon-close"
              @click="closeColumn({ column })"
            />
          </span>
        </template>
        <template
          v-if="columns.slot"
          #default="{ column, row, $columnIndex }"
        >
          <template v-if="columns.field === 'whetherPrint'">
            <vxe-checkbox
              v-model="row.whetherPrint"
              size="small"
              content=""
              :checked-value="1"
              :unchecked-value="0"
              @change="update(row, 'whetherPrint')"
            ></vxe-checkbox>
          </template>
          <template v-if="columns.field === 'calculateFormula'">
            <icon-font
              type="icon-bianji"
              class="more-icon"
              @click.stop="editCalc(row)"
            ></icon-font>
            <div>{{ row.calculateFormula }}</div>
          </template>
          <template v-if="!['calculateFormula','whetherPrint'].includes(columns.field)">
            {{ row[column.field] }}
          </template>
        </template>
        <template
          v-if="columns.slot"
          #edit="{ column, row, $columnIndex }"
        >
          <template v-if="columns.field == 'dispNo'">
            <vxe-input
              :clearable="false"
              v-model.trim="row.dispNo"
              type="text"
              @blur="clear()"
              @keyup="row.dispNo = row.dispNo.replace(/[^\w.]/g, '')"
            ></vxe-input>
          </template>
          <template v-else-if="columns.field == 'code'">
            <vxe-input
              :clearable="false"
              v-model.trim="row.code"
              type="text"
              @blur="clear()"
              @keyup="row.code = row.code.replace(/[^\w_]/g, '')"
            ></vxe-input>
          </template>
          <template v-else-if="columns.field == 'name'">
            <cell-textarea
              v-if="row.whetherTax !== 1"
              :clearable="false"
              v-model.trim="row.name"
              @blur="clear()"
              placeholder="请输入名称"
              :textHeight="row.height"
              @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')"
            ></cell-textarea>
            <span v-else>{{ row.name }}</span>
          </template>
          <template v-else-if="columns.field == 'calculateFormula'">
            <cell-textarea
              :clearable="false"
              v-model.trim="row.calculateFormula"
              placeholder="请输入计算基数"
              :textHeight="row.height"
              @blur="clear()"
              @keyup="
                row.calculateFormula = row.calculateFormula.replace(
                  /[^\w\-\+\*\/]/g,
                  ''
                )
              "
            ></cell-textarea>
          </template>
          <template v-else-if="columns.field == 'instructions'">
            <cell-textarea
              :clearable="false"
              v-model.trim="row.instructions"
              placeholder="请输入基数说明"
              :textHeight="row.height"
              @blur="clear()"
              @keyup="
                row.instructions = row.instructions.replace(
                  /[^\w\-\+\*\/]/g,
                  ''
                )
              "
            ></cell-textarea>
          </template>
          <template v-else-if="columns.field == 'rate'">
            <vxe-input
              :clearable="false"
              v-model="row.rate"
              @blur="clear()"
              @keyup="row.rate = pureNumber(row.rate)"
            ></vxe-input>
          </template>
          <template v-else-if="columns.field == 'remark'">
            <cell-textarea
              :clearable="false"
              v-model.trim="row.remark"
              @blur="clear()"
              placeholder="请输入备注"
              :textHeight="row.height"
            ></cell-textarea>
          </template>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModel = false"
    :mask="false"
    style="position: releative"
  >
    <content-down
      :isTextArea="isTextArea"
      :textValue="textValue"
      ref="comArea"
    ></content-down>
    <span class="btns">
      <a-button @click="cancelData()">取消</a-button>
      <a-button
        type="primary"
        @click="sureData()"
      >确定</a-button>
    </span>
  </common-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  onActivated,
  getCurrentInstance,
} from 'vue';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { getUrl } from '@/utils/index';
import ContentDown from '@/views/projectDetail/customize/summaryExpense/ContentDown.vue';
import { Modal } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
import { tableColumns, orgTableColumns } from './columns.js';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import { summaryExpenseJs } from '@/views/projectDetail/customize/summaryExpense/comContent.js';
import operateList from '@/views/projectDetail/customize/operate';
let insertTwo = operateList.value.find(item => item.name == 'insert');
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const upTable = ref();
let tableData = ref([]);
let totalFee = ref([]);
let taxMode = ref(); //1-一般计税，2-简易计税
let isCurrent = ref(0);
let comModel = ref(false); //计算基数编写弹框
let isTextArea = ref(true);
let textValue = ref('');
let rowValue = ref('');
let deleteInfo = ref();
let loading = ref(false);
let oldValue = ref('');
const comArea = ref();
let selectData = ref(null); //批量选择的数据
const emits = defineEmits(['getMoveInfo']);
const setMoveInfoObj = () => {
  let obj = {
    isCurrent: isCurrent.value,
    isLast: tableData.value.length - 1 === isCurrent.value,
  };
  console.log('111111111111-obj', obj);
  emits('getMoveInfo', obj);
};
const {
  keyDownOperate,
  validateAndFormatCode,
  editCalc,
  cancelData,
  sureData,
  // getTaxMethods,
  // getTotalFeeCode,
  getTableData,
  clear,
  editClosedEvent,
  update,
  pasteIsDisabled,
  menuConfig,
  getCurrentIndex,
  operate,
  contextMenuClickEvent,
  pasteItem,
  addItem,
  copyItem,
  deleteItem,
} = summaryExpenseJs({
  pageType: 'yusuan',
  upTable: upTable,
  comModel,
  textValue,
  oldValue,
  comArea,
  tableData,
  taxMode,
  totalFee,
  isCurrent,
  loading,
  deleteInfo,
  setMoveInfoObj,
});
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
onMounted(async () => {
  await initColumns({
    columns: store.currentTreeInfo.originalFlag
      ? orgTableColumns.value
      : tableColumns.value,
    pageName: store.currentTreeInfo.originalFlag ? 'HTNfyhz' : 'HTWfyhz',
  });
  if (store.currentTreeInfo.originalFlag) {
    insertTwo.disabled = true;
  } else {
    insertTwo.disabled = false;
  }
  taxMode.value = store.taxMade;
  getTableData();
  setUse();
});
const setUse = () => {
  window.addEventListener('keydown', keyDownOperate);
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
};
onActivated(() => {
  setUse();
});
const currentChangeEvent = ({ $rowIndex }) => {
  isCurrent.value = $rowIndex;
  setMoveInfoObj();
};
const moveDeData = ({ state, type }) => {
  console.log('moveDeData', state, type);
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    index: isCurrent.value,
    move: state === 1 ? 'up' : 'down',
  };
  console.log('apiData', apiData);
  if (type === 'move') {
    feePro.moveUpDown(apiData).then(res => {
      console.log('res移动', res);
      if (res.status === 200) {
        isCurrent.value =
          state === 1 ? isCurrent.value - 1 : isCurrent.value + 1;
        message.success('移动成功');
        getTableData();
      }
    });
  }
};
const props = defineProps({
  isCharu: {
    type: Boolean,
  },
});
watch(
  () => props.isCharu,
  () => {
    operate('insert', {});
  }
);
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  async ([val, oldVal], [newY, oldy]) => {
    if (
      store.tabSelectName === '费用汇总' &&
      store.currentTreeInfo.levelType === 3
    ) {
      isCurrent.value = 0; //切换页面选中行默认选第一行
      getTableData();
      if (store.currentTreeInfo.originalFlag) {
        insertTwo.disabled = true;
      } else {
        insertTwo.disabled = false;
      }
      await initColumns({
        columns: store.currentTreeInfo.originalFlag
          ? orgTableColumns.value
          : tableColumns.value,
        pageName: store.currentTreeInfo.originalFlag ? 'HTNfyhz' : 'HTWfyhz',
      });
    }
  }
);
const headerCellClassName = ({ column }) => {
  if (column.field === 'index') {
    return 'index-bg';
  }
  return null;
};
const rowClassName = ({ row }) => {
  const originalDataClass = '';
  // 量差
  if (row.sourceFlag === '1') {
    return `row-jc ${originalDataClass}`;
  } else {
    return `row-lc ${originalDataClass}`;
  }
  return originalDataClass;
};
const cellClassName = ({ $columnIndex, row, column }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};
const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns();
// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};
defineExpose({
  getTableData,
});
</script>
<style lang="scss" scoped>
.btns {
  position: absolute;
  width: 200px;
  bottom: -20px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.multiple-select {
  width: 50px;
  height: 30px;
  line-height: 30px;
  margin-left: -10px;
  text-indent: 10px;
  cursor: pointer;
}
.table-content {
  // width: 100%;
  height: 100%;
  // overflow: hidden;
  background: #ffffff;
  user-select: none;
  .more-icon {
    display: none;
  }
  ::v-deep(.vxe-table .cell-selected) .more-icon {
    display: block;
  }
  ::v-deep(.vxe-table .row-lc) {
    background: white;
  }

  ::v-deep(.vxe-table .row-jc) {
    background: #e9f7fa;
  }

  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }

  ::v-deep(.vxe-table .index-bg) {
    // background-color: rgba(243, 243, 243, 1);
    background-color: #fff;
  }
}
// }
</style>
