<!--
 * @Descripttion: 记取工程水、电费
 * @Author: liuxia
 * @Date: 2024-04-12 10:22:35
 * @LastEditors: liuxia
 * @LastEditTime: 2024-11-01 17:15:35
-->
<template>
  <div class="utility-bills">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.billVisible"
      :destroyOnClose="false"
      title="计取工程水、电费"
      :mask="spinning"
      :lockView="false"
      :lockScroll="false"
      width="1040px"
      @cancel="close"
      @close="close"
    >
      <div class="dialog-wrap">
        <a-spin :spinning="spinning">
          <div class="contentCenter">
            <vxe-table
              border
              show-footer
              show-overflow
              keep-source
              ref="vexTable"
              height="600"
              align="center"
              :loading="loading"
              :row-config="{ isCurrent: true, keyField: 'id' }"
              :column-config="{ resizable: true }"
              @cell-click="useCellClickEvent"
              :tree-config="{
                transform: true,
                rowField: 'id',
                parentField: 'parentId',
                showIcon: true,
                expandAll: true,
              }"
              :data="customWaterInfo.waterElectricData"
              :edit-config="{
                trigger: 'click',
                mode: 'cell',
                beforeEditMethod: cellBeforeEditMethod,
              }"
              @edit-closed="editClosedEvent"
              class="table-edit-common"
              :cell-class-name="selectedClassName"
              :footer-method="footerMethod"
            >
              <vxe-column tree-node field="name" title="工程专业"></vxe-column>
              <vxe-column
                width="200"
                align="left"
                field="rule"
                title="规则/章节"
              >
                <template #default="{ row }">
                  <vxe-radio
                    v-if="
                      row.selectOptionFlag === 0 || row.selectOptionFlag === 1
                    "
                    v-model="row.selectOptionFlag"
                    :label="1"
                    @change="radioChange(row)"
                    >{{ row.rule }}</vxe-radio
                  >
                  <span v-else>{{ row.rule }}</span>
                </template>
              </vxe-column>
              <vxe-column
                width="200"
                field="calculateBase"
                title="计算基数"
                :edit-render="{}"
              >
                <template #edit="{ row }">
                  <vxe-select
                    v-if="row.calculateBase"
                    v-model="row.calculateBase"
                    transfer
                  >
                    <vxe-option
                      v-for="item in row.calculateBaseOptions"
                      :key="item"
                      :value="item"
                      :label="item"
                    ></vxe-option>
                  </vxe-select>
                </template>
              </vxe-column>
              <vxe-colgroup title="扣除系数">
                <vxe-column
                  field="waterRate"
                  title="水费"
                  :edit-render="{ autofocus: '.vxe-input--inner' }"
                >
                  <template #edit="{ row }">
                    <vxe-input
                      v-if="row.dataFlag === 1"
                      v-model="row.waterRate"
                      placeholder="请输入水费"
                      v-model.trim="row.waterRate"
                      :maxlength="10"
                      clearable
                    ></vxe-input>
                    <span v-else>{{ row.waterRate }}</span>
                  </template>
                </vxe-column>
                <vxe-column
                  field="electricRate"
                  title="电费"
                  :edit-render="{ autofocus: '.vxe-input--inner' }"
                >
                  <template #edit="{ row }">
                    <vxe-input
                      v-if="row.dataFlag === 1"
                      v-model="row.electricRate"
                      placeholder="请输入电费"
                      v-model.trim="row.electricRate"
                      :maxlength="10"
                      clearable
                    ></vxe-input>
                    <span v-else>{{ row.electricRate }}</span>
                  </template>
                </vxe-column>
                <vxe-column
                  field="totalRate"
                  title="合计"
                  :edit-render="{ autofocus: '.vxe-input--inner' }"
                >
                  <template #edit="{ row }">
                    <vxe-input
                      v-if="row.dataFlag === 1"
                      v-model="row.totalRate"
                      placeholder="请输入合计"
                      v-model.trim="row.totalRate"
                      :maxlength="10"
                      clearable
                    ></vxe-input>
                    <span v-else>{{ row.totalRate }}</span>
                  </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-colgroup field="b" title="金额">
                <vxe-column field="waterCost" title="水费"></vxe-column>
                <vxe-column field="electricCost" title="电费"></vxe-column>
                <vxe-column field="totalCost" title="合计"></vxe-column>
              </vxe-colgroup>
            </vxe-table>
          </div>
          <div class="footer">
            <div class="custom-water-electric">
              <a-checkbox
                v-model:checked="customWaterInfo.customWaterElectricFlag"
                >独立计取水、电费</a-checkbox
              >
              <span class="text">水电费(元)</span>
              <a-input
                v-model:value="customWaterInfo.customWaterElectric"
                placeholder="支持两位小数"
              />
            </div>
            <a-button type="primary" @click="saveWaterElectricCostData"
              >提交</a-button
            >
          </div>
        </a-spin>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import api from '@/api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick({ rowKey: 'id' });
const props = defineProps(['billVisible']);
const emits = defineEmits(['update:billVisible']);
let spinning = ref(false);
let loading = ref(false);
const vexTable = ref(null);
let customWaterInfo = ref({
  waterElectricData: [],
  customWaterElectricFlag: null,
  customWaterElectric: null,
});
const projectStore = projectDetailStore();
const clear = () => {
  //清除编辑状态
  const $table = vexTable.value;
  $table.clearEdit();
};
watch(
  () => props.billVisible,
  () => {
    if (props.billVisible) {
      getWaterElectricCostData();
    }
  }
);

const close = () => {
  emits('update:billVisible', false);
};

const getWaterElectricCostData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.getWaterElectricCostData(apiData).then(res => {
    console.log('============', res);
    if (res.status === 200 && res.result) {
      customWaterInfo.value = res.result;
    }
  });
};

const saveWaterElectricCostData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    waterElectricCostData: JSON.parse(JSON.stringify(customWaterInfo.value)),
  };
  console.log('apiData', apiData);
  api.saveWaterElectricCostData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      close();
    }
  });
};

const updateWaterElectricCostData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    waterElectricCostData: JSON.parse(JSON.stringify(customWaterInfo.value)),
  };
  console.log('临时保存apiData', apiData);
  api.updateWaterElectricCostData(apiData).then(res => {
    console.log('res临时保存', res);
    if (res.status === 200 && res.result) {
      customWaterInfo.value = res.result;
    }
  });
};

// 单元格退出编辑事件
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  if (['waterRate', 'electricRate', 'totalRate'].includes(field)) {
    row[field] = pureNumber(row[field], 2);
    if (row[field] < 0 || row[field] > 100) {
      message.warning('请输入0-100内的值');
      $table.revertData(row, field);
      return;
    }
  }
  if ($table.isUpdateByRow(row, field)) {
    updateWaterElectricCostData();
  }
};

const footerMethod = () => {
  return [
    [
      '合计',
      '',
      '',
      '',
      '',
      '',
      customWaterInfo.value?.totalWaterCost,
      customWaterInfo.value?.totalElectricCost,
      customWaterInfo.value?.waterElectricCost,
    ],
  ];
};

const radioChange = row => {
  console.log('checked', row);
  customWaterInfo.value.waterElectricData.forEach(item => {
    if (item.parentId === row.parentId && item.id !== row.id) {
      item.selectOptionFlag = 0;
    }
  });
  row.selectOptionFlag = 1;
  updateWaterElectricCostData();
};

const visibleMethod = ({ row }) => {
  if (row.selectOptionFlag === 0 || row.selectOptionFlag === 1) {
    return true;
  }
  return false;
};
</script>

<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  align-items: center;
  .custom-water-electric {
    display: flex;
    align-items: center;
    .text {
      margin: 0 10px 0 32px;
    }
    .ant-input {
      width: 150px;
    }
  }
}
</style>
