/*
 * @Descripttion: 其他项目---子页面增删改查操作公共提取及右键列表
 * @Author: wang<PERSON>
 * @Date: 2024-01-30 10:12:01
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-23 16:29:29
 */

import { ref, toRaw, reactive, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import jiesuanApi from '@/api/jiesuanApi';
import { message, Modal } from 'ant-design-vue';
export const useOtherProRaction = ({ pageType }) => {
  const projectStore = projectDetailStore();
  let operateParams = ref({}); //右键操作参数
  let isCurrent = ref(); //选中行index
  let msgInfo = ref(); //操作类型文字提示
  const api = csProject;
  let deleteModal = ref({
    //删除操作选中行及点击确定与否
    isDelete: false,
    deleteRow: null,
  }); //弹框是否删除
  const feildLimit = {
    //表格编辑字段限制
    notEmpty: ['name', 'content', 'worksName', 'fxName', 'serviceContent'], //不为空字段
    taxRemoval: {
      max: 100,
      min: 0,
    },
    unit: {
      length: 10,
    },
    rate: {
      max: 100,
      min: 0,
    },
  };
  let hasDatatype = ['jrg', 'zcbfwf']; //计日工页面和总承包服务费页面有标题行和数据行区分
  let defaultParams = {
    //默认传参
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
  };
  const upDateList =
    //更改操作子页面所需要的参数
    {
      zlje: [
        'name',
        'price',
        'amount',
        'taxRemoval',
        'dispNo',
        'description',
        'unit',
        'jiesuanPrice',
        'jiesuanTotal',
        'jiesuanTaxRemoval',
      ],
      jrg: [
        'worksName',
        'specification',
        'quantitativeExpression',
        'taxRemoval',
        'dispNo',
        'price',
        'unit',
        'jiesuanPrice',
        'jiesuanTotal',
        'jiesuanTaxRemoval',
      ],
      zygczgj: [
        'name',
        'price',
        'amount',
        'taxRemoval',
        'dispNo',
        'description',
        'unit',
        'content',
        'jiesuanPrice',
        'jiesuanTotal',
        'jiesuanTaxRemoval',
      ],
      zcbfwf: [
        'fxName',
        'xmje',
        'amount',
        'rate',
        'dispNo',
        'dataType',
        'serviceContent',
        'jiesuanAmount',
        'jiesuanMode',
        'jieSuanFwje',
        'settlementType',
      ],
    }; //如果是修改操作需要传的修改参数
  const upDateKeyList =
    //更改操作子页面所需要的参数
    {
      zlje: 'projectProvisional',
      jrg: 'projectDayWork',
      zygczgj: 'projectZygcZgj',
      zcbfwf: 'projectServiceCost',
    }; //如果是修改操作需要传的key
  const pasteKeyList =
    //更改操作子页面所需要的参数
    {
      zlje: 'projectProvisional',
      jrg: 'projectDayWorkList',
      zygczgj: 'projectZygcZgj',
      zcbfwf: 'projectServiceCostList',
    }; //如果是粘贴操作操作需要传的key
  let disposeCurrentIndex = ref(null); //复制粘贴操作后的选中行index
  //增删改查操作接口需要的接口参数
  const getOperateParams = (type, row, $table) => {
    //type----操作类型，row-操作行   此处设置操作后选中行的index
    getCurrentIdx($table); //获取当前选中行号
    let operateType; //操作类型
    let isCurrentRow = $table.getCurrentRecord();
    let targetSequenceNbr = isCurrentRow?.sequenceNbr;
    switch (type) {
      case 'insert':
        // 插入
        operateType = 1;
        row ? getCurrentIndex($table, isCurrentRow) : '';
        msgInfo.value = '插入';
        break;
      case 'delete':
        // 删除
        operateType = 3;
        isCurrent.value = null;
        msgInfo.value = '删除';
        break;
      case 'paste':
        // 粘贴
        operateType = 2;
        msgInfo.value = '粘贴';
        if (hasDatatype.includes(pageType)) {
          //计日工页面和总承包服务费页面有标题行和数据行区分
          let tableData = $table.getTableData().tableData;
          let targetList = tableData.filter(
            item =>
              item.sequenceNbr === targetSequenceNbr ||
              item.parentId === targetSequenceNbr
          );
          isCurrentRow.dataType === 1 &&
          projectStore.otherProCopyInfo.copyInfo[0].dataType === 2
            ? (isCurrent.value += 1)
            : (isCurrent.value += targetList.length);
        } else {
          isCurrent.value += 1;
        }
        break;
      case 'update':
        // 修改
        operateType = 4;
        msgInfo.value = '修改';
        break;
      case 'insertData':
        // 插入数据行
        operateType = 1;
        type === 'insertData' ? (isCurrent.value += 1) : '';
        row ? getCurrentIndex($table, isCurrentRow, type) : '';
        msgInfo.value = '插入';
        break;
      case 'insertTitle':
        // 插入标题行
        operateType = 1;
        type === 'insertData' ? (isCurrent.value += 1) : '';
        row ? getCurrentIndex($table, isCurrentRow, type) : '';
        msgInfo.value = '插入';
        break;
    }
    disposeCurrentIndex.value = isCurrent.value;
    finallyParams(row, targetSequenceNbr, operateType, type); //处理最后的操作接口需要的参数
  };
  const finallyParams = (row, targetSequenceNbr, operateType, type) => {
    //处理 增删改查操作的参数
    refreshParams();
    let apiData = {
      ...defaultParams,
      operateType,
      targetSequenceNbr: targetSequenceNbr ? targetSequenceNbr : null,
    };
    if (type === 'update') {
      apiData.targetSequenceNbr = row.sequenceNbr;
      updateParems(row);
    } else if (type === 'paste') {
      if (!hasDatatype.includes(pageType)) {
        apiData[pasteKeyList[pageType]] = { ...row };
      } else {
        apiData.dataType = projectStore.otherProCopyInfo.copyInfo[0].dataType;
        let copyList = toRaw(projectStore.otherProCopyInfo.copyInfo); //将获取ref定义的数据的原始值
        apiData[pasteKeyList[pageType]] = [...copyList];
      }
    }
    if (type === 'update') {
      let operateObj = toRaw(operateParams.value);
      // if (hasDatatype.includes(pageType)) {
      //   apiData.dataType = operateObj.projectServiceCost.dataType;
      // }
      operateParams.value = { ...toRaw(operateParams.value), ...apiData };
    } else {
      if (hasDatatype.includes(pageType)) {
        operateType === 1
          ? (apiData.dataType = type === 'insertData' ? 2 : 1)
          : '';
        operateType === 3 ? (apiData.dataType = row.dataType) : '';
      }
      operateParams.value = { ...apiData };
    }
  };
  const updateParems = row => {
    //修改数据子页面不同参数
    let params = {};
    upDateList[pageType].map(item => {
      params[item] = row[item];
    });
    operateParams.value[upDateKeyList[pageType]] = { ...params };
  };
  const copyOperate = (item, $table) => {
    //复制操作----只需要将数据存储
    let copyInfo;
    if (hasDatatype.includes(pageType)) {
      copyInfo = [{ ...item }];
      //计日工页面和总承包服务费页面有标题行和数据行区分
      let tableData = $table.getTableData().tableData;
      tableData.map(i => {
        item.sequenceNbr === i.parentId ? copyInfo.push(toRaw(i)) : '';
      });
      copyInfo = toRaw(copyInfo);
    } else {
      copyInfo = { ...item };
    }
    projectStore.SET_OTHERPRO_COPYINFO({
      copyInfo: copyInfo,
      asideTitle: pageType,
    });
  };
  const deleteOperate = (item, $table) => {
    deleteModal.value = {
      isDelete: false,
      deleteRow: item,
    };
    Modal.confirm({
      title: '是否确认删除？',
      content:
        hasDatatype.includes(pageType) && item.dataType !== 2
          ? '是否删除当前标题行及包含数据行'
          : '是否删除当前数据行',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        deleteModal.value.isDelete = true;
      },
      onCancel() {},
    });
  };
  const getCurrentIndex = ($table, item = null, type = '') => {
    //设置选中行序号
    let tableData = $table.getTableData().tableData;
    if (item) {
      tableData.map((n, index) => {
        if (n.sequenceNbr === item.sequenceNbr) {
          isCurrent.value = index;
          type === 'insertData' ? (isCurrent.value += 1) : '';
        }
      });
    } else {
      isCurrent.value = 0;
    }
    disposeCurrentIndex.value = isCurrent.value;
  };
  const getCurrentIdx = $table => {
    //获取当前表格选中行数据的index
    let tableData = $table.getTableData().tableData;
    let isCurrentRow = $table.getCurrentRecord();
    tableData.map((n, index) => {
      if (n.sequenceNbr === isCurrentRow.sequenceNbr) {
        isCurrent.value = index;
      }
    });
  };
  //右键下拉列表内容---公共
  const menuConfigOptions = {
    className: 'my-menus',
    body: {
      options: [
        [
          {
            name: '插入',
            code: 'add',
            children: [
              {
                code: 'addNull',
                name: '数据行',
                disabled: true,
                visible: hasDatatype.includes(pageType) ? true : false, //有数据行和标题行的页面展示子数据
              },
              {
                code: 'addTitle',
                name: '标题行',
                disabled: true,
                visible: hasDatatype.includes(pageType) ? true : false,
              },
            ],
          },

          {
            code: 'copy',
            name: '复制',
          },
          {
            code: 'paste',
            name: '粘贴',
            disabled: true,
          },
          {
            code: 'delete',
            name: '删除',
            disabled: true,
            className: 'redFont',
          },
        ],
      ],
    },
  };
  const pasteIsDisabled = (row = null) => {
    // 右键粘贴--另外加插入的禁用设置
    let copy = menuConfigOptions.body.options[0][2];
    let del = menuConfigOptions.body.options[0][3];
    let asideTitle = projectStore.otherProCopyInfo?.asideTitle;
    // 如果不是新增行则不可以进行删除操作
    if (row.jiesuanOriginal === 1) {
      del.disabled = true;
      del.className = '';
    } else {
      del.disabled = false;
      del.className = 'redFont';
    }
    if (!hasDatatype.includes(pageType)) {
      //zlje 和zygczgj页面不需要判断数据行和标题行
      asideTitle === pageType
        ? (copy.disabled = false)
        : (copy.disabled = true);
    } else {
      //插入标题行和数据行是否置灰
      let insertChild = menuConfigOptions.body.options[0][0].children;
      if (row.dataType === 1) {
        insertChild[0].disabled = false;
        insertChild[1].disabled = false;
      } else if (row.dataType === 2) {
        insertChild[0].disabled = false;
        insertChild[1].disabled = true;
      }
      //粘贴按钮是否置灰
      if (asideTitle !== pageType) return;
      let copyDataType =
        projectStore.otherProCopyInfo.copyInfo &&
        projectStore.otherProCopyInfo.copyInfo[0].dataType;
      if (copyDataType === 1 && row.dataType === 1) {
        copy.disabled = false;
      } else if (copyDataType === 2 && row.dataType === 2) {
        copy.disabled = false;
      } else if (copyDataType === 2 && row.dataType === 1) {
        copy.disabled = false;
      } else {
        copy.disabled = true;
      }
    }
  };
  const editCheckLength = (row, column, $table) => {
    const field = column.field;
    let value = row[field];
    // console.log('editCheckLength', field, value);
    if (!$table.isUpdateByRow(row, field)) {
      return;
    }
    if (feildLimit.notEmpty.includes(field) && !value) {
      $table.revertData(row, field);
      return;
    }
    if (
      feildLimit.hasOwnProperty(field) &&
      feildLimit[field].hasOwnProperty('max')
    ) {
      //有字段设置大小
      if (
        value > feildLimit[field]['max'] ||
        value < feildLimit[field]['min']
      ) {
        $table.revertData(row, field);
        return;
      }
    }
    if (
      feildLimit.hasOwnProperty(field) &&
      feildLimit[field].hasOwnProperty('length')
    ) {
      //长度限制---超出截取
      if (value.length > feildLimit[field]['length']) {
        row[field] = value.slice(0, feildLimit[field]['length']);
      }
    }
  };

  const tabBtnIsValid = (data, $table) => {
    //有数据行和标题行的tab栏按钮置灰状态
    if (projectStore.dataType === 1) {
      data[1].isValid = true;
      data[0].isValid = true;
    } else if (projectStore.dataType === 2) {
      data[0].isValid = true;
      data[1].isValid = false;
    } else {
      data[0].isValid = true;
      data[1].isValid = true;
    }
  };

  const setInsertType = ($table, tableData) => {
    //获取计日工等页面的dataType
    // let tableData = $table.getTableData().tableData;
    console.log('tableData', tableData);
    let typeValue;
    if (tableData && tableData.length > 0) {
      if (hasDatatype.includes(pageType)) {
        typeValue =
          $table.getCurrentRecord() && $table.getCurrentRecord().dataType;
      } else {
        typeValue = 0;
      }
    } else {
      typeValue = 0;
    }
    projectStore.SET_DATATYPE(typeValue);
    projectStore.isAutoPosition = false;
    return typeValue;
  };
  onMounted(() => {
    if (projectStore.tabSelectName === '其他项目') {
      console.log('pageType', pageType);
    }
  });
  const refreshParams = () => {
    defaultParams.constructId = projectStore.currentTreeGroupInfo?.constructId;
    defaultParams.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    defaultParams.unitId = projectStore.currentTreeInfo?.id; //单位ID
  };
  const getChildPageData = async () => {
    refreshParams();
    let apiData = {
      ...defaultParams,
      levelType: projectStore.currentTreeInfo?.levelType,
    };
    let resData;
    switch (pageType) {
      case 'zlje':
        resData = await api.getOtherProjectZljeList(apiData);
        break;
      case 'zygczgj':
        resData = await api.getOtherProjectZygcZgjList(apiData);
        break;
      case 'jrg':
        resData = await api.getOtherProjectJrgList(apiData);
        break;
      case 'zcbfwf':
        resData = await api.getOtherProjectZcbfwfList(apiData);
        break;
    }
    return resData;
  };
  const getOperateData = async data => {
    let resData;
    switch (pageType) {
      case 'zlje':
        resData = await jiesuanApi.operateOtherProjectProvisional(data);
        break;
      case 'zygczgj':
        resData = await jiesuanApi.operateOtherProjectZygcZgj(data);
        break;
      case 'jrg':
        resData = await jiesuanApi.operateOtherProjectDayWork(data);
        break;
      case 'zcbfwf':
        resData = await jiesuanApi.operateOtherProjectServiceCost(data);
        break;
    }
    // jiesuanApi.countCostCodePrice(defaultParams);
    return resData;
  };
  const hasDataType = () => {
    //有标题行和数据行的表格
    let flag;
    hasDatatype.includes(pageType) ? (flag = true) : (flag = false);
    return flag;
  };
  return {
    getOperateParams,
    copyOperate,
    disposeCurrentIndex,
    getCurrentIndex,
    operateParams,
    msgInfo,
    deleteOperate,
    deleteModal,
    menuConfigOptions,
    pasteIsDisabled,
    editCheckLength,
    tabBtnIsValid,
    setInsertType,
    getChildPageData,
    getOperateData,
    hasDataType,
  };
};
