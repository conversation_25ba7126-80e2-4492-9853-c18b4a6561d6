<!--
 * @Descripttion: 人材机汇总
-->
<template>
  <teleport
    to="#teleport-anchor-humanMachineSummary"
    v-if="
      projectStore.componentId === 'humanMachineSummary' &&
      projectStore.asideMenuCurrentInfo?.code == '14'
    "
  >
    <a-dropdown :trigger="['click']">
      <span class="guolv-humanM">
        <icon-font
          type="icon-rencaijiguolv"
          style="font-size: 14px; cursor: pointer; top: 0px"
        ></icon-font>
        过滤
        <DownOutlined />
      </span>
      <template #overlay>
        <a-menu>
          <a-checkbox-group
            class="guolv-human-checkgroup"
            v-model:value="filter.checkedList"
            :options="filter.options"
            @change="filterData"
          />
        </a-menu>
      </template>
    </a-dropdown>
  </teleport>
  <div class="table-content table-content-flex-column" id="humanTable">
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <div
          class="table-content"
          v-if="
            handlerColumns.length > 0 &&
            projectStore.asideMenuCurrentInfo?.code != '99'
          "
        >
          <vxe-table
            align="center"
            ref="humanTable"
            :loading="loading"
            height="auto"
            min-height="0"
            :menu-config="menuConfig"
            :column-config="{ isCurrent: true, resizable: true }"
            :keyboard-config="keyboardConfig"
            :mouse-config="{ selected: true }"
            v-if="
              handlerColumns.length > 0 &&
              projectStore.asideMenuCurrentInfo?.code != '99'
            "
            :row-config="{
              isHover: true,
              isCurrent: true,
              keyField: 'sequenceNbr',
            }"
            :data="tableDataOnshow"
            :cell-style="
              projectStore.currentTreeInfo.type == 3
                ? cellStyle
                : cellTableStyle
            "
            :row-style="rowStyle"
            @edit-closed="
              event => {
                if (
                  ['type', 'unit', 'kindSc', 'ifDonorMaterial'].includes(
                    event.column.field
                  )
                ) {
                  return;
                }
                editClosedEvent(event);
              }
            "
            keep-source
            @menu-click="contextMenuClickEvent"
            @cell-mouseleave="cellMouseLeaveEvent"
            @cell-click="
              cellData => {
                resetDragFillInfo();
                currentCellDragFill = cellData;
                useCellClickEvent(cellData, null, ['']);
                if (
                  displayedFields.includes(cellData.column.field) &&
                  (!cellData.column?.editRender ||
                    cellData.column.editRender?.notCanEdit)
                ) {
                  currentCellData.columnIndex = cellData.$columnIndex;
                  currentCellData.rowId = cellData.row.sequenceNbr;
                }
              }
            "
            class="table-edit-common"
            :cell-class-name="cellClassName"
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              beforeEditMethod: customCellBeforeEditMethod,
            }"
            :scroll-y="{ enabled: true, gt: 30 }"
            @current-change="currentChange"
            :tooltip-config="{
              showAll: false,
            }"
            :export-config="{}"
            @cell-mouseenter="
              event => {
                cellMouseenterEvent(event);
                cellMouseEnterEventCustom(event);
              }
            "
            @keydown="
              event => {
                useKeyDownEvent(event, { deleteEnable: false });
              }
            "
          >
            <vxe-column type="checkbox" fixed="left" :width="columnWidth(28)">
            </vxe-column>
            <vxe-column
              field="dispNo"
              :width="columnWidth(40)"
              title="序号"
              fixed="left"
              :visible="handlerColumns.find(a => a.field === 'dispNo').visible"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column
              field="materialCode"
              :width="columnWidth(120)"
              :visible="
                handlerColumns.find(a => a.field === 'materialCode').visible
              "
              title="编码"
              fixed="left"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('materialCode')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'materialCode' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'materialCode' && !sortVal"
                    alt=""
                  />

                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span>{{ '‎' + row.materialCode }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="type"
              :width="columnWidth(100)"
              title="类别"
              :visible="handlerColumns.find(a => a.field === 'type').visible"
              fixed="left"
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span style="cursor: pointer" @click="sortClick('type')">{{
                    column.title
                  }}</span>
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'type' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'type' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span>{{ row.type }}</span>
              </template>
              <template #edit="{ $table, row, column }">
                <vxe-select
                  v-model="row.type"
                  transfer
                  v-if="
                    row.isFyrcj == 1 &&
                    row.typeList?.length &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !deMapFun.isOtherMaterial(row.materialCode)
                  "
                  @change="editClosedEvent({ $table, row, column })"
                >
                  <vxe-option
                    v-for="item in row.typeList"
                    :key="item.desc"
                    :value="item.desc"
                    :label="item.desc"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{ row.type }} </span>
              </template>
            </vxe-column>
            <vxe-column
              field="materialName"
              :width="columnWidth(200)"
              title="名称"
              fixed="left"
              :visible="
                handlerColumns.find(a => a.field === 'materialName').visible
              "
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('materialName')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'materialName' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'materialName' && !sortVal"
                    alt=""
                  />

                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span>{{ row.materialName }}</span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  v-if="
                    row.isFyrcj == 1 &&
                    Number(row.edit) !== 1 &&
                    !deMapFun.isOtherMaterial(row.materialCode)
                  "
                  :clearable="false"
                  v-model.trim="row.materialName"
                  type="text"
                  @blur="clear()"
                ></vxe-input>
                <span v-else>{{ row.materialName }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="specification"
              :width="columnWidth(100)"
              title="规格型号"
              :visible="
                handlerColumns.find(a => a.field === 'specification').visible
              "
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('specification')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'specification' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'specification' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <Annotations
                  @close="v => closeAnnotations(v, row)"
                  @onfocusNode="onFocusNode(row)"
                  v-if="
                    projectStore.currentTreeInfo.type === 3 &&
                    (row?.noteViewVisible ||
                      row?.isShowAnnotations ||
                      row?.noteEditVisible)
                  "
                  :note="row.annotations"
                  :isDisabled="row?.noteEditVisible"
                  :ref="el => getAnnotationsRef(el, row)"
                  :type="2"
                ></Annotations>
                <Annotations
                  @close="v => closeAnnotations(v, row)"
                  @onfocusNode="onFocusNode(row)"
                  v-if="
                    [1].includes(projectStore.currentTreeInfo.type) &&
                    (row?.noteViewVisible ||
                      row?.isShowAnnotationsPro ||
                      row?.noteEditVisible)
                  "
                  :note="row.annotationsPro"
                  :isDisabled="row?.noteEditVisible"
                  :ref="el => getAnnotationsRef(el, row)"
                  :type="2"
                ></Annotations>
                <Annotations
                  @close="v => closeAnnotations(v, row)"
                  @onfocusNode="onFocusNode(row)"
                  v-if="
                    [2].includes(projectStore.currentTreeInfo.type) &&
                    (row?.noteViewVisible ||
                      row?.isShowAnnotationsSingle ||
                      row?.noteEditVisible)
                  "
                  :note="row.annotationsSingle"
                  :isDisabled="row?.noteEditVisible"
                  :ref="el => getAnnotationsRef(el, row)"
                  :type="2"
                ></Annotations>
                <span>{{ row.specification }}</span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.specification"
                  type="text"
                  @blur="clear()"
                  v-if="
                    (Number(row.edit) !== 1 &&
                      !deMapFun.isTz(row.materialCode) &&
                      !deMapFun.isJxxs(row.materialCode) &&
                      row.supplementDeRcjFlag != 1) ||
                    ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                      row.materialCode.split('#')[0]
                    )
                  "
                ></vxe-input>
                <span v-else>{{ row.specification }}</span>
              </template>
            </vxe-column>

            <vxe-column
              field="unit"
              :visible="handlerColumns.find(a => a.field === 'unit').visible"
              :width="columnWidth(70)"
              title="单位"
              :edit-render="{
                enabled:
                  [1, 2].includes(projectStore.currentTreeInfo.type) || isZG
                    ? false
                    : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span style="cursor: pointer" @click="sortClick('unit')">{{
                    column.title
                  }}</span>
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'unit' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'unit' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span>{{ row.unit }}</span>
              </template>
              <template #edit="{ $table, row, column }">
                <vxe-select
                  v-model="row.unit"
                  transfer
                  v-if="
                    ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                      row.materialCode.split('#')[0]
                    )
                  "
                  @change="editClosedEvent({ $table, row, column })"
                >
                  <vxe-option
                    v-for="item in unitList"
                    :key="item"
                    :value="item"
                    :label="item"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{ row.unit }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="totalNumber"
              :visible="
                handlerColumns.find(a => a.field === 'totalNumber').visible
              "
              :width="columnWidth(80)"
              title="数量"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('totalNumber')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'totalNumber' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'totalNumber' && !sortVal"
                    alt=""
                  />

                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                {{
                  decimalFormat(row.totalNumber, 'RCJ_COLLECT_TOTALNUMBER_PATH')
                }}
              </template>
            </vxe-column>
            <vxe-column
              v-if="projectStore.taxMade == 1"
              field="baseJournalPrice"
              :width="columnWidth(100)"
              :visible="
                handlerColumns.find(a => a.field === 'baseJournalPrice').visible
              "
              title="不含税基期价"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('baseJournalPrice')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'baseJournalPrice' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'baseJournalPrice' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(
                        row.baseJournalPrice,
                        'RCJ_COLLECT_BASEJOURNALPRICE_PATH'
                      )
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              v-else
              field="baseJournalTaxPrice"
              :width="columnWidth(100)"
              :visible="
                handlerColumns.find(a => a.field === 'baseJournalTaxPrice')
                  .visible
              "
              title="含税基期价"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('baseJournalTaxPrice')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'baseJournalTaxPrice' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'baseJournalTaxPrice' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(
                        row.baseJournalTaxPrice,
                        'RCJ_COLLECT_BASEJOURNALTAXPRICE_PATH'
                      )
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="marketPrice"
              :visible="
                handlerColumns.find(a => a.field === 'marketPrice').visible
              "
              :width="columnWidth(100)"
              title="不含税市场价"
              :edit-render="{
                enabled: isZG ? false : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('marketPrice')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'marketPrice' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'marketPrice' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>

              <template #default="{ row, column }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(
                        row.marketPrice,
                        'RCJ_COLLECT_MARKETPRICE_PATH'
                      )
                }}</span>
              </template>

              <template #edit="{ row, column }">
                <vxe-input
                  v-if="
                    row.isFyrcj == 1 &&
                    !row.ifProvisionalEstimate &&
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum == 1 &&
                      (row.levelMark == 1 || row.levelMark == 2)
                    ) &&
                    Number(row.edit) !== 1 &&
                    !deMapFun.isJxxs(row.materialCode) &&
                    !deMapFun.isQtclf(row.materialCode) &&
                    !isChangeAva(row)
                  "
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                    row.marketPrice = pureNumber0(row.marketPrice);
                    materialCodeChange(row);
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(
                        row.marketPrice,
                        'RCJ_COLLECT_MARKETPRICE_PATH'
                      )
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="marketTaxPrice"
              :visible="
                handlerColumns.find(a => a.field === 'marketTaxPrice').visible
              "
              :width="columnWidth(100)"
              title="含税市场价"
              :edit-render="{
                enabled: isZG ? false : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('marketTaxPrice')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'marketTaxPrice' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'marketTaxPrice' && !sortVal"
                    alt=""
                  />

                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(
                        row.marketTaxPrice,
                        'RCJ_COLLECT_MARKETTAXPRICE_PATH'
                      )
                }}</span>
              </template>
              <template #edit="{ row, column }">
                <vxe-input
                  v-if="
                    row.isFyrcj == 1 &&
                    !row.ifProvisionalEstimate &&
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum == 1 &&
                      (row.levelMark == 1 || row.levelMark == 2)
                    ) &&
                    Number(row.edit) !== 1 &&
                    !deMapFun.isJxxs(row.materialCode) &&
                    !deMapFun.isQtclf(row.materialCode) &&
                    !isChangeAva(row)
                  "
                  :clearable="false"
                  v-model.trim="row.marketTaxPrice"
                  type="text"
                  @blur="
                    row.marketTaxPrice = pureNumber0(row.marketTaxPrice);
                    materialCodeChange(row, 'marketTaxPrice');
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(
                        row.marketTaxPrice,
                        'RCJ_COLLECT_MARKETTAXPRICE_PATH'
                      )
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              v-if="projectStore.taxMade == 1"
              field="total"
              :visible="handlerColumns.find(a => a.field === 'total').visible"
              :width="columnWidth(110)"
              title="不含税市场价合计"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span style="cursor: pointer" @click="sortClick('total')">{{
                    column.title
                  }}</span>
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'total' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'total' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(row.total, 'RCJ_COLLECT_TOTAL_PATH')
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              v-else
              field="totalTax"
              :visible="
                handlerColumns.find(a => a.field === 'totalTax').visible
              "
              :width="columnWidth(100)"
              title="含税市场价合计"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('totalTax')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'totalTax' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'totalTax' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(row.totalTax, 'RCJ_COLLECT_TOTALTAX_PATH')
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="taxRate"
              :visible="handlerColumns.find(a => a.field === 'taxRate').visible"
              :width="columnWidth(110)"
              title="税率（%）"
              :edit-render="{
                enabled: isZG ? false : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span style="cursor: pointer" @click="sortClick('taxRate')">{{
                    column.title
                  }}</span>
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'taxRate' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'taxRate' && !sortVal"
                    alt=""
                  />

                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(row.taxRate, 'RCJ_COLLECT_TAXRATE_PATH')
                }}</span>
              </template>
              <template #edit="{ row, column }">
                <vxe-input
                  v-if="
                    (row.taxRate || row.taxRate == 0) &&
                    !row.ifProvisionalEstimate &&
                    row.isFyrcj == 1 &&
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum == 1 &&
                      (row.levelMark == 1 || row.levelMark == 2)
                    ) &&
                    Number(row.edit) !== 1 &&
                    !deMapFun.isJxxs(row.materialCode) &&
                    !deMapFun.isQtclf(row.materialCode) &&
                    !isChangeAva(row)
                  "
                  :clearable="false"
                  v-model.trim="row.taxRate"
                  type="number"
                  min="0"
                  @blur="taxRateBlur(row)"
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row)
                    ? '-'
                    : decimalFormat(row.taxRate, 'RCJ_COLLECT_TAXRATE_PATH')
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="sourcePrice"
              :visible="
                handlerColumns.find(a => a.field === 'sourcePrice').visible
              "
              :width="columnWidth(150)"
              title="价格来源"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column
              field="priceDifferenc"
              :visible="
                handlerColumns.find(a => a.field === 'priceDifferenc').visible
              "
              :width="columnWidth(100)"
              title="价差"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('priceDifferenc')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'priceDifferenc' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'priceDifferenc' && !sortVal"
                    alt=""
                  />

                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  decimalFormat(row.priceDifferenc, 'RCJ_COLLECT_JC_PATH')
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="priceDifferencSum"
              :visible="
                handlerColumns.find(a => a.field === 'priceDifferencSum')
                  .visible
              "
              :width="columnWidth(100)"
              title="价差合计"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span
                    style="cursor: pointer"
                    @click="sortClick('priceDifferencSum')"
                    >{{ column.title }}</span
                  >
                  <img
                    class="sortImg"
                    src="@/assets/img/upSort.png"
                    v-if="sortFiled == 'priceDifferencSum' && sortVal"
                    alt=""
                  />
                  <img
                    class="sortImg"
                    src="@/assets/img/downSort.png"
                    v-if="sortFiled == 'priceDifferencSum' && !sortVal"
                    alt=""
                  />
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  decimalFormat(row.priceDifferencSum, 'RCJ_COLLECT_JCHJ_PATH')
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="ifDonorMaterial"
              :width="columnWidth(100)"
              :visible="
                handlerColumns.find(a => a.field === 'ifDonorMaterial').visible
              "
              title="供应方式"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ column, row, $columnIndex, rowIndex }">
                {{
                  cellectType?.supplyType?.find(
                    a => a.value == row.ifDonorMaterial
                  )?.label
                }}
                <div
                  v-if="handLabelDisplay({ $columnIndex, row, column })"
                  class="fill-handle"
                  @mousedown="
                    handleMouseDown(
                      $event,
                      row,
                      column,
                      $columnIndex,
                      rowIndex,
                      useDragCb
                    )
                  "
                ></div>
              </template>
              <!-- &&
                    row?.supplementDeRcjFlag != 1 -->
              <template #edit="{ $table, row, column }">
                <vxe-select
                  v-if="row.checkIsShow"
                  v-model="row.ifDonorMaterial"
                  transfer
                  @change="
                    () => {
                      ifDonorMaterialChange(row);
                      editClosedEvent({ $table, row, column });
                    }
                  "
                >
                  <vxe-option
                    v-for="item in cellectType.supplyType"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{
                  cellectType?.supplyType?.find(
                    a => a.value == row.ifDonorMaterial
                  )?.label
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="donorMaterialPrice"
              :width="columnWidth(100)"
              :visible="
                handlerColumns.find(a => a.field === 'donorMaterialPrice')
                  .visible
              "
              title="甲供价"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <!-- :edit-render="{ autofocus: '.vxe-input--inner' }" -->
              <template #default="{ row }">
                <span v-if="row.checkIsShow">{{
                  decimalFormat(
                    handlePrice(row),
                    'RCJ_COLLECT_DONORMATERIALPRICE_PATH'
                  )
                }}</span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  v-if="
                    row.ifDonorMaterial == 1 && row.ifLockStandardPrice !== 1
                  "
                  :clearable="false"
                  v-model.trim="row.donorMaterialPrice"
                  type="text"
                  @blur="
                    row.donorMaterialPrice = pureNumber0(
                      row.donorMaterialPrice
                    );
                    donorMaterPriceChange(row);
                  "
                ></vxe-input>
                <span v-else>{{
                  row.ifDonorMaterial == 1 ? row.donorMaterialPrice : ''
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="donorMaterialNumber"
              :width="columnWidth(90)"
              :visible="
                handlerColumns.find(a => a.field === 'donorMaterialNumber')
                  .visible
              "
              title="甲供数量"
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                <span v-if="row.checkIsShow">{{
                  decimalFormat(
                    row.ifDonorMaterial == 1 ? row.donorMaterialNumber : '',
                    'RCJ_COLLECT_DONORMATERIALNUMBER_PATH'
                  )
                }}</span>
                <span v-else></span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  v-if="row.checkIsShow"
                  :clearable="false"
                  v-model.trim="row.donorMaterialNumber"
                  @blur="clear()"
                  @keyup="keyupDonorMaterialNumber"
                ></vxe-input>
                <span v-else-if="row.checkIsShow">{{
                  row.ifDonorMaterial == 1 ? row.donorMaterialNumber : ''
                }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="kindSc"
              :width="columnWidth(100)"
              :visible="handlerColumns.find(a => a.field === 'kindSc').visible"
              title="三材类别"
              :edit-render="{
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                {{ row.kindSc === '空' ? '' : row.kindSc }}
              </template>
              <template #edit="{ $table, row, column }">
                <vxe-select
                  v-model="row.kindSc"
                  transfer
                  @change="editClosedEvent({ $table, row, column })"
                >
                  <vxe-option
                    v-for="item in scList"
                    :key="item.name"
                    :value="item.name"
                    :label="item.name"
                  ></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
            <!-- enabled: projectStore.currentTreeInfo.type == 1 ? false : true, -->
            <vxe-column
              field="transferFactor"
              :width="columnWidth(100)"
              :visible="
                handlerColumns.find(a => a.field === 'transferFactor').visible
              "
              title="三材系数"
              :edit-render="{
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row, column }">
                <span>{{
                  decimalFormat(
                    row.transferFactor ?? '',
                    'RCJ_COLLECT_TRANSFERFACTOR_PATH'
                  )
                }}</span>
              </template>
              <template #edit="{ row, column }">
                <vxe-input
                  :clearable="false"
                  v-if="row.kindSc && row.kindSc !== '空'"
                  v-model.trim="row.transferFactor"
                  type="text"
                  @blur="
                    row.transferFactor = pureNumber0(row.transferFactor);
                    clear();
                  "
                ></vxe-input>
              </template>
            </vxe-column>
            <!-- <vxe-column
              field="markSum"
              width="130"
              title="是否汇总(二次分析)"
              :visible="handlerColumns.find(a => a.field === 'markSum').visible"
              :cell-render="{}"
            >
              <template #default="{ row }">
                <vxe-checkbox
                  v-model="row.markSum"
                  size="small"
                  content=""
                  :disabled="projectStore.currentTreeInfo.type == 1"
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'markSum')"
                  v-if="row.levelMark == '1' || row.levelMark == '2'"
                ></vxe-checkbox>
              </template>
            </vxe-column> -->

            <vxe-column
              field="ifProvisionalEstimate"
              :visible="
                handlerColumns.find(a => a.field === 'ifProvisionalEstimate')
                  ?.visible
              "
              :width="columnWidth(70)"
              title="是否暂估"
              :exportMethod="
                ({ row }) => {
                  return ![2, 4, 5, 6, 7, 8, 9, 10].includes(+row.kind) ||
                    isChangeAva(row)
                    ? ''
                    : row.ifProvisionalEstimate && row.checkIsShow
                      ? '是'
                      : '否';
                }
              "
              :edit-render="{
                notCanEdit: true,
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ column, row, $columnIndex, rowIndex }">
                <!-- v-if="!isChangeAva(row)" -->
                <vxe-checkbox
                  v-if="[2, 4, 5, 6, 7, 8, 9, 10].includes(+row.kind)"
                  v-model="row.ifProvisionalEstimate"
                  size="small"
                  content=""
                  :disabled="
                    ifProvisionalEstimateDisabled(row) || isChangeAva(row)
                  "
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'ifProvisionalEstimate')"
                ></vxe-checkbox>
                <div
                  v-if="
                    [2, 4, 5, 6, 7, 8, 9, 10].includes(+row.kind) &&
                    handLabelDisplay({ $columnIndex, row, column })
                  "
                  class="fill-handle"
                  @mousedown="
                    handleMouseDown(
                      $event,
                      row,
                      column,
                      $columnIndex,
                      rowIndex,
                      useDragCb
                    )
                  "
                ></div>
              </template>
              <!-- 选中当前单元格，且可正常编辑 -->
              <template #edit="{ column, row, $columnIndex, rowIndex }">
                <!-- v-if="!isChangeAva(row)" -->
                <vxe-checkbox
                  v-if="[2, 4, 5, 6, 7, 8, 9, 10].includes(+row.kind)"
                  v-model="row.ifProvisionalEstimate"
                  size="small"
                  content=""
                  :disabled="
                    ifProvisionalEstimateDisabled(row) || isChangeAva(row)
                  "
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'ifProvisionalEstimate')"
                ></vxe-checkbox>
                <div
                  v-if="
                    [2, 4, 5, 6, 7, 8, 9, 10].includes(+row.kind) &&
                    handLabelDisplay({ $columnIndex, row, column })
                  "
                  class="fill-handle"
                  @mousedown="
                    handleMouseDown(
                      $event,
                      row,
                      column,
                      $columnIndex,
                      rowIndex,
                      useDragCb
                    )
                  "
                ></div>
              </template>
            </vxe-column>
            <vxe-column
              field="ifLockStandardPrice"
              :width="columnWidth(80)"
              title="市场价锁定"
              :cell-render="{}"
              :visible="
                handlerColumns.find(a => a.field === 'ifLockStandardPrice')
                  .visible
              "
              :edit-render="{
                notCanEdit: true,
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <!-- BCRGF\BCCLF\BCSBF\BCJXF\BCZCF -->
              <template #default="{ column, row, $columnIndex, rowIndex }">
                <vxe-checkbox
                  v-model="row.ifLockStandardPrice"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'ifLockStandardPrice')"
                  v-if="
                    (row.checkIsShow && row?.supplementDeRcjFlag != 1) ||
                    ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                      row.materialCode.split('#')[0]
                    )
                  "
                  :disabled="
                    Number(row.edit) == 1 || deMapFun.isTz(row.materialCode)
                  "
                ></vxe-checkbox>
                <div
                  v-if="
                    ((row.checkIsShow && row?.supplementDeRcjFlag != 1) ||
                      ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                        row.materialCode.split('#')[0]
                      )) &&
                    handLabelDisplay({ $columnIndex, row, column })
                  "
                  class="fill-handle"
                  @mousedown="
                    handleMouseDown(
                      $event,
                      row,
                      column,
                      $columnIndex,
                      rowIndex,
                      useDragCb
                    )
                  "
                ></div>
              </template>
              <!-- 选中当前单元格，且可正常编辑 -->
              <template #edit="{ column, row, $columnIndex, rowIndex }">
                <vxe-checkbox
                  v-model="row.ifLockStandardPrice"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'ifLockStandardPrice')"
                  v-if="
                    (row.checkIsShow && row?.supplementDeRcjFlag != 1) ||
                    ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                      row.materialCode.split('#')[0]
                    )
                  "
                  :disabled="
                    Number(row.edit) == 1 || deMapFun.isTz(row.materialCode)
                  "
                ></vxe-checkbox>
                <div
                  v-if="
                    ((row.checkIsShow && row?.supplementDeRcjFlag != 1) ||
                      ['BCRGF', 'BCCLF', 'BCSBF', 'BCJXF', 'BCZCF'].includes(
                        row.materialCode.split('#')[0]
                      )) &&
                    handLabelDisplay({ $columnIndex, row, column })
                  "
                  class="fill-handle"
                  @mousedown="
                    handleMouseDown(
                      $event,
                      row,
                      column,
                      $columnIndex,
                      rowIndex,
                      useDragCb
                    )
                  "
                ></div>
              </template>
            </vxe-column>
            <vxe-column
              field="producer"
              :width="columnWidth(100)"
              title="产地"
              :visible="
                handlerColumns.find(a => a.field === 'producer').visible
              "
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.producer"
                  type="text"
                  @blur="clear()"
                  v-if="
                    !deMapFun.isJxxs(row.materialCode) &&
                    row?.supplementDeRcjFlag != 1
                  "
                ></vxe-input>
                <span v-else>{{ row.producer }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="manufactor"
              :width="columnWidth(100)"
              title="厂家"
              :visible="
                handlerColumns.find(a => a.field === 'manufactor').visible
              "
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.manufactor"
                  type="text"
                  @blur="clear()"
                  v-if="
                    !deMapFun.isJxxs(row.materialCode) &&
                    row?.supplementDeRcjFlag != 1
                  "
                ></vxe-input>
                <span v-else>{{ row.manufactor }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="brand"
              :width="columnWidth(100)"
              title="品牌"
              :visible="handlerColumns.find(a => a.field === 'brand').visible"
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.brand"
                  type="text"
                  @blur="clear()"
                  v-if="
                    !deMapFun.isJxxs(row.materialCode) &&
                    row?.supplementDeRcjFlag != 1
                  "
                ></vxe-input>
                <span v-else>{{ row.producer }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="deliveryLocation"
              :width="columnWidth(100)"
              title="送达地点"
              :visible="
                handlerColumns.find(a => a.field === 'deliveryLocation').visible
              "
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.deliveryLocation"
                  type="text"
                  @blur="clear()"
                  v-if="
                    !deMapFun.isJxxs(row.materialCode) &&
                    row?.supplementDeRcjFlag != 1
                  "
                ></vxe-input>
                <span v-else>{{ row.producer }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="qualityGrade"
              :width="columnWidth(100)"
              title="质量等级"
              :visible="
                handlerColumns.find(a => a.field === 'qualityGrade').visible
              "
              :edit-render="{
                enabled: [1, 2].includes(projectStore.currentTreeInfo.type)
                  ? false
                  : true,
                autofocus: '.vxe-input--inner',
              }"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.qualityGrade"
                  type="text"
                  @blur="clear()"
                  v-if="
                    !deMapFun.isJxxs(row.materialCode) &&
                    row?.supplementDeRcjFlag != 1
                  "
                ></vxe-input>
                <span v-else>{{ row.producer }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="supplyTime"
              :width="columnWidth(120)"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
              :visible="
                handlerColumns.find(a => a.field === 'supplyTime').visible
              "
              title="供应时间"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                {{ row.supplyTime }}
              </template>
              <template #edit="{ row }">
                <vxe-input
                  v-model="row.supplyTime"
                  placeholder="日期选择"
                  type="datetime"
                  :value-format="YYYY - MM - DD"
                ></vxe-input>
              </template>
            </vxe-column>
            <vxe-column
              field="remark"
              :width="columnWidth(100)"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
              :visible="handlerColumns.find(a => a.field === 'remark').visible"
              title="备注"
            >
              <template v-slot:header="{ column, $index }">
                <span class="custom-header">
                  <span>{{ column.title }}</span>
                  <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  />
                </span>
              </template>
              <template #default="{ row }">
                {{ row.remark }}
              </template>
              <template #edit="{ row }">
                <vxe-input v-model="row.remark" :clearable="false"></vxe-input>
              </template>
            </vxe-column>
            <template #empty>
              <span
                style="
                  color: #898989;
                  font-size: 14px;
                  display: block;
                  margin: 25px 0;
                "
              >
                <img :src="getUrl('newCsProject/none.png')" />
              </span>
            </template>
          </vxe-table>
        </div>
        <div
          class="table-content"
          v-if="
            handlerColumns.length > 0 &&
            projectStore.asideMenuCurrentInfo?.code == '99'
          "
        >
          <threeMaterials ref="threeMaterialsRef" :scList="scList" />
        </div>
      </template>
      <template #two>
        <template v-if="projectStore.currentTreeInfo.type != 3">
          <p class="selectTab">
            <a-radio-group
              v-model:value="selectdTab"
              :style="{ marginBottom: '8px' }"
            >
              <a-radio-button :value="item.key" v-for="item of activeOptions">{{
                item.tab
              }}</a-radio-button>
            </a-radio-group>
          </p>

          <keep-alive>
            <component
              ref="componentRef"
              :is="components.get(selectdTab)"
              @getUpList="getHumanMachineData"
              @upDateMarketPrice="upDateMarketPrice"
              :showInfo="currentInfo"
              :currentData="currentInfo"
              :originalCurrentData="originalCurrentInfo"
              :mergeFormData="mergeFormData"
            ></component>
          </keep-alive>
        </template>
        <machine-service
          v-else
          ref="machineServiceRef"
          @getUpList="getHumanMachineData"
          @upDateMarketPrice="upDateMarketPrice"
          :showInfo="currentInfo"
          :mergeFormData="mergeFormData"
        ></machine-service>
      </template>
    </split>
  </div>

  <common-modal
    className="dialog-comm"
    :title="typeModal"
    :width="
      typeModal == `${classifyType === '1' ? '新建' : '编辑'}汇总分类` ||
      typeModal == '人材机无价差'
        ? 400
        : 1020
    "
    :height="
      typeModal == '载价编辑'
        ? 560
        : typeModal == '载价报告'
          ? 500
          : typeModal == `${classifyType === '1' ? '新建' : '编辑'}汇总分类`
            ? 'auto'
            : typeModal == '人材机无价差'
              ? 180
              : 530
    "
    v-model:modelValue="reportModel"
    @cancel="cancel"
    @close="reportModel = false"
    :mask="true"
    :lockView="true"
    destroy-on-close
  >
    <!--     :mask="typeModal == '载价编辑' ? false : true"
      :lockView="typeModal == '载价编辑' ? false : true" -->
    <batch-load-price
      v-if="typeModal == '批量载价'"
      @close="close"
    ></batch-load-price>
    <edit-load-price
      v-if="typeModal == '载价编辑'"
      :propsData="propsData"
      @close="close"
    ></edit-load-price>
    <report-load-price v-if="typeModal == '载价报告'"></report-load-price>
    <new-add-classify
      v-if="typeModal == `${classifyType === '1' ? '新建' : '编辑'}汇总分类`"
      :classifyType="classifyType"
      :cellectType="cellectType"
      @updateMenu="updateHztype"
      :menudata="menuData"
      :menuList="menuList"
      @close="close"
    ></new-add-classify>
    <no-price-difference
      v-if="typeModal == '人材机无价差'"
      :currentInfo="quotaHeaderData"
      :selectList="humanTable.getCheckboxRecords()"
      @close="close"
      @updateData="getHumanMachineData()"
    ></no-price-difference>
  </common-modal>

  <!-- 关联定额弹窗 -->
  <quota-popup
    v-if="quotaPopupVisible"
    :levelType="projectStore.currentTreeInfo.type"
    :HeaderData="quotaHeaderData"
    @closeDialog="quotaPopupVisible = false"
  ></quota-popup>

  <!-- 设置主要材料显示隐藏 -->
  <SetMainMaterials
    :tableData="allRCJTableData"
    @successCallback="getHumanMachineData"
    v-model:materialVisible="materialVisible"
  />

  <areaModal
    v-if="areaStatus"
    :type="areaVisibleType"
    @closeDialog="closeAreaModal"
  ></areaModal>
  <!-- 汇总范围弹框 -->
  <summary-scope
    ref="summaryScopeRef"
    v-if="isSummaryScope"
    @closeDialog="isSummaryScope = false"
    @querySummary="querySummary"
  ></summary-scope>
  <LookupFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="
      row => {
        lookupCallback(row, 'look');
      }
    "
    @changeCurrentInfo="changeCurrentInfo"
  />
  <FiltErate
    v-if="filterateInit"
    ref="filtErateRef"
    v-model:filterateVisible="filterateVisible"
    v-model:tableData="tableData"
    v-model:originalTableData="originalTableData"
    @lookupCallback="
      row => {
        lookupCallback(row, 'FiltErate');
      }
    "
    @saveHistoryCondition="saveHistoryCondition"
  />
  <common-modal
    className="dialog-comm area-modal"
    width="500"
    @close="
      () => {
        deleteAllVisible = false;
      }
    "
    v-model:modelValue="deleteAllVisible"
    title="删除所有批注"
  >
    <div class="tree-content-wrap">
      <div class="group-list">
        是否删除所有批注？点击确定后将清除{{
          projectStore.currentTreeInfo.type === 1
            ? '项目'
            : projectStore.currentTreeInfo.type === 2
              ? '单项'
              : '单位'
        }}层级人材机汇总所有批注。
      </div>
      <div class="footer-btn-list">
        <a-button
          @click="
            () => {
              deleteAllVisible = false;
            }
          "
          >取消</a-button
        >
        <a-button
          type="primary"
          @click="
            () => {
              deleteAllVisible = false;
              closeAreaModal(1);
            }
          "
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
  <!-- 智能询价 -->
  <inquiryPopup
    v-if="priceVisible"
    :info="currentInfo"
    :type="
      [1, 2].includes(projectStore.currentTreeInfo.type)
        ? 'HumanMachineSummaryOne'
        : 'HumanMachineSummary'
    "
    @closeDialog="closeInquiryPopup"
  ></inquiryPopup>
  <!-- 合并相似材料 -->
  <mergeMaterials
    ref="mergeMaterialsRef"
    v-if="mergeStatus"
    :formData="mergeFormData"
    @refresh="mergeRefresh"
    @closeDialog="mergeCloseDialog"
    @selfTestLocateTable="selfTestLocateTable"
  >
  </mergeMaterials>
  <!-- 调整市场价系数 -->
  <common-modal
    className="dialog-comm"
    title="调整市场价系数"
    width="300"
    height="200"
    v-model:modelValue="adjustFactor"
    :mask="true"
  >
    <div class="adjustFactorMoadl">
      <div>
        <span> 市场价系数： </span>
        <a-input
          v-model:value="marcketFactor"
          placeholder="请输入市场价系数"
          @blur="marcketFactor = selfCheck(marcketFactor, 0, 1000)"
          @keyup="
            marcketFactor = (marcketFactor.match(
              /-?\d{0,8}(\.\d{0,4}|100)?/
            ) || [''])[0]
          "
        />
      </div>
      <p class="footor">
        <a-button @click="adjustFactor = false">取消</a-button>
        <a-button type="primary" @click="sureOrCancel()">确定</a-button>
      </p>
    </div>
  </common-modal>

  <!-- 替换人材机 -->
  <material-machine-index
    v-model:indexVisible="indexVisible"
    v-model:currentMaterialInfo="currentInfo"
    :currentInfo="currentInfo"
    :indexLoading="indexLoading"
    businessType="HumanMachineSummary"
    @currentRcjInfo="currentRcjInfo"
    @addChildrenRcjData="addChildrenRcjData"
    @currentInfoReplace="currentInfoReplace"
  ></material-machine-index>
</template>

<script setup>
import {
  onMounted,
  onActivated,
  onDeactivated,
  onUpdated,
  ref,
  toRaw,
  watch,
  getCurrentInstance,
  provide,
  inject,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  watchEffect,
  markRaw,
} from 'vue';
import xeUtils from 'xe-utils';
import mergeMaterials from './mergeMaterials.vue';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import MachineService from './MachineService.vue';
import feePro from '@gongLiaoJi/api/feePro';
import loadApi from '../../../../api/loadPrice';
import infoMode from '@/plugins/infoMode.js';
import { disposeDeTypeData } from '@gongLiaoJi/hooks/publicApiData';
import csProject from '@gongLiaoJi/api/csProject';
import { getUrl, pureNumber, pureNumber0 } from '@/utils/index';
import HumanHeader from './HumanHeader.vue';
import { insetBus } from '@gongLiaoJi/hooks/insetBus';
import MaterialMachineIndex from '@gongLiaoJi/views/projectDetail/customize/materialMachineIndex/index.vue';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
import { CloseOutlined } from '@ant-design/icons-vue';
const { decimalFormat, getDecimalPlaces } = useDecimalPoint();

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
import operateList, { updateOperateByName } from '../operate';

import BatchLoadPrice from './BatchLoadPrice.vue';
import EditLoadPrice from './EditLoadPrice.vue';
import ReportLoadPrice from './ReportLoadPrice.vue';
import NewAddClassify from './NewAddClassify.vue';
import noPriceDifference from './noPriceDifference.vue';
import summaryScope from './summaryScope.vue';
import threeMaterials from './threeMaterials.vue';
import deMapFun from '../deMap';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';
import gSdetailApi from '@/gongLiaoJiProject/api/projectDetail.js';

import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import tableColumns from './tableColumns';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import Decimal from 'decimal.js';
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
import { DownOutlined } from '@ant-design/icons-vue';
import { useDragFillCell } from '@gongLiaoJi/hooks/useDragFillCell';
let { updateGljSelrowId } = recordProjectData();
// 设置主要材料
const SetMainMaterials = defineAsyncComponent(
  () => import('./SetMainMaterials.vue')
);

const customCellBeforeEditMethod = () => {
  return cellBeforeEditMethod();
};

console.log('tableColumns阿斯顿撒大', tableColumns);
let currentInfo = ref(null);
let reportModel = ref(false);
let priceVisible = ref(false);
let typeModal = ref(null); //弹框类型
let propsData = ref(); //载价编辑的优先级
const isSummaryScope = ref(false); //汇总范围
const summaryScopeRef = ref(); //汇总范围ref
const summarySequenceNbr = ref([]); //汇总范围选择的定额
const emit = defineEmits(['updateMenuList']);
const intNum = ref(0);
let deleteAllVisible = ref(false);
let areaStatus = ref(false);
let areaVisibleType = ref('');
let AnnotationsCurrent = ref(null);
let AnnotationsRefList = ref({});
const isFiltErate = ref(false);
const filtErateRef = ref();
let threeMaterialsRef = ref();
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import { columnWidth } from '@gongLiaoJi/hooks/useSystemConfig.js';

const quotaPopup = defineAsyncComponent(
  () => import('@/components/gljSummaryPopup/index.vue')
);
const inquiryPopup = defineAsyncComponent(
  () => import('../quotaInfo/inquiryPopup/index.vue')
);
let unifyData = operateList.value.find(
  item => item.name == 'unify-humanMachineSummary'
);
let isLoad = operateList.value.find(item => item.name == 'batch-loadprice');

let quotaPopupBtn = operateList.value.find(item => item.name == 'quota-popup'); //查询关联定额
let filterateBtn = operateList.value.find(item => item.name == 'filterate'); //过滤按钮

// let isSeeReport = operateList.value.find(
//   item => item.name == 'loadprice-report'
// );
let quotaHeaderData = ref(null);
let isCurrent = ref(null);
let scList = ref([]);
let colorUnitList = ref([]);
let oldData = ref([]); //工程项目级别人材机汇总旧数据
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
  menuList: {
    type: Array,
    default: () => [],
  },
});
let humanTable = ref();
let loading = ref(false);
let adjustFactor = ref(false); //调整市场价系数
let marcketFactor = ref('1');
let oldMarketFactor = ref('1');
const projectStore = projectDetailStore();
let tableData = ref([]);
// let tableDataOnshow = ref([]);
let upDateRow = ref();
let cellectType = ref([]);
let newAddClassifyRef = ref(null);
let classifyType = ref('1');
let menuData = ref({});

const { isMove } = inject('mainData');
const allFilters = ['人工费', '材料费', '机械费', '主材费', '设备费'];
const filter = reactive({
  checkedList: allFilters,
  options: [
    {
      label: '人工费',
      value: '人工费',
    },
    {
      label: '材料费',
      value: '材料费',
    },
    {
      label: '机械费',
      value: '机械费',
    },
    {
      label: '主材费',
      value: '主材费',
    },
    {
      label: '设备费',
      value: '设备费',
    },
  ],
});

const tableDataOnshow = computed(() => {
  let tableList = [...tableData.value];
  if (
    projectStore.componentId === 'humanMachineSummary' &&
    projectStore.asideMenuCurrentInfo?.code == '14'
  ) {
    tableList = [...tableData.value].filter(item =>
      filter.checkedList.includes(item.type)
    );
  }
  return tableList;
});

const keyboardConfig = {
  isArrow: true,
};

const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  resetIsRunEvent,
  currentCellData,
  useKeyDownEvent,
  getSelectedColumn,
  handleKeyDownCustom,
} = useCellClick({
  rowKey: 'sequenceNbr',
  tableData: tableDataOnshow,
  currentInfo: currentInfo,
  vexTableRef: humanTable,
  currentChange: currentChange,
});

// 初始统一修改操作按钮属性
const initSetOperateBtnDisabled = () => {
  const list = [
    {
      // 设置主要材料显示隐藏
      name: 'set-main-materials',
      callback: item => {
        item.levelType =
          Number(projectStore.asideMenuCurrentInfo.code) == 7 ? [3] : [];
      },
    },
  ];
  list.forEach(item => {
    updateOperateByName(item.name, item.callback);
  });
};

const ifProvisionalEstimateDisabled = row => {
  if (row.isFyrcj == 0) {
    return false;
  } else {
    return ![2, 4, 5, 6, 7, 8, 9, 10].includes(+row.kind);
  }
};
watch(
  [
    () => projectStore.convenienceSettings.get('PRICING_ZSDIFF'),
    () => projectStore.convenienceSettings.get('PRICING_RCJDIFF'),
  ],
  () => {
    if (projectStore.tabSelectName === '人材机汇总') {
      getHumanMachineData();
    }
  }
);

watch(
  () => projectStore.asideMenuCurrentInfo,
  val => {
    if (projectStore.tabSelectName === '人材机汇总' && val) {
      updateGljSelrowId(val.sequenceNbr, '人材机汇总', 'leftTwoTreeId');
      let whitelist = ['costView', 'onSelfCheck', 'unify-humanMachineSummary'];
      operateList.value.map(item => {
        if (!whitelist.includes(item.name)) {
          item.visible = val.code != '99';
        }
      });
      if (val.code != '99') {
        resetIsRunEvent();
      }

      initSetOperateBtnDisabled();
    }
  },
  { deep: true }
);

watch(
  () => [currentInfo.value, tableData.value],
  (newValue, oldValue) => {
    let val = newValue[0];

    // 调整费用系数
    // marketPriceBtn.disabled = val?.isFyrcj == 0;

    let tableList = newValue[1];

    quotaPopupBtn.disabled = !tableList.length;

    if (tableList.length === 0) {
      isMove.value.isFirst = true;
      isMove.value.isLast = true;
    } else {
      if (
        projectStore.currentTreeInfo.levelType !== 2 &&
        projectStore.tabSelectName === '人材机汇总'
      ) {
        if (tableList.findIndex(a => a.sequenceNbr == val?.sequenceNbr) === 0) {
          isMove.value.isFirst = true;
        } else {
          isMove.value.isFirst = false;
        }
        if (
          tableList.findIndex(a => a.sequenceNbr == val?.sequenceNbr) ===
          tableList.length - 1
        ) {
          isMove.value.isLast = true;
        } else {
          isMove.value.isLast = false;
        }
      }
    }
  }
);

let originalCurrentInfo = ref();
watch(
  () => currentInfo.value,
  newVal => {
    if (newVal) {
      updateGljSelrowId(newVal.sequenceNbr, '人材机汇总', 'selRowId');
      if (originalTableData) {
        originalCurrentInfo.value = originalTableData.find(
          item => item.sequenceNbr == newVal.sequenceNbr
        );
      }
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => projectStore.tabSelectName,
  () => {
    if (projectStore.tabSelectName === '人材机汇总') {
      unifyData.disabled = true;
    } else {
      let whitelist = ['costView', 'onSelfCheck', 'unify-humanMachineSummary'];
      operateList.value.map(item => {
        if (!whitelist.includes(item.name)) {
          item.visible = true;
        }
      });
      isCurrent.value = null;
    }
  }
);

// 设置主要材料
let materialVisible = ref(false);
let allRCJTableData = ref([]);
const openSetMainMaterial = () => {
  materialVisible.value = true;
};
const closeSetMainMaterial = () => {
  materialVisible.value = false;
};

const getsc = () => {
  csProject.getKindSc().then(res => {
    scList.value = res.result;
  });
};
getsc();
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
} = useFormatTableColumns({
  type: [1, 1, 3, 2][projectStore.currentTreeInfo.type],
  initColumnsCallback: () => {
    initColumns({
      columns: tableColumns,
    });
  },
});

// 根据左侧点击菜单树的层级，拿到项目，单项，单位的批注字段
const AnnotationFelid = computed(() => {
  return [
    'annotationsPro',
    'annotationsPro',
    'annotationsSingle',
    'annotations',
  ][projectStore.currentTreeInfo.type];
});

// 根据左侧点击菜单树的层级，拿到项目，单项，单位的是否显示批注字段
const isShowAnnotationFelid = computed(() => {
  return [
    'isShowAnnotationsPro',
    'isShowAnnotationsPro',
    'isShowAnnotationsSingle',
    'isShowAnnotations',
  ][projectStore.currentTreeInfo.type];
});

const quotaPopupVisible = ref(false); // 关联定额弹窗
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'search',
          name: '查询关联定额',
          visible: true,
          disabled: false,
        },
        {
          code: 'remove',
          name: '清除载价',
          visible: true,
          disabled: false,
        },
        {
          code: 'noPriceDifference',
          name: '人材机无价差',
          visible: true,
          disabled: false,
        },
        {
          code: 'export',
          name: '导出excel',
          visible: false,
          disabled: false,
        },
        {
          code: 'noteList',
          name: '批注',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'add-note',
              name: '插入批注',
              type: 1,
              visible: false,
              disabled: false,
            },
            {
              code: 'edit-note',
              name: '编辑批注',
              type: 2,
              visible: false,
              disabled: false,
            },
            {
              code: 'del-note',
              name: '删除批注',
              type: 3,
              visible: false,
              disabled: false,
            },
            {
              code: 'show-note',
              name: '显示批注',
              type: 4,
              visible: false,
              disabled: false,
            },
            {
              code: 'hide-note',
              name: '隐藏批注',
              type: 5,
              visible: false,
              disabled: false,
            },
            {
              code: 'del-all-note',
              name: '删除所有批注',
              type: 6,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'export',
          name: '导出excel',
          visible: false,
          disabled: false,
        },
        {
          code: 'loadprice',
          name: '智能询价',
          visible: true,
          disabled: false,
        },
        {
          code: 'replace',
          name: '替换数据',
          visible: true,
          disabled: false,
        },
        {
          code: 'removeSort',
          name: '取消排序',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, row);
    currentInfo.value = row;
    getCurrentIndex(row);

    if (!row) return;
    // 普通专业的安装定额、措施定额、泵送定额的人材机均不可以编辑
    // for (let i of menuConfig.body.options[0]) {
    //   i.visible = isEditDe.value;
    // }

    let tar = menuConfig.body.options[0].find(a => a.name === '智能询价');
    if (
      ((row?.levelMark == 1 || row?.levelMark == 2) && row?.markSum == 1) ||
      row.unit === '元' ||
      row.isFyrcj == 0 ||
      row.supplementRcjFlag == 1 ||
      row.ifLockStandardPrice === 1
    ) {
      tar.disabled = true;
      //单位为元、单位为%、补充人材机、已锁定市场价、已勾选是否汇总/二次解析的父级材料 都不可以智能询价  这些材料都不可以智能询价
    } else {
      tar.disabled = false;
    }
    // if (row.libraryCode?.startsWith('2022')) {
    //   options[0][1].visible = false;
    //   // console.log('options[0][1]', options[0][1]);
    // }
    if (row.ifLockStandardPrice == 1) {
      options[0][2].disabled = true;
    } else {
      options[0][2].disabled = false;
    }
    // 如果批注不为空
    let AnnotationData = row[AnnotationFelid.value];
    let isShowAnnotationData = row[isShowAnnotationFelid.value];

    if (AnnotationData !== '' && AnnotationData !== undefined) {
      options[0][4].children[0].visible = false;
      options[0][4].children[1].visible = true;
      options[0][4].children[2].visible = true;
      if (isShowAnnotationData) {
        options[0][4].children[3].visible = false;
        options[0][4].children[4].visible = true;
      } else {
        options[0][4].children[3].visible = true;
        options[0][4].children[4].visible = false;
      }
    } else {
      options[0][4].children[0].visible = true;
      options[0][4].children[1].visible = false;
      options[0][4].children[2].visible = false;
      options[0][4].children[3].visible = false;
      options[0][4].children[4].visible = false;
    }

    options[0].forEach(e => {
      if (e.code == 'replace') {
        e.disabled = row.isFyrcj === 0;
        e.visible = projectStore.currentTreeInfo.type == 3;
      }
      if (e.code == 'removeSort') {
        e.disabled = !isSort.value;
        e.visible = true;
      }

      if (e.code == 'remove') {
        e.disabled =
          row.ifProvisionalEstimate == 1 ||
          !row.highlight ||
          row.sourcePrice == '自行载价';
        e.visible = true;
      }
    });

    return true;
  },
});
/**
 * 关闭了智能询价
 * @param {*} v  true 点击了确定按钮  false 点击了取消按钮
 */
const closeInquiryPopup = v => {
  priceVisible.value = false;
  if (v) {
    projectStore.SET_HUMAN_UPDATA_DATA(null);
    unifyData.disabled = true;
    getHumanMachineData();
  }
};

let prevTaxRate = ref(0);
// 税率失去焦点的blur事件
const taxRateBlur = row => {
  if (row.taxRate >= 0) {
    row.taxRate = pureNumber0(row.taxRate);
    materialCodeChange(
      row,
      projectStore.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice'
    );
    prevTaxRate.value = row.taxRate;
  } else {
    tableData.value.map(item => {
      if (item.sequenceNbr == row.sequenceNbr) {
        item.taxRate = prevTaxRate.value;
      }
    });
    const $table = humanTable.value;
    $table.revertData(currentInfo.value, 'taxRate');
  }
};

const editAnnotations = row => {
  row.noteEditVisible = true;
  AnnotationsCurrent.value = row.sequenceNbr;
  nextTick(() => {
    AnnotationsRefList.value[row.sequenceNbr]?.focusNode();
  });
};
const onFocusNode = row => {
  if (AnnotationsCurrent.value != row.sequenceNbr) {
    console.log('🚀 ~手动选中');
    editAnnotations(row);
  }
};

// 关闭编辑的
const closeAnnotations = (v, row) => {
  let AnnotationData = row[AnnotationFelid.value];
  let isShowAnnotationData = row[isShowAnnotationFelid.value];
  if (!isShowAnnotationData) {
    row.noteEditVisible = false;
    row.noteViewVisible = false;
  }
  if (v == AnnotationData) {
    return;
  }

  let postData = {
    ...row,
  };

  postData[AnnotationFelid.value] = v;
  upDate({ ...postData }, AnnotationFelid.value);
};

const cellMouseEnterEventCustom = ({ row, rowIndex, column, $event }) => {
  if (['specification'].includes(column.field) && row[AnnotationFelid.value]) {
    row.noteViewVisible = true;
  }
};
// 处理如果鼠标移出，在移入时，出现短连的情况
const processingData = dragFillInfo => {
  const { rows, startRow, startRowIndex, column, endRowIndex } = dragFillInfo;
  const idList = [];
  const updatedRows = [];

  const commonFieldHandler = (item, field, defaultValue = 0) => {
    if (item.checkIsShow) {
      item[field] = startRow[field] == null ? 0 : startRow[field]; // 使用空值合并运算符
      idList.push(item.sequenceNbr);
    }
  };
  // 定义字段处理逻辑
  const fieldHandlers = {
    // 供货方式
    ifDonorMaterial: item => commonFieldHandler(item, column.field),
    // 是否暂估
    ifProvisionalEstimate: item => commonFieldHandler(item, column.field),
    // 市场价锁定
    ifLockStandardPrice: item => commonFieldHandler(item, column.field),
  };

  // 获取需要处理的数据范围
  console.log(startRowIndex, endRowIndex);
  let arrList = [];
  if (startRowIndex > endRowIndex) {
    arrList = tableData.value.slice(endRowIndex, startRowIndex + 1).reverse();
  } else {
    arrList = tableData.value.slice(startRowIndex, endRowIndex + 1);
  }

  // 遍历处理数据
  arrList.forEach((item, index) => {
    const handler = fieldHandlers[column.field];
    if (handler) {
      handler(item);
    }

    // 更新 dragFillInfo.rows
    updatedRows.push({
      rowIndex: startRowIndex + index, // 动态计算 rowIndex
      row: item,
    });
  });

  // 更新 dragFillInfo
  dragFillInfo.rows = updatedRows;
  dragFillInfo.rowIds = idList;
};

const useDragCb = async dragFillInfo => {
  const { rows, startRow, column, rowIds } = dragFillInfo;
  const idList = [];
  // 定义字段处理逻辑
  // const fieldHandlers = {
  //   // 供货方式
  //   'ifDonorMaterial': (item) => {
  //     if (item.row.checkIsShow) {
  //       item.row[column.field] = startRow[column.field];
  //       idList.push(item.row.sequenceNbr);
  //     }
  //   },
  //   //是否暂估
  //   'ifProvisionalEstimate': (item) => {
  //     if (item.row.checkIsShow && !['人工费', '机械费'].includes(item.row.type) &&
  //         !(otherCodeList.includes(item.row.materialCode) && item.row.isBfh)) {
  //       item.row[column.field] = startRow[column.field] == null ? 0 : startRow[column.field];// 首次创建的数据 为undefined
  //       idList.push(item.row.sequenceNbr);
  //     }
  //   },
  //   //市场价锁定
  //   'ifLockStandardPrice': (item) => {
  //     if (item.row.checkIsShow) {
  //       item.row[column.field] = startRow[column.field] == null ? 0 : startRow[column.field];// 首次创建的数据 为undefined
  //       idList.push(item.row.sequenceNbr);
  //     }
  //   }
  // };
  const commonFieldHandler = (item, field, defaultValue = 0) => {
    if (item.checkIsShow) {
      idList.push(item.sequenceNbr);
    }
  };
  // 定义字段处理逻辑
  const fieldHandlers = {
    // 供货方式
    ifDonorMaterial: item => commonFieldHandler(item, column.field),
    // 是否暂估
    ifProvisionalEstimate: item => {
      if (
        item.checkIsShow &&
        !['人工费', '机械费'].includes(item.type) &&
        item.isFyrcj !== 0
      ) {
        commonFieldHandler(item, column.field);
      }
    },
    // 市场价锁定
    ifLockStandardPrice: item => commonFieldHandler(item, column.field),
  };
  // 遍历 rows 并根据字段类型调用相应的处理逻辑
  rows.forEach(item => {
    const handler = fieldHandlers[column.field];
    if (handler) {
      handler(item.row);
    }
    // 根据 levelType 决定是否调用 CheckboxChange
    if (projectStore.currentTreeInfo.type < 3) {
      CheckboxChange(item.row, column.field);
    }
  });

  // 如果 levelType 为 3，发送 API 请求
  if (projectStore.currentTreeInfo.type == 3) {
    try {
      // 构造 API 请求数据
      const apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        column: column.field,
        value: startRow[column.field],
        idList: idList,
      };

      console.log('csProject.unitRcjBatchUpdate 下拉填充参数=》', apiData);
      const res = await csProject.unitRcjBatchUpdate(apiData);
      console.log('csProject.unitRcjBatchUpdate 下拉填充返回值=》', res);

      if (res.status === 200 && res.code === 200) {
        console.log('csProject.unitRcjBatchUpdate 下拉填充完成');
        await getHumanMachineData();
      }
    } catch (error) {
      console.error('csProject.unitRcjBatchUpdate 错误', error);
    }
  }
};
const cellMouseLeaveEvent = ({ row, rowIndex, column, $event }) => {
  if (
    ['specification'].includes(column.field) &&
    !row?.noteEditVisible &&
    row[AnnotationFelid.value] &&
    !row[isShowAnnotationFelid.value]
  ) {
    row.noteViewVisible = false;
  }
};
const getAnnotationsRef = (el, row) => {
  if (el) {
    AnnotationsRefList.value[row.sequenceNbr] = el;
  } else {
    AnnotationsRefList.value[row.sequenceNbr] = null;
  }
};
const closeAreaModal = v => {
  areaStatus.value = false;
  if (v) {
    console.log(
      '🚀 ~ closeAreaModal ~ areaVisibleType.value:',
      areaVisibleType.value
    );
    let delName =
      areaVisibleType.value == 'note-all'
        ? 'delBatchAnnotationsController'
        : 'batchRmoveMainQdController';
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      // levelType: v=='1'?1:3,
      levelType: +projectStore.currentTreeInfo.type,
    };
    if (projectStore.currentTreeInfo.type === 3) {
      apiData['isAllUnit'] = v == '1' ? true : false;
    }
    gSdetailApi[delName](apiData)
      .then(res => {
        getHumanMachineData(1);
      })
      .finally(() => {
        areaVisibleType.value = '';
      });
  }
};
// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (!row) return;

  if (['replace'].includes(menu.code)) {
    indexVisible.value = true;
    return;
  }

  if (['removeSort'].includes(menu.code)) {
    removeRcjCellectSort();
    return;
  }

  if (['edit-note', 'add-note', 'show-note'].includes(menu.code)) {
    // 批注相关的，根据是否显示字段处理
    const isShowNoteField = handlerColumns.value.find(
      a => a.field === 'specification'
    ).visible;

    if (!isShowNoteField) {
      return message.warning('规格型号列已隐藏，请展开后操作！');
    }
  }

  switch (menu.code) {
    case 'edit-note':
    case 'add-note':
      editAnnotations(row);
      break;
    case 'del-note':
      upDate({ ...row, [AnnotationFelid.value]: '' }, AnnotationFelid.value);

      upDate(
        { ...row, [isShowAnnotationFelid.value]: false },
        isShowAnnotationFelid.value
      );

      // if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
      //   upDate({ ...row, annotationsPro: '' }, 'annotationsPro');
      //   upDate({ ...row, isShowAnnotationsPro: false }, 'isShowAnnotationsPro');
      // } else {
      //   upDate({ ...row, annotations: '' }, 'annotations');
      //   upDate({ ...row, isShowAnnotations: false }, 'isShowAnnotations');
      // }
      break;
    case 'show-note':
      upDate(
        { ...row, [isShowAnnotationFelid.value]: true },
        isShowAnnotationFelid.value
      );
      // if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
      //   upDate({ ...row, isShowAnnotationsPro: true }, 'isShowAnnotationsPro');
      // } else {
      //   upDate({ ...row, isShowAnnotations: true }, 'isShowAnnotations');
      // }
      break;
    case 'hide-note':
      upDate(
        { ...row, [isShowAnnotationFelid.value]: false },
        isShowAnnotationFelid.value
      );
      break;
    case 'del-all-note':
      if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
        deleteAllVisible.value = true;
      } else {
        areaStatus.value = true;
      }
      areaVisibleType.value = 'note-all';
      break;
    case 'search':
      quotaHeaderData.value = row;
      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData('', () => {
          quotaPopupVisible.value = true;
        });
      } else {
        quotaPopupVisible.value = true;
      }
      break;
    case 'noPriceDifference':
      typeModal.value = '人材机无价差';
      quotaHeaderData.value = row;

      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData('', () => {
          reportModel.value = true;
        });
      } else {
        reportModel.value = true;
      }
      break;
    case 'export':
      exportExcel('all');
      break;
    case 'loadprice':
      priceVisible.value = true;
      break;
    case 'remove':
      // 掉接口恢复系统默认值
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '该条材料市场价已被锁定，'
            : '是否确定清除选中数据的载价数据？',
        descText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '请取消勾选后再进行清除载价操作'
            : '删除后无法撤销恢复',
        isFunction: false,
        isSureModal: +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice,
        confirm: () => {
          if (!(+row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice)) {
            clearZaijia(row);
          }
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
      break;
  }
};
const exportExcel = (dataType = '') => {
  const $table = humanTable.value;
  if (dataType !== 'all' && $table.getCheckboxRecords().length === 0) {
    message.info('请选择导出数据');
    return;
  }
  console.log('请选择导出数据', $table.getCheckboxRecords(), tableData.value);
  $table.exportData({
    filename: '人材机汇总导出报表' + new Date().getTime(),
    sheetName: 'Sheet1',
    type: 'xlsx',
    // types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
    // sheetMethod: sheetMethod, // 配置导出样式
    useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
    isFooter: true, //是否导出表尾（比如合计）
    data:
      dataType === 'all' ? tableDataOnshow.value : $table.getCheckboxRecords(),
    columnFilterMethod({ column, $columnIndex }) {
      console.log('人材机汇总表导出数据', column);
      return !($columnIndex === 0) && !['市场价锁定'].includes(column.title);
    },
  });
};
// 清除载价格
const clearZaijia = data => {
  let postData = {
    type: [1, 1, 3, 2][projectStore.currentTreeInfo.type],
    rcj: toRaw(data),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    sequenceNbr: data.sequenceNbr,
  };
  if (projectStore.currentTreeInfo.type == 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  csProject.clearLoadPriceUse(postData).then(res => {
    if (res.result) {
      message.success('清除成功');
      if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
        // data.isChange = true; //标识编辑行
        // getSameUnit();
        projectStore.SET_HUMAN_UPDATA_DATA(null);
        unifyData.disabled = true;
      }
      getHumanMachineData();
    }
  });
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};
// 切换供应方式
const ifDonorMaterialChange = row => {
  console.info(22222222222, row.ifDonorMaterial);
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    if (row.ifDonorMaterial == '0') {
      row.donorMaterialNumber = '';
    } else {
      row.donorMaterialNumber = row.totalNumber;
    }
    if (row.ifDonorMaterial == '1') {
      const taxMethod = +projectStore.taxMade;
      const pricingMethod = +projectStore.pricingMethod;
      if (taxMethod === 1 && pricingMethod === 0) {
        // 一般计税且非市场价组价，取不含税基期价
        row.donorMaterialPrice = row.baseJournalPrice;
      } else if (taxMethod === 1 && pricingMethod === 1) {
        // 一般计税且市场价组价，取不含税市场价
        row.donorMaterialPrice = row.marketPrice;
      } else if (taxMethod === 0 && pricingMethod === 0) {
        // 简易计税且非市场价组价，取含税基期价
        row.donorMaterialPrice = row.baseJournalTaxPrice;
      } else if (taxMethod === 0 && pricingMethod === 1) {
        // 简易计税且市场价组价，取含税市场价
        row.donorMaterialPrice = row.marketTaxPrice;
      }
    } else {
      row.donorMaterialPrice = '';
    }
  }
  clear();
};

/**
 * 处理甲供价格展示
 * @param row
 * @param v
 */
const handlePrice = row => {
  if (row) {
    if (row.ifDonorMaterial == '0') {
      row.donorMaterialNumber = '';
    } else {
      // row.donorMaterialNumber = row.totalNumber;
    }
    if (row.ifDonorMaterial == '1') {
      const taxMethod = +projectStore.taxMade;
      const pricingMethod = +projectStore.pricingMethod;
      if (taxMethod === 1 && pricingMethod === 0) {
        // 一般计税且非市场价组价，取不含税基期价
        row.donorMaterialPrice = row.baseJournalPrice;
      } else if (taxMethod === 1 && pricingMethod === 1) {
        // 一般计税且市场价组价，取不含税市场价
        row.donorMaterialPrice = row.marketPrice;
      } else if (taxMethod === 0 && pricingMethod === 0) {
        // 简易计税且非市场价组价，取含税基期价
        row.donorMaterialPrice = row.baseJournalTaxPrice;
      } else if (taxMethod === 0 && pricingMethod === 1) {
        // 简易计税且市场价组价，取含税市场价
        row.donorMaterialPrice = row.marketTaxPrice;
      }
    } else {
      row.donorMaterialPrice = '';
    }
    return row.donorMaterialPrice;
  }
};

/**
 *
 * @param row
 * @param type marketPrice 不含税市场价 baseJournalPrice 不含税基期价
 * marketTaxPrice 含税市场价   baseJournalTaxPrice 含税基期价
 */
// 市场价修改
const materialCodeChange = (row, type = 'marketPrice') => {
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    row.donorMaterialPrice = '';
    if (projectStore.taxMade == 1) {
      // 不含税基期价
      if (row.marketPrice == row.baseJournalPrice) {
        row.sourcePrice = '';
      } else {
        row.sourcePrice = '自行询价';
      }
    } else {
      if (row.marketTaxPrice == row.baseJournalTaxPrice) {
        row.sourcePrice = '';
      } else {
        row.sourcePrice = '自行询价';
      }
    }
  }
  clear();
};

// 甲供价修改
const donorMaterPriceChange = row => {
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    row.marketPrice =
      row.donorMaterialPrice == '' ? 0 : +row.donorMaterialPrice;

    let checkData = 0;
    if (projectStore.taxMade == 1) {
      // 不含税基期价
      checkData = row['baseJournalPrice'];
    } else {
      checkData = row['baseJournalTaxPrice'];
    }

    if (row.marketPrice == checkData) {
      row.sourcePrice = '';
    } else {
      row.sourcePrice = '自行询价';
    }
  }
  clear();
};

const keyupDonorMaterialNumber = ({ value }) => {
  // const data = value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3');
  const data = pureNumber0(value);
  currentInfo.value.donorMaterialNumber = data;
  for (let i of tableData.value) {
    if (i.sequenceNbr == currentInfo.value.sequenceNbr) {
      // 退出循环
      i.donorMaterialNumberCopy = data;
      break;
    }
  }
};

const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table?.clearEdit();
};
const selfTestLocateTable = row => {
  let data = tableData.value.find(item => item.sequenceNbr === row.sequenceNbr);
  humanTable.value.setCurrentRow(data);
  humanTable.value.scrollToRow(data);
};
let currentSelectCell = ref(null);
const editClosedEvent = async e => {
  const { $table, row, column } = e;
  const field = column.field;

  if (field == 'donorMaterialNumber' && row?.donorMaterialNumberCopy) {
    row.donorMaterialNumber = row.donorMaterialNumberCopy;
  }

  console.log('field------------', row, field);
  // 判断单元格值没有修改;
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }

  if (column.field == 'donorMaterialNumber' && isNaN(row.donorMaterialNumber)) {
    $table.revertData(row, field);
    return;
  }

  // 市场价、甲供价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (['marketPrice', 'donorMaterialPrice', 'marketTaxPrice'].includes(field)) {
    row[field] = +row[field];
  }

  let value = row[field];
  switch (field) {
    case 'unit':
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  row.ifDonorMaterial = row.ifDonorMaterial;
  if (field == 'marketPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field == 'marketPrice' &&
    value > 0 &&
    (row.marketPrice + '').length > 20
  ) {
    row.marketPrice = +(value + '').slice(0, 20);
  }

  if (field == 'marketTaxPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field == 'marketTaxPrice' &&
    value > 0 &&
    (row.marketTaxPrice + '').length > 20
  ) {
    row.marketTaxPrice = +(value + '').slice(0, 20);
  }

  if (field == 'donorMaterialNumber') {
    value = +value;
    console.log('value', value, row.totalNumber);
    if (row.ifDonorMaterial == 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
      return;
    } else if (row.ifDonorMaterial != 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
      return;
    } else if (
      row.ifDonorMaterial != 1 &&
      value <= row.totalNumber &&
      value > 0
    ) {
      row.ifDonorMaterial = '1';
    } else if (row.ifDonorMaterial == 1 && (value <= 0 || value == '')) {
      row.ifDonorMaterial = '0';
      row.donorMaterialNumber = '';
    }
  }
  if (field == 'kindSc' && [1, 2].includes(projectStore.currentTreeInfo.type)) {
    if (!row.transferFactor) {
      row.transferFactor = row[field] != '' ? 1 : 0;
    }
    if (row[field] == '' || row[field] == '空') {
      row.transferFactor = 0;
    }
  }
  if (field == 'transferFactor') {
    if (row.transferFactor === '') {
      row.transferFactor = 0;
    }
  }
  console.info('---------打印编辑结束------------', row);
  try {
    const targetCol = getSelectedColumn();
    if(targetCol) {
      currentSelectCell.value = targetCol
    }
    humanTable.value?.clearSelected();
  } catch (err) {
    console.log(err);
  }
  if (projectStore.currentTreeInfo.type == 3) {
    upDate(row, field);
  } else {
    row.isChange = true; //标识编辑行
    getSameUnit();
    let upDateList = getPropData(
      ['marketPrice', 'marketTaxPrice', 'taxRate'].includes(field)
    );

    if (['marketPrice', 'marketTaxPrice', 'taxRate'].includes(field)) {
      // 因为含税基数和不含税基数需要联动，所以特殊判断
      let rcjData = upDateList.find(
        item => item.sequenceNbr == row.sequenceNbr
      );
      const postData = {
        type:
          field == 'taxRate'
            ? projectStore.taxMade
            : 'marketPrice' == field
              ? 1
              : 0,
        rcj: {
          ...rcjData,
          marketPrice: +rcjData?.marketPrice,
          marketTaxPrice: +rcjData?.marketTaxPrice,
        },
      };
      const calculateTaxData = await csProject.calculateTax(postData);
      row.marketTaxPrice = calculateTaxData.marketTaxPrice;
      row.marketPrice = calculateTaxData.marketPrice;
      autoChangePrice(
        row,
        projectStore.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice'
      );

      materialCodeChange(row);
      humanTable.value.reloadRow(row, {});

      // 更新来源分析
      updateDataSource(row.sequenceNbr, {
        marketTaxPrice: row.marketTaxPrice,
        marketPrice: row.marketPrice,
        total: row.total,
        totalTax: row.totalTax,
      });
    } else {
      let datasourcefield = {};
      datasourcefield[field] = row[field];
      updateDataSource(row.sequenceNbr, datasourcefield);
    }

    $table.reloadRow(row, {});

    if (upDateList && upDateList.length > 0) {
      console.log('upDateList', upDateList, oldData.value);

      // 处理含税和不含税编辑后联动问题
      upDateList.forEach(item => {
        if (
          item.sequenceNbr == row.sequenceNbr &&
          ['marketPrice', 'marketTaxPrice', 'taxRate'].includes(field)
        ) {
          item.marketPrice = row.marketPrice;
          item.marketTaxPrice = row.marketTaxPrice;
        }
      });
      // }

      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: 'unify-humanMachineSummary',
        updataData: upDateList,
      });

      // message.success('修改成功！');
    }
  }
};

// 自动模拟价格修改
const autoChangePrice = (row, filed) => {
  if (!row[filed]) {
    return;
  }

  const totalNumber = new Decimal(
    decimalFormat(row.totalNumber, 'RCJ_COLLECT_TOTALNUMBER_PATH')
  );
  const marketPrice = new Decimal(
    decimalFormat(row.marketPrice, 'RCJ_COLLECT_MARKETPRICE_PATH')
  );
  const baseJournalPrice = decimalFormat(
    row.baseJournalPrice,
    'RCJ_COLLECT_BASEJOURNALPRICE_PATH'
  );

  const marketTaxPrice = new Decimal(
    decimalFormat(row.marketTaxPrice, 'RCJ_COLLECT_MARKETTAXPRICE_PATH')
  );
  const baseJournalTaxPrice = decimalFormat(
    row.baseJournalTaxPrice,
    'RCJ_COLLECT_BASEJOURNALTAXPRICE_PATH'
  );

  if (filed == 'marketPrice') {
    // 不含税市场价
    row.total = totalNumber.mul(marketPrice).toNumber().toString() ?? 0;

    row.priceDifferenc =
      new Decimal(marketPrice)
        .minus(new Decimal(baseJournalPrice))
        .toString() ?? 0;
  } else {
    row.totalTax = totalNumber.mul(marketTaxPrice).toNumber().toString() ?? 0;
    row.priceDifferenc =
      new Decimal(marketTaxPrice)
        .minus(new Decimal(baseJournalTaxPrice))
        .toString() ?? 0;
  }

  const priceDifferenc = decimalFormat(
    row.priceDifferenc,
    'RCJ_COLLECT_JC_PATH'
  );
  row.priceDifferencSum =
    new Decimal(totalNumber).mul(new Decimal(priceDifferenc)).toString() ?? 0;
};

// 设置汇总范围
const querySummary = sequenceNbr => {
  summarySequenceNbr.value = sequenceNbr;
  isSummaryScope.value = false;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  csProject.temporaryDataGet(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    getHumanMachineData(4, 1);
  });
};
const getPropData = (hasPrice = false) => {
  let constructProjectRcjList = [];
  let upDateList = tableData.value.filter(item => item.isChange == true);
  upDateList.map(item => {
    console.log(
      '🚀 ~ getPropData ~ item:',
      currentInfo.value.sequenceNbr,
      item.sequenceNbr
    );

    let obj = {};
    let same = oldData.value.filter(l => l.sequenceNbr == item.sequenceNbr)[0];

    // if (item.marketPrice !== same.marketPrice) {
    //   obj.marketPrice = item.marketPrice;
    // }

    obj.ifLockStandardPrice = item.ifLockStandardPrice;

    if (item.taxRate !== same.taxRate && item.taxRate !== null) {
      obj.taxRate = item.taxRate;
    }

    if (item.marketTaxPrice != same.marketTaxPrice) {
      obj.marketTaxPrice = item.marketTaxPrice;
    }

    if (item.marketPrice != same.marketPrice) {
      obj.marketPrice = item.marketPrice;
    }

    // 直接需要价格，增加当前行判断
    if (hasPrice && currentInfo.value.sequenceNbr == item.sequenceNbr) {
      obj.marketPrice = item.marketPrice;
      obj.marketTaxPrice = item.marketTaxPrice;
    }

    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial == 1) {
        obj.totalNumber = item.totalNumber;
      } else {
        obj.totalNumber = '';
      }
    }
    if (item.ifProvisionalEstimate != same.ifProvisionalEstimate) {
      obj.ifProvisionalEstimate = item.ifProvisionalEstimate;
    }
    // if (item.ifLockStandardPrice != same.ifLockStandardPrice) {
    //   obj.ifLockStandardPrice = item.ifLockStandardPrice;
    // }
    if (item.kindSc != same.kindSc) {
      obj.kindSc = item.kindSc;
    }
    if (item.transferFactor != same.transferFactor) {
      obj.transferFactor = item.transferFactor;
    }
    if (item.materialName != same.materialName) {
      obj.materialName = item.materialName;
    }
    if (item.supplyTime != same.supplyTime) {
      obj.supplyTime = item.supplyTime;
    }
    if (item.donorMaterialPrice != same.donorMaterialPrice) {
      obj.donorMaterialPrice = item.donorMaterialPrice;
    }
    if (item.remark != same.remark) {
      obj.remark = item.remark;
    }
    if (
      obj.hasOwnProperty('ifDonorMaterial') &&
      !obj.hasOwnProperty('totalNumber')
    ) {
      obj.totalNumber = obj.ifDonorMaterial == 1 ? item.totalNumber : '';
    }
    obj.sequenceNbr = item.sequenceNbr;
    obj.libraryCode = item.libraryCode;

    if (obj.hasOwnProperty('ifDonorMaterial')) {
      obj.ifDonorMaterial = +obj.ifDonorMaterial;
    }

    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const isUse = (type, callback = null) => {
  //点击统一应用按钮
  console.log(projectStore.humanUpdataData);
  if (!projectStore.humanUpdataData) {
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    levelType: +projectStore.currentTreeInfo.type,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(projectStore.humanUpdataData.updataData)
    ),
  };
  console.log('apiData111', apiData);
  //只是清除载价就传空值，清除载价+改市场价传修改数据
  csProject.changeRcjConstructProject(apiData).then(res => {
    if (res.status == 200) {
      message.success('应用成功！');
      projectStore.SET_HUMAN_UPDATA_DATA(null);
      getHumanMachineData();
      if (callback) {
        nextTick(() => {
          setTimeout(() => {
            callback();
          }, 100);
        });
      }
      unifyData.disabled = true;
      let obj = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      };
      csProject.temporaryDataDel(obj).then(res => {
        if (res.status !== 200) {
          return false;
        }
      });
    }
  });
};
const upDate = (row, field) => {
  console.log('1-------------1', row, field);
  let constructProjectRcj = {};
  if (
    field == 'materialName' ||
    field == 'specification' ||
    field == 'unit' ||
    field == 'ifProvisionalEstimate' ||
    field == 'ifLockStandardPrice' ||
    field == 'markSum' ||
    field == 'donorMaterialNumber' ||
    field == 'producer' ||
    field == 'manufactor' ||
    field == 'brand' ||
    field == 'deliveryLocation' ||
    field == 'annotations' ||
    field == 'isShowAnnotations' ||
    field == 'annotationsPro' ||
    field == 'annotationsSingle' ||
    field == 'isShowAnnotationsPro' ||
    field == 'isShowAnnotationsSingle' ||
    field == 'qualityGrade' ||
    field == 'kindSc' ||
    field == 'transferFactor' ||
    field == 'remark' ||
    field == 'supplyTime' ||
    field == 'donorMaterialPrice' ||
    field == 'taxRate' ||
    ['marketTaxPrice'].includes(field)
  ) {
    let rowData = ['marketTaxPrice', 'donorMaterialNumber'].includes(field)
      ? +row[field]
      : row[field];
    constructProjectRcj[field] = rowData;
  } else if (field == 'type') {
    constructProjectRcj.kind = getKind(row.type);
  } else if (field == 'ifDonorMaterial') {
    constructProjectRcj.ifDonorMaterial = row[field] ?? 0;
    console.log('row[field] ', row[field], row[field] == 1);
    if (row[field] == 1) {
      constructProjectRcj.totalNumber = row.totalNumber;
    }
  } else if (field == 'marketPrice') {
    constructProjectRcj.marketPrice = row[field];
  }
  if (field == 'donorMaterialNumber') {
    constructProjectRcj.totalNumber = row.totalNumber;
  }
  if (field == 'kindSc') {
    constructProjectRcj[field] = row[field];
    if (!row.transferFactor) {
      constructProjectRcj.transferFactor = row[field] != '' ? 1 : 0;
    }
    if (row[field] == '' || row[field] == '空') {
      constructProjectRcj.transferFactor = 0;
    }
  }
  // apiData.libraryCode = row.libraryCode;

  if (!['donorMaterialPrice'].includes(field)) {
    // 甲供价不需要这个字段ifDonorMaterial
    constructProjectRcj.ifDonorMaterial = Number(row.ifDonorMaterial ?? 0);
  }

  let typeData = {};
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    constructProjectRcj.sequenceNbr = row.sequenceNbr;
    typeData['constructProjectRcjList'] = [constructProjectRcj];
  } else {
    typeData['constructProjectRcj'] = constructProjectRcj;
  }
  let apiData = {
    levelType: projectStore.currentTreeInfo.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    ...typeData,
  };
  console.log('修改人材机数据', apiData);

  if (projectStore.currentTreeInfo.type == 2) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId;
  }

  if (projectStore.currentTreeInfo.type == 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  console.log('修改人材机数据返回结果', apiData);
  csProject.updateRcjCellect(apiData).then(res => {
    console.log('修改人材机数据返回结果', res);
    if (res.status == 200) {
      isCurrent.value = row.sequenceNbr;
      if (
        [
          'isShowAnnotations',
          'isShowAnnotationsSingle',
          'isShowAnnotationsPro',
        ].includes(field)
      ) {
        getHumanMachineData(3);
      } else {
        getHumanMachineData();
      }
      message.success('修改成功！');
    }
  });
};

// 合并相似材料
let mergeMaterialsRef = ref(null);
let mergeStatus = ref(false);
const mergeRefresh = () => {
  getHumanMachineData();
  // mergeCloseDialog();

  if (
    projectStore.asideMenuCurrentInfo?.code == '99' &&
    threeMaterialsRef.value &&
    threeMaterialsRef.value?.getTableData
  ) {
    threeMaterialsRef.value?.getTableData();
  }
};
/**
 * 确认或取消当前选择的人材机数据
 * @function sureOrCancel
 * @param {boolean} [hasCheck=false] - 是否有选中数据
 */
const sureOrCancel = () => {
  let hasCheck =
    humanTable.value.getCheckboxRecords().length === 0 ? false : true;
  if (!hasCheck) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '请选中要调整的人材机数据行',
      confirm: () => {
        infoMode.hide();
      },
    });
  }
  if (!hasCheck) return;
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    let updataDataList = [];
    humanTable.value
      .getCheckboxRecords()
      .filter(
        row =>
          row.ifLockStandardPrice !== 1 &&
          isPartEdit &&
          row.isFyrcj == 1 &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          Number(row.edit) !== 1 &&
          !deMapFun.isJxxs(row.materialCode) &&
          !deMapFun.isQtclf(row.materialCode) 
      )
      .map(async a => {
        let res = new Decimal(
          projectStore.taxMade == 1 ? a.marketPrice : a.marketTaxPrice
        )
          .mul(new Decimal(Number(marcketFactor.value)))
          .toNumber();

        let postData = {
          type: +projectStore.taxMade,
          rcj: {
            ...toRaw(a),
          },
        };
        console.log('🚀 ~ sureOrCancel ~ postData:', postData);

        if (projectStore.taxMade == 1) {
          // 不含税基期价  marketPrice  baseJournalPrice
          postData.rcj.marketPrice = res;
        }

        if (projectStore.taxMade == 0) {
          // 含税基期价  marketTaxPrice  baseJournalTaxPrice
          postData.rcj.marketTaxPrice = res;
        }

        const calculateTaxData = await csProject.calculateTax(postData);
        console.log('🚀 ~ sureOrCancel ~ calculateTaxData:', calculateTaxData);
        a.marketTaxPrice = calculateTaxData.marketTaxPrice;
        a.marketPrice = calculateTaxData.marketPrice;
        autoChangePrice(
          a,
          projectStore.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice'
        );
        if (res != a.marketPrice && projectStore.taxMade == 1) {
          a.sourcePrice = '自行询价';
        }
        if (res != a.marketTaxPrice && projectStore.taxMade == 0) {
          a.sourcePrice = '自行询价';
        }

        updataDataList.push({
          libraryCode: a.libraryCode,
          marketPrice: Number(a.marketPrice),
          marketTaxPrice: Number(a.marketTaxPrice),
          materialName: a.materialName,
          sequenceNbr: a.sequenceNbr,
          sourcePrice: a.sourcePrice,
        });

        if (projectStore.currentTreeInfo.type != 3) {
          // 更新数据来源
          updateDataSource(a.sequenceNbr, {
            ...a,
          });
        }

        return a;
      });
    console.log('tableData', tableData.value, updataDataList);
    projectStore.SET_HUMAN_UPDATA_DATA({
      isEdit: true,
      name: 'unify-humanMachineSummary',
      updataData: updataDataList,
    });
  }
  if (projectStore.currentTreeInfo.type === 3 && hasCheck) {
    changeMarketFactor();
  } else {
  }
  adjustFactor.value = false;
};
/**
 * 调整市场价系数
 * @function changeMarketFactor
 * @description 将人材机数据中的市场价系数调整为指定的系数
 * @param {object} apiData - 请求参数
 * @param {number} apiData.constructId - 工程ID
 * @param {number} apiData.coefficient - 系数
 * @param {array} apiData.rcjList - 人材机数据
 * @param {number} [apiData.singleId] - 单项ID
 * @param {number} [apiData.unitId] - 单位ID
 * @returns {Promise} - Promise对象
 */
const changeMarketFactor = () => {
  //调整市场价系数
  let coefficient = [1, 2].includes(projectStore.currentTreeInfo.type)
    ? projectStore.humanUpdataData.adjustFactor.marcketFactor * 1
    : toRaw(marcketFactor.value);
  // 市场价不可编辑过滤
  let rcjList = [1, 2].includes(projectStore.currentTreeInfo.type)
    ? JSON.parse(
        JSON.stringify(
          projectStore.humanUpdataData.adjustFactor.selectRows
        ).filter(
          row =>
            row.ifLockStandardPrice !== 1 &&
            isPartEdit &&
            row.isFyrcj == 1 &&
            !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
            Number(row.edit) !== 1 &&
            !deMapFun.isJxxs(row.materialCode) &&
            !deMapFun.isQtclf(row.materialCode)
        )
      )
    : JSON.parse(JSON.stringify(humanTable.value.getCheckboxRecords())).filter(
        row =>
          row.ifLockStandardPrice !== 1 &&
          isPartEdit &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          Number(row.edit) !== 1 &&
          !deMapFun.isTz(row.materialCode) &&
          !deMapFun.isJxxs(row.materialCode) &&
          !deMapFun.isQtclf(row.materialCode)
      );
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    coefficient,
    rcjList,
  };

  if (projectStore.currentTreeInfo.type === 2) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId;
  }

  if (projectStore.currentTreeInfo.type === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    apiData.apply = false;
  }
  let apiName =
    projectStore.currentTreeInfo.type === 3
      ? 'marketPriceAdjustUnit'
      : 'marketPriceAdjustConstructApply';
  console.log('changeMarketFactor调整市场价系数返回结果', apiData);
  csProject[apiName](apiData).then(res => {
    if (res.status === 200) {
      console.log('changeMarketFactor调整市场价系数返回结果', res);
      getHumanMachineData();
    }
  });
};
const mergeCloseDialog = () => {
  mergeStatus.value = false;
};

const saveHumanData = (oldVal, callback = null) => {
  unifyData.value = operateList.value.find(
    item => item.name === projectStore.humanUpdataData.name
  );
  let infoText = '人材机数据已修改，是否应用整个工程项目?';
  if (projectStore.currentTreeInfo.type == 2) {
    infoText = '人材机数据已修改，是否应用整个单项工程?';
  }
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      isUse(oldVal, callback);
      infoMode.hide();
    },
    close: () => {
      getHumanMachineData();
      setTimeout(() => {
        callback();
      }, 100);
      projectStore.humanUpdataData.isEdit = false;
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
};
// 点击汇总范围
const summaryScopeClick = () => {
  isSummaryScope.value = true;
  summaryScopeRef.value?.open();
};
const changeRowColor = data => {
  console.log(currentInfo.value, 'a1');
  let apiData = {
    levelType: projectStore.currentTreeInfo.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    code: currentInfo.value.materialCode,
    rcjList:
      JSON.parse(JSON.stringify(humanTable.value.getCheckboxRecords())).length >
      0
        ? JSON.parse(JSON.stringify(humanTable.value.getCheckboxRecords()))
        : [toRaw(currentInfo.value)],
  };

  if (projectStore.currentTreeInfo.type == 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }

  if (data.activeKind) {
    console.log(data, currentInfo.value);
    console.log('apiData', apiData);
    if (data.activeKind !== 'default') {
      apiData.color = data.options.find(a => a.kind == data.activeKind).color;
      csProject.saveRcjCellectColor(apiData).then(res => {
        getHumanMachineData();
      });
    } else {
      csProject.delRcjCellectColor(apiData).then(res => {
        console.log('delRcjCellectColor', res);
        getHumanMachineData();
      });
    }
    isFiltErate.value = false;
  }

  // //只是清除载价就传空值，清除载价+改市场价传修改数据
  // csProject.changeRcjConstructProject(apiData).then(res => {});
};

// 基期价、市场价为“-
const isChangeAva = row => {
  return Number(row.isDataTaxRate) == 0;
};

const loadPrice = (type, state, menudata) => {
  reportModel.value = false;
  switch (type) {
    case 'batch-loadprice':
      typeModal.value = '批量载价';
      break;
    case 'loadprice-report':
      typeModal.value = '载价报告';
      break;
    case 'rcj-no-margin':
      typeModal.value = '批量载价';
      break;
    case 'no-price-difference':
      typeModal.value = '人材机无价差';
      quotaHeaderData.value = currentInfo.value;
      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData('', () => {
          reportModel.value = true;
        });
      } else {
        reportModel.value = true;
      }
      break;
    case 'rcj-newAdd-classify':
      console.log('newAdd', state);
      typeModal.value = `${!state || state == '1' ? '新建' : '编辑'}汇总分类`;
      reportModel.value = true;
      menuData.value = menudata;
      classifyType.value = state || '1';
      break;
  }

  // 勾选暂估价后，价格不可以修改编辑， 也不能允许载价
  // if (
  //   type == 'batch-loadprice' &&
  //   currentInfo.value.ifProvisionalEstimate == 1
  // ) {
  //   message.warning('该行数据不支持载价，请重新选择');
  //   // infoMode.show({
  //   //   isSureModal: true,
  //   //   iconType: 'icon-qiangtixing',
  //   //   infoText: '暂估价已勾选，不可载价',
  //   //   confirm: () => {
  //   //     infoMode.hide();
  //   //   },
  //   // });
  //   return;
  // }

  reportModel.value = true;
};
const close = bol => {
  reportModel.value = false;
};
const nextEdit = data => {
  propsData.value = data;
  typeModal.value = data.nextType;
  reportModel.value = true;
  if (typeModal.value == '载价报告') {
    getHumanMachineData();
  }
  console.log('执行nextEdit', reportModel.value);
};
provide('nextStep', nextEdit);
const jsz = () => {
  // message.info('功能建设中...');
  marcketFactor.value = '1';
  // oldMarketFactor.value = '1';
  adjustFactor.value = true;
};

// 初始化汇总范围
const initRCJProjectTreeMenu = code => {
  isFiltErate.value = false;
  // 只有工程项目才需要初始化
  if (projectStore.currentTreeInfo.type !== 1) {
    //侧边栏数据变化重新更新
    getHumanMachineData(1);
    return false;
  }
  let sequenceNbrs = JSON.parse(JSON.stringify(summarySequenceNbr.value));
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    type: sequenceNbrs.length == 0 ? 0 : 1,
    sequenceNbrs,
  };
  csProject.updateRCJProjectTreeMenu(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    //侧边栏数据变化重新更新
    if (sequenceNbrs.length == 0) {
      getHumanMachineData(1);
    } else {
      querySummary(sequenceNbrs);
    }
  });
};
const deleteLei = code => {
  console.log('删除方法');
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '确认删除当前分类？',
    isFunction: false,
    confirm: () => {
      delRcjCellectMenuData(code);
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const delRcjCellectMenuData = code => {
  csProject
    .delRcjCellectMenuData({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo.id,
      code,
    })
    .then(res => {
      emit('updateMenuList');
      console.log('删除人材机', res);
    });
};
// 获取分类汇总信息
const getRadioList = () => {
  let apiData = {
    type: 1,
    levelType: projectStore.currentTreeInfo.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo.id,
  };
  csProject.getRcjCellectTypeData(apiData).then(res => {
    console.log('getRcjCellectTypeData', res);
    cellectType.value = {
      expenseType: res.expenseType.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
      matchingType: res.matchingType.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
      supplyType: res.supplyType.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      }),
    };
  });
};
const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return ![
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
  ].includes(currentInfo.value?.materialCode);
});

// watchEffect(() => {
//   console.log('当前的', currentInfo.value);
// });
const selfCheck = (value, min, max) => {
  let length = getDecimalPlaces('RCJ_COLLECT_MARKETPRICEADJUSTUNIT_PATH');
  // length-小数点长度   min-最小值  max-最大值
  let newValue = value * 1 + '';
  if (newValue === '') return oldMarketFactor.value;
  if (newValue <= min || newValue > max) {
    newValue = '1';
    message.info('市场价系数输入范围为(0,1000])');
  }
  let after = newValue.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newValue = parseFloat(newValue).toFixed(length);
  }
  oldMarketFactor.value = newValue;
  return newValue;
};
onMounted(() => {
  console.log('projectStoreaa', projectStore);
  if (
    projectStore.tabSelectName == '人材机汇总' &&
    projectStore.asideMenuCurrentInfo?.code == '0'
  ) {
    isFiltErate.value = false;
    tableColumns.forEach((item, index) => {
      // 1 一般计税  0 简易计税
      if (
        (projectStore.taxMade == '1' &&
          (item.title == '含税基期价' || item.title == '含税市场价合计')) ||
        (projectStore.taxMade == '0' &&
          (item.title == '不含税基期价' || item.title == '不含税市场价合计'))
      ) {
        tableColumns.splice(index, 1);
      }
    });
    initColumns({
      columns: tableColumns,
    });
    // tableData.value = [];
    getHumanMachineData(1);
  }
  getLoadStatus();
});
getRadioList();

const getTaxMethods = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  csProject.gljProjectTaxCalculationMethod(apiData).then(res => {
    if (res.status !== 200) {
      return message.error(res.message);
    }
    tableColumns.forEach((item, index) => {
      // 1 一般计税  0 简易计税
      if (
        (res.result == 1 &&
          (item.title == '含税基期价' || item.title == '含税市场价合计')) ||
        (res.result == 0 &&
          (item.title == '不含税基期价' || item.title == '不含税市场价合计'))
      ) {
        tableColumns.splice(index, 1);
      }
    });
    initColumns({
      columns: tableColumns,
    });
  });
};
const getLoadStatus = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    type: [1, 1, 3, 2][projectStore.currentTreeInfo.type],
  };
  if (projectStore.currentTreeInfo.type == 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }
  loadApi.loadPriceStatus(apiData).then(res => {
    console.log('++++++++++++++', res);
    // isSeeReport.disabled = false;
    if (res.result && res.result.includes(5)) {
      isLoad.disabled = true;
    } else if (res.result && res.result.includes(3)) {
      // isSeeReport.disabled = true;
    } else if (res.result && res.result.includes(4)) {
      feePro.isOnline().then(res => {
        console.log('判断是否有网------', res);
        if (res.result) {
          isLoad.disabled = false;
        } else {
          message.error('请连接网络后使用！');
        }
      });
    }
  });
};
const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.length &&
    tableData.value.map(item => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum == 1 && (item.levelMark == 1 || item.levelMark == 2)) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};
const getKind = type => {
  let value;
  switch (type) {
    case '其他费':
      value = 0;
      break;
    case '人工费':
      value = 1;
      break;
    case '材料费':
      value = 2;
      break;
    case '机械费':
      value = 3;
      break;
    case '设备费':
      value = 4;
      break;
    case '主材费':
      value = 5;
      break;
    case '商砼':
      value = 6;
      break;
    case '砼':
      value = 7;
      break;
    case '浆':
      value = 8;
      break;
    case '商浆':
      value = 9;
      break;
    case '配比':
      value = 10;
      break;
  }
  return value;
};
const getType = type => {
  let value = '';
  switch (type) {
    case 0:
      value = '其他费';
      break;
    case 1:
      value = '人工费';
      break;
    case 2:
      value = '材料费';
      break;
    case 3:
      value = '机械费';
      break;
    case 4:
      value = '设备费';
      break;
    case 5:
      value = '主材费';
      break;
    case 6:
      value = '商砼';
      break;
    case 7:
      value = '砼';
      break;
    case 8:
      value = '浆';
      break;
    case 9:
      value = '商浆';
      break;
    case 10:
      value = '配比';
      break;
  }
  return value;
};
const getOldData = () => {
  oldData.value = [];
  tableData.value &&
    tableData.value.map(item => {
      oldData.value.push({
        sequenceNbr: item.sequenceNbr,
        marketPrice: item.marketPrice,
        marketTaxPrice: item.marketTaxPrice,
        ifDonorMaterial: item.ifDonorMaterial,
        donorMaterialNumber: item.donorMaterialNumber,
        ifProvisionalEstimate: item.ifProvisionalEstimate,
        ifLockStandardPrice: item.ifLockStandardPrice,
      });
    });
  console.log('getOldData', oldData.value);
};
const updateHztype = () => {
  emit('updateMenuList', true);
  setTimeout(() => {
    getHumanMachineData(1);
  }, 5000);
};

const isZG = computed(() => {
  return projectStore.asideMenuCurrentInfo?.code == 8;
});

let mergeFormData = ref();

const getHumanMachineData = (type = '', summary = 0, row) => {
  if (projectStore.asideMenuCurrentInfo?.code == '99') {
    if (threeMaterialsRef.value && threeMaterialsRef.value?.getTableData) {
      threeMaterialsRef.value?.getTableData();
    }
    return;
  }
  loading.value = true;

  let kind =
    projectStore.asideMenuCurrentInfo?.code.length > 2
      ? projectStore.asideMenuCurrentInfo?.code
      : Number(projectStore.asideMenuCurrentInfo?.code);

  let formData = {
    kind,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType: projectStore.currentTreeInfo.type,
  };
  // 主要材料、设备表 禁用合并相似材料
  for (let i of operateList.value) {
    if (i.name == 'Merge-similar-materials') {
      i.disabled = projectStore.asideMenuCurrentInfo?.code == 7;
      break;
    }
  }
  if (projectStore.asideMenuCurrentInfo?.code == 7) {
    mergeStatus.value = false;
  }

  if (type === 1 || summary == 1) {
    formData['isShowAnnotations'] = false;
    formData['isShowAnnotationsPro'] = false;
    formData['isShowAnnotationsSingle'] = false;
  }
  if (projectStore.currentTreeInfo.type == 3) {
    // formData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    formData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    formData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  if (projectStore.currentTreeInfo.type == 2) {
    formData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
  }
  if (type == 1 || summary == 1) {
    tableData.value = [];
    sortFiled.value = '';
    sortVal.value = false;
    isSort.value = false;
  }
  if (sortFiled.value !== '' || type == 2) {
    formData.sort = {
      field: sortFiled.value,
      order: sortVal.value ? 'asc' : 'desc',
    };
    isSort.value = true;
  }
  console.log('人材机汇总列表查询参数formData', formData);
  mergeFormData.value = formData;
  isSortTable();

  let apiName = 'getRcjCellectData';
  csProject[apiName](formData)
    .then(res => {
      if (res.status == 200 && res.result && res.result.length > 0) {
        let num = 1;
        let zgNum = 1;
        res.result &&
          res.result.map((item, index) => {
            item.dispNo = num++;
            item.ifDonorMaterial = String(item.ifDonorMaterial);
            item.type = getType(Number(item.kind));
            item.donorMaterialNumber =
              item.ifDonorMaterial != 1
                ? ''
                : Number(item.donorMaterialNumber) === 0
                  ? ''
                  : item.donorMaterialNumber;
            item.origindonorMaterialNum = item.donorMaterialNumber
              ? item.donorMaterialNumber
              : '0';
            item.kindSc = item.kindSc === '空' ? '' : item.kindSc;
            item.unitIdSet = [...item.unitIdSet];
            // 后期导入数据是否特殊处理
            autoChangePrice(
              item,
              projectStore.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice'
            );
          });
        let datas = res.result;
        console.info('人材机汇总列表查询参数', datas);
        if (isFiltErate.value) {
          // for (let item of searchData) {
          //   for (let item2 of datas) {
          //     if (item.sequenceNbr === item2.sequenceNbr) {
          //       item = item2;
          //     }
          //   }
          // }
          tableData.value = tableData.value.map(a => {
            return datas.find(b => a.sequenceNbr == b.sequenceNbr);
          });
          console.log('searchData', tableData.value);
          // humanTable.value.loadData(searchData);
        } else {
          datas = disposeDeTypeData(res.result, true, true);

          tableData.value = datas;
        }
        // const fieldPathArray = [
        //   {
        //     property: 'transferFactor',
        //     pathKey: 'RCJ_COLLECT_TRANSFERFACTOR_PATH',
        //   }, // 三材系数
        // ];
        // tableData.value.forEach(row => {
        //   fieldPathArray.forEach(({ property, pathKey }) => {
        //     const value = row[property];
        //     row[property] = decimalFormat(value, pathKey);
        //   });
        // });
        console.log('tableData.value', tableData.value);

        checkBoxIsShow();
        if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
          setTimeout(() => {
            getOldData();
            getSameUnit();
          }, 200);
        }
        if (mergeStatus.value) {
          mergeMaterialsRef.value.open();
        }
        console.log('人材机汇总表格数据', datas);
        originalTableData = xeUtils.clone(datas, true);
        console.log('typeeee', row);

        let currentData = tableData.value[0];
        let gljCheckTab = projectStore.gljCheckTab;
        let upSelRow = gljCheckTab[
          projectStore.currentTreeInfo.sequenceNbr
        ].tabList.find(a => a.tabName == '人材机汇总');
        if (upSelRow && upSelRow.selRowId !== '') {
          let obj = tableData.value.find(
            a => a.sequenceNbr == upSelRow.selRowId
          );
          if (!obj) {
            obj = tableData.value[0];
          }
          currentData = obj;
          setTimeout(() => {
            humanTable.value.scrollToRow(obj);
          }, 100);
        } else if (row) {
          currentData =
            tableData.value.find(item => item.sequenceNbr == row[0]) ||
            currentData;
        }
        try {
          humanTable.value?.setSelectCell(currentData, currentSelectCell.value);
        } catch (err) {
          console.log(err);
        }
        // 更新数据
        // setMoveRowList(currentInfo.value, tableData.value, true);
        // filtErateRef.value.queryClick1()

        if (FiltErateCondition.value.length > 0) {
          // 如果有历史过滤条件
          nextTick(() => {
            filtErateRef.value.queryClick1(FiltErateCondition.value);
          });
        } else {
          nextTick(() => {
            humanTable.value.setCurrentRow(currentData);
            currentChange({ row: currentData });
          });
        }
      } else {
        tableData.value = [];
      }

      if (kind === 0) {
        // 记录所有人材机数据
        allRCJTableData.value = xeUtils.clone(tableData.value, true);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const isSortTable = () => {
  if (!isSort.value) {
    let kind =
      projectStore.asideMenuCurrentInfo?.code.length > 2
        ? projectStore.asideMenuCurrentInfo?.code
        : Number(projectStore.asideMenuCurrentInfo?.code);

    let postData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      levelType: projectStore.currentTreeInfo.type,
      kind,
    };

    if (projectStore.currentTreeInfo.type == 2) {
      postData.singleId = projectStore.currentTreeGroupInfo?.singleId;
      postData.unitId = null; //单项ID
    }

    if (projectStore.currentTreeInfo.type == 3) {
      postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
      postData.unitId = projectStore.currentTreeInfo?.id; //单项ID
    }

    csProject.isRcjCellectSort(postData).then(res => {
      isSort.value = res.result;
    });
  }
};

const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  tableData.value &&
    tableData.value.map(item => {
      let otherSameUnit = tableData.value.filter(
        unit =>
          (projectStore.taxMade == 1 &&
            unit.materialCode == item.materialCode &&
            unit.materialName == item.materialName &&
            // unit.unitId == item.unitId &&
            unit.unit == item.unit &&
            unit.specification == item.specification &&
            Number(unit.baseJournalPrice) == Number(item.baseJournalPrice) &&
            unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
            unit.sequenceNbr !== item.sequenceNbr &&
            Number(unit.marketPrice) !== Number(item.marketPrice)) ||
          (projectStore.taxMade == 0 &&
            unit.materialCode == item.materialCode &&
            unit.materialName == item.materialName &&
            // unit.unitId == item.unitId &&
            unit.unit == item.unit &&
            unit.specification == item.specification &&
            Number(unit.baseJournalTaxPrice) ==
              Number(item.baseJournalTaxPrice) &&
            unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
            unit.sequenceNbr !== item.sequenceNbr &&
            Number(unit.marketTaxPrice) !== Number(item.marketTaxPrice))
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  console.log(
    '🚀 ~ getSameUnit ~ addColorList:',
    projectStore.taxMade,
    addColorList
  );
  tableData.value &&
    tableData.value.map(
      item =>
        (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );

  // originalTableData和tableData.value做比对，更新原始数据
  let checkList = [];
  for (let i of originalTableData) {
    let j = originalTableData.find(item => item.sequenceNbr == i.sequenceNbr);
    if (j) {
      checkList.push(j);
    } else {
      checkList.push(i);
    }
  }

  originalTableData = checkList;
  console.log('addColorList', checkList, addColorList);
};

const cellTableStyle = ({ row, column }) => {
  let style = {};
  if (row.rcjColor) {
    if (column.field == 'donorMaterialNumber') {
      if (
        row.donorMaterialNumber >= 0 &&
        row.totalNumber >= 0 &&
        row.donorMaterialNumber > row.totalNumber
      ) {
        style['backgroundColor'] = row.rcjColor;
        style['color'] = '#D40C0C';
        // return {
        //   // fontWeight: 'bold',
        //   backgroundColor: row.rcjColor,
        //   // color: '#D40C0C',
        // };
      }
    } else {
      style['backgroundColor'] = row.rcjColor;
    }
  } else {
    if (column.field == 'donorMaterialNumber') {
      if (
        row.donorMaterialNumber >= 0 &&
        row.totalNumber >= 0 &&
        row.donorMaterialNumber > row.totalNumber
      ) {
        style['color'] = '#D40C0C';
      }
    }
  }
  if (column.field == 'marketPrice') {
    // 不含税projectStore.taxMade == 1
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      if (row.ifProvisionalEstimate != 1) {
        style['border'] = '1px solid #059421';
      }
    }

    if (projectStore.taxMade == 1) {
      if (
        row.marketPrice >= 0 &&
        row.baseJournalPrice >= 0 &&
        row.marketPrice > row.baseJournalPrice
      ) {
        style['color'] = '#D40C0C';
      } else if (
        row.marketPrice >= 0 &&
        row.baseJournalPrice >= 0 &&
        row.marketPrice < row.baseJournalPrice
      ) {
        style['color'] = '#059421';
      }
    }
  }

  if (column.field == 'marketTaxPrice') {
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      if (row.ifProvisionalEstimate != 1) {
        style['border'] = '1px solid #059421';
      }
    }

    if (projectStore.taxMade == 0) {
      if (
        row.marketTaxPrice >= 0 &&
        row.baseJournalTaxPrice >= 0 &&
        row.marketTaxPrice > row.baseJournalTaxPrice
      ) {
        style['color'] = '#D40C0C';
      } else if (
        row.marketTaxPrice >= 0 &&
        row.baseJournalTaxPrice >= 0 &&
        row.marketTaxPrice < row.baseJournalTaxPrice
      ) {
        style['color'] = '#059421';
      }
    }
  }

  return style;
};
const cellStyle = ({ row, column }) => {
  let style = {};

  if (row.rcjColor) {
    if (column.field == 'donorMaterialNumber') {
      if (
        row.donorMaterialNumber >= 0 &&
        row.totalNumber >= 0 &&
        row.donorMaterialNumber > row.totalNumber
      ) {
        style['backgroundColor'] = row.rcjColor;
        style['color'] = '#D40C0C';
      }
    } else {
      style['backgroundColor'] = row.rcjColor;
    }
  }
  if (column.field == 'donorMaterialNumber') {
    if (
      row.donorMaterialNumber >= 0 &&
      row.totalNumber >= 0 &&
      row.donorMaterialNumber > row.totalNumber
    ) {
      style['color'] = '#D40C0C';
    }
  }

  // 不含税市场价涂色
  if (column.field == 'marketPrice') {
    //市场价高于定额价标红，低于的话标绿

    if (projectStore.taxMade == 1) {
      if (
        row.marketPrice >= 0 &&
        row.baseJournalPrice >= 0 &&
        row.marketPrice > row.baseJournalPrice
      ) {
        style['color'] = '#D40C0C';
      } else if (
        row.marketPrice >= 0 &&
        row.baseJournalPrice >= 0 &&
        row.marketPrice < row.baseJournalPrice
      ) {
        style['color'] = '#059421';
      }
    }
  }

  // 含税市场价涂色
  if (column.field == 'marketTaxPrice' && row.ifProvisionalEstimate != 1) {
    //市场价高于定额价标红，低于的话标绿

    if (projectStore.taxMade == 0) {
      if (
        row.marketTaxPrice >= 0 &&
        row.baseJournalTaxPrice >= 0 &&
        row.marketTaxPrice > row.baseJournalTaxPrice
      ) {
        style['color'] = '#D40C0C';
      } else if (
        row.marketTaxPrice >= 0 &&
        row.baseJournalTaxPrice >= 0 &&
        row.marketTaxPrice < row.baseJournalTaxPrice
      ) {
        style['color'] = '#059421';
      }
    }
  }
  return style;
};
const rowStyle = ({ row }) => {
  if (row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) {
    if (row.field !== 'dispNo') {
      return {
        color: '#ACACAC',
      };
    }
  }
  if (row.highlight) {
    return {
      backgroundColor: '#FCF8EF',
    };
  }
};
const CheckboxChange = (row, type) => {
  switch (type) {
    case 'markSum':
      row.checkIsShow = row.markSum == 1 ? false : true;
      break;
    case 'ifDonorMaterial':
      break;
    case 'ifProvisionalEstimate':
      if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
        row.ifLockStandardPrice = row.ifProvisionalEstimate;
      }
      break;
    case 'ifLockStandardPrice':
      row.cusTomIfLockStandardPrice = row.ifLockStandardPrice;
      if (
        [1, 2].includes(projectStore.currentTreeInfo.type) &&
        row.ifLockStandardPrice == 0
      ) {
        //         勾选暂估，市场价锁定勾选
        // 取消暂估，市场价取消勾选
        // 勾选市场价，暂估不勾选
        // 取消市场价，暂估取消勾选
        row.ifProvisionalEstimate = row.ifLockStandardPrice;
      }
      break;
  }
  if (projectStore.currentTreeInfo.type == 3) {
    upDate(row, type);
  } else if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    //工程项目级别
    row.isChange = true; //标识编辑行
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      console.log('upDateList', upDateList);
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: 'unify-humanMachineSummary',
        updataData: upDateList,
      });
    }
    if (type == 'ifDonorMaterial') {
      row.donorMaterialNumber = row.ifDonorMaterial == 1 ? row.totalNumber : '';
      humanTable.value.reloadRow(row, {});
    }
  }
};

const getCurrentIndex = (item, type) => {
  console.log('item', item, tableData.value);
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr == item.sequenceNbr) {
        isCurrent.value = n;
      }
    });
  } else {
    isCurrent.value = tableData.value[0];
  }
  humanTable.value.setCurrentRow(isCurrent.value);
  humanTable.value.scrollToRow(isCurrent.value);

  console.log('==========', isCurrent);
};

function currentChange({ row }) {
  currentInfo.value = { ...toRaw(row) };
  prevTaxRate.value = row?.taxRate;
  if ([1, 2].includes(projectStore.currentTreeInfo.type)) {
    getCurrentIndex(row);
  }

  if (tableData.value.length > 0) {
    humanTable.value?.clearCheckboxRow(row, true);
    humanTable.value?.setCheckboxRow(row, true);
  }

  setMoveRowList(row, tableData.value);
}

/**
 *
 * @param {*} row
 * @param {*} list
 * @param {*} resetList  只更新列表
 */
const setMoveRowList = (row, list, resetList = false) => {
  if (resetList) {
    projectStore.moveRow.tableData = list;
    return;
  }
  projectStore.moveRow = {
    tableData: toRaw(list),
    isTree: false,
    useRowList: [
      {
        ...row,
      },
    ],
  };
};

// 定位方法
const posRow = sequenceNbr => {
  console.log('人材机汇总定位', sequenceNbr);
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  getHumanMachineData();
  setTimeout(() => {
    getCurrentIndex({ sequenceNbr });
  }, 800);
  // currentInfo.value = { sequenceNbr };
};

// 批注样式处理
const customCell = ({ rowIndex, column, row }) => {
  let className = '';
  // let style = {}
  // // 批注提示
  if (column.field === 'materialName' && row?.annotations) {
    className += ' note-tips';
  }
  // if (column.field === 'materialName') {
  //   className += ' addNote';
  // }
  // return { style: style,class: className};
  return className;
  //return projectStore.currentTreeInfo.type === 3 ? cellStyle({  column , row }) : cellTableStyle({ column , row });
};

const cellClassName = info => {
  const { column, columnIndex, $columnIndex, row, rowIndex, $rowIndex } = info;
  let className = selectedClassName(info);
  // 批注提示
  if (column.field == 'specification' && row[AnnotationFelid.value]) {
    className += ' note-tips';
  }
  // 税率和库里税率不同，标红
  if (column.field === 'taxRate') {
    if (+row.taxRate !== row.taxRateInit && row.taxRateInit !== undefined) {
      className += ' diff-tax-rate';
    }
  }
  const { columnIndex: cIndex, rowCurrentCellDragFill } =
    currentCellDragFill.value;
  if (
    displayedFields.value.includes(column.field) &&
    !className &&
    rowCurrentCellDragFill &&
    'sequenceNbr' in rowCurrentCellDragFill &&
    rowCurrentCellDragFill.sequenceNbr === row.sequenceNbr &&
    $columnIndex === cIndex
  ) {
    // 处理 非编辑状态，选中的样式效果
    // const { columnIndex, rowId } = currentCellData.value;
    // if((!column?.editRender || column.editRender?.notCanEdit) && infoRow['sequenceNbr'] === rowId && $columnIndex === columnIndex){
    //   className = 'cell-selected';
    // }else {
    //   className = '';
    // }
    className = '';
  }
  className += dragFillCellClassName(info) + customCell(info);

  return className;
};

// =====================过滤逻辑
let filterateVisible = ref(false);
// =====================查找逻辑

onActivated(() => {
  filter.checkedList = allFilters;
  if (FiltErateCondition.value?.length) {
    FiltErateCondition.value = [];
  }

  unifyData.disabled = true;
  projectStore.SET_HUMAN_UPDATA_DATA(null);

  setTimeout(() => {
    useFormatTableColumns({
      type: [1, 1, 3, 2][projectStore.currentTreeInfo.type],
    });
  }, 1000);
  let options = operateList.value.find(
    item => item.name === 'rcj-color-sign'
  )?.options;
  options.forEach(item => {
    item.isValid = true;
  });
  operateList.value.find(item => item.name === 'rcj-color-sign').disabled =
    false;
  bus.off('humanMenu');

  bus.on('saveSource', () => {
    // 数据来源修改了
    getHumanMachineData('', 0, [currentInfo.value?.sequenceNbr]);
  });

  bus.on('humanMenu', ({ code, menuKey, menudata }) => {
    console.log('humanMenu', menuKey);
    if (menuKey == '1') loadPrice('rcj-newAdd-classify', menuKey);
    if (menuKey == '2') loadPrice('rcj-newAdd-classify', menuKey, menudata);
    if (menuKey == '3') deleteLei(code);
  });
  summarySequenceNbr.value = [];
  insetBus(bus, projectStore.componentId, 'humanMachineSummary', async data => {
    // 执行载价\载价报告\人材机无差价\新建汇总分类
    if (data.name === 'set-main-materials') {
      openSetMainMaterial();
    }

    if (data.name === 'Merge-similar-materials') {
      mergeStatus.value = true;
    }
    console.log('修改颜色', data);
    if (
      data.name == 'batch-loadprice' ||
      data.name == 'loadprice-report' ||
      data.name == 'rcj-no-margin' ||
      data.name == 'rcj-newAdd-classify' ||
      data.name == 'no-price-difference'
    ) {
      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData();
      } else {
        loadPrice(data.name);
      }
    } else if (data.name == 'market-price') {
      console.log('调整市场价系数');

      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData('', () => {
          jsz();
        });
      } else {
        jsz();
      }
    } else if (data.name == 'unify-humanMachineSummary') {
      console.info('统一应用');
      isUse();
    } else if (data.name == 'rcj-exportNow-summaryTable') {
      (console.log('导出报表'), exportExcel('all'));
    } else if (data.name == 'rcj-color-sign') {
      console.log('修改颜色', data);
      changeRowColor(data);
    } else if (data.name == 'summary-scope') {
      console.log('汇总范围');
      // 表格是否有修改
      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData(2, () => {
          summaryScopeClick();
        });
      } else {
        summaryScopeClick();
      }
    } else if (data.name === 'quota-popup') {
      console.info('查询关联定额');
      quotaHeaderData.value = currentInfo.value;
      if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
        saveHumanData('', () => {
          quotaPopupVisible.value = true;
        });
      } else {
        quotaPopupVisible.value = true;
      }
    } else if (data.name === 'lookup') {
      console.info('查找');
      openLookup();
    } else if (data.name === 'filterate') {
      console.info('过滤');
      filterateVisible.value = true;
    } else if (data.name === 'Deposit-price') {
      console.info('存价');
      depositPriceClick();
    } else if (data.name === 'hardware-tools') {
      csProject.triggerHardwareManual();
    }
  });
  window.addEventListener('keydown', openLookup);
  window.addEventListener('keydown', handleGlobalKeyDown);
});
function handleGlobalKeyDown(event) {
  handleKeyDownCustom(event, {
    targetCellSelector:
      '.table-edit-common tbody tr:first-child td:nth-child(2)',
  });
}
onDeactivated(() => {
  console.log('onDeactivated');
  mergeStatus.value = false;
  lookupVisible.value = false;
  if (FiltErateCondition.value?.length) {
    FiltErateCondition.value = [];
  }
  componentRef.value?.clearHistoryCondition();

  window.removeEventListener('keydown', openLookup);
  window.removeEventListener('keydown', handleGlobalKeyDown);
});
let lookupVisible = ref(false);
// 点击存价
const depositPriceClick = () => {
  let fileName =
    projectStore.currentTreeGroupInfo.name === projectStore.currentTreeInfo.name
      ? `${projectStore.currentTreeGroupInfo.name}`
      : `${projectStore.currentTreeGroupInfo.name}${projectStore.currentTreeInfo.name}`;
  const $table = humanTable.value;
  $table.exportData({
    filename:
      `${fileName}${projectStore.asideMenuCurrentInfo.name}价格表` +
      new Date().getTime(),
    sheetName: 'Sheet1',
    type: 'xlsx',
    useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
    isFooter: true, //是否导出表尾（比如合计）
    data: JSON.parse(JSON.stringify(tableData.value)),
    columnFilterMethod({ column, $columnIndex }) {
      console.log('人材机存价导出数据', column);
      return (
        !($columnIndex === 0) &&
        [
          '编码',
          '名称',
          '规格型号',
          '单位',
          '含税市场价',
          '不含税市场价',
        ].includes(column.title)
      );
    },
  });
};
const openLookup = event => {
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};

let originalTableData = []; // 查找之前的源数据
const lookupConfig = reactive({
  columns: [
    {
      field: 'materialName',
      label: '名称',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'specification',
      label: '规格',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'materialCode',
      label: '编码',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'marketTaxPrice',
      label: '含税市场价',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'marketPrice',
      label: '不含税市场价',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'totalNumber',
      label: '数量',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
  ],
  conditionType: '||',
  tableData: tableData,
});

//  look 查找
// FiltErate 过滤
const lookupCallback = (rows, type = 'FiltErate') => {
  if (!rows || !rows.length) {
    tableData.value =
      type == 'look' ? xeUtils.clone(originalTableData, true) : [];
  } else {
    let num = 1;
    tableData.value = rows.map(i => {
      i.dispNo = num++;
      return i;
    });
  }

  if (tableData.value.length !== originalTableData.length) {
    isFiltErate.value = true;
  } else {
    isFiltErate.value = false;
  }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    humanTable.value.setCurrentRow(info);
    currentChange({ row: info });
  });
};

/***
 * @description: 过滤历史条件
 * @param {*}
 * @return {*}
 */
let FiltErateCondition = ref([]); //过滤的历史条件

watchEffect(() => {
  // FiltErateCondition
  filterateBtn['activation'] = FiltErateCondition.value.length;
});

const saveHistoryCondition = ({ history, set, rows }) => {
  FiltErateCondition.value = history;
  if (set) {
    if (!rows || !rows.length) {
      // tableData.value = xeUtils.clone(originalTableData, true);
      tableData.value = [];
    } else {
      let num = 1;
      tableData.value = rows.map(i => {
        i.dispNo = num++;
        return i;
      });
    }
    if (tableData.value.length !== originalTableData.length) {
      isFiltErate.value = true;
    } else {
      isFiltErate.value = false;
    }

    nextTick(() => {
      const s = tableData.value.find(i => {
        return i.sequenceNbr == currentInfo.value?.sequenceNbr;
      });
      let currentData = s || tableData.value[0];
      humanTable.value.setCurrentRow({ ...currentData });
      currentChange({ row: { ...currentData } });
    });
  }
};

const changeCurrentInfo = row => {
  if (row) {
    humanTable.value.setCurrentRow(row);
    humanTable.value.scrollToRow(row);
    currentInfo.value = row;
  }
};
const sortFiled = ref('');
const sortVal = ref(false);
let isSort = ref(false);
// 点击排序
const sortClick = filedId => {
  if (sortFiled.value === filedId) {
    sortVal.value = !sortVal.value;
  } else {
    sortVal.value = true;
  }
  sortFiled.value = filedId;

  if (
    projectStore.humanUpdataData &&
    projectStore.humanUpdataData.isEdit &&
    [1, 2].includes(projectStore.currentTreeInfo?.type)
  ) {
    saveHumanData('', () => {
      getHumanMachineData(2);
    });
  } else {
    getHumanMachineData(2);
  }
};
const setProUpdate = (updataData = []) => {
  projectStore.SET_HUMAN_UPDATA_DATA({
    isEdit: true,
    name: 'unify-humanMachineSummary',
    updataData: updataData,
    adjustFactor: projectStore.humanUpdataData?.adjustFactor,
  });
};
//工程项目更新载价市场价
const upDateMarketPrice = row => {
  let target = tableData.value.find(
    a => a.sequenceNbr === currentInfo.value.sequenceNbr
  );
  console.info(222222, projectStore.deType);
  if (projectStore.deType === '12') {
    // target.marketPrice = row.marketPrice;
    target.marketPrice = row.marketPriceAfter;
    let num = target.totalNumber * row.marketPriceAfter;
    target.total = Math.round(num * 100) / 100;
  } else {
    computedPirce(target);
  }
  target.sourcePrice = row.sourcePrice;
  target.isExecuteLoadPrice = true;
  target.isChange = true; //标识编辑行
  getSameUnit();
  let upDateList = getPropData();
  if (upDateList && upDateList.length > 0) {
    setProUpdate(upDateList);
  }
};

/**
 *
 * @param row 计算价格
 */
const computedPirce = row => {
  const priceFiled =
    projectStore.taxMade == 1 ? 'marketPrice' : 'marketTaxPrice';
  //含税市场价
  //不含税市场价
  row[priceFiled] = NumberUtil.numberScale2(
    NumberUtil.multiply(
      100,
      NumberUtil.divide(row[priceFiled], NumberUtil.add(100, row.taxRate))
    )
  );
};

// 单位的下拉选择
let unitList = ref();
const queryUnit = () => {
  gSdetailApi.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};
queryUnit();

// 替换人材机
const indexVisible = ref(false);
let indexLoading = ref(false); // 索引页面loading
const currentInfoReplace = row => {
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    newRcj: JSON.parse(JSON.stringify(row)),
    rcj: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  console.log('人材机替换功能', apiData);
  csProject
    .updateRcjByCollect(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        message.success('替换成功');
        console.log('替换成功', res.result, row);
        getHumanMachineData('', 0, [currentInfo.value.sequenceNbr]);
        // getHumanMachineData('', 0, currentInfo.value);
      } else {
        message.error(
          '插入子目必须与其他子目类型相同且不能具有层级结构，请重新插入！'
        );
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

// 取消排序
const removeRcjCellectSort = () => {
  // {constructId, singleId, unitId, levelType, kind}

  let kind =
    projectStore.asideMenuCurrentInfo?.code.length > 2
      ? projectStore.asideMenuCurrentInfo?.code
      : Number(projectStore.asideMenuCurrentInfo?.code);

  let postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    levelType: projectStore.currentTreeInfo.type,
    kind,
  };

  if (projectStore.currentTreeInfo.type == 2) {
    postData.singleId = projectStore.currentTreeGroupInfo?.singleId;
    postData.unitId = null; //单项ID
  }

  if (projectStore.currentTreeInfo.type == 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }
  console.log('取消排序', postData);
  csProject.removeRcjCellectSort(postData).then(res => {
    if (res.status == 200) {
      message.success('取消排序成功');
      sortFiled.value = '';
      getHumanMachineData(1, 0, [currentInfo.value.sequenceNbr]);
    }
  });
};

const components = markRaw(new Map());
components.set(
  'lyfx',
  defineAsyncComponent(() => import('./dataSource.vue'))
);
components.set(
  'xxfw',
  defineAsyncComponent(() => import('./MachineService.vue'))
);
//下表格-两部分
let selectdTab = ref('lyfx');
const activeOptions = reactive([
  {
    key: 'lyfx',
    tab: '来源分析',
  },
  {
    key: 'xxfw',
    tab: '信息价服务',
  },
]);

let machineServiceRef = ref(null);
watch(
  () => [projectStore.asideMenuCurrentInfo, projectStore.levelType],
  (newV, oldV) => {
    if (projectStore.tabSelectName == '人材机汇总') {
      let options = operateList.value.find(
        item => item.name === 'rcj-color-sign'
      )?.options;
      options.forEach(item => {
        item.isValid = true;
      });
      operateList.value.find(item => item.name === 'rcj-color-sign').disabled =
        false;
      let columnArr = JSON.parse(JSON.stringify(tableColumns));
      // 切换左侧树时，设置最少6列失败
      // columnArr.splice(0, 1);
      initColumns({
        columns: columnArr,
        pageName: 'fbfx',
        oldType: [1, 1, 3, 2][projectStore.currentTreeInfo.type],
      });
      console.log('人材机汇总表格列', tableColumns);
      // 初始化汇总范围
      initRCJProjectTreeMenu();
      // 概算未做
      getLoadStatus();

      // nextTick(() => {
      //   machineServiceRef.value?.open();
      // });
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => projectStore.humanUpdataData,
  () => {
    if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
      unifyData.disabled = false;
    }
  }
);

// 监听如果左侧树层级修改，则清空过滤条件
let filterateInit = ref(true);
watch(
  () => projectStore.currentTreeInfo.type,
  () => {
    if (projectStore.tabSelectName == '人材机汇总') {
      FiltErateCondition.value = [];
      filterateInit.value = false;
      nextTick(() => {
        filterateInit.value = true;
      });
    }
  },
  { immediate: true, deep: true }
);

// 可以编辑的定额吓得人材机普通专业的装饰超高、垂直运输定额下人材机可自由编辑
const isEditDe = computed(() => {
  // return deMapFun.isEditRcj(props.currentInfo?isCostDe);
  return currentInfo.value.isFyrcj !== 0;
});

let componentRef = ref(null);

// updata:{
//   'marketPrice':2,
//   'marketTaxPrice'：2
// }
const updateDataSource = (rcjSequenceNbr, fileds) => {
  componentRef.value?.updateData(rcjSequenceNbr, fileds);
};

const {
  isDragFill,
  cellMouseenterEvent,
  handleMouseDown,
  dragFillCellClassName,
  resetDragFillInfo,
  currentCellDragFill,
  displayedFields,
  handLabelDisplay,
} = useDragFillCell({ currentCellData, processingData, humanTable });

defineExpose({
  posRow,
  threeMaterialsRef,
  getTableData: getHumanMachineData,
  handlerColumns,
  updateColumns,
  getDefaultColumns,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}

.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
}

#humanTable :deep(.vxe-table) {
  .row--current {
    .vxe-body--column {
      background-color: var(
        --vxe-table-row-current-background-color
      ) !important;
    }
    .col--actived {
      background-color: var(
        --vxe-table-row-current-background-color
      ) !important;
    }
    .vxe-input--inner {
      background-color: var(
        --vxe-table-row-current-background-color
      ) !important;
    }
  }
}
::v-deep(.vxe-table--render-default .vxe-body--column.cell-selected) {
  box-shadow: inset 0 0 0 2px var(--vxe-primary-color);
}

.custom-header {
  .icon-close {
    position: absolute;
    right: 2px;
    top: 50%;
    background: #ffffff;
    z-index: 20;
    padding: 3px;
    opacity: 0;
  }
}

.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }

  .row--current {
    background-color: var(--vxe-table-row-current-background-color) !important;
  }
  .vxe-header--column {
    position: relative;
    &:hover .custom-header .icon-close {
      opacity: 1;
    }
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
.sortImg {
  position: relative;
  right: 3px;
  width: 22px;
}
.adjustFactorMoadl {
  .title {
    font-size: 14px;
  }
  div {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    // padding: 20px 0px 20px 5px;
    // border: 1px solid #6666;
    .ant-input {
      width: 78%;
    }
    span {
      width: 21%;
      margin: auto;
    }
  }
  .footor {
    display: flex;
    justify-content: space-between;
    width: 150px;
    margin: 10px auto 0;
  }
}

.selectTab {
  background-color: #e7e7e7;
  height: 32px;
  line-height: 30px;
  // padding-left: 20px;
  position: relative;
  border-bottom: 2px solid #e7e7e7;
  margin: 3px 0;
  .label {
    color: grey;
    font-size: 12px;
  }
  .showTitle {
    position: absolute;
    right: 0px;
    top: 0px;
    line-height: 30px;
    height: 30px;
    padding: 0 20px;
    font-size: 12px;
    // background-color: #e7e7e7;
    border-radius: 5px;
  }
  .ant-radio-button-wrapper {
    font-size: 12px;
    background-color: #e7e7e7;
    border: none;
    box-shadow: none;
    // border-radius: 5px;
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
  .ant-radio-button-wrapper-checked {
    // border-color: none;
    background-color: white;
    border: none;
    border-top: 3px solid #4786ff;
    color: black;
    &:hover {
      color: black;
    }
  }
}
::v-deep(.vxe-table .diff-tax-rate) {
  color: #de3f3f;
}
::v-deep(.guolv-human-checkgroup) {
  display: flex;
  flex-direction: column;
  padding: 6px 10px;
  .ant-checkbox-group-item {
    margin-right: 0px;
    margin-bottom: 2px;
  }
}

.guolv-humanM {
  margin-right: 12px;
  position: relative;
}
.fill-handle {
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 5px;
  height: 5px;
  background: #419fff;
  cursor: crosshair;
  opacity: 0.8;
  z-index: 11;
}

.fill-handle:hover {
  opacity: 1;
}
</style>
