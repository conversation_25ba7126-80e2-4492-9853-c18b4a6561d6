import { ref } from 'vue';
export const fbTableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 100,
    align: 'center',
    classType: 1,
    sort: 1,
    visible: true,
    children: [],
    initialize: true,
  },
  {
    title: '项目名称',
    field: 'projectName',
    treeNode: true,
    slot: true,
    classType: 1,
    sort: 2,
    visible: true,
    width: 180,
    children: [],
    initialize: true,
  },
  {
    title: '合同金额(不含设备费及其税金)',
    field: 'gczj_ys',
    treeNode: true,
    slot: true,
    classType: 1,
    sort: 3,
    visible: true,
    width: 200,
    children: [],
    initialize: true,
  },
  {
    title: '结算金额（不含人材机调整）',
    field: 'jsje',
    treeNode: true,
    slot: true,
    classType: 1,
    sort: 4,
    visible: true,
    children: [
      {
        title: '结算合计（不含设备费及其税金）',
        field: 'gczjsbsj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 1,
        visible: true,
        width: 200,
        children: [],
        initialize: true,
      },
      {
        title: '分部分项',
        field: 'fbfxhj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 2,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '措施项目',
        field: 'csxhj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 3,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '其他项目',
        field: 'qtxmhj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 4,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '规费',
        field: 'gfee',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 5,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '安全、生产文明施工费',
        field: 'safeFee',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 6,
        visible: true,
        width: 180,
        children: [],
        initialize: true,
      },
      {
        title: '进项税额',
        field: 'jxse',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 7,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '销项税额',
        field: 'xxse',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 8,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '增值税应纳税额',
        field: 'zzsynse',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 9,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '附加税费',
        field: 'fjse',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 10,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '税金',
        field: 'sj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 11,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '设备费及其税金',
        field: 'sbfsj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 12,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
    ],
    initialize: true,
  },
  {
    title: '人材机调整',
    field: 'rcjTz',
    treeNode: true,
    slot: true,
    classType: 1,
    sort: 5,
    visible: true,
    children: [
      {
        title: '合计（不含设备费及其税金）',
        field: 'jsjc',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 1,
        visible: true,
        width: 180,
        children: [],
        initialize: true,
      },
      {
        title: '人工差价',
        field: 'jsjcrgf',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 2,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '材料差价',
        field: 'jsjcclf',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 3,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '暂估材料差',
        field: 'jsjczgj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 4,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '机械价差',
        field: 'jsjcjxf',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 5,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '价差规费',
        field: 'jcgfhj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 6,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '价差安全、生产文明施工费',
        field: 'jcaqwmsgfhj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 7,
        visible: true,
        width: 180,
        children: [],
        initialize: true,
      },
      {
        title: '价差进项税额',
        field: 'jcjxse',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 8,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '价差销项税额',
        field: 'jcg',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 9,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '价差增值税应纳税额',
        field: 'jch',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 10,
        visible: true,
        width: 140,
        children: [],
        initialize: true,
      },
      {
        title: '价差附加税费',
        field: 'jci',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 11,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '价差税金',
        field: 'jcj',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 12,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
      {
        title: '设备费及其税金',
        field: 'jsjcsbf',
        treeNode: true,
        slot: true,
        classType: 1,
        sort: 13,
        visible: true,
        width: 120,
        children: [],
        initialize: true,
      },
    ],
    initialize: true,
  },
  {
    title: '结算金额(含人材机调整、不含设备费及其税金）',
    field: 'jieSuanPrice',
    treeNode: true,
    slot: true,
    classType: 1,
    sort: 6,
    visible: true,
    width: 280,
    children: [],
    initialize: true,
  },
]);
