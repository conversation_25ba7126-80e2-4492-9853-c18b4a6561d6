const { Service } = require('../../../core');
const { ObjectUtils } = require('../utils/ObjectUtils');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const { ConstructProjectRcj } = require('../../../electron/model/ConstructProjectRcj');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const WildcardMap = require('../core/container/WildcardMap');
const ProjectDomain = require('../domains/ProjectDomain');
const ResourceModel = require('../domains/deProcessor/models/ResourceModel');
const PropertyUtil = require('../domains/utils/PropertyUtil');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const ResourceConstants = require('../constants/ResourceConstants');
const { ConvertUtil } = require('../../../electron/utils/ConvertUtils');
const BranchProjectLevelConstant = require('../constants/BranchProjectLevelConstant');
const DeTypeConstants = require('../constants/DeTypeConstants');
const EE = require('../../../core/ee');
const CommonConstants = require('../constants/CommonConstants');
const { SqlUtils } = require('../../../electron/utils/SqlUtils');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const RcjTypeEnum = require('../../../electron/enum/RcjTypeEnum');
const { DeTypeCheckUtil } = require('../domains/utils/DeTypeCheckUtil');
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const DeCommonConstants = require("../constants/DeCommonConstants");
const {ResponseData} = require("../utils/ResponseData");
const {RCJKind} = require("../enums/ConversionSourceEnum");
const { GsConstructProjectRcj } = require('../models/GsConstructProjectRcj');

class GsRcjService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   *  增加人材机明细
   * @param deId
   * @param baseRcjModel
   * @param constructId
   * @param singleId
   * @param unitId
   * @return {Promise<string>} 人材机id
   */
  async addRcjData(deId, baseRcjModel, constructId, singleId, unitId, deRowId, rcjId, param={}) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    if (ObjectUtils.isEmpty(deId) || ObjectUtils.isEmpty(baseRcjModel)) {
      // 定额id, baseRcj为空
      return false;
    }
    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    let rcjDetailKind;
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      if (ObjectUtils.isNotEmpty(rcjList)) {
        if (ObjectUtils.isNotEmpty(rcjList[0].pbs)) {
          rcjDetailKind = rcjList[0]?.pbs[0]?.kind;
        } else {
          rcjDetailKind = rcjList[0].kind;
        }
      }
      if (rcjDetailKind === 2 && baseRcjModel.kind === 5) {

      } else if (baseRcjModel.levelMark != RcjCommonConstants.LEVELMARK || rcjDetailKind != baseRcjModel.kind) {
        return false;
      }
    }
    let alikeRcj = constructProjectRcjs.find(item => item.materialCode === baseRcjModel.materialCode);
    let sequence = param.sequenceNbr?param.sequenceNbr:Snowflake.nextId();
    let resource = new ResourceModel(deModel.constructId, deModel.unitId, sequence, deRowId, baseRcjModel.kind);
    PropertyUtil.copyProperties(baseRcjModel, resource, ['sequenceNbr']);
    resource.rcjId = baseRcjModel.sequenceNbr;
    resource.kind = baseRcjModel.kind;
    resource.kindSc = baseRcjModel.kindSc;
    resource.transferFactor = baseRcjModel.transferFactor;
    resource.parentId = deModel.sequenceNbr;
    resource.deId = deModel.sequenceNbr;
    resource.deRowId = deModel.sequenceNbr;
    resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
    resource.specification = baseRcjModel.specification;
    resource.producer = null;
    resource.manufactor = null;
    resource.brand = null;
    resource.deliveryLocation = null;
    resource.qualityGrade = null;
    resource.remark = null;
    resource.markSum = ObjectUtils.isEmpty(alikeRcj) ? RcjCommonConstants.MARKSUM_JX : alikeRcj.markSum;
    resource.total = RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
    resource.libraryCode = baseRcjModel.libraryCode;
    resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
    resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
    resource.numLockNum = 0;
    resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
    resource.isTempRemove = deModel.isTempRemove;

    resource.dePrice=baseRcjModel.price;
    //主材设备类型判断
    if(resource.kind == 4
      || resource.kind == 5){
      deModel.isExistedZcSb = CommonConstants.COMMON_YES;
    }
        let unitRcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        unitRcjList.find(item=>{
            item.materialName ===resource.materialName  && item.specification===resource.specification && item.unit===resource.unit && item.dePrice===resource.dePrice && item.kind===resource.kind
        });
        resource.marketPrice=baseRcjModel.price;
        if (ObjectUtil.isNotEmpty(baseRcjModel)) {
            resource.levelMark = baseRcjModel.levelMark;
            baseRcjModel.levelMark =  resource.levelMark ;
        }
        for (let key in RcjTypeEnum) {
            if (RcjTypeEnum[key].code == resource.kind) {
                resource.type=   RcjTypeEnum[key].desc;
            }
        }
        resource.resQty=RcjCommonConstants.DEFAULT_RESQTY;
        resource.initResQty = RcjCommonConstants.DEFAULT_RESQTY;
        resource.originalQty = RcjCommonConstants.DEFAULT_RESQTY;
        if (resource.isTempRemove === CommonConstants.COMMON_YES) {
          resource.changeResQty = resource.resQty
        }
        let   parentRcj;
        if(ObjectUtil.isNotEmpty(rcjId)){
            parentRcj = rcjList.find(item=>item.sequenceNbr ===rcjId)
        }else {
        if (!ObjectUtil.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) {
            await  ProjectDomain.getDomain(constructId).getDeDomain().attachPBs(resource, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr);
            let subSourcePrice ='';
            resource.pbs.forEach(item => {
                    item.marketPrice = item.dePrice;
                    item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
                    item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
                    item.markSum = RcjCommonConstants.MARKSUM_JX;
                    item.isTempRemove = resource.isTempRemove;
                    item.total = RcjCommonConstants.TOTALNUMBER_DEFAULT;
                    item.unitId = resource.unitId;
                    item.constructId = resource.constructId;
                for (let key in RcjTypeEnum) {
                    if (RcjTypeEnum[key].code == item.kind) {
                        item.type=   RcjTypeEnum[key].desc;
                    }
                }
                this.processingMarketPrice(item);
                if(ObjectUtils.isNotEmpty(item.sourcePrice)){
                  subSourcePrice =  item.sourcePrice;
                }
                });
            resource.sourcePrice = subSourcePrice
            }
        }
        this.processingMarketPrice(resource);
        this.processingDonorMaterial(resource);

    if (ObjectUtil.isNotEmpty(parentRcj)) {
      // 浆-商浆-砼-商砼-配比下子项只能新插入材料
      if (parentRcj.kind === 6 || parentRcj.kind === 7 || parentRcj.kind === 8 || parentRcj.kind === 9) {
        if (resource.kind !== 2) {
          return ResponseData.fail("明细材料的费用类别必须与主材料相同，请重新输入！");
        }
      }
      if (!ObjectUtil.isEmpty(resource.levelMark) && (resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX)) {
        return ResponseData.fail("明细材料不能包含明细，请重新输入！");
      }
      // 非机械类型的二次解析材料下子项中，仅能新增材料费类型的材料
      if (parentRcj.levelMark === ResourceConstants.LEVEL_MARK_PB_CL && resource.kind !== RCJKind.材料) {
        return ResponseData.fail("非机械类型的二次解析材料下子项中，仅能新增材料费类型的材料，请重新输入！");
      }
      if (ObjectUtils.isNotEmpty(parentRcj.pbs)) {
        rcjDetailKind = parentRcj?.pbs[0]?.kind;
      } else {
        rcjDetailKind = parentRcj.kind;
      }
      if (rcjDetailKind === 2 && resource.kind === 5) {

      } else if ((ObjectUtils.isEmpty(rcjDetailKind) ? parentRcj.kind : rcjDetailKind) != resource.kind || resource.levelMark != RcjCommonConstants.LEVELMARK_ZERO) {
        return false;
      }
      delete resource.levelMark;
      resource.parentId = parentRcj.sequenceNbr;
      resource.isTempRemove = parentRcj.isTempRemove;
      // 二次解析材料中插入标准编码段为QTCLF1（其他材料费）时，其单位将调整为“元”
      if (resource.materialCode.includes('QTCLF1')) {
        resource.unit = '元'
      }
      //处理内存 新增
      let  unitAllMemory=this.getRcjMemory(constructId,unitId);
      if(ObjectUtils.isNotEmpty(unitAllMemory)){
        let  memoryRcj = unitAllMemory.find(item=>item.materialCode ===parentRcj.materialCode);
        if(ObjectUtils.isNotEmpty(memoryRcj)){
          let useRcjArray = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
          let  userBeforeRcj= this.service.PreliminaryEstimate.gsRcjCollectService.findAlikeRcjArray(useRcjArray, parentRcj);
          if(userBeforeRcj.length===1){
            memoryRcj.pbs.push(resource);
          }
        }
      }

      parentRcj.pbs.push(resource);
      await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
      await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
    } else {
      await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);

      //插入父级人材机，初始化定额人材机名称集合
      let rcjNameObj = {};
      rcjNameObj.sequenceNbr = resource.sequenceNbr;
      rcjNameObj.initMaterialName = resource.materialName;
      let initDeRcjNameList = ObjectUtils.isNotEmpty(deModel.initDeRcjNameList) ? deModel.initDeRcjNameList : [];
      initDeRcjNameList.push(rcjNameObj);
      deModel.initDeRcjNameList = initDeRcjNameList;
    }
    //调用人材机的notify
    await ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId, unitId, deRowId }, false);
    //此处需要处理定额标识  
    if(ObjectUtil.isNotEmpty(deModel)){
      DeTypeCheckUtil.checkAndUpdateDeType(deModel,ProjectDomain.getDomain(constructId).ctx);
    }
    try {
    // 处理换算信息
    if (param.isConversionDeal !== true) {
      await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'add', null, null);
    }
      await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        constructMajorType: deModel.libraryCode
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    return deRowId;
  }


  /**
   *  替换人材机
   * @param deId
   * @param baseRcjModel
   * @param constructId
   * @param singleId
   * @param unitId
   * @return {Promise<string>} 人材机id
   */
  async replaceRcjData(deId, baseRcjModel, constructId, singleId, unitId, deRowId, rcjDetailId, rcjId,factor, param= {}) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    if (rcjList.length === 1 && rcjList[0].isDeResource === 1 && ObjectUtils.isNotEmpty(rcjList[0].pbs)  && singleId !== RcjCommonConstants.RCJ_MERGE_REPLACE) {
      rcjId = rcjList[0].sequenceNbr;
    }
    let original = null;
    if (ObjectUtils.isNotEmpty(rcjId)) {
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
      parentRcj.pbs.forEach(item => {
        if (item.sequenceNbr === rcjDetailId) {
          original = item;
        }
      });
    } else {
      original = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    }

    if (ObjectUtils.isEmpty(deId) || ObjectUtils.isEmpty(baseRcjModel)) {
      // 定额id, baseRcj为空
      return false;
    }
    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      let rcjDetailKind;
      if (ObjectUtils.isNotEmpty(rcjList)) {
        rcjDetailKind = rcjList[0].kind;
      }
      if ((rcjDetailKind === 7 || rcjDetailKind === 8)) {
        if (baseRcjModel.kind !== 2) {
          return false;
        }
      } else if (rcjDetailKind === 10 && baseRcjModel.kind === 5) {

      } else if (rcjDetailKind === 10 && baseRcjModel.kind === 2) {

      //} else if (baseRcjModel.levelMark != RcjCommonConstants.LEVELMARK || rcjDetailKind != baseRcjModel.kind) {
      } else if ( rcjDetailKind != baseRcjModel.kind) {
        return false;
      }
    }
    //let nextId = Snowflake.nextId();
    let resource = new ResourceModel(deModel.constructId, deModel.unitId, rcjDetailId, deRowId, baseRcjModel.kind);
    PropertyUtil.copyProperties(baseRcjModel, resource, ['sequenceNbr']);
    let alikeRcj = constructProjectRcjs.find(item => item.materialCode === baseRcjModel.materialCode);
    resource.rcjId = baseRcjModel.sequenceNbr;
    resource.kind = baseRcjModel.kind;
    resource.parentId = deModel.sequenceNbr;
    resource.deRowId = deModel.sequenceNbr;
    resource.deId = deModel.sequenceNbr;
    resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
    resource.specification = null;
    resource.producer = null;
    resource.manufactor = null;
    resource.brand = null;
    resource.deliveryLocation = null;
    resource.qualityGrade = null;
    resource.remark = null;
    resource.markSum = ObjectUtils.isEmpty(alikeRcj) ? RcjCommonConstants.MARKSUM_JX : alikeRcj.markSum;
    resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
    resource.libraryCode = baseRcjModel.libraryCode;
    resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
    resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
    resource.numLockNum = 0;
    resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
    resource.dePrice = baseRcjModel.price;
    resource.marketPrice = baseRcjModel.price;
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    if (ObjectUtil.isNotEmpty(baseRcjModel)) {
      resource.levelMark = baseRcjModel.levelMark;
      baseRcjModel.levelMark = resource.levelMark;
    }
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == resource.kind) {
        resource.type = RcjTypeEnum[key].desc;
      }
    }
    resource.resQty = original.resQty;
    resource.consumerResQty = original.consumerResQty;
    resource.changeResQty = original.changeResQty;
    resource.initResQty = RcjCommonConstants.DEFAULT_RESQTY;
    resource.originalQty = original.originalQty;
    resource.isTempRemove = original.isTempRemove;
    if(ObjectUtils.isNotEmpty(factor)){
      resource.resQty = NumberUtil.multiply(original.resQty, factor);
    }
    if (resource.isTempRemove === CommonConstants.COMMON_YES) {
      let resQty = original.consumerResQty? original.consumerResQty: original.changeResQty
      resource.changeResQty = NumberUtil.multiply(resQty, factor);
    }
    if (singleId === RcjCommonConstants.RCJ_MEMORY_REPLACE) {
      resource.rcjId =baseRcjModel.rcjId;
      resource.kind = baseRcjModel.kind;
      resource.ifDonorMaterial = baseRcjModel.ifDonorMaterial;
      resource.specification = baseRcjModel.specification;
      resource.producer = baseRcjModel.producer;
      resource.manufactor = baseRcjModel.manufactor;
      resource.brand = baseRcjModel.brand;
      resource.deliveryLocation = baseRcjModel.deliveryLocation;
      resource.qualityGrade = baseRcjModel.qualityGrade;
      resource.remark = baseRcjModel.remark;
      resource.markSum = baseRcjModel.markSum;
      resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
      resource.ifLockStandardPrice = baseRcjModel.ifLockStandardPrice;
      resource.libraryCode = baseRcjModel.libraryCode;
      resource.sourcePrice = baseRcjModel.sourcePrice;
      resource.isNumLock = baseRcjModel.isNumLock;
      resource.numLockNum = baseRcjModel.numLockNum;
      //resource.rcjDetailEdit = baseRcjModel.rcjDetailEdit;
      resource.dePrice = baseRcjModel.dePrice;
      resource.marketPrice = baseRcjModel.marketPrice;
      resource.initResQty = baseRcjModel.initResQty;
      //copy
      let    pbArray  =new Array();
      if(ObjectUtils.isNotEmpty( baseRcjModel.pbs)){
        baseRcjModel.pbs.forEach(item =>
        {
          let sequence = Snowflake.nextId();
          item.sequenceNbr =sequence;
          item.parentId  =resource.sequenceNbr;
          pbArray.push(ConvertUtil.deepCopy(item))
        } );
      }
      resource.pbs = pbArray;
    }
    //专用于材料合并替换
    if (singleId === RcjCommonConstants.RCJ_MERGE_REPLACE) {
      resource.rcjId = baseRcjModel.rcjId;
      resource.unitId = unitId;
      resource.materialCode = baseRcjModel.materialCode;
      resource.materialName = baseRcjModel.materialName;
      resource.unit = baseRcjModel.unit;
      resource.specification = baseRcjModel.specification;
      resource.dePrice = baseRcjModel.dePrice;
      resource.marketPrice = baseRcjModel.marketPrice;
      resource.markSum = baseRcjModel.markSum;
      resource.ifDonorMaterial = baseRcjModel.ifDonorMaterial;
      resource.donorMaterialPrice = baseRcjModel.donorMaterialPrice;
      resource.producer = baseRcjModel.producer;
      resource.manufactor = baseRcjModel.manufactor;
      resource.brand = baseRcjModel.brand;
      resource.deliveryLocation = baseRcjModel.deliveryLocation;
      resource.qualityGrade = baseRcjModel.qualityGrade;
      resource.kindSc = baseRcjModel.kindSc;
      resource.transferFactor = baseRcjModel.transferFactor;
    }
    let parentRcj;
    if (ObjectUtil.isNotEmpty(rcjId)) {
      parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
    } else {
      if (  (!ObjectUtil.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) && singleId !== RcjCommonConstants.RCJ_MEMORY_REPLACE){
           await ProjectDomain.getDomain(constructId).getDeDomain().attachPBs(resource, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr);
           resource.pbs.forEach(item => {
             item.marketPrice = item.dePrice;
             item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
             item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
             item.markSum = RcjCommonConstants.MARKSUM_JX;
             item.isTempRemove = resource.isTempRemove;
             item.unitId = resource.unitId;
             item.constructId = resource.constructId;
             if (singleId === RcjCommonConstants.RCJ_MEMORY_REPLACE) {
               item.dePrice = item.dePrice;
               item.marketPrice = item.marketPrice;
             }
             for (let key in RcjTypeEnum) {
               if (RcjTypeEnum[key].code == item.kind) {
                 item.type = RcjTypeEnum[key].desc;
               }
             }
             this.processingMarketPrice(item);
           });
           //合并材料专用
           if (singleId === RcjCommonConstants.RCJ_MERGE_REPLACE ) {
             let  pbs =new Array();
             if(ObjectUtils.isNotEmpty(baseRcjModel.pbs)){
               baseRcjModel.pbs.forEach(item=>{
                 let pb = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(), deRowId, item.kind);
                 PropertyUtil.copyProperties(item, pb, ['sequenceNbr']);
                 pb.parentId =resource.sequenceNbr;
                 pbs.push(pb);
               }) ;
             }
             resource.pbs = pbs;
           }
      }
    }

    this.processingMarketPrice(resource);
    this.processingDonorMaterial(resource);
    if (ObjectUtil.isNotEmpty(parentRcj)) {
      if ((parentRcj.kind === 7 || parentRcj.kind === 8)) {
        if (resource.kind !== 2) {
          return false;
        }
      } else if (parentRcj.kind === 10 && resource.kind === 5) {

      } else if (parentRcj.kind === 10 && resource.kind === 2) {

      } else if (parentRcj.kind != resource.kind || resource.levelMark != RcjCommonConstants.LEVELMARK_ZERO) {
        return false;
      }
      // 子项材料中主材费只能替换为主材费，设备费只能替换为设备费，材料费只能替换为材料费
      if (original.kind === RCJKind.主材 && resource.kind !== RCJKind.主材) {
        return ResponseData.fail("子项材料中主材费只能替换为主材费，请重新输入！");
      } else if (original.kind === RCJKind.设备 && resource.kind !== RCJKind.设备) {
        return ResponseData.fail("子项材料中设备费只能替换为设备费，请重新输入！");
      } else if (original.kind === RCJKind.材料 && resource.kind !== RCJKind.材料) {
        return ResponseData.fail("子项材料中材料费只能替换为材料费，请重新输入！");
      }
      delete resource.levelMark;
      resource.parentId = parentRcj.sequenceNbr;
      resource.sequenceNbr = Snowflake.nextId();
      parentRcj.pbs.push(resource);
      parentRcj.pbs = parentRcj.pbs.filter(item => item.sequenceNbr !== original.sequenceNbr);
      // if(ObjectUtils.isNotEmpty(factor)){
      //   parentRcj.resQty = original.resQty * factor ;
      // }
      await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
      await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs,  parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        de.specification = parentRcj.specification;
        de.deName = parentRcj.materialName;
        de.unit = parentRcj.unit;
        de.price = parentRcj.dePrice;
        de.marketPrice = parentRcj.marketPrice;
        await deDomain.updateDe(de);
      }
    } else {
      let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
      let de = deDomain.getDeById(resource.deId);
      if (de.type !== DeTypeConstants.DE_TYPE_USER_DE && de.type !== DeTypeConstants.DE_TYPE_USER_RESOURCE && resource.isDeResource !== 1 && singleId !== RcjCommonConstants.RCJ_MERGE_REPLACE) {
        if ((ObjectUtils.isEmpty(param) || param.source !== CommonConstants.BZHS) && ObjectUtils.isEmpty(param.isConversionDeal)) {
          await this.calDeNameByReplaceRcj(de, original, resource);
        }
        await deDomain.updateDe(de);
      }
      if((resource.isDeResource === 1  ||  ( ObjectUtils.isNotEmpty(original) && original.isDeResource ===1)) && singleId === RcjCommonConstants.RCJ_MERGE_REPLACE){
        de.deCode = resource.materialCode
        de.specification = resource.specification;
        de.deName = resource.materialName;
        de.unit = resource.unit;
        de.price = resource.dePrice;
        de.marketPrice = resource.marketPrice;
        await deDomain.updateDe(de);
      }
      await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
    }
    await ProjectDomain.getDomain(constructId).getResourceDomain().notify(resource);
    try {
    // 处理换算信息
    if (param.isConversionDeal !== true) {
      await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'replace', original, null);
    }
      await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        constructMajorType: resource.libraryCode
      });

      //联动计算装饰超高人材机数量
      await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
      let deDomain1 = ProjectDomain.getDomain(constructId).getDeDomain();
      await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, deDomain1.getDeById(deId).unitId, deId, "update");

    } catch (error) {
      console.error('捕获到异常:', error);
    }
    return true;

  }

  /**
   * 按定额生成主材/设备
   * @param deId
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @return {Promise<string>} 人材机id
   */
  async supplementRcjDataByDe(deId, constructId, singleId, unitId, deRowId, kind, materialCode) {
    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let detail = {
      materialName: deModel.deName,
      materialCode: materialCode || (Number(kind) === 4 ? '补充设备001' : '补充主材001'),
      specification: '',
      unit: deModel.unit?.replace(/^\d+/, ''),
      resQty: this.extractUnit(deModel.unit),
      initResQty: this.extractUnit(deModel.unit),
      totalNumber: this.extractUnit(deModel.unit) * deModel.quantity,
      dePrice: 0,
      marketPrice: 0,
      taxRemoval: '',
      kind: Number(kind)
    };
    if (deModel.isTempRemove === 1) {
      detail.resQty = 0
      detail.changeResQty = this.extractUnit(deModel.unit)
    }
    await this.supplementRcjData(deId, constructId, singleId, unitId, deRowId, detail, null, null);
    deModel.isExistedZcSb = CommonConstants.COMMON_YES;
  }

  extractUnit(str) {
    // 使用正则表达式匹配字符串开头的数字
    const match = str.match(/^\d+/);
    // 如果找到匹配项,返回数字部分
    if (match) {
      return parseFloat(match[0]);
    }
    // 如果没有找到匹配项,返回 NaN
    return 1;
  }

  /**
   * 将主材/设备同步到子目
   * @param deId
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @return {Promise<string>} 人材机id
   */
  async syncDeDataByRcj(deId, constructId, singleId, unitId, deRowId, content, addType) {
    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    switch (addType) {
      case 'replace': //替换
        deModel.deName = content;
        break;
      case 'append': //追加
        deModel.deName = deModel.deName + ' ' + content;
        break;
    }
  }

  /**
   *  补充 人材机明细
   * @param deId
   * @param baseRcjModel
   * @param constructId
   * @param singleId
   * @param unitId
   * @return {Promise<string>} 人材机id
   */
  async supplementRcjData(deId, constructId, singleId, unitId, deRowId, detail, rcjId, rcjDetailId, replaceFlag) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }

    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    let { materialName, materialCode, specification, unit, resQty, initResQty, totalNumber, changeResQty, dePrice, marketPrice, kind } = detail;
    let sequence = Snowflake.nextId();

    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      let rcjs = rcjList.filter(item => item.deId === deId);
      for (let rcj of rcjs) {
        rcj.pbs?.forEach(item => {
          if (item.sequenceNbr === rcjDetailId) {
            rcjId = item.parentId
          }
        });
        if (ObjectUtils.isNotEmpty(rcjId)) {
          break
        }
      }
    }

    let original = null;
    if (ObjectUtils.isNotEmpty(rcjId)) {
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
      parentRcj?.pbs.forEach(item => {
        if (item.sequenceNbr === rcjDetailId) {
          original = item;
        }
      });
    } else {
      original = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    }
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    let rcjDetailKind;
    if (ObjectUtils.isNotEmpty(rcjList)) {
      if (ObjectUtils.isNotEmpty(rcjList[0].pbs)) {
        rcjDetailKind = rcjList[0]?.pbs[0]?.kind;
      } else {
        rcjDetailKind = rcjList[0].kind;
      }
    }
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      if (rcjDetailKind === 10 && kind === 2) { //配比材料 里只能补充材料类型
      } else if (rcjDetailKind != kind) {
        return RcjCommonConstants.ERROR_MESSAGE;
      }
    }

    let resource = new ResourceModel(deModel.constructId, deModel.unitId, replaceFlag === 1 ? rcjDetailId : sequence, deRowId, kind);
    //let resource = new ResourceModel(deModel.constructId, deModel.unitId, sequence, deRowId, kind);
    resource.deRowId = deModel.sequenceNbr;
    resource.kind = kind;
    resource.rcjId = Snowflake.nextId();
    resource.parentId = deModel.sequenceNbr;
    resource.deId = deModel.sequenceNbr;
    resource.materialName = materialName;
    resource.materialCode = materialCode;
    resource.specification = specification;
    resource.unit = unit;
    resource.dePrice = dePrice;
    resource.marketPrice = marketPrice;
    resource.price = dePrice;

    resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
    resource.producer = null;
    resource.manufactor = null;
    resource.brand = null;
    resource.deliveryLocation = null;
    resource.qualityGrade = null;
    resource.remark = null;
    resource.markSum = RcjCommonConstants.MARKSUM_JX;
    resource.totalNumber = totalNumber?totalNumber:RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
    resource.libraryCode = deModel.libraryCode;

    resource.resQty = resQty;
    resource.initResQty = initResQty?initResQty:RcjCommonConstants.DEFAULT_RESQTY;
    resource.changeResQty = changeResQty;
    resource.originalQty = RcjCommonConstants.DEFAULT_RESQTY;
    resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
    resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
    resource.numLockNum = 0;
    resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
    resource.isTempRemove = deModel.isTempRemove;
    resource.supplementRcjFlag = RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
    // if (ObjectUtils.isEmpty(rcjDetailId)) {
    //   resource.levelMark = RcjCommonConstants.LEVELMARK_ZERO;
    // }
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == resource.kind) {
        resource.type = RcjTypeEnum[key].desc;
      }
    }
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList)) {
      rcjUserList = [];
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList);
    }
    this.processingMarketPrice(resource);
    this.processingDonorMaterial(resource);
    let deepResource = ConvertUtil.deepCopy(resource);
    let deepResource2 = ConvertUtil.deepCopy(resource);
    rcjUserList.push(deepResource);
    //判定内存中不存在，则放入内存
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap) || objMap.size===0) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let  memoryRcj=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
    if(ObjectUtils.isNotEmpty(memoryRcj)){
      let  existRcj =await this.service.PreliminaryEstimate.gsRcjCollectService.findAlikeRcj(memoryRcj,deepResource2);
      if(ObjectUtils.isEmpty(existRcj)){
        memoryRcj.push(deepResource2);
      }
    }else {
      let   memoryArray=new Array();
      memoryArray.push(deepResource2)
      objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId , memoryArray );
    }

    let parentRcj;
    if (ObjectUtil.isNotEmpty(rcjId)) {
      parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
    }
    if (ObjectUtil.isNotEmpty(parentRcj)) {
      let flag= true;
      if(parentRcj.kind != kind){
        if(parentRcj.kind==10 &&kind ==2){
          flag=false;
        }
        if(  (parentRcj.kind==6 || parentRcj.kind==7 || parentRcj.kind==8 || parentRcj.kind==9)  &&kind ==2){
          flag=false;
        }
      }else {
        flag=false;
      }
      if ((parentRcj.kind === 7 || parentRcj.kind === 8)) {
        if (resource.kind !== 2) {
          return false;
        }
      }
      if ( flag) {
        return false;
      }
      resource.parentId = parentRcj.sequenceNbr;
      if (ObjectUtils.isNotEmpty(rcjDetailId)) {
        parentRcj.pbs = parentRcj.pbs.filter(item => item.sequenceNbr !== rcjDetailId);
      }
      resource.isTempRemove = parentRcj.isTempRemove;
      if (replaceFlag === 1) {
        parentRcj.pbs = parentRcj.pbs.filter(item => item.sequenceNbr !== original.sequenceNbr);
      }
      parentRcj.pbs.push(resource);
      await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
      await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs,  parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
      await ProjectDomain.getDomain(constructId).getResourceDomain().notify(parentRcj);
    } else {
      resource.levelMark = RcjCommonConstants.LEVELMARK_ZERO;
      // let constructRcjArray = new Array();
      //
      // let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
      // Array.prototype.push.apply(constructRcjArray, constructProjectRcjs);
      // rcjListHasDetail.forEach(item => {
      //   Array.prototype.push.apply(rcjDetailList, item.pbs);
      // });
      // Array.prototype.push.apply(constructRcjArray, rcjDetailList)
      // let deepResource2 = ConvertUtil.deepCopy(resource);
      // await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(deepResource2, true, constructRcjArray);
      await ProjectDomain.getDomain(constructId).getResourceDomain().notify(resource);
      await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
      let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
      let de = deDomain.getDeById(resource.deId);
      if (de.type !== DeTypeConstants.DE_TYPE_USER_DE && de.type !== DeTypeConstants.DE_TYPE_USER_RESOURCE && resource.isDeResource !== 1 && replaceFlag === 1) {
        await this.calDeNameByReplaceRcj(de, original, resource);
      }
    }
    try {
    // 处理换算信息
    await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'add', null, null);

      await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        constructMajorType: resource.libraryCode
      });

      //联动计算装饰超高人材机数量
      await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
      //联动计取安装费
      await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    return true;
  }


  async calDeNameByReplaceRcj(de, original, resource) {
    if (ObjectUtils.isNotEmpty(de.initDeRcjNameList) && de.initDeRcjNameList.length > 0) {
      let initDeRcjNameList = de.initDeRcjNameList;
      let initDeRcjName = initDeRcjNameList.find(o => o.sequenceNbr === resource.sequenceNbr);
      if (ObjectUtils.isNotEmpty(initDeRcjName)) {
        initDeRcjName.replaceMaterialName = " 换为【" + resource.materialName + "】";
        if (resource.materialName === original.materialName || resource.kind == 4 || resource.kind == 5) {
          //如果替换后名称==上一次名称，则取消替换信息、如果替换为主材/设备取消替换信息
          initDeRcjName.replaceMaterialName = "";
        }
        if ((original.kind == 4 || original.kind == 5) && resource.kind != 4 && resource.kind != 5) {
          //之前为主材/设备替换为不是,换算信息为空
          initDeRcjName.initMaterialName = resource.materialName;
          initDeRcjName.replaceMaterialName = "";
        }
        if (original.materialCode.includes("#") || resource.materialCode.includes("#")) {
          //替换前后编码段一致则不显示替换信息
          let indexOld = original.materialCode.indexOf("#");
          let indexNew = resource.materialCode.indexOf("#");
          let oldCode = indexOld >= 0 ? original.materialCode.slice(0, indexOld) : original.materialCode;
          let newCode = indexNew >= 0 ? resource.materialCode.slice(0, indexNew) : resource.materialCode;
          if (oldCode === newCode) {
            initDeRcjName.replaceMaterialName = "";
          }
        }
      } else {
        //编辑定额名称后会清除关联，此时再次替换相当于插入
        initDeRcjName = {};
        initDeRcjName.sequenceNbr = resource.sequenceNbr;
        initDeRcjName.initMaterialName = resource.materialName;
        initDeRcjName.replaceMaterialName = " 换为【" + resource.materialName + "】";
        initDeRcjNameList.push(initDeRcjName);
        de.initDeRcjNameList = initDeRcjNameList;
      }
      await ProjectDomain.getDomain(de.constructId).getDeDomain().updateDe(de);
    }
  }



  /**
   *  通过编码 替换 人材机明细
   * @param deId
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @return {Promise<string>} 人材机id
   */
  async replaceRcjByCodeData(deId, constructId, singleId, unitId, deRowId, code, rcjDetailId, rcjId, replaceFlag) {
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let baseRcj = this.isMainLibStandRcj(constructId, singleId, unitId, code);
    if (ObjectUtils.isNotEmpty(baseRcj)) {
      return this.replaceRcjData(deId, baseRcj, constructId, singleId, unitId, deRowId, rcjDetailId, rcjId, null, {});
    }
    //查询内存
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap)) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
    if (ObjectUtils.isNotEmpty(unitMemory) ) {
      let  rcj=unitMemory.find(item => item.materialCode === code);
      if(ObjectUtils.isNotEmpty(rcj)){
        rcj.isDeResource=CommonConstants.COMMON_NO;
        return this.replaceRcjData(deId, rcj, constructId, RcjCommonConstants.RCJ_MEMORY_REPLACE, unitId, deRowId, rcjDetailId, rcjId, null, {});
      }
    }
   //用户rcj
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList)|| rcjUserList.size===0) {
      rcjUserList = [];
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList);
    }
    let rcjUser = rcjUserList.find(item => code === item.materialCode && unitId ===  item.unitId);
    if (ObjectUtils.isNotEmpty(rcjUser)) {
      return this.supplementRcjData(deId, constructId, singleId, unitId, deRowId, rcjUser, rcjId, rcjDetailId, 1);
    }
    return false;
  }


  /**
   * 删除 人材机明细
   * @param deId
   * @param constructId
   * @param unitId
   * @param rcjDetailId
   * @returns {Promise<boolean>}
   */
  async deleteRcjByCodeData(deId, constructId, unitId, rcjDetailId, isCountCost = true, param= {}) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    let rcjListDetails = [];
    for (let t of rcjList) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjListDetails.push(item);
          }
        );
      }
    }
    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    let rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
    if (ObjectUtils.isNotEmpty(rcjRes)) {
      await ProjectDomain.getDomain(constructId).getResourceDomain().removeResource(WildcardMap.generateKey(unitId, deId, rcjDetailId));
      // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
      if (isCountCost) {
        try {
          await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: '1',
            unitId: unitId,
            constructMajorType: deModel.libraryCode
          });

          //联动计算装饰超高人材机数量
          await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
          //联动计算安装计取费用
          await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
        } catch (error) {
          console.error('捕获到异常:', error);
        }
      }
    try {
      // 处理换算信息
      if (param.isConversionDeal !== true) {
        await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjRes, 'del', null, null);
      }
    } catch (error) {
      console.error('捕获到异常:', error);
    }
      return true;
    }
    if (ObjectUtils.isNotEmpty(rcjListDetailsPb)) {
      await ProjectDomain.getDomain(constructId).getResourceDomain().removeResourcePb(WildcardMap.generateKey(unitId, deId, rcjListDetailsPb.parentId), rcjDetailId);
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjListDetailsPb.parentId);
      await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
      // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
      if (isCountCost) {
        try {
          await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: '1',
            unitId: unitId,
            constructMajorType: deModel.libraryCode
          });

          //联动计算装饰超高人材机数量
          await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
          //联动计算安装计取费用
          await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
        } catch (error) {
          console.error('捕获到异常:', error);
        }
      }
    try {
      // 处理换算信息
      if (param.isConversionDeal !== true) {
        await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjListDetailsPb, 'del', null, null);
      }
    } catch (error) {
      console.error('捕获到异常:', error);
    }
      return true;
    }
    return false;
  }


  /**
   *  否是住定额库标准人材机
   * @param constructId
   * @param singleId
   * @param unitId
   * @param code
   * @returns {null}
   */
  isMainLibStandRcj(constructId, singleId, unitId, code) {
    let mainLibCode = ProjectDomain.getDomain(constructId).getProjectById(unitId).constructMajorType;
    let sql = 'select * from gs_base_rcj where library_code = ? and material_code = ? limit 1';
    let res = this.app.gsSqlite3DataSource.prepare(sql).get(mainLibCode, code);
    if (!res) {
      res = null;
    } else {
      res = SqlUtils.convertToModel([res])[0];
    }
    return res;
  }

  /**
   * 获取定额下所有人材机不管是那一级别的定额
   * @param {*} constructId
   * @param {*} unitId
   * @param {*} deRowId
   * @returns
   */
  async getAllRcjDetailByDeId(constructId, unitId, deRowId) {
    let resultList = [];
    let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    let deRow = deDomain.getDeById(deRowId);
    if (ObjectUtils.isEmpty(deRow)) {
      return resultList;
    }
    let deRowIds = [deRowId];
    if (deRow.type === DeTypeConstants.DE_TYPE_DELIST) {

      let childRowList = [deRow];
      deDomain.findChilds(deRow, childRowList, [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_DELIST, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_RESOURCE]);
      childRowList.forEach(item => {
        deRowIds.push(item.rowId);
      });
    }
    deRowIds.forEach(item => {
      let rcjs = deDomain.ctx.resourceMap.findByValueProperty('deRowId', item);
      resultList = resultList.concat(rcjs.filter(item => item.isDeResource === CommonConstants.COMMON_NO));
    });
    return this.rcjSort(resultList);
  }

  /**
   *  获取各层级人材机明细
   * @params  constructId, unitId, deId,type
   * @returns {Promise<void>}
   */
  async getAllRcjDetail(constructId, unitId, deRowId, type) {
    let deTree = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId);
    let deIds = [];
    let rcjDeKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    let resultList = new Array();
    if (BranchProjectLevelConstant.top === type) {
      this.groupHandle(rcjList, resultList);
      return this.rcjSort(resultList);
    }
    if (BranchProjectLevelConstant.fb === type || BranchProjectLevelConstant.zfb === type) {
      let fbrjclist = new Array();
      this.findDeRows(deRowId, deTree, deIds);
      deIds.forEach(deId => {
        fbrjclist = fbrjclist.concat(rcjList.filter(item => item.deRowId === deId));
      });
      this.groupHandle(fbrjclist, resultList);
      return this.rcjSort(resultList);
    }
    if (BranchProjectLevelConstant.qd === type) {
      resultList = rcjList.filter(item => item.deRowId === deRowId);
      return this.rcjSort(resultList);
    }
    if (BranchProjectLevelConstant.de === type || DeTypeConstants.DE_TYPE_ANZHUANG_FEE === type) {
      let de = deTree.find(item => item.deRowId === deRowId);
      if (de.isDeResource == 1) {
        let rcj = rcjList.find(item => item.deRowId === deRowId);
        if (ObjectUtils.isNotEmpty(rcj.pbs) && rcj.levelMark !== RcjCommonConstants.LEVELMARK_ZERO) {
          resultList = rcj.pbs;
        }
      }
      if (de.isDeResource == 0) {
        resultList = rcjList.filter(item => item.deRowId === deRowId);
      }
      return this.rcjSort(resultList);
    }
    if (DeTypeConstants.DE_TYPE_USER_DE === type || DeTypeConstants.DE_TYPE_USER_RESOURCE === type) {
      return this.rcjSort(rcjList.filter(item => item.deRowId === deRowId));
    }
    if (DeTypeConstants.DE_TYPE_RESOURCE === type) {
      let result = rcjList.find(a => a.deRowId === deRowId);
      if (ObjectUtils.isNotEmpty(result) && result.markSum === 1) {
        resultList = result.pbs;
      }
      return this.rcjSort(resultList);
    }

  }


  /**
   * 人材机 排序
   * @param rcj
   */
  rcjSort(rcj) {
    if (ObjectUtils.isEmpty(rcj)) {
      return rcj;
    }
    rcj.sort((a, b) => {
      // if (a.kind === b.kind) {
      //     return b.materialCode.localeCompare(a.materialCode);
      // }
      return a.kind - b.kind;
    });
    let zcRcj = rcj.filter(item => item.kind == 5);
    let sbRcj = rcj.filter(item => item.kind == 4);
    let fzcRcj = rcj.filter(item => item.kind != 5 && item.kind != 4);
    let rcjResult = zcRcj.concat(sbRcj).concat(fzcRcj);
    return rcjResult;
  }

  /**
   * 分组处理并重新封装
   * @param constructRcjArray
   * @param array1
   * @returns {Promise<void>}
   */
  async groupHandle(constructRcjArray, array1) {
    //拼接相同材料
    if (!ObjectUtils.isEmpty(constructRcjArray)) {
      for (let arrayElement of constructRcjArray) {
        arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
          .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
          .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
          .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
          .concat(!ObjectUtils.isEmpty(arrayElement.dePrice) ? arrayElement.dePrice : '')
          .concat(!ObjectUtils.isEmpty(arrayElement.marketPrice) ? arrayElement.marketPrice : '');
      }
    } else {
      return null;
    }
    //分组
    const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
      // 将分组作为对象的 key，相同分组的项放入同一个数组
      (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
      return accumulator;
    }, {});

    //循环分组之后的人材机
    for (let group in grouped) {
      if (grouped.hasOwnProperty(group)) {
        let groupedElement = grouped[group][0];
        let totalNumber = 0;
        let allPbs = new Array();
        //let set = new Set();
        grouped[group].forEach(item => {
          totalNumber = NumberUtil.add(totalNumber, item.totalNumber);
          if (ObjectUtils.isNotEmpty(item.pbs) && item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO) {
            allPbs = allPbs.concat(item.pbs);
            let pbsGroup = new Array();
            this.groupHandle(allPbs, pbsGroup);
            allPbs = pbsGroup;
          }
        });
        groupedElement.unitTotalNumber = NumberUtil.numberScale(totalNumber, 4);
        if (allPbs.length > 0) {
          groupedElement.pbs = allPbs;
        }
        //groupedElement.total = NumberUtil.multiplyToString(totalNumber, groupedElement.marketPrice, 2);
        groupedElement.type = this.service.PreliminaryEstimate.gsRcjCollectService.getRcjTypeEnumDescByCode(groupedElement.kind);
        array1.push(groupedElement);
      }
    }
  }

  /**
   * 编辑 人材机明细区
   * @param args
   */
  async updateRcjDetail(args) {
    let { constructId, singleId, unitId, deId, rcjDetailId, constructRcj } = args;
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    constructProjectRcjs.forEach(item => constructRcjArray.push(item));
    //Array.prototype.push.apply(constructRcjArray,constructProjectRcjs);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    //Array.prototype.push.apply(constructRcjArray,rcjDetailList);
    if (ObjectUtils.isNotEmpty(rcjDetailList)) {
      rcjDetailList.forEach(item => constructRcjArray.push(item));
    }
    let resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetailId);
    let oldResRcj = ConvertUtil.deepCopy(resRcj);
    let kind = constructRcj.kind;
    let materialName = constructRcj.materialName;
    let specification = constructRcj.specification;
    let unitBj = constructRcj.unit;
    //s消耗量
    let resQty = constructRcj.resQty? Number(constructRcj.resQty) : constructRcj.resQty;
    let marketPrice = parseFloat(constructRcj.marketPrice);
    let sourcePrice = constructRcj.sourcePrice;
    //  合计数量
    let totalNumber = constructRcj.totalNumber? Number(constructRcj.totalNumber) : constructRcj.totalNumber;
    let isNumLock = constructRcj.isNumLock;

    let highlight = ObjectUtils.isEmpty(constructRcj.highlight)? null: constructRcj.highlight;

    //查询修改 对象
    let t = constructRcjArray.find(i => i.sequenceNbr === rcjDetailId);

    //确定 已有编码
    let ts = constructRcjArray.filter(constructRcjArrayElement => !(constructRcjArrayElement.materialCode === t.materialCode
      && constructRcjArrayElement.materialName === t.materialName
      && constructRcjArrayElement.specification === t.specification
      && constructRcjArrayElement.unit === t.unit
      && constructRcjArrayElement.dePrice === t.dePrice));

    if (ObjectUtils.isEmpty(resRcj)) {
      let rcjDetail = rcjDetailList.find(item => item.sequenceNbr === rcjDetailId);
      resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetail.parentId);
    }
    if (!ObjectUtils.isEmpty(t)) {
      let constructRcjArrayElement = t;
      let ifChangeMaterialCode = false;
      let ifChangeMaterialNumber = false;

      if (ObjectUtils.isNotEmpty(isNumLock)) {
        if (isNumLock == RcjCommonConstants.ISNUMLOCK) {
          constructRcjArrayElement.isNumLock = isNumLock;
          constructRcjArrayElement.totalNumber = constructRcjArrayElement.numLockNum;
          if (deModel.quantity !== 0) {
            constructRcjArrayElement.resQty = NumberUtil.numberScale(NumberUtil.divide(constructRcjArrayElement.totalNumber, deModel.quantity), 3);
          }
        }
        if (isNumLock == RcjCommonConstants.ISNUMLOCK_TRUE) {
          constructRcjArrayElement.isNumLock = isNumLock;
          constructRcjArrayElement.numLockNum = constructRcjArrayElement.totalNumber;
        }
      }

            //类型
      if (ObjectUtils.isNotEmpty(kind) && constructRcjArrayElement.kind != kind) {
          constructRcjArrayElement.kind = kind;
          for (let key in RcjTypeEnum) {
              if (RcjTypeEnum[key].code === kind) {
                  constructRcjArrayElement.type=   RcjTypeEnum[key].desc;
              }
          }
          ifChangeMaterialCode =true;
          deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        //主材设备类型判断
        if(constructRcjArrayElement.kind === 4 || constructRcjArrayElement.kind === 5){
          deModel.isExistedZcSb = CommonConstants.COMMON_YES;
          if(constructRcjArrayElement.marketPrice != constructRcjArrayElement.dePrice){
            marketPrice = constructRcjArrayElement.marketPrice;
          }
        }
      }
      if (!ObjectUtils.isEmpty(materialName)) {
          constructRcjArrayElement.materialName = materialName;
          ifChangeMaterialCode =true;
          deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
      }
      if (!ObjectUtils.is_Undefined(specification) && constructRcjArrayElement.specification !=specification) {
          if (specification !=""){
              constructRcjArrayElement.specification = specification;
              deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
          }else {
              constructRcjArrayElement.specification = null;
          }
          ifChangeMaterialCode =true;
      }
      if (!ObjectUtils.isEmpty(unitBj)) {
          constructRcjArrayElement.unit = unitBj;
          ifChangeMaterialCode =true;
          deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
      }
      if(ObjectUtils.isNotEmpty(resQty)){
          constructRcjArrayElement.lastResQty = constructRcjArrayElement.resQty
          constructRcjArrayElement.resQty = resQty;
          constructRcjArrayElement.consumerResQty = resQty;
          deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
          if (!constructRcjArrayElement.hasOwnProperty("levelMark")) {
              ifChangeMaterialCode =true;
              ifChangeMaterialNumber =true;
          }else {
            resRcj.updateCalcuTotalNumber = true;
            await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deId, resRcj,false)
            await  ProjectDomain.getDomain(constructId).getResourceDomain().notify(resRcj);
          }
      try {
          // 人材机修改消耗量，处理换算信息
        if (args.isConversionDeal !== true) {
          await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'updateQty', null, null);
        }
      } catch (error) {
        console.error('捕获到异常:', error);
      }
      }

      if (ObjectUtils.isNotEmpty(totalNumber)) {
        constructRcjArrayElement.totalNumber = totalNumber;
        if (!ZSFeeConstants.ZS_RCJ_LIST.includes(resRcj.materialCode)) {
          constructRcjArrayElement.lastResQty = constructRcjArrayElement.resQty
          constructRcjArrayElement.resQty = NumberUtil.numberScale(NumberUtil.divide(constructRcjArrayElement.totalNumber, deModel.quantity), 3);
          let t2 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
          if (ObjectUtils.isNotEmpty(t2) && !constructRcjArrayElement.hasOwnProperty('levelMark')) {
            constructRcjArrayElement.resQty = NumberUtil.numberScale(NumberUtil.divide(constructRcjArrayElement.totalNumber, t2.totalNumber), 3);
            ifChangeMaterialCode =true;
            ifChangeMaterialNumber =true;
          }
          //constructRcjArrayElement.total = NumberUtil.multiplyToString(constructRcjArrayElement.marketPrice, constructRcjArrayElement.totalNumber,2);
          await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'updateQty', null, null);
        }
        //若数量发生变化，可能会导致三材量发生变化
        if (ObjectUtils.isNotEmpty(constructRcjArrayElement.kindSc) && ObjectUtils.isNotEmpty(constructRcjArrayElement.transferFactor)) {
          await this.service.PreliminaryEstimate.gsRcjCollectService.updateOtherProjectScGJ(constructId);
        }
      }

      if (ObjectUtil.isNotEmpty(marketPrice)) {
        constructRcjArrayElement.marketPrice = marketPrice;
        constructRcjArrayElement.donorMaterialPrice = null;
        constructRcjArrayElement.total = NumberUtil.multiplyToString(constructRcjArrayElement.marketPrice, constructRcjArrayElement.totalNumber, 2);
        constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
        constructRcjArrayElement.highlight = highlight;
        if (constructRcjArrayElement.marketPrice === constructRcjArrayElement.price) {
          constructRcjArrayElement.sourcePrice = '';
        }
        // 主材和设备，修改市场价，同步定额价
        constructRcjArrayElement.kind = constructRcjArrayElement.kind? Number(constructRcjArrayElement.kind): constructRcjArrayElement.kind
        let  baseRCJ = await this.service.PreliminaryEstimate.gsBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
        if (constructRcjArrayElement.kind === 4 || constructRcjArrayElement.kind === 5 ||  baseRCJ?.kind === 4 || baseRCJ?.kind === 5) {
          constructRcjArrayElement.dePrice = constructRcjArrayElement.marketPrice;
          //ifChangeMaterialCode = true;
        }
        // 标准编码段为BCRGF、BCJXF、BCCLF、BCZCF、BCSBF 的人材机市场价=定额价
        if (constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG)
            || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX)
            || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL)
            || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC)
            || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB)
        ) {
          constructRcjArrayElement.dePrice = marketPrice;
        }
        let  unitAllMemory=this.getRcjMemory(constructId,unitId);
        if(ObjectUtils.isNotEmpty(unitAllMemory)){
          let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode ===constructRcjArrayElement.materialCode);
          if(ObjectUtils.isNotEmpty(rcjMemorys)){
            rcjMemorys.forEach(item=>{
              item.marketPrice= constructRcjArrayElement.marketPrice;
              item.sourcePrice= constructRcjArrayElement.sourcePrice;
              item.dePrice= constructRcjArrayElement.dePrice;
              item.donorMaterialPrice= constructRcjArrayElement.donorMaterialPrice;
            });
          }
        }

        //修改的子
        let t2 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
        if (ObjectUtils.isNotEmpty(t2) && !constructRcjArrayElement.hasOwnProperty('levelMark')) {
          await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialPrice(rcjDetailList, t2);
        }
        await this.service.PreliminaryEstimate.gsRcjCollectService.updateUnitRcjCellect({
          constructId: constructId,
          unitId: unitId,
          constructProjectRcj: { marketPrice: constructRcjArrayElement.marketPrice, sourcePrice: constructRcjArrayElement.sourcePrice, highlight: highlight},
          sequenceNbr: constructRcjArrayElement.sequenceNbr
        });
      }

      // QTCLF1（其他材料费）编码排序影响要素仅包括：类型、规格型号
      if (constructRcjArrayElement.materialCode.includes('QTCLF1')){
        if(ObjectUtils.isNotEmpty(constructRcj.specification) || ObjectUtils.isNotEmpty(constructRcj.kind)){
          ifChangeMaterialCode = true;
        }else {
          ifChangeMaterialCode = false;
        }
      }

      //修改子编码
      if (ifChangeMaterialCode) {
        if (constructProjectRcjs.ifDonorMaterial === 1) {
          constructProjectRcjs.donorMaterialNumber = 0;
        }
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        //if (!((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark) ? constructRcjArrayElement.levelMark : ResourceConstants.LEVEL_MARK_NONE_PB) !== ResourceConstants.LEVEL_MARK_NONE_PB && constructRcjArrayElement.markSum === RcjCommonConstants.MARKSUM_JX)) {
        if (!((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark) ? constructRcjArrayElement.levelMark : ResourceConstants.LEVEL_MARK_NONE_PB) !== ResourceConstants.LEVEL_MARK_NONE_PB )) {
          //await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCode(constructRcjArrayElement, true, constructRcjArray);
          await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(constructRcjArrayElement, true, constructRcjArray);
          this.processingMarketPrice(constructRcjArrayElement);
        }
        //修二次解析改父
        else {
          //await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChange(constructProjectRcjs, rcjDetailList, constructRcjArrayElement, true);
          await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, constructRcjArrayElement, true);
        }
        //修改二次解析的子，触发修改父
        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
          let t1 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
          //await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChange(constructProjectRcjs, rcjDetailList, t1, true);
          await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs,  t1, true);
        }
      }
      if(ifChangeMaterialNumber){
        this.processMemory(constructId, unitId, constructRcjArrayElement,constructProjectRcjs);
      }

      // 换算信息
      if (ifChangeMaterialCode) {
        await this.service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'update', null, oldResRcj.materialCode);
      }


    }
    try {
      await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        constructMajorType: resRcj.libraryCode
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    if (resRcj.isDeResource === 1) {
      let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
      let de = deDomain.getDeById(resRcj.deId);
      de.deCode = resRcj.materialCode;
      //de.specification = resRcj.specification;
      await deDomain.updateDe(de);
    }
    await ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deId, resRcj, false);
    await ProjectDomain.getDomain(constructId).getResourceDomain().notify(resRcj);


        try {
            //联动计算装饰超高人材机数量
            await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);

            let deDomain1 = ProjectDomain.getDomain(constructId).getDeDomain();
            await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, deDomain1.getDeById(deId).unitId, deId, "update");
        } catch (error) {
            console.error("联动计算安装计取费用捕获到异常:", error);
        }
    }


  async multiUpdateRcjDetail (args){
    let { constructId, singleId, rcjs } = args;
    if(ObjectUtils.isNotEmpty(rcjs)&& rcjs.length !==0){
      rcjs.forEach(item=>{
        let unitId= item.unitId;
        let deId= item.deId;
        let rcjDetailId= item.rcjDetailId;
        let constructRcj= item.constructRcj;
        let arg ={ constructId, singleId, unitId , deId, rcjDetailId, constructRcj };
        this.updateRcjDetail(arg);
      });
    }
  }

  /**
   *  处理二次解析情况下，内存子 更新total 与 消耗量
   * @param constructId
   * @param unitId
   * @param constructRcjArrayElement
   * @param constructProjectRcjs
   */
   processMemory(constructId, unitId, constructRcjArrayElement,constructProjectRcjs){
    //  1.找父，用父的code匹配出内存中父
     let  parentRcj=constructProjectRcjs.find(item=>item.sequenceNbr ===constructRcjArrayElement.parentId );
       let  unitAllMemory=this.getRcjMemory(constructId,unitId);
       if(ObjectUtils.isNotEmpty(unitAllMemory)){
         let  parentRcjMemory = unitAllMemory.find(item=>item.materialCode ===parentRcj.materialCode);
         let  parentRcjMemorys = unitAllMemory.filter(item=>item.materialCode ===parentRcj.materialCode);
         if(ObjectUtils.isNotEmpty(parentRcjMemory) && ObjectUtils.isNotEmpty(parentRcjMemorys) && parentRcjMemorys.length ===1 ){
           //  2.找出内存中父下的子
           let rcjMemory= parentRcjMemory.pbs.find(item=>item.materialCode ===constructRcjArrayElement.materialCode);
           rcjMemory.resQty= constructRcjArrayElement.resQty;
           rcjMemory.total= constructRcjArrayElement.total;
         }
       }


   }



  /**
   *  处理定额rcj
   * @param constructId
   * @param rcjDetailId
   * @param unitId
   */
  async processDeRcj(constructId, unitId, rcjDetailId) {
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    constructProjectRcjs.forEach(item => constructRcjArray.push(item));
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    let resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetailId);
    if (ObjectUtils.isEmpty(resRcj)) {
      let rcjDetail = rcjDetailList.find(item => item.sequenceNbr === rcjDetailId);
      resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetail.parentId);
    }
    let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    let de = deDomain.getDeById(resRcj.deId);
    de.materialCode = resRcj.materialCode;
    await deDomain.updateDe(de);
  }


  /**
   * 定额rowId收集
   * @param deRow
   * @param relatedRowIds
   */
  findDeRows(deRowId, deTree, deIds) {
    let arrayDe = deTree.filter(item => item.parentId === deRowId);
    if (ObjectUtils.isNotEmpty(arrayDe) && arrayDe.length > 0) {
      arrayDe.forEach(item => this.findDeRows(item.deRowId, deTree, deIds));
    }
    deIds.push(deRowId);
  }

  /**
   *  处理市场价，初始化市场价
   */
  processingMarketPrice(rcj) {
    // let rcjDeKey = WildcardMap.generateKey(rcj.unitId) + WildcardMap.WILDCARD;
    // let rcjList = ProjectDomain.getDomain(rcj.constructId).getResourceDomain().getResource(rcjDeKey);
    // if (ObjectUtils.isEmpty(rcjList)) {
    //   return null;
    // }
    // let rcjDetailList = new Array();
    // let constructRcjArray = new Array();
    // // rcjList = rcjList.filter(item => item.isDeResource !== 1);
    // rcjList.forEach(item => {
    //   if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs)) {
    //     item.pbs.forEach(item2 => rcjDetailList.push(item2));
    //   }
    // });
    // Array.prototype.push.apply(constructRcjArray, rcjList);
    // Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    let constructRcjArray =this.getAllRcj(rcj);
    // 继承锁定数量 && 锁定市场价
    for (let item of constructRcjArray) {
      let code = item.materialCode;
      // if (item.materialCode.includes("#")){
      //     code = item.materialCode.substring(0,item.materialCode.lastIndexOf("#"));
      // }
      if (rcj.materialCode === code) {
        rcj.isNumLock = item.isNumLock;
        rcj.ifLockStandardPrice = item.ifLockStandardPrice;
        rcj.markSum = item.markSum;
        break;
      }
    }

    //constructRcjArray = constructRcjArray.filter(item => item.dePrice != item.marketPrice || item.kind === 5 || item.kind === 4);

    // 找出所有符合要求 rcj  明细
    for (let i = 0; i < constructRcjArray.length; i++) {
      let item = constructRcjArray[i];
      let code = item.materialCode;
      // if (item.materialCode.includes("#")){
      //     code = item.materialCode.substring(0,item.materialCode.lastIndexOf("#"));
      // }
      if (
        rcj.materialCode === code
        && rcj.kind === item.kind
        && rcj.materialName === item.materialName
        && rcj.specification === item.specification
        && rcj.unit === item.unit
        && rcj.sequenceNbr !== item.sequenceNbr
        && (item.kind === 4 || item.kind === 5 ? true : rcj.dePrice === item.dePrice)
      ) {
        if (item.kind === 4 || item.kind === 5) {
          rcj.dePrice = item.dePrice;
        }
        if(item.markSum === RcjCommonConstants.MARKSUM_BJX && ObjectUtils.isNotEmpty(item.marketPriceCp)){
          rcj.marketPrice = item.marketPriceCp;
          rcj.sourcePrice = item.sourcePriceCp;
        }else {
          rcj.marketPrice = item.marketPrice;
          rcj.sourcePrice = item.sourcePrice;
        }
        rcj.highlight = item.highlight;

        rcj.kindSc = item.kindSc;
        rcj.transferFactor = item.transferFactor;
        break;
      }
    }
    if (rcj.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtil.isNotEmpty(rcj.pbs)) {
      for (let j = 0; j < rcj.pbs; j++) {
        let rcjDetail = rcj.pbs[j];
        constructRcjArray.forEach(item => {
          let code = item.materialCode;
          // if (item.materialCode.includes("#")){
          //     code = item.materialCode.substring(0,item.materialCode.lastIndexOf("#"));
          // }
          if (
            rcjDetail.materialCode === code
            && rcjDetail.kind === item.kind
            && rcjDetail.materialName === item.materialName
            && rcjDetail.specification === item.specification
            && rcjDetail.unit === item.unit
            && rcjDetail.sequenceNbr !== item.sequenceNbr
            && rcjDetail.dePrice === item.dePrice
          ) {
            rcjDetail.marketPrice = item.marketPrice;
            rcjDetail.sourcePrice = item.sourcePrice;
            rcjDetail.highlight = item.highlight;

            rcjDetail.kindSc = item.kindSc;
            rcjDetail.transferFactor = item.transferFactor;
          }
        });
      }
    }
  }

  processingDonorMaterial(rcj) {
    let rcjDeKey = WildcardMap.generateKey(rcj.unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(rcj.constructId).getResourceDomain().getResource(rcjDeKey);
    if (ObjectUtils.isEmpty(rcjList)) {
      return null;
    }
    let rcjDetailList = new Array();
    let constructRcjArray = new Array();
    rcjList.forEach(item => {
      if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs)) {
        item.pbs.forEach(item2 => rcjDetailList.push(item2));
      }
    });
    Array.prototype.push.apply(constructRcjArray, rcjList);
    Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    // 找出所有符合要求 rcj  明细
    for (let i = 0; i < constructRcjArray.length; i++) {
      let item = constructRcjArray[i];
      let code = item.materialCode;
      if (item.materialCode.includes('#')) {
        code = item.materialCode.substring(0, item.materialCode.lastIndexOf('#'));
      }
      if (
        rcj.materialCode === code
        && rcj.kind === item.kind
        && rcj.materialName === item.materialName
        && rcj.specification === item.specification
        && rcj.unit === item.unit
        && rcj.dePrice === item.dePrice
      ) {
        rcj.updateFalg = ObjectUtils.isEmpty(item.updateFalg) ? 0 : item.updateFalg;
        rcj.ifDonorMaterial = item.ifDonorMaterial;
        rcj.donorMaterialNumber = item.donorMaterialNumber;
        rcj.donorMaterialPrice = item.donorMaterialPrice;
        break;
      }
    }
    if (rcj.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtil.isNotEmpty(rcj.pbs)) {
      for (let j = 0; j < rcj.pbs; j++) {
        let rcjDetail = rcj.pbs[j];
        constructRcjArray.forEach(item => {
          let code = item.materialCode;
          if (item.materialCode.includes('#')) {
            code = item.materialCode.substring(0, item.materialCode.lastIndexOf('#'));
          }
          if (
            rcjDetail.materialCode === code
            && rcjDetail.kind === item.kind
            && rcjDetail.materialName === item.materialName
            && rcjDetail.specification === item.specification
            && rcjDetail.unit === item.unit
            && rcjDetail.dePrice === item.dePrice
          ) {
            rcjDetail.updateFalg = ObjectUtils.isEmpty(item.updateFalg) ? 0 : item.updateFalg;
            rcjDetail.ifDonorMaterial = item.ifDonorMaterial;
            rcjDetail.donorMaterialNumber = item.donorMaterialNumber;
            rcjDetail.donorMaterialPrice = item.donorMaterialPrice;
          }
        });
      }
    }
    if (rcj.updateFalg === 0 && rcj.ifDonorMaterial === 1) {
      let totalNumber = constructRcjArray.reduce((acc, item) => {
        let code = item.materialCode;
        if (item.materialCode.includes('#')) {
          code = item.materialCode.substring(0, item.materialCode.lastIndexOf('#'));
        }
        if (
          rcj.materialCode === code
          && rcj.kind === item.kind
          && rcj.materialName === item.materialName
          && rcj.specification === item.specification
          && rcj.unit === item.unit
          && rcj.dePrice === item.dePrice) {
          return acc + item.totalNumber;
        }
        return acc;
      }, 0);
    }

  }


  /**
   * 临时删除
   * @param {*} deRowId
   * @param {*} rcjDetailId
   */
  async tempRemoveRcjRow(constructId, deRowId, rcjDetailId) {
    try {
      let deRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deRowId);
      if (deRow.isTempRemove === CommonConstants.COMMON_YES) {
        return;//父级临时删除，子级不能再临时删除了
      }
      let unitId = deRow.unitId;

      let existsId = false;
      let rcjDeKey = WildcardMap.generateKey(unitId, deRowId) + WildcardMap.WILDCARD;
      let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
      let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
      let rcjListDetailsPb = null;
      //定额人材机
      if (ObjectUtils.isNotEmpty(rcjRes)) {
        existsId = true;
        rcjRes.isTempRemove = CommonConstants.COMMON_YES;
        rcjRes.isFirstTempRemove = CommonConstants.COMMON_YES;
        rcjRes.changeResQty = rcjRes.resQty;
        rcjRes.resQty = 0;
        if (rcjRes.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(rcjRes.pbs) && rcjRes.markSum === RcjCommonConstants.MARKSUM_JX) {
          rcjRes.pbs.forEach(item2 => {
            item2.isTempRemove = CommonConstants.COMMON_YES;
            rcjRes.isFirstTempRemove = CommonConstants.COMMON_NO;
            // item2.changeResQty = item2.resQty;
            // item2.resQty = 0;

          });
        }
      } else {

        let rcjListDetails = [];
        for (let t of rcjList) {
          let ts2 = t.pbs;
          if (!ObjectUtils.isEmpty(ts2)) {
            ts2.forEach(item => {
                item.parentId = t.sequenceNbr;
                rcjListDetails.push(item);
              }
            );
          }
        }
        rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
        //配比材料
        if (ObjectUtils.isNotEmpty(rcjListDetailsPb)) {
          existsId = true;
          rcjListDetailsPb.isTempRemove = CommonConstants.COMMON_YES;
          rcjListDetailsPb.changeResQty = rcjListDetailsPb.resQty;
          rcjListDetailsPb.resQty = 0;

        }
      }

      if (existsId) {
        await ProjectDomain.getDomain(constructId).getResourceDomain().notify({ constructId, unitId, deRowId });
        await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: '1',
          unitId: unitId,
          constructMajorType: ObjectUtils.isNotEmpty(rcjRes) ? rcjRes.libraryCode : rcjListDetailsPb.libraryCode
        });

        //联动计算装饰超高人材机数量
        await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(deRow.constructId, deRow.unitId, true);
        //临时删除，联动计取安装费
        await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, deRow.unitId, deRowId, 'delete');
      }
    } catch (error) {
      console.error('捕获到异常:', error);
    }
  }

  /**
   * 取消临时删除
   * @param {*} deRowId
   * @param {*} rcjDetailId
   * @returns
   */
  async cancelTempRemoveRcjRow(constructId, deRowId, rcjDetailId) {
    try {

      let deRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deRowId);
      if (deRow.isTempRemove === CommonConstants.COMMON_YES) {
        return;
      }
      let unitId = deRow.unitId;
      let existsId = false;
      let rcjDeKey = WildcardMap.generateKey(unitId, deRowId) + WildcardMap.WILDCARD;
      let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
      let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
      let rcjListDetailsPb = null;
      if (ObjectUtils.isNotEmpty(rcjRes) && rcjRes.isTempRemove === CommonConstants.COMMON_YES) {
        existsId = true;
        rcjRes.isTempRemove = CommonConstants.COMMON_NO;
        rcjRes.resQty = rcjRes.changeResQty;
        if (rcjRes.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(rcjRes.pbs) && rcjRes.markSum === RcjCommonConstants.MARKSUM_JX) {
          rcjRes.pbs.forEach(item2 => {
            item2.isTempRemove = CommonConstants.COMMON_NO;
            // item2.resQty =item2.changeResQty;
          });
        }
      } else {
        let rcjListDetails = [];
        for (let t of rcjList) {
          let ts2 = t.pbs;
          if (!ObjectUtils.isEmpty(ts2)) {
            ts2.forEach(item => {
                item.parentId = t.sequenceNbr;
                rcjListDetails.push(item);
              }
            );
          }
        }

        rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
        //配比材料
        if (ObjectUtils.isNotEmpty(rcjListDetailsPb) && rcjListDetailsPb.isTempRemove === CommonConstants.COMMON_YES) {
          existsId = true;
          rcjListDetailsPb.isTempRemove = CommonConstants.COMMON_NO;
          rcjListDetailsPb.resQty = rcjListDetailsPb.changeResQty;

        }
      }

      if (existsId) {
        await ProjectDomain.getDomain(constructId).getResourceDomain().notify({ constructId, unitId, deRowId });
        await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: '1',
          unitId: unitId,
          constructMajorType: ObjectUtils.isNotEmpty(rcjRes) ? rcjRes.libraryCode : rcjListDetailsPb.libraryCode
        });

        //联动计算装饰超高人材机数量
        await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(deRow.constructId, deRow.unitId, true);
        //取消临时删除后，重新计算安装计取数据
        await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, deRow.unitId, deRowId, 'update');
      }

    } catch (error) {
      console.error('捕获到异常:', error);
    }
  }

  /**
   *
   * @param args
   * @returns {Promise<void>}
   */
  async getDefaultCode2(args) {
    let { constructId, unitId, prefix } = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList) ) {
      return '#1';
    } else {
      let result = rcjUserList.filter(item => {
          return prefix ===item.materialCode.replace( /#\d+/g, '') && item.unitId === unitId;
        }
      );
      let  max =await this.service.PreliminaryEstimate.gsRcjCollectService.getMaxNumber(result,{'materialCode':prefix});
      return   '#'+max ;
    }
  }
  async getDefaultCode(args) {
    let { constructId, unitId, prefix } = args;
    let regex = new RegExp(`${prefix}\\d{3}`);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList)) {
      return String(1).padStart(3, '0');
    } else {
      let result = rcjUserList.filter(item => {
          return regex.test(item.materialCode) && item.unitId === unitId;
        }
      );
      let num = String(result.length + 1).padStart(3, '0');
      return this.handleAlike(result, num, result.length + 1, prefix);
    }
  }
  handleAlike(resultList, code, length, type) {
    let relust = resultList.find(item => item.materialCode === (type + code));
    if (ObjectUtils.isNotEmpty(relust)) {
      return this.handleAlike(resultList, String(length + 1).padStart(3, '0'), length + 1, type);
    }
    return String(code).padStart(3, '0');
  }



    getRcjMemory(constructId,unitId){
      //   unitId 为空
      let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
      let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
      if (ObjectUtils.isEmpty(objMap)) {
        businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
        objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
      }
      let unitMemory =new Array();
      if(ObjectUtils.isEmpty(unitId)){
        let idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3 && item.scopeFlag === true  ).map(item=>item.sequenceNbr);
        idArray.forEach(item => {
          let  unitMemorySub= objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + item )
          if(ObjectUtils.isNotEmpty(unitMemorySub)){
            unitMemory.push(unitMemorySub);
          }
          });
      }else {
         unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
      }

      let unitAllMemory = new Array() ;
      let rcjDetailList = new Array();
      if(ObjectUtils.isEmpty(unitMemory) || unitMemory.length ===0  ){
        return  null
      }
      let rcjListHasDetail = unitMemory.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
      for (let t of rcjListHasDetail) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
          );
        }
      }
      unitAllMemory= unitMemory.concat(rcjDetailList);
      return  unitAllMemory;
    }

   getAllRcj(rcj){
    let rcjDeKey = WildcardMap.generateKey(rcj.unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(rcj.constructId).getResourceDomain().getResource(rcjDeKey);
    if (ObjectUtils.isEmpty(rcjList)) {
      return [];
      }
    let rcjDetailList = new Array();
    let constructRcjArray = new Array();
    // rcjList = rcjList.filter(item => item.isDeResource !== 1);
    rcjList.forEach(item => {
      if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs)) {
        item.pbs.forEach(item2 => rcjDetailList.push(item2));
      }
    });
    Array.prototype.push.apply(constructRcjArray, rcjList);
    Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    return   constructRcjArray;
  }


  /**
   * 查询内存下拉code
   * @param args
   * @returns {Promise<void>}
   */
  async getMemoryCode(args) {
    let { constructId, unitId,materialCode } = args;
    let  unitAllMemory=this.service.PreliminaryEstimate.gsRcjService.getRcjMemory(constructId,unitId);
    let  resultArray = new Array();
    materialCode = materialCode.replace(/#\d+/g, '');
    let params = {"materialCode": materialCode};
    let baseRcjModel = await this.service.PreliminaryEstimate.gsBaseRcjService.queryRcjByCode(params);
    if(ObjectUtils.isEmpty(baseRcjModel)){
      let  rcjList=this.getUserRcj(constructId);
      baseRcjModel=rcjList.find(item=>item.materialCode ===materialCode );
    }
    resultArray.push({"materialCode":baseRcjModel.materialCode,"materialName":baseRcjModel.materialName,"specification":baseRcjModel.specification,"unit":baseRcjModel.unit});
    //处理内存
    if(ObjectUtils.isNotEmpty(unitAllMemory)){
      let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode.replace(/#\d+/g, '') ===materialCode);
      if(ObjectUtils.isNotEmpty(rcjMemorys)){
        rcjMemorys.forEach(item => {
          if(materialCode !==item.materialCode){
            resultArray.push({"materialCode":item.materialCode,"materialName":item.materialName,"specification":item.specification,"unit":item.unit});
          }
        });
      }
    }
      return resultArray;
    }

  getUserRcj(constructId){
      let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
      let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
      // 放入用戶rcj
      if(ObjectUtils.isEmpty(rcjUserList)||rcjUserList.size===0){
      rcjUserList = new Array();
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ,rcjUserList);
    }
      return  rcjUserList;
  }

}


GsRcjService.toString = () => '[class GsRcjService]';
module.exports = GsRcjService;
