<!--
 * @Descripttion:措施项目
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: wangru
 * @LastEditTime: 2024-09-12 14:15:30
-->
<template>
  <div class="subItem-project custom-tree-table">
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <frameSelect
          type="measure"
          ref="frameSelectRef"
          eventDom="multiple-select"
          :tableData="renderedList"
          @scrollTo="scrollTo"
          @selectData="getSelectData"
          class="table-content"
        >
          <vxe-table
            ref="vexTable"
            class="table-scrollbar table-edit-common trends-table-column"
            keep-source
            emptyText=" "
            id="sequenceNbr"
            :column-config="{ resizable: true }"
            :mouse-config="{ selected: true }"
            :scroll-y="{ enabled: true, gt: 0 }"
            height="auto"
            :data="renderedList"
            @cell-click="
              cellData => {
                useCellClickEvent(cellData, tableCellClickEvent, [
                  'unit',
                  'fxCode',
                  'costMajorName',
                  'measureType',
                  'itemCategory',
                ]);
              }
            "
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              beforeEditMethod: cellBeforeEditMethod,
            }"
            :tooltip-config="{
              showAll: false,
              trigger: 'cell',
              enterable: true,
            }"
            :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
            @keydown="tableKeydown"
            :keyboard-config="{
              isArrow: true,
              isDel: true,
              isEnter: true,
              isTab: true,
              isChecked: true,
            }"
            @current-change="currentChangeEvent"
            @edit-closed="editClosedEvent"
            @cell-mouseenter="cellMouseEnterEvent"
            @cell-mouseleave="cellMouseLeaveEvent"
            :menu-config="menuConfig"
            @menu-click="contextMenuClickEvent"
            :row-class-name="rowClassName"
            :cell-class-name="cellClassName"
            :cell-style="cellStyle"
            :custom-config="{ storage: true }"
            :header-cell-class-name="setHeaderCellClassName"
          >
            <vxe-column fixed="left" field="" width="50" title="">
              <template #default="{ row }">
                <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
                  shChangeLabel(row.ysshSysj?.change).label
                }}</span>
              </template>
            </vxe-column>
            <vxe-column fixed="left" field="index" width="35" align="center">
              <template #default="{ row }">
                <div class="multiple-select" @click="clickIndex(row)">
                  {{ row.index }}
                </div>
              </template>
            </vxe-column>
            <template v-for="columns of tableColumns">
              <template v-if="columns.colgroup">
                <vxe-colgroup :title="columns.title">
                  <vxe-column
                    v-for="groupColumn of columns.colgroup"
                    v-bind="groupColumn"
                  >
                    <template
                      v-if="groupColumn.slot"
                      #default="{ column, row, $columnIndex }"
                    >
                      <span>{{ row[groupColumn.field] }}</span>
                    </template>
                    <template
                      v-if="groupColumn.slot"
                      #edit="{ column, row, $columnIndex }"
                    >
                      <!-- 工程量 -->
                      <template v-if="groupColumn.field == 'quantity'">
                        <vxe-input
                          v-if="row.kind === '03' || row.kind === '04'"
                          v-model="row.quantity"
                        />
                      </template>

                      <!-- 项目特征 -->
                      <template v-else-if="columns.field == 'projectAttr'">
                        <icon-font
                          type="icon-bianji"
                          class="more-icon"
                          v-if="
                            row.kind === '03' &&
                            isSelectedCell({
                              $columnIndex,
                              column,
                              row,
                            })
                          "
                          @click.stop="openEditDialog('projectAttr')"
                        ></icon-font>
                        <a-tooltip placement="topLeft">
                          <template #title>{{ row.projectAttr }}</template>
                          <span class="cell-line-break-el">{{
                            row.projectAttr
                          }}</span>
                        </a-tooltip>
                      </template>
                    </template>
                  </vxe-column>
                </vxe-colgroup>
              </template>
              <template v-else>
                <vxe-column v-bind="columns">
                  <template
                    #header="{
                      column,
                      columnIndex,
                      $columnIndex,
                      _columnIndex,
                      $rowIndex,
                    }"
                  >
                    <span class="custom-header">
                      <span>{{ column.title }}</span>
                      <!-- <CloseOutlined
                    class="icon-close"
                    @click="closeColumn({ column })"
                  /> -->
                    </span>
                  </template>
                  <template
                    v-if="columns.slot"
                    #default="{ column, row, $columnIndex }"
                  >
                    <div
                      class="cell-line-break-el"
                      v-if="columns.field == 'fxCode'"
                    >
                      <icon-font
                        v-if="row.isLocked"
                        type="icon-qingdan-suoding"
                      ></icon-font>
                      <i
                        @click="changeStatus(row)"
                        v-if="row.displaySign === 1"
                        class="vxe-icon-caret-down"
                      ></i>
                      <i
                        @click="changeStatus(row)"
                        v-if="row.displaySign === 2"
                        class="vxe-icon-caret-right"
                      ></i>
                      <span>
                        <a-tooltip>
                          <template #title
                            >{{
                              isSheHeDelete(row) && !row.fxCode
                                ? row.ysshSysj?.fxCode
                                : row.fxCode
                            }}
                            {{
                              row.redArray?.length > 0
                                ? row.redArray.join(',')
                                : ''
                            }}</template
                          >
                          {{
                            isSheHeDelete(row) && !row.fxCode
                              ? row.ysshSysj?.fxCode
                              : row.fxCode
                          }}
                          {{
                            row.redArray?.length > 0
                              ? row.redArray.join(',')
                              : ''
                          }}
                        </a-tooltip> </span
                      ><span
                        class="code-black"
                        v-if="row.blackArray?.length > 0"
                        >{{ row.blackArray.join(',') }}</span
                      >
                    </div>

                    <template v-else-if="columns.field == 'name'">
                      <Annotations
                        @close="v => closeAnnotations(v, row)"
                        @onfocusNode="onFocusNode(row)"
                        v-if="
                          row?.noteViewVisible ||
                          row?.isShowAnnotations ||
                          row?.noteEditVisible
                        "
                        :note="row.annotations"
                        :isDisabled="
                          row?.noteEditVisible || row?.isShowAnnotations
                        "
                        :ref="el => getAnnotationsRef(el, row)"
                      ></Annotations>
                      <icon-font
                        type="icon-bianji"
                        class="more-icon"
                        v-if="
                          isSelectedCell({
                            $columnIndex,
                            column,
                            row,
                          })
                        "
                        @click.stop="openEditDialog('name')"
                      ></icon-font>
                      <div v-maxLineNumber="row" class="content-tip-box">
                        {{
                          isSheHeDelete(row) && !row.name
                            ? row.ysshSysj?.name
                            : row.name
                        }}
                      </div>
                      <shtooltip :row="row" contrast="name" />
                    </template>
                    <template v-else-if="columns.field == 'qfCode'">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ row.qfName }}</template>
                        <span class="cell-line-break-el">{{ row.qfName }}</span>
                      </a-tooltip>
                    </template>
                    <div v-else-if="columns.field == 'type'">
                      <span
                        v-if="
                          row.matchStatus === '1' &&
                          projectStore.combinedVisible
                        "
                        class="flag-green"
                        >精</span
                      >
                      <span
                        v-if="
                          row.matchStatus === '2' &&
                          projectStore.combinedVisible
                        "
                        class="flag-orange"
                        >近</span
                      >
                      <span
                        v-if="
                          row.matchStatus === '0' &&
                          projectStore.combinedVisible
                        "
                        class="flag-red"
                        >未</span
                      >
                      <span
                        v-if="
                          (!row.borrowFlag && !row.changeFlag) ||
                          row.type === '费'
                        "
                        >{{
                          isSheHeDelete(row) ? row.ysshSysj?.type : row.type
                        }}</span
                      ><span class="code-flag" v-if="row.type !== '费'"
                        >{{ row.changeFlag ? row.changeFlag : '' }}
                      </span>
                      <span
                        class="code-flag"
                        v-if="row.type !== '费' && !row.changeFlag"
                        >{{ row.borrowFlag ? row.borrowFlag : '' }}
                      </span>
                    </div>

                    <!-- coldResistantSuborder防寒子目 -->
                    <template
                      v-else-if="columns.field === 'coldResistantSuborder'"
                    >
                      <a-checkbox
                        v-if="row.kind === '04' && projectStore.deType === '12'"
                        v-model:checked="row.coldResistantSuborder"
                        @change="itemUpdate(row, 'coldResistantSuborder')"
                      ></a-checkbox>
                    </template>
                    <!-- 工程量表达式 -->
                    <!-- <template v-else-if="columns.field == 'quantityExpression'">
                      <span>{{ row.quantityExpression }}</span>
                    </template> -->
                    <template
                      v-else-if="columns.field == 'ysshSysj.changeExplain'"
                    >
                      {{ row.ysshSysj?.changeExplain }}
                    </template>
                    <template v-else-if="columns.field == 'unit'">
                      {{ isSheHeDelete(row) ? row.ysshSysj?.unit : row.unit }}
                      <shtooltip :row="row" contrast="unit" />
                    </template>
                    <template v-else-if="column.field == 'quantityExpression'">
                      <icon-font
                        type="icon-bianji"
                        class="more-icon"
                        v-if="
                          (row.kind === '03' || row.kind === '04') &&
                          isSelectedCell({
                            $columnIndex,
                            column,
                            row,
                          })
                        "
                        @click="openEditDialog('quantityExpression')"
                      ></icon-font>
                      <span>{{
                        !row.fxCode && row.kind === '04'
                          ? 0
                          : row.quantityExpression
                      }}</span>
                    </template>
                    <template v-else-if="columns.field == 'zjfPrice'">
                      <span>{{ row.kind === '03' ? '' : row.zjfPrice }}</span>
                    </template>
                    <template v-else-if="columns.field == 'zjfTotal'">
                      <span>{{ row.kind === '04' ? row.zjfTotal : '' }}</span>
                    </template>
                    <template
                      v-else-if="
                        columns.field == 'ifMainQd' &&
                        !['04'].includes(row.kind)
                      "
                    >
                      <a-checkbox
                        v-model:checked="row.ifMainQd"
                        @change="itemUpdate(row, 'ifMainQd')"
                      ></a-checkbox>
                    </template>
                    <template
                      v-else-if="
                        ['description', 'costMajorName'].includes(column.field)
                      "
                    >
                      <a-tooltip placement="topLeft">
                        <template #title>{{ row[columns.field] }}</template>
                        <span class="cell-line-break-el">{{
                          row[columns.field]
                        }}</span>
                      </a-tooltip>
                    </template>
                    <template v-else-if="columns.field == 'measureType'">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ row[columns.field] }}</template>
                        <span class="cell-line-break-el">{{
                          ![2, 5].includes(Number(row.isCostDe))
                            ? row[columns.field]
                            : ''
                        }}</span>
                      </a-tooltip>
                    </template>
                    <!-- 备注 施工组织措施类别 取费文件 单价 工程量 单位-->
                    <template v-else>
                      <span>{{ row[columns.field] }}</span>
                    </template>
                  </template>

                  <template
                    v-if="columns.slot"
                    #edit="{ column, row, $columnIndex }"
                  >
                    <template v-if="columns.field == 'name'">
                      <Annotations
                        @close="v => closeAnnotations(v, row)"
                        @onfocusNode="onFocusNode(row)"
                        v-if="
                          row?.noteViewVisible ||
                          row?.isShowAnnotations ||
                          row?.noteEditVisible
                        "
                        :note="row.annotations"
                        :isDisabled="
                          row?.noteEditVisible || row?.isShowAnnotations
                        "
                        :ref="el => getAnnotationsRef(el, row)"
                      ></Annotations>

                      <vxe-pulldown
                        ref="bdNamePulldownRef"
                        transfer
                        v-if="['03'].includes(row.kind)"
                      >
                        <template #default>
                          <vxe-textarea
                            v-model="row.name"
                            rows="1"
                            className="custom-input"
                            placeholder="请输入项目名称"
                            @compositionstart="onCompositionStart(row)"
                            @compositionend="onCompositionEnd(row)"
                            @change="bdNameKeyupEvent(row, $event)"
                          >
                          </vxe-textarea>
                        </template>
                        <template #dropdown>
                          <div class="my-dropdown4">
                            <vxe-grid
                              border
                              auto-resize
                              :show-header="false"
                              height="auto"
                              width="500"
                              :row-config="{ isHover: true }"
                              :data="bdNameTableList"
                              :columns="tableColumn"
                              @cell-click="dbNameCellClickEvent"
                            >
                            </vxe-grid>
                          </div>
                        </template>
                      </vxe-pulldown>

                      <cell-textarea
                        :maxlength="2000"
                        v-model="row.name"
                        :textHeight="row.height"
                        v-else-if="row.constructionMeasureType !== 2"
                      ></cell-textarea>
                      <span v-else>{{ row.name }}</span>
                    </template>
                    <template v-else-if="columns.field == 'fxCode'">
                      <vxe-pulldown
                        ref="pulldownRef"
                        transfer
                        v-if="row.kind === '03' || row.kind === '04'"
                      >
                        <template #default>
                          <vxe-input
                            v-model="row.fxCode"
                            placeholder="请输入项目编码"
                            spellcheck="false"
                            @keyup="keyupEvent(row, $event)"
                          ></vxe-input>
                        </template>
                        <template #dropdown>
                          <div class="my-dropdown4" v-if="row.kind === '03'">
                            <vxe-grid
                              border
                              auto-resize
                              :show-header="false"
                              height="200"
                              width="500"
                              :row-config="{ isHover: true }"
                              :loading="loading"
                              :data="tableList"
                              :columns="tableColumn"
                              @cell-click="cellClickEvent"
                            >
                            </vxe-grid>
                          </div>
                        </template>
                      </vxe-pulldown>
                      <template v-else
                        ><i
                          @click="changeStatus(row)"
                          v-if="row.displaySign === 1"
                          class="vxe-icon-caret-down"
                        ></i>
                        <i
                          @click="changeStatus(row)"
                          v-if="row.displaySign === 2"
                          class="vxe-icon-caret-right"
                        ></i>
                        {{ row.fxCode }}</template
                      >
                    </template>

                    <!-- 项目特征 -->
                    <template v-else-if="columns.field == 'projectAttr'">
                      <cell-textarea
                        v-if="row.kind === '03'"
                        :maxlength="2000"
                        v-model="row.projectAttr"
                        :clearable="false"
                        :textHeight="'28'"
                        @focus="projectAttrFocus(row)"
                        @change="projectAttrChange(row, $event)"
                      ></cell-textarea>
                      <icon-font
                        type="icon-bianji"
                        class="more-icon"
                        @click.stop="openEditDialog('projectAttr')"
                      ></icon-font>
                      <div
                        class="association-selected"
                        v-if="row.kind === '03' && projectStore.deType === '12'"
                      >
                        <project-attr-association
                          ref="associationRef"
                          @dblclickHandler="
                            data => associationDblClick(data, row)
                          "
                          v-if="associationVisible"
                        />
                      </div>
                    </template>

                    <!-- 单位 -->
                    <template v-else-if="columns.field == 'unit'">
                      <vxe-select
                        v-if="row.kind === '03' && row.fxCode"
                        v-model="row.unit"
                        transfer
                      >
                        <vxe-option
                          v-for="item in row.unitList?.split('/')"
                          :key="item"
                          :value="item"
                          :label="item"
                        ></vxe-option>
                      </vxe-select>
                      <vxeTableEditSelect
                        v-if="row.kind === '04' && row.fxCode"
                        :filedValue="row.unit"
                        :list="projectStore.unitListString"
                        :transfer="true"
                        @update:filedValue="
                          newValue => {
                            saveCustomInput(newValue, row, 'unit', $rowIndex);
                          }
                        "
                      ></vxeTableEditSelect>
                    </template>

                    <!-- 工程量表达式 -->
                    <template v-else-if="columns.field == 'quantityExpression'">
                      <vxe-input
                        v-if="row.kind === '03' || row.kind === '04'"
                        v-model="row.quantityExpression"
                        :clearable="false"
                      >
                      </vxe-input>
                    </template>
                    <!-- 工程量 -->
                    <template v-else-if="columns.field == 'quantity'">
                      <vxe-input
                        v-if="
                          (row.kind === '03' || row.kind === '04') &&
                          row.isCostDe !== 1
                        "
                        v-model="row.quantity"
                      />
                      <span v-else>{{ row.quantity }}</span>
                    </template>

                    <!-- 单价 -->
                    <template v-else-if="columns.field == 'zjfPrice'">
                      <vxe-input
                        v-if="
                          row.kind === '04' &&
                          !ishasRCJList &&
                          row.showZjfPrice != '0' &&
                          isNotCostDe
                        "
                        v-model.trim="row.zjfPrice"
                        :maxlength="10"
                        @blur="row.zjfPrice = pureNumber(row.zjfPrice, 2)"
                      />
                      <span v-else>{{
                        row.kind === '03' ? '' : row.zjfPrice
                      }}</span>
                    </template>
                    <!-- 合价 -->
                    <template v-else-if="columns.field == 'zjfTotal'">
                      <span>{{ row.kind === '04' ? row.zjfTotal : '' }}</span>
                    </template>
                    <!-- 单价构成文件 -->
                    <template v-else-if="columns.field == 'qfCode'">
                      <vxe-select
                        v-if="
                          row.kind === '04' && row.fxCode && !row.zjcsClassCode
                        "
                        v-model="row.qfCode"
                        transfer
                      >
                        <vxe-option
                          v-for="item in djgcFileList"
                          :key="item.qfCode"
                          :value="item.qfCode"
                          :label="item.qfName"
                        ></vxe-option>
                      </vxe-select>
                      <span v-else class="cell-line-break-el">
                        {{ row.qfName }}
                      </span>
                    </template>
                    <template
                      v-else-if="columns.field == 'ysshSysj.changeExplain'"
                    >
                      <vxe-input v-model="row.ysshSysj.changeExplain">
                      </vxe-input>
                    </template>
                    <!-- 取费文件 -->
                    <template v-else-if="columns.field == 'costMajorName'">
                      <vxe-select
                        v-model="row.costMajorName"
                        v-if="
                          row.kind === '04' && row.fxCode && !row.zjcsClassCode
                        "
                        transfer
                      >
                        <vxe-option
                          v-for="item in feeFileList"
                          :key="item.qfCode"
                          :value="item.qfName"
                          :label="item.qfName"
                        ></vxe-option>
                      </vxe-select>
                      <span v-else class="cell-line-break-el">{{
                        row.costMajorName
                      }}</span>
                    </template>

                    <!-- 施工组织措施类别 -->
                    <template v-else-if="columns.field == 'measureType'">
                      <vxe-select
                        v-model="row.measureType"
                        v-if="
                          row.kind === '04' &&
                          row.fxCode &&
                          !row.zjcsClassCode &&
                          ![2, 5].includes(Number(row.isCostDe))
                        "
                        transfer
                      >
                        <vxe-option
                          v-for="item in szTypeList"
                          :key="item.qfCode"
                          :value="item.cslbName"
                          :label="item.cslbName"
                        ></vxe-option>
                      </vxe-select>
                      <span v-else class="cell-line-break-el">
                        {{
                          ![2, 5].includes(Number(row.isCostDe))
                            ? row.measureType
                            : ''
                        }}
                      </span>
                    </template>
                    <template v-else-if="columns.field == 'itemCategory'">
                      <vxe-select
                        v-if="
                          row.kind === '01' && row.constructionMeasureType !== 2
                        "
                        v-model="row.itemCategory"
                        transfer
                      >
                        <vxe-option
                          v-for="(item, index) in measureList"
                          :key="index"
                          :value="item"
                          :label="item"
                        ></vxe-option>
                      </vxe-select>
                      <span v-else>{{ row.itemCategory }}</span>
                    </template>
                    <!-- 备注 -->
                    <template v-else-if="columns.field == 'description'">
                      <cell-textarea
                        v-model="row.description"
                        :textHeight="row.height"
                      ></cell-textarea>
                    </template>

                    <!-- 备注 -->
                    <template v-else>
                      <vxe-input v-model="row[columns.field]" />
                    </template>
                  </template>
                </vxe-column>
              </template>
            </template>
          </vxe-table>
        </frameSelect>
      </template>
      <template #two>
        <div class="quota-content">
          <quota-info
            :currentInfo="currentInfo"
            :isAttrContent="isAttrContent"
            :type="2"
            :fatherLoading="tableLoading"
            :isUpdateFile="isUpdateFile"
            :isUpdateQuantities="isUpdateQuantities"
            @refreshCurrentInfo="refreshCurrentInfo"
            @tabClickBefore="showInfo"
            @onDbClickFile="onDbClickFile"
            :isComplete="isComplete"
            :isUpdate="isUpdate"
            ref="quotaInfoRef"
          ></quota-info>
        </div>
      </template>
    </split>
    <inventory-and-quota-index
      v-model:indexVisible="indexVisible"
      @currentQdDeInfo="currentQdDeInfo"
      @currentInfoReplace="currentInfoReplace"
      :dataType="dataType"
      :indexLoading="indexLoading"
      :originInfo="currentInfo"
    ></inventory-and-quota-index>
    <common-modal
      v-model:modelValue="deleteVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px"
    >
      <div class="content" v-if="deleteList.length === 2">请选择删除范围？</div>
      <div class="content" v-if="deleteList[0] === 5">
        是否确定删除？将删除{{
          currentInfo.kind === '01' ? '标题' : '清单'
        }}及其下挂所有数据.
      </div>
      <div
        class="content"
        v-if="deleteList.length === 1 && deleteList[0] === 4"
      >
        是否确定删除？
      </div>
      <div class="footer-btn-list">
        <a-button
          v-if="deleteList.length === 2"
          @click="delFbData(false)"
          :disabled="deleteLoading"
          >删除当前行</a-button
        >
        <a-button
          v-if="deleteList.length === 2"
          type="primary"
          @click="delFbData(true)"
          :disabled="deleteLoading"
          >删除关联数据</a-button
        >
        <div class="content" v-if="isBatchDelete">
          是否确定删除选中所有数据？
        </div>
        <a-button
          v-if="deleteList.length === 1 || isBatchDelete"
          type="primary"
          ghost
          @click="cancel"
          >取消</a-button
        >
        <a-button
          v-if="deleteList.length === 1 || isBatchDelete"
          type="primary"
          @click="delFbData(currentInfo.kind !== '04')"
          :disabled="deleteLoading"
          >确定</a-button
        >
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="isPriceModel"
      className="dialog-comm"
      :title="showPriceTitle"
      :width="
        showModelType === 'csfy'
          ? '1000'
          : showModelType === 'azfy'
            ? 1200
            : 800
      "
      height="auto"
      @close="closePriceModel"
    >
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          @updateData="updateData"
          @close="closePriceModel"
        ></component>
      </keep-alive>
      <!-- <zscy-content
        v-if="showModelType === 'zscy'"
        @updateData="updateData"
      ></zscy-content>
      <zscg-content v-if="showModelType === 'zscg'"></zscg-content>
      <azfy-content v-if="showModelType === 'azfy'"></azfy-content>
      <csfy-content
        v-if="showModelType === 'csfy'"
        @updateData="updateData"
      ></csfy-content> -->
    </common-modal>
    <common-modal
      v-model:modelValue="isShowModel"
      className="dialog-comm"
      :title="showModelTitle"
      width="700"
      height="350"
    >
      <a-textarea
        v-model:value="editContent"
        placeholder="请输入编辑内容"
        :rows="8"
        class="edit-content"
      />
      <div class="footer-btn-list">
        <a-button @click="editCancel">取消</a-button>
        <a-button type="primary" @click="saveContent()">确定</a-button>
      </div>
    </common-modal>
    <info-modal
      v-model:infoVisible="infoVisible"
      :infoText="infoText"
      :isSureModal="isSureModal"
      :iconType="iconType"
      @updateCurrentInfo="updateCurrentInfo"
    ></info-modal>
    <bcQd
      v-model:qdVisible="qdVisible"
      :code="bdCode"
      :type="2"
      :currentInfo="currentInfo"
      @saveData="saveData"
      @bcCancel="bcCancel"
    ></bcQd>
    <bcDe
      v-model:visible="deVisible"
      :code="bdCode"
      :type="2"
      :currentInfo="currentInfo"
      @deSaveData="deSaveData"
      @bcCancel="bcCancel"
    ></bcDe>
    <bcRcj
      :code="bdCode"
      v-model:visible="rcjVisible"
      @rcjSaveData="rcjSaveData"
      @bcCancel="bcCancel"
    ></bcRcj>

    <div class="unit-radio" v-if="showUnitTooltip">
      <div class="title">选择单位</div>
      <a-radio-group v-model:value="selectUnit">
        <a-radio
          v-for="(unit, index) in addCurrentInfo?.unit"
          :key="index"
          :value="unit"
        >
          {{ unit }}
        </a-radio>
      </a-radio-group>
      <a-button type="link" @click="selectHandler(addCurrentInfo)"
        >确定</a-button
      >
    </div>
    <set-standard-type
      v-model:standardVisible="standardVisible"
      :type="2"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      @refreshCurrentInfo="refreshCurrentInfo"
    ></set-standard-type>
    <set-main-material
      v-model:materialVisible="materialVisible"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      :mainMaterialTableData="mainMaterialTableData"
      @setUpdate="setUpdate"
    >
    </set-main-material>
    <common-modal
      v-model:modelValue="comMatchModal"
      className="dialog-comm"
      title="组价方案匹配"
      width="600"
      height="400"
    >
      <component-matching @closeComMatch="closeComMatch"></component-matching>
    </common-modal>
    <schedule-file
      v-model:dialogVisible="showSchedule"
      strokeColor="#54a1f3"
      :percent="percent"
      :desc="percentInfo?.dec"
      :pageType="'comMatch'"
      @isContinue="isContinue"
      :isNoClose="isNoClose"
      :percentInfo="percentInfo"
      :width="600"
    ></schedule-file>
    <common-modal
      className="titleNoColor noHeaderHasclose"
      v-model:modelValue="resetModal"
      title=" "
      width="400"
      height="200"
    >
      <div class="reCheck">
        <p style="font-weight: 600">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />组价进行中，是否确定关闭？
        </p>
        <p style="padding-left: 20px; margin-bottom: 26px">
          当前数据已发生变化是否应用组价后数据
        </p>

        <a-button style="margin: 0 30px 0 20px" @click="recover(true)"
          >否，恢复至组价前数据
        </a-button>
        <a-button type="primary" @click="recover(false)"> 确定</a-button>
      </div>
    </common-modal>
    <common-modal
      className="dialog-comm"
      title="组价方案匹配"
      width="550"
      height="400
    "
      v-model:modelValue="reportModel"
      @cancel="reportModel = false"
      @close="reportModel = false"
    >
      <match-pic
        @lookView="lookView"
        :startMatchData="startMatchData"
      ></match-pic>
    </common-modal>
    <!--    <combined-search-->
    <!--      @filterData="filterData"-->
    <!--      :id="'combined-search'"-->
    <!--    ></combined-search>-->
  </div>

  <!-- 复用组价 -->
  <ReuseGroupPriceDialog
    ref="ReuseGroupPriceRef"
    @refresh="queryBranchDataById('Refresh', '', false)"
    type="csxm"
    :currentInfo="currentInfo"
    :lockBtnStatus="lockBtnStatus"
  ></ReuseGroupPriceDialog>

  <!-- 清单快速组价 -->
  <qdQuickPricing
    ref="qdQuickPricingRef"
    @refresh="queryBranchDataById('Refresh', '', false)"
    @posRow="posRow"
    :currentInfo="currentInfo"
    :lockBtnStatus="lockBtnStatus"
  ></qdQuickPricing>

  <batch-delete
    v-model:batchDeleteVisible="batchDeleteVisible"
    :batchDataType="batchDataType"
    @updateData="queryBranchDataById"
  ></batch-delete>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />

  <areaModal
    v-if="areaStatus"
    :type="areaVisibleType"
    @closeDialog="closeAreaModal"
  ></areaModal>
  <compareMatch
    v-model:visible="compareMatchVisible"
    :sequenceNbr="projectStore.currentTreeGroupInfo?.constructId"
    :original="3"
    @updateData="queryBranchDataById"
  >
  </compareMatch>
</template>

<script setup>
import {
  onActivated,
  reactive,
  inject,
  ref,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
  markRaw,
  onDeactivated,
  defineAsyncComponent,
} from 'vue';
import QuotaInfo from '@/yuSuanShenHe/views/projectDetail/customize/quotaInfo/index.vue';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import {
  quantityExpressionHandler,
  pureNumber,
  everyNumericHandler,
  shChangeLabel,
} from '@/utils/index';
import InventoryAndQuotaIndex from '@/views/projectDetail/customize/inventoryAndQuotaIndex/index.vue';
import api from '@/api/projectDetail.js';
import shApi from '@/api/shApi.js';
import { checkisOnline } from '@/utils/publicInterface';
import ProjectAttrAssociation from '@/components/ProjectAttrAssociation/ProjectAttrAssociation.vue';
import ComponentMatching from '@/views/projectDetail/customize/measuresItem/componentMatching.vue';
import MatchPic from '@/views/projectDetail/customize/measuresItem/MatchPic.vue';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import SetStandardType from '@/views/projectDetail/customize/quotaInfo/setStandardType.vue';
import SetMainMaterial from '@/views/projectDetail/customize/quotaInfo/setMainMaterial.vue';
import { useCheckBefore } from '@/hooks/useCheckBefore';
const { checkUnit, showInfo, isComplete } = useCheckBefore();
import { useCellClick } from '@/hooks/useCellClick';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { useAttrAssociation } from '@/hooks/useAttrAssociation.js';
import { useSubItem } from '@/hooks/useSubItem.js';
import { useRoute } from 'vue-router';

import qdQuickPricing from '@/components/qdQuickPricing/index.vue';
import bdNameSelect from '@/components/bdNameSelect/index.vue';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';
import { setGlobalLoading } from '@/hooks/publicApiData';
import shtooltip from '../shComponents/shtooltip.vue';

const vLineBreak = el => {
  function unitTransform(unit) {
    return Number(unit.slice(0, -2));
  }
  function setLineClamp() {
    const { height, lineHeight, paddingTop } = window.getComputedStyle(
      el.closest('td'),
      null
    );
    const line = Math.floor(
      (unitTransform(height) - unitTransform(paddingTop) * 2) /
        unitTransform(lineHeight)
    );
    el.style['-webkit-line-clamp'] = line;
  }
  setLineClamp();
};
const route = useRoute();

const { dataSearchPosition } = useReversePosition();
const ReuseGroupPriceDialog = defineAsyncComponent(
  () => import('@/components/ReuseGroupPriceDialog/index.vue')
);
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
  resetCellData,
} = useCellClick();

const components = markRaw(new Map());
components.set(
  'zscy',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/zscyContent.vue')
  )
);
components.set(
  'zscg',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/zscgContent.vue')
  )
);
components.set(
  'csfy',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/csfyContent.vue')
  )
);
components.set(
  'azfy',
  defineAsyncComponent(
    () => import('@/views/projectDetail/customize/measuresItem/azfyContent.vue')
  )
);
import frameSelect from '@/components/frameSelect/index.vue';
import zscyContent from '@/views/projectDetail/customize/measuresItem/zscyContent.vue';
import zscgContent from '@/views/projectDetail/customize/measuresItem/zscgContent.vue';
import csfyContent from '@/views/projectDetail/customize/measuresItem/csfyContent.vue';

import { insetBus } from '@/hooks/insetBus';
import operateList from '@/views/projectDetail/customize/operate';
import split from '@/components/split/index.vue';
import { commonSheHe } from '@ysshDetail/views/projectDetail/customize/subItemProject/commonSheHe.js';

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

const emits = defineEmits(['getCurrentInfo', 'updateMenuList']);

const quotaInfoRef = ref();
let frameSelectRef = ref();
let vexTable = ref();
const projectStore = projectDetailStore();
let contextmenuList = ref([]);
let szTypeList = ref([]);
let measureList = ref([]);
let dataType = ref('03');
let isAttrContent = ref(false);
let unitList = ref([]);
let deleteVisible = ref(false);
let compareMatchVisible = ref(false);
let menuList = ref([
  {
    type: 0,
    name: '添加标题',
    kind: '01',
    isValid: false,
  },
  {
    type: 1,
    name: '添加子项',
    kind: '02',
    isValid: false,
  },
  {
    type: 2,
    name: '添加清单',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '添加定额',
    kind: '04',
    isValid: false,
  },
]);
let bcMenuList = ref([
  {
    type: 2,
    name: '补充清单',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '补充定额',
    kind: '04',
    isValid: false,
  },
  {
    type: 3,
    name: '补充人材机',
    kind: '05',
    isValid: false,
  },
]);
let deleteList = ref([]);
let page = ref(1);
let limit = ref(300000);
let scrollSwitch = ref(false);
let loading = ref(false);
let deleteLoading = ref(false);
let showModelType = ref('');
let isPriceModel = ref(false);
let showPriceTitle = ref('');
let isEditEnabled = ref(true); // 是否可编辑

const pulldownRef = ref(); // 编码推荐数据ref
let indexLoading = ref(false); // 索引页面loading

const bcQd = defineAsyncComponent(
  () =>
    import('@/views/projectDetail/customize/subItemProject/components/bcQd.vue')
);
const bcDe = defineAsyncComponent(
  () =>
    import('@/views/projectDetail/customize/subItemProject/components/bcDe.vue')
);
const bcRcj = defineAsyncComponent(
  () =>
    import(
      '@/views/projectDetail/customize/subItemProject/components/bcRcj.vue'
    )
);

let copyData = ref(null);
let isBatchDelete = ref(false); // 是否批量删除
let standardData = ref([]); // 定额数据下挂的标准换算数据,如若为空,则不展示设置标准换算弹框
// let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
let isUpdate = ref(false); // 修改主材市场价刷新人材机明细数据

const checkList = ref([]); // 组价方案匹配筛选选中值
watch(
  () => projectStore.currentTreeInfo,
  () => {
    if (
      projectStore.tabSelectName === '措施项目' &&
      projectStore.currentTreeInfo.levelType === 3
    ) {
      page.value = 1;
      initColumns({ columns: tableColumns.value, pageName: 'csxm' });
      if (!projectStore.isAutoPosition) {
        // 不是自动定位的才调用接口
        queryBranchDataById('other');
      }
      // nextTick(() => {
      //   initVirtual(vexTable.value);
      // });
    }
  }
);

watch(
  () => projectStore.positionId,
  () => {
    console.log('措施项目定位', projectStore.positionId);
    if (
      projectStore.tabSelectName === '措施项目' &&
      projectStore.positionId &&
      projectStore.currentTreeInfo.levelType === 3
    ) {
      // currentInfo.value = { sequenceNbr: projectStore.positionId };在queryBranchDataById会给currentInfo.value赋值，此处不赋值，不然会出现12518bug
      queryBranchDataById('other', projectStore.positionId);
    }
  }
);
watch(
  () => projectStore.combinedSearchList,
  () => {
    if (
      projectStore.tabSelectName === '措施项目' &&
      projectStore.combinedSearchList &&
      projectStore.currentTreeInfo.levelType === 3
    ) {
      filterData(projectStore.combinedSearchList);
    }
  }
);

//项目特征关联
let {
  associationVisible,
  associationRef,
  dblClickHandler,
  projectAttrChange,
  projectAttrFocus,
} = useAttrAssociation({ type: 'csxm' });

// 关联数据双击应用回调
const associationDblClick = (data, row) => {
  dblClickHandler({
    data,
    row,
    callback: () => {
      queryBranchDataById();
      setTimeout(() => {
        quotaInfoRef.value.manualTabChange('groupSchemeTable');
      }, 500);
    },
  });
};

//组价部分
const scheduleFile = defineAsyncComponent(
  () => import('@/components/schedule/schedule.vue')
);
const $ipc = cxt.appContext.config.globalProperties.$ipc;
let comMatchModal = ref(false); //组价方案弹框
let percentInfo = ref(); //进度条描述
let percent = ref(0); //进度条百分比
let resetModal = ref(false); //是否确认关闭进度条
let isNoClose = ref(false); //进度条关闭前执行函数
let showSchedule = ref(false);
let reportModel = ref(false); //组价饼图弹框
let startMatchData = ref();

let {
  updateQdByName,
  dbNameCellClickEvent,
  bdNameTableList,
  bdNamePulldownRef,
  showUnitTooltipType,
  bdNameKeyupEvent,
  onCompositionEnd,
  onCompositionStart,
  editClosedEvent,
  initVirtual,
  getScroll,
  renderedList,
  init,
  loading: tableLoading,
  EnterType,
  scrollToPosition,
  currentChangeEvent,
  mainMaterialTableData,
  updateFbData: itemUpdate,
  queryBranchDataById,
  queryFeeFileData,

  saveContent,
  openEditDialog,
  showModelTitle,

  currentInfo,
  isShowModel,
  editContent,
  editKey,
  infoVisible,
  infoText,
  iconType,
  isSureModal,

  ishasRCJList,
  isClearEdit,
  isSortQdCode,
  bdCode,
  rcjVisible,
  deVisible,
  qdVisible,

  isUpdateFile,
  indexVisible,
  isUpdateQuantities,
  selectUnit,
  showUnitTooltip,
  addCurrentInfo,
  isIndexAddInfo,
  addDataSequenceNbr,
  lockFlag,
  feeFileList,
  djgcFileList,
  tableData,
  originalTableData,
  materialVisible,
  DJGCrefreshFeeFile,
  updateDelTempStatusColl,
  batchDelByTypeOfColl,
  batchDeleteVisible,
  codeType,
  radioStyle,
  isOpenLockedStatus,
  batchDataType,
  selectData,
  addDeInfo,
  queryRcjDataByDeId,
  handleNote,
  handleNoteClick,
  areaStatus,
  areaVisibleType,
  handleMainList,
  handleMainListClick,
  closeAreaModal,
  closeAnnotations,
  getAnnotationsRef,
  cellMouseEnterEvent,
  cellMouseLeaveEvent,
  onFocusNode,
  standardVisible,
  queryRule,
  renderLine,
  isNotCostDe,
  deleteStateFn,
  needAddQDandFB,
  tableKeydown,
  setTableKeydownEnd,
} = useSubItem({
  operateList,
  frameSelectRef: frameSelectRef,
  resetCellData,
  checkUnit,
  vexTable,
  emits,
  codeField: 'fxCode',
  nameField: 'name',
  pageType: 'csxm',
  projectType: 'yssh',
  api: {
    updateData: api.itemUpdate,
    getList: shApi.csxmListSearch,
  },
});
watch(
  () => tableLoading.value,
  () => {
    setGlobalLoading(tableLoading.value, '加载中，请稍后');
  }
);
watch(
  () => lockFlag.value,
  () => {
    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
  }
);
const isContinue = type => {
  console.log('进度条点击时间', type);
  if (type === '关闭') {
    isNoClose.value = true;
    resetModal.value = true;
  } else if (type === '暂停') {
    console.log('继续', projectStore.currentTreeGroupInfo?.constructId);

    api
      .pauseMerge({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {});
  } else if (type === '继续') {
    console.log('继续', JSON.parse(JSON.stringify(startMatchData.value)));
    api
      .startMerge(JSON.parse(JSON.stringify(startMatchData.value)))
      .then(res => {});
  }
};
const closeComMatch = data => {
  //关闭组价方案匹配
  console.log('关闭closeComMatch');
  startMatchData.value = data;
  // comMatchModal.value = false;
  // reportModel.value = true; //饼图弹框
  startMatch(data);
};
const startMatch = async data => {
  isNoClose.value = true;
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  //初始化进度条
  percentInfo.value = {
    finish: 0,
    noFinish: 0,
    total: 0,
    dec: '组价方案匹配中，请稍后…',
  };
  percent.value = 0;
  console.log('startMerge', data);
  comMatchModal.value = false;
  // setTimeout(() => {
  showSchedule.value = true; //组价进度条开始

  $ipc.on(formData.constructId, (event, arg) => {
    // console.log('constructId', arg);
    if (arg.percent >= percent.value) {
      percentInfo.value = {
        finish: arg.succeed,
        noFinish: arg.notSuccess,
        total: arg.total,
        dec: arg.percent >= 100 ? '组价方案完成' : '组价方案匹配中，请稍后…',
      };
      percent.value = arg.percent;
    }
    if (arg.percent >= 100) {
      $ipc.removeAllListeners('formData.constructId'); //监听事件移除
      if (isopenReport.value) {
        console.log('ReuseGroupPriceRef.value', ReuseGroupPriceRef.value);
        isopenReport.value = false;
        closeSchedule();
      }
    }
  });
  let res = await api.startMerge(data).then();
  if (res.status === 500 && percent.value === 0) {
    setTimeout(() => {
      percentInfo.value = {
        finish: 0,
        noFinish: 0,
        total: 0,
        dec: '组价方案完成',
      };
      percent.value = 100;
      // console.log('startMerge返回', res, percent.value);
      closeSchedule();
    }, 1000);
  }
};
const closeSchedule = () => {
  isopenReport.value = false;
  setTimeout(() => {
    isNoClose.value = false;
    showSchedule.value = false; //组价进度条关闭
    if (resetModal.value) {
      resetModal.value = false;
    }
    $ipc.removeAllListeners('formData.constructId'); //监听事件移除
    if (projectStore.tabSelectName === '措施项目') {
      reportModel.value = true; //饼图弹框
      queryBranchDataById('Refresh');
    }
  }, 2000);
};
const recover = async bol => {
  //否，恢复至组价前数据
  //  bol--为true恢复
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  let getRes;
  if (bol) {
    getRes = await api.beforeRestoring(formData).then();
  } else {
    getRes = await api.determine(formData).then();
  }
  console.log('recover', bol, getRes);
  isNoClose.value = false;
  resetModal.value = false;
  showSchedule.value = false;
  queryBranchDataById('Refresh'); //点击是或否都更新数据
};
//扇形图点击查看功能
const lookView = data => {
  checkList.value = [];
  reportModel.value = false; //饼图弹框
  console.log('组价-扇形图点击查看部分', data, startMatchData.value);
  switch (data.name) {
    case '精准组价':
      checkList.value.push('1');
      break;
    case '近似组价':
      checkList.value.push('2');
      break;
    case '未匹配组价':
      checkList.value.push('0');
      break;
  }
  dataSearchPosition({
    treeId: startMatchData.value.selectedUnitIdList[0],
    tabMenuName: '分部分项',
    type: checkList.value,
  });
  // filterData(checkList.value)
};
onMounted(() => {
  projectStore.isOpenIndexModal = {
    open: false,
    tab: null,
  };
});
// onBeforeUnmount(() => {
//   window.removeEventListener('keydown', copyAndPaste);
// });
// const copyAndPastea = inject('copyAndPaste');

// const triggerCopyAndPaste = () => {
//   if (copyAndPastea) {
//     copyAndPastae(/* 传递需要的参数 */);
//   }
// };

let lockBtnStatus = ref(false);
onActivated(() => {
  initColumns({ columns: tableColumns.value, pageName: 'csxm' });
  lockBtnStatus.value = false;
  // initVirtual(vexTable.value);
  if (!projectStore.isAutoPosition) {
    // 不是自动定位的才调用接口
    queryBranchDataById('other');
  }
  queryFeeFileData();
  querySzType();
  getMeasureTypes();
  // queryUnit();
  bus.off('handleCopyEvent');
  bus.on('handleCopyEvent', ({ event, name }) => {
    if (name === 'measuresItem') copyAndPaste(event);
  });
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
  insetBus(bus, projectStore.componentId, 'measuresItem', async data => {
    if (data.name === 'insert-subItem') {
      if (!data.activeKind) {
        contextMenu();
      } else {
        addData(data.activeKind);
      }
    }
    if (data.name === 'supplement') {
      if (!data.activeKind) {
        bcContextMenu();
        console.log('执行补充');
      } else {
        bcData(data);
        console.log('执行补充子菜单');
      }
    }
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
    operateList.value.find(item => item.name === 'code-reset').disabled =
      lockFlag.value;
    if (data.name === 'delete-subItem') deleteType(null);
    if (data.name === 'code-reset') {
      batchRefresh();
    }
    if (data.name === 'vertical-transport') showModel('zscy');
    if (data.name === 'superelevation') showModel('zscg');
    if (data.name === 'installation-costs') showModel('azfy');
    if (data.name === 'calculation-measures') showModel('csfy');
    if (data.name === 'lock-subItem') allLock();
    if (data.name === 'component-matching') checkOnline(data);
    if (data.name === 'reuse-group-price') openReuseGroupPrice(data);
    if (data.name === 'qd-group-price') openQdQuickPricing(data);
    if (data.name === 'comparative-match') compareMatchVisible.value = true;
  });
});

onDeactivated(() => {
  lockBtnStatus.value = true;
  qdQuickPricingRef.value?.cancel(false);
});
let isopenReport = ref(false);
const checkOnline = async data => {
  if (Object.prototype.hasOwnProperty.call(data, 'activeKind')) {
    if (data.activeKind === '01') {
      //点击组价方案匹配
      comMatchModal.value = true;
      isopenReport.value = true;
    } else if (data.activeKind === '02') {
      //点击组件筛选
      if (!projectStore.combinedVisible)
        projectStore.SET_COMBINED_VISIBLE(true);
    }
  } else {
    const hasOnline = await checkisOnline(true);
    hasOnline
      ? data.options.forEach(item => (item.isValid = true))
      : data.options.forEach(item => (item.isValid = false));
  }
};
const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = val => {
  // console.log('selectData',val)
  selectData.value = val;
};

/**
 *
 * @param {*} event
 * @param {*} isHandCopy 是否手动执行复制操作
 */
const copyAndPaste = (event, isHandCopy = false) => {
  console.log('event', event.code);
  console.log('选中的', selectData.value);
  console.log('当前的', currentInfo.value);
  if (['input', 'textarea'].includes(event.target.nodeName.toLowerCase()))
    return;
  // 如果选中数据为空，情景1，刚开始进入页面，2点击了input,然后点击空白处
  if (!selectData.value || !selectData.value?.data?.length) {
    frameSelectRef.value?.isBranchCopy([currentInfo.value?.sequenceNbr]);
  }

  if (isHandCopy) {
    copyFun();
  }

  // if (event.ctrlKey && event.code === 'KeyC') {
  //   copyFun();
  // }
  // if (event.ctrlKey && event.code === 'KeyV') {
  //   if (!vexTable.value.getSelectedCell()) return; //vexTable.value.getSelectedCell()如果当前表格不是选中，就不进行ctr+v
  //   pasteFun();
  // }
};

// 编码重刷
const batchRefresh = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '是否确认刷新数据？',
    confirm: () => {
      api.batchRefresh(apiData).then(res => {
        if (res.status === 200 && res.result) {
          page.value = 1;
          message.success('项目编码重刷成功');
          queryBranchDataById();
        }
      });
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};

const copyFun = () => {
  const $table = vexTable.value;
  // 判断行是否为激活编辑状态
  console.log(
    '$table.isUpdateByRow(currentInfo.value)',
    $table.isEditByRow(currentInfo.value)
  );
  if ($table.isEditByRow(currentInfo.value)) return;
  if (!selectData.value) {
    message.error('暂无选中数据');
  } else {
    if (selectData.value.isCopy) {
      console.log('111111111111');
      copyData.value = selectData.value;
      copyBranchDataQdDe();
    } else {
      message.error(selectData.value.msg);
    }
  }
};

const pasteFun = async () => {
  console.log('粘贴方法');
  const $table = vexTable.value;
  // 判断行是否为激活编辑状态
  console.log(
    '$table.isUpdateByRow(currentInfo.value)',
    $table.isEditByRow(currentInfo.value)
  );
  if ($table.isEditByRow(currentInfo.value)) return;
  let clipboardText;
  const clipPromise = navigator.clipboard.readText();
  await clipPromise.then(function (clipText) {
    //粘贴板粘贴的数据
    clipboardText = clipText;
    if (!copyData.value && clipboardText) {
      copyData.value = clipboardText;
    }
  });
  if (!copyData.value) {
    message.error('暂无复制数据');
  } else {
    if (
      projectStore.standardGroupOpenInfo.isOpen &&
      copyData.value.data.find(i => i.kind === '03')
    ) {
      message.error('标准组价不可粘贴包含清单行数据');
      return;
    }
    console.log(frameSelectRef.value.getRowCurrent());
    if (!frameSelectRef.value.getRowCurrent()) {
      return message.error('请选中需要粘贴行！');
    } else {
      let row = frameSelectRef.value.getRowCurrent();
      console.log('else方法', row);
      try {
        await frameSelectRef.value.frameSelectJs.isPasteBranch(
          row,
          copyData.value
        );
        console.log('粘贴数据到此页面：', copyData.value, currentInfo.value);
        batchPasteQdDeData();
        // frameSelectRef.value.clearSelect();
        // copyData.value = null;
        // selectData.value = null;
      } catch (error) {
        console.log('周这儿了', error);
        // message.error(error);
      }
    }
  }
};

// 批量粘贴数据
const batchPasteQdDeData = () => {
  console.log('这儿进来了不');
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  api.batchPasteQdDeData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('粘贴成功');
      queryBranchDataById();
    } else {
      message.error('粘贴失败');
    }
  });
};

// 定位方法
const posRow = (sequenceNbr, EnterTypes = 'other') => {
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  // currentInfo.value = { sequenceNbr };在queryBranchDataById会给currentInfo.value赋值，此处不赋值，不然会出现12518bug
  queryBranchDataById(EnterTypes, sequenceNbr);
};

const addType = () => {
  console.log('插入类型');
};

const deleteType = () => {
  console.log('删除类型');
  isBatchDelete.value = false;
  if (currentInfo.value.kind === '0') {
    message.warning('该行不可删除');
    return;
  }
  deleteList.value = [];
  if (selectData.value && selectData.value.data?.length > 1) {
    isBatchDelete.value = true;
  } else {
    currentInfo.value.optionMenu.forEach(item => {
      if (item === 4 || item === 5) {
        deleteList.value.push(item);
      }
    });
  }
  if (deleteList.value.length > 0 || isBatchDelete.value) {
    deleteVisible.value = true;
  }
  console.log('delete', deleteList.value);
};

const cancel = () => {
  deleteVisible.value = false;
};

const addData = (kind, formData) => {
  console.log('2222222222', kind);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: {
      kind: currentInfo.value.kind,
      sequenceNbr: currentInfo.value.sequenceNbr,
      parentId: currentInfo.value.parentId,
      displayStatu: currentInfo.value.displayStatu,
      displaySign: currentInfo.value.displaySign,
    },
    newLine: {
      kind: kind,
    },
  };
  api.itemSave(apiData).then(res => {
    console.log('============', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      message.success('插入成功');
      console.log('page.value', page.value);
      queryBranchDataById();
    }
  });
};

/**
 * 点击index事件 为什么从上面单独拿下来，因为收起分部，然后这时候多次点击，没有触发currentChangeEvent事件。所以拿不到当前行子级数据了就是空数据了
 * @param {*} row
 */
const clickIndex = row => {
  const $table = vexTable.value;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(currentInfo.value)) return;
  currentInfo.value = row;
  projectStore.SET_SUB_CURRENT_INFO(row);
  nextTick(() => {
    if (row.kind === '04') return;
    // 等选中的样式更新完，
    queryAllDataByBranchId();
  });
};

// 是否编辑处理
const setEditEnabled = row => {
  const { isCostDe, kind } = row;
  if (
    kind === '03' &&
    row.hasOwnProperty('zjcsClassCode') &&
    row.zjcsClassCode !== null &&
    row.zjcsClassCode !== undefined &&
    Number(row.zjcsClassCode) === 0
  ) {
    isEditEnabled.value = false;
    return;
  }
  if (kind === '04' && isCostDe === 1) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const onDbClickFile = v => {
  itemUpdate(
    {
      ...currentInfo.value,
      projectAttr: v.value,
    },
    'projectAttr'
  );
};

// 弹框提示点击确定按钮事件
const updateCurrentInfo = () => {
  itemUpdate(currentInfo.value, 'quantityExpression');
};

// 工程量明细更改后刷新当前行数据
const refreshCurrentInfo = () => {
  if (projectStore.tabSelectName !== '措施项目') return;
  addDataSequenceNbr.value = addCurrentInfo.value
    ? addCurrentInfo.value.sequenceNbr
    : currentInfo.value.sequenceNbr;
  queryBranchDataById();
};

/**
 * 设置主材市场价弹框操作更新
 * @param type 1 关闭弹框, 2 刷新数据
 */
const setUpdate = type => {
  materialVisible.value = false;
  if (type === 2) {
    isUpdate.value = true;
    setTimeout(() => {
      isUpdate.value = false;
    }, 100);
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
    queryBranchDataById();
  }
  //if (projectStore.globalSettingInfo.standardConversionShowFlag) {
  queryRule();
  //}
};

const changeStatus = row => {
  let index = tableData.value.findIndex(x => x.sequenceNbr === row.sequenceNbr);
  page.value = Math.ceil((index + 1) / limit.value);
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // itemUpdate(row, 'seq');
  console.log('row', row);
};
const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  console.log('打开节点参数', apiData);
  api.itemOpen(apiData).then(res => {
    console.log('打开节点', res);
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  console.log('关闭节点参数', apiData);
  api.itemClose(apiData).then(res => {
    console.log('关闭节点', res);
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};

const toggleMethod = ({ expanded, row }) => {
  console.log('操作树展开与关闭', row);
  if (expanded) {
    console.log('展开');
  } else {
    console.log('关闭');
  }
  // row.closeFlag = expanded
  // itemUpdate(row)
  return true;
};

const cellDBLClickEvent = ({ row, column }) => {
  // if (projectStore.standardGroupOpenInfo.isOpen) return;
  if (column.field === 'fxCode') {
    indexVisible.value = true;
    dataType.value = row.kind;
    console.log('清单定额双击', indexVisible.value, dataType.value);
  } else if (column.field === 'projectAttr' && !associationVisible.value) {
    isAttrContent.value = true;
    setTimeout(() => {
      isAttrContent.value = false;
    }, 100);
  }
  currentInfo.value = row;
};

// 表格单击事件
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  setTableKeydownEnd({ $table: vexTable.value });
  if (dbClickTime < 250) {
    cellDBLClickEvent({ row, column });
    // 前后点击相差250毫秒触发双击
  }
  const $table = vexTable.value;
  if ($event.ctrlKey) {
    const document1 = document.querySelector('.multiple-check');
    if (document1) {
      const firstInfo = $table.getRowNode(document1);
      $table.setCurrentRow(firstInfo.item);
      currentInfo.value = firstInfo.item;
      projectStore.SET_SUB_CURRENT_INFO(firstInfo.item);
    }
  }
  if (
    row.isLocked ||
    row.tempDeleteFlag ||
    (isSheHeDelete(row) && row.quantity !== 0)
  )
    return false;
  return true;
};

const currentQdDeInfo = row => {
  console.log('当前信息', row);
  // let apiData = {
  //   unit: row.unit,
  //   sequenceNbr: row.sequenceNbr,
  // };
  fillMeasureFromIndexPage(row);
  // console.log('1111111111', apiData.formData);
  // addData(row.deName ? '04' : '03', apiData);
};
const fillMeasureFromIndexPage = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    kind: row.kind,
    indexId: row.sequenceNbr,
    unit: row.unit,
    rcjFlag: row.rcjFlag,
    libraryCode: row.libraryCode,
    fbfxOrCsxm: 'csxm',
  };
  console.log('插入参数', apiData);
  let apiName = 'fillMeasureFromIndexPage';

  if (row.deArray?.length) {
    // 清单指引点击插入子目保存
    apiData.deArray = row.deArray;
    apiName = 'saveDeArray';
  }

  if (row.baseListModel?.sequenceNbr) {
    // 清单指引点击插入清单
    apiData.baseListModel = row.baseListModel;
    apiName = 'saveQdAndDeArray';
  }

  indexLoading.value = true;
  api[apiName](apiData)
    .then(res => {
      console.log('插入数据', res);
      if (res.status === 200 && res.result) {
        indexLoading.value = false;
        // if (
        //   currentInfo.value?.kind !== '03' &&
        //   currentInfo.value?.kind !== '04' &&
        //   row.kind === '03'
        // ) {
        // } else {
        isIndexAddInfo.value = true;
        // }

        if (apiName === 'saveDeArray') {
          // 插入子目
          addDataSequenceNbr.value = res.result[0].data.sequenceNbr;
          queryRcjDataByDeId();
        } else if (apiName === 'saveQdAndDeArray') {
          addDataSequenceNbr.value =
            res.result?.saveQdResult?.data?.sequenceNbr;
        } else {
          addDataSequenceNbr.value = res.result.data.sequenceNbr;
          page.value = Math.ceil((res.result.index + 1) / limit.value);
          if (row.kind === '04' && row.rcjFlag === 0) {
            addDeInfo.value = res.result.data;
            // standardVisible.value = true;
            queryRcjDataByDeId();
          }
        }

        message.success('插入成功');
        queryBranchDataById();
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

// 替换功能
const currentInfoReplace = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentInfo.value.sequenceNbr,
    type: 1,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    libraryCode: row?.libraryCode,
    fbfxOrCsxm: 'csxm',
    rootLineId: route.query.constructSequenceNbr,
  };
  indexLoading.value = true;

  let apiName = 'itemReplaceFromIndexPage';
  if (row?.qdzyReplace) {
    apiName = 'replaceQdAndSaveDeArray';
    apiData.pointLine = JSON.parse(JSON.stringify(currentInfo.value));
    apiData.deArray = row.deArray;
    apiData.baseListModel = row.baseListModel;
  }

  api[apiName](apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = res.result.sequenceNbr;
        indexLoading.value = false;

        if (apiData.deArray?.length) {
          queryRcjDataByDeId();
        } else if (row.kind === '04' && row.rcjFlag === 0) {
          addDeInfo.value = res.result;
          queryRcjDataByDeId();
        }
        queryBranchDataById();
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

const contextMenu = () => {
  let tempList = xeUtils.clone(menuList.value, true);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'insert-subItem').options =
    contextmenuList.value;
};

const bcContextMenu = () => {
  console.log('补充', currentInfo.value);
  let tempList = xeUtils.clone(bcMenuList.value, true);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'supplement').options = tempList;
};

const bcData = item => {
  bdCode.value = '';
  if (item.activeKind === '03') {
    qdVisible.value = true;
  } else if (item.activeKind === '04') {
    deVisible.value = true;
  } else {
    rcjVisible.value = true;
  }
};

const querySzType = () => {
  api
    .querySzType({ constructId: route.query.constructSequenceNbr })
    .then(res => {
      if (res.status === 200 && res.result) {
        szTypeList.value = res.result;
      }
    });
};

const getMeasureTypes = () => {
  api.getMeasureTypes().then(res => {
    if (res.status === 200 && res.result) {
      measureList.value = res.result;
    }
  });
};

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};

const delFbData = type => {
  if (deleteLoading.value) return;
  deleteLoading.value = true;
  if (isBatchDelete.value) {
    let index = tableData.value.findIndex(
      x => x.sequenceNbr === selectData.value.data[0].sequenceNbr
    );
    console.log('index', index, typeof index);
    page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
    delBatchData();
    return;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    isBlock: type,
    sequenceNbr: currentInfo.value.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  let index = tableData.value.findIndex(
    x => x.sequenceNbr === currentInfo.value.sequenceNbr
  );
  api
    .itemRemove(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');
        page.value = Math.ceil((index + 1) / limit.value);
        queryBranchDataById();
      } else {
        message.error(res.message);
        deleteVisible.value = false;
        deleteLoading.value = false;
        selectData.value.data = [];
      }
    })
    .catch(err => {
      message.error('删除失败');
      selectData.value.data = [];
      deleteLoading.value = false;
      deleteVisible.value = false;
    });
};

const menuConfig = reactive({
  className: 'my-menus-subItem',
  body: {
    options: [
      [
        {
          code: 'add',
          name: '插入',
          children: [
            {
              code: 0,
              name: '添加标题',
              kind: '01',
              visible: true,
              disabled: true,
            },
            {
              code: 1,
              name: '添加子项',
              kind: '02',
              visible: true,
              disabled: true,
            },
            {
              code: 2,
              name: '添加清单',
              kind: '03',
              visible: true,
              disabled: true,
            },
            {
              code: 3,
              name: '添加定额',
              kind: '04',
              visible: true,
              disabled: true,
            },
          ],
        },
        {
          code: 'copy',
          name: '复制',
          visible: true,
          disabled: false,
        },
        {
          code: 'paste',
          name: '粘贴',
          visible: true,
          disabled: false,
        },
        {
          code: 'delete',
          name: '删除',
          visible: true,
          disabled: true,
        },
        {
          code: 'lock',
          name: '清单锁定',
          visible: true,
          disabled: false,
        },
        {
          code: 'tempDelete',
          name: '临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'batchDelete',
          name: '批量删除',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'batchDelete-child1',
              name: '批量删除所有临时删除项',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'batchDelete-child2',
              name: '批量删除所有工程量为0项',
              type: 2,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'MainList',
          name: '主要清单',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'set-list',
              name: '设置主要清单',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-current',
              name: '取消当前行',
              type: 2,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-all',
              name: '取消所有清单',
              type: 3,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'noteList',
          name: '批注',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'add-note',
              name: '插入批注',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'edit-note',
              name: '编辑批注',
              type: 2,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-note',
              name: '删除批注',
              type: 3,
              visible: true,
              disabled: false,
            },
            {
              code: 'show-note',
              name: '显示批注',
              type: 4,
              visible: true,
              disabled: false,
            },
            {
              code: 'hide-note',
              name: '隐藏批注',
              type: 5,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-all-note',
              name: '删除所有批注',
              type: 6,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'pageColumnSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, column, columnIndex, row, rowIndex);
    if (!row) return;
    // frameSelectRef.value?.clearSelect();
    // 获取当前行状态
    currentInfo.value = row;
    projectStore.SET_SUB_CURRENT_INFO(row);
    vexTable.value.setCurrentRow(row);
    deleteStateFn();
    let index = selectData.value?.data.findIndex(
      x => x.sequenceNbr === row.sequenceNbr
    );
    if (index === -1 && selectData.value?.data.length > 1) {
      selectData.value?.data.push(row);
    }
    options.forEach(list => {
      list.forEach(async (item, index) => {
        console.log('row', row);
        if (!copyData.value && item.code === 'paste') {
          item.disabled = true;
        }
        if (copyData.value && item.code === 'paste') {
          item.disabled = false;
          try {
            await frameSelectRef.value.frameSelectJs.isPasteMeasure(
              row,
              copyData.value
            );
            item.disabled = false;
          } catch (error) {
            item.disabled = true;
          }
        }
        if (item.code === 'copy') {
          if (
            currentInfo.value.optionMenu.includes(4) ||
            currentInfo.value.optionMenu.includes(5)
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        } else if (item.code === 'delete') {
          if (
            (currentInfo.value.optionMenu.includes(4) ||
              currentInfo.value.optionMenu.includes(5)) &&
            ((currentInfo.value.kind === '03' &&
              currentInfo.value.hasOwnProperty('zjcsClassCode') &&
              currentInfo.value.zjcsClassCode !== null &&
              currentInfo.value.zjcsClassCode !== undefined &&
              Number(currentInfo.value.zjcsClassCode) === 0) ||
              currentInfo.value.constructionMeasureType === 2)
          ) {
            item.disabled = true;
          } else {
            item.disabled = currentInfo.value.isLocked;
          }
          if (
            (currentInfo.value.optionMenu.includes(4) ||
              currentInfo.value.optionMenu.includes(5)) &&
            currentInfo.value.name !== '安全生产、文明施工费' &&
            !currentInfo.value.isLocked
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        } else if (item.code === 'lock') {
          if (projectStore.standardGroupOpenInfo.isOpen) {
            item.disabled = true;
          } else {
            if (currentInfo.value.kind === '03') {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
            if (currentInfo.value.isLocked) {
              item.name = '清单解锁';
            } else {
              item.name = '清单锁定';
            }
          }
        } else if (item.code === 'tempDelete') {
          let parentInfo = renderedList.value.filter(
            x => x.sequenceNbr === currentInfo.value.parentId
          )[0];
          if (
            ((currentInfo.value.kind === '03' && !currentInfo.value.isLocked) ||
              (currentInfo.value.kind === '04' &&
                !parentInfo.tempDeleteFlag)) &&
            currentInfo.value.name !== '安全生产、文明施工费'
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
          if (currentInfo.value.tempDeleteFlag) {
            item.name = '取消临时删除';
          } else {
            item.name = '临时删除';
          }
        }
        if (
          item.children &&
          !['batchDelete', 'noteList', 'MainList'].includes(item.code)
        ) {
          item.disabled = false;
          item.children.forEach(childItem => {
            childItem.disabled = true;
            currentInfo.value.optionMenu.forEach(child => {
              if (child === childItem.code) {
                childItem.disabled = false;
              }
            });
          });
        }
        if (item.code === 'add') needAddQDandFB(item);

        handleNote(item, row);

        handleMainList(item, row);
        if (
          [
            'lock',
            'tempDelete',
            'MainList',
            'noteList',
            'batchDelete',
            'pageColumnSetting',
            'copy',
            'paste',
          ].includes(item.code)
        ) {
          // 预算审核这几个菜单暂时隐藏
          item.visible = false;
        }
      });
    });
    return true;
  },
});

const contextMenuClickEvent = async ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (menu?.fatherCode === 'noteList' || menu.code == 'noteList') {
    if (menu.code != 'noteList') {
      handleNoteClick(menu, row);
    }
  } else if (menu?.fatherCode === 'MainList' || menu.code == 'MainList') {
    if (menu.code != 'MainList') {
      handleMainListClick(menu, row);
    }
  } else if (menu.code === 'delete') {
    deleteType();
  } else if (menu.code === 'copy') {
    // 复制方法
    // message.error('请执行复制方法')
    console.log('执行复制方法');
    const handleData =
      await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
        selectData.value.data
      );
    selectData.value.data = handleData;
    // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
    if (handleData.length) {
      selectData.value.isCopy = true;
    }
    copyFun();
  } else if (menu.code === 'paste') {
    // 粘贴方法
    // message.error('请执行粘贴方法')
    console.log('执行粘贴方法');
    pasteFun();
  } else if (menu.code === 'lock') {
    if (row.isLocked === 1) {
      csUnLockQd();
    } else {
      csLockQd();
    }
  } else if (menu.code === 'pageColumnSetting') {
    showPageColumnSetting();
  } else if (menu.code === 'tempDelete') {
    updateDelTempStatusColl();
  } else if (
    menu.code === 'batchDelete-child1' ||
    menu.code === 'batchDelete-child2'
  ) {
    batchDeleteVisible.value = true;
    batchDataType.value = menu.type;
  } else if (menu.code !== 'add' && menu.code !== 'batchDelete') {
    addData(menu.kind);
  }
};

const rowClassName = ({ row }) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    if (!row.ysshSysj.change) {
      ClassStr = 'row-qd';
    }
    let rowClass = shChangeLabel({ row: row });
    ClassStr = 'row-qd ' + rowClass;
  } else if (row.kind === '04' && row.ysshSysj.change === 2) {
    ClassStr = 'row-del-text-decoration';
  }
  if (row.tempDeleteFlag) {
    ClassStr = 'temp-delete';
  }

  if (row.sequenceNbr == renderedList.value[0]?.sequenceNbr) {
    ClassStr += ' first-row';
  }

  return ClassStr;
};

const cellStyle = ({ row, column }) => {
  if (['fxCode'].includes(column.field)) {
    return {
      paddingLeft: row.customLevel * 12 + 'px',
    };
  }
};

const cellClassName = ({ $columnIndex, column, row }) => {
  let className = selectedClassName({ $columnIndex, column, row });
  let rowClass = shChangeLabel({ row: row });
  if (column.field === 'fxCode') {
    className += ` code-color Virtual-pdLeft${row.customLevel}`;
  } else if (column.field === 'index') {
    className += ' index-bg ';
  }

  if (
    row.kind === '04' &&
    (((column.field === 'ysshSysj.quantity' || column.field === 'quantity') &&
      row.ysshSysj.quantity !== row.quantity) ||
      ((column.field === 'ysshSysj.price' || column.field === 'price') &&
        row.ysshSysj.price !== row.price) ||
      ((column.field === 'ysshSysj.total' || column.field === 'total') &&
        row.ysshSysj.total !== row.total))
  ) {
    className += rowClass;
  }

  // 批注提示
  if (column.field == 'name' && row?.annotations) {
    className += ' note-tips ';
  }
  if (column.field === 'fxCode' && row.kind !== '04') {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line} `;
  }
  // 添加默认两行类名
  if (
    [
      'qfCode',
      'measureType',
      'description',
      'costMajorName',
      'projectAttr',
    ].includes(column.field) ||
    (column.field === 'fxCode' && row.kind === '04')
  ) {
    const line = row.maxLine || 2;
    className += ` cell-line-break-${line} `;
  }
  if (['qfCode', 'measureType', 'costMajorName'].includes(column.field)) {
    className += ` single-item `;
  }
  if (['projectAttr'].includes(column.field)) {
    className += ` projectAttr-item `;
  }
  return className;
};
const showModel = type => {
  isPriceModel.value = true;
  switch (type) {
    case 'zscy':
      // 装饰垂运
      showModelType.value = 'zscy';
      showPriceTitle.value = '设置装饰垂运';
      break;
    case 'zscg':
      // 装饰超高
      showModelType.value = 'zscg';
      showPriceTitle.value = '设置装饰超高降效';
      break;
    case 'azfy':
      // 安装费用
      showModelType.value = 'azfy';
      showPriceTitle.value = '安装费用计取';
      break;
    case 'csfy':
      // 自动计算措施费用
      showModelType.value = 'csfy';
      showPriceTitle.value = '自动计取总价措施';

      break;
  }
};

const saveCustomInput = (newValue, row, name, index) => {
  if (newValue) {
    row[name] = newValue;
  }
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
};

// 编辑弹框关闭方法
const editCancel = () => {
  isShowModel.value = false;
};

// 计取费用数据更新
const updateData = () => {
  isPriceModel.value = false;
  queryBranchDataById();
};

const closePriceModel = () => {
  isPriceModel.value = false;
};

const tableColumn = ref([
  { field: 'bdCodeLevel04', title: '项目编码' },
  { field: 'bdNameLevel04', title: '项目名称' },
  { field: 'unit', title: '单位' },
]);
const tableList = ref([]);

const keyupEvent = (row, e) => {
  console.log('keyupEvent', row, e);
  if (row.kind !== '03') return;
  if (e.value.length > 1) {
    const $pulldown = pulldownRef.value;
    console.log('keyupEvent', $pulldown);
    if ($pulldown) {
      $pulldown[0]?.showPanel();
    }
    searchQdByCode(e.value);
  }
};

const cellClickEvent = ({ row }) => {
  addCurrentInfo.value = row;
  const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
  row.unit = unit;
  const $pulldown = pulldownRef.value;
  if ($pulldown) {
    const $table = vexTable.value;
    if ($table) {
      console.log('22222222222222', $table);
      isClearEdit.value = true;
      $table.clearEdit();
    }
    if (row.unit && row.unit.length > 1) {
      showUnitTooltip.value = true;
    } else {
      updateQdByCode(row.bdCodeLevel04, row.unit ? row.unit[0] : null);
    }
    $pulldown[0]?.hidePanel();
    isClearEdit.value = false;
  }
};

// 根据编码模糊搜索标准清单
const searchQdByCode = code => {
  api.searchQdByCode({ code: code }).then(res => {
    console.log('根据编码模糊搜索标准清单', res);
    if (res.status === 200 && res.result) {
      tableList.value = res.result;
    }
  });
};

// 通过标准编码插入清单
const updateQdByCode = (code, unit) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    code: code,
    unit: unit,
    isSortQdCode: isSortQdCode.value,
  };
  api.updateQdByCode(apiData).then(res => {
    console.log('标准清单编码插入', res);
    if (res.status === 200 && res.result) {
      if (currentInfo.value.standardId) {
        message.success('清单替换成功');
      } else {
        message.success('清单插入成功');
      }
      selectUnit.value = '';
      queryBranchDataById();
    }
  });
};

const saveData = inputData => {
  if (bdCode.value) {
    updateQdByPage(inputData);
  } else {
    addBcQdData(inputData);
  }
};

const deSaveData = inputData => {
  if (bdCode.value) {
    updateDeByPage(inputData);
  } else {
    addBcDeData(inputData);
  }
};

const rcjSaveData = inputData => {
  if (bdCode.value) {
    spRcjByPage(inputData);
  } else {
    addBjqBcRcjData(inputData);
  }
};

// 通过修改编码补充清单替换当前行数据
const updateQdByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
  };
  console.log('补充清单参数', apiData);
  api.updateQdByPage(apiData, inputData).then(res => {
    console.log('通过界面信息插入清单', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

const updateDeByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
  };
  api.updateDeByPage(apiData).then(res => {
    console.log('updateDeByPage', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};
// 点击补充按钮补充清单数据
const addBcQdData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
    rootLineId: '',
  };

  console.log('补充清单参数', apiData);
  api.addBcQdData(apiData).then(res => {
    console.log('通过界面信息插入清单', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

// 判断输入的定额编码是否是标准定额
const isStandardDe = code => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
  };
  api.isStandardDe(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        updateDeReplaceData(code);
      } else {
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: '标准定额库下未找到该定额，是否补充定额？',
          confirm: () => {
            deVisible.value = true;
            bdCode.value = code;
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            currentInfo.value.fxCode = currentInfo.value.originalFxCode;
          },
        });
      }
      console.log('判断输入的定额编码是否为主定额库编码', res);
    }
  });
};

// 分部分项 措施项目 替换定额数据
const updateDeReplaceData = code => {
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
    type: 2,
  };
  console.log('通过标准编码插入定额', apiData);
  api.updateDeReplaceData(apiData).then(res => {
    console.log('通过标准编码插入定额结果', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      deVisible.value = false;
      message.success('定额替换成功');
      queryBranchDataById();
    }
  });
};

// 点击补充按钮补充定额数据
const addBcDeData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    rootLineId: '',
    type: 2,
  };

  api.addBcDeData(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};

// 分部分项 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
    region: 0,
  };
  api.spRcjByPage(apiData).then(res => {
    console.log('1111111111', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      message.success('人材机替换成功');
      rcjVisible.value = false;
      queryBranchDataById();
    }
  });
};
// 分部分项 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
    region: 0,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };
  api.addBjqBcRcjData(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      rcjVisible.value = false;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      message.success('人材机新建成功');
      queryBranchDataById();
    }
  });
};

// 整体锁定
const allLock = () => {
  if (lockFlag.value) {
    csUnLockAll();
  } else {
    csLockAll();
  }
};

// 清单整体锁定
const csLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体解锁';
      queryBranchDataById();
    }
  });
};

// 清单整体解锁
const csUnLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitUnLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体锁定';
      queryBranchDataById();
    }
  });
};

// 清单锁定
const csLockQd = () => {
  currentInfo.value.isLocked = 1;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.csLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      queryBranchDataById();
    }
  });
};

// 清单解锁
const csUnLockQd = () => {
  currentInfo.value.isLocked = 0;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.csUnLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      queryBranchDataById();
    }
  });
};

// 补充页面点击取消操作 type 1 清单 2 定额 3 人材机
const bcCancel = type => {
  if (type === 1) {
    qdVisible.value = false;
  } else if (type === 2) {
    deVisible.value = false;
  } else {
    rcjVisible.value = false;
  }
  if (bdCode.value) {
    currentInfo.value.fxCode = currentInfo.value.originalFxCode;
  }
};

// 清单多单位时选择单位确定事件
const selectHandler = dataRef => {
  if (!selectUnit.value) {
    return message.warning('请选择单位');
  }
  showUnitTooltip.value = false;
  dataRef.unit = selectUnit.value;
  let obj = {
    bdCode: dataRef.bdCodeLevel04,
    bdName: dataRef.bdNameLevel04,
    sequenceNbr: dataRef.sequenceNbr,
    unit: dataRef.unit,
    quantityExpression: dataRef.quantityExpression,
    libraryCode: dataRef.libraryCode,
  };
  updateQdByCode(obj.bdCode, obj.unit);
};

// 获取当前点击行下挂所有数据
/**
 * 以前的这里也修改了用户选择的数据，用户选择了数据，然后又在这掉接口了，不知道为啥，待以后优化吧
 */
const queryAllDataByBranchId = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: currentInfo.value?.sequenceNbr,
    pageSize: 10000,
    pageNum: page.value,
  };
  api.itemSearchForSequenceNbr(apiData).then(async res => {
    console.log('================当前点击数据下挂所有数据', res);
    if (res.status === 200 && res.result) {
      // 后端给的所有的数据，需要过滤。
      const handleData =
        await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
          res.result
        );
      selectData.value.data = handleData;
      // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
      if (handleData.length) {
        selectData.value.isCopy = true;
      }
    }
  });
};

// 批量复制接口
const copyBranchDataQdDe = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
  };
  console.log('批量复制接口参数', apiData);
  let isAwfData = false;
  let awfObj = tableData.value.filter(x => x.constructionMeasureType === 2)[0];
  selectData.value.data.forEach(item => {
    awfObj.children.forEach(child => {
      if (item.sequenceNbr === child) {
        isAwfData = true;
      }
    });
  });
  if (isAwfData) {
    message.error('安全生产、文明施工费下挂数据不可进行复制操作~');
    return;
  }
  api.copyBranchDataQdDe(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200 && res.result) {
      message.success('已复制');
    }
  });
};

// 批量删除
const delBatchData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
  };
  api.itemBatchDelete(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      queryBranchDataById();
    } else {
      message.error(res.message);
      deleteVisible.value = false;
      deleteLoading.value = false;
    }
  });
};

// 措施项目定额上移下移
const moveDeData = ({ state, type }) => {
  console.log(state, type);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    upId: projectStore.currentTreeInfo?.id,
    selectId: currentInfo.value.sequenceNbr,
    operateAction: state === 1 ? 'up' : 'down',
    type: 'csxm',
  };
  if (type === 'move') {
    api.moveDeData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('移动成功');
        emits('updateMenuList');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById();
      }
    });
  }
};

// 组价方案匹配条件筛选
const filterData = val => {
  let tempList = [];
  tableData.value = [];
  if (val.length === 0 || !val) {
    tableData.value = originalTableData.value;
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  } else {
    originalTableData.value.forEach(item => {
      if (val.includes(item.matchStatus)) {
        tempList.push(item.sequenceNbr);
      }
    });
    for (let i = 0; i < originalTableData.value.length; i++) {
      if (
        tempList.includes(originalTableData.value[i].sequenceNbr) ||
        tempList.includes(originalTableData.value[i].parentId)
      ) {
        tableData.value.push(originalTableData.value[i]);
      }
    }
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
    // tableData.value = originalTableData.value.filter(x =>
    //   val.includes(x.matchStatus)
    // );
  }
  // const initList = init(tableData.value);
  // nextTick(() => {
  //   initList();
  // });
};

const zrMb = () => {
  page.value = 1;
  if (!projectStore.isAutoPosition) {
    // 不是自动定位的才调用接口
    queryBranchDataById('other');
  }
  // nextTick(() => {
  //   initVirtual(vexTable.value);
  // });
};

projectStore.measuresItemGetList = zrMb;

// 复用组价
let ReuseGroupPriceRef = ref(null);

const openReuseGroupPrice = ({ activeKind }) => {
  if ([0, 1, 2].includes(activeKind)) {
    ReuseGroupPriceRef.value.open(activeKind);
  }
};

// 清单快速组价
const qdQuickPricingRef = ref(null);
const openQdQuickPricing = () => {
  qdQuickPricingRef.value.open('csxm');
};

const tableColumns = ref([
  // 常用项
  {
    title: '序号',
    field: 'dispNo',
    width: 50,
    classType: 1,
    fixed: 'left',
  },
  {
    title: '项目编码',
    field: 'fxCode',
    align: 'left',
    headerAlign: 'center',
    editRender: { autofocus: '.vxe-input--inner' },
    slot: true,
    classType: 1,
    fixed: 'left',
    showTooltip: true,
  },
  {
    title: '类型',
    field: 'type',
    width: 50,
    slot: true,
    classType: 1,
    fixed: 'left',
    showTooltip: true,
  },
  {
    title: '项目名称',
    field: 'name',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
    showTooltip: true,
  },
  {
    title: '单位',
    field: 'unit',
    slot: true,
    editRender: {},
    width: 60,
    classType: 1,
  },
  {
    title: '送审',
    field: 'ysshSysj',
    colgroup: [
      {
        title: '项目特征',
        field: 'ysshSysj.projectAttr',
        width: 120,
        classType: 1,
        showTooltip: true,
      },
      {
        title: '工程量',
        field: 'ysshSysj.quantity',
        width: 100,
        classType: 1,
      },
      {
        title: '综合单价',
        field: 'ysshSysj.price',
        width: 100,
        classType: 1,
      },
      {
        title: '综合合价',
        field: 'ysshSysj.total',
        width: 100,
        classType: 1,
      },
    ],
  },
  {
    title: '审定',
    field: 'sd',
    colgroup: [
      {
        title: '项目特征',
        field: 'projectAttr',
        width: 120,
        slot: true,
        editRender: { autofocus: '.vxe-textarea--inner' },
        classType: 1,
        showTooltip: true,
      },
      {
        title: '工程量',
        field: 'quantity',
        width: 100,
        slot: true,
        editRender: { autofocus: '.vxe-input--inner' },
        classType: 1,
      },
      {
        title: '综合单价',
        field: 'price',
        width: 100,
        classType: 1,
      },
      {
        title: '综合合价',
        field: 'total',
        width: 100,
        classType: 1,
      },
    ],
  },
  {
    title: '工程量表达式',
    field: 'quantityExpression',
    width: 100,
    slot: true,
    editRender: { autofocus: '.vxe-input--inner' },
    classType: 1,
  },
  {
    title: '增减金额',
    field: 'ysshSysj.changeTotal',
    width: 100,
    classType: 1,
  },
  {
    title: '增减比例（%）',
    field: 'ysshSysj.changeRatio',
    width: 100,
    classType: 1,
  },
  {
    title: '增减说明',
    field: 'ysshSysj.changeExplain',
    width: 160,
    slot: true,
    editRender: { autofocus: '.vxe-textarea--inner' },
    classType: 1,
  },
  {
    title: '施工组织措施类别',
    field: 'measureType',
    editRender: {},
    slot: true,
    classType: 1,
  },
  {
    title: '取费文件',
    field: 'costMajorName',
    width: 100,
    editRender: {},
    slot: true,
    visible: true,
    classType: 1,
  },
  {
    title: '措施类别',
    field: 'itemCategory',
    editRender: {},
    slot: true,
    classType: 1,
  },
  // {
  //   title: '人工费合价',
  //   field: 'totalRfee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '材料费单价',
  //   field: 'cfee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '材料费合价',
  //   visible: false,
  //   field: 'totalCfee',
  //   classType: 2,
  // },
  // {
  //   title: '机械费单价',
  //   field: 'jfee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '机械费合价',
  //   field: 'totalJfee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '管理费单价',
  //   field: 'managerFee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '管理费合价',
  //   field: 'totalManagerFee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '利润单价',
  //   field: 'profitFee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '利润合价',
  //   field: 'totalProfitFee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '主材费单价',
  //   field: 'zcfee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '主材费合价',
  //   field: 'totalZcfee',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '防寒子目',
  //   field: 'coldResistantSuborder',
  //   slot: true,
  //   classType: 1,
  //   visible: false,
  // },
  // {
  //   title: '主要清单',
  //   field: 'ifMainQd',
  //   slot: true,
  //   visible: false,
  //   classType: 1,
  // },
  // {
  //   title: '备注',
  //   field: 'description',
  //   slot: true,
  //   editRender: { autofocus: '.vxe-textarea--inner' },
  //   classType: 1,
  // },
  // {
  //   title: '设备费单价',
  //   field: 'sbfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '设备费合价',
  //   field: 'sbfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '暂估单价',
  //   field: 'zgfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '暂估合价',
  //   field: 'zgfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '规费单价',
  //   field: 'gfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '规费合价',
  //   field: 'gfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '生产工具使用费合价',
  //   field: 'scgjsyfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '生产工具使用费单价',
  //   field: 'scgjsyfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '繁华地段管理增加费单价',
  //   field: 'fhddglzjfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '繁华地段管理增加费合价',
  //   field: 'fhddglzjfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '冬季防寒费单价',
  //   field: 'gjfhfPrice',
  //   classType: 2,
  //   visible: false,
  // },
  // {
  //   title: '冬季防寒费合价',
  //   field: 'gjfhfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '山地管护增加费单价',
  //   field: 'sdghzjfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '山地管护增加费合价',
  //   field: 'sdghzjfTotal',
  //   visible: false,
  //   classType: 2,
  // },

  // {
  //   title: '绿色施工安全防护措施费单价',
  //   field: 'lssgaqfhcsfPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '绿色施工安全防护措施费合价',
  //   field: 'lssgaqfhcsfTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '进项税额单价',
  //   field: 'jxsePrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '进项税额合价',
  //   field: 'jxseTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '销项税额单价',
  //   field: 'xxsePrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '销项税额合价',
  //   field: 'xxseTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '增值税应纳税额单价',
  //   field: 'zzsynsePrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '增值税应纳税额合价',
  //   field: 'zzsynseTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '附加税费单价',
  //   field: 'fjsePrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '附加税费合价',
  //   field: 'fjseTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '税前工程造价单价',
  //   field: 'sqgczjPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '税前工程造价合价',
  //   field: 'sqgczjTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '风险费用单价',
  //   field: 'fxfyPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '风险费用合价',
  //   field: 'fxfyTotal',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '税金单价',
  //   field: 'sjPrice',
  //   visible: false,
  //   classType: 2,
  // },
  // {
  //   title: '税金合价',
  //   field: 'sjTotal',
  //   classType: 2,
  //   visible: false,
  // },
]);
const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'yssh',
  initCallback: () => {
    renderLine();
  },
});

const {
  isSheHeDelete,
  listAssociationList,
  listAssociationChange,
  importBasedOnMethod,
  isDisabledImportBasedOn,
  operateKeyItemFilteringMark,
} = commonSheHe({
  refresh: queryBranchDataById,
});

// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};
defineExpose({
  queryBranchDataById,
  getTableData: zrMb,
  copyAndPaste,
  posRow,
});
</script>

<style lang="scss" scoped>
.code-line-break {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.association-selected {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
}
.normal-info .code-flag {
  color: #a73d3d;
}
.code-black {
  color: #2a2a2a;
}
.flag-green {
  background: #90c942;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-orange {
  background: #ffaa09;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-red {
  background: #de3f3f;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.multiple-select {
  height: 16px;
  line-height: 16px;
  //text-indent: 10px;
  cursor: pointer;
}
.subItem-project {
  background: #ffffff;
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    // height: 40px;
    padding: 0 10px;
  }
  .table-content {
    // height: calc(65%);
    height: 100%;
    //user-select: none;

    ::v-deep(.vxe-table .row-unit) {
      background: #e6dbeb;
    }
    ::v-deep(.vxe-table .row-sub) {
      background: #efe9f2;
    }
    ::v-deep(.vxe-table .row-qd) {
      background: #dce6fa;
    }
    ::v-deep(.vxe-body--row.row--current) {
      background: #a6c3fa;
    }
    ::v-deep(.vxe-table .row-qd .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
      }
    }
    ::v-deep(.vxe-table .normal-info .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        // text-overflow: ellipsis;
        //max-height: 3.0em; /* 高度为字体大小的两倍 */
        //line-height: 1.5em; /* 行高 */
        //height: auto; /* 高度为行高的两倍 */
      }
    }
    ::v-deep(.vxe-table .index-bg) {
      background-color: #ffffff;
    }
    ::v-deep(.vxe-table .temp-delete) {
      background: #f3f2f3;
      color: #a7a7a7;
      text-decoration: line-through;
    }
    ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
      background-image:
        linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
      background-image:
        linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
    }
  }
  .quota-content {
    // height: 35%;
    height: 100%;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    //user-select: none;
  }
  .project-attr {
    white-space: pre-wrap;
    text-align: left;
  }
}
.content {
  margin-bottom: 20px;
}
.edit-content {
  margin-bottom: 20px;
}
.my-dropdown4 {
  width: 600px;
  height: 200px;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}

.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.reCheck {
  margin-top: 10px;
}
.custom-header .icon-close {
  right: 20px;
  background: #ffffff;
  z-index: 20;
  padding: 3px;
}
</style>
