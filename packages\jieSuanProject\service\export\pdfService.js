const path = require('path');
const {Service} = require("../../../../core");
const util = require('util');
const exec = util.promisify(require('child_process').exec);
const UtilsPs = require('../../../../core/ps');
const {ExcelUtil} = require("../../../../electron/utils/ExcelUtil.js");

class PdfService extends Service {
    constructor(ctx) {
        super(ctx);
    }




}
PdfService.toString = () => '[class PdfService]';
module.exports = PdfService;
