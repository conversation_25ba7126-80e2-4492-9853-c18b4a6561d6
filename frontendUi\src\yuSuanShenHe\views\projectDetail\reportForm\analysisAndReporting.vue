<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-13 17:04:36
 * @LastEditors: liuxia
 * @LastEditTime: 2024-07-03 10:51:34
 * @Introduce:
-->
<template>
  <!-- rendered--渲染成功； error--渲染失败 -->
  <div>
    <div class="tab-wrap" @click="menuClick">
      <div
        class="menu"
        v-for="i of tabList"
        :key="i.type"
        :class="{ active: useMenu === i.type }"
        :style="{ cursor: address ? '' : 'not-allowed' }"
        :data-type="i.type"
      >
        <icon-font
          class="icon-font"
          :type="i.icon"
          :data-type="i.type"
        ></icon-font>
        <span :data-type="i.type">{{ i.name }}</span>
      </div>
    </div>
    <vue-office-docx :src="url" class="docx-calss" />
  </div>
</template>
<script setup>
import VueOfficeDocx from '@vue-office/docx';
import '@vue-office/docx/lib/index.css';
import { message } from 'ant-design-vue';

import { onMounted, ref, nextTick, markRaw } from 'vue';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/shApi';
const store = projectDetailStore();
const route = useRoute();
const url = ref('');
const address = ref('');
const tabList = markRaw([
  {
    icon: 'icon-daochuWord',
    name: '导出word',
    type: 'word',
  },
]);
const getcreateWordData = () => {
  console.log(route.query, 'route.query');
  let apiData = {
    constructId: route.query.constructId,
    ssConstructId: route.query.ssConstructId,
  };
  console.log(apiData, 'apiData');
  csProject
    .createWordData(apiData)
    .then(route => {
      csProject
        .readCreateWordData({ baseDataDir: route.result })
        .then(res => {
          nextTick(() => {
            const blob = new Blob([res.result], {
              type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            });
            url.value = URL.createObjectURL(blob);
            address.value = route.result;
          });
        })
        .catch(err => {
          console.log('readCreateWordData', err);
        });
    })
    .catch(err => {
      console.log(err, 'err');
    });
};
const menuClick = () => {
  if (!address.value) {
    return;
  }
  let apiData = {
    constructId: route.query.constructId,
    baseDataDir: address.value,
  };
  console.log(apiData, 'apidata');
  csProject.downloadWordFile(apiData).then(res => {
    console.log(res, 'res');
    if (res.status === 200) {
      message.success(res.result);
    }
  });
};
onMounted(() => {
  getcreateWordData();
});
</script>
<style lang="scss" scoped>
.tab-wrap {
  display: flex;
  align-items: center;
  height: 42px;
  border-bottom: 2px solid #dcdfe6;
  padding-left: 22px;
  background-color: #fff;
  .icon-font {
    font-size: 19px;
    margin-right: 5px;
  }
  .menu {
    position: relative;
    padding: 5px 4px;
    border-radius: 4px;
    margin: 0 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    &:last-child {
      &::after {
        display: none;
      }
    }
    &::after {
      position: absolute;
      content: '';
      height: 100%;
      width: 1px;
      background-color: #dedede;
      right: -8px;
      top: 0;
    }
    &:hover,
    &.active {
      background: rgba(226, 227, 231, 0.79);
      opacity: 1;
    }
  }
}
</style>
