<!--
 * @Descripttion: 人材机汇总
-->
<template>
  <div
    class="table-content table-content-flex-column"
    id="humanTable"
  >
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <vxe-table
          align="center"
          ref="humanTable"
          height="auto"
          :menu-config="menuConfig"
          :column-config="{ resizable: true }"
          :row-config="{
            isHover: true,
            isCurrent: true,
          }"
          :data="tableData"
          :cell-style="
            projectStore.currentTreeInfo.levelType === 3
              ? cellStyle
              : cellTableStyle
          "
          :row-style="rowStyle"
          @edit-closed="editClosedEvent"
          keep-source
          @menu-click="contextMenuClickEvent"
          @cell-click="
            cellData => {
              useCellClickEvent(cellData, null, ['ifDonorMaterial']);
            }
          "
          class="table-edit-common"
          :cell-class-name="selectedClassName"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethod,
          }"
          :scroll-y="{ enabled: true, gt: 30 }"
          @current-change="currentChange"
          :export-config="{}"
          show-overflow
          :header-cell-class-name="setHeaderCellClassName"
        >
          <vxe-column
            type="checkbox"
            width="40"
            fixed="left"
          ></vxe-column>
          <vxe-column
            v-for="columns of showColumns"
            v-bind="columns"
          >
            <template #header="{ column }">
              <span
                class="custom-header"
                v-if="
                  columns.slot &&
                  [
                    'materialCode',
                    'type',
                    'materialName',
                    'specification',
                    'unit',
                    'totalNumber',
                    'dePrice',
                    'marketPrice',
                    'priceMarket',
                    'priceMarketTax',
                    'total',
                    'priceMarketTotal',
                    'priceMarketTaxTotal',
                    'taxRate',
                    'priceDifferenc',
                    'priceDifferencSum',
                  ].includes(column.field)
                "
              >
                <span
                  style="cursor: pointer"
                  @click="sortClick(column.field)"
                >{{ column.title }}</span>
                <img
                  class="sortImg"
                  src="@/assets/img/upSort.png"
                  v-if="sortFiled == column.field && sortVal"
                  alt=""
                />
                <img
                  class="sortImg"
                  src="@/assets/img/downSort.png"
                  v-if="sortFiled == column.field && !sortVal"
                  alt=""
                />
                <CloseOutlined
                  class="icon-close"
                  @click="closeColumn({ column })"
                />
              </span>
              <span
                class="custom-header"
                v-else
              >
                <span>{{ column.title }}</span>
                <CloseOutlined
                  class="icon-close"
                  @click="closeColumn({ column })"
                />
              </span>
            </template>

            <template
              v-if="columns.slot"
              #default="{ column, row, $columnIndex }"
            >
              <template v-if="column.field === 'materialCode'">
                {{ '‎' + row.materialCode }}
              </template>
              <template v-else-if="
                  [
                    'priceMarketTotal',
                    'marketPrice',
                    'total',
                    'priceMarketTotal',
                    'priceMarketTaxTotal',
                  ].includes(column.field)
                ">
                {{ isChangeAva(row) ? '-' : row[column.field] }}
              </template>

              <template v-else-if="
                  ['priceMarketTax', 'priceMarket'].includes(column.field)
                ">{{ getValueByDeType('12', row, column.field) }}</template>
              <template v-else-if="['markSum'].includes(column.field)">
                <vxe-checkbox
                  v-model="row.markSum"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  :disabled="projectStore.currentTreeInfo.levelType === 1"
                  @change="CheckboxChange(row, 'markSum')"
                  v-if="[1, 2].includes(Number(row.levelMark))"
                ></vxe-checkbox>
                <!-- 王浩让工程项目级别禁止勾选是否汇总复选框 -->
              </template>
              <template v-else-if="column.field === 'ifDonorMaterial'">
                {{ getDonorMaterialText(row.ifDonorMaterial) }}
              </template>
              <template v-else-if="column.field === 'donorMaterialNumber'">
                <span v-if="row.checkIsShow">{{
                  row.donorMaterialNumber
                }}</span>
              </template>

              <template v-else-if="column.field === 'ifProvisionalEstimate'">
                <vxe-checkbox
                  v-model="row.ifProvisionalEstimate"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  :disabled="isChangeAva(row)"
                  @change="CheckboxChange(row, 'ifProvisionalEstimate')"
                  v-if="
                    row.checkIsShow && !['人工费', '机械费'].includes(row.type)
                  "
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'ifLockStandardPrice'">
                <vxe-checkbox
                  v-model="row.ifLockStandardPrice"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'ifLockStandardPrice')"
                  v-if="row.checkIsShow"
                  :disabled="Number(row.edit) === 1"
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'output'">
                <vxe-checkbox
                  v-model="row.output"
                  size="small"
                  content=""
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="CheckboxChange(row, 'output')"
                ></vxe-checkbox>
              </template>
              <template v-else-if="column.field === 'taxRate'">
                {{ getValueByDeType('12', row, column.field) }}
              </template>
              <template v-else-if="['taxRemoval', 'jxTotal'].includes(column.field)">
                {{
                  isDeType('22', row.deStandardReleaseYear)
                    ? '/'
                    : row[column.field]
                }}
              </template>
              <template v-else>{{ row[column.field] }}</template>
            </template>

            <template
              v-if="columns.slot"
              #edit="{ column, row, $columnIndex }"
            >
              <template v-if="column.field === 'type'">
                <vxe-select
                  v-model="row.type"
                  :clearable="false"
                  transfer
                  v-if="
                    (row.type === '主材费' ||
                      row.type === '材料费' ||
                      row.type === '设备费') &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                >
                  <vxe-option
                    v-for="item in selectOptions"
                    :key="item.type"
                    :value="item.type"
                    :label="item.type"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{ row.type }} </span>
              </template>

              <template v-else-if="column.field === 'dispNo'">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.dispNo"
                  type="text"
                  @blur="clear()"
                  @keyup="row.dispNo = row.dispNo.replace(/[^\w.]/g, '')"
                ></vxe-input>
              </template>
              <template v-else-if="column.field === 'materialName'">
                <vxe-input
                  v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                  :clearable="false"
                  v-model.trim="row.materialName"
                  type="text"
                  @blur="clear()"
                ></vxe-input>
                <span v-else>{{ row.materialName }}</span>
              </template>
              <template v-else-if="column.field === 'specification'">
                <vxe-input
                  :clearable="false"
                  v-model.trim="row.specification"
                  type="text"
                  @blur="clear()"
                  v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                ></vxe-input>
                <span v-else>{{ row.specification }}</span>
              </template>
              <template v-else-if="column.field === 'unit'">
                <vxeTableEditSelect
                  v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                  :filedValue="row.unit"
                  :list="projectStore.unitListString"
                  @update:filedValue="
                    newValue => {
                      saveCustomInput(newValue, row, 'unit', $rowIndex);
                    }
                  "
                ></vxeTableEditSelect>
                <span v-else>{{ row.unit }}</span>
              </template>
              <template v-else-if="column.field === 'marketPrice'">
                <vxe-input
                  v-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                    row.marketPrice = pureNumber(row.marketPrice, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row) ? '-' : row.marketPrice
                }}</span>
              </template>
              <template v-else-if="column.field === 'priceMarket'">
                <template v-if="row.deStandardReleaseYear === '12'">
                  <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                  <vxe-input
                    v-else-if="
                      row.ifLockStandardPrice !== 1 &&
                      isPartEdit &&
                      !(
                        row.markSum === 1 &&
                        [1, 2].includes(Number(row.levelMark))
                      ) &&
                      Number(row.edit) !== 1 &&
                      !isChangeAva(row) &&
                      !isOtherMaterial
                    "
                    :clearable="false"
                    v-model.trim="row.marketPrice"
                    type="text"
                    @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                  ></vxe-input>
                  <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
                </template>
                <vxe-input
                  v-else-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                  :clearable="false"
                  v-model.trim="row.priceMarket"
                  type="text"
                  @blur="
                    row.priceMarket = pureNumber(row.priceMarket, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarket
                }}</span>
              </template>
              <template v-else-if="column.field === 'priceMarketTax'">
                <template v-if="row.deStandardReleaseYear === '12'">
                  <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                  <vxe-input
                    v-else-if="
                      row.ifLockStandardPrice !== 1 &&
                      isPartEdit &&
                      !(
                        row.markSum === 1 &&
                        [1, 2].includes(Number(row.levelMark))
                      ) &&
                      Number(row.edit) !== 1 &&
                      !isChangeAva(row) &&
                      !isOtherMaterial
                    "
                    :clearable="false"
                    v-model.trim="row.marketPrice"
                    type="text"
                    @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                  ></vxe-input>
                  <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
                </template>
                <vxe-input
                  v-else-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                  :clearable="false"
                  v-model.trim="row.priceMarketTax"
                  type="text"
                  @blur="
                    row.priceMarketTax = pureNumber(row.priceMarketTax, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarketTax
                }}</span>
              </template>
              <template v-else-if="column.field === 'taxRate'">
                <vxe-input
                  v-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial &&
                    !row.de2012In2022 &&
                    !isDeType('12', row.deStandardReleaseYear)
                  "
                  :clearable="false"
                  v-model.trim="row.taxRate"
                  type="text"
                  @blur="
                    row.taxRate = pureNumber(row.taxRate, 2);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{
                  getValueByDeType('12', row, column.field)
                }}</span>
              </template>
              <template v-else-if="column.field === 'taxRemoval'">
                <span v-if="isDeType('22', row.deStandardReleaseYear)">/</span>
                <vxe-input
                  v-else-if="row.type !== '人工费'"
                  :clearable="false"
                  v-model.trim="row.taxRemoval"
                  type="text"
                  :maxlength="10"
                  @blur="
                    (row.taxRemoval = pureNumber(row.taxRemoval, 4)), clear()
                  "
                ></vxe-input>
                <span v-else>{{ row.taxRemoval }}</span>
              </template>
              <template v-else-if="column.field === 'ifDonorMaterial'">
                <vxe-select
                  v-if="row.checkIsShow"
                  v-model="row.ifDonorMaterial"
                  @change="CheckboxChange(row, 'ifDonorMaterial')"
                  transfer
                >
                  <vxe-option
                    v-for="item in donorMaterialList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  ></vxe-option>
                </vxe-select>
                <span v-else>{{
                  getDonorMaterialText(row.ifDonorMaterial)
                }}</span>
              </template>
              <template v-else-if="column.field === 'donorMaterialNumber'">
                <vxe-input
                  v-if="row.checkIsShow"
                  :clearable="false"
                  v-model.trim="row.donorMaterialNumber"
                  type="text"
                  @blur="
                    (row.donorMaterialNumber =
                      row.donorMaterialNumber * 1 + ''),
                      clear()
                  "
                  @keyup="
                    row.donorMaterialNumber = row.donorMaterialNumber.replace(
                      /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                      '$1$2.$3'
                    )
                  "
                ></vxe-input>
                <span v-else></span>
              </template>
              <template v-else-if="column.field === 'kindSc'">
                <vxe-select
                  v-model="row.kindSc"
                  :clearable="true"
                  v-if="row.showScxs !== '0'"
                >
                  <!-- transfer -->
                  <vxe-option
                    v-for="item in classOptions"
                    :key="item"
                    :value="item"
                    :label="item"
                  ></vxe-option>
                </vxe-select>
              </template>
              <template v-else-if="column.field === 'transferFactor'">
                <vxe-input
                  v-if="row.showScxs !== '0' && row.kindSc"
                  :clearable="false"
                  v-model.trim="row.transferFactor"
                  type="text"
                  @blur="
                    row.transferFactor = pureNumber(row.transferFactor, 4);
                    clear();
                  "
                ></vxe-input>
                <span v-else>{{ row.transferFactor }}</span>
              </template>
              <template v-else>
                <vxe-input
                  :clearable="false"
                  v-model.trim="row[column.field]"
                  type="text"
                  @blur="clear()"
                ></vxe-input>
              </template>
            </template>
          </vxe-column>
          <template #empty>
            <span style="
                color: #898989;
                font-size: 14px;
                display: block;
                margin: 25px 0;
              ">
              <img :src="getUrl('newCsProject/none.png')" />
            </span>
          </template>
        </vxe-table>
      </template>
      <template #two>
        <div v-if="projectStore.currentTreeInfo.levelType === 1">
          <p class="selectTab">
            <a-radio-group
              v-model:value="selectdTab"
              :style="{ marginBottom: '8px' }"
            >
              <a-radio-button
                :value="item.key"
                v-for="item of activeOptions"
              >{{
                item.tab
              }}</a-radio-button>
            </a-radio-group>
          </p>

          <keep-alive>
            <component
              :is="components.get(selectdTab)"
              @getUpList="getHumanMachineData"
              @upDateMarketPrice="upDateMarketPrice"
              :showInfo="currentInfo"
            ></component>
          </keep-alive>
        </div>
        <machine-service
          v-else
          @getUpList="getHumanMachineData"
          @upDateMarketPrice="upDateMarketPrice"
          :showInfo="currentInfo"
        ></machine-service>
      </template>
    </split>
  </div>
  <common-modal
    className="dialog-comm"
    :title="typeModal"
    :width="typeModal === '载价编辑' ? 1130 : 1020"
    :height="
      typeModal === '载价编辑' ? 560 : typeModal === '载价报告' ? 500 : 530
    "
    v-model:modelValue="reportModel"
    @cancel="cancel"
    @close="reportModel = false"
    :mask="true"
    :lockView="true"
  >
    <!--     :mask="typeModal === '载价编辑' ? false : true"
    :lockView="typeModal === '载价编辑' ? false : true" -->
    <batch-load-price
      v-if="typeModal === '批量载价'"
      @close="close"
    ></batch-load-price>
    <edit-load-price
      v-if="typeModal === '载价编辑'"
      :propsData="propsData"
      @close="close"
    ></edit-load-price>
    <report-load-price v-if="typeModal === '载价报告'"></report-load-price>
    <!-- 载价报告 -->
  </common-modal>

  <!-- 关联定额弹窗 -->
  <quota-popup
    v-if="quotaPopupVisible"
    :levelType="projectStore.currentTreeInfo.levelType"
    :HeaderData="quotaHeaderData"
    @closeDialog="quotaPopupVisible = false"
  ></quota-popup>
  <material-machine-index
    pageFr="rcjSummary"
    v-model:indexVisible="indexVisible"
    :currentMaterialInfo="currentInfo"
    :indexLoading="indexLoading"
    @addChildrenRcjData="() => {}"
    @currentInfoReplace="currentInfoReplace"
  ></material-machine-index>
  <SetMainMaterials
    :tableData="allRCJTableData"
    @successCallback="getHumanMachineData"
    v-model:materialVisible="materialVisible"
  />
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
  <LookupFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
  />
  <!-- 调整市场价系数 -->
  <common-modal
    className="dialog-comm noMask"
    title="调整市场价系数"
    width="300"
    height="200"
    v-model:modelValue="adjustFactor"
    :mask="false"
  >
    <div class="adjustFactorMoadl">
      <!-- <p class="title">
        该功能针对所有选中行进行调整
      </p> -->
      <div>
        <span> 市场价系数： </span>
        <a-input
          v-model:value="marcketFactor"
          placeholder="请输入市场价系数"
          @blur="marcketFactor = selfCheck(marcketFactor, 2, 0, 1000)"
          @keyup="marcketFactor = marcketFactor.replace(/[^\d.]/g, '')"
        />
      </div>
      <p class="footor">
        <a-button @click="adjustFactor = false">取消</a-button>
        <a-button
          type="primary"
          @click="sureOrCancel()"
        >确定</a-button>
      </p>
    </div>
  </common-modal>
  <merge-materials
    ref="mergeMaterialsRef"
    :formData="mergeFormData"
    :list="tableData"
    @refresh="getHumanMachineData"
    @selfTestLocateTable="selfTestLocateTable"
  ></merge-materials>
  <import-excel
    ref="importExcelRef"
    @updateImportData="updateImportData"
    @closeImportExcel="getHumanMachineData"
  ></import-excel>
  <!-- 设置汇总范围 -->
  <set-aggre-scope
    ref="setAggreScopeRef"
    @refresh="getHumanMachineData"
  ></set-aggre-scope>
</template>

<script setup>
import {
  onMounted,
  onUpdated,
  ref,
  toRaw,
  watch,
  getCurrentInstance,
  provide,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  watchEffect,
  onActivated,
  onDeactivated,
  markRaw,
} from 'vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '../../../../store/projectDetail';
import feePro from '@/api/feePro';
import loadApi from '../../../../api/loadPrice';
import infoMode from '@/plugins/infoMode.js';
import MachineService from './MachineService.vue';
import SourceAnalysis from './SourceAnalysis.vue';
import MergeMaterials from './mergeMaterials.vue';
import { disposeDeTypeData, setGlobalLoading } from '@/hooks/publicApiData';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail';
import { updateOperateByName } from '../operate';
import { getUrl, pureNumber } from '@/utils/index';
import HumanHeader from './HumanHeader.vue';
import { insetBus } from '@/hooks/insetBus';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
import operateList from '../operate';
import BatchLoadPrice from './BatchLoadPrice.vue';
import EditLoadPrice from './EditLoadPrice.vue';
import ReportLoadPrice from './ReportLoadPrice.vue';
import SetAggreScope from './SetAggreScope.vue';
import mergeMaterials from './mergeMaterials.vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import ImportExcel from './ImportExcel.vue';
import ObjectUtils from '@/components/qdQuickPricing/utils/ObjectUtils';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import { onClickOutside } from '@vueuse/core';
import getTableColumns, {
  isDeType,
  donorMaterialList,
  getDonorMaterialText,
} from './tableColumns';

const projectStore = projectDetailStore();
// 设置主要材料
const SetMainMaterials = defineAsyncComponent(() =>
  import('./SetMainMaterials.vue')
);
const components = markRaw(new Map());
components.set(
  'lyfx',
  defineAsyncComponent(() => import('./SourceAnalysis.vue'))
);
components.set(
  'xxfw',
  defineAsyncComponent(() => import('./MachineService.vue'))
);
// 一、二、三类工
const renGongCodeList = [
  '10000001',
  '10000002',
  '10000003',
  'JXPB-005',
  'R00001',
];
//下表格-两部分
let selectdTab = ref('lyfx');
const activeOptions = reactive([
  {
    key: 'lyfx',
    tab: '来源分析',
  },
  {
    key: 'xxfw',
    tab: '信息价服务',
  },
]);
const isOtherMaterial = computed(() => {
  const { materialCode } = currentInfo.value || {};
  return [
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
    'C11408',
    'C11388',
    'J00006',
    'J00008',
  ].includes(materialCode);
});

/**
 * 获取是否勾选政策文件
 */
const isSelectFeePolicyDoc = ref(false);
const getFeePolicyDocData = () => {
  const isProject = projectStore.currentTreeInfo.levelType === 1;
  let apiData = {
    type: isProject ? 1 : 2,
    constructId: isProject
      ? projectStore.currentTreeInfo?.id
      : projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  feePro.getPolicyDocument(apiData).then(res => {
    console.log('政策文件', apiData, res);
    if (res.status === 200) {
      const { rgfId, awfId, gfId } = res.result;
      isSelectFeePolicyDoc.value = rgfId;
    }
  });
};
const classOptions = ['钢材', '木材', '水泥', '钢筋', '商砼', '商品砂浆'];

let currentInfo = ref(null);
let reportModel = ref(false);
let typeModal = ref(null); //弹框类型
let propsData = ref(); //载价编辑的优先级

import { useCellClick } from '@/hooks/useCellClick';
import { clone } from 'lodash';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();

const quotaPopup = defineAsyncComponent(() =>
  import('@/components/SummaryPopup/index.vue')
);

let unifyData = operateList.value.find(
  item => item.name === 'unify-humanMachineSummary'
);
let isLoad = operateList.value.find(item => item.name === 'batch-loadprice');
// let isSeeReport = operateList.value.find(
//   item => item.name === 'loadprice-report'
// );
let quotaHeaderData = ref(null);
let isCurrent = ref(null);
let colorUnitList = ref([]);
let oldData = ref([]); //工程项目级别人材机汇总旧数据
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});

// 设置主要材料
let materialVisible = ref(false);
let allRCJTableData = ref([]);
const openSetMainMaterial = () => {
  materialVisible.value = true;
};
const closeSetMainMaterial = () => {
  materialVisible.value = false;
};

// 人材机索引
let indexVisible = ref(false);
// 人材机索引数据loading
let indexLoading = ref(false);
/**
 * 菜单右键替换数据处理
 */
const menuReplaceHandler = () => {
  indexVisible.value = true;
};
/**
 * 关闭替换人材机索引
 */
const closeReplaceRCJ = () => {
  indexVisible.value = false;
};

/**
 * 数据替换
 * replaceRcj 被替换的人材机
 * targetRcj 目标人材机
 */
const currentInfoReplace = targetInfo => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    replaceRcj: JSON.parse(JSON.stringify(currentInfo.value)),
    targetRcj: JSON.parse(JSON.stringify(targetInfo)),
  };
  api.replaceRcjToUnit(params).then(res => {
    console.log('人材机数据替换', params, res);
    if (res.status === 200) {
      message.success('替换成功!');
      getHumanMachineData();
      closeReplaceRCJ();
    }
  });
};

let humanTable = ref();
let tableData = ref([]);
let upDateRow = ref();
const selectOptions = [
  { type: '主材费' },
  { type: '材料费' },
  { type: '设备费' },
];

// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    projectStore.deStandardReleaseYear === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};
const quotaPopupVisible = ref(false); // 关联定额弹窗
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'search',
          name: '查询关联定额',
          visible: true,
          disabled: false,
        },
        {
          code: 'replace',
          name: '替换数据',
          visible: true,
          disabled: false,
        },
        {
          code: 'remove',
          name: '清除载价',
          visible: true,
          disabled: false,
        },
        {
          code: 'export',
          name: '导出excel',
          visible: true,
          disabled: false,
        },
        {
          code: 'pageColumnSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, row);
    if (!row) return;

    options[0].find(item => item.code === 'remove').disabled =
      !row.highlight || row.sourcePrice == '自行载价';
    //22人材机也可以清除载价-bug20487
    // if (row.libraryCode?.startsWith('2022')) {
    //   options[0].find(item => item.code === 'remove').visible = false;
    //   // console.log('options[0][1]', options[0][1]);
    // }
    let replaceInfo = options[0].find(item => item.code === 'replace');
    replaceInfo.visible = !!projectStore.currentTreeGroupInfo?.singleId;
    replaceInfo.disabled = !!row.rcjmx || row.isFyrcj === 0;
    return true;
  },
});

// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (!row) return;
  humanTable.value.setCurrentRow(row);
  currentInfo.value = row;
  switch (menu.code) {
    case 'search':
      quotaHeaderData.value = row;
      quotaPopupVisible.value = true;
      break;
    case 'replace':
      menuReplaceHandler();
      break;
    case 'export':
      exportExcel('all');
      break;
    case 'pageColumnSetting':
      showPageColumnSetting();
      break;
    case 'remove':
      // 掉接口恢复系统默认值
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '该条材料市场价已被锁定，'
            : '是否确定清除选中数据的载价数据？',
        descText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '请取消勾选后再进行清除载价操作'
            : '删除后无法撤销恢复',
        isFunction: false,
        confirm: () => {
          if (!row.ifLockStandardPrice || !row.cusTomIfLockStandardPrice) {
            clearZaijia(row);
          }
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
      break;
  }
};

// 清除载价格
const clearZaijia = data => {
  let postData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    rcj: { ...data },
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: data.sequenceNbr,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    postData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  console.log(
    '🚀 ~ file: index.vue:768 ~ csProject.clearLoadPriceUse ~ postData:',
    postData
  );
  csProject
    .clearLoadPriceUse(JSON.parse(JSON.stringify(postData)))
    .then(res => {
      console.log(
        '🚀 ~ file: index.vue:760 ~ csProject.clearLoadPriceUse ~ res:',
        res
      );
      if (res.result) {
        message.success('清除成功');
        if (projectStore.currentTreeInfo.levelType === 1) {
          data.isChange = true; //标识编辑行
          setProUpdate();
          unifyData.disabled = false;
        }
        getHumanMachineData();
      }
    });
};
const setProUpdate = (updataData = []) => {
  projectStore.SET_HUMAN_UPDATA_DATA({
    isEdit: true,
    name: 'unify-humanMachineSummary',
    updataData: updataData,
    adjustFactor: projectStore.humanUpdataData?.adjustFactor,
  });
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};
const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table.clearEdit();
};
const upDateMarketPrice = row => {
  //工程项目更新载价市场价
  let target = tableData.value.find(
    a => a.sequenceNbr === currentInfo.value.sequenceNbr
  );
  if (
    projectStore.deStandardReleaseYear === '12' ||
    (target.deStandardReleaseYear === '12' &&
      projectStore.deStandardReleaseYear === '22')
  ) {
    target.marketPrice = row.marketPrice;
  } else {
    if (Number(projectStore?.taxMade) === 1) {
      target.priceMarket = row.marketPrice;
      target.priceMarketTax = NumberUtil.numberScale2(
        NumberUtil.multiply(
          0.01,
          NumberUtil.multiply(
            target.priceMarket,
            NumberUtil.add(100, target.taxRate)
          )
        )
      );
    } else {
      //  简易----含税
      target.priceMarketTax = row.marketPrice;
      target.priceMarket = NumberUtil.numberScale2(
        NumberUtil.multiply(
          100,
          NumberUtil.divide(
            target.priceMarketTax,
            NumberUtil.add(100, target.taxRate)
          )
        )
      );
    }
  }
  target.sourcePrice = row.sourcePrice;
  target.isExecuteLoadPrice = true;
  target.isChange = true; //标识编辑行
  getSameUnit();
  let upDateList = getPropData();
  if (upDateList && upDateList.length > 0) {
    setProUpdate(upDateList);
  }
};
const editClosedEvent = async e => {
  const { $table, row, column } = e;
  let field = column.field;
  // 选择重复调用处理
  if (
    [
      'markSum',
      'ifDonorMaterial',
      'ifProvisionalEstimate',
      'ifLockStandardPrice',
    ].includes(field)
  ) {
    return;
  }

  // 市场价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (isMarketPriceField(row, column.field)) {
    field = 'marketPrice';
    row[field] = +row[field];
  }

  let value = row[field];
  if (
    ['marketPrice', 'priceMarket', 'priceMarketTax', 'taxRate'].includes(
      field
    ) &&
    (value < 0 || value == '')
  ) {
    $table.revertData(row, field);
    return;
  }
  // // 判断单元格值没有修改
  if (['kindSc'].includes(field) && row.kindSc === row.oldKindSC) {
    return;
  }
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (
    field === 'transferFactor' &&
    (value < 0 || value > 1000 || value === '')
  ) {
    message.info('三材系数可输入范围[0-1000]');
    $table.revertData(row, field);
    return;
  }
  switch (field) {
    case 'unit':
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  if (field === 'marketPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field === 'marketPrice' &&
    value > 0 &&
    row.marketPrice.length > 20
  ) {
    row.marketPrice = value.slice(0, 20);
  }
  if (
    field === 'marketPrice' &&
    value > 0 &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (
      Number(row.marketPrice) - Number(row.dePrice)
    ).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (field === 'donorMaterialNumber') {
    if (row.ifDonorMaterial === 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (row.ifDonorMaterial !== 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (
      row.ifDonorMaterial !== 1 &&
      value <= row.totalNumber &&
      value > 0
    ) {
      row.ifDonorMaterial = 1;
    } else if (row.ifDonorMaterial === 1 && (value <= 0 || value === '')) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = '';
    }
    let same = oldData.value.find(l => l.sequenceNbr === row.sequenceNbr);
    console.log(same, row.donorMaterialNumber, same.donorMaterialNumber);
    if (
      ['0', ''].includes(row.donorMaterialNumber) &&
      ['0', ''].includes(same.donorMaterialNumber)
    ) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = '';
      return;
    }
  }
  if (!(await isEditRenGongMarketPrice(field, row))) {
    $table.revertData(row, field);
    return;
  }
  if (field === 'kindSc') {
    row['kindSc'] = [null, '', undefined].includes(value) ? '' : value;
    row.oldKindSC = row['kindSc'];
    if (projectStore.currentTreeInfo.levelType === 1) {
      row['transferFactor'] = ![null, '', undefined].includes(value) ? '1' : '';
    }
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    console.log('🚀 ~ filnt ~ upDate:', row);
    upDate(row, field);
  } else {
    if (['priceMarket', 'priceMarketTax', 'taxRate'].includes(field)) {
      if (field === 'priceMarket' && !ObjectUtils.is_Undefined(value)) {
        //不含税市场价
        row.priceMarketTax = NumberUtil.numberScale2(
          NumberUtil.multiply(
            0.01,
            NumberUtil.multiply(value, NumberUtil.add(100, row.taxRate))
          )
        );
      }
      //含税市场价
      if (field === 'priceMarketTax' && !ObjectUtils.is_Undefined(value)) {
        //不含税市场价
        row.priceMarket = NumberUtil.numberScale2(
          NumberUtil.multiply(
            100,
            NumberUtil.divide(value, NumberUtil.add(100, row.taxRate))
          )
        );
      }

      //税率
      if (field === 'taxRate' && !ObjectUtils.is_Undefined(value)) {
        //含税市场价
        row.priceMarketTax = NumberUtil.numberScale2(
          NumberUtil.multiply(
            0.01,
            NumberUtil.multiply(row.priceMarket, NumberUtil.add(100, value))
          )
        );
      }
    }
    if (
      ['priceMarket', 'priceMarketTax', 'taxRate', 'marketPrice'].includes(
        field
      ) &&
      row?.isExecuteLoadPrice
    ) {
      row.sourcePrice = row.oldSourcePrice;
      row.isExecuteLoadPrice = false;
    }
    if (['marketPrice', 'priceMarket', 'priceMarketTax'].includes(field)) {
      //isImportDataType为true，是导入excel市场价手动设置调用的线下数据导入，不可修改为自行询价
      if (!row.isImportDataType) {
        //bug20364   工程市场价修改-价格来源更改为自行询价
        row.sourcePrice = '自行询价';
      }
    }
    row.isChange = true; //标识编辑行
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      setProUpdate(upDateList);
    }
  }
};
// 是否修改人工1、2、3类工市场价
const isEditRenGongMarketPrice = (field, row) => {
  return new Promise(resolve => {
    if (
      ['marketPrice'].includes(field) &&
      isSelectFeePolicyDoc.value &&
      renGongCodeList.includes(row.materialCode) &&
      !row.isImportDataType
    ) {
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText: '该市场价已受政策文件调整，是否确认修改',
        isFunction: false,
        confirm: () => {
          resolve(true);
          infoMode.hide();
        },
        close: () => {
          resolve(false);
          infoMode.hide();
        },
      });
    } else {
      resolve(true);
    }
  });
};
const getPropData = () => {
  let constructProjectRcjList = [];
  let upDateList = tableData.value.filter(item => item.isChange === true);
  upDateList.map(item => {
    let obj = {};
    let same = oldData.value.filter(l => l.sequenceNbr === item.sequenceNbr)[0];
    if (item.marketPrice !== same.marketPrice) {
      obj.marketPrice = item.marketPrice;
    }
    // debugger;
    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial === 1) {
        obj.donorMaterialNumber = item.totalNumber;
      } else {
        obj.donorMaterialNumber = '';
      }
    }
    if (item.ifProvisionalEstimate != same.ifProvisionalEstimate) {
      obj.ifProvisionalEstimate = item.ifProvisionalEstimate;
    }
    if (item.ifLockStandardPrice != same.ifLockStandardPrice) {
      obj.ifLockStandardPrice = item.ifLockStandardPrice;
    }
    if (item.kindSc != same.kindSc) {
      obj.kindSc = item.kindSc;
    }
    if (item.transferFactor != same.transferFactor) {
      obj.transferFactor = item.transferFactor;
    }
    if (item.markSum !== same.transferFactor) {
      obj.markSum = item.markSum;
    }
    if (
      obj.hasOwnProperty('ifDonorMaterial') &&
      !obj.hasOwnProperty('donorMaterialNumber')
    ) {
      obj.donorMaterialNumber =
        obj.ifDonorMaterial === 1 ? item.totalNumber : '';
    }
    if (item.taxRemoval != same.taxRemoval) {
      obj.taxRemoval = item.taxRemoval;
    }
    if (item.priceMarket != same.priceMarket) {
      obj.priceMarket = item.priceMarket;
    }
    if (item.priceMarketTax != same.priceMarketTax) {
      obj.priceMarketTax = item.priceMarketTax;
    }
    if (item.taxRate != same.taxRate) {
      obj.taxRate = item.taxRate;
    }
    if (item.sourcePrice != same.sourcePrice) {
      obj.sourcePrice = item.sourcePrice;
    }
    if (item.isExecuteLoadPrice) {
      obj.isExecuteLoadPrice = true;
    }
    obj.sequenceNbr = item.sequenceNbr;
    obj.libraryCode = item.libraryCode;
    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const isUse = async () => {
  //点击统一应用按钮
  if (!projectStore.humanUpdataData) {
    return;
  }

  // let tar = apiData.constructProjectRcjList[0];
  // if (
  //   Object.keys(tar).length === 1 &&
  //   apiData.constructProjectRcjList.length === 1
  // ) {
  //   apiData.constructProjectRcjList = [];
  // }

  //只是清除载价就传空值，清除载价+改市场价传修改数据
  setGlobalLoading(true, '统一应用中，请稍后...');
  if (projectStore.humanUpdataData.adjustFactor?.isEdit) {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      coefficient: projectStore.humanUpdataData.adjustFactor.marcketFactor * 1,
      rcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.adjustFactor.selectRows)
      ),
    };
    console.log('constructAdjustmentCoefficient', apiData);
    await csProject.constructAdjustmentCoefficient(apiData).then(res => {
      console.log('统一应用系数', res);
    });
  }
  if (projectStore.humanUpdataData.updataData) {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.updataData)
      ),
    };
    console.log('统一应用接口参数', apiData);
    await csProject.changeRcjConstructProject(apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (projectStore.humanUpdataData.sourcePriceData) {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.sourcePriceData)
      ),
    };
    console.log('统一应用接口参数', apiData);
    await feePro.rcjFromUnitUpdate(apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  message.success('应用成功!');
  projectStore.SET_HUMAN_UPDATA_DATA(null);
  getHumanMachineData();
  unifyData.disabled = true;
  setGlobalLoading(false);
};
const upDate = (row, field) => {
  let apiData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    constructProjectRcj: {},
  };
  const upDateField = [
    'transferFactor',
    'kindSc',
    'marketPrice',
    'materialName',
    'specification',
    'unit',
    'ifProvisionalEstimate',
    'ifLockStandardPrice',
    'markSum',
    'donorMaterialNumber',
    'producer',
    'manufactor',
    'brand',
    'deliveryLocation',
    'qualityGrade',
    'output',
    'taxRemoval',
    'priceMarket',
    'priceMarketTax',
    'taxRate',
    'dispNo',
  ];
  if (upDateField.includes(field)) {
    apiData.constructProjectRcj[field] = row[field];
  } else if (field === 'type') {
    apiData.constructProjectRcj.kind = getKind(row.type);
  } else if (field === 'ifDonorMaterial') {
    apiData.constructProjectRcj.ifDonorMaterial = row[field];
    // apiData.constructProjectRcj.donorMaterialNumber = row.totalNumber;
  }
  apiData.libraryCode = row.libraryCode;
  console.log('修改人材机数据', apiData);
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  setGlobalLoading(true);
  csProject
    .changeRcj(apiData)
    .then(res => {
      if (res.status === 200) {
        console.log('修改人材机数据返回结果', res);
        isCurrent.value = row;
        getHumanMachineData();
      }
    })
    .finally(() => {
      setGlobalLoading(false);
    });
};

// =====================查找逻辑
let lookupVisible = ref(false);
const openLookup = event => {
  // console.log(event);
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};
let originalTableData = []; // 查找之前的源数据
const lookupConfig = reactive({
  columns: [
    {
      field: 'materialName',
      label: '名称',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'specification',
      label: '规格',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'materialCode',
      label: '编码',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'marketPrice',
      label: '市场价',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'totalNumber',
      label: '数量',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
  ],
  conditionType: '&&',
  tableData: tableData,
});
const lookupCallback = rows => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData, true);
  } else {
    tableData.value = rows;
  }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    humanTable.value.setCurrentRow(info);
    currentInfo.value = info;
  });
};

const changeCurrentInfo = row => {
  if (row) {
    humanTable.value.setCurrentRow(row);
    currentInfo.value = row;
  }
};

const exportBeforeColumnHandler = () => {
  // 导出前将必须导出得列添加进去，进行隐藏处理
  const $table = humanTable.value;
  let necessaryColumns = [
    {
      title: '材料编码',
      field: 'materialCode',
      fixed: 'left',
    },
    {
      title: '名称',
      field: 'materialName',
      fixed: 'left',
    },
    {
      title: '规格型号',
      field: 'specification',
    },
    {
      title: '类型',
      field: 'type',
      fixed: 'left',
    },
    {
      title: '单位',
      field: 'unit',
    },
  ];
  if (isDeType('12')) {
    necessaryColumns = [
      ...necessaryColumns,
      { title: '定额价', field: 'dePrice' },
      { title: '市场价', field: 'marketPrice' },
      { title: '除税系数(%)', field: 'taxRemoval' },
    ];
  } else {
    necessaryColumns = [
      ...necessaryColumns,
      {
        title:
          Number(projectStore.taxMade) === 1 ? '不含税基期价' : '含税基期价',
        field: 'dePrice',
      },
      { title: '不含税市场价', field: 'priceMarket' },
      { title: '含税市场价', field: 'priceMarketTax' },
      { title: '税率', field: 'taxRate' },
    ];
  }
  let currentHideColumns = [];
  for (let item of necessaryColumns) {
    if (!showColumns.value.find(i => i.field === item.field)) {
      showColumns.value.push(item);
      currentHideColumns.push(item.field);
      nextTick(() => {
        $table.hideColumn(item.field);
      });
    }
  }
  return currentHideColumns;
};
const exportExcel = (dataType = '') => {
  const $table = humanTable.value;
  if (dataType !== 'all' && $table.getCheckboxRecords().length === 0) {
    message.info('请选择导出数据');
    return;
  }
  const hideColumns = exportBeforeColumnHandler();
  setTimeout(() => {
    $table.exportData({
      filename: '人材机汇总导出报表',
      sheetName: 'Sheet1',
      type: 'xlsx',
      // types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
      // sheetMethod: sheetMethod, // 配置导出样式
      useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
      isFooter: true, //是否导出表尾（比如合计）
      columns: $table.getTableColumn().fullColumn,
      data: dataType === 'all' ? tableData.value : $table.getCheckboxRecords(),
      columnFilterMethod({ column, $columnIndex }) {
        return !($columnIndex === 0);
      },
      afterExportMethod: () => {
        // 还原列
        for (let item of hideColumns) {
          const index = showColumns.value.findIndex(
            i => i.field === item.field
          );
          if (index >= 0) {
            showColumns.value.splice(index, 1);
            nextTick(() => {
              $table.showColumn(field);
            });
          }
        }
      },
    });
  }, 500);
};
const loadPrice = type => {
  reportModel.value = false;
  switch (type) {
    case 'batch-loadprice':
      typeModal.value = '批量载价';
      break;
    case 'loadprice-report':
      typeModal.value = '载价报告';
      break;
  }
  reportModel.value = true;
  console.log('执行loadPrice', reportModel.value);
};
const close = bol => {
  reportModel.value = false;
  console.log('执行close', reportModel.value);
};
let zjUse = ref(false); //工程项目载价后需要统一应用--未载价默认false
let zjData = ref([]); //载价数据
let saveChangeRow = ref([]); //原表格修改数据
const nextEdit = async data => {
  propsData.value = data;
  typeModal.value = data.nextType;
  reportModel.value = true;
  if (typeModal.value === '载价报告') {
    //工程项目载价成功后需要统一应用
    if (projectStore.currentTreeInfo.levelType === 1) {
      let formData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
      };
      let apiFun = loadApi.loadPriceList;
      apiFun(formData).then(res => {
        let list = tableData.value.filter(a => a.isChange);
        if (res.status === 200 && res.result) {
          zjData.value = res.result;
          if (zjData.value?.length > 0) {
            //如果载价成功且有数据-需要重新调用接口并将当前数据保存
            zjUse.value = true;
            list = list.filter(
              a => !zjData.value.find(b => b.sequenceNbr === a.sequenceNbr)
            );
            saveChangeRow.value = JSON.parse(JSON.stringify(list));
            getHumanMachineData();
          }
        }
      });
    } else {
      getHumanMachineData();
    }
  }
  console.log('执行nextEdit', reportModel.value);
};
provide('nextStep', nextEdit);
let adjustFactor = ref(false); //调整市场价系数
let marcketFactor = ref('1');
const selfCheck = (value, length, min, max) => {
  // length-小数点长度   min-最小值  max-最大值
  let newValue = value * 1 + '';
  if (newValue === '') return oldMarketFactor.value;
  if (newValue <= min || newValue > max) {
    newValue = oldMarketFactor.value;
    message.info('市场价系数输入范围为(0,1000])');
  }
  let after = newValue.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newValue = parseFloat(newValue).toFixed(length);
  }
  oldMarketFactor.value = newValue;
  return newValue;
};
const changeMarketFactor = () => {
  //调整市场价系数
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    coefficient: marcketFactor.value * 1,
    rcjList: JSON.parse(JSON.stringify(humanTable.value.getCheckboxRecords())),
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  console.log('调整市场价系数', apiData);
  let apiName =
    projectStore.currentTreeInfo.levelType === 3
      ? 'unitAdjustmentCoefficient'
      : '';
  csProject[apiName](apiData).then(res => {
    if (res.status === 200) {
      console.log('调整市场价系数返回结果', res);
      getHumanMachineData();
    }
  });
};
const clickOutside = () => {
  if (adjustFactor.value) {
    console.log('点击人材机汇总表格外部'); //暂时不需要设置-后续人材机汇总左侧树可能需要
    return;
  }
};
onClickOutside(humanTable, clickOutside); //打开调整市场价系数表格可编辑
const sureOrCancel = () => {
  let hasCheck =
    humanTable.value.getCheckboxRecords().length === 0 ? false : true;
  if (!hasCheck) {
    message.warning('请选中要调整的人材机数据行');
    // infoMode.show({
    //   isSureModal: true,
    //   iconType: 'icon-querenshanchu',
    //   infoText: '请选中要调整的人材机数据行',
    //   confirm: () => {
    //     infoMode.hide();
    //   },
    // });
  }
  if (!hasCheck) return;

  if (projectStore.currentTreeInfo.levelType === 3 && hasCheck) {
    changeMarketFactor();
  } else {
    let selectRows = humanTable.value.getCheckboxRecords();
    selectRows.map(row => {
      if (
        !(
          (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) ||
          row.isFyrcj === 0 ||
          [
            'QTCLFBFB',
            '34000001-2',
            'J00004',
            'J00031',
            'J00031',
            'C11384',
            'C00007',
            'C000200',
            'C11408',
          ].includes(row.materialCode)
        )
      ) {
        //1. 注1：勾选了二次解析的配比材料、机械台班不受调整系数影响，但其市场价会因子级数据变更而联动变更（当前勾选了二次解析的父级材料、机械不可修改市场价，其值仅会通过子级数据是市场价变更而联动计算）
        //2. 注2：其他材料费、费用人材机即使勾选，也不受人材机调整系数影响；
        if (
          projectStore.deStandardReleaseYear === '12' ||
          (row.deStandardReleaseYear === '12' &&
            projectStore.deStandardReleaseYear === '22')
        ) {
          row.marketPrice = (row.marketPrice * marcketFactor.value).toFixed(2);
        } else {
          if (Number(projectStore.taxMade) === 1) {
            //一般计税: 不含税市场价影响含税
            row.priceMarket = (row.marketPrice * marcketFactor.value).toFixed(
              2
            );
            row.priceMarketTax = NumberUtil.numberScale2(
              NumberUtil.multiply(
                0.01,
                NumberUtil.multiply(
                  row.priceMarket,
                  NumberUtil.add(100, row.taxRate)
                )
              )
            );
          } else {
            // 简易计税 : 含税市场价影响不含税
            row.priceMarketTax = (
              row.marketPrice * marcketFactor.value
            ).toFixed(2);
            row.priceMarket = NumberUtil.numberScale2(
              NumberUtil.multiply(
                100,
                NumberUtil.divide(
                  row.priceMarketTax,
                  NumberUtil.add(100, row.taxRate)
                )
              )
            );
          }
        }
        row.isChange = true; //标识编辑行
        row.isUpdateNum = true; //调整市场价系数
        row.sourcePrice = '自行询价';
      }
    });
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      setProUpdate(upDateList);
    }
    // projectStore.SET_HUMAN_UPDATA_DATA({
    //   isEdit: true,
    //   name: 'unify-humanMachineSummary',
    //   updataData: projectStore.humanUpdataData?.updataData,
    //   adjustFactor: {
    //     isEdit: projectStore.currentTreeInfo.levelType === 1 ? true : false,
    //     marcketFactor: marcketFactor.value,
    //     selectRows: selectRows,
    //   },
    // });
    // console.log(projectStore.humanUpdataData);
  }
  adjustFactor.value = false;
};
let oldMarketFactor = ref('1'); //调整市场价系数旧值
const jsz = () => {
  marcketFactor.value = '1';
  oldMarketFactor.value = '1';
  adjustFactor.value = true;
};

// 初始统一修改操作按钮属性
const initSetOperateBtnDisabled = () => {
  const list = [
    {
      // 设置导入Excel市场价是否可操作
      name: 'importExcel',
      callback: item => {
        item.disabled = Number(projectStore.asideMenuCurrentInfo.key) !== 0;
      },
    },
    {
      // 设置主要材料显示隐藏
      name: 'set-main-materials',
      callback: item => {
        item.levelType =
          Number(projectStore.asideMenuCurrentInfo.key) === 7 ? [3] : [];
      },
    },
  ];
  list.forEach(item => {
    updateOperateByName(item.name, item.callback);
  });
};
watch(
  () => [projectStore.asideMenuCurrentInfo, projectStore.levelType],
  () => {
    if (
      projectStore.currentTreeInfo.levelType !== 2 &&
      projectStore.tabSelectName === '人材机汇总'
    ) {
      //侧边栏数据变化重新更新
      sortFiled.value = '';
      sortVal.value = false;
      initColumns({
        columns: getTableColumns(),
        pageName: `rcjhz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
      });
      getHumanMachineData();
      getLoadStatus();
      initSetOperateBtnDisabled();
    }
    setScopeBtn();
  }
);
const setScopeBtn = () => {
  //设置汇总范围功能按钮置灰状态
  // if (projectStore.currentTreeInfo.levelType !== 1) return;
  // let settingScope = operateList.value.find(
  //   item => item.name === 'setting-aggregate-scope'
  // );
  // if (projectStore.asideMenuCurrentInfo?.name === '所有人材机') {
  //   settingScope.disabled = false;
  // } else {
  //   settingScope.disabled = true;
  // }
};
watch(
  () => [projectStore.currentTreeInfo, projectStore.tabSelectName],
  () => {
    if (
      projectStore.currentTreeInfo.levelType !== 2 &&
      projectStore.tabSelectName === '人材机汇总'
    ) {
      //侧边栏数据变化重新更新
      currentInfo.value = null;
      isCurrent.value = null;
      getHumanMachineData();
    }
  }
);
watch(
  () => projectStore.humanUpdataData,
  () => {
    if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
      unifyData.disabled = false;
    } else if (!projectStore.humanUpdataData) {
      unifyData.disabled = true;
    }
  }
);

const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
      'C11408',
      'C11388',
      'J00006',
      'J00008',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});

onMounted(() => {
  if (
    projectStore.currentTreeInfo.levelType !== 2 &&
    projectStore.tabSelectName === '人材机汇总' &&
    projectStore.asideMenuCurrentInfo?.key === '0'
  ) {
    initColumns({
      columns: getTableColumns(),
      pageName: `rcjhz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
    });
    getHumanMachineData();
  }
  getLoadStatus();
  setScopeBtn();
});

onActivated(() => {
  console.log('onActivated');
  insetBus(bus, projectStore.componentId, 'humanMachineSummary', async data => {
    if (data.name === 'batch-loadprice')
      console.log('执行载价'), loadPrice(data.name);
    if (data.name === 'loadprice-report')
      console.log('载价报告'), loadPrice(data.name);
    if (data.name === 'market-price') console.log('调整市场价系数'), jsz();
    if (data.name === 'unify-humanMachineSummary') {
      isUse();
    }
    if (data.name === 'export-table') console.log('导出报表'), exportExcel();
    if (data.name === 'set-main-materials') openSetMainMaterial();
    if (data.name === 'lookup') openLookup();
    if (data.name === 'importExcel') {
      importExcelHandle();
    }
    if (data.name === 'mergeMaterials') openMergeMaterials();
    if (data.name === 'setting-aggregate-scope') {
      setAggreScopeFun();
    }
  });
  getFeePolicyDocData();
  window.addEventListener('keydown', openLookup);
});
let setAggreScopeRef = ref();
const setAggreScopeFun = () => {
  setAggreScopeRef.value.open(true);
};
let mergeMaterialsRef = ref();
const openMergeMaterials = () => {
  mergeMaterialsRef.value.open(true);
};
let importExcelRef = ref();
const importExcelHandle = () => {
  importExcelRef.value.open();
};

/**
 * 工程级别导入数据前端更新处理
 */
const updateImportData = updateRcj => {
  for (let rcj of updateRcj) {
    let data = tableData.value.find(
      item => item.sequenceNbr === rcj.sequenceNbr
    );
    data[rcj.updatePrice] = rcj[rcj.updatePrice];
    data.sourcePrice = '线下数据导入';
    data.isImportDataType = true;
    editClosedEvent({
      $table: humanTable.value,
      row: data,
      column: { field: rcj.updatePrice },
    });
  }
};

onDeactivated(() => {
  console.log('onDeactivated');
  lookupVisible.value = false;
  window.removeEventListener('keydown', openLookup);
});
const getLoadStatus = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }
  loadApi.loadPriceStatus(apiData).then(res => {
    console.log('++++++++++++++', res);
    // isSeeReport.disabled = false;
    if (res.result && res.result.includes(5)) {
      isLoad.disabled = true;
    } else if (res.result && res.result.includes(3)) {
      // isSeeReport.disabled = true;
    } else if (res.result && res.result.includes(4)) {
      feePro.isOnline().then(res => {
        console.log('判断是否有网------', res);
        if (res.result) {
          isLoad.disabled = false;
        } else {
          message.error('请连接网络后使用！');
        }
      });
    }
  });
};
const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.map(item => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum === 1 && [1, 2].includes(Number(item.levelMark))) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};
const getKind = type => {
  let value;
  switch (type) {
    case '其他费':
      value = 0;
      break;
    case '人工费':
      value = 1;
      break;
    case '材料费':
      value = 2;
      break;
    case '机械费':
      value = 3;
      break;
    case '设备费':
      value = 4;
      break;
    case '主材费':
      value = 5;
      break;
    case '商砼':
      value = 6;
      break;
    case '砼':
      value = 7;
      break;
    case '浆':
      value = 8;
      break;
    case '商浆':
      value = 9;
      break;
    case '配比':
      value = 10;
      break;
  }
  return value;
};
const getType = type => {
  let value = '';
  switch (type) {
    case 0:
      value = '其他费';
      break;
    case 1:
      value = '人工费';
      break;
    case 2:
      value = '材料费';
      break;
    case 3:
      value = '机械费';
      break;
    case 4:
      value = '设备费';
      break;
    case 5:
      value = '主材费';
      break;
    case 6:
      value = '商砼';
      break;
    case 7:
      value = '砼';
      break;
    case 8:
      value = '浆';
      break;
    case 9:
      value = '商浆';
      break;
    case 10:
      value = '配比';
      break;
  }
  return value;
};
const getOldData = () => {
  tableData.value &&
    tableData.value.map(item => {
      oldData.value.push({
        sequenceNbr: item.sequenceNbr,
        marketPrice: item.marketPrice,
        ifDonorMaterial: item.ifDonorMaterial,
        donorMaterialNumber: item.donorMaterialNumber,
        ifProvisionalEstimate: item.ifProvisionalEstimate,
        ifLockStandardPrice: item.ifLockStandardPrice,
        kindSc: item.kindSc,
        transferFactor: item.transferFactor,
        taxRemoval: item.taxRemoval,
        priceMarket: item.priceMarket,
        priceMarketTax: item.priceMarketTax,
        taxRate: item.taxRate,
      });
    });
  console.log('getOldData', oldData.value);
};
const setCurrentInfo = () => {
  //设置当前选中行
  if (isCurrent.value) {
    let isCurrentRow = tableData.value.find(
      item => isCurrent.value.sequenceNbr === item.sequenceNbr
    );
    humanTable.value.setCurrentRow(isCurrentRow || tableData.value[0]);
  } else {
    humanTable.value.setCurrentRow(tableData.value && tableData.value[0]);
    isCurrent.value = humanTable.value.getCurrentRecord();
  }
  currentInfo.value = humanTable.value.getCurrentRecord();
  //设置复选框的置灰状态
  checkBoxIsShow();
};
let mergeFormData = ref({});
const getHumanMachineData = (type = '') => {
  setGlobalLoading(true);
  changeMergeMaterialsDisabled();
  const kind = Number(projectStore.asideMenuCurrentInfo?.key);
  let formData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    kind,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    formData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    formData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  if (type == 2) {
    formData.sort = {
      field: sortFiled.value,
      order: sortVal.value ? 'asc' : 'desc',
    };
  }
  mergeFormData.value = formData;
  console.log('queryConstructRcjByDeId', formData);
  feePro
    .queryConstructRcjByDeId(formData)
    .then(res => {
      if (res.status === 200 && res.result && res.result.length > 0) {
        // let num = 1;
        res.result &&
          res.result.map((item, index) => {
            // item.dispNo = num++;
            item.type = getType(item.kind);
            item.donorMaterialNumber =
              Number(item.donorMaterialNumber) === 0
                ? ''
                : item.donorMaterialNumber;
            item.origindonorMaterialNum = item.donorMaterialNumber
              ? item.donorMaterialNumber
              : '0';
            if ([undefined, null].includes(item.kindSc)) {
              item.kindSc = '';
            }
            item.oldKindSC = item.kindSc;
            item.oldSourcePrice = item.sourcePrice;
          });
        // tableData.value = res.result;
        nextTick(() => {
          if (!zjUse.value) {
            tableData.value = disposeDeTypeData(res.result, true, true);
            setCurrentInfo();
            oldData.value = [];
            getOldData();
          } else if (
            zjUse.value &&
            projectStore.currentTreeInfo.levelType === 1
          ) {
            console.log(' zjData.value', zjData.value);
            res.result.map((a, index) => {
              if (
                saveChangeRow.value?.find(c => c.sequenceNbr === a.sequenceNbr)
              ) {
                let row = saveChangeRow.value.find(
                  c => c.sequenceNbr === a.sequenceNbr
                );
                res.result.splice(index, 1, row);
              }
              if (zjData.value.find(b => b.sequenceNbr === a.sequenceNbr))
                a.isChange = true;
            });
            tableData.value = disposeDeTypeData(res.result, true, true);
            setCurrentInfo();
            if (saveChangeRow.value?.length > 0) {
              //获取变更行便于统一应用
              let upDateList = getPropData();
              // debugger;
              if (upDateList && upDateList.length > 0) {
                setProUpdate(upDateList);
              }
            }
            zjUse.value = false;
          }
          if (projectStore.currentTreeInfo.levelType === 1) {
            getSameUnit();
          }
          originalTableData = xeUtils.clone(tableData.value, true);
          if (kind === 0) {
            // 记录所有人材机数据
            allRCJTableData.value = xeUtils.clone(tableData.value, true);
          }
          console.log('人材机汇总数据', tableData.value);
        });
      } else {
        tableData.value = [];
        currentInfo.value = '';
      }
    })
    .finally(() => {
      setGlobalLoading(false);
    });
};
const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  tableData.value &&
    tableData.value.map(item => {
      let otherSameUnit = tableData.value.filter(
        unit =>
          unit.materialCode === item.materialCode &&
          unit.materialName === item.materialName &&
          // unit.unitId === item.unitId &&
          unit.unit === item.unit &&
          unit.specification === item.specification &&
          Number(unit.dePrice) === Number(item.dePrice) &&
          unit.ifDonorMaterial == item.ifDonorMaterial &&
          unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
          unit.ifLockStandardPrice == item.ifLockStandardPrice &&
          unit.sequenceNbr !== item.sequenceNbr &&
          Number(unit.marketPrice) !== Number(item.marketPrice)
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  tableData.value &&
    tableData.value.map(
      item =>
        (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );
  console.log('addColorList', addColorList);
};
const cellTableStyle = ({ row, column }) => {
  if (
    isMarketPriceField(row, column.field) ||
    (projectStore.deType === '22' && ['priceMarket'].includes(column.field))
  ) {
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      return {
        color: '#059421',
        backgroundColor: '#fae4b2',
      };
    }
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
    //22定额不含税市场价和定额价比较标识颜色
    if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
/**
 * 是否在含税/不含税市场价格字段操作市场价字段，只在工程级别得12定额数据下存在
 */
const isPriceMarketOperateMarketPrice = (row, field) => {
  const taxMade = 'priceMarket' === field ? 1 : 0;
  return (
    isDeType('12', row.deStandardReleaseYear) &&
    ['priceMarketTax', 'priceMarket'].includes(field) &&
    Number(projectStore.taxMade) === taxMade
  );
};

/**
 * 是否市场价字段
 * 工程项目级别，12定额数据市场价显示和操作是在含税市场价或者不含税市场价字段上操作得
 * @param row
 * @param field
 */
const isMarketPriceField = (row, field) => {
  return 'marketPrice' === field || isPriceMarketOperateMarketPrice(row, field);
};
const cellStyle = ({ row, column }) => {
  if (column.field === 'marketPrice') {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
  if (column.field === 'priceMarket' && projectStore.taxMade === 1) {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.priceMarket >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarket < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
  if (column.field === 'priceMarketTax' && projectStore.taxMade === 0) {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.priceMarketTax >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarketTax > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.priceMarketTax >= 0 &&
      row.dePrice >= 0 &&
      row.priceMarketTax < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
const rowStyle = ({ row }) => {
  if (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) {
    if (row.field !== 'dispNo') {
      return {
        backgroundColor: '#FFFEC9',
        color: '#ACACAC',
        'font-weight': 'bold',
      };
    }
  }
  if (row.highlight) {
    return {
      backgroundColor: '#FCF8EF',
    };
  }
  if (row.sourcePrice) {
    return {
      backgroundColor: '#FFFEC9',
    };
  }
};
const CheckboxChange = (row, type) => {
  switch (type) {
    case 'markSum':
      row.checkIsShow = row.markSum === 1 ? false : true;
      break;
    case 'ifDonorMaterial':
      break;
    case 'ifProvisionalEstimate':
      break;
    case 'ifLockStandardPrice':
      row.cusTomIfLockStandardPrice = row.ifLockStandardPrice;
      break;
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    upDate(row, type);
  } else if (projectStore.currentTreeInfo.levelType === 1) {
    //工程项目级别
    row.isChange = true; //标识编辑行
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      setProUpdate(upDateList);
    }
    if (type === 'ifDonorMaterial') {
      row.donorMaterialNumber =
        row.ifDonorMaterial === 1 ? row.totalNumber : '';
      humanTable.value.reloadRow(row, {});
    }
  }
};

const getCurrentIndex = (item, type) => {
  console.log('item', item, tableData.value);
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = n;
      }
    });
  } else {
    isCurrent.value = tableData.value[0];
  }
  humanTable.value.setCurrentRow(isCurrent.value);
  console.log('==============', isCurrent);
};

const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
  isCurrent.value = row;
  if (projectStore.currentTreeInfo.levelType === 1) {
    getCurrentIndex(row);
  }
};
const sortFiled = ref('');
const sortVal = ref(false);
// 点击排序
const sortClick = filedId => {
  if (sortFiled.value === filedId) {
    sortVal.value = !sortVal.value;
  } else {
    sortVal.value = true;
  }
  sortFiled.value = filedId;
  getHumanMachineData(2);
};
// 定位方法
const posRow = sequenceNbr => {
  console.log('人材机汇总定位', sequenceNbr);
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  getHumanMachineData();
  setTimeout(() => {
    getCurrentIndex({ sequenceNbr });
  }, 800);
  // currentInfo.value = { sequenceNbr };
};

const getValueByDeType = (deType, row, field = '') => {
  if (isDeType(deType, row.deStandardReleaseYear)) {
    if (['priceMarketTax', 'priceMarket'].includes(field)) {
      const taxMade = 'priceMarket' === field ? 1 : 0;
      if (Number(projectStore.taxMade) === taxMade) {
        //   if (projectStore.currentTreeInfo.levelType === 1) {
        //   return isChangeAva(row) ? '-' : row.marketPrice;
        // }
        return isChangeAva(row) ? '-' : row.marketPrice;
      }
    }
    return '/';
  }
  return isChangeAva(row) ? '-' : row[field];
};
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'ys',
  initCallback: () => {},
  initColumnsCallback: () => {
    initColumns({
      columns: getTableColumns(),
      pageName: `rcjhz_${projectStore.currentTreeInfo.levelType}_${projectStore.deStandardReleaseYear}`,
    });
  },
});
// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};

const changeMergeMaterialsDisabled = () => {
  updateOperateByName('mergeMaterials', item => {
    item.disabled = ![0, 1, 2, 3, 4, 5, 6].includes(
      Number(projectStore.asideMenuCurrentInfo?.key)
    );
  });
};

const selfTestLocateTable = row => {
  let data = tableData.value.find(item => item.sequenceNbr === row.sequenceNbr);
  humanTable.value.setCurrentRow(data);
  humanTable.value.scrollToRow(data);
};

defineExpose({
  getHumanMachineData,
  posRow,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}
.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
  :deep(.background-red) {
    color: #2a2a2a;
    background: #de3f3f;
  }
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }
  .row--current {
    background-color: var(--vxe-table-row-current-background-color) !important;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
.adjustFactorMoadl {
  .title {
    font-size: 14px;
  }
  div {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    // padding: 20px 0px 20px 5px;
    // border: 1px solid #6666;
    .ant-input {
      width: 78%;
    }
    span {
      width: 21%;
      margin: auto;
    }
  }
  .footor {
    display: flex;
    justify-content: space-between;
    width: 150px;
    margin: 10px auto 0;
  }
}
.selectTab {
  background-color: #e7e7e7;
  height: 32px;
  line-height: 30px;
  // padding-left: 20px;
  position: relative;
  border-bottom: 2px solid #e7e7e7;
  margin: 3px 0;
  .label {
    color: grey;
    font-size: 12px;
  }
  .showTitle {
    position: absolute;
    right: 0px;
    top: 0px;
    line-height: 30px;
    height: 30px;
    padding: 0 20px;
    font-size: 12px;
    // background-color: #e7e7e7;
    border-radius: 5px;
  }
  .ant-radio-button-wrapper {
    font-size: 12px;
    background-color: #e7e7e7;
    border: none;
    box-shadow: none;
    // border-radius: 5px;
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
  .ant-radio-button-wrapper-checked {
    // border-color: none;
    background-color: white;
    border: none;
    border-top: 3px solid #4786ff;
    color: black;
    &:hover {
      color: black;
    }
  }
}
</style>
