<!--
 * @Descripttion:章节选择
 * @Author: sunchen
 * @Date: 2023-06-15 13:59:14
 * @LastEditors: wangru
 * @LastEditTime: 2024-09-10 10:09:24
-->
<template>
  <common-modal
      className="select-chapter-wrap"
      title="补充定额"
      width="auto"
      v-model:modelValue="sonVisible"
      @cancel="cancel"
      @close="cancel"
      :mask="true"
      :showHeader="false"
  >
    <div class="my-dropdown">
      <div class="my-dropdown-wrap">
        <span class="title">章节选择</span>
        <a-select
            class="chapter-select"
            v-model:value="props.filedValue"
            :options="props.groupTypeList"
            :field-names="{
            label: 'libraryName',
            value: 'libraryCode',
          }"
            placeholder="请选择"
            @change="selectChange"
        ></a-select>
        <CloseOutlined
            @click="cancel"
            class="out-icon"
            :style="{ fontSize: '16px', color: '#888888' }"
        />
      </div>
      <div class="tree-list">
        <a-tree
            :tree-data="treeData"
            :expandedKeys="expandedKeys"
            :field-names="{
            title: isDeType ? 'name' : 'details',
            children: 'childrenList',
            key: isDeType ? 'key' : 'details',
          }"
            :defaultExpandAll="true"
            @select="selectEvent"
        >
          <template #switcherIcon="{ switcherCls,dataRef }"><down-outlined
              :class="switcherCls"
              @click="expandedFun(dataRef)"
            /></template>
          <template #title="data">
            <a-tooltip placement="rightTop">
              <template #title>{{ handleName(data) }}</template>
              <span class="ellipsis"> {{ handleName(data) }}</span>
            </a-tooltip>
          </template>
        </a-tree>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { reactive, ref, watchEffect } from 'vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { CloseOutlined, DownOutlined } from '@ant-design/icons-vue';

import api from '@/api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  filedValue: {
    required: true,
    type: String,
  },
  groupTypeList: {
    type: Array,
    default: () => [],
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  isDeType: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits([
  'update:visible',
  'update:filedValue',
  'selectChange',
  'selectInfo',
]);

let sonVisible = ref(false);
let expandedKeys = ref([]);
let pulldown = reactive({
  visible: false,
  values: '',
  zj: '',
});

watchEffect(() => {
  sonVisible.value = props.visible;
  if (props.visible) {
    if (!props.isDeType) {
      props.treeData.forEach(item => {
        expandedKeys.value.push(item.details);
      });
    } else {
      defaultExpanded(props.treeData);
    }
  }
});

const handleName = data => {
  console.log('handleName', data);
  const { name, details, detailsCode, sequenceNbr } = data;
  const isDeType = props.isDeType;
  if (isDeType) {
    return name;
  } else {
    return `${detailsCode}  ${details}`;
  }
};

const cancel = () => {
  emit('update:visible', false);
};

const selectChange = e => {
  emit('update:filedValue', e);
  emit('selectChange', e);
};

/**
 * 树结构选择
 * @param {} item
 */
const selectEvent = (item, e) => {
  console.log('🚀 ~ file: index.vue:109 ~ selectEvent ~ item:', item, e);
  if (e.node.dataRef.childrenList && e.node.dataRef.childrenList.length > 0) {
    if (!e.node.expanded) {
      expandedKeys.value.push(e.node.key);
    } else {
      expandedKeys.value.splice(expandedKeys.value.indexOf(e.node.key), 1);
    }
  } else {
    emit('selectInfo', e.node.dataRef);
    emit('update:visible', false);
  }
};
const expandedFun = data => {
  if (expandedKeys.value.includes(data.key)) {
    expandedKeys.value.splice(expandedKeys.value.indexOf(data.key), 1);
  } else {
    expandedKeys.value.push(data.key);
  }
};
const defaultExpanded = tree => {
  return tree.map(item => {
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
        ? defaultExpanded(item.childrenList)
        : null;
    return item;
  });
};
</script>

<style lang="scss">
.select-chapter-wrap {
  .vxe-modal--content {
    padding: 0;
  }
  .pulldown-wrap {
    width: 100%;
  }
  .my-dropdown {
    max-height: 50vh;
    width: 60%;
    max-width: 800px;
    min-width: 600px;
    box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);
    .my-dropdown-wrap {
      position: relative;
      display: flex;
      align-items: center;
      padding: 2px 14px;
      background: rgba(214, 214, 214, 0.39);
      .title {
        margin-left: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
      }
      .chapter-select {
        width: 300px;
        margin-left: 35px;
      }
      .out-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .tree-list {
      max-height: calc(50vh - 50px);
      overflow-y: hidden;
      &:hover {
        overflow-y: auto;
      }
    }
  }
}
</style>
