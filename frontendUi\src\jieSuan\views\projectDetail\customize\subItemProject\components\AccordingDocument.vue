<!--
 * @Descripttion: 结算依据文件
 * @Author: renmingming
 * @Date: 2024-03-06 18:56:51
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-12 17:13:12
-->
<template>
  <commonModal
    className="dialog-comm"
    :width="564"
    v-model:modelValue="show"
    title="依据文件"
  >
    <div class="difference-box">
      <div class="upload-box">
        <a-button type="danger" shape="round" @click="uploadFile">
          <FolderOpenOutlined />上传依据文件</a-button
        >
        <div class="tip-text">
          附件支持：<strong>pdf、excel 、docx、png、jpg格式</strong>
        </div>
      </div>
      <div class="upload-list">
        <div class="list" v-for="item in fileList" :key="item">
          <icon-font type="icon-lianjiewenjian"></icon-font>
          <div class="name" @click="viewFile(item.fileName)">
            {{ item.fileName }}
          </div>
          <div class="size">{{ item.fileSize }}</div>
          <icon-font
            type="icon-shanchu2"
            @click="deleteFile(item.fileName)"
          ></icon-font>
        </div>
      </div>
    </div>
    <div class="list-btn">
      <a-button type="primary" @click="onHandleClose">关闭</a-button>
    </div>
  </commonModal>
</template>

<script setup>
import { ref, toRaw, createVNode, computed, watch } from 'vue';
import jsApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import infoMode from '@/plugins/infoMode.js';
import { FolderOpenOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();
const emit = defineEmits(['successCallback', 'update:visible']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const show = computed({
  get: () => props.visible,
  set: val => {
    if (!val) {
    }
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      open();
    }
  }
);
const currentInfo = computed(() => {
  return projectStore.subCurrentInfo;
});
const viewFile = fileName => {
  jsApi.openAccordingFile({
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLineId: currentInfo.value?.sequenceNbr,
    fileName,
  });
};
let fileList = ref([]);
const uploadFile = () => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLineId: currentInfo.value?.sequenceNbr,
  };
  console.log(params);
  jsApi.uploadAccordingFile(params).then(res => {
    console.log(res);
    const { code, result } = res;
    if (code === 200 && result) {
      fileList.value = result;
      emit('successCallback');
      message.success('操作成功');
    }
  });
};
const showTipConfirm = fileName => {
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText: '是否删除当前依据文件？',
    confirm: () => {
      deleteHandler(fileName);
      infoMode.hide();
    },
    close: () => {
      console.log('Cancel');
      infoMode.hide();
    },
  });
};
const deleteFile = fileName => {
  showTipConfirm(fileName);
};
const onHandleClose = () => {
  show.value = false;
};
const deleteHandler = fileName => {
  const param = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLineId: currentInfo.value?.sequenceNbr,
    fileName,
  };
  console.log(param);
  jsApi.deleteAccordingFile(param).then(res => {
    const { code } = res;
    if (code === 200) {
      emit('successCallback');
      message.success('操作成功');
      setTimeout(() => {
        fileList.value = currentInfo.value.accordingFiles;
      }, 1000);
    }
  });
};
const open = () => {
  fileList.value = currentInfo.value?.accordingFiles || [];
};
</script>
<style lang="scss" scoped>
.list-btn {
  margin-top: 20px;
  text-align: center;
}
.upload-box {
  padding: 30px 0;
  background: rgba(255, 246, 246, 1);
  border: 1px rgba(151, 151, 151, 0.3686) dashed;
  text-align: center;
  .tip-text {
    margin-top: 10px;
    font-size: 12px;
    color: #000;
    strong {
      color: #2a2a2a;
    }
  }
}
.upload-list {
  margin-top: 15px;
  .list {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    cursor: pointer;
    &:nth-child(even) {
      background: #f5f5f5;
    }
    .name {
      flex: 1;
      margin: 0 8px;
      font-size: 12px;
    }
    .size {
      margin-right: 8px;
      font-size: 12px;
      color: #a2a2a2;
    }
  }
}
</style>
