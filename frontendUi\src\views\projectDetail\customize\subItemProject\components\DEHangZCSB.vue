<!--
 * @Descripttion: 
 * @Author: renmingming
 * @Date: 2024-08-05 09:27:41
 * @LastEditors: renmingming
 * @LastEditTime: 2024-08-22 16:42:49
-->
<template>
  <common-modal
    className="dialog-comm"
    title="主材（设备）编辑"
    width="600px"
    v-model:modelValue="show"
    @cancel="cancel"
    @close="cancel"
  >
    <a-form class="form" layout="vertical" :model="formState">
      <a-form-item label="清单名称">
        <div
          class="combination-name"
          @mouseup.stop="combinationMouseup"
          v-html="formState.combinationName"
        ></div>
      </a-form-item>
      <a-form-item label="主材（设备）名称" style="color: #287cfa">
        <a-input
          v-model:value="formState.name"
          @focus="setCurrentField('name')"
          placeholder=""
        />
      </a-form-item>
      <a-form-item label="主材（设备）规格型号">
        <a-input
          v-model:value="formState.specification"
          @focus="setCurrentField('specification')"
          placeholder=""
        />
      </a-form-item> </a-form
    ><a-row>
      <a-col :span="20"
        ><icon-font
          type="icon-querenshanchu"
          style="margin-right: 5px"
        ></icon-font
        >您可通过选中清单名称中描述快速填充主材（设备）名称、规格型号</a-col
      >
      <a-col :span="4" style="text-align: right"
        ><a-button type="primary" @click="submit">提交</a-button></a-col
      >
    </a-row>
  </common-modal>
</template>

<script setup>
import { computed, reactive, watch, ref } from 'vue';
const props = defineProps(['visible', 'currentInfo']);
const emits = defineEmits(['update:visible', 'updateData']);
let formState = reactive({
  name: '',
  specification: '',
  combinationName: '',
});
watch(
  () => props.visible,
  val => {
    if (val) {
      open();
    }
  }
);
const show = computed({
  get: () => {
    return props.visible;
  },
  set: val => {
    emits('update:visible', val);
  },
});
const cancel = () => {
  emits('update:visible', false);
};
let currentField = ref('name');
const setCurrentField = field => {
  currentField.value = field;
};
const submit = () => {
  const { specification, name } = formState;
  let params = {
    name,
    specification,
  };
  if ([94, 95].includes(props.currentInfo?.kind)) {
    params = {
      materialName: name,
      specification: specification,
    };
  }
  emits('updateData', params);
  cancel();
};
const open = () => {
  const qdInfo = getQDInfo(props.currentInfo);
  const name = qdInfo?.name || '';
  const projectAttr = qdInfo?.projectAttr || '';
  formState.combinationName = name;
  if (projectAttr) {
    formState.combinationName += `\n${projectAttr}`;
  }
  formState.name = props.currentInfo?.name;
  formState.specification = props.currentInfo?.specification;
};
// 获取当前行的父级清单行
const getQDInfo = (row, kind = '03') => {
  const customParent = row?.customParent;
  if (!customParent) return '';
  if (customParent.kind === kind) return customParent;
  return getQDInfo(customParent, kind);
};
const combinationMouseup = e => {
  const selection = window.getSelection();
  if (selection.toString().length > 0) {
    formState[currentField.value] = selection.toString();
  }
};
</script>
<style lang="scss" scoped>
.combination-name {
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.5715;
  min-height: 30px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: text;
  user-select: text;
  ::v-deep p {
    margin: 0;
    user-select: text;
  }
}
.form ::v-deep .ant-form-item-label label {
  color: #287cfa;
}
</style>
