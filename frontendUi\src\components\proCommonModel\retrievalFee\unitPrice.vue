<template>
  <div class="singleCon">
    <ul class="menu-list">
      <li
        v-for="(item, index) in decData.djgc"
        :key="item.feeFileName"
        @click="handleSelect(item, index)"
        :class="{ on: categoryIndex === index }"
      >
        <a-tooltip placement="right" :title="item.feeFileName">
          <span class="name-content"> {{ item.feeFileName }}</span>
        </a-tooltip>
      </li>
    </ul>
    <div class="table">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :data="decData.djgc[categoryIndex].djgc"
        height="auto"
        ref="upTable"
        border="full"
        keep-source
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
        }"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, tableCellClickEvent, ['caculateBase']);
          }
        "
        class="table-edit-common"
      >
        <vxe-column field="sort" width="60" title="序号"> </vxe-column>
        <vxe-column field="name" min-width="220" title="名称">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
        </vxe-column>
        <vxe-colgroup title="标准模板">
          <vxe-column
            field="templateCaculateBase"
            min-width="150"
            title="计算基数"
          >
          </vxe-column>
          <vxe-column field="templateRate" width="120" title="费率（%）">
          </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="本工程审定模板">
          <vxe-column
            field="caculateBase"
            min-width="150"
            title="计算基数"
            :edit-render="{ autofocus: '.vxe-textarea--inner' }"
          >
            <template #default="{ row }">
              {{ row.caculateBase }}
            </template>
            <template #edit="{ row }">
              <vxeTableEditTable
                tableType="DJGC"
                @showTable="() => {}"
                :filedValue="row.caculateBase"
                :propInfo="store.subCurrentInfo"
                placement="bottom"
                @update:filedValue="
                  newValue => {
                    row.caculateBase = newValue;
                  }
                "
              ></vxeTableEditTable
            ></template>
          </vxe-column>

          <vxe-column
            field="rate"
            width="120"
            title="费率（%）"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span
                :style="row.templateRate != row.rate ? 'color: red;' : ''"
                >{{ row.rate }}</span
              >
            </template>
            <template #edit="{ row }">
              <vxe-input
                :clearable="false"
                v-model="row.rate"
                :maxlength="10"
                @blur="(row.rate = pureNumber(row.rate, 2)), clear(row)"
              ></vxe-input>
            </template>
          </vxe-column>
          <vxe-column field="desc" min-width="200" title="基数说明">
          </vxe-column>
          <!-- <vxe-column field="费用类别" min-width="200" title="费用类别">
                  </vxe-column> -->
        </vxe-colgroup>
      </vxe-table>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import { pureNumber } from '@/utils/index';
import { useCellClick } from '@/hooks/useCellClick';

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
let categoryIndex = ref(0);
const store = projectDetailStore();
const upTable = ref();

const props = defineProps({
  decData: {
    type: Object,
  },
  pushData: {
    type: Array,
  },
});
const clear = row => {
  // props.pushData.djgc.push()
  let rowData = props.decData.djgc[categoryIndex.value];
  console.log(store, 'store');
  let djgcData = {
    sequenceNbr: rowData.sequenceNbr,
    managementFee: Number(rowData.djgc.find(a => a.type === 7).rate),
    profit: Number(rowData.djgc.find(a => a.type === 8).rate),
    fees: Number(rowData.fees),
    anwenRateBase: Number(rowData.anwenRateBase),
    anwenRateAdd: Number(rowData.anwenRateAdd),
    levelType: store.currentTreeInfo.levelType,
    constructId: store.currentTreeGroupInfo?.constructId,
    feeFileId: rowData.feeFileId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  if (props.pushData.djgc.find(a => a.sequenceNbr === rowData.sequenceNbr)) {
    Object.keys(djgcData).forEach(key => {
      props.pushData.djgc.find(a => a.sequenceNbr === rowData.sequenceNbr)[
        key
      ] = djgcData[key];
    });
  } else {
    props.pushData.djgc.push(djgcData);
  }
  console.log(props.pushData, '提交数据');
  // } else {
  //   let apiData = {
  //     constructId: store.currentTreeGroupInfo?.constructId,
  //     singleId: store.currentTreeGroupInfo?.singleId, //单项ID
  //     unitId: store.currentTreeInfo?.id, //单位ID
  //     unitCostSummary: { ...row },
  //   };
  //   if (
  //     props.pushData.summary.find(
  //       a => a.unitCostSummary.sequenceNbr === row.sequenceNbr
  //     )
  //   ) {
  //     Object.keys(apiData).forEach(key => {
  //       props.pushData.summary.find(
  //         a => a.unitCostSummary.sequenceNbr === row.sequenceNbr
  //       )[key] = apiData[key];
  //     });
  //   } else {
  //     props.pushData.summary.push(apiData);
  //   }
  // }

  //清除编辑状态
  const $table = upTable.value;
  $table.clearEdit();
};
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log('获取当前点击信息', row, column);
  if (
    ['subItemProject', 'measuresItem'].includes(store.componentId) &&
    store.subCurrentInfo?.kind !== '04'
  ) {
    // 不是定额不能编辑
    return false;
  }
  return true;
};
const handleSelect = (item, index) => {
  categoryIndex.value = index;
  console.log(item);
};
defineExpose({
  categoryIndex,
});
</script>
<style lang="scss" scoped>
.head-action {
  margin-bottom: 5px;
  // height: 35px;
  flex: 1;
  :deep(.ant-tabs-tab) {
    // height: 35px;
    background: transparent;
    border: none;
    color: #7c7c7c;
  }
  :deep(.ant-tabs-nav) {
    background: #e7e7e7;
  }
  :deep(.ant-tabs-tab-active) {
    background: #ffffff;
    border-top: 2px solid #4786ff;
    .ant-tabs-tab-btn {
      color: #000000;
    }
  }
  button {
    float: right;
    margin-right: 15px;
  }
  .singleCon {
    display: flex;

    .menu-list {
      width: 170px;
      list-style: none;
      padding: 1px 0px 9px;
      li {
        text-align: left;
        // margin: 0 0 6px 6px;
        margin: 0 0 1px 6px;

        cursor: pointer;
        .name-content {
          display: block;
          padding: 0 20px;

          line-height: 1.6;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
        }
        &:hover {
          background-color: #dae7f4;
        }
      }
      .on {
        background-color: #deeaff;
      }
    }
    .table {
      height: 60vh;
      padding-left: 10px;
      width: calc(100% - 170px);
    }
  }
}
.table {
  height: 60vh;
}
</style>
