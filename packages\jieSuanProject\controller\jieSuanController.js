const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");


class JieSuanController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 打开选择文件路径框
     * @return
     */
     openFileSelection () {
        return  this.service.jieSuanProject.jieSuanProjectService.openFileSelection();

    }




    /**
     * 新建结算项目
     * @return
     */
    async creatJieSuanProJect (arg) {
        return await this.service.jieSuanProject.jieSuanProjectService.creatJieSuanProJect(arg);

    }

    /**
     * 打开结算项目
     * @return
     */
    async openJieSuanProJect (arg) {
        return await this.service.jieSuanProject.jieSuanProjectService.openJieSuanProJect(arg);

    }




    /**
     * 生成项目层级树结构
     */
    async generateLevelTreeNodeStructure (arg) {
        const result = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure(arg);
        return ResponseData.success(result);
    }


    async handleSingleProject (arg) {
        const result = await this.service.jieSuanProject.jieSuanProjectService.handleSingleProject(arg);
        return ResponseData.success(result);
    }


    /**
     * 处理新增单位后的罗技
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async handleAddUnit (arg) {
        const result = await this.service.jieSuanProject.jieSuanProjectService.handleAddUnit(arg);
        return ResponseData.success(result);
    }



    /**
     * 处理修改人材机后的结算特有逻辑
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async handleRcj (arg) {
        const result = await this.service.jieSuanProject.jieSuanProjectService.handleRcj(arg);
        return ResponseData.success(result);
    }





    // /**
    //  * 打开结算项目
    //  * @return
    //  */
    // async openJieSuanProJect (arg) {
    //     return await this.service.jieSuanProject.jieSuanProjectService.openJieSuanProJect(arg);
    //
    // }



}

JieSuanController.toString = () => '[class JieSuanController]';
module.exports = JieSuanController;

