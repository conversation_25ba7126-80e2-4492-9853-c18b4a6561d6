import { In } from "typeorm";
import { CommonDto, Service } from "../../../core";
import { GljBatchOperationalConversionRuleItemDto } from "../controller/gljConversionDeController";
import {
	ConversionSourceCodeEnum,
	ConversionSourceDescEnum,
	StandardConvertMod,
} from "../enums/ConversionSourceEnum";
import { GsBaseDe } from "../models/GsBaseDe";
import { BaseRcj2022 } from "../models/BaseRcj2022";
import { GsBaseRuleDetailFull } from "../models/GsBaseRuleDetailFull";
import { GljConstructProjectRcj } from "../models/GljConstructProjectRcj";
import { GsConversionInfoItem } from "../models/GsConversionInfoItem";
import { GsItemBillProject } from "../models/GsItemBillProject";
import { GsMeasureProjectTable } from "../models/GsMeasureProjectTable";
import {GljUnitProject} from "../models/GljUnitProject";
import { RCJKind } from "../enums/ConversionSourceEnum";
import { GsConversionListItem } from "../models/GsConversionListItem";
// const InsertStrategy = require("../main_editor/insert/insertStrategy");
// const InsertRcjStrategy = require("../rcj_handle/insert/insertRcjStrategy");
const Log = require("../../../core/log");
const { ObjectUtils } = require("../utils/ObjectUtils");
const {
	ConversionRuleOperationRecord,
} = require("../models/ConversionRuleOperationRecord");
const { Snowflake } = require("../utils/Snowflake");
const { SqlUtils } = require("../utils/SqlUtils");
const { NumberUtil } = require("../utils/NumberUtil");
const { ParamUtils } = require("../../../core/core/lib/utils/ParamUtils");
const _ = require("lodash");
const ConstantUtil = require("../enums/ConstantUtil.js");
const {ConvertUtil} = require("../utils/ConvertUtils");
const ConversionStrategy = require("../standard_conversion/conversionStrategy")

// kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)
/**
 * 定额换算 process
 */
class GljConversionDeService extends Service {
	/**
	 * 构造函数
	 * @param ctx
	 */
	constructor(ctx: unknown) {
		super(ctx);
	}

	static toString() {
		return "[class GljConversionDeService]";
	}

	private readonly baseDeRuleRelationService =
		this.service.baseDeRuleRelationService;
	private readonly baseRuleDetailsService = this.service.baseRuleDetailsService;
	private readonly baseRuleFileDetailsService =
		this.service.baseRuleFileDetailsService;
	private readonly conversionRuleOperationRecordService =
		this.service.conversionRuleOperationRecordService;
	private readonly conversionInfoService = this.service.gsConversionInfoService;

	private sortedKind = Object.freeze([3, 1, 2, 0]);
	// R,C,J * k对应定额下的人工，材料（含主材），机械
	// 0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比
	private rcjKindConvert: Readonly<Record<string, string>> = Object.freeze({
		"1": "R",
		"2": "C",
		"3": "J",
		"5": "C",
		"6": "C",
		"7": "C",
		"8": "C",
		"9": "C",
		"10": "C",
	});

	/**
	 * 标准换算中 默认换算的模板数据
	 * 根据产品要求。只保留R/C/J 三类
	 * @return {[{val: number, sort: number, type: string},{val: number, sort: number, type: string},{val: number, sort: number, type: string},{val: number, sort: number, type: string}]}
	 */
	private getDefPiaoZhunHuanSuan = Object.freeze([
		{ sort: 1, type: "人工费", val: 1 },
		{ sort: 2, type: "材料费", val: 1 },
		{ sort: 3, type: "机械费", val: 1 },
		// 转测演示提出 主材不要了
		//{"sort": 4, type: "主材费", "val": 1},
		{ sort: 5, type: "单价", val: 1 },
	]);

	/**
	 * 初始化默认换算数据 默认换算挂在  unitProject.defaultConcersions 下
	 * 以deId为key 对应一个 [换算列表]
	 * 数据结构为：
	 * unitProject.defaultConcersions = {deId:[默认换算列表]}
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param deId
	 */
	async initDef(constructId: any, singleId: any, unitId: any, deId: string | number) {
		if (!constructId && !singleId && !unitId) {
			constructId = ParamUtils.getPatram("commonParam").constructId;
			singleId = ParamUtils.getPatram("commonParam").singleId;
			unitId = ParamUtils.getPatram("commonParam").unitId;
		}

		const defHuanSuan = this.getDefPiaoZhunHuanSuan.map((v) => {
			return {
				...v,
				// 设置统一换算的主键id, 后续该id将记录在换算信息以及人材机下挂规则中.BS需要该字段。
				sequenceNbr: Snowflake.nextId(),
			};
		});
		let unitProject: GljUnitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		if (!unitProject.defaultConcersions) {
			unitProject.defaultConcersions = {};
		}
		unitProject.defaultConcersions[deId] = defHuanSuan;
		return defHuanSuan;
	}

	async getDefDonversion(dto: CommonDto & { deId: string }) {
		return await this.service.gongLiaoJiProject.gljRuleDetailFullService.getDefDonversion(dto.constructId, dto.unitId, dto.deId);

		let { defaultConcersions = {} } = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(dto.constructId, dto.unitId);

		return !defaultConcersions || !Array.isArray(defaultConcersions[dto.deId])
			? this.initDef(dto.constructId, dto.singleId, dto.unitId, dto.deId)
			: defaultConcersions[dto.deId];
	}

	async getDefDonversion2(constructId: string, singleId: string, unitId: string, deId: string) {
		let { defaultConcersions = {} } = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);

		return !defaultConcersions || !Array.isArray(defaultConcersions[deId])
			? this.initDef(constructId, singleId, unitId, deId)
			: defaultConcersions[deId];
	}

	async upDateDefault(
		constructId: string,
		singleId: string,
		unitId: string,
		deId: string,
		uniteRules: any[]
	) {
		let line: GsItemBillProject = await this.service.gongLiaoJiProject.gljProjectCommonService.findLineOnlyById(constructId, unitId, deId);
		let defaultConcersions = uniteRules.map((v) => ({
			sequenceNbr: v.sequenceNbr,
			sort: v.sort,
			type: v.type,
			val: v.val,
			kind: "4",
		}));

		await this.service.gongLiaoJiProject.gljRuleDetailFullService.setDefDonversion(constructId, unitId, deId, defaultConcersions);
		const rules = this.formatRuleByUniteRules(defaultConcersions, line);
		await this.conversionRule(constructId, singleId, unitId, deId, rules, uniteRules);
	}

	private formatRuleByUniteRules(
		uniteRules: any[],
		line: GsItemBillProject | GsMeasureProjectTable
	): GljBatchOperationalConversionRuleItemDto[] {
		const typeMaps = new Map([
			["人工费", "R"],
			["机械费", "J"],
			["材料费", "C"],
			["单价", ""],
			// ["主材费", "Z"],
		]);
		return (uniteRules || [])
			.map((uniteRule, index) => {
				const type = typeMaps.get(uniteRule.type);
				/*
			转测演示提出，主材不要了
			else if (newAlgorithm.type === "主材费") {
				type = "Z";
			}*/
				if (uniteRule.val == 1) return;
				if (type == null || type == void 0) return;
				// 拼接 R*n C*n J*n
				let math = type + "*" + uniteRule.val;
				// 默认的规则用 定额id + def +类型标识
				// 注释该seqNo, 直接使用主键newAlgorithm.sequenceNbr; //let seqNo = deId + "def" + type;
				return {
					sequenceNbr: uniteRule.sequenceNbr,
					type: "0",
					kind: uniteRule.kind,
					math: math,
					relation: math,
					defaultValue: 1,
					selectedRule: uniteRule.val,
					fbFxDeId: line.sequenceNbr, // 分部分项或措施项目定额id; ps:标准换算中有fbFxDeId,这里在统一换算中也加上,用于BS端在处理ysf文件时,通过deId+ruleId反查出operatingRecord.
					index: 999999 + index,
					libraryCode: line.libraryCode,
					isUniteRule: true,
				};
			})
			.filter((v) => !!v);
	}

	private formatRuleByConversionList(
		conversionList: GsConversionListItem[]
	): GljBatchOperationalConversionRuleItemDto[] {
		return (conversionList || []).map((v) => {
			return {
				sequenceNbr: v.sequenceNbr,
				type: "0",
				kind: "0",
				math: v.math,
				relation: v.relation,
				defaultValue: v.defaultValue,
				selectedRule: v.selectedRule,
				fbFxDeId: v.deId, // 分部分项或措施项目定额id; ps:标准换算中有fbFxDeId,这里在统一换算中也加上,用于BS端在处理ysf文件时,通过deId+ruleId反查出operatingRecord.
				index: v.index,
				libraryCode: v.libraryCode,
				ruleInfo: v.ruleInfo,
				selected: v.selected,
				isUniteRule: false,
			};
		});
	}

	/**
	 * 1.获取定额，人材机
	 * 2.获取当前改动的规则
	 *    2.1. 如果规则是kind3->b
	 *        <1> 新增定额
	 *        <2> 新增定额下的人材机执行计算
	 *        <3> 工程量表达式处理
	 *        <4> 如果修改和默认值相等 删除新定额
	 *    2.2. 如果规则是kind3->c
	 *        <1> 拿到人材机
	 *        <2> 人材机新增到当前定额下，如果有相同人材机 加消耗量
	 *        return {math: sq1+?, sq2+?}
	 *        <3> kind3->c 改回默认值 删除rcj 下的规则
	 * 3.对每一个人材机挂规则
	 *    3.0. default 的 直接挂上
	 *    3.1. kind1 seleced挂上，不seleced的则从人材机上删除
	 *    3.2. kind2 若没有 直接挂上  若有 更新
	 *    3.3. kind3 若没有，且 类型 不是 kind3-b  kind3 - c直接挂上
	 *               若已经存在，更新用户输入值 （e的情况 每一行视为单独的）
	 *               若已存在 且 用户修改值等于默认值 在人材机上删除该规则
	 * 4.执行规则 以人材机为单位循环
	 *    4.1. kind1 拆分规则 并翻译规则 执行拆分后的每个小规则  如果规则是 非激活 则不执行
	 *    4.2. kind2 翻译规则 执行规则 保留 standid  保留 resQty 保留 seqNbr
	 *    4.3. kind3 a
	 *        拆分规则并翻译 执行拆封后的规则 如果规则是非激活 则不执行
	 *    4.4. kind3 d
	 *        拆分规则并翻译 执行拆分后的规则 如果规则是非激活 则不执行
	 *    4.4. kind3 e
	 *        拆分规则并翻译 执行拆分后的规则 如果规则是非激活 则不执行
	 * 5.定额维度问题处理
	 *    5.1. 换算信息记录
	 *    5.2. 换字
	 *    5.3. 名称增加描述
	 *    5.4. 编码增加换算公式
	 */
	async conversionRule(
		constructId: string,
		singleId: string,
		unitId: string,
		deId: string,
		rules: GljBatchOperationalConversionRuleItemDto[],
		uniteRules: any[] = null
	) {

		// await this.getDefDonversion2(constructId, singleId, unitId, deId);
		// let de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
		// let isSaveConversionInfo = de.standardConvertMod === StandardConvertMod.Current? true:false;
		// let deRow = await this.service.gongLiaoJiProject.gljRuleDetailFullService.replaceDe(constructId, unitId, deId, isSaveConversionInfo);
		// for (let rule of rules) {
		// 	rule.currentRcjCode = rule.defaultRcjCode
		// }
		const conversionStrategy = new ConversionStrategy();
		await conversionStrategy.init(constructId,
			singleId,
			unitId,
			deId,
			rules,
			uniteRules)
		// return await conversionStrategy.execute();
		await conversionStrategy.executeConvInfo();

		// 对子级定额执行换算信息
		// for (let child of deRow.children) {
		// 	let de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, child.sequenceNbr);
		// 	if (ObjectUtils.isEmpty(de.conversionInfo)) {
		// 		continue;
		// 	}
		// 	const childConversionStrategy = new ConversionStrategy();
		// 	await childConversionStrategy.init(constructId,
		// 		singleId,
		// 		unitId,
		// 		child.sequenceNbr,
		// 		[],
		// 		de.defaultConcersions)
		// 	await childConversionStrategy.executeConvInfo();
		// }
		return;

		return await this.standardConvert(
			constructId,
			singleId,
			unitId,
			deId,
			rules
		);
		const sortedRule = rules.sort((a, b) => a.index - b.index);
		// 按照这个kind顺序执行规则
		const changedRule = sortedRule.filter((v) => {
			if (v.ruleInfo && v.ruleInfo != v.defaultValue) return true;
			if (typeof v.selected == "string") {
				return v.nowChange || v.selectedRule != v.defaultValue;
			} else {
				return v.nowChange || v.selectedRule != v.defaultValue || v.selected;
			}
			// return v.nowChange;
		});

		const unitProject: GljUnitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		unitProject.conversionRuleSeqNbrSortIndex
			? (unitProject.conversionRuleSeqNbrSortIndex[deId] = sortedRule.map(
					(v) => v.sequenceNbr
			  ))
			: (unitProject.conversionRuleSeqNbrSortIndex = {
					[deId]: sortedRule.map((v) => v.sequenceNbr),
			  });
		// 保存维护材料替换信息
		if (typeof unitProject.lastMaterialReplaceInfo !== "object")
			unitProject.lastMaterialReplaceInfo = {};
		const materialReplaceInfo = new Map(
			unitProject.lastMaterialReplaceInfo[deId]
				? unitProject.lastMaterialReplaceInfo[deId]
				: []
		);
		changedRule
			.filter((v) => !!v.clpb)
			.map((v) => [v.sequenceNbr, v.clpb.detailsCode])
			.forEach((v) => {
				const [k, val] = v;
				if (!materialReplaceInfo.get(k)) materialReplaceInfo.set(k, val);
			});
		unitProject.lastMaterialReplaceInfo[deId] = [
			...materialReplaceInfo.entries(),
		];
		// 添加材料配置
		changedRule.forEach((v) => {
			const detailsCode = materialReplaceInfo.get(v.sequenceNbr);
			if (!v.clpb && detailsCode) v.clpb = { detailsCode };
		});
		// 清除 重新计算
		unitProject.constructProjectRcjs = unitProject.constructProjectRcjs.map(
			(v: any) => ({
				...v,
				resQty: v.consumerResQty || v.initResQty,
			})
		);
		// 查fbFxDeId 得到定额行 并得出是分部分项 还是 措施项目
		const { line, belong: type } =
			this.service.baseBranchProjectOptionService.findLineOnlyById(deId);
		if (!line.standardConvertMod)
			line.standardConvertMod = StandardConvertMod.Current;
		if (line.standardConvertMod == StandardConvertMod.Default) {
			// 重置人材机列表
			this.service.rcjProcess.delRcjAndRcjDetailBatch(
				[deId],
				constructId,
				singleId,
				unitId
			);
			let findRes = this.service.baseBranchProjectOptionService.findFromAllById(
				constructId,
				singleId,
				unitId,
				deId
			);
			await this.service.rcjProcess.batchSaveRcjData(
				findRes.item,
				constructId,
				singleId,
				unitId
			);
		}
		line.deKind3cRules = null;
		// unitProject.conversionInfoList = [];
		let index = 0;
		// 结束清除
		for (const rule of changedRule) {
			//   kind2 默认的 不处理
			if (
				(rule.kind == "3" &&
					(isNaN(+rule.selectedRule) || !rule.selectedRule) &&
					rule.selectedRule != "0") ||
				// kind 2  没有材料配比
				(rule.kind == "2" && !(rule as any).clpb)
			)
				continue;
			// let is2022DeStandard = line.libraryCode.startsWith("2022");
			// 获取人材机
			/*let rcjs = unitProject.constructProjectRcjs.filter(f => f.deId == deId);*/
			//处理 kind=3 e1 情况
			let rcjs = (
				rule.kind == "3" && rule.type == "e1"
					? await this.kind3e1rcjAdd(constructId, singleId, unitId, line, rule)
					: await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId)
							.constructProjectRcjs.filter(
								(f: { deId: string }) => f.deId == deId
							)
							.map((v: any) => {
								//yl说 kind =3 d 先标准换算 后 修改消耗量 标准换算不生效
								if (rule.kind == "3" && rule.type == "d") {
									v.kind3dType = 1;
									v.resQtyChangeType != 1;
								}
								return v;
							})
			).filter(
				(v: any) =>
					!(v.unit == "%" && ["其他材料", "其他机械"].includes(v.materialName))
			);
			// rcjs.forEach((f: any) => {
			// 	// 如果用户有输入消耗量，则用户输入的消耗量
			// 	f.resQty = f.consumerResQty || f.initResQty;
			// });
			// 处理上一次换算信息 维护换算信息列表
			// unitProject.conversionInfoList = unitProject.conversionInfoList?.filter(
			// 	(f: { ruleId: any; deId: string }) => {
			// 		if (f.ruleId == rule.sequenceNbr && f.deId == deId) return false;
			// 		return true;
			// 	}
			// );
			// 特殊处理 kind3 - b
			if (rule.kind == "3" && rule.type == "b") {
				await this._dealKind3bRule(
					constructId,
					singleId,
					unitId,
					line,
					type,
					rule
				);
			} else if (rule.kind == "3" && rule.type == "c") {
				// 特殊处理 kind3 - c
				// 如果已经被kind1 系数处理 则 对kind3同系数处理
				const baseRule = await this.app.gljAppDataSource
					.getRepository(
						`${rule.libraryCode}`.startsWith("2022")
							? GsBaseRuleDetailFull
							: GsBaseRuleDetailFull
					)
					.findOneBy({
						sequenceNbr: rule.sequenceNbr,
					});
				await this.dealKind3cRule(
					constructId,
					singleId,
					unitId,
					line,
					type,
					rule,
					baseRule.description
				);
			} else {
				// 给人材机挂规则 并执行人材机下挂规则
				const params = {
					constructId: constructId,
					singleId: singleId,
					unitId: unitId,
					rule: rule,
					deId: deId,
					rcjs: rcjs,
				};
				// 挂规则前处理需要新增的人材机
				rcjs = await this._beforePackingRulesToRcj(params);
				// 挂规则
				this._packingRulesToRcj(constructId, singleId, unitId, rcjs, rule);
				if (index == 0) {
					// 执行前， 将人材机的resQty恢复原始值
					// TODO 这里重置了消耗量?
					rcjs.forEach((f: any) => {
						// 如果用户有输入消耗量，则用户输入的消耗量
						f.resQty = f.consumerResQty || f.initResQty;
					});
				}
				// 对人材机执行规则
				await this._caculateRcjRules(constructId, singleId, unitId, rcjs, line);
			}

			// 定额维度问题处理 换算信息 换字 名称 编码
			this._dealDeInfos(constructId, singleId, unitId, line, rule, index);
			index = index + 1;

			// 人材机 合计数量 合价  定额 单价构成
			this._reCaculateSomeThings(constructId, singleId, unitId, line, type);

			rcjs.forEach((rcj: any) => {
				this.service.unitPriceService.caculateDeByRcj(
					constructId,
					singleId,
					unitId,
					rcj
				);
			});
		}
	}

	// 替换材料
	private replaceMaterial(
		unitProject: GljUnitProject,
		fromMaterial: string,
		toMaterial: string
		// rule: any
	) {
		const index = unitProject.constructProjectRcjs.findIndex(
			(v) => v.materialCode == fromMaterial
		);
		const currentRCJ = unitProject.constructProjectRcjs[index];
		if (index > -1) {
			unitProject.constructProjectRcjs.splice(
				index,
				1,
				Object.assign(currentRCJ, {
					sequenceNbr: Snowflake.nextId(),
					deId: currentRCJ.deId,
					materialCode: toMaterial,
					materialName: 0,
				})
			);
		}
	}

	async _dealDeInfos(
		constructId: string,
		singleId: string,
		unitId: string,
		deLine: any,
		rule: GljBatchOperationalConversionRuleItemDto,
		index: number
	) {
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		// 换算信息记录
		this._cleanFromMer(unitProject, rule, deLine.sequenceNbr);
		let conversionInfo: {
			sequenceNbr: string;
			deId: any;
			conversionString: any;
			conversionExplain: string;
			ruleId?: any;
			kind?: string;
			sourceCode?: number;
			source?: string;
			math?: any;
			selectedRule?: string;
			displayMath?: any;
			selectedRuleGroup?: any;
			index: number;
		} = {
			sequenceNbr: Snowflake.nextId(),
			deId: deLine.sequenceNbr,
			conversionString: rule.math,
			conversionExplain: rule.relation,
			index,
		};
		if (rule.kind == "3") {
			let aft = "";
			if (rule.type == "e1" || rule.type == "e2") {
				aft = rule.relationGroupName;
			}
			conversionInfo.conversionExplain =
				rule.relation + aft + " : " + rule.selectedRule;
		}
		conversionInfo.ruleId = rule.sequenceNbr;
		conversionInfo.kind = rule.kind;
		// 先写死来源
		if (rule.kind != "0") {
			conversionInfo.sourceCode = ConversionSourceCodeEnum.标准换算;
			conversionInfo.source = ConversionSourceDescEnum.标准换算;
		} else {
			conversionInfo.sourceCode = 0;
			conversionInfo.source = "统一换算";
		}
		// 换算算法
		conversionInfo.math = rule.math;
		conversionInfo.selectedRule = rule.selectedRule;
		conversionInfo.displayMath = this._getDisPlayRule(rule);

		let clpb = (rule as any).clpb;
		if (rule.kind == "2") {
			// 兼容22
			let tableName = "base_rcj";
			if (rule.libraryCode.startsWith(ConstantUtil.YEAR_2022)) {
				tableName = "base_rcj_2022";
			}
			let sql =
				"select material_code as mcode from " +
				tableName +
				" where sequence_nbr = ?";
			const sqlRes = this.app.betterSqlite3DataSource
				.prepare(sql)
				.get(rule.rcjId) as { mcode: any };
			let oriCode = sqlRes.mcode;
			(conversionInfo.conversionString =
				"H" + oriCode + " " + clpb.detailsCode), // 用来记 kind2的换算规则
				(conversionInfo.conversionExplain = "换算材料:" + clpb.details); // 用来记kind2的查看
			conversionInfo.selectedRuleGroup = clpb.groupName;

			this._pushNewIntoMer(unitProject, conversionInfo, rule);
		}

		if (
			(rule.kind == "1" && rule.selected) ||
			(rule.kind == "3" && rule.selectedRule != rule.defaultValue) ||
			(rule.kind == "0" && rule.selectedRule != rule.defaultValue)
		) {
			this._pushIntoMer(unitProject, conversionInfo);
		}

		//处理 定额标示 换
		let item = deLine;
		if (!_.isEmpty(unitProject.conversionInfoList)) {
			if (!item.appendType) {
				item.appendType = [];
			} else {
				item.appendType = item.appendType.filter((f: any) => f != "换");
			}
			item.appendType.push("换");
		} else {
			if (!item.appendType) {
				item.appendType = [];
			} else {
				item.appendType = item.appendType.filter((f: any) => f != "换");
			}
		}
		/* if (rule.kind == 2 || (rule.kind == 3&&rule.type=='c')) {
             if (!item.appendType) {
                 item.appendType = [];
             } else {
                 item.appendType = item.appendType.filter(f=>f!="换");
             }
             item.appendType.push("换");
         }*/

		/*let name = deLine.bdName?deLine.bdName:deLine.name;
        if (rule.kind == 1) {
            deLine.bdName = this._deleteSubstring(name, rule.relation);
            deLine.name = this._deleteSubstring(name, rule.relation);
            if (rule.selected) {
                deLine.bdName += "\n";
                deLine.bdName += rule.relation;
                deLine.name += "\n";
                deLine.name += rule.relation;
            }
        } else if (rule.kind == 2) {
            deLine.bdName += "\n";
            deLine.bdName += rule.relation;
            deLine.name += "\n";
            deLine.name += rule.relation;

        } else {
            deLine.bdName = this._deleteSubstring(name, rule.relation);
            deLine.name = this._deleteSubstring(name, rule.relation);
            if (rule.selectedRule != rule.defaultValue) {
                deLine.bdName += "\n";
                deLine.bdName += rule.relation;
                deLine.name += "\n";
                deLine.name += rule.relation;
            }
        }

        let item = deLine;
        if (rule.kind == 2 || (rule.kind == 3&&rule.type=='c')) {
            if (!item.appendType) {
                item.appendType = [];
            } else {
                item.appendType = item.appendType.filter(f=>f!="换");
            }
            item.appendType.push("换");
        }*/

		/*// kind2,kind3-c
        let item = deLine;
        if (rule.kind == 2 || (rule.kind == 3&&rule.type=='c')) {
            if (!item.appendType) {
                item.appendType = [];
            } else {
                item.appendType = item.appendType.filter(f=>f!="换");
            }
            item.appendType.push("换");
        }
        // kind2 名称增加描述
        let appendName = "";
        if (rule.kind == 2) {
            appendName = clpb.details;
        } else {
            appendName = rule.relation;
        }
        if (appendName) {
            if (item.bdName) {
                if (!item.bdName.endsWith(appendName)) {
                    item.bdName += "\n";
                    item.bdName += appendName;
                }
            } else {
                if (!item.name.endsWith(appendName)) {
                    item.name += "\n";
                    item.name += appendName;
                }
            }
        }*/
	}

	_pushIntoMer(
		unitProject: { conversionInfoList: any[] },
		conversionInfo: {
			sequenceNbr: string;
			deId: any;
			conversionString: any;
			conversionExplain: string;
			ruleId?: any;
			kind?: string;
			sourceCode?: number;
			source?: string;
			math?: any;
			selectedRule?: string;
			displayMath?: any;
			selectedRuleGroup?: any;
			index: number;
		}
	) {
		// 维护标准换算记录
		// 单位下是否有换算信息
		if (ObjectUtils.isEmpty(unitProject.conversionInfoList)) {
			//  无
			unitProject.conversionInfoList = [];
			unitProject.conversionInfoList.push(conversionInfo);
		} else {
			//  有
			unitProject.conversionInfoList.push(conversionInfo);
		}
	}

	_deleteSubstring(mainString: string, substring: string) {
		let mains = mainString.split("\n");
		let arrayAfterDel = mains.filter((f) => f != substring);
		let res = "";
		for (let i = 0; i < arrayAfterDel.length; ++i) {
			if (res.length > 2) {
				res += "\n";
			}
			res += arrayAfterDel[i];
		}

		return res;
	}

	/**
	 * yl说 kind 3 d 先标准换算 后 修改消耗量 标准换算不生效
	 * @param rcjs
	 */
	async kind3dRcjNoChange(rcjs: any[]) {
		if (ObjectUtils.isEmpty(rcjs)) {
			return;
		}
		rcjs.forEach((i) => (i.kind3dType = 1));
		rcjs = rcjs.filter((i) => i.resQtyChangeType != 1);

		return rcjs;
	}

	_pushNewIntoMer(
		unitProject: { conversionInfoList: any[] },
		conversionInfo: {
			sequenceNbr: string;
			deId: any;
			conversionString: any;
			conversionExplain: string;
			ruleId?: any;
			kind?: string;
			sourceCode?: number;
			source?: string;
			math?: any;
			selectedRule?: string;
			displayMath?: any;
			selectedRuleGroup?: any;
		},
		rule: GljBatchOperationalConversionRuleItemDto
	) {
		// 单位下是否有换算信息
		if (ObjectUtils.isEmpty(unitProject.conversionInfoList)) {
			// 无
			unitProject.conversionInfoList = [];
			unitProject.conversionInfoList.push(conversionInfo);
		} else {
			/* 24.3.14 这段代码看不懂，理论上不应该存在。先注调，因为 if (f.deId != deId) 中的deId没有定义会报错，即便入参中加入deId, 也感觉这的逻辑怪怪的。
            unitProject.conversionInfoList = unitProject.conversionInfoList.filter(f=> {
                if (f.ruleId == rule.sequenceNbr) {
                    if (f.deId != deId) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            });*/
			// 有
			unitProject.conversionInfoList.push(conversionInfo);
		}
	}

	_cleanFromMer(
		unitProject: { conversionInfoList: any[] },
		rule: GljBatchOperationalConversionRuleItemDto,
		deId: any
	) {
		// 单位下是否有换算信息
		if (ObjectUtils.isEmpty(unitProject.conversionInfoList)) {
			// 无
			unitProject.conversionInfoList = [];
			return;
		} else {
			// 有
			unitProject.conversionInfoList = unitProject.conversionInfoList.filter(
				(f) => {
					if (f.ruleId == rule.sequenceNbr) {
						if (f.deId != deId) {
							return true;
						} else {
							return false;
						}
					} else {
						return true;
					}
				}
			);
		}
	}

	private async dealKind3cRule(
		constructId: string,
		singleId: string,
		unitId: string,
		deLine: { deKind3cRules: any[]; sequenceNbr: any },
		type: string,
		rule: GljBatchOperationalConversionRuleItemDto,
		description: string
	) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let allRcjs = unit.constructProjectRcjs;
		if (ObjectUtils.isEmpty(deLine.deKind3cRules)) {
			deLine.deKind3cRules = []; //
			deLine.deKind3cRules.push(rule);
		} else if (
			ObjectUtils.isEmpty(
				deLine.deKind3cRules.filter(
					(item) => item.sequenceNbr == rule.sequenceNbr
				)
			)
		) {
			//如果定额没有这条规则就进行添加
			deLine.deKind3cRules.push(rule);
		} else {
			deLine.deKind3cRules = deLine.deKind3cRules.filter(
				(item) => item.sequenceNbr != rule.sequenceNbr
			);
			deLine.deKind3cRules.push(rule);
		}

		// 1. 删除之前由kind3-c导致新增的数据 kind3-c-add 直接删除
		// isFromConversion 标识是由于规则导致的新增人材机    此部分数据删除
		// conversionAdd    标识是原本存在的人材机 由于规则导致数据有增加   此部分数据做减法
		unit.constructProjectRcjs = allRcjs.filter(
			(rcj: { deId: any; isFromConversion: any }) =>
				!(rcj.deId == deLine.sequenceNbr && rcj.isFromConversion)
		);
		let addRcjs = unit.constructProjectRcjs.filter(
			(rcj: { deId: any; conversionAdd: any }) =>
				rcj.deId == deLine.sequenceNbr && rcj.conversionAdd
		);
		//对每一次人材机消耗量的原始值进行备份  以便后续计算记录消耗量的变化
		let rcjResQtyBak: Record<string, number> = {};
		if (addRcjs && addRcjs.length > 0) {
			addRcjs.forEach(
				(rcj: {
					sequenceNbr: string | number;
					resQty: number;
					conversionAdd: number;
				}) => {
					rcjResQtyBak[rcj.sequenceNbr] = +rcj.resQty;
					// rcj.resQty = rcj.resQty - rcj.conversionAdd;
				}
			);
		}
		// 1.5 删除上一次的记录
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		if (unitProject.conversionInfoList) {
			unitProject.conversionInfoList = unitProject.conversionInfoList.filter(
				(f: { ruleId: any }) => f.ruleId != rule.sequenceNbr
			);
		}

		for (let i = 0; i < deLine.deKind3cRules.length; i++) {
			let rule = deLine.deKind3cRules[i];
			let math = this._convertMath(rule.math, rule.selectedRule, description);
			await this.dealKind3cByOneRule(rule, math, deLine, unit, rcjResQtyBak);
		}
	}

	/**
	 * 处理单个Kind3c规则对于定额下人材机的影响
	 * @param rule
	 * @param math
	 * @param deLine
	 * @param unit
	 * @returns {Promise<void>}
	 */
	async dealKind3cByOneRule(
		rule: { libraryCode: string; relationDeId: unknown },
		math: any,
		deLine: { deKind3cRules?: any[]; sequenceNbr: any },
		unit: { constructProjectRcjs: any[] },
		rcjResQtyBak: Record<string, number>
	) {
		// 是否存在kind1系数 存在系数
		// 兼容22
		const [baseRcjTableName, baseDeRcjRelationTableName] =
			rule.libraryCode.startsWith("2022")
				? ["base_rcj_2022", "base_de_rcj_relation_2022"]
				: ["base_rcj", "base_de_rcj_relation"];
		// 2. 根据relationDe查询人材机
		let sql = `select b.*,r.res_qty from ${baseRcjTableName} b left join ${baseDeRcjRelationTableName} r on b.sequence_nbr = r.rcj_id where r.quota_id = ?`;
		let sqlRes = this.app.betterSqlite3DataSource
			.prepare(sql)
			.all(rule.relationDeId);
		// 3. 根据math计算人材机消耗量
		let baseRcjs = SqlUtils.convertToModel(sqlRes).map((v: any) => {
			v.kindBackUp = v.kind;
			v.markSum = 1;
			//v.initResQty = v.resQty;
			//yl说 原始含量应该是0(删除后，因为标准换算该条数据又被新增了，新增的该条数据，原始含量应该是0)
			v.initResQty = 0;
			v.marketPrice = v.dePrice;
			v.resQty = NumberUtil.numberScale(eval(v.resQty + math), 6);
			if (v.tempDeleteBackupResQty) v.tempDeleteBackupResQty = v.resQty;
			// v.isFromConversion = true;
			v.deId = deLine.sequenceNbr;
			v.sequenceNbr = Snowflake.nextId();
			return v;
		});
		// 4. 根据编码 将数据分为定额下没有的， 定额下有的
		let deRcjs = unit.constructProjectRcjs.filter(
			(f) => f.deId == deLine.sequenceNbr
		);
		const existMaterialCodes: string[] = deRcjs.map((v) => v.materialCode);
		// 5. 定额下没有的，直接添加  并增加字段标识 来源 isFromConversion = true
		const news = baseRcjs
			.filter(
				(v: { materialCode: string }) =>
					!existMaterialCodes.includes(v.materialCode)
			)
			.map((v: { isFromConversion: boolean }) => {
				v.isFromConversion = true;
				return v;
			});
		if (news.length > 0) {
			unit.constructProjectRcjs.push(...news);
		}
		// exits 已存在的
		// 6. 定额下有的，在当前定额数据的工程量基础上增加  并标注 conversionAdd = newResQty
		const filterRcj = baseRcjs.filter((v: { materialCode: string }) =>
			existMaterialCodes.includes(v.materialCode)
		);
		filterRcj.forEach(
			(ercj: { materialCode: any; resQty: string | number }) => {
				const deRcj: any = deRcjs.find(
					(v) => v.materialCode == ercj.materialCode && v.addRcjType != 1
				);
				let varietyBefore = ObjectUtils.isEmpty(rcjResQtyBak[deRcj.sequenceNbr])
					? deRcj.resQty
					: +rcjResQtyBak[deRcj.sequenceNbr];
				deRcj.conversionAdd = ObjectUtils.isEmpty(deRcj.conversionAdd)
					? 0
					: +deRcj.conversionAdd;
				// 消耗量
				// 如何将kind1 系数考虑进去
				deRcj.resQty = NumberUtil.numberScale(+deRcj.resQty + +ercj.resQty, 6);
				if (deRcj.tempDeleteBackupResQty)
					deRcj.tempDeleteBackupResQty = deRcj.resQty;
				deRcj.conversionAdd += deRcj.resQty - varietyBefore;
				rcjResQtyBak[deRcj.sequenceNbr] = +deRcj.resQty;
			}
		);
	}

	/**
	 * 处理kind 3 e1 如果有删除人材机的情况
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param deLine
	 * @param rule
	 * @returns {Promise<ConstructProjectRcj[]>}
	 */
	async kind3e1rcjAdd(
		constructId: string,
		singleId: string,
		unitId: string,
		deLine: { standardId: any; sequenceNbr: any },
		rule: GljBatchOperationalConversionRuleItemDto
	) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);

		let math = this._convertMath(rule.math, `${rule.selectedRule}`);
		//查询源人材机
		let newDeId = deLine.standardId;
		if (ObjectUtils.isEmpty(newDeId)) {
			return;
		}
		// 兼容22
		let baseRcjTableName = "base_rcj";
		let baseDeRcjRelationTableName = "base_de_rcj_relation";
		if (rule.libraryCode.startsWith(ConstantUtil.YEAR_2022)) {
			baseRcjTableName = "base_rcj_2022";
			baseDeRcjRelationTableName = "base_de_rcj_relation_2022";
		}
		// 2. 根据relationDe查询人材机
		let sql =
			"select b.*, r.res_qty\n" +
			"from " +
			baseRcjTableName +
			" b\n" +
			"         left join " +
			baseDeRcjRelationTableName +
			" r on b.sequence_nbr = r.rcj_id\n" +
			"where r.quota_id = ?";
		let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(newDeId);
		let baseRcjs = SqlUtils.convertToModel(sqlRes);

		// 3. 根据math计算人材机消耗量
		for (let i = 0; i < baseRcjs.length; ++i) {
			baseRcjs[i].kindBackUp = baseRcjs[i].kind;
			baseRcjs[i].markSum = 1;
			//baseRcjs[i].initResQty = baseRcjs[i].resQty;
			//yl说 原始含量应该是0(删除后，因为标准换算该条数据又被新增了，新增的该条数据，原始含量应该是0)
			baseRcjs[i].initResQty = 0;
			baseRcjs[i].marketPrice = baseRcjs[i].dePrice;
			baseRcjs[i].resQty = 0;
			// baseRcjs[i].isFromConversion = true;
			baseRcjs[i].deId = deLine.sequenceNbr;
			baseRcjs[i].sequenceNbr = Snowflake.nextId();
		}
		// 4. 根据编码 将数据分为定额下没有的， 定额下有的
		let deRcjs = unit.constructProjectRcjs.filter(
			(f: { deId: any }) => f.deId == deLine.sequenceNbr
		);

		let existRcjs: Record<string, boolean> = {};
		for (let i = 0; i < deRcjs.length; ++i) {
			existRcjs[deRcjs[i].materialCode] = true;
		}

		let exists = [];
		let news = [];
		for (let i = 0; i < baseRcjs.length; ++i) {
			if (existRcjs[baseRcjs[i].materialCode]) {
				exists.push(baseRcjs[i]);
			} else {
				news.push(baseRcjs[i]);
			}
		}

		// 5. 定额下没有的，直接添加  并增加字段标识 来源 isFromConversion = true
		if (news.length > 0) {
			for (let news1 of news) {
				this.service.rcjProcess.rcjUseUnitRcj(
					constructId,
					singleId,
					unitId,
					news1
				);
			}
			/*news.forEach(f=>{
                f.isFromConversion = true;
            });*/
			unit.constructProjectRcjs = unit.constructProjectRcjs.concat(news);
		}
		let constructProjectRcjs = unit.constructProjectRcjs.filter(
			(f: { deId: any }) => f.deId == deLine.sequenceNbr
		);
		return constructProjectRcjs;
	}

	/**
	 * 处理kind3-b的换算
	 *
	 * 1. 删除关联定额
	 * 2. 新增关联定额
	 * 3. 对关联定额的人材机消耗量进行计算
	 * 4. 关联定额，处理工程量
	 *
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param deLine
	 * @param type
	 * @param rule
	 * @private
	 */
	async _dealKind3bRule(
		constructId: string,
		singleId: string,
		unitId: string,
		deLine: {
			costMajorName: any;
			quantityExpressionNbr: any;
			measureType: any;
			sequenceNbr: any;
			createDeId: string;
			parentId: string;
		},
		type: string,
		rule: GljBatchOperationalConversionRuleItemDto
	) {
		// 1.首先删除关联出来的定额
		if (deLine.createDeId) {
			let moduleData = await this.service.gongLiaoJiProject.gljProjectCommonService.getModuleData(constructId, singleId, unitId, deLine.createDeId);

			if (!ObjectUtils.isEmpty(moduleData)) {
				let find = moduleData.find(
					(i: { sequenceNbr: string }) => i.sequenceNbr == deLine.createDeId
				);
				if (!ObjectUtils.isEmpty(find) && find.quantityExpression == "HSGCL") {
					(type == "fbfx"
						? this.service.itemBillProjectOptionService
						: this.service.stepItemCostService
					).removeLine(
						constructId,
						singleId,
						unitId,
						{
							sequenceNbr: deLine.createDeId,
							parentId: deLine.parentId,
							kind: "04",
						},
						false
					);
				}
			}
			deLine.createDeId = null;
		}
		// 1.5 删除上一次的记录
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		if (unitProject.conversionInfoList) {
			unitProject.conversionInfoList = unitProject.conversionInfoList.filter(
				(f: { ruleId: string }) => f.ruleId != rule.sequenceNbr
			);
		}
		// 如果还原了默认值 则不执行后续 并删除记录
		if (rule.defaultValue == rule.selectedRule) {
			return;
		}
		// 2.添加新的定额
		let newDeId = rule.relationDeId;
		let math = this._convertMath(rule.math, `${rule.selectedRule}`);
		// 根据规则中的deid查询定额
		let sql = "select * from base_de where sequence_nbr = ?";
		let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(newDeId);
		let convertRes = SqlUtils.convertToModel(sqlRes);
		// 根据fbfx 或者 csxm 新增定额
		let newDataInfo: { data: any; unit?: any; index?: any };
		if (type == "fbfx") {
			// (constructId, singleId, unitWorkId, pointLine, createType, indexId, unit, rootLineId, rcjFlag, bzhs, type, libraryCode, isInvokeCountCostCodePrice)
			newDataInfo =
				await this.service.itemBillProjectOptionService.fillDataFromIndexPage(
					constructId,
					singleId,
					unitId,
					deLine,
					"04",
					convertRes[0].sequenceNbr,
					convertRes[0].unit,
					null,
					null,
					1,
					null,
					convertRes[0].libraryCode
				);
		} else {
			// (constructId, singleId, unitWorkId, pointLine, createType, indexId, unit, rcjFlag, bzhs, type, isInvokeCountCostCodePrice, libraryCode)
			newDataInfo =
				await this.service.stepItemCostService.fillDataFromIndexPage(
					constructId,
					singleId,
					unitId,
					deLine,
					"04",
					convertRes[0].sequenceNbr,
					convertRes[0].unit,
					null,
					1,
					null,
					null,
					convertRes[0].libraryCode
				);
		}
		// 3.对新数据的的人材机消耗量 进行计算
		let newRcjs = await this.service.gongLiaoJiProject.gljProjectCommonService.getRcjList(
			constructId,
			unitId
		).filter((f: { deId: any }) => f.deId === newDataInfo.data.sequenceNbr);
		let t = false;
		if (newRcjs && newRcjs.length > 0) {
			newRcjs.forEach((rcj: any) => {
				rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + math), 6);
				if (rcj.tempDeleteBackupResQty) rcj.tempDeleteBackupResQty = rcj.resQty;
				if (t || rcj.resQty != rcj.initResQty) {
					t = true;
				}
			});
		}
		// 4.新数据和当前数据进行关联

		deLine.createDeId = newDataInfo.data.sequenceNbr;
		newDataInfo.data.relationDeId = deLine.sequenceNbr;
		// 补充：处理新定额的measureType rcjFlag
		newDataInfo.data.measureType = deLine.measureType;
		newDataInfo.data.rcjFlag = 0;
		// 5.处理新定额的工程量表达式
		newDataInfo.data.quantityExpression = "HSGCL";
		newDataInfo.data.quantityExpressionNbr = deLine.quantityExpressionNbr;
		newDataInfo.data.costMajorName = deLine.costMajorName;
		let newDataUnit = Number.parseFloat(newDataInfo.unit);
		if (Number.isNaN(newDataUnit) || newDataUnit == 0) {
			newDataUnit = 1;
		}
		let quantity = newDataInfo.data.quantityExpressionNbr / newDataUnit;
		newDataInfo.data.quantity = quantity;
		newDataInfo.data.quantityVariableValue = quantity;

		if (newRcjs && newRcjs.length > 0) {
			newRcjs.forEach(
				(rcj: {
					totalNumber: any;
					resQty: any;
					total: any;
					marketPrice: any;
				}) => {
					rcj.totalNumber = NumberUtil.numberScale4(
						NumberUtil.multiply(rcj.resQty, newDataInfo.data.quantity)
					);
					rcj.total = NumberUtil.numberScale2(
						NumberUtil.multiply(rcj.totalNumber, rcj.marketPrice)
					);
				}
			);
		}

		if (t) {
			newDataInfo.data.appendType = newDataInfo.data.appendType.filter(
				(f: string) => f != "换"
			);
			newDataInfo.data.appendType.push("换");
		}
	}

	/**
	 * 定额修改工程量之后 kind=3b联动
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param allData
	 * @param deData
	 */
	deQuantityChaneToKind3b(
		constructId: any,
		singleId: any,
		unitId: any,
		allData: any[],
		deData: {
			createDeId: any;
			quantityExpressionNbr: any;
			costMajorName: any;
			unit: string;
		}
	) {
		if (ObjectUtils.isEmpty(deData)) {
			return;
		}

		if (ObjectUtils.isEmpty(deData.createDeId)) {
			return;
		}

		let find = allData.find((i) => i.sequenceNbr == deData.createDeId);
		if (ObjectUtils.isEmpty(find) || find.quantityExpression != "HSGCL") {
			return;
		}

		find.quantityExpressionNbr = deData.quantityExpressionNbr;
		find.costMajorName = deData.costMajorName;
		let newDataUnit = Number.parseFloat(deData.unit);
		if (Number.isNaN(newDataUnit) || newDataUnit == 0) {
			newDataUnit = 1;
		}
		let quantity = NumberUtil.numberScale6(
			find.quantityExpressionNbr / newDataUnit
		);
		find.quantity = quantity;
		find.quantityVariableValue = quantity;

		let rcjs = this.service.rcjProcess.getRcjListByDeId(
			find.sequenceNbr,
			constructId,
			singleId,
			unitId
		);
		this.service.rcjProcess.reCaculateRcjPrice(
			find,
			rcjs,
			constructId,
			singleId,
			unitId
		);
		this.service.unitPriceService.caculataDEUnitPrice(
			constructId,
			singleId,
			unitId,
			find.sequenceNbr,
			true,
			allData
		);
	}

	_convertEMath(oriMath: string, inputVal: any) {
		oriMath = oriMath.replace(/H([^\s]*)(\s+\1)/g, "$2").trim();
		if (oriMath.indexOf(" ") > 0) {
			let code = oriMath.split(" ")[0];
			let math = oriMath.split(" ")[1];
			let oriSingle = math.substring(0, 1);
			let convertMath = eval(this._convertMath(math.substring(1), inputVal));
			return code + " " + oriSingle + convertMath;
		}
		return this._convertMath(oriMath, inputVal);
	}

	_convertMath(oriMath: string, inputVal: string, description?: string) {
		// description 特殊范围公式
		const rega = new RegExp("\\b\\V\\b", "g"); // 替换 V
		const regb = new RegExp("\\b(RU)\\b", "g"); // 替换 RU
		const regc = new RegExp("\\b(RD)\\b", "g"); // 替换 RD
		const regn = new RegExp("\\bn\\b", "g"); // 替换n
		const regd = new RegExp("%"); // 替换 %
		let convertMath = oriMath.replace(rega, inputVal);
		// 特殊范围公式取整逻辑为特殊处理
		if (description?.includes("特殊范围公式")) {
			// 处理区间内取整规则
			// (0, 1.2] => 1  (1.2,2.4] => 2
			// *n，n=RU(3.9-3.6)
			// 预先计算RU内的结果 根据结果计算区间 并取值
			// 每个区间长度1.2 取区间下标
			convertMath = convertMath.replace(/\(.*?\)/, (match) => {
				return `(${match}/1.2)`;
			});
		}
		convertMath = convertMath.replace(regb, "Math.ceil");

		convertMath = convertMath.replace(regc, "Math.floor");
		convertMath = convertMath.replace(regd, "*0.01");

		// 处理 ^
		if (convertMath.indexOf("^") > 0) {
			// 存在Math.pow(R*2,123)这种形式
			convertMath = (
				convertMath.startsWith("R") ||
				convertMath.startsWith("C") ||
				convertMath.startsWith("J")
					? convertMath.substring(1)
					: convertMath
			).replace(/\^/g, "**");
			// let params = convertMath.split("^");
			// params[0] = params[0].substring(1);
			// convertMath = "*" + "Math.pow(" + params[0] + " , " + params[1] + ")";
		}
		//数仓给的数据中有中文的逗号
		convertMath = convertMath.replace(/，/g, ",").replace(/, n/g, ",n");
		// 处理n
		if (convertMath.includes(",n")) {
			const [a, b] = convertMath.split(",");
			// 给公式加括号，提高运算的优先级。例如：避免计算 *n,n=3.8-3.6 时，会计算成 消耗量*3.8-3.6，应计算成 消耗量*(3.8-3.6)。目前数仓的数据中有的有括号有的没有，即便原本有括号，再加个括号也不影响。
			convertMath = a.replace(regn, `(${b.substr(2)})`);
		}

		return convertMath;
	}
	//对应规则放到对应的人材机下
	async _packingRulesToRcj(
		constructId: string,
		singleId: string,
		unitId: string,
		rcjs: string | any[],
		rule: GljBatchOperationalConversionRuleItemDto
	) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		if (!unit.rcjRules) unit.rcjRules = {};
		// 1. 如果规则是kind 1 且 是取消规则 则删除规则
		if (rule.kind == "1" && !rule.selected) {
			for (const rcj of rcjs) {
				unit.rcjRules[rcj.sequenceNbr] =
					unit.rcjRules[rcj.sequenceNbr]?.filter(
						(v: any) => v.sequenceNbr !== rule.sequenceNbr
					) || [];
			}
			return;
		}
		// 2. 如果规则是kind 3 且 输入值 == 默认值 认为删除规则  default类型
		if (
			(rule.kind == "0" && rule.selectedRule == "1") ||
			(rule.kind == "3" && rule.selectedRule == rule.defaultValue)
		) {
			for (const rcj of rcjs) {
				unit.rcjRules[rcj.sequenceNbr] =
					unit.rcjRules[rcj.sequenceNbr]?.filter(
						(v: any) => v.sequenceNbr !== rule.sequenceNbr
					) || [];
			}
			return;
		}
		// 3. 其他情况 直接将规则挂在rcj下
		/*
		 *
		 * 内存
		 * unit.rcjRules
		 * {
		 *     rcjId1 : [rule1,rule2,rule3]
		 *     rcjId2 : [rule1,rule2,rule3]
		 *     rcjId3 : [rule1,rule2,rule3]
		 * }
		 * 做复制粘贴时候：
		 * 根据定额
		 *  遍历人材机
		 *  然后人材机的id替换新的， 人材机下的[rule1,rule2,rule3] 的id修改一下
		 */
		for (const rcj of rcjs) {
			if (!unit.rcjRules[rcj.sequenceNbr]) unit.rcjRules[rcj.sequenceNbr] = [];
			let existOne = unit.rcjRules[rcj.sequenceNbr].find(
				(f: { sequenceNbr: any }) => f.sequenceNbr == rule.sequenceNbr
			);
			if (existOne) {
				existOne.selectedRule = rule.selectedRule;
			} else {
				unit.rcjRules[rcj.sequenceNbr].push(rule);
			}
			// 排序
			unit.rcjRules[rcj.sequenceNbr] = (
				unit.rcjRules[
					rcj.sequenceNbr
				] as GljBatchOperationalConversionRuleItemDto[]
			).sort((a, b) => {
				if (a.index || b.index) {
					return a.index - b.index;
				} else {
					return (
						this.sortedKind.indexOf(+a.kind) - this.sortedKind.indexOf(+b.kind)
					);
				}
			});
		}
	}

	async _caculateRcjRules(
		constructId: string,
		singleId: string,
		unitId: string,
		rcjs: string | any[],
		line: any
	) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let allRcjRules = unit.rcjRules;
		const materialCodes: string[] = [];
		for (const rcj of rcjs) {
			// 如果是kind 3 type f 保存标准换算的编号 后续相同的编号不参与换算
			let rcjRules = allRcjRules[rcj.sequenceNbr];
			if (!rcjRules || rcjRules.length == 0) continue;
			for (const rule of rcjRules) {
				if (
					rule.kind == "3" &&
					rule.type == "f" &&
					materialCodes.includes(rcj.materialCode)
				)
					continue;
				let excuted: boolean;
				if (rule.kind == "2") {
					excuted = await this._doReplaceRcj(
						constructId,
						singleId,
						unitId,
						rcj,
						rule
					);
					// return null;
				} else {
					let excutedRules = await this._doConversionRcjRules(
						unit,
						constructId,
						singleId,
						unitId,
						rcj,
						rule
					);
					if (!excutedRules) return;
					excuted = await this._doCaculateRcjResQty(
						constructId,
						singleId,
						unitId,
						rcj,
						excutedRules,
						line
					);
				}
				if (rule.kind == 2 && excuted) {
					// 类型2执行后其实数据已经替换 不再执行后续的
					// rcj 删除规则链
					unit.rcjRules[rcj.sequenceNbr] = [];
					break;
				}
			}
			materialCodes.push(rcj.materialCode);
		}
	}

	/**
	 * kind1, kind3-a, kind3-d, kin3-e1, kind3-e2
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param rcj
	 * @param rule
	 * @return {undefined}
	 * @private
	 */
	async _doConversionRcjRules(
		unitProject: any,
		constructId: string,
		singleId: string,
		unitId: string,
		rcj: any,
		rule: GljBatchOperationalConversionRuleItemDto
	): Promise<
		{
			findType: string;
			findAttr: string;
			math: string;
		}[]
	> {
		let excuteRules;
		if (rule.kind == "1") {
			excuteRules = this._convertKind1Rule(rule, unitProject);
		} else if (rule.kind == "3") {
			if (rule.type == "a") {
				excuteRules = this._convertKind3ARule(rule);
			}
			if (rule.type == "d") {
				excuteRules = this._convertKind3DERule(rule);
			}
			if (rule.type == "e1" || rule.type == "e2") {
				excuteRules = this._convertKind3DERule(rule);
			}
			if (rule.type == "f") {
				excuteRules = this._convertKind3FRule(rule);
			}
			if (rule.type == "g") {
				excuteRules = this._convertKind3GRule(rule);
			}
		} else if (rule.kind == "0") {
			excuteRules = this._convertKind1Rule(rule, unitProject);
		}

		return excuteRules;
	}

	_doCaculateRcjResQty(
		constructId: string,
		singleId: string,
		unitId: string,
		rcj: { [x: string]: any; resQty: any; isFromConversionF: any },
		excuteRules: { findAttr: string; findType: string; math: string }[],
		line: any
	) {
		let excuted = false;
		for (let i = 0; i < excuteRules.length; ++i) {
			let oneRule = excuteRules[i];
			if (!oneRule) continue;
			// TODO 这里决定是否将该规则应用到 人材机数据中
			// 如果没有findAttr 正常处理kind1系数
			if (!oneRule.findAttr) {
				// console.log(oneRule.math);
				// 根据模式选择性屏蔽主材
				if (rcj.kind == RCJKind.主材 && !line.mainMatConvertMod) continue;
				// 处理Math.pow有可能出现的问题
				rcj.resQty = NumberUtil.numberScale(
					eval(`${rcj.resQty}${oneRule.math}`),
					6
				);
				if (rcj.tempDeleteBackupResQty) rcj.tempDeleteBackupResQty = rcj.resQty;
				excuted = true;
			} else {
				// 始终屏蔽主材 不参与计算
				if (oneRule.findAttr == "C" && rcj.kind == RCJKind.主材) continue;
				// 人材机下的材料编码 是否是这个规则中math包含的材料编码
				if (
					rcj[oneRule.findType] == oneRule.findAttr ||
					this.rcjKindConvert[rcj[oneRule.findType]] === oneRule.findAttr
				) {
					// TODO 勾选
					// 编码匹配或者值匹配
					rcj.resQty = NumberUtil.numberScale(
						eval(rcj.resQty + oneRule.math),
						6
					);
					if (rcj.tempDeleteBackupResQty)
						rcj.tempDeleteBackupResQty = rcj.resQty;
					excuted = true;
				}
				/*
                    f规则中有种公式是"增C00271 +0.48*(V-0)/100"，这种公式无论定额下有没有该C00271人材机，都需要新增这条人材机。
                    所以如果这条定额下原本有C00271，但是执行f规则后还会新增一条C00271，导致有2条C00271，但是只有标准换算f新增的人材机需要执行消耗量的计算。所以f规则新增人材机时标记isFromConversionF。
                    所以这里需要判断是否是以增开头且截取增以后的值等于rcj的编码，且该人材机是由标准换算isFromConversionF得来的
                 */
				if (
					oneRule.findAttr.startsWith("增") &&
					rcj[oneRule.findType] == oneRule.findAttr.substring(1) &&
					rcj.isFromConversionF
				) {
					rcj.resQty = NumberUtil.numberScale(
						eval(rcj.resQty + oneRule.math),
						6
					);
					if (rcj.tempDeleteBackupResQty)
						rcj.tempDeleteBackupResQty = rcj.resQty;
					excuted = true;
				}
			}
		}

		return excuted;
	}

	/**
	 * kind2 替换人材机
	 * @param rcj
	 * @param rule
	 * @return {undefined}
	 * @private
	 */
	async _doReplaceRcj(
		constructId: string,
		singleId: string,
		unitId: string,
		rcj: { [x: string]: any; standardId: any; sequenceNbr: any; deId: any },
		rule: GljBatchOperationalConversionRuleItemDto
	) {
		let unit: GljUnitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		const historyMap = new Map(unit.lastMaterialReplaceInfo[rcj.deId] || []);
		const historyReplace = historyMap.get(rule.sequenceNbr);
		let toCode = rule?.clpb?.detailsCode || historyReplace;
		rule.math = rule.rcjId + "^" + rule.libraryCode + ":" + toCode;
		let findCode = rule.rcjId;

		// 最近一次材料替换信息
		if (historyReplace !== toCode) {
			historyMap.set(rule.sequenceNbr, toCode);
			unit.lastMaterialReplaceInfo[rcj.deId] = [...historyMap.entries()];
		}
		let findStandId = findCode;
		if (rcj.standardId == findStandId) {
			// 删除本人材机的配合比
			if (Array.isArray(unit.rcjDetailList)) {
				unit.rcjDetailList = unit.rcjDetailList.filter(
					(f: any) => f.rcjId !== rcj.sequenceNbr
				);
			}
			// 兼容22
			const is2022 = rule.libraryCode.startsWith(ConstantUtil.YEAR_2022);
			let tableName = is2022 ? "base_rcj_2022" : "base_rcj";
			// 替换人材机
			let sql =
				"select sequence_nbr as sid from " +
				tableName +
				" where library_code = ? and material_code = ?";
			let baseIndex = (
				this.app.betterSqlite3DataSource
					.prepare(sql)
					.get(rule.libraryCode, toCode) as {
					sid: any;
				}
			).sid;
			let { newRcj, pb } = await this.service.rcjProcess.addRcjLineOnOptionMenu(
				constructId,
				singleId,
				unitId,
				baseIndex,
				rcj.deId,
				is2022
			);
			for (let attr in newRcj) {
				if (
					[
						"standardId",
						"sequenceNbr",
						"constructor",
						"resQty",
						"initResQty",
					].includes(attr)
				) {
					continue;
				}
				if (rcj[attr]) {
					rcj[attr] = newRcj[attr];
				}
			}
			// 存入配合比
			if (pb?.length > 0) {
				pb.forEach((p: { rcjId: any }) => (p.rcjId = rcj.sequenceNbr));
			}
			if (ObjectUtils.isEmpty(unit.rcjDetailList)) {
				unit.rcjDetailList = [];
			}
			unit.rcjDetailList.concat(pb);

			return true;
		}

		return false;
	}

	// addRcjByCodees = (
	// 	constructId: any,
	// 	singleId: any,
	// 	unitId: any,
	// 	deId: any,
	// 	math: any
	// ) => {
	// 	let unitProject = PricingFileFindUtils.getUnit(
	// 		constructId,
	// 		singleId,
	// 		unitId
	// 	);
	// 	let codes = this._getCodes(math);
	// 	let deRcjs = unitProject.constructProjectRcjs.filter(
	// 		(f: { deId: any }) => f.deId == deId
	// 	);
	// 	let erjs = deRcjs;
	// 	let notExistCode = [];
	// 	for (let i = 0; i < codes.length; ++i) {
	// 		let code = codes[i];
	// 		let codeEist = false;
	// 		for (let j = 0; j < erjs.length; ++j) {
	// 			if (erjs[j].materialCode == code) {
	// 				codeEist = true;
	// 				continue;
	// 			}
	// 		}
	// 		if (!codeEist) {
	// 			notExistCode.push(code);
	// 		}
	// 	}

	// 	let news = [];
	// 	for (let i = 0; i < codes.length; ++i) {
	// 		let sql =
	// 			"select b.*, r.res_qty\n" +
	// 			"from base_rcj b\n" +
	// 			"         left join base_de_rcj_relation r on b.sequence_nbr = r.rcj_id\n" +
	// 			"where r.material_code = ? limit 1";
	// 		let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(codes[i]);
	// 		let baseRcjs = SqlUtils.convertToModel(sqlRes);
	// 		news.push(baseRcjs[0]);
	// 	}

	// 	unitProject.constructProjectRcjs =
	// 		unitProject.constructProjectRcjs.concat(news);
	// };

	/**
	 * 类型1 转换
	 * @param rule
	 * @return {{findAttr: string, findType: string, math: string}}
	 * @private
	 */
	_convertKind1Rule(
		rule: GljBatchOperationalConversionRuleItemDto,
		unitProject: GljUnitProject
	) {
		const ruleMath = rule.math;
		return ruleMath.split(",").map((oriMath) => {
			// findType 最终是人材机的属性key 用来取对应属性的值做比较
			let findType, findAttr, math;
			let singleIndex = oriMath.match("[*+-/]")?.index;
			if (oriMath.indexOf(" ") === -1) {
				// kind1中的常规系数处理
				// 是 （R/C/J）+-*/ (num) 或 *Num格式
				findType = "kind";
				findAttr = oriMath.substring(0, singleIndex);
				math = oriMath.substring(singleIndex);
			} else {
				// kind1 中的math 存在对特定材料处理
				// 是 Hcode code ?Num
				findType = "materialCode";
				const mathSplit = oriMath.split(" ");
				let orgMath: string;
				if (mathSplit.length == 2) {
					// TODO 替换材料
					const [from, to] = mathSplit;
					// findAttr = oriMath.split(" ")[1];
					// orgMath = oriMath.split(" ")[2];
					this.replaceMaterial(unitProject, from.slice(1), to);
					return void 0;
				} else if (mathSplit.length == 3) {
					findAttr = oriMath.split(" ")[1];
					orgMath = oriMath.split(" ")[2];
				} else {
					throw new Error(`math(${oriMath})解析失败`);
				}
				let mathRes = orgMath.match("[*+-/]");
				let single, num;
				if (!mathRes) {
					single = "+";
					num = orgMath;
				} else {
					single = mathRes[0];
					num = orgMath.substring(mathRes.index + 1);
				}
				math = single + num;
			}
			return {
				findType: findType, // R/C/J --> kind    H20001  --> meraCode    null
				findAttr: findAttr, //   1 23 ...9              ?                 null
				math: math,
			};
		});
	}

	_convertKind3DERule(rule: GljBatchOperationalConversionRuleItemDto) {
		let ruleMath = rule.math;
		// 数仓的数据math中既有中文逗号，也有英文逗号，所以判断是中还是英
		let separator = ruleMath.includes("，") ? "，" : ",";
		let spRules = ruleMath.split(separator);
		let convertRules = [];

		for (let i = 0; i < spRules.length; ++i) {
			let oriMath = spRules[i];
			let findType, findAttr, math;
			let singleIndex = oriMath.match("[*+-/]").index;
			if (oriMath.indexOf(" ") == -1) {
				// 是 （R/C/J）+-*/ (num) 或 *Num格式
				findType = "kind";
				findAttr = oriMath.substring(0, singleIndex);
				math = this._convertMath(oriMath, `${rule.selectedRule}`);
			} else {
				// 是 ZS3-3001 +0.015*(V-15)
				findType = "materialCode";
				findAttr = oriMath.split(" ")[0];
				let orgMath = oriMath.split(" ")[1];
				math = this._convertMath(orgMath, `${rule.selectedRule}`);
			}

			convertRules.push({
				findType: findType, // R/C/J --> kind    H20001  --> meraCode    null
				findAttr: findAttr, //   1 23 ...9              ?                 null
				math: math,
			});
		}

		return convertRules;
	}

	_convertKind3ARule(rule: GljBatchOperationalConversionRuleItemDto) {
		let convertRules: {
			findType: string;
			findAttr: string;
			math: string;
		}[] = [];
		let oriMath = rule.math;
		// 处理 ，n = XXX
		if (oriMath.indexOf("n=") > 0) {
			// 数仓的数据math中既有中文逗号，也有英文逗号，所以判断是中还是英,也可以让数仓统一下。
			let splitMath = oriMath.replace("，", ",").split(",");
			let mathStr = splitMath[0];
			let nstr = splitMath[1].substring(2);
			let regn = new RegExp("\\b(n)\\b", "g");
			oriMath = mathStr.replace(regn, nstr);
		}
		let convertMath = this._convertMath(oriMath, `${rule.selectedRule}`);
		convertRules.push({
			findType: "kind", // R/C/J --> kind    H20001  --> meraCode    null
			findAttr: null, //   1 23 ...9              ?                 null
			math: convertMath,
		});

		return convertRules;
	}

	async _reCaculateSomeThings(
		constructId: string,
		singleId: string,
		unitId: string,
		deItem: {
			quantity(resQty: any, quantity: any): any;
			baseNum: any;
			sequenceNbr: any;
		},
		type: string
	) {
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let deRcjs = unitProject.constructProjectRcjs.filter(
			(f: { deId: any }) => f.deId == deItem.sequenceNbr
		);

		// 处理人材机 合计数量  合价
		let itemBillDe = deItem;

		for (let i = 0; i < deRcjs.length; ++i) {
			let constructProjectRcj = deRcjs[i];
			const baseNum = itemBillDe.baseNum
				? itemBillDe.baseNum["def"] ||
				  itemBillDe.baseNum[constructProjectRcj.kind]
				: 1;
			let percent = 1;
			if (constructProjectRcj.unit === "%") {
				percent = 0.01;
			}
			// 特殊材料的合计数量不可修改，固定为1。不为特殊材料的，计算合计数量
			if (
				!this.service.rcjProcess.specialRcjCode.includes(
					constructProjectRcj.materialCode
				)
			) {
				constructProjectRcj.totalNumber = NumberUtil.multiply(
					NumberUtil.multiply(constructProjectRcj.resQty, itemBillDe.quantity),
					baseNum
				);
				constructProjectRcj.totalNumber = NumberUtil.numberScale(
					NumberUtil.multiply(constructProjectRcj.totalNumber, percent),
					4
				);
			}
			// 市场价：12定额取marketPrice，22定额取priceMarket
			let marketPrice = constructProjectRcj.libraryCode.startsWith(
				ConstantUtil.YEAR_2022
			)
				? constructProjectRcj.priceMarket
				: constructProjectRcj.marketPrice;
			//合价
			constructProjectRcj.total = NumberUtil.numberScale(
				NumberUtil.multiply(constructProjectRcj.totalNumber, marketPrice),
				2
			);
		}

	}

	_getDisPlayRule(rule: GljBatchOperationalConversionRuleItemDto) {
		if (rule.kind == "1") {
			return "[ " + rule.math + "]";
		}
		if (rule.kind == "2") {
			return "[ " + rule.math + "]";
		}
		if (rule.kind == "3" && rule.type == "a") {
			let cit = rule.selectedRule;
			let convertRules = this._convertKind3ARule(rule);
			let ruleConvert = "";
			for (let i = 0; i < convertRules.length; ++i) {
				ruleConvert += convertRules[i].math + "，";
			}
			let spMaths = ruleConvert.split("，");
			let res = "[ ";
			for (let i = 0; i < spMaths.length; ++i) {
				let convertMath = this._convertMath(spMaths[i], `${cit}`);
				let single = convertMath.substring(0, 1);
				let pds = convertMath.substring(1);
				if (pds == "") {
					continue;
				}
				let convertMathRes = NumberUtil.numberScale(eval(pds), 4);
				let oneRule = " " + single + convertMathRes + ";";
				res += oneRule;
			}
			res += " ]";
			return res;
		}
		if (rule.kind == "3" && rule.type == "b") {
			let cit = rule.selectedRule;
			let spMaths = rule.math.replace("，", ",").split(",");
			let deCode = rule.relationDeCode;
			let res = "[ ";
			for (let i = 0; i < spMaths.length; ++i) {
				let convertMath = this._convertMath(spMaths[i], `${cit}`);
				let single = convertMath.substring(0, 1);
				let pds = convertMath.substring(1);
				let convertMathRes = NumberUtil.numberScale(eval(pds), 4);
				let oneRule = "(" + deCode + ")" + "" + single + convertMathRes + ";";
				res += oneRule;
			}
			res += " ]";
			return res;
		}
		if (rule.kind == "3" && rule.type == "c") {
			let deCode = rule.relationDeCode;
			let res = "[ ";
			let convertMath = this._convertMath(rule.math, `${rule.selectedRule}`);

			let single = convertMath.substring(0, 1);
			let pds = convertMath.substring(1);
			let convertMathRes = NumberUtil.numberScale(eval(pds), 4);
			let oneRule = "(" + deCode + ")" + "" + single + convertMathRes + ";";
			res += oneRule + " ]";
			return res;
		}
		if (rule.kind == "3" && rule.type == "d") {
			let cit = rule.selectedRule;
			let spMaths = rule.math.replace("，", ",").split(",");
			let res = "[ ";
			for (let i = 0; i < spMaths.length; ++i) {
				let convertMath = this._convertMath(spMaths[i], `${cit}`);
				let singleIndex = convertMath.match("[*+/]").index;
				let single = convertMath.substr(singleIndex, 1);
				let pds = convertMath.substring(singleIndex);
				let convertMathRes = NumberUtil.numberScale(eval(pds.substr(1)), 4);
				let bct = spMaths[i].substr(0, singleIndex);
				let oneRule = bct + " " + single + convertMathRes + ";";
				res += oneRule;
			}
			res += " ]";
			return res;
		}
		if (rule.kind == "3" && rule.type == "e1") {
			let cit = rule.selectedRule;
			let spMaths = rule.math.replace("，", ",").split(",");
			let res = "[ ";
			for (let i = 0; i < spMaths.length; ++i) {
				let convertMath = this._convertEMath(spMaths[i], cit);
				if (convertMath.indexOf(" ") > 0) {
					let code = convertMath.split(" ")[0];
					let math = convertMath.split(" ")[1];
					let single = math.substring(0, 1);
					let pds = math.substring(1);
					let oneRule = code + single + pds + ";";
					res += oneRule;
				} else {
					let single = convertMath.substring(0, 1);
					let pds = convertMath.substring(1);
					let convertMathRes = NumberUtil.numberScale(eval(pds), 4);
					let oneRule = " " + single + convertMathRes + ";";
					res += oneRule;
				}
			}
			res += " ]";
			return res;
		}
		if (rule.kind == "3" && rule.type == "e2") {
			let cit = rule.selectedRule;
			let spMaths = rule.math.replace("，", ",").split(",");
			let res = "[ ";
			for (let i = 0; i < spMaths.length; ++i) {
				let convertMath = this._convertEMath(spMaths[i], cit);
				if (convertMath.indexOf(" ") > 0) {
					let code = convertMath.split(" ")[0];
					let math = convertMath.split(" ")[1];
					let single = math.substring(0, 1);
					let pds = math.substring(1);
					let oneRule = code + single + pds + ";";
					res += oneRule;
				} else {
					let singleIndex = convertMath.match("[*+/]").index;
					let single = convertMath.substr(singleIndex, 1);
					let pds = convertMath.substring(singleIndex + 1);
					let convertMathRes = NumberUtil.numberScale(eval(pds), 4);
					let bct = spMaths[i].substr(0, singleIndex);
					let oneRule = bct + " " + single + convertMathRes + ";";
					res += oneRule;
				}
			}
			res += " ]";
			return res;
		}
		if (rule.kind == "3" && ["f", "g"].includes(rule.type)) {
			let cit = rule.selectedRule;
			// 数仓的数据math中既有中文逗号，也有英文逗号，所以判断是中还是英,也可以让数仓统一下。
			let spMaths = rule.math.replace("，", ",").split(",");
			let res = "[ ";
			for (let i = 0; i < spMaths.length; ++i) {
				let convertMath = this._convertMath(spMaths[i], `${cit}`);
				let singleIndex = convertMath.match("[*+/]").index;
				let single = convertMath.substr(singleIndex, 1);
				let pds = convertMath.substring(singleIndex);
				let convertMathRes = NumberUtil.numberScale(eval(pds.substr(1)), 4);
				let bct = spMaths[i].substr(0, singleIndex);
				let oneRule = `${bct} ${
					+convertMathRes < 0 && single == "+"
						? convertMathRes
						: `${single}${convertMathRes}`
				};`;
				res += oneRule;
			}
			res += " ]";
			return res;
		}

		return rule.math;
	}

	// _getParticipationRcjs(constructId, singleId, unitId, deId, rule, is2022DeStandard) {
	//     // 查询对应定额的人材机
	//     return PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs.filter(f => f.deId == deId);
	// }

	async _beforePackingRulesToRcj(params: {
		constructId: any;
		singleId: any;
		unitId: any;
		rule: any;
		deId: any;
		rcjs: any;
	}) {
		// 执行挂规则前，doSomething ... 例如挂规则前处理需要新增的人材机
		// 入参
		let { constructId, singleId, unitId, rcjs, rule, deId } = params;

		if (rule.kind != "3") {
			// 目前只有kind3的规则中有增加人材机的情况，不是kind3直接跳出
			return rcjs;
		}
		if (rule.type != "f" && rule.type != "g") {
			// 目前只有f和g类型的规则在这个方法新增rcj，不是f和g直接跳出
			return rcjs;
		}

		// 获取单位和人材机List
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let constructProjectRcjList = unitProject.constructProjectRcjs;

		if (rule.kind == 3) {
			if (rule.type == "f" || rule.type == "g") {
				let isFrom =
					rule.type == "f" ? "isFromConversionF" : "isFromConversionG";
				// 先删除该定额下之前由f或g规则添加的人材机
				let needDelRcjList = constructProjectRcjList.filter(
					(i: { [x: string]: any; deId: any }) => i.deId == deId && i[isFrom]
				);
				needDelRcjList.forEach((i: { sequenceNbr: any }) => {
					let delRcj = this.service.constructProjectRcjService.delConstructRcj(
						i.sequenceNbr,
						constructId,
						singleId,
						unitId
					);
					let delRcjDetail =
						this.service.constructProjectRcjService.delConstructRcjDetail(
							i.sequenceNbr,
							constructId,
							singleId,
							unitId
						);
				});
				// 删除后重新指向constructProjectRcjList
				constructProjectRcjList = unitProject.constructProjectRcjs;
			}
		}

		// 定额下人材机List
		let rcjListOfDe = constructProjectRcjList.filter(
			(i: { deId: any }) => i.deId == deId
		);

		// 规则如果是默认值，跳出
		if (rule.defaultValue == rule.selectedRule) {
			return rcjListOfDe;
		}

		// 处理f和g规则中会新增人材机的情况
		if (rule.kind == 3) {
			// 1.处理kind3中的f和g规则
			if (rule.type == "f" || rule.type == "g") {
				// f和g都： 如果规则中的人材机被删除，则需新增对应人材机且并将消耗量以0作为基数
				// 拆分规则，获取人材机code
				let rcjCodes = this._getCodes(rule.math);
				// 除了增以外的人材机编码集 (ps:先排除规则中以'增'开头的人材机，后面在做特殊处理)
				let rcjCodesBarringZeng = rcjCodes.filter((i) => !i.startsWith("增"));
				// （1.1）收集需要添加的人材机编码
				// 通过规则中的人材机编码和定额下人材机比较，过滤出规则中定额下不存在的人材机
				let notExistRcjs = rcjCodesBarringZeng.filter(
					(i) =>
						!rcjListOfDe.some(
							(rcj: { materialCode: any }) => rcj.materialCode == i
						)
				);
				let needInsertRcjCodes = []; // 需要插入的人材机编码
				needInsertRcjCodes.push(...notExistRcjs);
				if (rule.type == "f") {
					/*
                        f规则中还存在1种情况，
                            1.例'增C00271'不论当前人材机中是否有，都会新增该人材机且将消耗量以0作为基数。
                    */
					// （1.2）收集需要添加的人材机编码
					// 只保留规则中以'增'开头的人材机, 并截取增只保留人材机编码
					let rcjCodesIncludeZeng = rcjCodes
						.filter((i) => i.startsWith("增"))
						.map((i) => i.substring(1));
					needInsertRcjCodes.push(...rcjCodesIncludeZeng);
				}

				if (ObjectUtils.isEmpty(needInsertRcjCodes)) {
					// 无需新增人材机跳出
					return rcjListOfDe;
				}
				// （2）根据人材机编码找到base人材机，并将base人材机转为constructProjectRcj挂到定额下
				// 根据deId 得到定额行，获取定额册编码
				let deInfo =
					this.service.baseBranchProjectOptionService.findLineOnlyById(deId);
				let deLine = deInfo.line; // 定额数据
				let libraryCode = deLine.libraryCode; // 定额册编码
				// 根据人材机编码集和定额册编码，获取base人材机
				let baseRcjArray = await this.service.rcjProcess.listBaseRcjArray(
					libraryCode,
					needInsertRcjCodes
				);
				// 循环baseRcjArray，将base人材机转为constructProjectRcj挂到定额下
				if (ObjectUtils.isNotEmpty(baseRcjArray)) {
					for (let baseRcj of baseRcjArray) {
						// 添加人材机及配比明细后，需要标识人材机的add来源, add来源例如用于在删除人材机时通过这个字段判断及在执行人材机消耗量计算时判断等。
						let rcjId = await this.service.rcjProcess.addRcjData(
							deId,
							baseRcj,
							constructId,
							singleId,
							unitId
						);
						let cpRcjModel = unitProject.constructProjectRcjs.find(
							(i: { sequenceNbr: any }) => i.sequenceNbr == rcjId
						);
						cpRcjModel[
							rule.type == "f" ? "isFromConversionF" : "isFromConversionG"
						] = true;
					}
				}
				// 添加人材机后，返回定额下的人材机List
				return unitProject.constructProjectRcjs.filter(
					(i: { deId: any }) => i.deId == deId
				);
			}
		}
		return rcjs;
	}

	_getCodes = (ruleMath: string) => {
		// 数仓的数据math中既有中文逗号，也有英文逗号，所以判断是中还是英
		let separator = ruleMath.includes("，") ? "，" : ",";
		let rules = ruleMath.split(separator);
		let codes = [];
		for (let i = 0; i < rules.length; ++i) {
			let oneRule = rules[i];
			let ruleCode = oneRule.substring(0, oneRule.indexOf(" "));
			codes.push(ruleCode);
		}
		return codes;
	};

	_convertKind3FRule(rule: GljBatchOperationalConversionRuleItemDto) {
		// kind3 f
		// 目前已知的产品需求和数仓数据可得出结论：kind3的f规则的计算完全可以用kind3DE规则的方法。仅需处理会添加人材机的情况，目前在_beforePackingRules()方法中处理。
		return this._convertKind3DERule(rule);
	}

	_convertKind3GRule(rule: GljBatchOperationalConversionRuleItemDto) {
		// kind3 g
		// 目前已知的产品需求和数仓数据可得出结论：kind3的g规则的计算完全可以用kind3DE规则的方法。仅需处理会添加人材机的情况，目前在_beforePackingRules()方法中处理。
		return this._convertKind3DERule(rule);
	}

	// 切换标准换算模式 使用默认值或当前值
	async switchConversionMod(dto: {
		constructId: string;
		unitId: string;
		deId: string;
		standardConvertMod: StandardConvertMod;
	}) {
		// 获取对应定额数据 修改该定额的标准换算模式
		await this.service.gongLiaoJiProject.gljRuleDetailFullService.switchConversionMod(dto.constructId, dto.unitId, dto.deId, dto.standardConvertMod);
		return true;
	}

	// 切换主材换算模式
	async switchConversionMainMatMod(dto: {
		constructId: string;
		unitId: string;
		deId: string;
		mainMatConvertMod: string;
	}) {
		await this.service.gongLiaoJiProject.gljRuleDetailFullService.switchConversionMainMatMod(dto.constructId, dto.unitId, dto.deId, dto.mainMatConvertMod);
		return true;
	}

	/*************************************************************************************/
	/**
	 * 标准换算
	 * 1: 解析规则 --- 解析规则数据 将后续用到的数据全部解析出来以备后续使用
	 * 2：准备数据 --- 根据解析的规则 确定最终的人材机列表或定额列表 新增 替换 等... 对要新增或修改（除消耗量）的人材机或定额进行处理
	 * 3: 在解析及准备过程中 在合适的时机 对相关换算信息 名称 等 处理
	 * 3：计算 --- 根据解析数据对每条人材机进行计算 获取每条人材机需要执行的规则 将多个规则公式解析并合并 执行合并后的计算公式得出结果
	 */
	async standardConvert(
		constructId: string,
		singleId: string,
		unitId: string,
		deId: string,
		rules: GljBatchOperationalConversionRuleItemDto[]
	) {
		try {
			// if (this.conversionProcessInfo[constructId])
			// 	throw new Error("正在执行标准换算,请稍后再试！");
			// this.conversionProcessInfo[constructId] = {};
			const unit: GljUnitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);

			let defaultConcersions = await this.getDefDonversion2(constructId, singleId, unitId, deId);

			let constructProjectRcjs: Array<GljConstructProjectRcj> = await this.service.gongLiaoJiProject.gljProjectCommonService.getConstructProjectRcjs(constructId, unitId);
			// 清除过程历史数据 本次执行换算会重新填充数据
			unit.constructProjectRcjs = constructProjectRcjs
				.map((v) => {
					// 清除由标准换算 添加的数据 后续重新执行过程
					if (v.deId == deId && v.addFromConversionRuleId) return null;
					if (v.deId == deId && v.materialReplaceHistory) {
						v.materialCode = v.materialReplaceHistory.materialCode;
						v.materialName = v.materialReplaceHistory.materialName;
						v.dePrice = v.materialReplaceHistory.dePrice;
					}
					return v;
				})
				.filter((v) => !!v);
			const RCJs = unit.constructProjectRcjs.filter((v) => v.deId == deId);
			// const RCJRules = unit.rcjRules;
			if (!Array.isArray(RCJs) || RCJs.length <= 0) return;
			// const { line, belong } =
			// 	this.service.baseBranchProjectOptionService.findLineOnlyById(deId) as {
			// 		line: GsItemBillProject | GsMeasureProjectTable;
			// 		belong: "fbfx" | "csxm";
			// 	};
			const belong = "fbfx";
			let line: GsItemBillProject = await this.service.gongLiaoJiProject.gljProjectCommonService.findLineOnlyById(constructId, unitId, deId);
			// 从所有规则（包括标准换算和统一换算）中筛选出 所有本次启用的规则
			const changedRules = this.combineRules(unit, line, rules)
				.filter((v) => {
					if (v.selectedRule == "NaN") return false;
					if (v.selectedRule) {
						if (v.selectedRule == v.defaultValue) return false;
						return true;
					}
					if (v.ruleInfo) {
						if (v.ruleInfo == v.relation) return false;
						return true;
					}
					return !!v.selected;
				})
				.sort((a, b) => a.index - b.index)
				.map((v) => {
					// 从快照中恢复必要但前端没传的数据 (材料配比)
					if (!v.clpb) {
						const history = line.conversionList.find(
							(item) => item.sequenceNbr == v.sequenceNbr
						);
						if (history?.clpb) v.clpb = history.clpb;
					}
					return v;
				});
			// 从所有规则中筛选  未启用的规则ID
			const cancelRuleIds = rules
				.filter(
					(v) => !changedRules.find((c) => c.sequenceNbr == v.sequenceNbr)
				)
				.map((v) => v.sequenceNbr);
			// 处理表格中的展示信息
			if (
				changedRules.length <= 0 &&
				Array.isArray((line as GsItemBillProject).appendType)
			)
				(line as GsItemBillProject).appendType = (
					line as GsItemBillProject
				).appendType.filter((v) => v != "换");
			// 换算历史快照
			this.conversionHistorySnapshot(line, rules);
			// 解析规则元数据
			const ruleMetadata = await this.parseRule(changedRules, RCJs);
			// 新增定额行
			const ruleIdToLineId = (
				await this.addLine({
					ruleMetadata,
					constructId,
					unitId,
					singleId,
					line,
					unit,
					changedRules,
					belong,
				})
			)?.ruleIdToLineId;
			this.deleteLine(cancelRuleIds, unit);
			// 修改换算信息列表的信息
			this.updateConversionInfo(line, changedRules, ruleMetadata);
			// 修改定额行，修改或填充定额行的一些信息
			this.updateLine(line, changedRules, ruleMetadata);
			// 生成材料的处理信息
			const materialReplaceInfo = this.generateDataProcessInfo(ruleMetadata);
			// 处理数据
			await this.processData({
				materialReplaceInfo,
				unit,
				constructId,
				unitId,
				deId,
				singleId,
				line,
			});
			// 执行消耗量计算
			await this.computeResQty({
				ruleMetadata,
				RCJs: unit.constructProjectRcjs.filter((v) => v.deId == deId),
				ruleIdToLineId,
				unit,
				line,
				cancelRulesIds: cancelRuleIds,
			});
			await this.service.gongLiaoJiProject.gljProjectCommonService.gsItemGsBillProject2De(line, constructId, unitId, deId)
		} catch (error) {
			throw error;
		} finally {
			// 重置标准换算过程数据
			// delete this.conversionProcessInfo[constructId];
		}
	}

	private updateConversionInfo(
		line: GsItemBillProject | GsMeasureProjectTable,
		changedRules: GljBatchOperationalConversionRuleItemDto[],
		ruleMetadata: RuleMetadataGroup
	) {
		line.conversionInfo = changedRules
			.map((v) => {
				const conversionInfoItem = new GsConversionInfoItem();
				conversionInfoItem.ruleId = v.sequenceNbr;
				conversionInfoItem.source = "标准换算";
				conversionInfoItem.conversionExplain = v.relation;
				conversionInfoItem.conversionString = v.math;
				return conversionInfoItem;
			})
			.map((v) => {
				const ruleId = v.ruleId;
				const ruleMetadataGroup = ruleMetadata.filter(
					(r) => r.ruleId == ruleId
				);
				const replaceInfo = ruleMetadataGroup.flatMap(
					(item) => item.materialReplaceInfo
				);
				const toMaterialNames = replaceInfo
					.map((v) =>
						v.toMaterialName.includes(v.toSpecification)
							? v.toMaterialName
							: `${v.toMaterialName}${v.toSpecification || ""}`
					)
					.filter((v) => !!v.trim())
					.join(";");
				v.conversionExplain = toMaterialNames || v.conversionExplain;
				if (!v.conversionString && toMaterialNames) {
					v.conversionString = replaceInfo
						.map((v) => `H${v.fromMaterialCode} ${v.toMaterialCode}`)
						.join(";");
					v.conversionExplain = `换算材料:${v.conversionExplain}`;
				}
				return v;
			});
	}

	resetLineSuffix(line: GsItemBillProject | GsMeasureProjectTable) {
		if (
			Array.isArray(line.nameSuffixHistory) &&
			line.nameSuffixHistory.length > 0
		) {
			// 恢复名称
			for (const history of line.nameSuffixHistory) {
				line.name = line.name.replace(history, "").trim();
			}
		}
		// if (line.codeSuffixHistory) {
		// 	// 恢复编码
		// 	if ((line as ItemBillProject).bdCode) {
		// 		(line as ItemBillProject).bdCode = (line as ItemBillProject).bdCode
		// 			.split(`${line.codeSuffixHistory}`)
		// 			.filter((v) => !!v)
		// 			.join("");
		// 	} else {
		// 		(line as MeasureProjectTable).fxCode = (
		// 			line as MeasureProjectTable
		// 		).fxCode
		// 			.split(`${line.codeSuffixHistory}`)
		// 			.filter((v) => !!v)
		// 			.join("");
		// 	}
		// }
	}

	private updateLine(
		line: GsItemBillProject | GsMeasureProjectTable,
		changedRules: GljBatchOperationalConversionRuleItemDto[],
		ruleMetadata: RuleMetadataGroup
	) {
		// 判断是否需要更新 如果是标准换算需要新增定额 那应该更新新增定额的信息 而非当前定额
		const uniteChangeRule = changedRules.filter((v) => v.isUniteRule);
		const standardRule = changedRules.filter((v) => !v.isUniteRule);
		const redArray = standardRule
			.map((v) => {
				const metadata = ruleMetadata.filter(
					(item) => v.sequenceNbr == item.ruleId
				);
				// 如果是新增定额并计算 修改的是是新增的定额 不修改当前的定额
				if (v.math) {
					if (metadata[0].deAddType == DeAddType.AddAndComputeOwn) return null;
					return `[${v.math}]`;
				} else if (v.kind == "2") {
					if (Array.isArray(metadata) && metadata.length > 0) {
						return metadata
							.flatMap((v) => v.materialReplaceInfo)
							.map((v) => `[H${v.fromMaterialCode} ${v.toMaterialCode}]`)
							.join(",");
					}
					return null;
				} else {
					return null;
				}
			})
			.filter((v) => !!v);
		const blackArray = uniteChangeRule
			.map((v) => {
				const metadata = ruleMetadata.filter(
					(item) => v.sequenceNbr == item.ruleId
				);
				// 如果是新增定额并计算 修改的是是新增的定额 不修改当前的定额
				if (v.math) {
					if (metadata[0].deAddType == DeAddType.AddAndComputeOwn) return null;
					return `[${v.math}]`;
				} else if (v.kind == "2") {
					if (Array.isArray(metadata) && metadata.length > 0) {
						return metadata
							.flatMap((v) => v.materialReplaceInfo)
							.map((v) => `[H${v.fromMaterialCode} ${v.toMaterialCode}]`)
							.join(",");
					}
					return null;
				} else {
					return null;
				}
			})
			.filter((v) => !!v);
		// const codeSuffix = codeSuffixNested ? `\n[${codeSuffixNested}]` : "";
		const nameSuffixArr = changedRules
			.map((v) => {
				if (
					ruleMetadata.find((item) => item.ruleId == v.sequenceNbr)
						?.deAddType == DeAddType.AddAndComputeOwn
				)
					return null;
				return `${v.relation}`;
			})
			.filter((v) => !!v);
		const nameSuffix = nameSuffixArr.join(" ");
		this.resetLineSuffix(line);
		// 修改
		line.name = `${line.name} ${nameSuffix}`;
		if ((line as GsItemBillProject).deCode) {
			if (redArray) {}(line as GsItemBillProject).appendType.push("换");
		}
		line.redArray = redArray;
		line.blackArray = blackArray;
		line.codeSuffixHistory = redArray;
		line.nameSuffixHistory = nameSuffixArr;
	}

	private async addLine(data: {
		ruleMetadata: RuleMetadataGroup;
		constructId: string;
		singleId: string;
		unitId: string;
		line: GsItemBillProject | GsMeasureProjectTable;
		unit: GljUnitProject;
		changedRules: GljBatchOperationalConversionRuleItemDto[];
		belong: "fbfx" | "csxm";
	}): Promise<{
		hasConversion: boolean;
		ruleIdToLineId: { ruleId: string; lineId: string }[];
	}> {
		const {
			ruleMetadata,
			constructId,
			unitId,
			singleId,
			line,
			unit,
			changedRules,
			belong,
		} = data;
		const addDeRules = ruleMetadata.filter(
			(v) => v.deAddType != null && v.relationDeId
		);
		if (addDeRules.length <= 0) return;
		const des = await this.app.gljAppDataSource
			.getRepository(
				ruleMetadata[0].libraryCode.startsWith("2022") ? GsBaseDe : GsBaseDe
			)
			.findBy({
				sequenceNbr: In(addDeRules.map((v) => v.relationDeId)),
			});
		let hasConversion = false;
		const ruleIdToLineId = [];
		for (const de of des) {
			if (line.standardId == de.sequenceNbr) continue;
			const rule = addDeRules.find((v) => v.relationDeId == de.sequenceNbr);
			// 判断是否已新增
			const exitsLine =
				unit.itemBillProjects.find(
					(v) =>
						v.standardId == de.sequenceNbr &&
						v.fromConversionRuleId == rule.ruleId
				) ||
				unit.measureProjectTables.find(
					(v) =>
						v.standardId == de.sequenceNbr &&
						v.fromConversionRuleId == rule.ruleId
				);
			if (!exitsLine) {
				// const { data }: { data: ItemBillProject | MeasureProjectTable } =
				// 	await this.service.itemBillProjectOptionService.fillDataFromIndexPage(
				// 		constructId,
				// 		singleId,
				// 		unitId,
				// 		{
				// 			createDeId: de.sequenceNbr,
				// 			parentId: line.sequenceNbr,
				// 		},
				// 		"04",
				// 		de.sequenceNbr,
				// 		de.unit,
				// 		null,
				// 		null,
				// 		1,
				// 		null,
				// 		de.libraryCode
				// 	);
				// const { line: newLine, belong: _ } =
				// 	this.service.baseBranchProjectOptionService.findLineOnlyById(
				// 		data.sequenceNbr
				// 	) as {
				// 		line: ItemBillProject | MeasureProjectTable;
				// 		belong: "fbfx" | "csxm";
				// 	};
				let newLine: GsItemBillProject | GsMeasureProjectTable = null;
				// let newLine: GsItemBillProject | GsMeasureProjectTable =
				// 	await new InsertStrategy({
				// 		constructId,
				// 		singleId,
				// 		unitId,
				// 		pageType: "fbfx",
				// 	}).execute({
				// 		pointLine: { ...line },
				// 		indexId: de.sequenceNbr,
				// 		newLine: { kind: "04" },
				// 		libraryCode: ruleMetadata[0].libraryCode,
				// 		rcjFlag: 0,
				// 		unit: unitId,
				// 		option: "insert",
				// 	});
				// const insertStrategy = new InsertStrategy({
				// 	constructId,
				// 	singleId,
				// 	unitId,
				// 	pageType: belong,
				// });
				// line.parentId;
				// const newLine = await insertStrategy.executeJq({
				// 	pointLine: line,
				// 	newLine: { kind: "04" },
				// 	indexId: de.sequenceNbr,
				// 	libraryCode: de.libraryCode,
				// 	option: "save",
				// });
				newLine.fromConversionRuleId = addDeRules.find(
					(v) => v.relationDeId == de.sequenceNbr
				).ruleId;
				newLine.conversionInfo = [];
				newLine.conversionList = [];
				(newLine as GsItemBillProject).appendType.push("换");
				await this.standardConvert(
					constructId,
					singleId,
					unitId,
					newLine.sequenceNbr,
					changedRules.filter((v) => v.sequenceNbr == rule.ruleId)
				);
				ruleIdToLineId.push({
					ruleId: rule.ruleId,
					lineId: newLine.sequenceNbr,
				});
			} else {
				ruleIdToLineId.push({
					ruleId: rule.ruleId,
					lineId: exitsLine.sequenceNbr,
				});
				await this.standardConvert(
					constructId,
					singleId,
					unitId,
					exitsLine.sequenceNbr,
					changedRules.filter((v) => v.sequenceNbr == rule.ruleId)
				);
			}
			hasConversion = true;
		}
		return { hasConversion, ruleIdToLineId: ruleIdToLineId };
	}

	// 根据取消的规则删除对应的定额
	async deleteLine(cancelRuleIds: string[], unit: GljUnitProject) {
		// 删除换算新增的定额
		let itemBillProjects: Array<GsItemBillProject> = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByUnit(unit.constructId, unit.sequenceNbr);
		const delItemBillIds = itemBillProjects
			.filter(
				(v) =>
					v.fromConversionRuleId &&
					cancelRuleIds.includes(v.fromConversionRuleId)
			)
			.map((v) => v.sequenceNbr);
		for (const id of delItemBillIds) {
			(unit.itemBillProjects as any).removeNode(id);
		}
	}

	// 生成处理数据信息
	private generateDataProcessInfo(
		ruleMetadata: RuleMetadataGroup
	): (MaterialEditInfoItem & {
		ruleId: string;
		libraryCode: string;
	})[] {
		if (ruleMetadata?.length <= 0) return null;
		return ruleMetadata.flatMap((v) =>
			v.materialReplaceInfo.map((info) => ({
				...info,
				ruleId: v.ruleId,
				libraryCode: v.libraryCode,
			}))
		);
	}

	// 处理数据
	private async processData(data: {
		unit: GljUnitProject;
		materialReplaceInfo: (MaterialEditInfoItem & {
			ruleId: string;
			libraryCode: string;
		})[];
		constructId: string;
		singleId: string;
		unitId: string;
		deId: string;
		line: GsItemBillProject | GsMeasureProjectTable;
	}) {
		const {
			unit,
			materialReplaceInfo,
			constructId,
			singleId,
			unitId,
			deId,
			line,
		} = data;
		if (!materialReplaceInfo) return;
		// 处理新增或更新人材机
		for (const replaceInfo of materialReplaceInfo) {
			const targetRcj = unit.constructProjectRcjs
				.filter((v) => v.deId == deId)
				.find(
					(v) =>
						v.materialCode == replaceInfo.fromMaterialCode &&
						v.libraryCode == replaceInfo.libraryCode
				);
			if (!targetRcj) {
				// 没有人材机
				if (replaceInfo.type == MaterialEditInfoType.OnlyUpdate) {
					// 仅更新 不做处理
					continue;
				} else if (
					[
						MaterialEditInfoType.UpdateOrAdd,
						MaterialEditInfoType.AwalyAdd,
					].includes(replaceInfo.type)
				) {
					// 更新或新增 及 始终新增 添加对应人材机
					const newRcj = await this.addNewRCJ({
						replaceInfo,
						constructId,
						singleId,
						unitId,
						line,
						unit,
					});
					newRcj.addFromConversionRuleId = replaceInfo.ruleId;
				}
			} else {
				// 有人材机
				if (
					replaceInfo.type == MaterialEditInfoType.AwalyAdd &&
					targetRcj.addFromConversionRuleId != replaceInfo.ruleId
				) {
					// 有的人材机不是通过标准换算添加的 需要新增
					const newRcj = await this.addNewRCJ({
						replaceInfo,
						constructId,
						singleId,
						unitId,
						line,
						unit,
					});
					newRcj.addFromConversionRuleId = replaceInfo.ruleId;
				} else if (
					[
						MaterialEditInfoType.OnlyUpdate,
						MaterialEditInfoType.UpdateOrAdd,
					].includes(replaceInfo.type)
				) {
					// 对该人材机更新 替换材料 编码 等...
					await this.editeRcjMaterial(targetRcj, replaceInfo);
				}
			}
		}
	}

	// 计算消耗量
	private async computeResQty(data: {
		RCJs: GljConstructProjectRcj[];
		ruleMetadata: RuleMetadataGroup;
		ruleIdToLineId: { ruleId: string; lineId: string }[];
		unit: GljUnitProject;
		line: GsItemBillProject | GsMeasureProjectTable;
		cancelRulesIds: string[];
	}) {
		const { RCJs, ruleMetadata, ruleIdToLineId, unit, line, cancelRulesIds } =
			data;
		// 处理完需要新增的人材机后 开始执行材料替换及消耗量计算
		for (const rcj of RCJs) {
			if (rcj.kind == RCJKind.主材 && !line.mainMatConvertMod) {
				continue;
			}
			// const replaceInfo = this.parseRcjMaterailReplaceInfo({
			// 	rcjStandarId: rcj.standardId,
			// 	formatMathGroup: ruleMetadata,
			// });
			// if (replaceInfo) await this.editeRcjMaterial(rcj, replaceInfo);
			// 当存在冻结的规则ID时 判断冻结的规则ID是否全部被取消 全部被取消后重新参与计算 否则 不参与计算
			let unlock = true;
			if (rcj.freezeRuleIds?.length > 0) {
				for (const freezeRuleId of rcj.freezeRuleIds) {
					if (!cancelRulesIds.includes(freezeRuleId)) {
						unlock = false;
						break;
					}
				}
			}
			if (!unlock) continue;
			const currentRcjApplyRules = ruleMetadata
				// 筛选当前人材机要执行的规则
				.filter(
					(formatMath) =>
						(formatMath.activeKind.includes(rcj.kind) &&
							formatMath.materialCodeInMath.length <= 0) ||
						formatMath.materialCodeInMath.includes(rcj.materialCode) ||
						formatMath.deAddType != DeAddType.AddAndComputeOwn
				);
			delete rcj.ruleIds;
			const finalMath = this.parseRcjFinalMath({
				rcj,
				ruleMetadata: currentRcjApplyRules,
				ruleIdToLineId,
				unit,
				line,
			});
			if (finalMath) {
				try {
					console.log(
						"定额：",
						(line as any).deCode,
						"\n人材机：",
						rcj.materialName,
						"\n类型：",
						this.matchRcjKind(rcj.kind),
						"\n编码：",
						rcj.materialCode,
						rcj.initResQty,
						"\n新增材料编码：",
						currentRcjApplyRules
							.map((v) =>
								v.materialReplaceInfo
									.filter((v) => v.type == MaterialEditInfoType.AwalyAdd)
									.map((v) => `${v.fromMaterialCode} => ${v.toMaterialCode}`)
							)
							.join(";"),
						"\n更新或新增材料编码：",
						currentRcjApplyRules
							.map((v) =>
								v.materialReplaceInfo
									.filter((v) => v.type == MaterialEditInfoType.UpdateOrAdd)
									.map((v) => `${v.fromMaterialCode} => ${v.toMaterialCode}`)
							)
							.join(";"),
						"\n仅更新材料编码：",
						currentRcjApplyRules
							.map((v) =>
								v.materialReplaceInfo
									.filter((v) => v.type == MaterialEditInfoType.OnlyUpdate)
									.map((v) => `${v.fromMaterialCode} => ${v.toMaterialCode}`)
							)
							.join(";"),
						"\n规则：",
						currentRcjApplyRules.map((v) => v.math).join(";"),
						"\n公式：",
						finalMath,
						"\n"
					);
					this.editeRcjResQty(rcj, finalMath);
				} catch (e) {
					console.error(
						`${rcj.materialName}公式解析失败！(${JSON.stringify(
							currentRcjApplyRules
						)}  =>  ${finalMath})`
					);
				}
			} else {
				rcj.resQty = rcj.initResQty;
			}
		}
	}

	private async addNewRCJ(data: {
		replaceInfo: {
			ruleId: string;
			libraryCode: string;
			fromMaterialCode: string;
			toMaterialCode: string;
			type: MaterialEditInfoType;
		};
		constructId: string;
		singleId: string;
		unitId: string;
		line: GsItemBillProject | GsMeasureProjectTable;
		unit: GljUnitProject;
	}) {
		const { replaceInfo, constructId, singleId, unitId, line, unit } = data;
		const baseRcj = await this.app.gljAppDataSource
			.getRepository(
				replaceInfo.libraryCode.startsWith("2022") ? BaseRcj2022 : BaseRcj2022
			)
			.findOneBy({
				libraryCode: replaceInfo.libraryCode,
				materialCode: replaceInfo.toMaterialCode,
			});
		if (!baseRcj) throw new Error(`找不到替换的人材机(${replaceInfo})`);
		// 无人材机 或 无该规则新增的人材机 则 新增该人材机
		// const insertRcjStrategy = new InsertRcjStrategy({
		// 	constructId,
		// 	singleId,
		// 	unitId,
		// 	projectObj: await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectObjById(constructId)
		// });
		// // 定额行
		// const { sequenceNbr: id } = await insertRcjStrategy.execute({
		// 	de: line,
		// 	pointLine: null,
		// 	rcj: baseRcj,
		// });
		// const id = await this.service.rcjProcess.addRcjData(
		// 	deId,
		// 	baseRcj,
		// 	constructId,
		// 	singleId,
		// 	unitId
		// );
		// TODO
		// return unit.constructProjectRcjs.find((v) => v.sequenceNbr == id);
		return unit.constructProjectRcjs.find((v) => v.sequenceNbr == "id");
	}
	private parseRcjFinalMath(data: {
		rcj: GljConstructProjectRcj;
		ruleMetadata: RuleMetadataGroup;
		ruleIdToLineId: { ruleId: string; lineId: string }[];
		unit: GljUnitProject;
		line: GsItemBillProject | GsMeasureProjectTable;
	}) {
		const {
			rcj,
			ruleMetadata: formatMathGroup,
			ruleIdToLineId,
			unit,
			line,
		} = data;
		let finalMath = "";
		const ruleIds = new Set<string>();
		try {
			formatMathGroup
				.filter((v) => {
					if (v.materialCodeInMath.length > 0) {
						if (v.materialCodeInMath.includes(rcj.materialCode)) return true;
						return false;
					}
					if (v.activeKind.includes(rcj.kind)) return true;
					return false;
				})
				// 构建最终数学公式
				.forEach((mathInfo, index) => {
					// console.log(rcj.materialName, mathInfo, index, "<<<<<<<<<<<<<");
					if (
						mathInfo.materialCodeInMath.length > 0 &&
						!mathInfo.materialCodeInMath.includes(rcj.materialCode)
					)
						return;
					const resQty =
						line.standardConvertMod == StandardConvertMod.Default
							? rcj.initResQty
							: rcj.consumerResQty || rcj.initResQty;
					// 综合用工一类 [ 'R00003 +0.023*(40-30)', 'R*1.5', 'R*1.08', 'R*1.2' ]
					// 其他材料 []
					// 素水泥浆 []
					// 水泥 []
					// 干混地面砂浆 [ 'C03124 +0.102*(40-30)' ]
					// 水 [ 'C00001 +0.038*(40-30)' ]
					// 地面石材 [ 'C08082 *1.03' ]
					// 干混砂浆储料罐(带搅拌机) [ 'J00032 +0.012*(40-30)' ]
					// 石料切割机 []
					if (!finalMath) finalMath = mathInfo.parseMath;
					if (
						mathInfo.deAddType == DeAddType.AddAndAccumulateCurrent &&
						ruleIdToLineId?.length > 0
					) {
						// 获取该规则关联的定额ID 筛选人材机 与 当前人材机做累加
						const lineId = ruleIdToLineId.find(
							(v) => v.ruleId == mathInfo.ruleId
						)?.lineId;
						if (!lineId)
							throw new Error(`未找到规则ID为${mathInfo.ruleId}的添加的定额行`);
						const otherRCJ = unit.constructProjectRcjs.find(
							(v) => v.deId == lineId && v.materialCode == rcj.materialCode
						);
						if (otherRCJ) {
							if (index != 0) {
								finalMath = `${finalMath}+${otherRCJ.resQty || 0}`;
							} else {
								// 解析finalMath
								finalMath = `${resQty}+${otherRCJ.resQty || 0}`;
							}
						} else {
							// 新增定额 执行换算 后 累加到当前材料编码一致的定额人材机中
							console.log(`新增定额人材机中未找到与当前人材机相同材料的人材机`);
						}
					} else {
						if (index == 0) {
							// 解析首个数学公式中的变量占位值
							// 后续的变量将替换为前一个解析后的公式
							if (
								(mathInfo.activeKind.includes(RCJKind.材料) ||
									mathInfo.activeKind.includes(RCJKind.人工) ||
									mathInfo.activeKind.includes(RCJKind.机械)) &&
								mathInfo.materialCodeInMath.length <= 0
							) {
								if (/(?<=[+\-*/])[RCJ]|[RCJ](?=[+\-*/])/.test(finalMath)) {
									finalMath = finalMath.replace(
										/(?<=[+\-*/])[RCJ]|[RCJ](?=[+\-*/])/g,
										`${resQty}`
									);
								} else {
									// 公式中既不包含R C J关键字 也没有材料编码
									finalMath = `${resQty}${finalMath}`;
								}
							} else if (
								mathInfo.materialCodeInMath.includes(rcj.materialCode)
							) {
								if (!/[\+\-\*/]/.test(finalMath)) {
									// 不存在运算符
									finalMath = finalMath.replace(rcj.materialCode, "");
								} else {
									finalMath = finalMath.replace(rcj.materialCode, `${resQty}`);
								}
							}
						} else {
							if (
								/(?<=[+\-*/])[RCJ]|[RCJ](?=[+\-*/])/.test(mathInfo.parseMath)
							) {
								finalMath = mathInfo.parseMath.replace(
									/(?<=[+\-*/])[RCJ]|[RCJ](?=[+\-*/])/g,
									`(${finalMath})`
								);
							} else if (mathInfo.parseMath.includes(rcj.materialCode)) {
								if (!/[\+\-\*/]/.test(mathInfo.parseMath)) {
									// 不存在运算符 将材料置为某值
									finalMath = mathInfo.parseMath.replace(rcj.materialCode, "");
								} else {
									finalMath = finalMath.replace(rcj.materialCode, `${resQty}`);
									finalMath = mathInfo.parseMath.replace(
										rcj.materialCode,
										`(${finalMath})`
									);
								}
							} else {
								// 公式中既不包含R C J关键字 也没有材料编码
								finalMath = `(${finalMath})${mathInfo.parseMath}`;
							}
						}
					}
					ruleIds.add(mathInfo.ruleId);
				});
		} finally {
			rcj.ruleIds = [...ruleIds];
		}
		return finalMath;
	}

	private matchRcjKind(rcjKind: RCJKind) {
		switch (+rcjKind) {
			case RCJKind.人工:
				return "人工";
			case RCJKind.机械:
				return "机械";
			case RCJKind.材料:
				return "材料";
			case RCJKind.主材:
				return "主材";
			case RCJKind.其他:
				return "其他";
			case RCJKind.商浆:
				return "商浆";
			case RCJKind.商砼:
				return "商砼";
			case RCJKind.配比:
				return "配比";
			case RCJKind.浆:
				return "浆";
			case RCJKind.砼:
				return "砼";
			case RCJKind.设备:
				return "设备";
			default:
				break;
		}
	}

	private editeRcjResQty(rcj: any, finalMath: string) {
		// 修改对应人材机消耗量
		try {
			const resQty = NumberUtil.numberScale(eval(finalMath), 6);
			if (isNaN(resQty)) throw new Error("计算失败！");
			rcj.resQty = resQty;
		} catch (e) {
			throw e;
		}
		if (rcj.tempDeleteBackupResQty) rcj.tempDeleteBackupResQty = rcj.resQty;
	}

	private async editeRcjMaterial(
		rcj: GljConstructProjectRcj,
		replaceInfo: MaterialEditInfoItem & {
			libraryCode: string;
		}
	) {
		const materialReplaceHistory = {
			materialCode: rcj.materialCode,
			materialName: rcj.materialName,
			dePrice: rcj.dePrice,
		};
		rcj.materialReplaceHistory = materialReplaceHistory;
		Object.assign(rcj, {
			materialCode: replaceInfo.toMaterialCode,
			materialName: replaceInfo.toMaterialName,
			dePrice: replaceInfo.toDePrice,
			specification: replaceInfo.toSpecification,
		});
		// if (Array.isArray(pb) && pb.length > 0)
		// 	pb.forEach((v) => (v.rcjId = rcj.sequenceNbr));
		// unit.rcjDetailList = (unit.rcjDetailList || []).concat(pb);
	}

	private async parseRule(
		changedRules: GljBatchOperationalConversionRuleItemDto[],
		RCJs: GljConstructProjectRcj[]
	): Promise<RuleMetadataGroup> {
		if (changedRules.length <= 0) return [];
		const libraryCode = changedRules[0].libraryCode;
		const mathItemRules = changedRules
			.flatMap((v) => {
				if (v.kind == "2") return v;
				if (!v.math) return null;
				return v.math
					.replace(/，/g, ",")
					.split(",")
					.map((mathItem) => ({ ...v, math: mathItem }));
			})
			.filter((v) => !!v);
		const clpbDetailsCodes = changedRules
			.map((v) => v?.clpb?.detailsCode)
			.filter((v) => !!v);
		const toRcjInfos: BaseRcj2022[] = await this.app.gljAppDataSource
			.getRepository(libraryCode.startsWith("2022") ? BaseRcj2022 : BaseRcj2022)
			.findBy({
				libraryCode,
				materialCode: In(clpbDetailsCodes),
			});
		return await Promise.all(
			mathItemRules.map(async (rule) => {
				if (rule.kind == "2") {
					// 替换材料
					// 不考虑解析math
					const rcjInfo = RCJs.find((v) => v.standardId == rule.rcjId);
					const toRcjInfo: BaseRcj2022 | BaseRcj2022 = toRcjInfos.find(
						(v) => v.materialCode == rule.clpb.detailsCode
					);
					return {
						ruleId: rule.sequenceNbr,
						math: rule.math,
						parseMath: null,
						materialCodeInMath: [],
						activeKind: [],
						// R: false,
						// C: false,
						// J: false,
						initValue: isNaN(+rule.defaultValue) ? null : +rule.defaultValue,
						value: isNaN(+rule.selectedRule) ? null : +rule.selectedRule,
						materialReplaceInfo: [
							{
								fromMaterialCode: rcjInfo.materialCode,
								toMaterialCode: toRcjInfo.materialCode,
								type: MaterialEditInfoType.OnlyUpdate,
								toMaterialName: toRcjInfo.materialName,
								toDePrice:
									toRcjInfo instanceof BaseRcj2022
										? toRcjInfo.price
										: (toRcjInfo as BaseRcj2022).price,
								toUnite: toRcjInfo.unit,
								toType: toRcjInfo.kind.toString(),
								toSpecification: toRcjInfo.specification,
							},
						],
						libraryCode: rule.libraryCode,
						relationDeId: rule.relationDeId || null,
						deAddType: null,
						isUniteRule: rule.isUniteRule,
					};
				}
				// 解析math
				// 1: *n 整体乘
				// 2: R/C/J*n 对应R/C/J 乘
				// 3：HXXXX XXXX *N 对应材料编码 乘
				// 4: HXXXX XXXX 0 对应材料编码 重置0
				// 5: H XXXX n 对应材料编码 重置为n
				// 6: HXXXX YYYY 对应材料编码 替换为 YYYY 其余不变
				// 7： XXXX +/-/*/除 计算公式 对应材料编码 参与计算
				const ruleItem: RuleMetadata = {
					ruleId: rule.sequenceNbr,
					// 数学公式
					math: rule.math,
					parseMath: "",
					// 材料编码
					materialCodeInMath: [],
					activeKind: [],
					// R: false,
					// C: false,
					// J: false,
					initValue: isNaN(+rule.defaultValue) ? null : +rule.defaultValue,
					value: isNaN(+rule.selectedRule) ? null : +rule.selectedRule,
					materialReplaceInfo: [],
					libraryCode: rule.libraryCode,
					relationDeId: rule.relationDeId || null,
					deAddType:
						rule.type == "b"
							? DeAddType.AddAndComputeOwn
							: rule.type == "c"
							? DeAddType.AddAndAccumulateCurrent
							: null,
					// addMaterial: [],
					// editMaterial: [],
					isUniteRule: rule.isUniteRule,
				};
				// 检查材料编码 存在没有空格 但是是 材料编码直接参与计算
				// 匹配大写字母 数字 - _ 连续出现5次或以上的字符串
				if (/H [A-Z0-9_-]{5,}/.test(ruleItem.math)) {
					const addMaterialCodes = ruleItem.math
						.match(/H [A-Z0-9_-]{5,}/g)
						?.map((v) => v.replace("H ", "").trim());
					if (addMaterialCodes) {
						const replaceRcjInfo: BaseRcj2022[] = await this.app.gljAppDataSource
							.getRepository(
								libraryCode.startsWith("2022") ? BaseRcj2022 : BaseRcj2022
							)
							.findBy({
								libraryCode,
								materialCode: In(addMaterialCodes),
							});
						ruleItem.materialReplaceInfo.push(
							...addMaterialCodes.map((v) => {
								const targetReplaceInfo = replaceRcjInfo.find(
									(rcj) => rcj.materialCode == v
								);
								const item: MaterialEditInfoItem = {
									fromMaterialCode: null,
									toMaterialCode: v,
									toMaterialName: targetReplaceInfo?.materialName,
									type: MaterialEditInfoType.AwalyAdd,
									toDePrice:
										targetReplaceInfo instanceof BaseRcj2022
											? targetReplaceInfo.price
											: (targetReplaceInfo as BaseRcj2022)?.price,
									toType: targetReplaceInfo?.kind?.toString(),
									toUnite: targetReplaceInfo?.unit,
									toSpecification: targetReplaceInfo.specification,
								};
								return item;
							})
						);
						ruleItem.materialCodeInMath.push(...new Set(addMaterialCodes));
					}
				} else if (/H[A-Z0-9_-]{5,} [A-Z0-9_-]{5,}/.test(ruleItem.math)) {
					const matches = ruleItem.math.match(
						/H[A-Z0-9_-]{5,} [A-Z0-9_-]{5,}/g
					);
					if (matches) {
						ruleItem.materialReplaceInfo.push(
							...(await Promise.all(
								matches.map(async (v) => {
									let [from, to] = v.split(" ");
									from = from.slice(1).trim();
									to = to.trim();
									const toRcjInfo = await this.app.gljAppDataSource
										.getRepository(
											libraryCode.startsWith("2022") ? BaseRcj2022 : BaseRcj2022
										)
										.findOneBy({
											libraryCode,
											materialCode: to,
										});
									return {
										fromMaterialCode: from,
										toMaterialCode: toRcjInfo.materialCode,
										type: MaterialEditInfoType.UpdateOrAdd,
										toMaterialName: toRcjInfo.materialName,
										toSpecification: toRcjInfo.specification,
										toType: toRcjInfo.kind.toString(),
										toUnite: toRcjInfo.unit,
										toDePrice:
											toRcjInfo instanceof BaseRcj2022
												? toRcjInfo.price
												: (toRcjInfo as BaseRcj2022).price,
									};
								})
							))
						);
						ruleItem.materialCodeInMath.push(
							...new Set(
								ruleItem.materialReplaceInfo.flatMap((v) => [
									v.fromMaterialCode,
									v.toMaterialCode,
								])
							)
						);
					}
				} else if (/[A-Z0-9-_]{5,}/.test(ruleItem.math)) {
					const matches = ruleItem.math.match(/[A-Z0-9-_]{5,}/g);
					if (matches) {
						const materialCodeSet: Set<string> = new Set();
						for (const match of matches) {
							let materialCode = match;
							if (match.startsWith("H")) {
								materialCode = match.slice(1);
								const toRcjInfo = await this.app.gljAppDataSource
									.getRepository(
										libraryCode.startsWith("2022") ? BaseRcj2022 : BaseRcj2022
									)
									.findOneBy({
										libraryCode,
										materialCode,
									});
								ruleItem.materialReplaceInfo.push({
									fromMaterialCode: rule.rcjId,
									toMaterialCode: toRcjInfo.materialCode,
									type: MaterialEditInfoType.UpdateOrAdd,
									toMaterialName: toRcjInfo.materialName,
									toType: toRcjInfo.kind.toString(),
									toUnite: toRcjInfo.unit,
									toSpecification: toRcjInfo.specification,
									toDePrice:
										toRcjInfo instanceof BaseRcj2022
											? toRcjInfo.price
											: (toRcjInfo as BaseRcj2022).price,
								});
							}
							materialCodeSet.add(materialCode);
						}
						// @ts-ignore
						ruleItem.materialCodeInMath.push(...materialCodeSet);
					}
				}
				// ruleItem.addMaterial = addMaterialCodes.map((v) =>
				// 	v.replace("H ", "")
				// );
				// 处理公式中的RU/RD/MOD
				ruleItem.parseMath = ruleItem.math
					.replace(/RU/g, "Math.ceil")
					.replace(/RD/g, "Math.floor")
					.replace(/MOD\(([^)]+),\s*([^)]+)\)/g, "(($1)%($2))")
					.replace(/\^/g, "**")
					// 处理材料编码 H1234 1234 => 1234
					.replace(/H([^\s]*)(\s+\1)/g, "$2")
					// H1234 5678
					.trim();
				if (ruleItem.parseMath.match(/[V][+\-*/)]|[+\-*/(][V]/))
					ruleItem.parseMath = ruleItem.parseMath.replace(
						/V/g,
						`${ruleItem.value}`
					);
				if (ruleItem.parseMath.match(/[R][+\-*/]|[+\-*/][R]/))
					ruleItem.activeKind.push(RCJKind.人工);
				if (
					ruleItem.parseMath.match(/[C][+\-*/]|[+\-*/][C]/) ||
					ruleItem.materialCodeInMath.length > 0
				)
					ruleItem.activeKind.push(RCJKind.材料);
				if (ruleItem.parseMath.match(/[J][+\-*/]|[+\-*/][J]/))
					ruleItem.activeKind.push(RCJKind.机械);
				if (
					!ruleItem.activeKind.includes(RCJKind.人工) &&
					!ruleItem.activeKind.includes(RCJKind.材料) &&
					!ruleItem.activeKind.includes(RCJKind.机械)
				) {
					// 如果没有材料编码并且未检测到对人材机三类任何修改则表示对所有人材机进行修改
					ruleItem.activeKind.push(
						RCJKind.人工,
						RCJKind.材料,
						RCJKind.机械,
						RCJKind.商浆,
						RCJKind.浆,
						RCJKind.砼,
						RCJKind.商砼,
						RCJKind.配比
					);
				}
				return ruleItem;
			})
		);
	}

	// 换算时 把标准和统一换算信息合并 并与本次修改信息整合
	private combineRules(
		unit: GljUnitProject,
		line: GsItemBillProject | GsMeasureProjectTable,
		// 有可能是标准 有可能是统一
		currentRules: GljBatchOperationalConversionRuleItemDto[]
	) {
		const combine = [];
		combine.push(
			...this.formatRuleByUniteRules(
				unit.defaultConcersions[line.sequenceNbr],
				line
			)
		);
		combine.push(...this.formatRuleByConversionList(line.conversionList));
		return combine.map((v) => {
			const current = currentRules.find((c) => c.sequenceNbr == v.sequenceNbr);
			return current ? current : v;
		});
	}

	/**
	 * 换算历史快照
	 */
	private conversionHistorySnapshot(
		line: GsItemBillProject | GsMeasureProjectTable,
		rules: GljBatchOperationalConversionRuleItemDto[]
	) {
		// 查询列表时会初始化 如果这时未初始化 直接报错
		if (!Array.isArray(line.conversionList)) line.conversionList = [];
		line.conversionList = line.conversionList
			.map((v) => {
				const targetRule = rules.find((r) => r.sequenceNbr == v.sequenceNbr);
				if (!targetRule) return v;
				if (typeof targetRule.selected == "string") {
					v.value = targetRule.selected;
				} else {
					v.selected = targetRule.selected;
				}
				v.index = targetRule.index;
				v.ruleInfo = targetRule.ruleInfo;
				v.clpb = targetRule.clpb;
				v.selectedRule = targetRule.selectedRule;
				return v;
			})
			.sort((a, b) => a.index - b.index);
	}

	// 修改消耗量时 保存该条人材机启用规则的快照
	public freezeRuleIds(rcj: GljConstructProjectRcj) {
		if (rcj.ruleIds?.length >= 0) rcj.freezeRuleIds = rcj.ruleIds;
	}
}
enum MaterialEditInfoType {
	/**始终新增 */
	AwalyAdd,
	/**更新或新增 */
	UpdateOrAdd,
	/**仅更新 */
	OnlyUpdate,
}
enum DeAddType {
	/**新增定额并计算 */
	AddAndComputeOwn,
	/**新增定额计算后累加到当前定额 */
	AddAndAccumulateCurrent,
}
module.exports = GljConversionDeService;
type MaterialEditInfoItem = {
	fromMaterialCode: string;
	toMaterialCode: string;
	toMaterialName: string;
	toSpecification: string;
	type: MaterialEditInfoType;
	toType: string;
	toUnite: string;
	toDePrice: number;
};
type MaterialEditInfo = MaterialEditInfoItem[];
type RuleMetadata = {
	ruleId: string;
	/**原始计算公式 */
	math: string;
	/**计算公式解析结果 */
	parseMath: string;
	/**公式中包含的材料编码 */
	materialCodeInMath: string[];
	/**该规则需要应用的人材机类型 */
	activeKind: RCJKind[];
	/**人材机消耗量默认值 */
	initValue: number;
	/**人材机消耗量用户修改值 */
	value: number;
	/**定额库编码 */
	libraryCode: string;
	/**材料编辑信息 */
	materialReplaceInfo: MaterialEditInfo;
	relationDeId: string;
	deAddType: DeAddType;
	// /**需要修改的材料编码 如果不存在 则新增 */
	// editMaterial: string[];
	// /**始终新增人材机 */
	// addMaterial: string[];
	isUniteRule: boolean;
};
type RuleMetadataGroup = RuleMetadata[];
type OptionProps<T> = {
	[K in keyof T]?: T[K];
};
