/**
 * 载价条件设置
 */
const {ResponseData} = require("../../../common/ResponseData");
const {Controller} = require("../../../core");


class JieSuanLoadPriceSetController extends Controller{
    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 批量应用接口更新市场价、价格来源
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async applyLoadingPriceInRcjDetails(args) {

        await this.service.jieSuanProject.jieSuanLoadPriceSetService.applyLoadingPriceInRcjDetails(args);

        /*if(args.priceType!==2&& args.type===2){
            await this.service.jieSuanProject.management.sycnTrigger("unitDeChange");
        }*/
        return ResponseData.success(true);
    }


    /**
     * 点击批量载价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async loadingPrice(args) {
        let result = await this.service.jieSuanProject.jieSuanLoadPriceSetService.loadingPrice(args);
        if (result.length > 0) {
            return ResponseData.success(result);
        }else {  //没有数据  返回fail
            return ResponseData.fail(result);
        }
    }





    /**
     * 结算 鼠标右键清除载价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async jiesuanClearLoadPriceUse(args) {
        let result = await this.service.jieSuanProject.jieSuanLoadPriceSetService.jiesuanClearLoadPriceUse(args);

        return ResponseData.success(result);
    }



    /**
     * 结算中鼠标右键查询人材机关联定额
     * @param args
     * @return {Promise<ResponseData>}
     */
    async jieSuanGetRcjDe(args){

        const result = await this.service.jieSuanProject.jieSuanLoadPriceSetService.jieSuanGetRcjDe(args);

        return ResponseData.success(result);

    }

}

JieSuanLoadPriceSetController.toString = () => '[class JieSuanLoadPriceSetController]';
module.exports = JieSuanLoadPriceSetController;