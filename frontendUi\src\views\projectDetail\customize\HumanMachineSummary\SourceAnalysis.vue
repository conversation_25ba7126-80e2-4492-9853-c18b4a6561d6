<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-18 10:24:03
-->
<template>
  <div class="table-content">
    <div class="content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, isCurrent: true }"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'cell',
        }"
        @edit-closed="editClosedEvent"
        @cell-click="
            cellData => {
              useCellClickEvent(cellData, null, ['ifDonorMaterial']);
            }
          "
        :cell-class-name="selectedClassName"
        class="table-edit-common table-content"
        :data="tableData"
        height="auto"
        keep-source
        :cell-style="cellStyle"
        @current-change="currentChange"
        ref="lyfxTable"
      >
        <vxe-column
          v-for="columns of showColumns"
          v-bind="columns"
        >
          <template #default="{ column, row, $columnIndex }">
            <template v-if="column.field === 'location'">
              <span> {{row.singleName}}/{{row.unitName }}</span>
            </template>
            <template v-else-if="
                  [
                    'priceMarketTotal',
                    'marketPrice',
                    'total',
                    'priceMarketTotal',
                    'priceMarketTaxTotal',
                  ].includes(column.field)
                ">
              {{ isChangeAva(row) ? '-' : row[column.field] }}
            </template>

            <template v-else-if="
                  ['priceMarketTax', 'priceMarket'].includes(column.field)
                ">{{ getValueByDeType('12', row, column.field) }}</template>
            <template v-else-if="column.field === 'ifDonorMaterial'">
              {{ getDonorMaterialText(row.ifDonorMaterial) }}
            </template>
            <template v-else-if="column.field === 'donorMaterialNumber'">
              <span v-if="row.checkIsShow">{{
                  row.donorMaterialNumber
                }}</span>
            </template>

            <template v-else>{{ row[column.field] }}</template>
          </template>

          <template
            v-if="columns.slot"
            #edit="{ column, row, $columnIndex }"
          >

            <template v-if="column.field === 'materialName'">
              <vxe-input
                v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                :clearable="false"
                v-model.trim="row.materialName"
                type="text"
                @blur="clear()"
              ></vxe-input>
              <span v-else>{{ row.materialName }}</span>
            </template>
            <template v-else-if="column.field === 'specification'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.specification"
                type="text"
                @blur="clear()"
                v-if="Number(row.edit) !== 1 && !isOtherMaterial"
              ></vxe-input>
              <span v-else>{{ row.specification }}</span>
            </template>
            <template v-else-if="column.field === 'marketPrice'">
              <vxe-input
                v-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                :clearable="false"
                v-model.trim="row.marketPrice"
                type="text"
                @blur="
                    row.marketPrice = pureNumber(row.marketPrice, 2);
                    clear();
                  "
              ></vxe-input>
              <span v-else>{{
                  isChangeAva(row) ? '-' : row.marketPrice
                }}</span>
            </template>
            <template v-else-if="column.field === 'priceMarket'">
              <template v-if="row.deStandardReleaseYear === '12'">
                <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                <vxe-input
                  v-else-if="
                      row.ifLockStandardPrice !== 1 &&
                      isPartEdit &&
                      !(
                        row.markSum === 1 &&
                        [1, 2].includes(Number(row.levelMark))
                      ) &&
                      Number(row.edit) !== 1 &&
                      !isChangeAva(row) &&
                      !isOtherMaterial
                    "
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                ></vxe-input>
                <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
              </template>
              <vxe-input
                v-else-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                :clearable="false"
                v-model.trim="row.priceMarket"
                type="text"
                @blur="
                    row.priceMarket = pureNumber(row.priceMarket, 2);
                    clear();
                  "
              ></vxe-input>
              <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarket
                }}</span>
            </template>
            <template v-else-if="column.field === 'priceMarketTax'">
              <template v-if="row.deStandardReleaseYear === '12'">
                <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                <vxe-input
                  v-else-if="
                      row.ifLockStandardPrice !== 1 &&
                      isPartEdit &&
                      !(
                        row.markSum === 1 &&
                        [1, 2].includes(Number(row.levelMark))
                      ) &&
                      Number(row.edit) !== 1 &&
                      !isChangeAva(row) &&
                      !isOtherMaterial
                    "
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                ></vxe-input>
                <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
              </template>
              <vxe-input
                v-else-if="
                    row.ifLockStandardPrice !== 1 &&
                    isPartEdit &&
                    !(
                      row.markSum === 1 &&
                      [1, 2].includes(Number(row.levelMark))
                    ) &&
                    Number(row.edit) !== 1 &&
                    !isChangeAva(row) &&
                    !isOtherMaterial
                  "
                :clearable="false"
                v-model.trim="row.priceMarketTax"
                type="text"
                @blur="
                    row.priceMarketTax = pureNumber(row.priceMarketTax, 2);
                    clear();
                  "
              ></vxe-input>
              <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarketTax
                }}</span>
            </template>
            <template v-else>
              <vxe-input
                :clearable="false"
                v-model.trim="row[column.field]"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, reactive, toRaw } from 'vue';
import feePro from '@/api/feePro';
import jieSuanApi from '@/api/jieSuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
import { message } from 'ant-design-vue';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import ObjectUtils from '@/components/qdQuickPricing/utils/ObjectUtils';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import getTableColumns, {
  isDeType,
  getDonorMaterialText,
} from './sourceColumn';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const emits = defineEmits(['getUpList']);
const props = defineProps(['showInfo']);

const store = projectDetailStore();
let tableData = ref([]);
let lyfxTable = ref();
watch(
  () => props.showInfo,
  () => {
    setTableInfo();
  }
);
let showColumns = ref([]);
const setTableInfo = () => {
  showColumns.value = getTableColumns();
  getTableData();
};
onMounted(() => {
  setTableInfo();
});
// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    store.deStandardReleaseYear === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};
let oldTablesData = reactive([]);
const getTableData = async () => {
  const formdata = {
    constructId: store.currentTreeGroupInfo?.constructId,
    rcjSequenceNbr: props.showInfo?.sequenceNbr,
  };
  console.log('---------来源分析', formdata);
  await feePro.rcjFromUnit(formdata).then(res => {
    if (res.status === 200) {
      tableData.value = res.result;
      oldTablesData = JSON.parse(JSON.stringify(res.result));
      console.log('---------进项税额明细弹框结果', res.result);
    }
  });
};
const clear = () => {
  //清除编辑状态
  const $table = lyfxTable.value;
  $table.clearEdit();
};
const editClosedEvent = e => {
  // 市场价修改联动计算
  const { $table, row, column } = e;
  const field = column.field;
  let value = row[field];
  if (
    ['marketPrice', 'priceMarket', 'priceMarketTax', 'taxRate'].includes(
      field
    ) &&
    (value < 0 || value == '')
  ) {
    $table.revertData(row, field);
    return;
  }
  switch (field) {
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }
      break;
  }
  if (field === 'marketPrice' && value > 0) {
    if (row.marketPrice.length > 20) row.marketPrice = value.slice(0, 20);
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (
      Number(row.marketPrice) - Number(row.dePrice)
    ).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (['priceMarket', 'priceMarketTax'].includes(field)) {
    if (field === 'priceMarket' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarketTax = NumberUtil.numberScale2(
        NumberUtil.multiply(
          0.01,
          NumberUtil.multiply(value, NumberUtil.add(100, row.taxRate))
        )
      );
    }
    //含税市场价
    if (field === 'priceMarketTax' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarket = NumberUtil.numberScale2(
        NumberUtil.multiply(
          100,
          NumberUtil.divide(value, NumberUtil.add(100, row.taxRate))
        )
      );
    }
  }
  if (
    ['priceMarket', 'priceMarketTax', 'taxRate', 'marketPrice'].includes(field)
  ) {
    row.sourcePrice = '自行询价';
  }
  row.isChange = true;
  console.log(row);
  setBgColorList(row, column);
  let updateList = getUpdateList();
  console.log('updateList', updateList);
  setProUpdate(updateList);
};
const getUpdateList = () => {
  let list = [];
  let updateList = tableData.value.filter(a => a.isBgCulumn?.length > 0);
  updateList.map(item => {
    let obj = {
      unitId: item.unitId,
      singleId: item.singleId,
      constructProjectRcj: {},
      sequenceNbr: item.sequenceNbr,
    };
    let constructProjectRcj = {};
    for (let a of item.isBgCulumn) {
      constructProjectRcj[a] = item[a];
    }
    obj.constructProjectRcj = constructProjectRcj;
    list.push(obj);
  });
  return list;
};
const setProUpdate = updataData => {
  if (updataData.length > 0) {
    store.SET_HUMAN_UPDATA_DATA({
      isEdit: true,
      name: 'unify-humanMachineSummary',
      updataData: store.humanUpdataData?.updataData,
      adjustFactor: store.humanUpdataData?.adjustFactor,
      sourcePriceData: updataData,
    });
  } else if (
    updataData.length === 0 &&
    !store.humanUpdataData?.updataData &&
    !store.humanUpdataData?.adjustFactor
  ) {
    store.SET_HUMAN_UPDATA_DATA(null);
  }
};
const setBgColorList = (row, column) => {
  const field = column.field;
  let value = row[field];
  let tar = oldTablesData.find(a => a.sequenceNbr === row.sequenceNbr);
  if (tar) {
    let flag = ['marketPrice', 'priceMarket', 'priceMarketTax'].includes(field);
    console.log(
      !(tar[field] && value),
      tar[field],
      value,
      (flag && tar[field] / 1 != value / 1) || (!flag && tar[field] != value)
    );
    let isAllNull =
      [null, undefined, ''].includes(tar[field]) &&
      [null, undefined, ''].includes(value);
    if (
      !isAllNull &&
      ((flag && tar[field] / 1 != value / 1) || (!flag && tar[field] != value))
    ) {
      row.isBgCulumn =
        row.isBgCulumn?.length > 0 ? [field, ...row.isBgCulumn] : [field];
      row.isChange;
    } else {
      if (row.isBgCulumn?.length > 0) {
        let index = row.isBgCulumn?.indexOf(field);
        if (index !== -1) row.isBgCulumn.splice(index, 1);
      }
    }
  }
};
let currentInfo = ref(null);
const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});
const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
};
const isOtherMaterial = computed(() => {
  const { materialCode } = currentInfo.value || {};
  return [
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
  ].includes(materialCode);
});
const cellStyle = ({ row, column }) => {
  if (row.isBgCulumn?.length > 0) {
    if (row.isBgCulumn.includes(column.field)) {
      return {
        backgroundColor: '#F5FFDB !important',
      };
    }
  }
};
const getValueByDeType = (deType, row, field = '') => {
  if (isDeType(deType, row.deStandardReleaseYear)) {
    if (['priceMarketTax', 'priceMarket'].includes(field)) {
      const taxMade = 'priceMarket' === field ? 1 : 0;
      if (Number(store.taxMade) === taxMade) {
        return isChangeAva(row) ? '-' : row.marketPrice;
      }
    }
    return '/';
  }
  return isChangeAva(row) ? '-' : row[field];
};
</script>
<style lang="scss" scoped>
.table-content {
  .content {
    width: 100%;
    height: calc(100% - 45px);
  }
}
</style>
