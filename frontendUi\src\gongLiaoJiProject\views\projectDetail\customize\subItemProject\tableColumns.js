/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-03 11:24:30
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-07-08 18:40:44
 */
import { ref } from 'vue';
import deMapFun from '../deMap';
import { useSubItem } from '@gongLiaoJi/hooks/useGljSubItem.js';
import { Item } from 'ant-design-vue/lib/menu';
import { projectDetailStore } from '@/store/projectDetail';
import { ConstructMajorTypeConstant } from '@gongLiaoJi/views/projectDetail/customize/constant';
const projectStore = projectDetailStore();

const getTableColumns = emits => {
  let { editClosedEvent, tableData } = useSubItem({
    emits,
    pageType: 'fbfx',
  });
  const tableColumns = ref([
    {
      title: '序号',
      field: 'dispNo',
      dataIndex: 'dispNo',
      width: 45,
      autoHeight: true,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '编码',
      field: 'deCode',
      dataIndex: 'deCode',
      align: 'left',
      headerAlign: 'center',
      resizable: true,
      ellipsis: true,
      fixed: 'left',
      edit: true,
      width: 140,
      editableTrigger: 'click',
      editable: ({ record: row }) => {
        // console.log(record)
        // if (row?.kind !== '07') {
        return 'cellEditorSlot';
        // } else {
        //   return false;
        // }
      },
      // editable: ({ record }) => {
      //   return true;
      // },
      // valueParser: ({ newValue, oldValue }) => {
      //   return newValue;
      // },
    },
    {
      title: '类别',
      field: 'type',
      dataIndex: 'type',
      width: 65,
      align: 'center',
      edit: true,
      fixed: 'left',
      ellipsis: true,
      // editableTrigger: 'click',
      editable: ({ record: row }) => {
        // console.log(record)
        //  &&
        //   row.parentKind != '08'
        if (
          row.isTempRemove !== 1 &&
          (deMapFun.isDe(row?.kind) ||
            [2, 4, 5, 6, 7, 8, 9].includes(row.kind)) &&
          ((row?.kind !== '07' && row.isCostDe != 5) || row.isCostDe === 4) &&
          [2, 4, 5, 6, 7, 8, 9].includes(row.deResourceKind) &&
          !(row.kind == '06' && row.isFyrcj == 0) &&
          Number(row.isDataTaxRate) != 0
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '名称',
      field: 'deName',
      dataIndex: 'deName',
      resizable: true,
      width: 260,
      autoHeight: true,
      align: 'left',
      fixed: 'left',
      edit: true,
      editable: ({ record: row }) => {
        // console.log(record) && row.parentKind != '08'
        if (
          ![11, 5].includes(row.isCostDe) &&
          !['00', '07'].includes(row.kind) &&
          (row.deCode !== undefined || row.kind == '01' || row.kind == '02') &&
          !(row.kind == '06' && row.isFyrcj == 0)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '规格型号',
      field: 'specification',
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          ['06', '09', '05'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
      visible: false,
    },
    {
      title: '单位',
      field: 'unit',
      dataIndex: 'unit',
      width: 70,
      edit: true,
      editable: 'cellEditorSlot',
      editable: ({ record: row }) => {
        // console.log(record)
        // &&
        //   row.parentKind != '08'
        if (row.isCostDe && ['项'].includes(row.unit)) {
          return false;
        } else if (
          ![11, 5, 2].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          row.deCode &&
          !['07'].includes(row.kind) &&
          !deMapFun.isBc(row.deCode) &&
          !(row.kind == '06' && row.isFyrcj == 0)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '消耗量',
      field: 'resqtyExp',
      dataIndex: 'resqtyExp',
      resizable: true,
      edit: true,
      width: 80,
      editable: ({ record: row }) => {
        if (
          ![11, 5].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !row.isTopLevelType03 &&
          !['07'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
      // &&
      //     row.parentKind != '08'
      // valueParser: ({ newValue, oldValue, record: row, column }) => {
      //   if (newValue === oldValue) return newValue;
      //   editClosedEvent({ row, column }, +newValue, oldValue);
      //   return +newValue;
      // },
    },
    {
      title: '工程量表达式',
      field: 'quantityExpression',
      width: 120,
      slot: true,
      edit: true,
      ellipsis: true,
      editRender: { autofocus: '.vxe-input--inner' },
      editable: ({ record: row }) => {
        if ([5].includes(row.isCostDe)) {
          return false;
        }
        if (
          row.isTempRemove !== 1 &&
          ['03', '04', '06', '08', '09', '10'].includes(row.kind) &&
          ![11, 1].includes(row.isCostDe) &&
          !(row.kind == '06' && row.isFyrcj == 0)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '工程量',
      field: 'originalQuantity',
      resizable: true,
      dataIndex: 'originalQuantity',
      edit: true,
      ellipsis: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          row.type != '-1' &&
          row.type != '07' &&
          ![11, 5].includes(row.isCostDe) &&
          row.isOriginalQuantity &&
          !(row.kind == '06' && row.isFyrcj == 0)
        ) {
          if (row.kind === '05') {
            if (
              deMapFun.isDeHasQuantity(row) &&
              !deMapFun.isTz(row.deCode) &&
              !deMapFun.isJxxs(row.deCode) &&
              deMapFun.isPartEdit(row.deCode)
            ) {
              return 'cellEditorSlot';
            } else {
              return false;
            }
          } else {
            return 'cellEditorSlot';
          }
        } else {
          return false;
        }
      },
    },

    {
      title: '专业',
      field: 'classiflevel1',
      visible: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: '备注',
      field: 'remark',
      dataIndex: 'remark',
      resizable: true,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        return row.isTempRemove !== 1 && !['00', '05'].includes(row.kind)
          ? 'cellEditorSlot'
          : false;
      },
      // valueParser: ({ newValue, oldValue, record: row, column }) => {
      //   if (newValue === oldValue) return newValue;
      //   editClosedEvent({ row, column }, newValue, oldValue);
      //   return newValue;
      // },
    },
  ]);
  let deArr = [
    {
      title: '单价',
      field: 'baseJournalPrice',
      dataIndex: 'baseJournalPrice',
      resizable: true,
      edit: true,
      width: 70,

      editable: ({ record: row }) => {
        if (
          (row.isTempRemove !== 1 &&
            deMapFun.isDe(row.kind) &&
            !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
            row.type != '-1' &&
            !row.isNumLock &&
            row.ifLockStandardPrice != 1 &&
            !['07', '09'].includes(row.kind) &&
            ![11, 2, 5, 21].includes(row.isCostDe) &&
            !deMapFun.isCgf(row.deCode) &&
            !deMapFun.isBc(row.deCode) &&
            row.isDataTaxRate != 0) ||
          (row.kind == '06' && [4, 5].includes(row.deResourceKind))
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '合价',
      field: 'baseJournalTotalNumber',
      dataIndex: 'baseJournalTotalNumber',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '取费专业',
      field: 'costMajorName',
      dataIndex: 'costMajorName',
      resizable: true,
      edit: true,
      ellipsis: true,
      width: 120,
      editable: ({ record: row }) => {
        if (
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          row.type != '-1' &&
          row.deCode &&
          row.isTopLevelType03
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '超高过滤类别',
    //   field: 'fitHighType',
    //   ellipsis: true,
    // },
    // {
    //   title: '檐高类别',
    //   field: 'eaveHeightType',
    //   ellipsis: true,
    // },
    // {
    //   title: '修缮高度',
    //   field: 'eaveHeightType',
    //   ellipsis: true,
    // },
    {
      title: '施工组织措施类别',
      field: 'measureType',
      ellipsis: true,
      edit: true,
      resizable: true,
      editable: ({ record: row }) => {
        if (
          (row.kind == '04' &&
            ([0, 11, 12, 13, 14].includes(row.isCostDe) ||
              row.isCostDe == undefined)) ||
          ['06', '08', '09', '10'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '垂直运输类别',
    //   field: 'verticalTransType',
    //   ellipsis: true,
    // },
    {
      title: '人工费单价',
      field: 'RDSum',
      visible: false,
      edit: true,
      width: 100,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '人工费合价',
      field: 'rdTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '材料费单价',
      field: 'CDSum',
      visible: false,
      edit: true,
      width: 100,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '材料费合价',
      field: 'cdTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '机械费单价',
      field: 'JDSum',
      visible: false,
      width: 100,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '机械费合价',
      field: 'jdTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '主材费单价',
      field: 'ZDSum',
      visible: false,
      width: 100,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '主材费合价',
      field: 'zdTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '设备费单价',
      field: 'SDSum',
      visible: false,
      width: 100,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '设备费合价',
      field: 'sdTotalSum',
      width: 100,
      ellipsis: true,
      visible: false,
    },
  ];
  let scjArr = [
    {
      title: '单价',
      field: 'price',
      dataIndex: 'price',
      resizable: true,
      edit: true,
      width: 70,

      editable: ({ record: row }) => {
        if (
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !row.isNumLock &&
          row.ifLockStandardPrice != 1 &&
          !['07'].includes(row.kind) &&
          ![11, 2, 5, 21].includes(row.isCostDe) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          row.isDataTaxRate != 0
        ) {
          if (['05', '06'].includes(row.kind)) {
            if (row.isFyrcj != 0 || [4, 5].includes(row.deResourceKind)) {
              return 'cellEditorSlot';
            } else {
              return false;
            }
          } else {
            return 'cellEditorSlot';
          }
        } else {
          return false;
        }
      },
    },
    {
      title: '合价',
      field: 'totalNumber',
      dataIndex: 'totalNumber',
      width: 100,
      ellipsis: true,
      resizable: true,
    },
    {
      title: '取费专业',
      field: 'costMajorName',
      dataIndex: 'costMajorName',
      resizable: true,
      edit: true,
      width: 120,
      ellipsis: true,
      editable: ({ record: row }) => {
        // console.log(record)
        if (
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          row.type != '-1' &&
          row.deCode &&
          row.isTopLevelType03
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '超高过滤类别',
    //   field: 'fitHighType',
    //   ellipsis: true,
    // },
    // {
    //   title: '檐高类别',
    //   field: 'eaveHeightType',
    //   ellipsis: true,
    // },
    // {
    //   title: '修缮高度',
    //   field: 'eaveHeightType',
    //   ellipsis: true,
    // },
    {
      title: '施工组织措施类别',
      field: 'measureType',
      ellipsis: true,
      edit: true,
      resizable: true,
      editable: ({ record: row }) => {
        if (
          (row.kind == '04' &&
            ([0, 11, 12, 13, 14].includes(row.isCostDe) ||
              row.isCostDe == undefined)) ||
          ['06', '08', '09', '10'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    // {
    //   title: '垂直运输类别',
    //   field: 'verticalTransType',
    //   ellipsis: true,
    // },
    {
      title: '人工费单价',
      field: 'RSum',
      visible: false,
      width: 100,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '人工费合价',
      field: 'rTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '材料费单价',
      field: 'CSum',
      visible: false,
      width: 100,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '材料费合价',
      field: 'cTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '机械费单价',
      field: 'JSum',
      visible: false,
      width: 100,
      ellipsis: true,
      edit: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '机械费合价',
      field: 'jTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '主材费单价',
      field: 'ZSum',
      visible: false,
      edit: true,
      width: 100,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '主材费合价',
      field: 'zTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '设备费单价',
      field: 'SSum',
      visible: false,
      edit: true,
      width: 100,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ![11, 5, 21].includes(row.isCostDe) &&
          row.isTempRemove !== 1 &&
          deMapFun.isDe(row.kind) &&
          !(row.markSum == 1 && (row.levelMark == 1 || row.levelMark == 2)) &&
          row.type != '-1' &&
          !['05', '07'].includes(row.kind) &&
          !deMapFun.isCgf(row.deCode) &&
          !deMapFun.isBc(row.deCode) &&
          !['06', '09'].includes(row.type)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '设备费合价',
      field: 'sTotalSum',
      visible: false,
      width: 100,
      ellipsis: true,
    },
  ];
  let index = tableColumns.value.findIndex(a => a.title == '专业');
  // 如果是按市场价组价
  if (projectStore.setOption.isScj) {
    tableColumns.value.splice(index, 0, ...scjArr);
  } else {
    tableColumns.value.splice(index, 0, ...deArr);
  }
  // 如果为房屋建筑工程则隐藏檐高类别列否则隐藏修缮高度列
  // if (
  //   projectStore.currentTreeInfo.constructMajorType ==
  //     ConstructMajorTypeConstant.FXJZ_PROJECT ||
  //   projectStore.currentTreeInfo.constructMajorType ==
  //     ConstructMajorTypeConstant.FXAZ_PROJECT
  // ) {
  //   tableColumns.value = tableColumns.value.filter(
  //     item => item.title !== '檐高类别'
  //   );
  // } else {
  //   tableColumns.value = tableColumns.value.filter(
  //     item => item.title !== '修缮高度'
  //   );
  // }
  return tableColumns.value;
};
export default getTableColumns;
