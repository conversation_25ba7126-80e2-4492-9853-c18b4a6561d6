<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-14 19:10:53
-->
<template>
  <div class="table-content">
    <child-page-table
      :pageType="'zcbfwf'"
      :columnList="columnList"
    ></child-page-table>
  </div>
</template>
<script setup>
import ChildPageTable from './childPageTable.vue';
const columnList = [
  {
    field: '',
    title: '',
    minWidth: 60,
    slots: { default: 'changeIdentification' },
  },
  {
    title: '送审',
    children: [
      {
        field: 'dispNo',
        title: '序号',
        minWidth: 60,
        type: 'text',
        slots: { default: 'dispNo_default' },
      },
      {
        field: 'fxName',
        title: '项目名称',
        minWidth: 180,
        slots: { default: 'fxName_default' },
      },
      {
        field: 'xmje',
        title: '项目价值',
        minWidth: 100,
        slots: { default: 'xmje_default' },
      },
      {
        field: 'serviceContent',
        title: '服务内容',
        minWidth: 180,
        slots: { default: 'serviceContent_default' },
      },

      {
        field: 'rate',
        title: '费率(%)',
        minWidth: 80,
        slots: { default: 'rate_default' },
      },
      {
        field: 'fwje',
        minWidth: 100,
        title: '金额',
        slots: { default: 'fwje_default' },
      },
      {
        field: 'amount',
        title: '数量',
        minWidth: 80,
        slots: { default: 'amount_default' },
      },
    ],
  },
  {
    title: '审定',
    children: [
      {
        field: 'dispNo',
        title: '序号',
        minWidth: 60,
        type: 'text',
      },
      {
        field: 'fxName',
        title: '项目名称',
        minWidth: 180,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'fxName_edit' },
      },
      {
        field: 'xmje',
        title: '项目价值',
        minWidth: 100,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'xmje_edit' },
      },
      {
        field: 'serviceContent',
        title: '服务内容',
        minWidth: 180,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'service_edit' },
      },

      {
        field: 'rate',
        title: '费率(%)',
        minWidth: 80,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'rate_edit' },
      },
      {
        field: 'fwje',
        minWidth: 100,
        title: '金额',
      },
      {
        field: 'amount',
        title: '数量',
        minWidth: 80,
        editRender: { autofocus: '.vxe-input--inner' },
        slots: { edit: 'amount_edit' },
      },
    ],
  },
  {
    field: 'changeTotal',
    minWidth: 100,
    title: '增减金额',
    slots: { default: 'changeTotal_default' },
  },
  {
    field: 'ysshSysj.changeExplain',
    title: '增减说明',
    editRender: { autofocus: '.vxe-input--inner' },
    minWidth: 100,
    slots: { default: 'changeExplain_default', edit: 'changeExplain_edit' },
  },
];
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
