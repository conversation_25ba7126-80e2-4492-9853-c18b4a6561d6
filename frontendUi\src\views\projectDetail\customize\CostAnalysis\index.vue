<!--
 * @Author: wangru
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: renmingming
 * @LastEditTime: 2024-08-13 16:32:57
-->
<template>
  <div class="common-flex-upAndDown">
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <!-- <keep-alive> -->
        <!-- <div class="flex-auto"> -->
        <unit-analysis class="item" v-show="isUnit"></unit-analysis>
        <item-analysis v-show="!isUnit" ref="itemAnalysisRef"></item-analysis>
        <!-- </div> -->
        <!-- </keep-alive> -->
      </template>
      <template #two>
        <three-matericls></three-matericls>
      </template>
    </split>
  </div>
</template>

<script setup>
import { onMounted, ref, watch, getCurrentInstance, onActivated } from 'vue';
import ItemAnalysis from './ItemAnalysis.vue';
import UnitAnalysis from './UnitAnalysis.vue';
import { projectDetailStore } from '@/store/projectDetail';
import ThreeMatericls from './ThreeMatericls.vue';
import { message } from 'ant-design-vue';
import { insetBus } from '@/hooks/insetBus';
import AwfModal from './awfModal.vue';
const store = projectDetailStore();
let isUnit = ref(true);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
onActivated(() => {
  insetBus(bus, store.componentId, 'CostAnalysis', async data => {
    if (data.name === 'export-table') message.info('功能建设中...');
    if (data.name === 'fixed-awf') fixAWFfn();
  });
});
let itemAnalysisRef = ref();
watch(
  () => store.currentTreeInfo,
  () => {
    if (store.currentTreeInfo?.levelType === 3) {
      isUnit.value = true;
    } else {
      isUnit.value = false;
    }
  }
);
onMounted(() => {
  if (store.currentTreeInfo?.levelType === 3) {
    isUnit.value = true;
  } else {
    isUnit.value = false;
  }
});
const refreshList = () => {
  itemAnalysisRef.value?.getTableData();
};
const exportTable = () => {
  message.info('功能建设中...');
};
defineExpose({ refreshList });
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
}
</style>
