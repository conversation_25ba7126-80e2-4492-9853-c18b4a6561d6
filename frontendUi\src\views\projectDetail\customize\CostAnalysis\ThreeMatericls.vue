<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-30 14:59:31
-->
<template>
  <div class="table-content">
    <p class="title"><span class="text">三材汇总表</span></p>
    <div class="content">
      <vxe-table
        show-overflow
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ height:30,isHover: true, isCurrent: true }"
        :tree-config="{
          childrenField: 'children',
          transform: false,
        }"
        @cell-click="useCellClickEvent"
        :cell-class-name="selectedClassName"
        class="table-edit-common table-content"
        :data="tableData"
        keep-source
        height="auto"
        ref="threeMatericlsTable"
        :header-cell-class-name="headerCellClassName"
      >
        <vxe-column
          field=""
          width="50"
          title="序号"
          fixed="left"
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          field="name"
          width="150"
          title="名称"
        ></vxe-column>
        <vxe-column
          field="unit"
          width="80"
          title="单位"
        ></vxe-column>
        <vxe-column
          field="amount"
          width="70"
          title="数量"
        ></vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
import { message } from 'ant-design-vue';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
let tableData = ref([]);
let threeMatericlsTable = ref();
onMounted(() => {
  getTableData();
});
const headerCellClassName = ({ column }) => {
  // if (column.field === 'amount') {
  //   return 'col-yellow';
  // }
  return null;
};
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  ([val, oldVal], [newY, oldy]) => {
    if (store.tabSelectName === '造价分析') {
      getTableData();
    }
  }
);
const getTableData = () => {
  const formdata = {
    constructId: store.currentTreeGroupInfo?.constructId,
    levelType: store.currentTreeInfo?.levelType,
  };
  if (store.currentTreeInfo?.levelType === 2) {
    formdata.singleId = store.currentTreeInfo?.id;
  } else if (store.currentTreeInfo?.levelType === 3) {
    formdata.singleId = store.currentTreeInfo?.parentId;
    formdata.unitId = store.currentTreeInfo?.id;
  }
  console.log('---------三材汇总表传参', formdata);
  feePro.getThreeMaterialsSummary(formdata).then(res => {
    console.log('---------三材汇总表返回结果', res);
    if (res.status === 200) {
      tableData.value = res.result;
    }
  });
};
</script>
<style lang="scss" scoped>
.table-content {
  height: 100%;
  .title {
    width: 100%;
    background-color: #e7e7e7;
    height: 35px;
    text-align: left;
    margin: 2px 0 2px;
    .text {
      display: inline-block;
      width: 128px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: #f8fbff;
      border-top: 2px solid #4786ff;
    }
  }
  .content {
    width: 360px;
    height: calc(100% - 45px);
  }
  ::v-deep(.vxe-table .vxe-header--column.col-yellow) {
    background-color: #2db7f5;
    color: #fff;
  }
}
</style>
