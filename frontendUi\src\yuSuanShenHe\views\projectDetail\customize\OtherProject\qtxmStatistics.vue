<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-18 19:35:27
-->
<template>
  <div class="table-content">
    <split
      horizontal
      ratio="3/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
    >
      <template #one>
        <vxe-table
          :row-class-name="shChangeLabel"
          align="center"
          :loading="loading"
          :column-config="{ resizable: true }"
          :row-config="{ isCurrent: true, isHover: true }"
          :data="tableData"
          height="auto"
          show-overflow
          ref="qtxmTable"
          @edit-closed="editClosedEvent"
          keep-source
          @cell-click="
            cellData => {
              useCellClickEvent(cellData, tableCellClickEvent, [
                'ysshSysj.changeExplain',
              ]);
            }
          "
          class="table-edit-common"
          :cell-class-name="selectedClassName"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethod,
          }"
        >
          <vxe-column
            field=""
            width="60"
            title=""
          >
            <template #default="{ row }">
              <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
                shChangeLabel(row.ysshSysj?.change).label
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="dispNo"
            width="60"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="extraName"
            width="180"
            title="名称"
          > </vxe-column>
          <vxe-colgroup title="送审">
            <vxe-column
              field="calculationBase"
              width="180"
              title="计算公式(计算基数)"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj.calculationBase }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="amount"
              width="180"
              title="数量"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj.amount }}</span>
              </template>
            </vxe-column>
            <vxe-column
              field="total"
              width="180"
              title="金额"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj.total }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup title="审定">
            <vxe-column
              field="calculationBase"
              width="180"
              title="计算公式(计算基数)"
            ></vxe-column>
            <vxe-column
              field="amount"
              width="180"
              title="数量"
            ></vxe-column>
            <vxe-column
              field="total"
              width="180"
              title="金额"
            ></vxe-column>
          </vxe-colgroup>
          <vxe-column
            field=""
            width="180"
            title="增减金额"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj.changeTotal }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="ysshSysj.changeExplain"
            width="180"
            title="增减说明"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj.changeExplain }}</span>
            </template>
            <template #edit="{ row }">
              <vxe-input
                v-model="row.ysshSysj.changeExplain"
                :maxlength="50"
              />
            </template>
          </vxe-column>
        </vxe-table>
      </template>
      <template #two>
        <comparisonPage
          :currentInfo="currentInfo"
          pageType="qtxm"
          :needTitle="true"
        ></comparisonPage>
      </template>
    </split>
  </div>
</template>
<script setup>
import {
  onMounted,
  onActivated,
  onUpdated,
  nextTick,
  ref,
  watch,
  getCurrentInstance,
} from 'vue';
import { pureNumber, shChangeLabel } from '@/utils/index';
import { projectDetailStore } from '@/store/projectDetail';
import shApi from '@/api/shApi';
import qtxmCommon from './qtxmCommon';
import { insetBus } from '@/hooks/insetBus';
import { message } from 'ant-design-vue';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
let qtxmTable = ref();
const currentInfo = ref({});

const clear = () => {
  //清除编辑状态
  const $table = qtxmTable.value;
  $table.clearEdit();
};

// 定位方法
const posRow = sequenceNbr => {
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  // currentInfo.value = { sequenceNbr };
  getOtherProjectList();
};

defineExpose({
  posRow,
});
const tableCellClickEvent = ({ row, column, cell, event }) => {
  currentInfo.value = row;
  console.log(currentInfo.value, 'currentInfo.value-=-=-==-=--=');
  return true;
};
const editClosedEvent = ({ row, column }) => {
  console.log('其他项目修改', row);
  const $table = qtxmTable.value;
  const field = column.field;
  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'unit' && row.unit && row.unit.length > 2000) {
    row.unit = value.slice(0, 2000);
  }
  if (field === 'amount' && row.amount && row.amount.length > 20) {
    row.amount = value.slice(0, 20);
  }
  if ($table.isUpdateByRow(row, field)) {
    upDate(row);
  }
};
const upDate = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    sequenceNbr: row.sequenceNbr,
    changeExplain: row.ysshSysj.changeExplain,
    changeType: 4,
  };
  console.log('修改增减说明参数：', apiData);
  shApi.updateChangeExplain(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      getOtherProjectList();
    }
  });
};
const getOtherProjectList = isFirst => {
  loading.value = true;

  let apiData = qtxmCommon.requestParams();
  console.log(apiData, 'apiData');
  shApi
    .getOtherProjectList(apiData)
    .then(res => {
      console.log('********getOtherProjectList1', res.result);
      if (res.status === 200) {
        loading.value = false;
        tableData.value = res.result;
        tableData.value &&
          tableData.value.map(item => {
            item.isMarkSafe = item.markSafa === 1 ? true : false;
            item.isMarkSj = item.markSj === 1 ? true : false;
            if (['材料暂估价', '设备暂估价'].includes(item.extraName)) {
              item.isEdit = false;
            } else {
              item.isEdit = true;
            }
          });
        console.log(tableData.value[0], 'tableData.value[0]');

        nextTick(() => {
          qtxmTable.value.setCurrentRow(tableData.value[0]);
        });
        if (isFirst) currentInfo.value = res.result ? res.result[0] : [];
        console.log('********getOtherProjectList', res.result);
      }
    })
    .finally(err => {
      console.log(err, 'err');
    });
};

watch(
  () => projectStore.asideMenuCurrentInfo,
  () => {
    console.log('*****************其他项目');
    if (projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm00') {
      getOtherProjectList();
    }
  }
);
onMounted(() => {
  getOtherProjectList(true);
});

const changeEdit = val => {
  console.log('val', val);
};
const CheckChange = (column, row) => {
  console.log('复选框CheckChange', row);
  row.markSafa = row.isMarkSafe ? 1 : 0;
  row.markSj = row.isMarkSj ? 1 : 0;
  upDate(row);
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
};
const emits = defineEmits(['getCommonModel']);
onActivated(() => {
  bus.off('refreshzj');
  bus.on('refreshzj', type => {
    if (projectStore.tabSelectName === '其他项目') {
      getOtherProjectList(); // 刷新其他项目
    }
  });
  insetBus(bus, projectStore.componentId, 'qtxmStatistics', async data => {
    if (data.name === 'insert') message.info('功能建设中...');
    if (data.name === 'delete') message.info('功能建设中...');

    if (data.name === 'retrieval-fee')
      emits('getCommonModel', { type: data.name });
    if (data.name === 'comparative-match')
      emits('getCommonModel', { type: data.name });
    if (data.name === 'modify-submission-for-review')
      emits('getCommonModel', { type: data.name });
    if (data.name === 'convert-to') {
      // convertToTitle.value=data.activeKind==='01'?'审定转预算文件':'送审转预算文件'
      emits('getCommonModel', { type: data.name, activeKind: data.activeKind });
    }
    if (data.name === 'data-conversion') {
      emits('getCommonModel', { type: data.name, activeKind: data.activeKind });
    }
  });
});
</script>
<style lang="scss" scoped>
// @import './otherProject.scss';
.table-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
::v-deep(.vxe-body--row.row--current) {
  background: #a6c3fa !important;
}
</style>
