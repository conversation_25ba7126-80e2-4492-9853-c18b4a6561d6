<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-24 11:19:26
-->
<!--  -->
<template>
  <vxe-table
    border
    align="center"
    v-if="isTableShow"
    auto-resize
    :column-config="{ resizable: true }"
    :row-config="{ isHover: true, keyField: 'sequenceNbr' }"
    :menu-config="menuConfig"
    @menu-click="contextMenuClickEvent"
    height="100%"
    :tree-config=" {
            transform: true,
            rowField: 'dispNo',
            parentField: 'parentId',
            expandAll: false,
            // reserve: true,
          }
    "
    :data="tableData"
    ref="itemTable"
    keep-source
    @edit-closed="editClosedEvent"
    class="table-edit-common"
    @cell-click="
      cellData => {
        useCellClickEvent(cellData);
      }
    "
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      beforeEditMethod: cellBeforeEditMethod,
    }"
    :cell-class-name="selectedClassName"
  >
    <div
      v-for="columns of handlerColumns"
      :key="columns.field"
    >
      <vxe-column
        v-if="columns.children.length === 0"
        :field="columns.field"
        :width="columns.width"
        :title="columns.title"
        :tree-node="columns.field === 'dispNo'"
      ></vxe-column>
      <vxe-colgroup
        v-else
        :title="columns.title"
      >
        <vxe-column
          v-for="columnsChild of columns.children"
          :key="columnsChild.field"
          :field="columnsChild.field"
          :width="columnsChild.width"
          :title="columnsChild.title"
        ></vxe-column>
      </vxe-colgroup>
    </div>
  </vxe-table>
  <PageJsColumnSetting
    :columnOptions="fbTableColumns"
    :selColumnOptions="handlerColumns"
    :setingClickNum="setingClickNum"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="setPageQuery"
  />
</template>
<script setup>
import { watch, ref, onMounted, reactive } from 'vue';
// import api from '../../../../api/projectDetail.js';
import { Modal } from 'ant-design-vue';
import jiesuanApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { useJsFormatTableColumns } from '@/hooks/useJsFormatTableColumns.js';
import { fbTableColumns } from './columns.js';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: 'dispNo' });
import { pureNumber } from '@/utils/index';
const { handlerColumns, updateColumns, initColumns } =
  useJsFormatTableColumns();

const store = projectDetailStore();
const itemTable = ref();
const setingClickNum = ref(0);
const isTableShow = ref(true);
let tableData = ref([]);
let getData = [];
let averageRow = ref();
let average = ref();
let columnSettingRef = ref();
let taxMode = ref(); //1-一般计税，0-简易计税
let flag = ref(false); //修改单项建筑面积是否联动修改单位建筑面积
const setPageQuery = async data => {
  isTableShow.value = false;
  await updateColumns(data, fbTableColumns.value);

  isTableShow.value = true;
};

const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'pageSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, column, columnIndex, row, rowIndex);
    return true;
  },
});
// 点击右击菜单
const contextMenuClickEvent = ({ menu, row }) => {
  // 如果点击页面显示列设置
  if (menu.code === 'pageSetting') {
    showPageColumnSetting();
  }
};
// 页面列设置
const showPageColumnSetting = () => {
  setingClickNum.value++;
  columnSettingRef.value.open();
};
const getTableData = async () => {
  // getTaxMethods();
  taxMode.value = store.taxMade;
  console.log('taxMade', store.taxMade);
  // 动态表格列初始化设置
  await initColumns({
    columns: JSON.parse(JSON.stringify(fbTableColumns.value)),
  });
  getData = [];
  let apiData = {
    levelType: store.currentTreeInfo?.levelType,
  };
  apiData.constructId = store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo?.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id;
  }
  if (!apiData.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  console.log('获取造价分析', apiData);
  apiData.levelType &&
    jiesuanApi.getCostAnalysisData(apiData).then(res => {
      console.log('获取造价分析返回数据', res);
      if (res.status === 200) {
        if (store.currentTreeInfo?.levelType === 1) {
          getData = res.result.costAnalysisConstructVOList;
        } else if (store.currentTreeInfo?.levelType === 2) {
          getData = [res.result.costAnalysisSingleVOList];
        }
        getFinallyData(getData);
      }
    });
};
const getChildList = (data, children) => {
  if (data?.length > 0) {
    data.map(item => {
      if (item.childrenList && item.childrenList.length > 0) {
        children.push(...item.childrenList);
        item.childrenList.map(a => {
          a.parentId = item.dispNo;
          getChildList([a], children);
        });
      }
    });
  }
  return children;
};
const getFinallyData = data => {
  let list = [];
  let children = [];
  if (data?.length > 0) {
    children = getChildList(data, []);
    list.push(...data, ...children);
    let map = new Map();
    for (let item of list) {
      map.set(item.dispNo, item);
    }
  }
  tableData.value = getTotal(list);
  // getUniCost();
};
const getUniCost = () => {
  tableData.value.map(item => {
    if (store.currentTreeInfo?.levelType === 1) {
      if (
        flag.value &&
        Number(item.parentId) === Number(averageRow.value.dispNo)
      ) {
        item.average = averageRow.value.average;
      }
      item.average =
        item.average && item.average > 0
          ? Number(item.average).toFixed(2)
          : '0.00';
      item.unitcost =
        item.average && item.average > 0
          ? Number(item.gczj / item.average).toFixed(2)
          : '0.00';
    } else if (
      store.currentTreeInfo?.levelType === 2 &&
      item.levelType === 3 &&
      item.projectName !== '合计'
    ) {
      item.average =
        item.average > 0 ? Number(item.average).toFixed(2) : '0.00';
      item.unitcost =
        item.average > 0 ? Number(item.gczj / item.average).toFixed(2) : '0.00';
    } else if (
      store.currentTreeInfo?.levelType === 2 &&
      item.projectName === '合计'
    ) {
      item.average = item.average >= 0 ? Number(item.average).toFixed(2) : '/';
      item.unitcost =
        item.average >= 0 ? Number(item.gczj / item.average).toFixed(2) : '/';
    }
  });
};
const getTotal = arr => {
  let totalLast = {
    average: 0,
    unitcost: 0,
    // gczj: 0,
    gczj_ys: 0,
    gczjsbsj: 0,
    fbfxhj: 0,
    csxhj: 0,
    qtxmhj: 0,
    gfee: 0,
    safeFee: 0,
    jxse: 0,
    xxse: 0,
    zzsynse: 0,
    fjse: 0,
    sj: 0,
    sbfsj: 0,
    jsjc: 0,
    jsjcrgf: 0,
    jsjcclf: 0,
    jsjczgj: 0,
    jsjcjxf: 0,
    jcgfhj: 0,
    jcaqwmsgfhj: 0,
    jcjxse: 0,
    jcg: 0,
    jch: 0,
    jci: 0,
    jcs: 0,
    jcj: 0,
    jsjcsbf: 0,
    jieSuanPrice: 0,
    djcsxglf: 0,
  };
  let list = arr.filter(item => !item.parentId);
  for (let key in totalLast) {
    list.forEach(item => {
      if (Object.prototype.hasOwnProperty.call(totalLast, key)) {
        totalLast[key] = item[key]
          ? floatAdd(totalLast[key], item[key])
          : totalLast[key];
        totalLast[key] = Number(totalLast[key]).toFixed(2);
      }
    });
  }
  console.log('totalLast', totalLast);
  totalLast.projectName = '合计';
  totalLast.dispNo = '';

  arr && arr.push(totalLast); //有数据的话进行合计
  return arr;
};
const floatAdd = (arg1, arg2) => {
  var r1, r2, m;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
};
watch(
  () => store.tabSelectName,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.levelType !== 3
    ) {
      getTableData();
    }
  }
);
watch(
  () => store.currentTreeInfo,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.levelType !== 3
    ) {
      getTableData();
    }
  }
);
onMounted(() => {
  if (
    store.tabSelectName === '造价分析' &&
    store.currentTreeInfo?.levelType !== 3
  ) {
    getTableData();
  }
});
const clear = () => {
  //清除编辑状态
  const $table = itemTable.value;
  $table.clearEdit();
};
const upDateAverage = () => {
  // getUniCost();
  const $table = itemTable.value;
  const listValue = $table.data;
  if (store.currentTreeInfo?.levelType === 1) {
    let averageTotal = 0;
    const noChildList =
      listValue &&
      listValue.map(item => {
        if (!item.parentId && item.projectName !== '合计') {
          averageTotal += item.average / 1;
        }
      });
    listValue.map(item => {
      if (item.projectName === '合计') {
        item.average = averageTotal ? averageTotal.toFixed(2) : '0.00';
        item.unitcost =
          item.average !== '0.00'
            ? (item.gczj / item.average).toFixed(2)
            : '0.00';
      }
    });
  }
  let apiData = {
    levelType: averageRow.value.levelType,
    average: average.value,
    unitcost: averageRow.value.unitcost ? averageRow.value.unitcost : 0,
    constructId: store.currentTreeInfo?.id,
    flag: flag.value,
  };
  if (store.currentTreeInfo?.levelType === 1) {
    if (averageRow.value.levelType === 2) {
      apiData.singleId = averageRow.value.sequenceNbr;
    } else if (averageRow.value.levelType === 3) {
      const parent = tableData.value.filter(
        item => Number(item.dispNo) === Number(averageRow.value.parentId)
      );
      apiData.singleId = parent[0]?.sequenceNbr;
      apiData.unitId = averageRow.value.sequenceNbr;
    }
  } else if (store.currentTreeInfo?.levelType === 2) {
    (apiData.constructId = store.currentTreeInfo?.parentId),
      (apiData.singleId = store.currentTreeInfo?.id),
      (apiData.unitId = averageRow.value.sequenceNbr);
  }
  console.log('修改造价分析', apiData, averageRow.value);
  jiesuanApi.updateCostAnalysis(apiData).then(res => {
    console.log('修改成功造价分析', res);
    if (res.status === 200) {
      let expandRows;
      if (averageRow.value && averageRow.value.levelType === 2) {
        expandRows = tableData.value.filter(
          item => Number(item.parentId) === Number(averageRow.value.dispNo)
        );
        expandRows =
          expandRows && expandRows.length > 0
            ? [...expandRows, averageRow.value]
            : expandRows;
        setTimeout(() => {
          const $table = itemTable.value;
          if ($table) {
            console.log('expandRows', expandRows);
            $table.setTreeExpand(expandRows, true);
          }
        }, 10);
      }
    }
  });
};
const editClosedEvent = ({ row, column }) => {
  const $table = itemTable.value;
  const field = column.field;
  averageRow.value = row;
  average.value = row[field];
  if (!row[field]) {
    row[field] = 0;
    average.value = 0;
  }
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    flag.value = false;
    console.log('row-isUpdateByRow', row);
    const hasChildren =
      row.childrenList && row.childrenList.length > 0 ? true : false;
    averageRow.value.levelType === 2 && hasChildren
      ? Modal.confirm({
          title: '是否联动修改单位工程下的建筑面积？',
          zIndex: '99999',
          onOk() {
            flag.value = true;
            upDateAverage();
          },
          onCancel() {
            upDateAverage();
          },
        })
      : upDateAverage();
  }
};
defineExpose({ getTableData });
</script>
<style lang="scss" scoped>
.table-content {
  max-width: 95%;
  // width: calc(100% - 250px);
  max-height: calc(100% - 40px);
}

// ::v-deep(.vxe-table) {
//   min-height: 400px;
// }
</style>
