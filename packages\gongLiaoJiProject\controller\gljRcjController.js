const {Controller} = require("../../../core");

const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const WildcardMap = require("../core/container/WildcardMap");
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ObjectUtils } = require('../utils/ObjectUtils');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const DeTypeConstants = require("../constants/DeTypeConstants");
const CommonConstants = require("../constants/CommonConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const BranchProjectLevelConstant = require("../constants/BranchProjectLevelConstant");
const {NumberUtil} = require("../utils/NumberUtil");
const ProjectTaxCalculationConstants = require("../constants/ProjectTaxCalculationConstants");
const GsProjectSettingEnum = require('../enums/GsProjectSettingEnum');
class gljRcjController extends Controller {

  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取该定额下所有人材机，不管是03定额还是04定额
   * @param {*} args 
   * @returns 
   */
  async getAllRcjDetailByDeId(args){
    let {constructId, unitId, deRowId} = args;
    let result = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetailByDeId(constructId,unitId,deRowId);
    return ResponseData.success(result);
  }

  /**
   * 获取各层级人材机明细 /未计价材料
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllRcjDetail(args) {
    let {constructId, unitId, deRowId,type} = args;
    let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, deRowId,type);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let szssMediumRepair = businessMap.get(FunctionTypeConstants.PROJECT_SETTING).get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);
    if(ObjectUtils.isEmpty(rcjLists)){
      return ResponseData.success([]);
    }

    let deRowObject = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRowId);
    let csxmRowObject = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(deRowId);

    let pricingMethod = ProjectDomain.getDomain(args.constructId).getRoot().pricingMethod;      //组价方式
    let calculationMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;   //计税方式

    let pbs = []; //定额人材机配比
    let copyRcjLists = ConvertUtil.deepCopy(rcjLists)
    for (let rcj of copyRcjLists) {
      rcj.totalNumberPrice = rcj.total;
      if (pricingMethod === 1) {
        if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
          rcj.totalNumberPrice = rcj.totalTax;
        }
      } else {
        if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_1) {
          rcj.totalNumberPrice = rcj.baseJournalTotal;
        } else {
          rcj.totalNumberPrice = rcj.baseJournalTotalTax;
        }
      }

      if ((rcj.isDeResource === CommonConstants.COMMON_YES || rcj.isDeCompensation === CommonConstants.COMMON_YES) && ObjectUtils.isNotEmpty(rcj.pbs)) {
        let copyPbs = ConvertUtil.deepCopy(rcj.pbs)
        for (let pbsRcj of copyPbs) {
          pbsRcj.totalNumberPrice = pbsRcj.total;
          if (pricingMethod === 1) {
            if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
              pbsRcj.totalNumberPrice = pbsRcj.totalTax;
            }
          } else {
            if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_1) {
              pbsRcj.totalNumberPrice = pbsRcj.baseJournalTotal;
            } else {
              pbsRcj.totalNumberPrice = pbsRcj.baseJournalTotalTax;
            }
          }

        }
        Array.prototype.push.apply(pbs, copyPbs);
      }
    }
    let copyRcjList = copyRcjLists;
    Array.prototype.push.apply(copyRcjList, pbs);

    if (ObjectUtils.isNotEmpty(deRowObject)) {
      if (BranchProjectLevelConstant.top === type || BranchProjectLevelConstant.fb === type || BranchProjectLevelConstant.zfb === type) {
        for (let copyRcj of copyRcjList) {
          if (ObjectUtils.isNotEmpty(copyRcj.unitTotalNumber)) {
            copyRcj.totalNumber = copyRcj.unitTotalNumber;
          }
          copyRcj.isTempRemove = 0;
        }
      }
    } else if (ObjectUtils.isNotEmpty(csxmRowObject)) {
      if (BranchProjectLevelConstant.top === type || BranchProjectLevelConstant.fb === type || BranchProjectLevelConstant.zfb === type || BranchProjectLevelConstant.qd === type) {
        for (let copyRcj of copyRcjList) {
          if (ObjectUtils.isNotEmpty(copyRcj.unitTotalNumber)) {
            copyRcj.totalNumber = copyRcj.unitTotalNumber;
          }
          copyRcj.isTempRemove = 0;
        }
      }
    }

    copyRcjList.forEach(item =>{
      item.resQtyFactor =  ObjectUtils.isEmpty(item.resQtyFactor) ? 1: item.resQtyFactor
      if(szssMediumRepair && item.isMunicipal){
        item.resqtyExp =  `${item.originalConsumeQty}*${item.resQtyFactor}*0.9`
      }else {
        item.resqtyExp =  `${item.originalConsumeQty}*${item.resQtyFactor}`
      }
      if(ObjectUtils.isNotEmpty(item.pbs)){
        item.pbs.forEach(item1=>{
          item1.resQtyFactor =  ObjectUtils.isEmpty(item1.resQtyFactor) ? 1: item1.resQtyFactor
          if(szssMediumRepair && item1.isMunicipal){
            item1.resqtyExp =  `${item1.originalConsumeQty}*${item1.resQtyFactor}*0.9`
          }else {
            item1.resqtyExp =  `${item1.originalConsumeQty}*${item1.resQtyFactor}`
          }
          if(item1.isFyrcj === 1 && item1.unit ==='%' ){
            item1.unit ='元';
          }
        })
      }
    });

    let result = copyRcjList.filter(item => item.isDeResource !== CommonConstants.COMMON_YES && item.isDeCompensation !== CommonConstants.COMMON_YES);
    copyRcjList.forEach(item => {
       this.service.gongLiaoJiProject.gljBaseRcjService.typeListByKind(item);
    })
    return ResponseData.success(result);
  }


  /**
   * 通过索引 增加人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async addRcjData(args) {
    let {deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjId} = args;
    let result = await this.service.gongLiaoJiProject.gljRcjService.addRcjData(deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjId, {});
    if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.code)) {
      return result
    }
    return ResponseData.success(result);
  }


  /**
   * 通过索引 替换人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async replaceRcjData(args) {
    let {deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjDetailId,rcjId,factor} = args;
    let result = await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjDetailId,rcjId,factor, {});
    if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.code)) {
      return result
    }
    return ResponseData.success(result);
  }



  /**
   *  判断人材机code 是否存在内存，
   * @param args
   * @returns {ResponseData}
   */
  codeExistInUnit(args) {
    let {constructId, singleId, unitId, code} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if(ObjectUtils.isEmpty(rcjUserList)||rcjUserList.size===0){
      rcjUserList = new Array();
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ,rcjUserList);
    }
    let rcjUser=rcjUserList.find(item => code === item.materialCode && item.unitId ===unitId)
    if(ObjectUtil.isNotEmpty(rcjUser)){
      return ResponseData.success(true); // 如果没有匹配的项，返回false
    }
    return ResponseData.success(false);
  }


  /**
   *   补充 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async supplementRcjData(args) {
    let {deId, constructId, singleId, unitId,deRowId ,detail,rcjId,rcjDetailId,replaceFlag} = args;
    let {resQty,initResQty} = detail; //初始化时保持原始消耗量与消耗量一致，，保持定额类型一致
    if(ObjectUtils.isNotEmpty(resQty) && ObjectUtils.isEmpty(initResQty)){
      detail.initResQty = resQty;
    }
    let result = await this.service.gongLiaoJiProject.gljRcjService.supplementRcjData(deId, constructId, singleId, unitId,deRowId,detail,rcjId,rcjDetailId,replaceFlag);
    deId, constructId, singleId, unitId,deRowId,detail,rcjId,rcjDetailId,replaceFlag
    return ResponseData.success(result);
  }

  /**
   * 按定额生成主材/设备
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async supplementRcjDataByDe(args) {
    let {deId, constructId, singleId, unitId, deRowId, kind, materialCode} = args;
    let result = await this.service.gongLiaoJiProject.gljRcjService.supplementRcjDataByDe(deId, constructId, singleId, unitId, deRowId, kind, materialCode);
    return ResponseData.success(result);
  }

  /**
   * 将主材/设备同步到子目
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async syncDeDataByRcj(args) {
    let {deId, constructId, singleId, unitId, deRowId, content, addType} = args;
    await this.service.gongLiaoJiProject.gljRcjService.syncDeDataByRcj(deId, constructId, singleId, unitId, deRowId, content, addType);
    return ResponseData.success(true);
  }

  /**
   *   通过编码 替换 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async replaceRcjByCodeData(args) {
    let {constructId, singleId, unitId,deRowId,code,deId, rcjDetailId ,rcjId,replaceFlag} = args;
    let result = await this.service.gongLiaoJiProject.gljRcjService.replaceRcjByCodeData(deId, constructId, singleId, unitId,deRowId,code,rcjDetailId,rcjId,replaceFlag);
    return  ResponseData.success(result);
  }



  /**
   *   删除 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async deleteRcjByCodeData(args) {
    let {deId, constructId, unitId,rcjDetailId} = args;
    let result = await this.service.gongLiaoJiProject.gljRcjService.deleteRcjByCodeData(deId, constructId, unitId,rcjDetailId, true, {});
    return  ResponseData.success(result);
  }


  /**
   * 编辑 人材机明细区
   * @param args
   * @returns {ResponseData}
   */
  async updateRcjDetail(args){
    const result = await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(args);
    return ResponseData.success(result);
  }

  /**
   * 编辑 人材机明细区   多行
   * @param args
   * @returns {ResponseData}
   */
  async multiUpdateRcjDetail(args){
    const result = await this.service.gongLiaoJiProject.gljRcjService.multiUpdateRcjDetail(args);
    return ResponseData.success(result);
  }


  /**
   *  计算税率rcj
   * @param arg
   * @return {Promise<void>}
   */
  async calculateTax(args){
    let {rcj, type} = args
    rcj.isDataTaxRate = 1
    this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,type);
    return rcj;
  }






  /**
   * 获取单位工程下所有人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllRcjByUnit(args) {
    let {constructId, unitId} = args;
    let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
    return ResponseData.success(rcjList);
  }

  /**
   * 获取定额下所有人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllRcjByDe(args) {
    let {constructId, unitId, deId} = args;
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    return ResponseData.success(rcjList);
  }


  /**
   * 获取补充编码序列
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getDefaultCode(args) {
    const result = await this.service.gongLiaoJiProject.gljRcjService.getDefaultCode(args);
    return ResponseData.success(result);
  }

  /**
   *  查询内存下拉code
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getMemoryCode(args) {
    const result = await this.service.gongLiaoJiProject.gljRcjService.getMemoryCode(args);
    return ResponseData.success(result);
  }

  /**
   *  造价系数调整  锁定查询
   * @param args
   * @return {Promise<void>}
   */
  async getRcjCoefficientLock(args){
    const result =await this.service.gongLiaoJiProject.gljRcjService.getRcjCoefficientLock(args);
    return ResponseData.success(result);
  }


  /**
   *  造价系数调整  锁定保存
   * @param args
   * @return {Promise<void>}
   */
  async saveRcjCoefficientLock(args){
    await this.service.gongLiaoJiProject.gljRcjService.saveRcjCoefficientLock(args);
    return ResponseData.success(true);
  }

  /**
   *  造价系数调整 修改
   * @param args
   * @return {Promise<void>}
   */
  async changeRcjCoefficient(args){
    const result =await this.service.gongLiaoJiProject.gljRcjService.changeRcjCoefficient(args);
    return ResponseData.success(result);
  }

  /**
   * 获取工程造价预览
   * @param args
   * @returns {Promise<*>}
   */
  async getCostPreview(args) {
    const result = await this.service.gongLiaoJiProject.gljRcjService.getCostPreview(args)
    return ResponseData.success(result);
  }

}
gljRcjController.toString = () => '[class gsRcjController]';
module.exports = gljRcjController;
