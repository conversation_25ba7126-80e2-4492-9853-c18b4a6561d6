<!--
 * @Descripttion: 换算信息
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: liuxia
 * @LastEditTime: 2024-11-07 09:53:49
-->
<template>
  <!--  <div class="head">-->
  <!--    <a-button type="text"-->
  <!--      ><icon-font type="icon-biaodan-charu"></icon-font>插入</a-button-->
  <!--    >-->
  <!--    <a-button type="text"-->
  <!--      ><icon-font type="icon-biaodan-charu"></icon-font>上移</a-button-->
  <!--    >-->
  <!--    <a-button type="text"-->
  <!--      ><icon-font type="icon-biaodan-charu"></icon-font>下移</a-button-->
  <!--    >-->
  <!--    <a-button type="text"-->
  <!--      ><icon-font type="icon-biaodan-charu"></icon-font>补充</a-button-->
  <!--    >-->
  <!--    <a-button type="text"-->
  <!--      ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button-->
  <!--    >-->
  <!--  </div>-->
  <vxe-table
    :data="props.tableData"
    height="auto"
    class="table-scrollbar"
    :tree-config="{
      children: 'children',
      expandAll: true,
    }"
  >
    <vxe-column width="60" field="sortNumber"></vxe-column>
    <vxe-column title="换算串" field="conversionString" tree-node></vxe-column>
    <vxe-column title="说明" field="conversionExplain"></vxe-column>
    <vxe-column title="来源" field="source"></vxe-column>
  </vxe-table>
</template>

<script setup>
const props = defineProps(['tableData']);
</script>

<style lang="scss" scoped></style>
