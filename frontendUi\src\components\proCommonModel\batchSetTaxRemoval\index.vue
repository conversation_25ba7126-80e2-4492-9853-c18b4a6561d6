<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2024-03-14 11:23:42
 * @LastEditors: liuxia
 * @LastEditTime: 2024-07-24 14:33:05
-->
<template>
  <div class="batch-set-taxRemoval">
    <common-modal
      className="dialog-comm resizeClass"
      title="批量设置结算除税系数"
      width="700"
      height="200"
      v-model:modelValue="props.visible"
      :mask="false"
      @cancel="cancel"
    >
      <div class="range-content">
        设置除税系数(%)：<vxe-input
          :clearable="false"
          v-model.trim="inputValue"
          type="text"
        ></vxe-input>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="sureHandle">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';

const props = defineProps(['visible', 'selectData']);
const emits = defineEmits(['update:visible', 'updateData']);
const projectStore = projectDetailStore();

const inputValue = ref(1);

const cancel = () => {
  emits('update:visible', false);
};

const sureHandle = () => {
  if (projectStore.currentTreeInfo.levelType === 1) {
    constructBatchUpdateTaxRemovalController();
  } else {
    batchUpdateTaxRemoval();
  }
};

const batchUpdateTaxRemoval = () => {
  let selectData = projectStore.summaryProjectAutoPosition?.selectData || {};
  console.log('selectData', selectData);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    tcType: Number(projectStore.asideMenuCurrentInfo?.key),
    list: JSON.parse(
      JSON.stringify(projectStore.summaryProjectAutoPosition?.selectData)
    ),
    value: Number(inputValue.value),
  };
  console.log('apiData', apiData);
  api.batchUpdateTaxRemoval(apiData).then(res => {
    console.log('9999999999', res);
    if (res.status === 200 && res.result) {
      message.success('批量设置结算除税系数成功');
      cancel();
      emits('updateData');
    }
  });
};

const constructBatchUpdateTaxRemovalController = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    tcType: Number(projectStore.asideMenuCurrentInfo?.key),
    list: JSON.parse(
      JSON.stringify(projectStore.summaryProjectAutoPosition?.selectData)
    ),
    value: Number(inputValue.value),
  };
  console.log('apiData', apiData);
  api.constructBatchUpdateTaxRemovalController(apiData).then(res => {
    console.log('9999999999', res);
    if (res.status === 200 && res.result) {
      message.success('批量设置结算除税系数成功');
      cancel();
      emits('updateData');
    }
  });
};
</script>

<style lang="scss" scoped>
.range-content {
  margin-bottom: 30px;
}
</style>
