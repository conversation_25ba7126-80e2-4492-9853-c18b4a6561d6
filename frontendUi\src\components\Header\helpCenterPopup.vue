<!--
 * @Descripttion: 帮助中心
 * @Author: liuxia
 * @Date: 2024-11-26 15:37:01
 * @LastEditors: liuxia
 * @LastEditTime: 2024-11-26 15:37:01
-->
<template>
  <common-modal
    className="dialog-comm set-dialog"
    v-model:modelValue="visible"
    title="帮助中心"
    @close="close"
  >
    <div class="content">
      <div class="list-label">
        <div
          :class="{ active: item.key === tabKey, label: true }"
          for=""
          v-for="item in tabList"
          :key="item.key"
          @click="tabChange(item.key)"
        >
          {{ item.name }}
        </div>
      </div>

      <div class="list-content" v-if="tabKey === 'file'">
        <div class="item">
          <div class="path">
            <img :src="getUrl('document.png')">
            <span>2008年清单计价规范</span>
          </div>
        </div>
      </div>
      <div class="list-content" v-if="tabKey === 'setting'">
        <div class="item">
          <div class="path">
            <img :src="getUrl('document.png')">
            <span>2009年清单计价规范</span>
          </div>
        </div>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import {getUrl} from "@/utils/index.js";
const projectStore = projectDetailStore();
const emits = defineEmits(['closePopup']);

let tabList = [
  {
    key: 'file',
    name: '政策文件',
  },
  {
    key: 'setting',
    name: '勘误说明',
  },
];
let tabKey = ref('file');

const tabChange = key => {
  tabKey.value = key;
};

const visible = ref(true);
const close = () => {
  emits('closePopup');
};

</script>

<style lang="scss">
.set-dialog {
  width: 60%;
  max-width: 600px;
  min-width: 200px;
  .vxe-modal--content {
    padding-bottom: 0 !important;
  }
  .content {
    display: flex;
    .list-label {
      min-height: 260px;
      padding-bottom: 20px;
      border-right: 1px solid rgba(224, 224, 224, 1);
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #2a2a2a;
        opacity: 1;
        padding: 4px 20px 4px 0;
        margin-bottom: 10px;
        border-right: 1px solid transparent;
        white-space: nowrap;
        cursor: pointer;
        &.active {
          border-right-color: #287cfa;
          color: #287cfa;
        }
      }
    }
    .list-content {
      padding: 0 60px 40px 40px;
      .item {
        margin-bottom: 23px;
        .title {
          display: block;
          font-size: 14px;
          font-weight: 400;
          color: #2a2a2a;
          margin-bottom: 20px;
        }
        .path {
          display: flex;
          align-items: center;
          .change {
            background: rgba(255, 255, 255, 0.39);
            border: 1px solid #bfbfbf;
            opacity: 1;
            border-radius: 3px;
            font-size: 14px;
            font-weight: 400;
            color: #2a2a2a;
            outline: none;
            padding: 6px 10px;
          }
          span {
            font-size: 14px;
            color: #898989;
            margin-left: 15px;
          }
        }
      }
    }
  }
}
</style>
