import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { includes } from 'lodash';
const store = projectDetailStore();
/**
 * 定额类型判断
 * @param {*} type
 * @param {*} rowType 有则判断得是工程项目重得行数据类型
 * @returns
 */
export const isDeType = (type, rowType = '') => {
  // debugger;
  if (store.currentTreeInfo.levelType === 1 && rowType) {
    return rowType === type;
  }
  return store.deStandardReleaseYear === type;
};
const getTableColumns = (emits, type) => {
  let tableColumns = [
    // 常用项
    {
      title: '序号',
      field: 'dispNo',
      width: 50,
      align: 'center',
      classType: 1,
      slot: true,
      editRender: {
        enabled:
          store.currentTreeInfo.levelType === 3 &&
          store.asideMenuCurrentInfo?.name === '主要材料、设备表'
            ? true
            : false,
        autofocus: '.vxe-input--inner',
      },
      fixed: 'left',
    },
    {
      title: '材料编码',
      field: 'materialCode',
      width: 100,
      classType: 1,
      fixed: 'left',
      cellType: 'string',
    },
    {
      title: '类型',
      field: 'type',
      width: 80,
      slot: true,
      classType: 1,
      fixed: 'left',
      editRender: {
        enabled: store.currentTreeInfo.levelType === 1 ? false : true,
      },
    },
    {
      title: '名称',
      field: 'materialName',
      width: 150,
      editRender: {
        enabled: store.currentTreeInfo.levelType === 1 ? false : true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      fixed: 'left',
      slot: true,
    },
    {
      title: '规格型号',
      field: 'specification',
      width: 100,
      editRender: {
        enabled: store.currentTreeInfo.levelType === 1 ? false : true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '单位',
      field: 'unit',
      width: 100,
      editRender: {
        enabled: store.currentTreeInfo.levelType === 1 ? false : true,
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '数量',
      field: 'totalNumber',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: isDeType('12')
        ? '定额价'
        : Number(store.taxMade) === 1
        ? '不含税基期价'
        : '含税基期价',
      field: 'dePrice',
      width: 110,
      classType: 1,
      slot: true,
    },
    {
      title: '市场价',
      field: 'marketPrice',
      width: 110,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '不含税市场价',
      field: 'priceMarket',
      width: 110,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '含税市场价',
      field: 'priceMarketTax',
      width: 110,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '市场价合计',
      field: 'total',
      width: 110,
      classType: 1,
      slot: true,
    },
    {
      title: '不含税市场价合计',
      field: 'priceMarketTotal',
      width: 110,
      classType: 1,
      slot: true,
    },
    {
      title: '含税市场价合计',
      field: 'priceMarketTaxTotal',
      width: 110,
      classType: 1,
      slot: true,
    },
    {
      title: '税率',
      field: 'taxRate',
      width: 110,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '价格来源',
      field: 'sourcePrice',
      width: 150,
      classType: 1,
    },
    {
      title: '是否汇总(二次分析)',
      field: 'markSum',
      width: 130,
      classType: 1,
      slot: true,
      exportMethod: ({ row }) => {
        return row.markSum && [1, 2].includes(Number(row.levelMark))
          ? '是'
          : '';
      },
    },
    {
      title: '除税系数(%)',
      field: 'taxRemoval',
      width: 120,
      editRender: {
        autofocus: '.vxe-input--inner',
      },
      classType: 1,
      slot: true,
    },
    {
      title: '进项税额',
      field: 'jxTotal',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '价差',
      field: 'priceDifferenc',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '价差合计',
      field: 'priceDifferencSum',
      width: 100,
      classType: 1,
      slot: true,
    },
    {
      title: '供货方式',
      field: 'ifDonorMaterial',
      width: 100,
      classType: 1,
      slot: true,
      editRender: {
        enabled: store.standardGroupOpenInfo.isOpen ? false : true,
      },
      exportMethod: ({ row }) => {
        return getDonorMaterialText(row.ifDonorMaterial);
      },
    },
    {
      title: '甲供数量',
      field: 'donorMaterialNumber',
      width: 100,
      classType: 1,
      slot: true,
      editRender: {
        enabled:
          store.currentTreeInfo.levelType === 1 ||
          store.standardGroupOpenInfo.isOpen
            ? false
            : true,
        autofocus: '.vxe-input--inner',
      },
      className: ({ row }) => {
        return row.donorMaterialNumber > row.totalNumber
          ? 'background-red'
          : '';
      },
    },
    {
      title: '三材类别',
      field: 'kindSc',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '三材系数',
      field: 'transferFactor',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '是否暂估',
      field: 'ifProvisionalEstimate',
      width: 100,
      classType: 1,
      slot: true,
      exportMethod: ({ row }) => {
        return row.ifProvisionalEstimate && row.checkIsShow ? '是' : '';
      },
    },
    {
      title: '市场价锁定',
      field: 'ifLockStandardPrice',
      width: 100,
      classType: 1,
      slot: true,
      exportMethod: ({ row }) => {
        return row.ifLockStandardPrice && row.checkIsShow ? '是' : '';
      },
    },
    {
      title: '产地',
      field: 'producer',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '厂家',
      field: 'manufactor',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '品牌',
      field: 'brand',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '送达地点',
      field: 'deliveryLocation',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '质量等级',
      field: 'qualityGrade',
      width: 100,
      classType: 1,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
    },
    {
      title: '是否输出',
      field: 'output',
      width: 100,
      classType: 1,
      slot: true,
    },
  ];
  tableColumns = tableColumns.filter(item => {
    const field = item.field;
    if (store.asideMenuCurrentInfo?.key !== '7' && 'output' === field) {
      return false;
    }
    if (['priceMarketTotal'].includes(field) && Number(store.taxMade) === 0) {
      // 'priceMarket',如果是不含税并且是简易计税的话，不显示;一般-不含税
      return false;
    }
    if (
      ['priceMarketTaxTotal'].includes(field) &&
      Number(store.taxMade) === 1
    ) {
      // 'priceMarketTax',如果是含税并且是一般计税的话，不显示;简易-含税
      return false;
    }
    if (store.currentTreeInfo.levelType !== 3) {
      // 工程项目级别
      if (
        [
          'producer',
          'manufactor',
          'brand',
          'deliveryLocation',
          'qualityGrade',
        ].includes(field)
      ) {
        return false;
      }
      if (isDeType('22') && ['marketPrice', 'total'].includes(field)) {
        return false;
      }
      if (store.deType === '12') {
        if (
          [
            'taxRate',
            'priceMarketTax',
            'priceMarket',
            'priceMarketTaxTotal',
            'priceMarketTotal',
          ].includes(field)
        ) {
          // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
          return false;
        }
      }
      return true;
    }

    if (
      isDeType('12') &&
      [
        'taxRate',
        'priceMarketTax',
        'priceMarket',
        'priceMarketTaxTotal',
        'priceMarketTotal',
      ].includes(field)
    ) {
      // 22de 显示税率\含税市场价合计\不含税市场价合计\含税市场价\不含税市场价
      return false;
    }
    console.log('isDeType(12)', isDeType('12'), store.deType);
    if (isDeType('22')) {
      if (['marketPrice', 'total', 'taxRemoval'].includes(field)) {
        // 市场价、市场价合价、除税系数22不显示
        return false;
      }
    }
    if (
      'jxTotal' === field &&
      (isDeType('22') || Number(store.taxMade) === 0)
    ) {
      //进项税额 是22或者简易计税不显示
      return false;
    }
    return true;
  });
  return tableColumns;
};

export const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];
export const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
export default getTableColumns;
