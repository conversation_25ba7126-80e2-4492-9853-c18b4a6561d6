<!--
 * @Author: wangru
 * @Date: 2023-08-04 10:39:12
 * @LastEditors: liuxia
 * @LastEditTime: 2024-10-12 16:18:18
 *装饰超高
-->
<template>
  <div>
    <div class="content">
      <div class="table-content">
        <vxe-table
          align="center"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          :data="tableData"
          height="420"
          width="450"
		  class='table-line'
          :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parentId',
            line: true,
            showIcon: true,
            expandAll: true,
			iconOpen: 'icon-caret-down',
			iconClose: 'icon-caret-right'
          }"
          :row-class-name="rowClassName"
        >
          <vxe-column field="cgCode" width="20%" title="项目编码" tree-node>
          </vxe-column>
          <vxe-column field="type" width="5%" title="类型"> </vxe-column>
          <vxe-column field="name" width="29%" title="名称"> </vxe-column>
          <vxe-column field="up" width="23%" title="檐高/层数">
            <template #default="{ row }">
              <a-select
                v-model:value="row.up"
                style="width: 90%"
                placeholder="请选择檐高/层数"
                @select="selectChange(row, 'up')"
                :options="storeyList"
                :fieldNames="{
                  label: 'storey',
                  value: 'cgDeCode',
                }"
                class="dropdwonClass"
              >
              </a-select>
            </template>
          </vxe-column>
          <vxe-column field="value" width="23%" title="超高计取类型">
            <template #default="{ row }">
              <a-select
                v-model:value="row.value"
                style="width: 90%"
                placeholder="请选择超高计取类型"
                @select="selectChange(row, 'value')"
                :options="typeList"
                :fieldNames="{
                  label: 'label',
                  value: 'value',
                }"
                class="dropdwonClass"
              >
              </a-select>
            </template>
          </vxe-column>
        </vxe-table>

        <a-button
          @click="locationModel = true"
          type="primary"
          ghost
          :disabled="tableData && tableData.length === 0"
          >计取位置</a-button
        >
      </div>
    </div>
    <a-button type="primary" @click="qdExistDe()">提交</a-button>
  </div>
  <common-modal
    title="装饰超高计取位置"
    width="700"
    height="350"
    className="dialog-comm"
    v-model:modelValue="locationModel"
  >
    <a-radio-group
      v-model:value="optionType"
      style="width: 100%"
      @change="radioChange"
    >
      <p class="radioP" v-if="haveQdList.length > 0">
        <a-radio :value="1">计取至已有清单</a-radio>
        <a-select
          v-model:value="constructionMeasureType"
          :size="size"
          style="width: 70%"
          :options="haveQdList"
          placeholder="请选择"
          :field-names="{ label: 'className', value: 'classCode' }"
          :disabled="optionType !== 1"
          @change="getCheckQD()"
        ></a-select>
      </p>
      <p class="radioP">
        <a-radio :value="2">新建清单</a-radio>
        <a-select
          v-model:value="constructionMeasureType1"
          :size="size"
          style="width: 70%"
          :options="addQDTypeList"
          placeholder="请选择"
          :field-names="{ label: 'className', value: 'classCode' }"
          :disabled="optionType !== 2"
        ></a-select>
      </p>
    </a-radio-group>
    <p class="line"></p>
    <p class="radioP">
      <span class="title">具体清单</span>
      <span class="detail"
        ><a-input
          v-model:value="selectInputInfo"
          :disabled="selectInputInfo"
          placeholder="具体清单名称"
          style="width: 78%; margin-left: 10px"
        />
        <a-button
          type="primary"
          :disabled="optionType === 2"
          @click="detailModel = true"
          >详情</a-button
        >
      </span>
    </p>

    <p class="btns">
      <a-button type="primary" @click="sureLocation()">确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="指定具体清单"
    width="750"
    height="500"
    className="dialog-comm"
    v-model:modelValue="detailModel"
  >
    <div class="table-content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="
          haveQdList.filter(x => x.classCode === constructionMeasureType)[0]
            .data
        "
        height="350"
        width="450"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: true,
          showIcon: false,
          expandAll: true,
        }"
      >
        <vxe-column field="cgCode" width="20%" title="项目编码"> </vxe-column>
        <vxe-column field="type" width="20%" title="类型"> </vxe-column>
        <vxe-column field="name" width="20%" title="名称"> </vxe-column>
        <vxe-column field="±0.00以上" width="40%" title="计取至该清单">
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.isCheck"
              :disabled="row.kind !== '03'"
              :checked-value="1"
              :unchecked-value="2"
              @change="selectQDChange(row)"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <p class="btns">
      <a-button type="primary" @click="detailModel = false" ghost
        >取消</a-button
      >
      <a-button type="primary" @click="sureJQ()">确定</a-button>
    </p>
  </common-modal>
  <info-modal
    v-model:infoVisible="infoVisible"
    :infoText="infoText"
    :isSureModal="isSureModal"
    :iconType="iconType"
    @updateCurrentInfo="updateCurrentInfo"
  ></info-modal>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue';
import api from '../../../../api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();
let locationModel = ref(false);
let detailModel = ref(false);
let tableData = ref([]); // 装饰超高列表数据
let storeyList = ref([]);
let selectQDInfo = ref(); // 选择具体清单信息
let selectInputInfo = ref('');
const emits = defineEmits(['updateData', 'close']);

let haveQdList = ref([]); //记取清单位置列表
let recordPosition = ref(null);
let optionType = ref(); //记取位置选择 空值-直接点击默认  1 已有清单 2 新建清单
let constructionMeasureType = ref(); // 已有清单选择的类型
let constructionMeasureType1 = ref(); // 新建清单选择的类型
let infoVisible = ref(false); // 提示信息框是否显示
let infoText = ref(''); // 提示信息框的展示文本
let iconType = ref(''); // 提示信息框的图标
let isSureModal = ref(false); // 提示信息框是否为确认提示框
let cgCacheData = ref(); //缓存数据

const typeList = reactive([
  {
    label: '不计取超高费用',
    value: 0,
  },
  {
    label: '按50%计取超高费用',
    value: 0.5,
  },
  {
    label: '计取超高费用',
    value: 1,
  },
]);
const addQDTypeList = reactive([
  {
    className: '措施项目-单价措施',
    classCode: 1,
  },
  {
    className: '措施项目-其他总价措施',
    classCode: 3,
  },
  {
    className: '分部分项',
    classCode: 0,
  },
]);
onMounted(() => {
  cgStoreyList();
  conditionDeList();
  // getRecordPositionList();
});
// 列表数据获取
const conditionDeList = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.conditionDeList(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('列表数据', res.result);
      tableData.value = res.result;
      tableData.value.forEach(
        item => (
          (item.up = item.up === null ? '' : item.up),
          (item.cgCode = item.bdCode ? item.bdCode : item.cgCode)
        )
      );
      api.cgCostMathCache(apiData).then(a => {
        console.log('缓存数据', a);
        if (a.status === 200) {
          cgCacheData.value = a.result;
          getRecordPositionList();
        }
      });
    }
  });
};
const findParentUpValue = (item, tree) => {
  let parent = tree.find(a => a.sequenceNbr === item?.parentId);
  let flag = tableData.value.find(a => a.sequenceNbr === item?.parentId);
  return !parent && flag
    ? findParentUpValue(flag, tree)
    : parent
    ? parent
    : { up: '' };
};
const getFinallyData = () => {
  //对比缓存数据和获取到的列表数据
  tableData.value.map(item => {
    //列表数据和缓存数据对比
    let same = cgCacheData.value?.data.find(
      cg => cg.sequenceNbr === item.sequenceNbr
    );
    if (same) {
      item.up = same.up;
      item.value = same.value;
    } else {
      //查找父级是否有缓存数据，获取父级up值
      if (cgCacheData.value) {
        let target = findParentUpValue(item, cgCacheData.value.data);
        console.log('target', target);
        item.up = target?.up;
      }
    }
  });
  if (cgCacheData.value) {
    console.log('haveQdList.value', haveQdList.value);
    if (
      haveQdList.value.find(
        item => item.classCode === cgCacheData.value.constructionMeasureType
      )
    ) {
      optionType.value = 1;
    } else {
      optionType.value = cgCacheData.value.optionType;
    }

    if (optionType.value === 2) {
      constructionMeasureType1.value = Number(
        cgCacheData.value.constructionMeasureType
      );
      constructionMeasureType.value = null;
      getCgQd();
    } else {
      constructionMeasureType.value = Number(
        cgCacheData.value.constructionMeasureType
      );
      constructionMeasureType1.value = null;
    }
  } else if (!cgCacheData.value) {
    optionType.value = haveQdList.value.length > 0 ? 1 : 2;
    if (optionType.value === 2) {
      constructionMeasureType1.value = addQDTypeList[0].classCode;
      getCgQd();
    } else {
      constructionMeasureType.value = haveQdList.value[0]?.classCode;
    }
  }
};
//层高下拉框列表数据获取
const cgStoreyList = () => {
  api
    .cgStoreyList({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
      unitId: projectStore.currentTreeInfo?.id, //单位ID
    })
    .then(res => {
      if (res.status === 200 && res.result) {
        console.log('装饰超高列表数据', res.result);
        storeyList.value = res.result;
      }
    });
};
//获取记取清单位置列表
const getRecordPositionList = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  api.recordPositionCgList(apiData).then(res => {
    console.log('清单位置列表数据', res);
    if (res.status === 200 && res.result) {
      haveQdList.value = res.result;
      haveQdList.value.forEach(data => {
        data.data.forEach(
          item => (item.cgCode = item.bdCode ? item.bdCode : item.cgCode)
        );
      });
      getFinallyData();
      if (optionType.value === 1) {
        getCheckQD();
      }
    }
  });
};
const getCheckQD = (reset = false) => {
  //获取选中的清单并默认勾选
  if (cgCacheData.value && reset) {
    haveQdList.value.map(item => {
      item.data.map(i => {
        if (i.sequenceNbr === cgCacheData.value.qdId) {
          i.isCheck = 1;
          selectQDInfo.value = i;
        }
      });
    });
  } else {
    let defaultQdList = haveQdList.value.filter(
      x => x.classCode === constructionMeasureType.value
    )[0].data;
    let isCheckItem = defaultQdList.find(
      item => item.kind === '03' && item.isCheck === 1
    );
    selectQDInfo.value = isCheckItem
      ? isCheckItem
      : defaultQdList.find(item => item.kind === '03');
    selectQDInfo.value.isCheck = 1;
    console.log('!reset', defaultQdList);
  }
  if (selectQDInfo.value) {
    selectInputInfo.value =
      selectQDInfo.value.cgCode + ' ' + selectQDInfo.value.name;
  }
};
const selectQDChange = row => {
  //具体清单列表空值单选
  if (row.isCheck) {
    //具体清单记取只可以选择一项
    haveQdList.value.map(item => {
      item.data.map(i => {
        if (i.sequenceNbr !== row.sequenceNbr) {
          i.isCheck = 0;
        }
      });
    });
  }
};
const radioChange = e => {
  if (optionType.value === 2) {
    constructionMeasureType.value = null;
    constructionMeasureType1.value =
      cgCacheData.value && cgCacheData.value.optionType === 2
        ? Number(cgCacheData.value.constructionMeasureType)
        : 1;
    getCgQd();
  } else {
    constructionMeasureType1.value = null;
    selectInputInfo.value = '';
    constructionMeasureType.value =
      cgCacheData.value && cgCacheData.value.optionType === 1
        ? Number(cgCacheData.value.constructionMeasureType)
        : haveQdList.value[0].classCode;
    getCheckQD();
  }
};
const getCgQd = () => {
  api.getCgQd().then(res => {
    console.log('指定清单数据', res);
    if (optionType.value === 2) {
      selectQDInfo.value = res.result;
      selectInputInfo.value =
        selectQDInfo.value.bdCodeLevel04 +
        ' ' +
        selectQDInfo.value.bdNameLevel04;
    }
  });
};
const selectChange = (row, field) => {
  console.log('selectChange', row, field);
  assignPropertyValue(tableData.value, row.sequenceNbr, row[field], field);
};
const assignPropertyValue = (items, parentId, propertyValue, type) => {
  items.forEach(item => {
    if (item.parentId === parentId) {
      item[type] = propertyValue;
      assignPropertyValue(items, item.sequenceNbr, propertyValue, type);
    }
  });
};
//点击记取需要判断是否已记取
const qdExistDe = () => {
  if (tableData.value && tableData.value.length === 0) {
    message.warning('请先创建定额');
    emits('close');
    return;
  }
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    qdId: selectQDInfo.value?.sequenceNbr,
    constructionMeasureType:
      optionType.value === 1
        ? constructionMeasureType.value
        : constructionMeasureType1.value,
    isCostDe: 3, //超高
  };
  api.qdExistDe(apiData).then(res => {
    console.log('dddddddddddddd', res);
    if (res.status === 200) {
      // 弹框提示
      if (res.result) {
        infoVisible.value = true;
        isSureModal.value = false;
        infoText.value = '该清单下已计取定额，是否更新？';
      } else {
        submit();
      }
    }
  });
};
const submit = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    // data: JSON.parse(
    //   JSON.stringify(tableData.value.filter(x => x.kind === '04'))
    // ),
    data: JSON.parse(JSON.stringify(tableData.value)),
    optionType: optionType.value,
    constructionMeasureType:
      optionType.value === 1
        ? constructionMeasureType.value
        : constructionMeasureType1.value,
    qdId: selectQDInfo.value?.sequenceNbr,
  };
  console.log('装饰超高api参数', apiData);
  if (apiData.data.length === 0) {
    message.warning('请先创建定额');
    return;
  }
  api.cgCostMath(apiData).then(res => {
    console.log('装饰垂运结果', res);
    if (res.status === 200 && res.result) {
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = res.result;
      iconType.value = 'icon-ruotixing';
    } else if (res.status === 200 && !res.result) {
      emits('close');
    }
  });
};
const sureLocation = () => {
  locationModel.value = false;
};
const sureJQ = () => {
  detailModel.value = false;
  let allList = haveQdList.value.filter(
    x => x.classCode === constructionMeasureType.value
  )[0].data;
  selectQDInfo.value = allList.filter(item => item.isCheck)[0];
  selectInputInfo.value =
    selectQDInfo.value.cgCode + ' ' + selectQDInfo.value.name;
};
const updateCurrentInfo = type => {
  console.log('updateCurrentInfo', type);
  if (!infoVisible.value) return;
  infoVisible.value = false;
  if (type === 1) {
    emits('updateData');
  } else {
    submit();
  }
};

const rowClassName = ({ row }) => {
  let ClassStr = 'normal-info';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }

  return ClassStr;
};
</script>
<style lang="scss" scoped>
@use './tableIcon.scss';
.content {
  // .selectContent {
  // 	display: flex;
  // 	justify-content: space-around;
  // 	height: 40px;
  // }
  .table-content {
    height: 85%;
  }
}
.radioP {
  display: flex;
  justify-content: space-between;
  margin: 10px auto;
  width: 80%;
  .title {
    margin-left: 5%;
    font-size: 14px;
    color: #2a2a2a;
  }
}
.btns {
  width: 10%;
  display: flex;
  margin: auto;
  justify-content: space-around;
}
.line {
  width: 100%;
  height: 7px;
  background: rgba(221, 221, 221, 0.39);
  opacity: 0.52;
  margin: 20px 0;
}
.detail {
  width: 73%;
}
::v-deep .table-content .ant-btn-primary {
  margin: 10px !important;
}
::v-deep .ant-btn-primary {
  margin: 10px 42% !important;
}
::v-deep .detail .ant-btn-primary {
  margin: 0 -1px !important;
}
::v-deep .vxe-select > .vxe-input .vxe-input--inner {
  border: none !important;
}
:deep(
    .vxe-table .ant-select:not(.ant-select-customize-input) .ant-select-selector
  ) {
  border: none;
  background: transparent !important;
}

:deep(
    .vxe-table
      .ant-select-single.ant-select-show-arrow
      .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholde
  ) {
  font-size: 12px !important;
}

::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2 !important;
}
::v-deep(.vxe-table .row-qd) {
  background: #dce6fa;
}
//[data-v-5d6cbef2] .vxe-table .ant-select:not(.ant-select-customize-input) .ant-select-selector
// .dropdwonClass {
//   font-size: 12px;
//   :deep(.ant-select-dropdown-menu-item) {
//     font-size: 12px !important;
//   }
//   :deep(.ant-select-item) {
//     font-size: 12px !important;
//   }
// }
</style>
