<template>
  <div class="table-content table-content-flex-column">
    <split
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
      :onlyPart="!isShowPolicy ? 'Top' : 'all'"
    >
      <template #one>
        <fee-dec-total
          @isConfirm="isDisabled"
          ref="feeDecTotal"
          @getfeeTotalData="getFeeTotalData"
          @saveCalData="saveCalData"
          :isUpdateDocBol="isUpdateDocBol"
        ></fee-dec-total></template>
      <!-- 目前只有工程项目有政策文件的展示 -->
      <template #two>
        <policy-doc
          ref="policyDoc"
          @isConfirm="isDisabled"
          @getpolicyData="getPolicyFiles"
          @isUpdate="isUpdateDoc"
          v-if="isShowPolicy"
        ></policy-doc></template>
    </split>
  </div>
  <!-- <common-modal
    className="dialog-comm"
    title="计税方式"
    width="600"
    v-model:modelValue="isRate"
    @cancel="cancel"
    @close="isRate = false"
  >
    <rate-list @isShow="cancelRate"></rate-list>
  </common-modal> -->
</template>

<script setup>
import { onMounted, reactive, ref, watch, getCurrentInstance } from 'vue';
import FeeDecTotal from './FeeDecTotal.vue';
import PolicyDoc from './PolicyDoc.vue';
import { Modal } from 'ant-design-vue';
import RateList from './RateList.vue';
import { projectDetailStore } from '@/store/projectDetail';
import operateList from '../operate';
import infoMode from '@/plugins/infoMode.js';
import { context } from 'ant-design-vue/lib/vc-image/src/PreviewGroup';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let unifyData = operateList.value.find(item => item.name === 'unify');
let policyFiles = ref(null); //政策文件选中的数据
let feeTotalData = ref(); //费率总览和汇总的数据
const store = projectDetailStore();
let isRate = ref(false);
unifyData.disabled = true;
let isShowPolicy = ref(false);
onMounted(() => {
  getIsShowPolicy();
});
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  () => {
    if (
      store.tabSelectName === '取费表' &&
      store.currentTreeInfo.levelType !== 2
    ) {
      getIsShowPolicy();
    }
  }
);
const getIsShowPolicy = () => {
  isShowPolicy.value =
    store.deType === '12' ||
    (store.deType === '22' &&
      store.currentTreeInfo.levelType === 3 &&
      store.currentTreeInfo.deStandardReleaseYear === '12');
  console.log('isShowPolicy.value', isShowPolicy.value);
};
const feeDecTotal = ref();
const policyDoc = ref();
bus.on('unify', data => {
  if (store.componentId === 'feeWithDrawalTable') {
    console.log('----------------');
    showConfirm();
  }
});
bus.on('taxation', data => {
  if (store.componentId === 'feeWithDrawalTable') isRate.value = true;
});
const initData = () => {
  unifyData.disabled = true;
  store.SET_HUMAN_UPDATA_DATA(null);
};
const showConfirm = () => {
  if (!unifyData.disabled) {
    // Modal.confirm({
    //   title: '是否统一应用',
    //   content:
    //     '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？',
    //   zIndex: '99999',
    //   onOk() {
    //     feeDecTotal.value.projectUpdate();
    //     if (policyFiles.value) {
    //       policyDoc.value.saveData(policyFiles.value);
    //     }
    //     initData();
    //   },
    //   onCancel() {
    //     if (policyFiles.value) {
    //       policyDoc.value.getFeePolicyDocData(); //取消的话政策文件恢复
    //       initData();
    //     }
    //   },
    // });
    infoMode.show({
      iconType: 'icon-querenshanchu',
      infoText: '是否统一应用',
      descText:
        '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？',
      confirm: () => {
        feeDecTotal.value.projectUpdate();
        if (policyFiles.value) {
          policyDoc.value.saveData(policyFiles.value);
        }
        initData();
        infoMode.hide();
      },
      close: () => {
        if (policyFiles.value) {
          policyDoc.value.getFeePolicyDocData(); //取消的话政策文件恢复
          initData();
        }
        infoMode.hide();
      },
    });
  } else {
    console.log(111);
  }
};
let isUpdateDocBol = ref(false);
const isUpdateDoc = bol => {
  isUpdateDocBol.value = bol;
};
const isDisabled = boolean => {
  unifyData.disabled = boolean;
};
const getPolicyFiles = data => {
  policyFiles.value = data;
  saveData();
};
const saveData = () => {
  store.SET_HUMAN_UPDATA_DATA({
    isEdit: true,
    name: 'unify',
    updataData: {
      policy: policyFiles.value
        ? JSON.parse(JSON.stringify(policyFiles.value))
        : null,
      feeTotal: feeTotalData.value
        ? JSON.parse(JSON.stringify(feeTotalData.value))
        : null,
      calEditData: calEditData.value,
    },
  });
};
let calEditData = ref([]);
const saveCalData = data => {
  calEditData.value = data;
  saveData();
};
const getFeeTotalData = data => {
  feeTotalData.value = data;
  saveData();
};
const cancelRate = key => {
  if (key === 'isRate') {
    isRate.value = false;
    feeDecTotal.value.getTotalAndDesData();
  }
};
const refresh = () => {
  feeDecTotal.value.getTotalAndDesData();
  if (store.deType !== '22') {
    policyDoc.value.getpolicyData();
  }
};
defineExpose({
  refresh,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  padding: 0 10px;
  margin-bottom: 10px;
  img {
    margin-right: 5px;
  }
}
.table-content {
  width: 100%;
  margin-top: 10px;
  height: calc(100%);
  // overflow: auto;
}
.taxMethods {
  position: fixed;
  width: 600px;
  height: 500px;
  top: calc((100% - 500px) / 2);
  left: calc((100% - 600px) / 2);
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
  z-index: 10;
  border-radius: 5px;
  box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.26);
  .header {
    // cursor: move;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background-color: #4d89e3;
    color: #ffffff;
    position: relative;
    border-radius: 5px 5px 0 0;
    .drag {
      height: 50px;
      position: fixed;
      margin-left: 40px;
      cursor: move;
      width: 500px;
    }
    p {
      width: 90%;
      display: flex;
      margin: 0 auto 10px;
      justify-content: space-between;
      .taxTitle {
        font-size: 14px;
      }
      .close {
        cursor: pointer;
        font-size: 30px;
        img {
          margin-right: 40px;
        }
      }
      .taxTitle::before {
        content: '|';
        width: 2px;
        height: 14px;
        margin-right: 5px;
      }
    }
  }
}
</style>
