<!--
 * @Descripttion:批量载价
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-13 14:10:42
-->
<template>
  <div class="content">
    <priority-list
      class="priority"
      :priorityData="priorityData"
      @getFinList="getFinList"
    ></priority-list>
    <div class="periodicalList">
      <div
        class="periodcal"
        v-for="reType in regionList"
      >
        <span
          class="title"
          v-if="reType.title !== '信息价'"
        >
          {{ reType.title }}</span>
        <span
          v-else
          class="title"
        >
          <a-tooltip
            placement="rightTop"
            color="white"
          >
            <template #title>
              <span style="color: #000000; font-size: 12px">批量载价默认不会调整已手动修改相关材料数据</span>
            </template>
            信息价期刊 <icon-font type="icon-bangzhu"></icon-font>
          </a-tooltip>
        </span>

        <p
          class="selectList"
          v-for="(item, index) in reType.selectValue"
        >
          <a-cascader
            v-if="reType.title==='信息价'"
            v-model:value="item.cityVal"
            placeholder="请选择地区"
            :options="reType.cityList"
            style="width: 36%"
            :allowClear="false"
            @change="getItemdateVal(item, index, reType)"
          />
          <a-select
            v-if="reType.title!=='信息价'"
            v-model:value="item.cityVal"
            :options="reType.cityList.map(pro => ({ value: pro }))"
            placeholder="请选择地区"
            style="width: 36%"
            @change="getItemdateVal(item, index, reType)"
          ></a-select>
          <a-select
            v-model:value="item.dateVal"
            placeholder="请选择期刊"
            style="width: 43%"
            @dropdownVisibleChange="
              getItemdateVal(item, index, reType, 'date', $event)
            "
          >
            <a-select-option
              v-for="data in reType.showDateList"
              :label="data.date"
              :value="data.date"
              :disabled="data.isDisabled"
            ></a-select-option>
          </a-select>
          <icon-font
            class="delete"
            type="icon-shanchu"
            @click="deleteItem(reType.title, index)"
            v-if="index > 0"
          ></icon-font>
          <span
            v-else
            style="display: inline-block; width: 13px"
          ></span>
        </p>
        <a-button
          type="link"
          @click="addItem(reType.title)"
          v-if="reType.selectValue.length < 5"
          class="add"
        ><icon-font type="icon-zengjia"></icon-font>增加备选项</a-button>
      </div>
    </div>
    <div class="footer">
      <div>
        <a-checkbox v-model:checked="batchAllFlag">批量调整所有价格</a-checkbox>
        <p>
          <icon-font
            type="icon-querenshanchu"
            style="margin-right: 5px"
          ></icon-font>批量载价默认不会调整已手动修改相关材料数据
        </p>
      </div>
      <a-button
        type="primary"
        @click="doBatchLoadingPrice"
      >批量载价</a-button>
    </div>
  </div>
</template>
<script setup>
import { inject, onMounted, reactive, ref, watch } from 'vue';
import csLoadPrice from '@/api/loadPrice';
import * as aes from '@/utils/aes/public.js';
import jsApi from '@/api/jiesuanApi.js';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';

const store = projectDetailStore();
let priorityData = reactive(['信息价', '市场价', '推荐价']);
let priorty = reactive([1, 2, 3]);
const props = defineProps(['isOriginalFlag', 'priceType']);
const emits = defineEmits(['close']);
let propsFun = inject('nextStep');
let batchAllFlag = ref(false); //是否批量调整所有价格
let regionList = ref([
  {
    title: '信息价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: [
      {
        cityVal: [],
        dateVal: null,
      },
    ],
  },
  {
    title: '市场价',
    cityList: [],
    showDateList: [],
    dateList: [],
    selectValue: [
      {
        cityVal: null,
        dateVal: null,
      },
    ],
  },
  {
    title: '推荐价',
    cityList: [],
    showDateList: [],
    dateList: [],
    selectValue: [
      {
        cityVal: null,
        dateVal: null,
      },
    ],
  },
]);

let regionSelectData = ref([1]);
const getFinList = data => {
  console.log('data-----------------', data);
  priorityData = data.priorityData;
  priorty = data.priorty;
};
const getItemdateVal = (select, index, reType, useType = 'city', eve = '') => {
  //选择地区之后获取日期下拉列表及筛选出已选过选项设置为禁选
  if (eve === false) return;

  let selectedArr = [];
  const needDateList = [];
  regionList.value.map(obj => {
    if (obj.title === reType.title) {
      let list = null;
      if (reType.title === '信息价') {
        if (select?.cityVal?.length > 0) {
          list = reType.dateList[select.cityVal[select.cityVal.length - 1]];
        }
      } else {
        list = reType.dateList[select.cityVal];
      }
      list &&
        list.map(d => {
          needDateList.push({ value: select.cityVal + d, label: d });
        });
      obj.selectValue.map(item => {
        selectedArr.push(item.cityVal + item.dateVal);
      });
      selectedArr.splice(index, 1);
      obj.showDateList = [];
      needDateList.map(data => {
        if (selectedArr.includes(data.value)) {
          obj.showDateList.push({ date: data.label, isDisabled: true });
        } else {
          obj.showDateList.push({ date: data.label, isDisabled: false });
        }
      });
      if (useType === 'city') {
        let first = obj.showDateList.find(u => u.isDisabled === false);
        select.dateVal = first?.date;
      }
    }
  });
};
//增加备选项
const addItem = type => {
  console.log('item=>', type);
  regionList.value.map(item => {
    if (item.title === type) {
      item.selectValue.push({
        cityVal: null,
        dateVal: null,
      });
      item.showDateList = null;
    }
  });
};
const deleteItem = (type, index) => {
  console.log('item=>', type, index);
  regionList.value.map(item => {
    if (item.title === type) {
      item.selectValue.splice(index, 1);
    }
  });
};
onMounted(async () => {
  await getCityList();
  await queryLoadPriceAreaDate();
});
let cityAllList = reactive([]);
const getCityList = async () => {
  await csLoadPrice.getDimRegion({}).then(res => {
    if (res.status === 200) {
      let list = JSON.parse(aes.decrypt(res.result));
      list.map(a => {
        a.label = a.name;
        a.value = a.name;
        a?.children?.map(b => {
          b.label = b.name;
          b.value = b.name;
        });
      });
      cityAllList = list.filter(a => a.code !== '130000');
      console.log('cityAllList', cityAllList);
    }
  });
};
const queryLoadPriceAreaDate = async () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单项ID
  }
  console.log('载价数据加载参数', apiData, props.isOriginalFlag);
  await csLoadPrice.queryLoadPriceAreaDate(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('queryLoadPriceAreaDate', res.result);
      for (let citys in res.result) {
        if (citys === '信息价') {
          let cityAll = Object.keys(res.result[citys]);
          regionList.value[0].cityList = cityAllList;
          regionList.value[0].selectValue[0].cityVal = [
            regionList.value[0].cityList[0].value,
            regionList.value[0].cityList[0].children[0].value,
          ];
        }
        setSelectList(citys, res.result);
      }
    }
  });
};
const setSelectList = (item, total) => {
  regionList.value.map(i => {
    // if (i.title === item) {
    //   i.cityList = Object.keys(total[item]);
    //   i.dateList = total[item];
    //   i.selectValue[0].cityVal = i.cityList[0];
    //   getItemdateVal(i.selectValue[0], 0, i, 'city');
    // }
    if (i.title === item) {
      if (i.title !== '信息价') {
        i.cityList = Object.keys(total[item]);
      }
      i.dateList = total[item];
      if (i.title !== '信息价') {
        i.selectValue[0].cityVal = i.cityList[0];
      }
      getItemdateVal(i.selectValue[0], 0, i, 'city');
    }
  });
};

const doBatchLoadingPrice = () => {
  //批量载价
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    type: store.currentTreeInfo.levelType === 1 ? 1 : 2,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单项ID
  } else {
    apiData.deStandardReleaseYear = store.deStandardReleaseYear; //单项ID
  }

  let bodyData = {
    batchAllFlag: batchAllFlag.value,
    laodPriceConditionList: [],
  };
  bodyData.loadPriortyList = priorty;
  regionList.value.map((item, index) => {
    let list = [];
    item.selectValue.forEach(a => {
      let obj = {};
      if (index === 0) {
        obj[a.cityVal[a.cityVal.length - 1]] = a.dateVal;
      } else {
        obj[a.cityVal] = a.dateVal;
      }
      list.push(obj);
    });
    bodyData.laodPriceConditionList.push(list);
  });
  if (!isNextLoad(bodyData.laodPriceConditionList)) {
    message.error('已添加备选项不可为空,请补充完整');
    return;
  }
  store.SET_GLOBAL_LOADING({
    loading: true,
    info: '价格数据查询匹配中，请稍后...',
  });
  emits('close', false);
  apiData = { ...apiData, ...bodyData };

  let apiFun = csLoadPrice.doBatchLoadingPrice;
  if (
    store.type !== 'ys' &&
    (props.isOriginalFlag || store.currentTreeInfo.levelType === 1)
  ) {
    apiFun = jsApi.loadingPriceOriginal;
    const { num = null } = store.currentStageInfo || {};
    const {
      key,
      defaultFeeFlag: { frequencyList },
    } = store.asideMenuCurrentInfo || {};
    Object.assign(apiData, {
      priceType: props.priceType,
      num: frequencyList && frequencyList.length ? num : null,
      kind: Number(key),
    });
  }
  console.log(JSON.parse(JSON.stringify(apiData)), '批量载价传参');
  apiFun(JSON.parse(JSON.stringify(apiData)))
    .then(res => {
      if (res.status === 200) {
        console.log(res, '批量载价返回值');
        let propsData = {
          priorityData: priorityData,
          priorty: priorty,
          rcjList: res.result,
          nextType: '载价编辑',
        };
        setTimeout(() => {
          propsFun(propsData);
          // store.SET_GLOBAL_LOADING(false);
        }, 200);
      } else {
        message.error('暂无可载价的人材机数据!');
      }
    })
    .finally(() => {
      // store.SET_GLOBAL_LOADING(false);
      store.SET_GLOBAL_LOADING({
        loading: false,
        info: '价格数据查询匹配中，请稍后...',
      });
    });
};
const isNextLoad = data => {
  let flag = true;
  data.forEach(item => {
    item &&
      item.forEach(a => {
        for (let key in a) {
          if (key === 'null' || !a[key]) {
            flag = false;
          }
        }
      });
  });
  return flag;
};
watch([() => store.currentTreeInfo, () => store.tabSelectName], value => {});
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  .priority {
    height: 30px;
    // width: 70%;
    width: 600px;
  }
  .periodicalList {
    width: 100%;
    height: calc(100% - 100px);

    display: flex;
    justify-content: space-between;
    position: relative;
    border: 1px solid #e0e0e0;
    padding: 0 14px;
    margin-top: 17px;
    .periodcal {
      width: 31%;
      padding-right: 10px;
      position: relative;
      .title {
        width: 100%;
        display: inline-block;
        border-bottom: 1px solid #d9d9d9;
        margin-top: 10px;
        padding-bottom: 10px;
        color: #4786ff;
        font-size: 14px;
      }

      .selectList {
        :deep(.ant-select .ant-select-selector) {
          border-radius: 8px;
        }
        margin-top: 20px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .delete {
          font-size: 15px;
          margin-top: 8px;
        }
      }
      .add {
        margin-left: 70px;
      }
    }
    .periodcal:nth-of-type(1):after,
    .periodcal:nth-of-type(2):after {
      content: '';
      width: 3px;
      height: 280px;
      right: -15px;
      bottom: 0px;
      position: absolute;
      background: linear-gradient(
        180deg,
        rgba(66, 158, 252, 0) 0%,
        rgba(58, 148, 251, 0.55) 28%,
        #348cfb 51%,
        rgba(46, 133, 250, 0.55) 73%,
        rgba(40, 124, 250, 0) 100%
      );
      opacity: 0.52;
    }
  }

  .footer {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    :deep(.ant-checkbox-inner) {
      border-radius: 50% !important;
    }
    p {
      color: red;
      margin-top: 10px;
    }
  }
}
</style>
