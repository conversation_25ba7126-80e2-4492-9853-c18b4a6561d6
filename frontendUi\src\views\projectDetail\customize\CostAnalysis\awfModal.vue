<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: renmingming
 * @LastEditTime: 2024-04-28 17:31:50
-->
<template>
  <div class="table-content">
    <div class="content">
      <vxe-table
        ref="fixAwfTable"
        border="full"
        height="380"
        align="center"
        :loading="loading"
        show-overflow="tooltip"
        :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: false,
          showIcon: true,
          expandAll: true,
        }"
        :data="tableData"
        :row-class-name="
          ({ row }) => {
            return `level-${row.levelType}`;
          }
        "
      >
        <vxe-column
          min-width="80"
          tree-node
          field="dispNo"
        ></vxe-column>
        <vxe-column
          field="name"
          title="名称"
          min-width="80"
          show-overflow="ellipsis"
        >
        </vxe-column>
        <vxe-column
          min-width="170"
          field="total"
          title="安全生产、文明施工费（不含税）"
        ></vxe-column>
        <vxe-column
          min-width="170"
          field="hstotal"
          title="安全生产、文明施工费（含税）"
        >
        </vxe-column>
      </vxe-table>
      <div class="footer">
        <p class="setting">
          <span>
            设置固定安文费（含税）<a-input
              v-model:value="securityFee"
              @blur="securityFeeBlur"
              placeholder="支持两位小数"
            />
          </span>
          <a-button
            type="primary"
            :disabled="!securityFee"
            @click="submit()"
          >提交</a-button>
        </p>
        <p style="margin: 0">
          <span><icon-font
              type="icon-querenshanchu"
              class="iconFont"
              style="margin-right: 5px"
            ></icon-font>仅沧州地区可能需要配置固定安文费业务，应用后需清空当前项目所有已计取的安文费项，请先备份数据以防数据丢失</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import infoMode from '@/plugins/infoMode.js';
import { pureNumber } from '@/utils/index';
import api from '@/api/projectDetail.js';
import { message } from 'ant-design-vue';
import { emit } from 'process';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const emits = defineEmits(['successCallback']);
const store = projectDetailStore();
let tableData = ref([]);
let fixAwfTable = ref();
let securityFee = ref(''); //输入的固定安文费
onMounted(() => {
  getTableData();
});
const securityFeeBlur = () => {
  let val = pureNumber(securityFee.value, 2);
  securityFee.value = val === '0' ? '' : val;
};
const submit = () => {
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '执行固定安文费会将相关数据清空，该操作不可逆，请先备份',
    confirmText: '备份',
    confirm: () => {
      saveData();
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
  // emits('submitData');
};

const getTableData = () => {
  const params = {
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  api.queryAllProjectSecurity(params).then(res => {
    console.log('---------固定安文费列表传参', params, res);
    if (res.status === 200) {
      const { datas, unitProjects } = res.result;
      tableData.value = datas || [];
      securityFee.value = unitProjects === undefined ? '' : unitProjects;
    }
  });
};
const saveData = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    securityFee: securityFee.value,
    unitList: JSON.parse(JSON.stringify(tableData.value)),
  };
  console.log('固定安文费修改接口传参', apiData);
  api.updateAllProjectSecurity(apiData).then(res => {
    console.log(res);
    const { status } = res;
    if (status === 200) {
      message.success('操作成功');
      emits('successCallback');
    }
  });
};
</script>
<style lang="scss" scoped>
.table-content {
  margin: 0;
  .footer {
    .setting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 14px 0 12px 0;
      span {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 250px;
        :deep(.ant-input) {
          width: 100px;
          font-size: 12px;
          border: none;
          height: 30px;
          border: 1px solid rgba($color: #000000, $alpha: 0.15);
        }
      }
    }
  }

  :deep(.vxe-table) {
    .level-1 {
      background-color: #d7d7d7;
    }
    .level-2 {
      background-color: #ececec;
    }
    .level-3 {
      background-color: #e9eefa;
    }
    .ant-select-selector {
      border: none !important;
      background-color: transparent !important;
    }
    .vxe-body--column,
    .vxe-header--column,
    .vxe-footer--column {
      background-image: linear-gradient(#b9b9b9, #b9b9b9),
        linear-gradient(#b9b9b9, #b9b9b9);
    }
    .row--current {
      background-color: #a6c3fa;
      .ant-select,
      .ant-select-selection-placeholder,
      .ant-select-arrow {
        color: var(--primary-color);
      }
    }
  }
}
</style>
