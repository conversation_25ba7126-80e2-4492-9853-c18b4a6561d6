/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-06-09 10:33:58
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-07-15 14:23:04
 */

import { ref, nextTick } from 'vue';

const clickTimer = ref(null);
const iframeRefs = {};

export const useCellClick = (
  { rowKey, tableData, currentInfo, vexTableRef, componentId, currentChange } = {
    rowKey: 'sequenceNbr',
  }
) => {
  console.log('进入hooks');
  let currentCellData = ref({});
  let beforeCellEditStatus = ref(false);
  // 编辑触发之前勾子
  const cellBeforeEditMethod = () => {
    return beforeCellEditStatus.value;
  };
  // 是否选中
  const isSelectedCell = ({ $columnIndex, column, row }) => {
    if (!column?.editRender) return false;
    const { columnIndex: cIndex, rowId } = currentCellData.value;
    return row[rowKey] === rowId && $columnIndex === cIndex;
  };
  // 重置当前单元格数据
  const resetCellData = () => {
    currentCellData.value = {};
    document.removeEventListener('click', handleClickOutside);
  };
  const selectedClassName = ({ column, row, $columnIndex }) => {
    return isSelectedCell({ $columnIndex, row, column }) ? 'cell-selected' : '';
    // return ''
  };
  let lastClickTime = new Date().getTime();
  // 单击  clickEditFields点击一次触发编辑字段
  const useCellClickEvent = (
    cellData,
    callback = null,
    clickEditFields = []
  ) => {
    console.log('触发了点击');
    // debugger
    // console.log(cellData)
    const { $table, $columnIndex, row, $event, column } = cellData;
    cellData.dbClickTime = new Date().getTime() - lastClickTime;
    lastClickTime = new Date().getTime();
    if (
      (typeof callback === 'function' && !callback(cellData)) ||
      !column?.editRender
    ) {
      $table.clearEdit();
      currentCellData.value = {};
      beforeCellEditStatus.value = false;
      return;
    }
    const rowid = row[rowKey];
    // if (!cellData.row.height) {
    cellData.row.height = $event.target.closest('tr').clientHeight - 14;
    // }
    // 编辑字段执行底下
    let { columnIndex, clickNum, rowId } = currentCellData.value;
    const isSame = $columnIndex === columnIndex && rowId === rowid;
    if (!isSame) $table.clearEdit();
    currentCellData.value = {
      columnIndex: $columnIndex,
      rowId: rowid,
      clickNum: columnIndex === undefined || !isSame ? 1 : clickNum + 1,
    };
    beforeCellEditStatus.value =
      currentCellData.value.clickNum >= 2 ||
      clickEditFields.includes(column.property);
    runClickEvent();
  };
  // 双击清除
  const useCellDBLClickEvent = (cellData, callback) => {
    // $table.clearEdit();
    callback(cellData);
  };

  const handleClickOutside = event => {
    if (Object.keys(currentCellData.value).length) {
      console.log('点击body');
      resetCellData();
    }
  };
  let isRunEvent = false;
  const runClickEvent = () => {
    if (isRunEvent) return;
    isRunEvent = true;
    document.addEventListener('click', handleClickOutside);
    const tableAll = document.querySelectorAll('.table-edit-common');
    tableAll.forEach(table => {
      const bodyTables = table.querySelectorAll('.vxe-table--body');
      bodyTables.forEach(body => {
        body.addEventListener(
          'click',
          e => {
            e.stopPropagation();
          },
          false
        );
      });
    });
  };
  const resetIsRunEvent = () => {
    isRunEvent = false;
  };
  const getCellElement = type => {
    return document.querySelector(
      type === 'edit' ? '.col--edit.cell-selected' : '.col--selected'
    );
  };
  //  获取当前选中的列
  const getSelectedColumn = () => {
    let cell = vexTableRef.value.getSelectedCell();
   //独立费单独处理
    if (cell) return cell.column;

    const cellSel = getCellElement('edit');
    return cellSel
      ? vexTableRef.value.getColumnNode(cellSel)?.item
      : vexTableRef.value.getCurrentColumn();
  };

  const useKeyDownEvent = (event, config = {}) => {
    const {
      enterEnable = true,
      arrowEnable = true,
      deleteEnable = true,
      deleteCallback = () => {},
      delay = 20,
    } = config;
    const sTable = event.$table;
    const tableList = sTable.getTableData()?.visibleData;
    const { code } = event.$event;
    const row = sTable.getCurrentRecord();
    let rowIndex = sTable.getRowIndex(row);
    if (rowIndex == -1) {
      //查不到，树状结构数据
      if (tableList) {
        rowIndex = tableList.findIndex(
          item => item.sequenceNbr == row.sequenceNbr
        );
      }
    }
    const rowIsEdit = sTable.isEditByRow(row);
    // 移动到指定单元格
    const moveToCell = (targetRow, targetRowIndex, targetCol) => {
      sTable.clearEdit();
      sTable.clearSelected();
      sTable.setCurrentRow(targetRow);
      currentInfo.value = targetRow;
      sTable.setSelectCell(targetRow, targetCol);
      const cellClick = () => {
        // if (targetRowIndex === tableList.length - 1) return;
        const colSel = getCellElement('select');
        if (colSel) {
          colSel.click();
          return;
        }

        const cellSel = getCellElement('edit');
        if (!cellSel) return;

        const colIndex = cellSel.cellIndex;
        const currentRow = cellSel.parentElement;
        const nextRow = currentRow.nextElementSibling;

        if (nextRow && colIndex < nextRow.cells.length) {
          nextRow.cells[colIndex]?.click();
        }
      };
      if (componentId === 'IndependentFee') {
        // 独立费单独处理
        setTimeout(cellClick, delay ?? 20);
      } else {
        nextTick(cellClick);
      }
    };
    // 查找并且点击相邻单元格
    const findAndClickAdjacentCell = direction => {
      setTimeout(() => {
        const colSel = getCellElement('select');
          if (colSel) {
            colSel.click();
            return;
          }

        const cellSel = getCellElement('edit');
        if (!cellSel) return;

        const adjacentCell =
          direction === 'left'
            ? cellSel.previousElementSibling
            : cellSel.nextElementSibling;

        adjacentCell?.click();
      }, 0);
    };

    // 回车键处理逻辑
    const handleEnter = () => {
      const selColumn = getSelectedColumn();
      const nextRowIndex =
        rowIndex === tableList.length - 1 ? rowIndex : rowIndex + 1;
      const nextRow = tableList[nextRowIndex];
      if(!nextRow) {
        return
      }
      
      if (rowIsEdit) {
        moveToCell(nextRow, nextRowIndex, selColumn);
        if(currentChange && typeof currentChange == 'function'){
          currentChange({row: tableList?.[nextRowIndex]});
        }
      } else {
        try {
          const cellSel = getCellElement('edit');
          // 处理人材机汇总名称列回车光标不显示
          sTable.setEditCell(tableList[rowIndex], selColumn);
          cellSel?.click();

          setTimeout(() => {
            const activeSel = document.querySelector('.col--active');
            const focusInput = activeSel?.querySelector('.vxe-input--inner');
            if (!focusInput) {
              moveToCell(nextRow, nextRowIndex, selColumn);
              if(currentChange && typeof currentChange == 'function'){
                currentChange({row: tableList?.[nextRowIndex]});
              }
            }
          }, 50);
        } catch (e) {
          moveToCell(nextRow, nextRowIndex, selColumn);
        }
      }
    };

    // 方向键处理
    const handleArrow = direction => {
      const selColumn = getSelectedColumn();
      if(!selColumn){
        console.log('还是没有选中列')
        arrowSelectCell();
        return
      }
      if (rowIsEdit) return;

      switch (direction) {
        case 'up':
          if (rowIndex === 0) return;
          const prevRow = tableList[rowIndex - 1];
          if(!prevRow) {
            return
          }
          sTable.setCurrentRow(prevRow);
          currentInfo.value = prevRow;
          if(currentChange && typeof currentChange == 'function'){
            currentChange({row: currentInfo.value});
          }
          setTimeout(() => clickTargetCell('previousElementSibling'), 0);
          break;

        case 'down':
          if (rowIndex === tableList.length - 1) return;
          const nextRow = tableList[rowIndex + 1];
          if(!nextRow) {
            return
          }
          sTable.setCurrentRow(nextRow);
          currentInfo.value = nextRow;
          if(currentChange && typeof currentChange == 'function'){
            currentChange({row: currentInfo.value});
          }
          setTimeout(() => clickTargetCell('nextElementSibling'), 0);
          break;

        case 'left':
        case 'right':
          findAndClickAdjacentCell(direction);
          break;
      }
    };

    // 点击目标单元格
    const clickTargetCell = siblingType => {
      // 独立费特殊处理
        const cellSel = getCellElement('select');
        if (cellSel) {
          cellSel.click();
          return;
        }
      // 兼容写法
      const currentCell = getCellElement('edit');
      if (!currentCell) return;

      const colIndex = currentCell.cellIndex;
      const currentRow = currentCell.parentElement;
      const targetRow = currentRow[siblingType];

      if (targetRow && colIndex < targetRow.cells.length) {
        targetRow.cells[colIndex]?.click();
      }
    };

    // 主事件分发
    switch (code) {
      case 'Enter':
      case 'NumpadEnter':
        if (enterEnable) handleEnter();
        break;

      case 'Delete':
        if (!rowIsEdit && deleteEnable) deleteCallback(row);
        break;

      case 'ArrowUp':
        if (arrowEnable) handleArrow('up');
        break;

      case 'ArrowDown':
        if (arrowEnable) handleArrow('down');
        break;

      case 'ArrowLeft':
        if (arrowEnable) handleArrow('left');
        break;

      case 'ArrowRight':
        if (arrowEnable) handleArrow('right');
        break;
    }
  };
  // 如果只选中行未选中单元格，回车时换行
  function enterToNextLine() {
    const tableList = vexTableRef.value?.getTableData()?.visibleData;
    const row = vexTableRef.value?.getCurrentRecord();
    let rowIndex = vexTableRef.value?.getRowIndex(row);
    if (rowIndex >=0 ) {
      const nextRowIndex = rowIndex + 1;
      if (tableList?.[nextRowIndex]) {
        vexTableRef.value?.setCurrentRow(tableList?.[nextRowIndex]);
        if(currentChange && typeof currentChange == 'function'){
          currentChange({row: tableList?.[nextRowIndex]});
        }
      }
    }
  }
  // 只选中行，未选中单元格，方向键选中单元格
  function arrowSelectCell() {
    const row = vexTableRef.value?.getCurrentRecord();
    let rowIndex = vexTableRef.value?.getRowIndex(row);
    const tableList = vexTableRef.value?.getTableData()?.visibleData;
    let targetCellSelector = `.table-edit-common tbody tr:nth-child(${rowIndex + 1}) td:nth-child(2)`
    const targetTd = document.querySelector(targetCellSelector);
    if (targetTd) {
      targetTd.dispatchEvent(new MouseEvent('mousedown', { bubbles: true, window: true, cancelable: true }));
      targetTd.dispatchEvent(new MouseEvent('mouseup', { bubbles: true, window: true, cancelable: true }));
    }
  }
  const NAVIGATION_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
  const handleKeyDownCustom = (
    event,
    {
      targetCellSelector = '.table-edit-common tbody tr:first-child td:nth-child(1)',
    } = {}
  ) => {
    const hasCellSelected =
      document.querySelector('.cell-selected') ||
      document.querySelector('.col--selected');
    if (hasCellSelected) {
      return;
    }
    if (NAVIGATION_KEYS.includes(event.code)) {
      arrowSelectCell();
    }
    if (['NumpadEnter', 'Enter'].includes(event.code)) {
      enterToNextLine();
    }
  };
  return {
    cellBeforeEditMethod,
    useCellClickEvent,
    useCellDBLClickEvent,
    selectedClassName,
    resetCellData,
    isSelectedCell,
    resetIsRunEvent,
    currentCellData,
    useKeyDownEvent,
    getSelectedColumn,
    handleKeyDownCustom,
    getCellElement
  };
};

// 单击
export const useCellClickEvent = (cellData, vexTable, key, callback) => {
  if (!vexTable) return;
  if (!callback(cellData)) return;
  callback(cellData);
  clearTimeout(clickTimer.value);
  clickTimer.value = null;

  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }

  clickTimer.value = setTimeout(() => {
    const $table = vexTable;
    if ($table) {
      $table.setEditCell(cellData.row, cellData.column);
    }
  }, 200);
};

// 双击清除
export const useCellDBLClickEvent = (cellData, vexTable, key, callback) => {
  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }
  clearTimeout(clickTimer.value);
  clickTimer.value = null;

  const $table = vexTable;
  $table.clearEdit();
  callback(cellData);
};

// 单击 清除
export const useCellClearClickEvent = (cellData, vexTable, key) => {
  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }
  clearTimeout(clickTimer.value);
  clickTimer.value = null;
  const $table = iframeRefs[key];
  const isEditByRow = $table.isEditByRow(cellData.row);
  if (!isEditByRow) {
    $table.clearEdit();
  }
};

// 双击编辑
export const useCellEditDBLClickEvent = ({ row, column }, vexTable, key) => {
  clearTimeout(clickTimer.value);
  clickTimer.value = null;
  if (!iframeRefs[key]) {
    iframeRefs[key] = vexTable;
  }

  clickTimer.value = setTimeout(() => {
    const $table = iframeRefs[key];
    if ($table) {
      $table.setEditCell(row, column);
    }
  }, 200);
};
