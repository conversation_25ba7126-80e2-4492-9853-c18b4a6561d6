<!--
 * @Descripttion:其他项目子页面表格
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-06 14:44:47
-->
<template>
  <vxe-grid ref="childTable" v-bind="gridOptions" v-on="gridEvents">
    <template #dispNo_edit="{ row }">
      <vxe-input
        :clearable="false"
        v-model.trim="row.dispNo"
        type="text"
        @blur="clear()"
        @keyup="row.dispNo = sortAndlength(row.dispNo, 10)"
      ></vxe-input>
    </template>
    <template #unit_edit="{ row }">
      <vxeTableEditSelect
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :filedValue="row.unit"
        :list="projectStore.unitListString"
        @update:filedValue="
          newValue => {
            saveCustomInput(newValue, row, 'unit', $rowIndex);
          }
        "
      ></vxeTableEditSelect>
      <span v-else>
        {{ row.unit }}
      </span>
    </template>
    <template #type_edit="{ row, $rowIndex }">
      <vxeTableEditSelect
        :filedValue="row.type"
        :list="typeList"
        @update:filedValue="
          newValue => {
            saveCustomInput(newValue, row, 'type', $rowIndex);
          }
        "
      ></vxeTableEditSelect>
    </template>
    <template #name_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.name"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #project_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.project"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #qzspRelyOn_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.qzspRelyOn"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #worksName_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.worksName"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #spec_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.specification"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>
        {{ row.specification }}
      </span>
    </template>
    <template #fxName_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.fxName"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #content_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.content"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #xmje_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        v-model.trim="row.xmje"
        :maxlength="10"
        type="text"
        @blur="(row.xmje = pureNumber(row.xmje, 2)), clear()"
      ></vxe-input>
      <span v-else>
        {{ row.xmje }}
      </span>
    </template>
    <template #quant_edit="{ row }">
      <vxe-input
        v-if="hasDataType() && row.dataType === 2"
        :clearable="false"
        v-model.trim="row.quantitativeExpression"
        :maxlength="1000"
        type="text"
        @blur="clear()"
        @keyup="
          row.quantitativeExpression = row.quantitativeExpression.replace(
            /[^\d.\-\+\*\/\{\}\[\]\(\)]/g,
            ''
          )
        "
      ></vxe-input>
      <span v-else>
        {{ row.quantitativeExpression }}
      </span>
    </template>
    <!-- <template #quantity_edit="{ row }">
      <vxe-input
        v-if="hasDataType() && row.dataType === 2"
        :clearable="false"
        v-model.trim="row.tentativeQuantity"
        :maxlength="10"
        type="text"
        @blur="(row.tentativeQuantity = pureNumber(row.tentativeQuantity, 6)), clear()"
      ></vxe-input>
      <span v-else>
        {{ row.tentativeQuantity }}
      </span>
    </template> -->
    <template #service_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.serviceContent"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>
        {{ row.serviceContent }}
      </span>
    </template>
    <template #dec_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.description"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #amount_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        v-model.trim="row.amount"
        :maxlength="10"
        type="text"
        @blur="(row.amount = pureNumber(row.amount, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.amount }}
      </span>
    </template>
    <template #zhPrice_edit="{ row }">
      <vxe-input
        :clearable="false"
        v-model.trim="row.zhPrice"
        :maxlength="10"
        type="text"
        @blur="(row.zhPrice = pureNumber(row.zhPrice, 2)), clear()"
      >
      </vxe-input>
    </template>
    <template #rate_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        v-model.trim="row.rate"
        :maxlength="10"
        type="text"
        @blur="(row.rate = pureNumber(row.rate, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.rate }}
      </span>
    </template>
    <template #tentativeQuantity_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        v-model.trim="row.tentativeQuantity"
        :maxlength="10"
        type="text"
        @blur="(row.tentativeQuantity = pureNumber(row.tentativeQuantity, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.tentativeQuantity }}
      </span>
    </template>
    <template #price_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        v-model.trim="row.price"
        :maxlength="10"
        type="text"
        @blur="(row.price = pureNumber(row.price, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.price }}
      </span>
    </template>
    <template #taxRemoval_edit="{ row }">
      <vxe-input
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :clearable="false"
        v-model.trim="row.taxRemoval"
        :maxlength="10"
        type="text"
        @blur="(row.taxRemoval = pureNumber(row.taxRemoval, 4)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.taxRemoval }}
      </span>
    </template>
    <template #empty>
      <span
        style="color: #898989; font-size: 14px; display: block; margin: 25px 0"
      >
        <img :src="getUrl('newCsProject/none.png')" />
      </span>
    </template>
  </vxe-grid>
</template>
<script setup>
import {
  onMounted,
  onUpdated,
  onActivated,
  ref,
  watch,
  reactive,
  getCurrentInstance,
  nextTick,
  toRaw,
} from 'vue';
import { projectDetailStore } from '../../../../store/projectDetail';
import csProject from '../../../../api/csProject';
import qtxmCommon from './qtxmCommon';
import { message, Modal } from 'ant-design-vue';
import { insetBus } from '@/hooks/insetBus';
import { useCellClick } from '@/hooks/useCellClick';
import { useOtherProRaction } from '@/hooks/useOtherProRaction';
let $table; //全局定义
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
import {
  getUrl,
  pureNumber,
  sortAndlength,
  isNumericExpression,
} from '@/utils/index';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
let copyInfo = ref(''); //复制数据行
let childTable = ref();
let deleteInfo = ref();
let insertType = ref('');
const showColumnList = ['taxRemoval', 'jxTotal', 'csPrice', 'csTotal'];
const props = defineProps(['pageType', 'columnList']);
const {
  getOperateParams,
  copyOperate,
  disposeCurrentIndex,
  operateParams,
  getCurrentIndex,
  msgInfo,
  deleteOperate,
  deleteModal,
  menuConfigOptions,
  pasteIsDisabled,
  editCheckLength,
  setInsertType,
  getChildPageData,
  getOperateData,
  hasDataType,
  tabBtnIsValid,
  isCurrent,
} = useOtherProRaction({ pageType: props.pageType });
const clear = () => {
  //清除编辑状态
  $table.clearEdit();
};
const typeList = ref(['现场签证', '索赔']);
const getTableData = async (sequenceNbr = '') => {
  loading.value = true;
  let resData = await getChildPageData(); //获取子页面表格数据
  console.log('resData1111111111', resData);
  if (resData.status === 200) {
    loading.value = false;
    if (props.pageType === 'zlje') {
      resData.result &&
        resData.result.map(i => {
          i.originalAmount = i.amount;
          i.originalPrice = i.price;
        });
    }
    if (props.pageType === 'zygczgj') {
      resData.result &&
        resData.result.map(i => {
          i.total = i.total ? i.total : 0;
          i.originalAmount = i.amount;
          i.originalPrice = i.price;
        });
    }
    if (props.pageType === 'zcbfwf') {
      resData.result &&
        resData.result.map(i => {
          i.originalXmje = i.xmje;
          i.originalRate = i.rate;
        });
    }
    if (props.pageType === 'jrg') {
      resData.result &&
        resData.result.map(i => {
          i.originalPrice = i.price;
        });
    }
    tableData.value = resData.result;
    gridOptions.value.data = resData.result;
    if (tableData.value && tableData.value.length > 0) {
      if (sequenceNbr) {
        getCurrentIndex(tableData.value, { sequenceNbr });
      }
      isCurrent.value
        ? $table.setCurrentRow(tableData.value[isCurrent.value])
        : $table.setCurrentRow(tableData.value[0]);
    }
    setInsertType($table);
  } else {
    tableData.value = [];
    gridOptions.value.data = [];
  }
  console.log('获取其他项目子页面表格数据', tableData.value);
  if (projectStore.deStandardReleaseYear === '22') {
    showColumnList.map(item => {
      // 22定额标准隐藏 除税系数，进项合计，除税单价，除税合计列
      $table.hideColumn(item);
    });
  }
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
};
const editClosedEvent = ({ row, column }) => {
  const field = column.field;
  let value = row[field];
  editCheckLength(row, column, $table);
  if (
    field === 'quantitativeExpression' &&
    row.quantitativeExpression &&
    row.quantitativeExpression.length < 1000
  ) {
    const [isSuccess, msg] = isNumericExpression(row.quantitativeExpression);
    if (isSuccess) {
      $table.revertData(row, 'quantitativeExpression');
      message.warn('数量表达式输入非法，请重新输入');
      return;
    }
  }
  if (
    Number(row[field]) ===
    Number(row[`original${field.charAt(0).toUpperCase()}${field.slice(1)}`])
  ) {
    $table.revertData(row);
    return;
  }
  // 判断单元格值没有修改
  console.log(
    '$table.isUpdateByRow(row, field)',
    $table.isUpdateByRow(row, field)
  );
  if ($table.isUpdateByRow(row, field)) {
    operate('update', row);
  }
};
const menuConfig = reactive({
  ...menuConfigOptions,
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    $table.setCurrentRow(row);
    pasteIsDisabled(row);
    if (hasDataType()) {
      insertType.value = row.dataType;
      projectStore.SET_DATATYPE(insertType.value);
    }
    return true;
  },
});
const contextMenuClickEvent = ({ menu, row }) => {
  menu.code === 'delete'
    ? getCurrentIndex(tableData.value)
    : getCurrentIndex(tableData.value, row);
  switch (menu.code) {
    case 'copy':
      // 复制
      copyOperate(row, $table);
      message.success('复制成功');
      break;
    case 'delete':
      // 删除
      if (row.jinzhiFlag) menuConfig.body.options[0][3].disabled = true;
      menuConfig.body.options[0][2].disabled = true;
      deleteOperate(row, $table);
      break;
    case 'paste':
      // 粘贴
      row = { ...projectStore.otherProCopyInfo.copyInfo };
      operate('paste', row);
      break;
    case 'add':
      // 插入
      operate('insert', row);
      break;
    case 'addNull':
      // 插入数据行
      operate('insertData', row);
      break;
    case 'addTitle':
      // 插入标题行
      operate('insertTitle', row);
      break;
  }
};
const gridOptions = ref({
  align: 'center',
  loading: loading.value,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  data: tableData.value,
  height: 'auto',
  menuConfig: menuConfig,
  keepSource: true,
  class: 'table-edit-common',
  cellClassName: selectedClassName,
  editConfig: {
    trigger: 'click',
    mode: 'cell',
    beforeEditMethod: cellBeforeEditMethod,
  },
  columns: props.columnList,
});
const currentChange = ({ row }) => {
  if (hasDataType()) {
    insertType.value = row.dataType;
    projectStore.SET_DATATYPE(insertType.value);
    getCurrentIndex(tableData.value, row);
  }
};
const gridEvents = ref({
  menuClick: contextMenuClickEvent,
  editClosed: editClosedEvent,
  cellClick: useCellClickEvent,
  currentChange: currentChange,
});
const operate = async (type, row) => {
  console.log('operate', type);
  getOperateParams(type, row, $table, tableData.value);
  let apiData = toRaw(operateParams.value);
  let resData = await getOperateData(apiData);
  if (resData.status === 200) {
    isCurrent.value = disposeCurrentIndex.value;
    getTableData();
    message.success(`${msgInfo.value}成功`);
    if (type === 'update') {
      getCurrentIndex(tableData.value, row);
    }
  }
};

watch(
  () => [projectStore.asideMenuCurrentInfo, projectStore.currentTreeInfo],
  () => {
    console.log('*****************其他项目子页面', props.pageType);
    if (projectStore.tabSelectName !== '其他项目') return;
    console.log('999999999999999', qtxmCommon.isOtherProChild(props.pageType));
    if (qtxmCommon.isOtherProChild(props.pageType)) {
      getTableData();
      insertOperate();
    }
  }
);

watch(
  () => deleteModal.value.isDelete,
  () => {
    if (projectStore.tabSelectName !== '其他项目') return;
    if (
      qtxmCommon.isOtherProChild(props.pageType) &&
      deleteModal.value.isDelete
    ) {
      operate('delete', deleteModal.value.deleteRow);
    }
  }
);
// onActivated(() => {

// });
const insertOperate = () => {
  insetBus(
    bus,
    projectStore.componentId,
    qtxmCommon.getComId(props.pageType),
    async data => {
      if (data.name === 'insert') {
        operate('insert', {});
      }
      if (data.name === 'insert-op') {
        if (!data.activeKind) {
          tabBtnIsValid(data.options);
        } else {
          data.activeKind === '01'
            ? operate('insertData')
            : operate('insertTitle');
        }
      }
      if (data.name === 'feeExcel') message.info('功能建设中...');
    }
  );
};

onMounted(() => {
  window.addEventListener('keydown', keyDownOperate);
  nextTick(() => {
    getTableData();
    $table = childTable.value;
    insertOperate();
    projectStore[qtxmCommon.getStoreName(props.pageType)] = {
      posRow
    }
  });
});
const keyDownOperate = event => {
  let select = $table && $table.getCurrentRecord();
  if (
    !select ||
    !qtxmCommon.isOtherProChild(props.pageType) ||
    projectStore.tabSelectName !== '其他项目'
  )
    return;
  let selectRow = $table.getCurrentRecord();
  if (event.ctrlKey && event.code == 'KeyC') {
    copyOperate(selectRow, $table);
    message.success('复制成功');
  }
  if (event.ctrlKey && event.code == 'KeyV') {
    // console.log(
    //   'asideTitle',
    //   projectStore.otherProCopyInfo.asideTitle,
    //   props.pageType
    // );
    //判断存储数据是否是当前页面可粘贴数据
    if (projectStore.otherProCopyInfo.asideTitle !== props.pageType) return;
    if (hasDataType()) {
      //有标题行和数据行的表格
      let copyDataType =
        projectStore.otherProCopyInfo &&
        projectStore.otherProCopyInfo.copyInfo &&
        projectStore.otherProCopyInfo.copyInfo[0]?.dataType;
      let flag = true;
      if (
        !projectStore.otherProCopyInfo ||
        (copyDataType === 1 && projectStore.dataType === 2)
      ) {
        flag = false;
      }
      if (flag) {
        operate('paste', projectStore.otherProCopyInfo.copyInfo);
      } else {
        message.info('选中行不可粘贴');
      }
    } else {
      getCurrentIndex(tableData.value, selectRow);
      let row = { ...projectStore.otherProCopyInfo.copyInfo };
      row.unitId = projectStore.currentTreeInfo?.id;
      operate('paste', row);
    }
  }
};

// 定位方法
const posRow = sequenceNbr => {
  getTableData(sequenceNbr);
};

defineExpose({
  posRow,
});
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
