/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-06-20 19:30:43
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-23 14:39:23
 */

import jsApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { updateOperateByName } from '@/views/projectDetail/customize/operate';

/**
 * renderedList 虚拟渲染数据
 * tableData 全量数据
 * @param {*} param0 
 * @returns 
 */
export const commonJieSuan = ({ refresh, renderedList, tableData }) => {
  const projectStore = projectDetailStore();

  /**
   * 量差比例颜色样式
   * @param {*} colorType
   */
  const quantityDifferenceProportionStyle = colorType => {
    if (!colorType) return {};
    const colors = {
      1: 'red',
      2: 'green',
    };
    return {
      color: colors[colorType],
    };
  };
  /**
   * 原始数据类名
   * @param {*} row
   */
  const originalDataClass = row => {
    return projectStore.currentTreeInfo?.originalFlag && !row.originalFlag
      ? 'original-data-bg'
      : '';
  };

  /**
   * 原始数据加kind判断
   * @param {*} param0
   * @returns
   */
  const originalFlagDisabled = ({ row, kind, originalFlag }) => {
    if (originalFlag && row.kind === kind) return false;
    return true;
  };
  /**
   * 工程量表达式显示处理
   * @param {} row 
   * @returns 
   */
  const quantityExpressionTextHandler = (row) => {
    if (projectStore.currentTreeInfo?.originalFlag && row.originalFlag && row.kind === '04') return '';
    return !row.bdCode && row.kind === '04' ? 0 : row.quantityExpression
                  
  }
  /**
   * 分期编辑限制
   * @param {*} param0
   */
  const stageEditIntercept = ({ row, column }) => {
    // 分期时，不可新增、删除、编辑分部、清单、定额、人材机；
    // 清单的结算工程量和工程量表达式可编辑（选择“按分期比例输入”情况下
    // 1按分期比例输入 2按量
    if ([1,2].includes(row.stageType)) {
      if (row.stageType === 1 && row.kind === '03' && ['quantity', 'quantityExpression'].includes(column.field)) {
        return false;
      }
      return true
    }
    return false;
  };
  /**
   * 锁定价格编辑限制
   */
  const lockPriceFlagEditIntercept = ({ row, column }) => {
    if (row.lockPriceFlag) {
      // 清单只能编辑
      if (
        row.kind === '03' &&
        !['quantityExpression', 'quantity', 'description'].includes(
          column.field
        )
      )
        return true;
      // 定额只能编辑备注
      if (row.kind === '04' && column.field !== 'description') return true;
    }
    if (row.kind === '04') {
      // 锁定清单下的定额只能编辑备注
      if (row.deLockPriceFlag && column.field !== 'description') return true;
    }
    return false;
  };

  /**
   * 原始数据相关编辑拦截
   */
  const originalEditIntercept = ({ row, column }) => {
    // 合同内&原始数据&不是结算相关字段得不可以编辑
    if (!row.originalFlag || !projectStore.currentTreeInfo?.originalFlag)
      return false;
    // 原始定额、分部只能编辑备注
    if (
      ['04', '01', '0', '02'].includes(row.kind) &&
      column.field !== 'description'
    )
      return true;
    // 原始清单只能编辑备注、工程量表达式、结算工程量
    if (
      row.kind === '03' &&
      !['quantity', 'quantityExpression', 'description'].includes(column.field)
    )
      return true;
    return false;
  };
  /**
   * 数据相关编辑拦截
   */
  const csxmOriginalEditIntercept = ({ row, column }) => {
    if (!projectStore.currentTreeInfo?.originalFlag) return false;
    // 原始清单只能编辑备注、结算方式
    if (
      row.kind === '03' &&
      ['settlementMethodValue', 'description'].includes(column.field)
    ) {
      if (row.zjcsClassCode === '0' && 'settlementMethodValue' === column.field)
        return true;
      return false;
    }

    if (row.settlementMethodValue === 3) {
      // zjcsClassCode: '0'安文费 没有值单价措施，有值不等于总价措施
      // 结算方式为实际发生 // 总价措施、单价措施结算工程量可编辑
      if (row.zjcsClassCode !== '0' && ['quantity'].includes(column.field))
        return false;
      if (
        row.zjcsClassCode === '0' &&
        ['settlementMethodValue'].includes(column.field)
      )
        return true;
    }
    if (row.settlementMethodValue === 1) {
      //可调措施，其他总价措施项目 清单调整系数、结算工程量可编辑
      // 单价措施项目  调整系数、结算工程量可编辑
      if (
        row.zjcsClassCode !== '0' &&
        ['quantity', 'adjustmentCoefficient'].includes(column.field)
      )
        return false;
      if (!row.zjcsClassCode && ['quantity'].includes(column.field))
        return false;
    }
    return true;
  };

  // 设置操作菜单栏结算方式
  const setMenuSettlementMethodValue = row => {
    if (!row.settlementMethodValue) return;
    console.log('row.settlementMethodValue', row.settlementMethodValue);
    updateOperateByName('settlementWay', info => {
      info.value = row.settlementMethodValue;
    });
  };
  /**
   * 选中批量设置结算方式
   * @param {*} selectData 
   * @param {*} activeKind 
   * @returns 
   */
  const settlementWayHandler = (selectData, activeKind) => {
  // 选中批量设置结算方式的清单数据
  // zjcsClassCode: '0'安文费 没有值单价措施，有值不等于总价措施
    const selectSettlementWayQDData = selectData?.data?.filter(
      item => item.kind === '03' && item.zjcsClassCode !== '0'
    ) || []
    if (!selectSettlementWayQDData.length) {
      return message.error('请选择清单数据');
    }
    const params = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      qdList: JSON.parse(JSON.stringify(selectSettlementWayQDData)),
      settlementMethodValue: Number(activeKind),
    };
    console.log('批量设置结算方式参数', params);
    jsApi.batchSettlementMethodUpdate(params).then(res => {
      if (res.status === 200 && res.result) {
        message.success('批量设置结算方式工程');
        refresh();
      }
    });
  };
  const isCanStageAdjustment = () => {
    // 结算方式实际发生3、可调措施1
    // 并且总价措施、单价措施下可应用分期调整
    // zjcsClassCode: '0'安文费 没有值单价措施，有值不等于总价措施
    // return (
    //   [1, 3].includes(currentInfo.value.settlementMethodValue) &&
    //   currentInfo.value.zjcsClassCode !== '0'
    // );
  };
  /**
   * 插入或者替换接口调用之后参数
   * @param {*} addDataSequenceNbr 
   */
  const updateNewAddHtDataColl = async(addDataSequenceNbr) => {
    const type = projectStore.componentId === 'subItemProject' ? 1 : 2;
    const updateNewAddParams = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      id: addDataSequenceNbr,
      type,
    };
    console.log('插入或者替换接口调用之后参数', updateNewAddParams)
    try {
      await jsApi.updateNewAddHtDataColl(updateNewAddParams);
    } catch (error) {}
  }

  /**
   * 措施项目菜单拦截
   */
  const contractOriginalFlagMenuHandler = (item, currentInfo) => {
    if (projectStore.currentTreeInfo?.originalFlag) {
      if (['paste', 'delete', 'lock', 'add', 'copy'].includes(item.code))
        item.disabled = true;
      if (item.children) {
        item.children.forEach(childItem => {
          childItem.disabled = true;
        });
      }
    }
    if (item.code === 'cancelAssociationContracts') {
      item.disabled = !currentInfo?.relatedContractsQd?.name;
    }
  }

    /**
   * 右键菜单区分合同内外
   */
  const originalFlagMenuConfigHandler = (menuConfig) => {
    
    let list = [
      {
        code: 'tempDelete',
        name: '临时删除',
        visible: true,
        disabled: false,
      },
      {
        code: 'noteList',
        name: '批注',
        visible: true,
        disabled: false,
        children: [
          {
            code: 'add-note',
            name: '插入批注',
            type: 1,
            visible: true,
            disabled: false,
          },
          {
            code: 'edit-note',
            name: '编辑批注',
            type: 2,
            visible: true,
            disabled: false,
          },
          {
            code: 'del-note',
            name: '删除批注',
            type: 3,
            visible: true,
            disabled: false,
          },
          {
            code: 'show-note',
            name: '显示批注',
            type: 4,
            visible: true,
            disabled: false,
          },
          {
            code: 'hide-note',
            name: '隐藏批注',
            type: 5,
            visible: true,
            disabled: false,
          },
          {
            code: 'del-all-note',
            name: '删除所有批注',
            type: 6,
            visible: true,
            disabled: false,
          },
        ],
      },
      {
        code: 'batchDelete',
        name: '批量删除',
        visible: true,
        disabled: false,
        children: [
          {
            code: 'batchDelete-child1',
            name: '批量删除所有临时删除项',
            type: 1,
            visible: true,
            disabled: false,
          },
          {
            code: 'batchDelete-child2',
            name: '批量删除所有工程量为0项',
            type: 2,
            visible: true,
            disabled: false,
          },
        ],
      },
      {
        code: 'associationContracts',
        name: '关联合同清单',
        visible: true,
        disabled: false,
      },
      {
        code: 'cancelAssociationContracts',
        name: '取消关联合同清单',
        visible: true,
        disabled: false,
      },
      {
        code: 'accordingDocument',
        name: '依据文件',
        visible: true,
        disabled: false,
      },
    ];
    if (projectStore.componentId !== 'subItemProject') {
      list = list.slice(0, -1)
    }
    if (projectStore.currentTreeInfo?.originalFlag) {
      menuConfig.body.options[0] = menuConfig.body.options[0].filter(item => {
        return !list.map(item => item.code).includes(item.code);
      });
    } else {
      const options = menuConfig.body.options[0];
      if (options.find(item => item.code === 'associationContracts')) return;
      menuConfig.body.options[0] = [
        ...options.slice(0, -1),
        ...list,
        ...options.slice(-1),
      ];
    }
  };

  const isHasFB = () => {
    return tableData.value.length > 1 &&
    ['01', '02'].includes(tableData.value[1].kind); // 是否有分部
  }
  
  const contractOriginalFlagMenuInsert = (children, currentInfo) => {
    children.forEach(childItem => {
      if (
        (projectStore.currentTreeInfo?.originalFlag &&
          currentInfo.originalFlag &&
          ((!fbHasSubFb(currentInfo) && childItem.kind === '02') ||
            (['03', '04'].includes(currentInfo.kind) &&
              childItem.kind === '04'))) ||
        projectStore.stageCount
      ) {
        // 合同内&原始数据&不能添加子分部
        childItem.disabled = true;
        childItem.isValid = false; // 顶部操作拦菜单字段
      }
      if (projectStore.currentTreeInfo?.originalFlag && (!isHasFB() && ['01', '02'].includes(childItem.kind))) {
        // 合同内没有分部时，不能添加分部和子分部
        childItem.disabled = true;
        childItem.isValid = false; // 顶部操作拦菜单字段
      }
      if ((currentInfo.lockPriceFlag || currentInfo.deLockPriceFlag) && childItem.kind === '04') {
        // 锁定综合单价不可添加定额
        childItem.disabled = true;
        childItem.isValid = false; // 顶部操作拦菜单字段
      }
    });
  }
  
  /**
   * 分部分项合同内原始数据||分期针对于插入等菜单做限制
   * @param {*} children
   */
  const contractOriginalFlagInsert = (item, currentInfo) => {
    contractOriginalFlagMenuInsert(item?.children || [], currentInfo)
    // if (item.code === 'delete') {
    //   debugger
    // }
    // 是分期时
    if (projectStore.stageCount && item.code !== 'pageColumnSetting') {
      console.log(item);
      item.disabled = true;
    }
    if (
      projectStore.currentTreeInfo?.originalFlag &&
      currentInfo.originalFlag &&
      ['delete', 'lock'].includes(item.code)
    ) {
      item.disabled = true;
    }
    if(projectStore.currentTreeInfo?.originalFlag && ['tempDelete', 'noteList', 'batchDelete'].includes(item.code)) {
      item.disabled = true;
      if (item.children && item.children.length) {
        item.children.forEach(childItem => {
          childItem.disabled = true;
        })
      }
    }
    if ((currentInfo.lockPriceFlag || currentInfo.deLockPriceFlag) && ['delete'].includes(item.code)) {
      // 锁定综合单价不可删除
      item.disabled = true;
    }
    if (item.code === 'cancelAssociationContracts') {
      item.disabled = !currentInfo?.relatedContractsQd?.name;
    }
  };

  /**
   * 当前分部行下是否存在子分部
   * @param {*} row
   */
  const fbHasSubFb = (row) => {
    if (
      !['01', '02'].includes(row.kind) ||
      (row.children && !row.children.length)
    )
      return false;
    const nextRow = getRowById(row.children[0]);
    return ['01', '02'].includes(nextRow.kind);
  };

  const getRowById = (id) => {
    return renderedList.value?.find(item => item.sequenceNbr === id)
  }
  // 关联依据
  let accordingDocumentVisible = ref(false);
  let associationContractVisible = ref(false); // 关联合同清单
  const jieSuanBusHandler = (data) => {
    if (data.name === 'accordingDocument') {
      if (projectStore.subCurrentInfo.kind !== '03') {
        message.warning('请选择清单数据')
        return;
      }
      accordingDocumentVisible.value = true
    }
    if (data.name === 'associationContracts') {
      associationContractVisible.value = true;
    }
  }
  const jieSuanMenuClickEvent = (menu) => {
    if (menu.code === 'accordingDocument') {
      // 分部分项 
      accordingDocumentVisible.value = true;
    }
    if (menu.code === 'associationContracts') {
      associationContractVisible.value = true;
    }
    if (menu.code === 'cancelAssociationContracts') {
      cancelAssociation()
    }
  }
  
/**
 * 取消关联
 */
const cancelAssociation = () => {
  const param = {
    bizType: projectStore.componentId === 'subItemProject' ? 'fbfx' : 'csxm',
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: projectStore.subCurrentInfo?.sequenceNbr,
    relatedConstructId: '',
    relatedSingleId: '',
    relatedUnitId: '',
    relatedQdId: '',
  };
  console.log(param);
  jsApi.saveRelatedQd(param).then(res => {
    message.success('取消成功');
    refresh();
  });
};
  return {
    originalFlagDisabled,
    originalDataClass,
    quantityDifferenceProportionStyle,
    originalEditIntercept,
    lockPriceFlagEditIntercept,
    stageEditIntercept,
    csxmOriginalEditIntercept,
    setMenuSettlementMethodValue,
    settlementWayHandler,
    updateNewAddHtDataColl,
    originalFlagMenuConfigHandler,
    accordingDocumentVisible,
    associationContractVisible,
    contractOriginalFlagInsert,
    contractOriginalFlagMenuInsert,
    jieSuanMenuClickEvent,
    jieSuanBusHandler,
    contractOriginalFlagMenuHandler,
    quantityExpressionTextHandler,
  };
};
