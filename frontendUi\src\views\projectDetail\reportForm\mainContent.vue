<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: sunchen
 * @LastEditTime: 2024-11-25 18:04:38
-->
<template>
  <tab-menu @menuClick="menuClick"> </tab-menu>
  <div class="main-content">
    <sub-aside-tree
      ref="subAsideTreeRef"
      @onPreview="Preview"
      @onSelectReport="onSelectReport"
    ></sub-aside-tree>
    <div class="content" ref="content" @contextmenu.prevent>
      <div class="iframe-bar-list">
        <div
          class="bar-items"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
          @click="editLandScape"
        >
          <icon-font type="icon-geshitiaozheng" class="icon" />
          <span class="add-name">横版/竖版</span>
        </div>
        <div
          class="bar-items"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
          @click="editTable"
        >
          <icon-font type="icon-biaogesheji" class="icon" />
          <span class="add-name">临时编辑</span>
        </div>
        <div
          class="bar-items"
          v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)"
          @click="editReport"
        >
          <icon-font type="icon-biaogesheji" class="icon" />
          <span class="add-name">表格设计</span>
        </div>
        <div class="bar-items"  @click="saveReportTemplate"  v-if="!(!globalData.headLine || !globalData.excelDataTemplate?.id)">
          <icon-font type="icon-baocunbaobiao" class="icon"/>
          <span class="add-name">保存报表</span>
        </div>
        <div class="bar-items" @click="loadReportTemplate" >
          <icon-font type="icon-daorubaobiao" class="icon"/>
          <span class="add-name">导入报表</span>
        </div>
        <div class="bar-items" v-if="false"  @click="print">
          <icon-font type="icon-dayin" class="icon"/>
          <span class="add-name">打印</span>
        </div>
        <div
          class="bar-items"
          @click="setWeater"
          v-if="!(!globalData.headLine && !lanMuName.value)"
        >
          <icon-font type="icon-shuiyin" class="icon" />
          <span class="add-name">水印</span>
        </div>
        <div
          class="bar-items"
          @click="outPdf"
          v-if="!(!globalData.headLine && !lanMuName.value)"
        >
          <icon-font type="icon-daochuPDF" class="icon" />
          <span class="add-name">导出当前PDF</span>
        </div>
      </div>
      <div class="iframe-content">
        <img
          src="@/assets/img/data-null.png"
          alt=""
          class="noData"
          v-if="!PreviewData"
        />
        <iframe
          v-if="fileUrl && PreviewData"
          id="myIframe"
          ref="iframeRef"
          @load="passDataToIframe"
          :src="fileUrl"
          style="width: 100%; height: 100%; border: 2px solid #e8e8e7"
        />
      </div>
    </div>
  </div>
  <export-file ref="exportFileRef" @handleOk="start"></export-file>
  <schedule-file
    ref="scheduleFileRef"
    v-model:dialogVisible="showSchedule"
    strokeColor="#54a1f3"
    :percent="percent"
    :desc="showSchedule ? '正在导出，请稍后' : ''"
  ></schedule-file>

  <common-modal
    v-model:modelValue="isShowXML"
    className="dialog-comm"
    title="导出xml类型"
    width="auto"
  >
    <div class="radio-list">
      <div class="type-box">
        <div class="radio-title">
          <icon-font type="icon-zhaobiaoleixing" class="icon" />
          <span>招标类型</span>
        </div>
        <a-radio-group v-model:value="xmlType">
          <a-radio :style="radioStyle" :value="0">招标文件</a-radio>
          <a-radio :style="radioStyle" :value="1">投标文件</a-radio>
          <a-radio :style="radioStyle" :value="2">招标控制价文件</a-radio>
        </a-radio-group>
      </div>

      <div class="type-box">
        <div class="radio-title">
          <icon-font type="icon-changjia" class="icon" />
          <span>厂家</span>
        </div>
        <a-radio-group v-model:value="xmlCjType">
          <a-radio :style="radioStyle" :value="0">招标通</a-radio>
        </a-radio-group>
      </div>
    </div>
    <div class="footer-btn-list">
      <a-button @click="CancelDialog">取消</a-button>
      <a-button
        type="primary"
        :disabled="![0, 1, 2].includes(xmlType) || ExportXmlLoading"
        @click="selfCheckDialog()"
        >确定</a-button
      >
    </div>
  </common-modal>

  <!-- 选择模板： -->
  <selectReport
    ref="selectReportRef"
    @handleOk="handleSelectReport"
  ></selectReport>

  <!-- 临时编辑的窗口 -->
  <common-modal
    v-model:modelValue="editExcelaStatus"
    className="dialog-comm dialog-EditExcelDialog"
    title="临时编辑"
    :width="800"
    :height="600"
    show-zoom="true"
    @close="closeEditExcelDialog"
  >
    <div style="display: none">临时编辑</div>
    <editExcel
      :data="excelData"
      v-if="editExcelDomStatus"
      teleportTo=".dialog-EditExcelDialog .ant-spin-container"
      :lanMuName="lanMuName"
    ></editExcel>
  </common-modal>

  <editExcel
    :data="excelData"
    :lanMuName="lanMuName"
    v-if="editExcelStatus"
    @getData="getData"
    @onClose="onClose"
  ></editExcel>

  <zb-info v-model:infoVisible="infoVisible" :xmlType="xmlType" @successHandle="saveExportXml"></zb-info>

</template>

<script setup>
import editExcel from '@/components/editExcel.vue';
import selectReport from './selectReport.vue';
import {
  defineAsyncComponent,
  watch,
  ref,
  markRaw,
  onMounted,
  watchEffect,
  reactive,
  nextTick,
} from 'vue';
import tabMenu from './tabMenu.vue';
import csProject from '@/api/csProject';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import { downloadFileByUrl, delay } from '@/utils/index';
import { workBookData } from '@/components/reportTemplate/index.js';
import { globalData, getLocalStorage } from './reportFrom';
import xeUtils from 'xe-utils';
import { projectDetailStore } from '@/store/projectDetail';

import shApiProject from '@/api/shApi';

const store = projectDetailStore();
const route = useRoute();
let exportFileRef = ref(null); // 导出文件
let showSchedule = ref(false); // 进度文件
let percent = ref(0); // 进度
let PreviewData = ref(false); // 预览数据
const fileUrl = ref();
const useType = ref(); // 弹窗里面选择导出的类型
let timer = ref(null);
let exportType = ref('excel'); // 点击导出类型
let isShowXML = ref(false);
const xmlType = ref(-1);
let xmlCjType = ref(0);
const radioStyle = reactive({
  fontSize: '14px',
});
const props = defineProps({
  biddingType: {
    type: String,
    default: '',
  },
});
const ExportXmlLoading = ref(false);

const subAsideTree = defineAsyncComponent(() => import('./subAsideTree.vue'));
const exportFile = defineAsyncComponent(() => import('./exportFile.vue'));
const scheduleFile = defineAsyncComponent(() =>
  import('@/components/schedule/schedule.vue')
);

let excelData = ref([]);
let lanMuName = ref('');
let infoVisible = ref(false); // 导出xml信息弹框是否展示

watch(() => store.reportNextOption, (value) => {
  console.log('下一步操作，为啥没动静。', value)
  if (value) {
    isShowXML.value = true;
    store.SET_REPORT_NEXT_OPTION(false);
  }
})

onMounted(() => {
  if (store.reportNextOption) {
    isShowXML.value = true;
    store.SET_REPORT_NEXT_OPTION(false);
  }
})
// 选择报表
const handleSelectReport = ({ label, data }) => {
  // let [name,levelType,lanMuName,deType] = selectName.split('|')
  editExcelStatus.value = true;
  globalData.openEdit = true;
  let workData = xeUtils.clone(data, true);
  excelData.value = { ...workData, headLine: '', updateName: label };
};

/**
 * 开启导出进度条
 */
const start = ({ use_type, export_type }) => {
  useType.value = use_type;
  exportType.value = export_type;
  exportFileRef.value?.cancel();
  ProjectBar();
  timer.value = setInterval(() => {
    ProjectBar();
  }, 4000);
  showSchedule.value = true;
};

/**
 * 点击预览
 * @param {*} hasData 是否有报表数据
 */
const Preview = hasData => {
  fileUrl.value = null;
  PreviewData.value = hasData;
  nextTick(() => {
    fileUrl.value = `/pdf/index.html`;
  });
};

/**
 * 点击打开导出文件弹窗
 * @param {*} type
 */
const menuClick = type => {
  if (type === 'xml') {
    exportXml();
    return;
  }
  exportFileRef.value?.open(type);
};

const CancelDialog = () => {
  xmlType.value = -1;
  isShowXML.value = false;
};

const selfCheckDialog = () => {
  infoVisible.value = true;
}

const saveExportXml = async () => {
  if (ExportXmlLoading.value) return;
  ExportXmlLoading.value = true;
  try {
    const postData = {
      constructId: route.query.constructSequenceNbr,
      type: xmlType.value,
      vender: xmlCjType.value,
    };
    const res = await csProject.generateXml(postData);
    ExportXmlLoading.value = false;
    if (res?.result) {
      message.success('导出成功！');
      CancelDialog();
    }
  } catch (error) {
    console.error(error);
  } finally {
    ExportXmlLoading.value = false;
  }
};

// 点击了导出xml
const exportXml = () => {
  // isShowXML.value = true;
  infoMode.show({
    iconType: 'icon-tishineirong',
    infoText: '是否进行项目自检？',
    descText: '请注意：导出xml建议进行项目自检，避免出现不必要的错误',
    descTextStyle: { color: 'red' },
    confirm: () => {
      infoMode.hide();
      store.SET_CHECK_VISIBLE(true);
    },
    close: () => {
      infoMode.hide();
      isShowXML.value = true;
      // saveExportXml();
    },
  });
};

/**
 * 进度条
 */
const ProjectBar = async () => {
  try {
    const res = await csProject.exportProjectBar(
      route.query.constructSequenceNbr,
      useType.value,
      exportType.value
    );
    const { cloudStoragePath, progressBar, progressBarUnit, responseCode } =
      res.result;
    if (responseCode !== 200) {
      showToast();
      clearTimer();
      return;
    }

    percent.value = progressBar;

    if (progressBar === 100) {
      message.success('导出成功！');
      downloadFileByUrl(cloudStoragePath);
      delay(1000).then(() => {
        clearTimer();
      });
    }
  } catch (error) {
    clearTimer();
  }
};

const clearTimer = () => {
  clearInterval(timer.value);
  timer.value = null;
  percent.value = 0;
  showSchedule.value = false;
};

/**
 * 导出报表，报错，提示信息
 */
const showToast = () => {
  infoMode.show({
    infoText: '导出生成报表异常 请重新选择后导出',
    isSureModal: true,
    iconType: 'icon-qiangtixing',
    confirm: () => {
      console.log('确认事件触发');
      showSchedule.value = false;
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};

let editExcelStatus = ref(false);

let selectReportRef = ref(null);
const onSelectReport = ({ isOpen, lanMuName: name }) => {
  lanMuName.value = name;
  excelData.value.headLine = '';
  if (isOpen) {
    selectReportRef.value.open({
      lanMuName: lanMuName.value,
    });
  }
};

let subAsideTreeRef = ref(null);
const getData = data => {
  console.log('data', data);
  onClose();
  subAsideTreeRef.value.getTreeList(data.headLine || 'last');
};

const onClose = () => {
  editExcelStatus.value = false;
  globalData.openEdit = false;
};

// 编辑表格模板
const editReport = () => {
  editExcelStatus.value = true;
  excelData.value = {
    ...xeUtils.clone(globalData.excelDataTemplate, true),
    headLine: globalData.headLine,
  };
  globalData.openEdit = true;
};

const generateRandomString = () => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

let editExcelaStatus = ref(false); // 临时编辑的窗口
let editExcelDomStatus = ref(false);
const editTable = () => {
  let data = getLocalStorage();
  let screenPPI = 110;
  let A4Height = (29.6 / 2.54) * 72;
  let rowCount = data?.rows.length;
  let rowData = {
    ...data?.rows.map(i => {
      i.h = 11.69 * screenPPI * i.ratio;
      // i.ia = true
      return i;
    }),
  };
  let columnData = {
    ...data?.columns.map(i => {
      i.w = i.width;
      return i;
    }),
  };
  let mergeData = data?.merges.map(i => {
    i.startRow = i.firstRow;
    i.endRow = i.lastRow;
    i.startColumn = i.firstCol;
    i.endColumn = i.lastCol;
    return i;
  });

  // 处理样式集合，默认加上自动换行
  Object.values(data.styles).forEach(i => {
    i.tb = 3;
  });

  let cellDataMap = new Map();
  data?.rows.forEach((item, k) => {
    let rowData = data?.cells
      .filter(i => {
        return i.rowIndex == k;
      })
      .map(j => {
        return {
          custom: { ...j },
          v: j.content?.richText ? j.content.richText : j.content,
          t: 1,
          s: j.styleId,
        };
      });

    // rowData.forEach(i=>{
    //   let styleId = generateRandomString()
    //   i.s = styleId
    //   styles[styleId] = {
    //     ff:'宋体'
    //   }
    // })
    cellDataMap.set(k, { ...rowData });
  });
  let cellData = {};
  Array.from(cellDataMap).forEach(e => {
    cellData[e[0]] = e[1];
  });
  let workdata = {
    name: '表结构转换',
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      openType: 'editTable',
      appVersion: '0.2.6',
      locale: 'zhCN',
      styles: { ...data.styles },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData,
          name: 'Sheet1',
          hidden: 0,
          rowCount,
          columnCount: data?.columns.length,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 24,
          mergeData,
          rowData,
          columnData,
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      resources: [],
    },
  };

  // editExcelStatus.value = true;
  excelData.value = { ...workdata.data, headLine: globalData.headLine };
  // globalData.openEdit = true
  editExcelaStatus.value = true;

  nextTick(() => {
    editExcelDomStatus.value = true;
  });
};
const closeEditExcelDialog = () => {
  editExcelaStatus.value = false;
  editExcelDomStatus.value = false;
};

const editLandScape = () => {
  let landSpace = getLocalStorage()?.print?.landSpace;
  let postData = {
    ...globalData.treeParams,
    ...globalData.treeParams.constructObj,
    headLine: globalData.headLine,
    lanMuName: lanMuName.value,
    landScape: !landSpace,
  };
  delete postData.constructObj;
  console.log('🚀 ~ editLandScape ~ postData:', postData);

  csProject.landScapeChange(postData).then(res => {
    subAsideTreeRef.value.getTreeList();
  });
};

let iframeRef = ref(null);
let setWeaterStatus = ref(false);
const setWeater = () => {
  setWeaterStatus.value = !setWeaterStatus.value;
  iframePostMessage(setWeaterStatus.value);
};

// 导出PDF
const outPdf = async () => {
  const list = await csProject.exportProjectTree(
    route.query.constructSequenceNbr,
    lanMuName.value,
    'pdf'
  );
  removeExcelDataTemplate([list.result]);
  const postList = flattenTree([list.result])[0];
  const params = {
    lanMuName: lanMuName.value,
    params: JSON.parse(JSON.stringify(postList)),
    projectName: globalData.headLine,
  };
  console.log('🚀 ~ outPdf ~ params:', params);
  const res = await csProject.exportPdfFile(params);
  console.log('🚀 ~ outPdf ~ res:', res);
  if (res?.result) {
    message.success('导出成功！');
  }
};

const getOutList = () => {
  csProject
    .exportProjectTreeSH(
      route.query.constructSequenceNbr,
      // useType.value,
      'pdf'
    )
    .then(res => {
      console.log('数数据', res.result);
      if (res.status === 200 && res.result) {
        treeData.value = [res.result];
        expandedKeys.value.push(treeData.value[0].id);

        // for (const item of treeData.value[0].childrenList) {
        //   expandedKeys.value.push(item.id)
        // }
      }
    });
};

/**
 * 将树结构里面的某个字段与选中的数据进行关联
 * @param {*} treeList
 */
const flattenTree = treeList => {
  const result = [];
  let id = store.currentTreeInfo?.id;
  if (props.biddingType === 2) {
    id = store.currentTreeInfo?.parentId;
  }
  function traverse(node) {
    result.push(node); // 将当前节点添加到结果数组中
    if (node.childrenList && node.childrenList.length > 0) {
      // 递归处理子节点
      for (let i = 0; i < node.childrenList.length; i++) {
        const data = node.childrenList[i];
        data.selected = data.headLine == globalData.headLine && id == node?.id;
        traverse(data);
      }
    }
  }
  // 遍历树列表中的每个根节点
  for (let i = 0; i < treeList.length; i++) {
    const root = treeList[i];
    root.selected = root.headLine == globalData.headLine;
    traverse(root);
  }

  return result;
};

const removeExcelDataTemplate = tree => {
  // 遍历树的每一个节点
  tree.forEach(node => {
    // 删除当前节点的 excelDataTemplate 属性
    delete node.excelDataTemplate;
    // 如果当前节点有子节点，则递归调用此函数处理子节点
    if (node.childrenList && Array.isArray(node.childrenList)) {
      removeExcelDataTemplate(node.childrenList);
    }
  });
};

// js 清除报表数据里面的
const handlePdfList = newList => {};

let doms = ref(null);
let iframeWindow = ref();
//向子页面iframe传参
const passDataToIframe = event => {
  nextTick(() => {
    const iframeWindow = event.target.contentWindow;
    doms.value = iframeWindow.document.querySelectorAll('.watermark');
    iframePostMessage();
    if (isPrint.value) {
      iframeWindow.value = iframeWindow.printPage();
      isPrint.value = false;
    }
  });
};

// 根据状态，设置dom显示隐藏
const iframePostMessage = () => {
  doms.value.forEach(i => {
    i.style.display = setWeaterStatus.value ? 'block' : 'none';
  });
};

let isPrint = ref(false);
const print = () => {
  isPrint.value = true;
  fileUrl.value = null;
  nextTick(() => {
    fileUrl.value = `/pdf/index.html`;
  });
};


// 保存报表
const saveReportTemplate = () => {
  let itemLevel = ''
  if (store.currentTreeInfo) {
    const { levelType } = store.currentTreeInfo;
    itemLevel = itemLevelList[levelType - 1];
  }
  const postData = {
    jsonData:{
      ...xeUtils.clone(globalData.excelDataTemplate, true),
      headLine: globalData.headLine,
      itemLevel,
    },
    constructName: store.projectName,
  };
  console.log('保存报表参数',postData,)
  csProject.saveReportTemplateToLocal(postData).then(res => {
    if (res?.result) {
      message.success('导出成功！');
    }
  });
};

const itemLevelList = markRaw(['project', 'single', 'unit']);

// 导入报表
const loadReportTemplate = () =>{
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: null,
    unitId: null,
    lanMuName: lanMuName.value,
    itemLevel: '',
    constructName: store.projectName,
  };
  if (store.currentTreeInfo) {
    const { levelType } = store.currentTreeInfo;
    postData.itemLevel = itemLevelList[levelType - 1];

    switch (levelType) {
      case 1:
        postData.constructId = route.query?.constructSequenceNbr;
        postData.singleId = null;
        break;
      case 2:
        postData.singleId = store.currentTreeInfo.id;
        postData.unitId = null;
        break;
      case 3:
        postData.singleId = store.currentTreeInfo?.parentId;
        postData.unitId = store.currentTreeInfo.id;
        break;
    }
  }
  console.log('导入报表参数',postData,)
  csProject.loadReportTemplateFromLocal(postData).then(res => {
    console.log("🚀 ~ csProject.landScapeChange ~ res:", res)
    if(res.status == 200 ){
      if (res?.result) {
        message.success('导入成功！');
        subAsideTreeRef.value.getTreeList();
      }
    }else{
      message.error('导入失败！');
    }
  });
}
</script>
<style lang="scss" scoped>
.main-content {
  display: flex;
  height: calc(100vh - 139px);
}
.content {
  position: relative;
  flex: 1;
  max-height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(214, 214, 214, 1);
  .iframe-bar-list {
    padding: 8px 6px;
    width: 100%;
    height: 37px;
    background: #f8fbff;
    border-bottom: 1px solid rgba(214, 214, 214, 1);
    display: flex;
    align-items: center;
    .bar-items {
      display: flex;
      margin-left: 14px;
      align-items: center;
      cursor: pointer;
      .icon {
        font-size: 16px;
      }
      .add-name {
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        margin-left: 7px;
        &:hover {
          color: #409eff;
        }
      }
    }
  }
  .iframe-content {
    flex: 1;
    flex-shrink: 0;
  }
  .noData {
    position: absolute;
    width: 274px;
    height: auto;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.radio-list {
  width: 600px;
  .type-box {
    background: #ffffff;
    border: 1px solid #d9d9d9;
    margin-bottom: 11px;
    border-radius: 2px;
    padding: 14px 13px;
  }
  .radio-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    span {
      font-weight: 400;
      font-size: 13px;
      color: #287cfa;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
}
</style>
