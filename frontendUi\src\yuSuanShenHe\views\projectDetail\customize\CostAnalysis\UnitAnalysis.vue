<!--
 * @Author: wangru
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-20 15:09:26
-->
<template>
  <vxe-table
    align="center"
    :column-config="{ resizable: true }"
    :row-config="{ isHover: true, isCurrent: true }"
    :data="tableData"
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
      beforeEditMethod({ columnIndex, rowIndex }) {
        if (!cellBeforeEditMethod()) return false;
        if (rowIndex === editIndex && columnIndex === 2) {
          return true;
        }
        return false;
      },
    }"
    ref="unitTable"
    keep-source
    @edit-closed="editClosedEvent"
    height="100%"
    :tree-config="{
      children: 'childrenList',
      line: false,
      expandAll: true,
    }"
    @cell-click="useCellClickEvent"
    :cell-class-name="selectedClassName"
    class="table-edit-common table-content"
  >
    <vxe-column
      field="dispNo"
      width="120"
      title="序号"
      tree-node
    > </vxe-column>
    <vxe-column
      field="name"
      title="名称"
      width="400"
    > </vxe-column>
    <vxe-column
      field="context"
      title="内容"
      width="400"
      :edit-render="{ autofocus: '.vxe-input--inner' }"
    >
      <template #edit="{ row }">
        <vxe-input
          :clearable="false"
          v-model="row.context"
          :maxlength="10"
          @blur="(row.context = pureNumber(row.context, 2)), clear()"
        ></vxe-input>
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { pureNumber } from '@/utils/index';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { comCostAnalysisJs } from '@/views/projectDetail/customize/CostAnalysis/comCostAnalysis';
import { message } from 'ant-design-vue';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: 'dispNo' });
const store = projectDetailStore();
let editIndex = ref(); //设置只有建筑面积内容可编辑-----------单位工程
let tableData = ref([]);
let average = ref();
let unitTable = ref();
let currentChange = ref(null);
const { getPageData, updateOperate } = comCostAnalysisJs({
  tableData,
  unitTable,
  editIndex,
  currentChange,
  average,
});
const clear = () => {
  //清除编辑状态
  const $table = unitTable.value;
  $table.clearEdit();
};
const getTableData = async () => {
  if (!store.currentTreeInfo.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  await getPageData();
};
watch(
  () => store.currentTreeInfo,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo.levelType === 3
    ) {
      getTableData();
    }
  }
);
watch(
  () => store.tabSelectName,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo.levelType === 3
    ) {
      getTableData();
    }
  }
);
onMounted(() => {
  if (
    store.tabSelectName === '造价分析' &&
    store.currentTreeInfo.levelType === 3
  ) {
    getTableData();
  }
});
const upDateAverage = async value => {
  let res = await updateOperate(value);
  if (res.status === 200) {
    message.success('修改成功');
    getTableData();
  }
};
const editClosedEvent = ({ row, column }) => {
  const $table = unitTable.value;
  const field = column.field;
  if (!row[field]) {
    row[field] = '0.00';
  }
  if (Number(row[field]) == Number(average.value)) {
    row[field] = average.value;
    return;
  }
  currentChange.value = row.dispNo;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(row, field)) {
    upDateAverage(row[field]);
  }
};
</script>
<style lang="scss" scoped>
.table-content {
  max-width: 925px;
  overflow-x: scroll;
}
</style>
