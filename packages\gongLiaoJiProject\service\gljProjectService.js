const {Service} = require("../../../core");
const ProjectDomain = require("../domains/ProjectDomain");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ResponseData} = require("../utils/ResponseData");
const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {Snowflake} = require("../utils/Snowflake");
const xeUtils = require("xe-utils");
const WildcardMap = require("../core/container/WildcardMap");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const ProjectModel = require("../domains/projectProcessor/models/ProjectModel");
const LabelConstants = require("../constants/LabelConstants");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const fs = require('fs');
const CommonConstants = require("../constants/CommonConstants");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {NumberUtil} = require("../utils/NumberUtil");

/**
 * 工程项目  service
 */
class GljProjectService extends Service {

    constructor(ctx) {
        super(ctx);
    }


    // 拖拽项目结构(调整位置、调整顺序、批量复制、批量删除)
    async gljDragDropProjectStructure(param) {
        let constructId = param.id;
        let constructTree = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        let oldConstructFlatMap = await this.flatConstructTreeToMapByObj(constructTree);
        // 1. 工程项目处理
        let constructObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        return await this.dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj);
    }

    /**
     * 工程项目重新赋值
     * @param param 前端的树
     * @param oldConstructFlatMap 内存中的数据平铺
     * @param constructObj  内存中的数据
     * @returns {Promise<ResponseData>}
     */
    async dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj) {
        let constructId = constructObj.sequenceNbr;
        let projectTree = ProjectDomain.getDomain(constructId).getProjectTree().filter(o => o.type !== ProjectLevelConstant.construct); //复制前当前所有单位
        // 新增两个bak熟悉用于保存处理后的数据
        constructObj.singleProjectsBak = [];
        constructObj.unitProjectArrayBak = [];

        // 2. 单项工程处理
        if (ObjectUtils.isNotEmpty(param.children)) {
            for (const item of param.children) {
                if (item.type === ProjectLevelConstant.single) {
                    await this._editSingleProjectStructure(param, item, oldConstructFlatMap, constructObj, true, constructObj);
                } else if (item.type === ProjectLevelConstant.unit) {
                    await this._editUnitStructure(param, param.id, item, oldConstructFlatMap, constructObj, true, constructObj);
                }
            }
        } else {
            constructObj.children = [];
        }

        //处理后的数据回填
        if (ObjectUtils.isNotEmpty(constructObj.singleProjectsBak)) {
            constructObj.children = constructObj.singleProjectsBak;
        } else if (ObjectUtils.isNotEmpty(constructObj.unitProjectArrayBak)) {
            constructObj.children = constructObj.unitProjectArrayBak;
        }
        await this.calProjectTree(constructObj);        //编辑机构节点到树结构

        let newProjectIdList = await this.calAllUnitList(constructObj.children, []);

        // if (ObjectUtils.isNotEmpty(projectTree)) {
        //     projectTree = projectTree.sort((a, b) => b.type - a.type);
        // }
        for (let o of projectTree) {
            if (!newProjectIdList.includes(o.sequenceNbr)) {
                //说明旧数据被删除了(单位/单项)
                await ProjectDomain.getDomain(constructId).removeProject(o.sequenceNbr);
            }
        }

        // let projectTreeList = [];        //复制前的单项/单位集合
        // if (ObjectUtils.isNotEmpty(projectTree)) {
        //     projectTree.forEach(p => {
        //         projectTreeList.push(p.sequenceNbr);
        //     });
        // }
        // await this.calProjectTreeNode(constructId, constructObj, projectTreeList);
        // await this.doCostCodeOtherProject(constructId);        //编辑完后刷新费用汇总和建设其他费  该接口是复制出来的项目，不用刷新费用汇总

        delete constructObj.singleProjectsBak;
        return ResponseData.success();
    }


    /**
     * 编辑完后刷新费用汇总和建设其他费
     * @param constructId
     * @returns {Promise<void>}
     */
    async doCostCodeOtherProject(constructId) {
        let projectTree1 = ProjectDomain.getDomain(constructId).getProjectTree().filter(o => o.type === ProjectLevelConstant.unit); //复制后当前所有单位
        if (ObjectUtils.isNotEmpty(projectTree1)) {
            for (let ppp of projectTree1) {
                try {
                    //费用汇总
                    await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                        constructId: constructId,
                        unitId: ppp.sequenceNbr,
                        qfMajorType: ppp.qfMajorType
                    });
                } catch (error) {
                    console.error("捕获到异常:", error);
                }
            }
        }
    }


    async calProjectTree(constructObj) {
        if (ObjectUtils.isNotEmpty(constructObj.children)) {
            let index = 0;
            for (let item of constructObj.children) {
                if (ObjectUtils.isNotEmpty(item.unitProjects)) {
                    item.children = item.unitProjects;
                } else if (ObjectUtils.isNotEmpty(item.subSingleProjects)) {
                    item.children = item.subSingleProjects;
                } else {
                    item.children = [];
                }
                item.index = index++;
                item = await this.calProjectTree(item);
            }
        }
        return constructObj;
    }


    async calProjectTreeNode(constructId, constructObj1, projectTreeList) {
        let constructObj = ConvertUtil.deepCopy(constructObj1);
        if (ObjectUtils.isNotEmpty(constructObj.children)) {
            for (let p of constructObj.children) {
                p.parent = constructObj;
                // ProjectDomain.getDomain(constructId).ctx.treeProject.nodeMap.set(p.sequenceNbr, p); // 将节点添加到节点映射表中
                ProjectDomain.getDomain(constructId).updateProject(p);
                await this.calProjectTreeNode(constructId, p, projectTreeList);
            }
        }
    }


    async calAllUnitList(singleProjectsBak, idList) {
        if (ObjectUtils.isNotEmpty(singleProjectsBak)) {
            for (const p of singleProjectsBak) {
                idList.push(p.sequenceNbr);
                if (ObjectUtils.isNotEmpty(p.children)) {
                    await this.calAllUnitList(p.children, idList);
                }
            }
        }
        return idList;
    }

    async _editSingleProjectStructure(constructParam, singleParam, oldConstructFlatMap, parent, ifParentIsConstruct, parentSame) {
        let oldSingle = oldConstructFlatMap.get(singleParam.id);
        let newSingle = oldSingle;
        let constructId = constructParam.id;

        if (!ObjectUtils.isEmpty(singleParam.copyFromId)) {
            let newSingle = await ConvertUtil.deepCopy(oldSingle);
            newSingle.sequenceNbr = Snowflake.nextId();
            newSingle.parentId = parent.sequenceNbr;
            // newSingle.constructId = constructId;
            newSingle.name = singleParam.name;
            newSingle.scopeFlag = true;

            //复制过来的校验名称是否有重复
            await this.repeatInitSingleName(constructId, ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleParam.id);

            newSingle.childrenCopy = newSingle.children;
            newSingle.children = [];
            //添加单项
            let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newSingle.parentId);
            ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newSingle, parentNode);

            //重新赋值单项下的单位id和单项spId
            await this.repeatInitSingleItemId(constructId, newSingle);
            //重新计算取费表单项的值
            await this.dealSingleQfData(constructId, oldSingle, newSingle);
            if (ifParentIsConstruct) {
                parent.singleProjectsBak.push(newSingle);
            } else {
                parent.subSingleProjectsBak.push(newSingle);
            }
        } else {
            if (ObjectUtils.isEmpty(oldSingle)) {// 单项不存在
                let ProjectModel = {
                    name: singleParam.name,
                    parentId: singleParam.parentId,
                    type: ProjectLevelConstant.single
                };
                let arg = {
                    constructId: constructId,
                    ProjectModel: ProjectModel
                };
                newSingle = await this.addSingleUnit(arg);

                // if (ifParentIsConstruct) { // 新增单项
                //     newSingle = this.service.singleProjectService.addSingleProject(arg, false);
                // } else { // 新增子单项
                //     newSingle = this.service.singleProjectService.addSubSingleProject(arg, false);
                // }
            }

            //拖拽后如果重名前端修改名称
            newSingle.name = singleParam.name;
            newSingle.parentId = parent.sequenceNbr;

            if (ifParentIsConstruct) {
                parent.singleProjectsBak.push(newSingle);
            } else {
                parent.subSingleProjectsBak.push(newSingle);
            }

            // 新增两个bak熟悉用于保存处理后的数据
            newSingle.unitProjectsBak = new Array();
            newSingle.subSingleProjectsBak = new Array();
            // newSingle.projectName = singleParam.name;

            if (ObjectUtils.isNotEmpty(singleParam.children)) {
                for (const item of singleParam.children) {
                    if (item.type === ProjectLevelConstant.single) {
                        await this._editSingleProjectStructure(constructParam, item, oldConstructFlatMap, newSingle, false, parentSame);
                    } else if (item.type === ProjectLevelConstant.unit) {
                        await this._editUnitStructure(constructParam, newSingle.sequenceNbr, item, oldConstructFlatMap, newSingle, false, parentSame);
                    }
                }
            }

            //处理后的数据回填
            newSingle.unitProjects = newSingle.unitProjectsBak;
            newSingle.subSingleProjects = newSingle.subSingleProjectsBak;
            delete newSingle.unitProjectsBak;
            delete newSingle.subSingleProjectsBak;
        }
    }


    async dealSingleQfData(constructId, oldSingle, newSingle) {
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let oldSingleFeeKey = WildcardMap.generateKey(oldSingle.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);
        let oldSingleFeeY = singleQfbMap.get(oldSingleFeeKey);
        let oldSingleFee = await ConvertUtil.deepCopy(oldSingleFeeY);
        oldSingleFee.singleId = newSingle.sequenceNbr;
        let newSingleFeeKey = WildcardMap.generateKey(newSingle.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(newSingleFeeKey, oldSingleFee));

        let singleQfbMap1 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
        let oldSingleFeeKey1 = WildcardMap.generateKey(oldSingle.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);
        let oldSingleFee1Y = singleQfbMap1.get(oldSingleFeeKey1);
        let oldSingleFee1 = await ConvertUtil.deepCopy(oldSingleFee1Y);
        oldSingleFee1.singleId = newSingle.sequenceNbr;
        let newSingleFeeKey1 = WildcardMap.generateKey(newSingle.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FLSM, singleQfbMap1.set(newSingleFeeKey1, oldSingleFee1));

        let itemElementMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
        if (ObjectUtils.isEmpty(itemElementMap)) {
            itemElementMap = new Map();
        } else {
            for (const [key, value] of itemElementMap) {
                if (key.includes(oldSingle.sequenceNbr)) {
                    let newKey = ConvertUtil.deepCopy(key).replace(oldSingle.sequenceNbr, newSingle.sequenceNbr);
                    itemElementMap.set(newKey, value);
                }
            }
            await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMap);
        }
    }

    async addSingleUnit(args) {
        let {ProjectModel: argsModel, constructId} = args;
        argsModel.sequenceNbr = Snowflake.nextId();
        if (ObjectUtil.isEmpty(constructId) && argsModel.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            constructId = argsModel.sequenceNbr;
        }
        console.log("constructId-->:" + constructId + "type--->" + argsModel.type + "id=====》" + argsModel.sequenceNbr);
        let newProject = new ProjectModel(argsModel.sequenceNbr, argsModel.type, null);
        newProject.init(argsModel);
        newProject.type = argsModel.type;
        newProject.parentId = argsModel.parentId;
        this.do4Type(argsModel, newProject);
        await ProjectDomain.getDomain(constructId).createProject(newProject);
        // return ResponseData.success(ProjectDomain.filter4ProjectTree(newProject));
        return newProject;
    }


    async repeatInitSingleName(constructId, ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleId) {
        let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
        if (ifParentIsConstruct) {
            //单项
            // let singleProjects = parent.singleProjects;
            let singleProjects = projectTree.filter(o => o.parentId === parent.sequenceNbr);
            if (ObjectUtils.isNotEmpty(singleProjects)) {
                let singleUnitNameList = singleProjects.map(obj => obj.name);
                let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.name);
                newSingle.name = singleNewName;
            }
        } else {
            //子单项
            // let newVar = oldConstructFlatMap.get(singleId);
            // let subSingleProjects = newVar.subSingleProjects;
            let subSingleProjects = projectTree.filter(o => o.parentId === singleId);
            if (ObjectUtils.isNotEmpty(subSingleProjects)) {
                let singleUnitNameList = subSingleProjects.map(obj => obj.name);
                let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.name);
                newSingle.name = singleNewName;
            }
        }
    }

    //单位和单项本层级有重复名称时重新计算名称
    async repeatInitSingleNameCal(singleUnitNameList, oldName) {
        let newName = oldName;
        if (singleUnitNameList.includes(newName)) {
            newName = newName + "_1";
            while (singleUnitNameList.includes(newName)) {
                let lastIndex = newName.lastIndexOf("_");
                let count = newName.slice(lastIndex + 1);
                // const regex = "^[1-9]*$";
                let number = parseInt(count);
                number = number + 1;
                newName = newName.slice(0, -1) + number;
            }
        }
        return newName;
    }

    async repeatInitSingleItemId(constructId, newSingle) {
        let unitProjects = newSingle.childrenCopy.filter(o => o.type === ProjectLevelConstant.unit);
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            //单项下有单位
            for (let i = 0; i < unitProjects.length; i++) {
                let item = unitProjects[i];
                let oldUnitId = item.sequenceNbr;
                item.parentId = newSingle.sequenceNbr;
                item.sequenceNbr = Snowflake.nextId();
                item.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                item.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //重新赋值分部分项等数据的unitId
                await this.repeatInitUnitItemId(item, oldUnitId, constructId);

                // newSingle.children.push(item);
                //添加单位节点
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(item.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(item, parentNode);
            }
        } else if (ObjectUtils.isNotEmpty(newSingle.subSingleProjects)) {
            //单项下有子单项
            for (let i = 0; i < newSingle.subSingleProjects.length; i++) {
                let item = newSingle.subSingleProjects[i];
                let oldSingle = await ConvertUtil.deepCopy(item);
                item.sequenceNbr = Snowflake.nextId();
                item.parentId = newSingle.sequenceNbr
                item.childrenCopy = item.children;

                //添加单项节点
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(item.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(item, parentNode);

                //重新计算取费表单项的值
                await this.dealSingleQfData(constructId, oldSingle, item);
                await this.repeatInitSingleItemId(constructId, item);
            }
        }
    }

    async repeatInitUnitItemId(newUnit, oldUnitId, constructId) {
        let oldUnit = ProjectDomain.getDomain(constructId).getProjectById(oldUnitId);

        //拷贝预算书定额数据
        let oldIdNewIdMap = await this.repeatInitUnitYssDe(constructId, oldUnit, newUnit);
        //处理措施项目定额数据
        let oldIdNewIdCsxmMap = await this.repeatInitUnitCsxmDe(constructId, oldUnit, newUnit);
        //拷贝预算书人和措施项目材机数据
        let oldIdNewIdRcjMap = await this.repeatInitUnitYssResource(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap);
        //拷贝预算书定额数据下的initChildCodes替换新的人材机id
        await this.repeatInitUnitYssDeInitChildCodes(constructId, newUnit, oldIdNewIdRcjMap);

        //拷贝functionMap数据
        let functionDataMap = ProjectDomain.getDomain(constructId).functionDataMap;

        //复制单位新建局部汇总map
        // let objMap = functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL);
        // if (ObjectUtils.isEmpty(objMap)) {
        //     functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL, new Map());
        // }

        for (let item of functionDataMap) {
            if (ObjectUtils.isNotEmpty(item)) {
                if (item[0] === FunctionTypeConstants.JBXX_KEY) {
                    //基本信息
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            for (let jbxxItem of newVar) {
                                if (jbxxItem.name === "工程名称") {
                                    jbxxItem.remark = newUnit.name;
                                }
                            }
                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_DLF_KEY) {
                    //独立费
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);

                            let oldIdNewIdDlfMap = new Map();
                            for (let item of newVar) {
                                let oldId = item.sequenceNbr;
                                item.sequenceNbr = Snowflake.nextId();
                                oldIdNewIdDlfMap.set(oldId, item.sequenceNbr);
                                if (ObjectUtils.isNotEmpty(item.parentId)) {
                                    item.parentId = oldIdNewIdDlfMap.get(item.parentId);
                                }
                            }

                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_COST_SUMMARY) {
                    //费用汇总
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            await this.copyFunctionDataMap(constructId, newVar);
                            for (let item of newVar) {
                                item.unitId = newUnit.sequenceNbr;
                            }
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_COST_CODE) {
                    //费用代码
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_QFB) {
                    //单位取费表
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            newVar.unitId = newUnit.sequenceNbr;
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).set(keyNew, newVar);


                            //导入单位后同步到单项(这个只是上一层级的单项取费数据)
                            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
                            let freeKeySingle = WildcardMap.generateKey(newUnit.parentId, FunctionTypeConstants.SINGLE_QFB);
                            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": new Map()};
                            if (ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate.get(newVar.qfCode))) {
                                freeRateSingleModel.childFreeRate.set(newVar.qfCode, newVar);
                                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

                                //再迭代处理多层级单项的取费数据
                                let singleProject = ProjectDomain.getDomain(constructId).getProjectById(newUnit.parentId);
                                await this.service.gongLiaoJiProject.gljCommonService.dealLevelSingleFeeImport(constructId, singleProject.parentId, newVar);
                            }

                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_FLSM) {
                    //单位取费表
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            newVar.unitId = newUnit.sequenceNbr;
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.RCJ_MEMORY) {
                    //人材机缓存
                    let itemElementMap = item[1];
                    let oldVar = itemElementMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId + FunctionTypeConstants.SEPARATOR + oldUnitId);
                    if (ObjectUtils.isNotEmpty(oldVar)) {
                        let newVar = await ConvertUtil.deepCopy(oldVar);
                        await this.copyFunctionDataMap(constructId, newVar);
                        let keyNew = FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId + FunctionTypeConstants.SEPARATOR + newUnit.sequenceNbr;
                        for (let itemVar of newVar) {
                            itemVar.unitId = newUnit.sequenceNbr;
                            itemVar.sequenceNbr = Snowflake.nextId();
                            let newVar2 = oldIdNewIdMap.get(itemVar.parentId);
                            let newVar3 = oldIdNewIdRcjMap.get(itemVar.parentId);
                            itemVar.parentId = ObjectUtils.isNotEmpty(newVar2) ? newVar2 : newVar3;
                            itemVar.deRowId = oldIdNewIdMap.get(itemVar.deRowId);
                            itemVar.deId = oldIdNewIdMap.get(itemVar.deId);
                        }
                        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_MEMORY).set(keyNew, newVar);
                    }
                } else if (item[0] === FunctionTypeConstants.PROJECT_USER_RCJ) {
                    //补充人材机编码
                    let itemElementList = item[1];
                    if (ObjectUtils.isNotEmpty(itemElementList)) {
                        let newVar1 = await ConvertUtil.deepCopy(itemElementList);
                        for (let item of newVar1) {
                            item.unitId = newUnit.sequenceNbr;
                            if (oldIdNewIdMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdMap.get(item.deRowId);
                                item.deId = oldIdNewIdMap.get(item.deRowId);
                                item.parentId = oldIdNewIdMap.get(item.deRowId);
                            } else if (oldIdNewIdCsxmMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdCsxmMap.get(item.deRowId);
                                item.deId = oldIdNewIdCsxmMap.get(item.deRowId);
                                item.parentId = oldIdNewIdCsxmMap.get(item.deRowId);
                            }

                            if (oldIdNewIdRcjMap.get(item.sequenceNbr)) {
                                item.sequenceNbr = oldIdNewIdRcjMap.get(item.sequenceNbr);
                            }
                        }
                        itemElementList = itemElementList.concat(newVar1);
                    } else {
                        itemElementList = [];
                    }
                    ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, itemElementList);
                } else if (item[0] === FunctionTypeConstants.UNIT_CONVERSION) {
                    //定额标准换算
                    let itemElementMap = item[1];
                    let oldUnitConversionMap = itemElementMap?.get(oldUnitId);
                    if (ObjectUtils.isNotEmpty(oldUnitConversionMap)) {
                        for (let key of oldUnitConversionMap.keys()) {
                            let newDeId;
                            if (oldIdNewIdMap.has(key)) {
                                newDeId = oldIdNewIdMap.get(key);
                            } else if (oldIdNewIdCsxmMap.has(key)) {
                                newDeId = oldIdNewIdCsxmMap.get(key);
                            }

                            let deConversionMapCopy = ConvertUtil.deepCopy(oldUnitConversionMap.get(key));
                            deConversionMapCopy.constructId = constructId;
                            deConversionMapCopy.unitId = newUnit.sequenceNbr;
                            deConversionMapCopy.deId = newDeId;
                            deConversionMapCopy.sequenceNbr = newDeId;
                            let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
                            let unitConversionMap = conversionMap?.get(newUnit.sequenceNbr);
                            if (ObjectUtils.isEmpty(unitConversionMap)) {
                                let unitConversionMapNew = new Map();
                                unitConversionMapNew.set(newDeId, deConversionMapCopy);
                                conversionMap.set(newUnit.sequenceNbr, unitConversionMapNew);
                            } else {
                                unitConversionMap.set(newDeId, deConversionMapCopy);
                            }
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.YSH_GCL_EXP_NOTIFY) {
                    //定额引用工程规模的数据
                    let itemElementMap = item[1];
                    let oldGCLList = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                    if (ObjectUtils.isNotEmpty(oldGCLList)) {
                        for (let item of oldGCLList) {
                            item.unitId = newUnit.sequenceNbr;
                            if (oldIdNewIdMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdMap.get(item.deRowId);
                            } else if (oldIdNewIdCsxmMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdCsxmMap.get(item.deRowId);
                            }
                        }
                        itemElementMap.set(newUnit.sequenceNbr, oldGCLList);
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY, itemElementMap);
                    }
                } else if (item[0] === FunctionTypeConstants.MAIN_MATERIAL_SETTING) {
                    //单位级别人材机主要材料设置数据
                    let itemElementMap = item[1];
                    let oldMainSettingObj = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                    if (ObjectUtils.isNotEmpty(oldMainSettingObj)) {
                        itemElementMap.set(newUnit.sequenceNbr, oldMainSettingObj);
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, itemElementMap);
                    }
                // } else if(item[0] === FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL){
                //     //单位级别局部汇总
                //     let itemElementMap = item[1];
                //     let oldMainSettingObj = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                //     itemElementMap.set(newUnit.sequenceNbr, oldMainSettingObj);
                //     await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL, itemElementMap);
                // } else if (item[0] === FunctionTypeConstants.RCJ_COLLECT) {
                //     let itemElementMap = item[1];
                //     if (ObjectUtils.isEmpty(itemElementMap)) {
                //         itemElementMap = new Map();
                //     }
                //
                //     for (const [key, value] of itemElementMap) {
                //         if (key.includes(oldUnitId)) {
                //             let newKey = ConvertUtil.deepCopy(key).replace(oldUnitId, newUnit.sequenceNbr);
                //             itemElementMap.set(newKey, value);
                //         }
                //     }
                //     await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMap);
                } else if (item[0] === FunctionTypeConstants.UNIT_DE_CONTENT) {
                    let itemElementMap = item[1];
                    if (itemElementMap.get(oldUnit.sequenceNbr)) {
                        let deXinxiShuomingMap = itemElementMap.get(oldUnit.sequenceNbr);
                        let deXinxiShuomingMapNow = ConvertUtil.deepCopy(deXinxiShuomingMap);
                        for (let key of deXinxiShuomingMapNow.keys()) {
                            if (oldIdNewIdMap.has(key)) {
                                let deXinxi = deXinxiShuomingMapNow.get(key);
                                deXinxi.deId = oldIdNewIdMap.get(key);
                                deXinxiShuomingMapNow.delete(key);
                                deXinxiShuomingMapNow.set(oldIdNewIdMap.get(key), deXinxi);
                            }
                        }
                        itemElementMap.set(newUnit.sequenceNbr, deXinxiShuomingMapNow);
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA) {
                    let itemElementObj = item[1];
                    if (ObjectUtils.isNotEmpty(itemElementObj) && itemElementObj[oldUnitId]) {
                        let itemElementObjElement = itemElementObj[oldUnitId];
                        let itemElementObjElementCopy = ConvertUtil.deepCopy(itemElementObjElement);
                        itemElementObj[newUnit.sequenceNbr] = itemElementObjElementCopy;
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA, itemElementObj);
                    }
                } else if (item[0] === FunctionTypeConstants.RCJ_COLLECT) {
                    //单位人材机汇总排序、涂色
                    let itemElementMap = item[1];
                    if (ObjectUtils.isEmpty(itemElementMap)) {
                        itemElementMap = new Map();
                    }
                    if (ObjectUtils.isNotEmpty(itemElementMap)) {
                        for (const [key, value] of itemElementMap) {
                            if (ObjectUtils.isNotEmpty(value)) {
                                if (value instanceof Map) {
                                    //单位人材机涂色
                                    if (ObjectUtils.isNotEmpty(value.get(oldUnit.sequenceNbr))) {
                                        let unitColorUnit = value.get(oldUnit.sequenceNbr);
                                        let unitColorUnitCopy = ConvertUtil.deepCopy(unitColorUnit);
                                        value.set(newUnit.sequenceNbr, unitColorUnitCopy);
                                    }
                                } else if (value instanceof Array || value instanceof Object) {
                                    if (key.includes(oldUnit.sequenceNbr)) {
                                        //单位人材机排序、表格列设置
                                        let keyUnitNew = key.replace(oldUnit.sequenceNbr, newUnit.sequenceNbr);
                                        let valueUnitCopy = ConvertUtil.deepCopy(value);
                                        itemElementMap.set(keyUnitNew, valueUnitCopy);
                                    }
                                    if (key.includes(oldUnit.parentId)) {
                                        //单项人材机排序、表格列设置
                                        let keySingleNew = key.replace(oldUnit.parentId, newUnit.parentId);
                                        let valueSingleCopy = ConvertUtil.deepCopy(value);
                                        itemElementMap.set(keySingleNew, valueSingleCopy);
                                    }
                                }
                            }
                        }
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMap);
                    }
                } else if (item[0] === FunctionTypeConstants.YSH_TABLELIST || item[0] === FunctionTypeConstants.UNIT_BGLSZ || item[0] === FunctionTypeConstants.DLF_TABLELIST
                    || item[0] === FunctionTypeConstants.SC_TABLELIST) {
                    //表格列设置
                    let itemElementMap = item[1];
                    for (const [key, value] of itemElementMap) {
                        if (key.includes(oldUnit.sequenceNbr)) {
                            let tableListKey = key.replace(oldUnit.sequenceNbr, newUnit.sequenceNbr);
                            itemElementMap.set(tableListKey, ConvertUtil.deepCopy(value));
                            await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.TABLE_SETTING_CACHE, itemElementMap);
                        }
                    }
                }
            }
        }

        //拷贝计取缓存
        await this.repeatInitUnitJiqu(constructId, oldUnit, newUnit);

        //拷贝定额工程量明细
        await this.repeatInitUnitQuantities(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap);
    }


    /**
     * 拷贝预算书定额数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssDe(constructId, oldUnit, newUnit) {
        let oldDeAll = ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.getNodeById(oldUnit.defaultDeId);
        let oldDeAllCopy = await ConvertUtil.deepCopy(oldDeAll);

        oldDeAllCopy.sequenceNbr = newUnit.defaultDeId;
        oldDeAllCopy.deRowId = newUnit.defaultDeId;
        this.updatePropertyValue(oldDeAllCopy, 'unitId', newUnit.sequenceNbr);
        ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.addNode(oldDeAllCopy);
        let oldIdNewIdMap = new Map();
        oldIdNewIdMap = await this.copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap);
        return oldIdNewIdMap;
    }


    /**
     * 拷贝预算书定额数据,定额类型的initChildCodes数据修改id
     * @param constructId
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssDeInitChildCodes(constructId, newUnit, oldIdNewIdRcjMap) {
        let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === newUnit.sequenceNbr && item.type === DeTypeConstants.DE_TYPE_DE);
        let csxmList = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === newUnit.sequenceNbr && item.type === DeTypeConstants.DE_TYPE_DE);

        if (ObjectUtils.isNotEmpty(deList)) {
            for (let item of deList) {
                let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(item.sequenceNbr);
                if (ObjectUtils.isNotEmpty(de.initChildCodes)) {
                    de.initChildCodes.forEach(o => {
                        o.sequenceNbr = oldIdNewIdRcjMap.get(o.sequenceNbr);
                    });
                }
            }
        }
        if (ObjectUtils.isNotEmpty(csxmList)) {
            for (let item of csxmList) {
                let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
                if (ObjectUtils.isNotEmpty(de.initChildCodes)) {
                    de.initChildCodes.forEach(o => {
                        o.sequenceNbr = oldIdNewIdRcjMap.get(o.sequenceNbr);
                    });
                }
            }
        }
    }


    /**
     * 拷贝措施项目定额数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitCsxmDe(constructId, oldUnit, newUnit) {
        let oldDeAll = ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.getNodeById(oldUnit.defaultCsxmId);
        let oldDeAllCopy = await ConvertUtil.deepCopy(oldDeAll);

        oldDeAllCopy.sequenceNbr = newUnit.defaultCsxmId;
        oldDeAllCopy.deRowId = newUnit.defaultCsxmId;
        this.updatePropertyValue(oldDeAllCopy, 'unitId', newUnit.sequenceNbr);
        ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.addNode(oldDeAllCopy);
        let oldIdNewIdMap = new Map();
        oldIdNewIdMap = await this.copyCsxmData(constructId, oldDeAllCopy, oldIdNewIdMap);
        return oldIdNewIdMap;
    }


    /**
     * 拷贝预算书人材机数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssResource(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap) {
        let oldIdNewIdRcjMap = new Map();
        let rcjKey = WildcardMap.generateKey(oldUnit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let item of rcjList) {
                let itemNew = await ConvertUtil.deepCopy(item);
                this.updatePropertyValue(itemNew, 'unitId', newUnit.sequenceNbr);
                let deRowIdOld = itemNew.deRowId;
                if (oldIdNewIdMap.has(deRowIdOld)) {
                    itemNew.deRowId = oldIdNewIdMap.get(deRowIdOld);
                } else if (oldIdNewIdCsxmMap.has(deRowIdOld)) {
                    itemNew.deRowId = oldIdNewIdCsxmMap.get(deRowIdOld);
                }
                itemNew.deId = itemNew.deRowId;
                itemNew.parentId = itemNew.deRowId;
                itemNew.sequenceNbr = Snowflake.nextId();

                //处理人材机单项批注
                if (ObjectUtils.isNotEmpty(itemNew.annotationsSingleObj)) {
                    if (oldUnit.parentId != newUnit.parentId) {
                        let annotationsSingleObjElement = itemNew.annotationsSingleObj[oldUnit.parentId];
                        if (ObjectUtils.isNotEmpty(annotationsSingleObjElement)) {
                            itemNew.annotationsSingleObj[newUnit.parentId] = ConvertUtil.deepCopy(annotationsSingleObjElement);
                        }
                    }
                }

                if (ObjectUtils.isNotEmpty(itemNew.pbs)) {
                    itemNew.pbs.forEach(m => {
                        m.parentId = itemNew.sequenceNbr;
                    });
                }

                oldIdNewIdRcjMap.set(item.sequenceNbr, itemNew.sequenceNbr);
                ProjectDomain.getDomain(constructId).resourceDomain.ctx.resourceMap.set(WildcardMap.generateKey(newUnit.sequenceNbr, itemNew.deRowId, itemNew.sequenceNbr), itemNew);
            }
        }
        return oldIdNewIdRcjMap;
    }


    /**
     * 拷贝计取缓存数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<void>}
     */
    async repeatInitUnitJiqu(constructId, oldUnit, newUnit) {
        let gsCacheOld = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(gsCacheOld)) {
            let gsCacheOldCopy = await ConvertUtil.deepCopy(gsCacheOld);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + newUnit.sequenceNbr, gsCacheOldCopy);
        }
        let ysCacheOld = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(ysCacheOld)) {
            let ysCacheOldCopy = await ConvertUtil.deepCopy(ysCacheOld);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + newUnit.sequenceNbr, ysCacheOldCopy);
        }
    }

    /**
     * 拷贝定额工程量明细
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @param oldIdNewIdMap
     * @returns {Promise<void>}
     */
    async repeatInitUnitQuantities(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap) {
        //处理定额工程量明细
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let oldUnitQuantiesMap = quantitiesMap.get(oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(oldUnitQuantiesMap)) {
            let oldMapCopy = await ConvertUtil.deepCopy(oldUnitQuantiesMap);
            let keysToDelete = [];

            oldMapCopy.forEach((value, oldkey) => {
                if (oldIdNewIdMap.has(oldkey)) {
                    keysToDelete.push(oldkey);
                    let newkey = oldIdNewIdMap.get(oldkey);
                    value.constructId = constructId;
                    value.unitId = newUnit.sequenceNbr;
                    value.quotaListId = newkey;
                    if (ObjectUtils.isNotEmpty(value.quantities)) {
                        value.quantities.forEach(o => {
                            o.quotaListId = newkey;
                        });
                    }
                    oldMapCopy.set(newkey, value);
                } else if (oldIdNewIdCsxmMap.has(oldkey)) {
                    keysToDelete.push(oldkey);
                    let newkey = oldIdNewIdCsxmMap.get(oldkey);
                    value.constructId = constructId;
                    value.unitId = newUnit.sequenceNbr;
                    value.quotaListId = newkey;
                    if (ObjectUtils.isNotEmpty(value.quantities)) {
                        value.quantities.forEach(o => {
                            o.quotaListId = newkey;
                        });
                    }
                    oldMapCopy.set(newkey, value);
                }
            });
            keysToDelete.forEach(key => {
                oldMapCopy.delete(key);
            });
            quantitiesMap.set(newUnit.sequenceNbr, oldMapCopy);
        }
    }


    async copyFunctionDataMap(constructId, obj) {
        if (Array.isArray(obj)) {
            //Array
            if (ObjectUtils.isNotEmpty(obj)) {
                for (let o of obj) {
                    o.prentId = obj.sequenceNbr;
                    o.parentId = obj.sequenceNbr;
                    o = await this.copyFunctionDataMap(constructId, o);
                }
            }
        } else {
            //object
            obj.sequenceNbr = Snowflake.nextId();
            if (ObjectUtils.isNotEmpty(obj.children)) {
                for (let o of obj.children) {
                    o = await this.copyFunctionDataMap(constructId, o);
                }
            }
        }
        return obj;
    }


    async copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyDeData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }

    async copyCsxmData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyCsxmData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }


    updatePropertyValue(obj, propertyKey, newValue) {
        for (let key in obj) {
            if (key == "parent" || key == "prev" || key == "next") {
                continue;
            }

            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.updatePropertyValue(item, propertyKey, newValue);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.updatePropertyValue(obj[key], propertyKey, newValue);
                }
            } else if (key === propertyKey && !ObjectUtils.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = newValue;
            }
        }
    }


    async _editUnitStructure(constructParam, singleId, unitParam, oldConstructFlatMap, parent, ifParentIsConstruct, constructObj) {

        let oldUnit = oldConstructFlatMap.get(unitParam.id);
        let newUnit = oldUnit;

        let constructId = constructParam.id;
        // let constructObj = PricingFileFindUtils.getProjectObjById(constructId);

        if (!ObjectUtils.isEmpty(unitParam.copyFromId)) {
            //代表是复制的单位
            let newUnit = await ConvertUtil.deepCopy(oldUnit);
            let oldUnitId = newUnit.sequenceNbr;
            newUnit.sequenceNbr = Snowflake.nextId();
            newUnit.parentId = singleId;
            newUnit.name = unitParam.name;
            if (ifParentIsConstruct) {
                newUnit.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                newUnit.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //添加单位
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newUnit.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newUnit, parentNode);

                //重新赋值分部分项等数据的unitId和spId
                await this.repeatInitUnitItemId(newUnit, oldUnitId, constructId);
                constructObj.unitProjectArrayBak.push(newUnit);
            } else {
                //重新赋值单位名称（有重复的话+1）
                await this.repeatInitUnitName(constructId, singleId, newUnit);
                newUnit.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                newUnit.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //添加单位
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newUnit.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newUnit, parentNode);

                //重新赋值分部分项等数据的unitId和spId
                await this.repeatInitUnitItemId(newUnit, oldUnitId, constructId);
                parent.unitProjectsBak.push(newUnit);
            }
        } else {
            if (ObjectUtils.isEmpty(newUnit)) {// 单位不存在
                let ProjectModel = {
                    name: unitParam.name,
                    parentId: unitParam.parentId,
                    type: ProjectLevelConstant.unit,
                    constructMajorType: unitParam.libraryCode,
                    deLibrary: unitParam.libraryCode
                };
                let arg = {
                    constructId: constructId,
                    ProjectModel: ProjectModel
                };
                newUnit = await this.addSingleUnit(arg);

                // let libraryCode = unitParam.libraryCode;
                // if (!libraryCode && unitParam.constructMajorType) {
                //     libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, true).filter(f => f.defaultDeFlag === 1)[0].libraryCode
                // }
                // let secondInstallationProjectName = constructParam.secondInstallationProjectName ? constructParam.secondInstallationProjectName : unitParam.secondInstallationProjectName;
                // newUnit = await this.initUnit(constructObj, unitParam.parentId, unitParam.id, unitParam.name, unitParam.constructMajorType, null, true, libraryCode, secondInstallationProjectName);
            } else { // 单位存在
                let oldConstructMajorType = newUnit.constructMajorType;

                newUnit.name = unitParam.name;
                newUnit.constructMajorType = unitParam.constructMajorType;
                newUnit.secondInstallationProjectName = unitParam.secondInstallationProjectName;
                newUnit.constructId = constructId;
                newUnit.parentId = singleId;

                // ConstructOperationUtil.updateUnitName(newUnit, unitParam.name);
                // ConstructOperationUtil.updateUnitMajorType(newUnit, unitParam.constructMajorType);

                if (ObjectUtils.isNotEmpty(unitParam.constructMajorType)) {
                    // let libraryCode = newUnit.libraryCode || unitParam.libraryCode;
                    // if (!libraryCode && unitParam.constructMajorType) {
                    //     libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, true).filter(f => f.defaultDeFlag === 1)[0].libraryCode
                    // }
                    // newUnit.mainDeLibrary = libraryCode;
                    //
                    // if (oldConstructMajorType != unitParam.constructMajorType) {
                    //     //保存取费文件
                    //     newUnit = await this.service.baseFeeFileService.initFeeFile(newUnit);
                    // }
                }
            }
            // 将处理后的unit加入父级中
            if (ifParentIsConstruct) {
                constructObj.unitProjectArrayBak.push(newUnit);
            } else {
                parent.unitProjectsBak.push(newUnit);
            }
        }
    }

    async repeatInitUnitName(constructId, singleId, newUnit) {
        // let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
        // let unitProjects = singleProject.unitProjects;
        let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
        let unitProjects = projectTree.filter(o => o.parentId === singleId);
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            let singleUnitNameList = unitProjects.map(obj => obj.upName);
            let unitNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newUnit.upName);
            newUnit.upName = unitNewName;
        }
    }


    /**
     * 平铺工程项目，生成map，每一级对象在map中key为其自身的sequenceNbr
     * @param constructObj
     * @return {Map<any, any>}
     */
    flatConstructTreeToMapByObj(constructObj) {
        let treeMap = new Map();
        constructObj.levelType = 1;
        treeMap.set(constructObj.sequenceNbr, constructObj);

        if (ObjectUtils.isNotEmpty(constructObj.children)) {
            constructObj.children.forEach(o => {
                treeMap.set(o.sequenceNbr, o);
                if (ProjectLevelConstant.single === o.type) {
                    let singleTreeMap = this.flatConstructTreeToMapByObj(o);
                    for (let [key, value] of singleTreeMap.entries()) {
                        treeMap.set(key, value);
                    }
                }
            });
        }

        // let singleProjects = constructObj.singleProjects;
        // if (ObjectUtils.isEmpty(singleProjects)) {
        //     if(ObjectUtils.isNotEmpty(constructObj.unitProjectArray)) {
        //         let unitProjectArray = constructObj.unitProjectArray;
        //         for (let i in unitProjectArray) {
        //             let unitProject = unitProjectArray[i];
        //             unitProject.levelType = 3;
        //             treeMap.set(unitProject.sequenceNbr, unitProject);
        //         }
        //     }
        //     if(ObjectUtils.isNotEmpty(constructObj.unitProject)){
        //         constructObj.unitProject.levelType = 3;
        //         treeMap.set(constructObj.unitProject.sequenceNbr, constructObj.unitProject);
        //     }
        // }else{
        //     for (let i in singleProjects) {
        //         singleProjects[i].parentId = constructObj.sequenceNbr;
        //         let singleTreeMap = this._flatSingleTreeToMap(singleProjects[i]);
        //         for (let [key, value] of singleTreeMap.entries()) {
        //             treeMap.set(key, value);
        //         }
        //     }
        // }

        return treeMap;
    }


    async batchModifyName(args) {
        let constructFlatMap = new Map;

        let projectTree = ProjectDomain.getDomain(args.constructId).getProjectTree(); //复制前当前所有单位
        for (let itemProject of projectTree) {
            constructFlatMap.set(itemProject.sequenceNbr, itemProject);
        }

        for (let item of args.data) {
            let node = constructFlatMap.get(item.id);
            if (node.type == ConstantUtil.CONSTRUCT_LEVEL_TYPE) {
                let project = ProjectDomain.getDomain(args.constructId).getProjectById(item.id);
                project.name = item.name;
                let list = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(null, FunctionTypeConstants.JBXX_KEY_TYPE_11));
                for (let itemJbxx of list) {
                    if (itemJbxx.name === "项目名称") {
                        itemJbxx.remark = item.name;
                    }
                }
                ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(node.sequenceNbr, FunctionTypeConstants.JBXX_KEY_TYPE_11), list);
            } else if (node.type == ConstantUtil.SINGLE_LEVEL_TYPE) {
                let project = ProjectDomain.getDomain(args.constructId).getProjectById(item.id);
                project.name = item.name;
            } else if (node.type == ConstantUtil.UNIT_LEVEL_TYPE) {
                let project = ProjectDomain.getDomain(args.constructId).getProjectById(item.id);
                project.name = item.name;
                let list = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(node.sequenceNbr, FunctionTypeConstants.JBXX_KEY_TYPE_11));
                for (let itemJbxx of list) {
                    if (itemJbxx.name === "工程名称") {
                        itemJbxx.remark = item.name;
                    }
                }
                ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(node.sequenceNbr, FunctionTypeConstants.JBXX_KEY_TYPE_11), list);
            }
        }
    }

    /**
     * 11 基本信息 12 编制说明 13 特征
     * @param {*} unitId
     * @param {*} type
     * @returns
     */
    getDataMapKey(unitId, type) {

        if (ObjectUtils.isEmpty(unitId)) {
            unitId = "0";//保持key风格一致性
        }
        return "JBXX-" + unitId + "-" + type;
    }


    async calProjectSingleUnits(constructId, singleId, units) {
        let projectTreeList = ProjectDomain.getDomain(constructId).getProjectTree();
        let filter = projectTreeList.filter(o => o.parentId === singleId);
        if (ObjectUtils.isNotEmpty(filter)) {
            for (let item of filter) {
                if (item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    units.push(item.sequenceNbr)
                } else if (item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
                    units = await this.calProjectSingleUnits(constructId, item.sequenceNbr, units);
                }
            }
        }
        return units;
    }


    // 检查文件是否存在
    checkFileExistence(filePath) {
        try {
            fs.accessSync(filePath, fs.constants.F_OK);
            return true;
        } catch (err) {
            return false;
        }
    }



    /**
     * 历史文件没有保存小数点，先保留小数点，再重新计算
     * @param constructId
     * @param unitProjects
     */
    async repeatCalPrecision(constructId, unitProjects) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(projectDomain.getRoot().sequenceNbr);
        if (ObjectUtil.isEmpty(projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING))) {
            projectDomain.functionDataMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precision1);
        }

        for (let item of unitProjects) {
            let startTimeUnit = new Date().getTime();
            let deList = projectDomain.deDomain.getDeTree(p => p.unitId === item.sequenceNbr);
            let csxmList = projectDomain.csxmDomain.getDeTree(p => p.unitId === item.sequenceNbr);

            //todo 重新计算独立费
            await this.calDlf(constructId, item, precision1.UNIT_DLF);

            //todo 重新计算工程量明细
            if (ObjectUtil.isNotEmpty(deList)) {
                for (let deRow of deList) {
                    if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
                        && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB)) {
                        await this.service.gongLiaoJiProject.gljQuantitiesService.recaculateQuantity(constructId, item.sequenceNbr, deRow.sequenceNbr);
                    }
                }
            }

            //todo 重新计算取费表
            await this.calFeeRateUnit(constructId, item, precision1.FREE_RATE);

            //todo 重新计算人材机、预算书、措施项目、自动计取
            let startTimeDe = new Date().getTime();
            await this.calDeCsxm(constructId, item, deList, csxmList);
            let endTimeDe = new Date().getTime();


            //todo 重新计算费用计取、费用汇总
            await this.calAutoCostMathCostCodePrice(constructId, item);

            let endTimeUnit = new Date().getTime();
            console.log("----------定额计算时间: " + (endTimeDe - startTimeDe)/1000 + " 秒");
            console.log("----------整个单位计算时间: " + (endTimeUnit - startTimeUnit)/1000 + " 秒");
        }
    }


    /**
     *   重新计算de
     * @param constructId
     * @param unitProjects
     */
    async calProjectDeCsxm(constructId, unitProjects) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(projectDomain.getRoot().sequenceNbr);
        if (ObjectUtil.isEmpty(projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING))) {
            projectDomain.functionDataMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precision1);
        }
        for (let item of unitProjects) {
            let deList = projectDomain.deDomain.getDeTree(p => p.unitId === item.sequenceNbr);
            let csxmList = projectDomain.csxmDomain.getDeTree(p => p.unitId === item.sequenceNbr);
            //todo 重新计算人材机、预算书、措施项目、自动计取
            let startTimeDe = new Date().getTime();
            await this.calDeCsxm(constructId, item, deList, csxmList);
            let endTimeDe = new Date().getTime();
            console.log("----------定额计算时间: " + (endTimeDe - startTimeDe)/1000 + " 秒");
        }
    }




    async calDlf(constructId, unit, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let unitDlfKey = await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unit.sequenceNbr);
        let list = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).get(unitDlfKey);
        await this.service.gongLiaoJiProject.gljIndependentCostsService.caculatorTreeTotal(list, null, precision);

        for (let item of list) {
            //通知费用汇总变动消息
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: unit.parentId,
                unitId: unit.sequenceNbr,
                qfMajorType: item.costMajorCode
            });
        }
    }


    async calFeeRateConstruct(constructId, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        //项目级别
        let projectFee = projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        if (ObjectUtils.isNotEmpty(projectFee.childFreeRate)) {
            for (let [key, value] of projectFee.childFreeRate) {
                if (ObjectUtils.isEmpty(value.manageFeeRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.manageFeeRateUpdate = true;
                }

                if (ObjectUtils.isEmpty(value.profitRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.profitRateUpdate = true;
                }

                if (ObjectUtils.isEmpty(value.taxRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.taxRateUpdate = true;
                }

                if (ObjectUtils.isEmpty(value.anwenRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.anwenRateUpdate = true;
                }
            }
        }
    }

    async calFeeRateUnit(constructId, unit, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);

        //重新计算单位级别
        let unitFeeMap = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isNotEmpty(unitFeeMap)) {
            for (let [key, value] of unitFeeMap) {
                if (key.includes(unit.sequenceNbr)) {
                    if (ObjectUtils.isEmpty(value.manageFeeRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.manageFeeRateUpdate = true;
                    }

                    if (ObjectUtils.isEmpty(value.profitRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.profitRateUpdate = true;
                    }

                    if (ObjectUtils.isEmpty(value.taxRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.taxRateUpdate = true;
                    }

                    if (ObjectUtils.isEmpty(value.anwenRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.anwenRateUpdate = true;
                    }
                }
            }
        }


        //重新计算单项级别
        let singleId = unit.parentId;
        let singleFeeMap = projectDomain.functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        if (ObjectUtils.isNotEmpty(singleFeeMap)) {
            for (let [key, value] of singleFeeMap) {
                if (key.includes(singleId)) {
                    if (ObjectUtils.isNotEmpty(value.childFreeRate)) {
                        for (let [key1, value1] of value.childFreeRate) {
                            if (ObjectUtils.isEmpty(value1.manageFeeRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.manageFeeRateUpdate = true;
                            }

                            if (ObjectUtils.isEmpty(value1.profitRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.profitRateUpdate = true;
                            }

                            if (ObjectUtils.isEmpty(value1.taxRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.taxRateUpdate = true;
                            }

                            if (ObjectUtils.isEmpty(value1.anwenRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.anwenRateUpdate = true;
                            }
                        }
                    }
                }
            }
        }

        //重新计算项目级别
        await this.calFeeRateConstruct(constructId, precision);
    }

    async calDeCsxm(constructId, unit, deList, csxmList) {

        let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        deBaseDomain.notifyAll(constructId, unit.sequenceNbr);

        let rcjDeKey = WildcardMap.generateKey(unit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let rcj of rcjList) {
                await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(rcj);
            }
        }

        // if (ObjectUtil.isNotEmpty(deList)) {
        //     for (let deRow of deList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             // await projectDomain.deDomain.updateQuantityCopy(constructId, unitId, deRowId, deRow.originalQuantity);
        //             try {
        //                 await ProjectDomain.getDomain(constructId).deDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算预算书定额错误");
        //                 console.log(deRow);
        //             }
        //             // await projectDomain.deDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
        // if (ObjectUtil.isNotEmpty(csxmList)) {
        //     for (let deRow of csxmList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB && deRow.type !== DeTypeConstants.DE_TYPE_DELIST)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             try {
        //                 await ProjectDomain.getDomain(constructId).csxmDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算措施项目定额错误");
        //                 console.log(deRow);
        //             }
        //             // await projectDomain.csxmDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
    }

    async calDeCsxmProjectDomain(constructId, unit, deList, csxmList, projectDomain) {
        let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        deBaseDomain.notifyAll(constructId, unit.sequenceNbr);

        let rcjDeKey = WildcardMap.generateKey(unit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let rcj of rcjList) {
                await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(rcj);
            }
        }

        // if (ObjectUtil.isNotEmpty(deList)) {
        //     for (let deRow of deList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             // await projectDomain.deDomain.updateQuantityCopy(constructId, unitId, deRowId, deRow.originalQuantity);
        //             try {
        //                 await projectDomain.deDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算预算书定额错误");
        //                 console.log(deRow);
        //             }
        //
        //             // await projectDomain.deDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
        // if (ObjectUtil.isNotEmpty(csxmList)) {
        //     for (let deRow of csxmList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB && deRow.type !== DeTypeConstants.DE_TYPE_DELIST)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             try {
        //                 await projectDomain.csxmDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算措施项目定额错误");
        //                 console.log(deRow);
        //             }
        //
        //             // await projectDomain.csxmDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
    }


    async calAutoCostMathCostCodePrice(constructId, unit) {
        try {
            //费用计取
            this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
                unitId: unit.sequenceNbr,
                singleId: null,
                constructId: constructId
            });

            //费用汇总
            this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                unitId: unit.sequenceNbr,
                qfMajorType: unit.qfMajorType
            });
        } catch (error) {
            console.error("捕获到异常:", error);
        }
    }




}

GljProjectService.toString = () => '[class GljProjectService]';
module.exports = GljProjectService;
