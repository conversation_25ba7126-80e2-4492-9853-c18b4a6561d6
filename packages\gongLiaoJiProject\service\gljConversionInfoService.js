'use strict';

const { Service, Log } = require('../../../core');
const { ObjectUtils } = require("../utils/ObjectUtils");
const { Snowflake } = require("../utils/Snowflake");
const { GsConversionInfoItem } = require("../models/GsConversionInfoItem");
const ConversionSourceEnum = require("../enums/ConversionSourceEnum");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ConversionInfoUtil} = require("../standard_conversion/util/ConversionInfoUtil");
const { ParamUtils } = require("../../../core/core/lib/utils/ParamUtils");
const ConversionInfoStrategy = require("../conversion_information/conversionInfoStrategy");
const DeTypeConstants = require('../constants/DeTypeConstants');
const DeCommonConstants = require("../constants/DeCommonConstants");
const { ConvertUtil } = require('../utils/ConvertUtils');
const GsProjectSettingEnum = require("../enums/GsProjectSettingEnum");
const WildcardMap = require("../core/container/WildcardMap");

/**
 * 示例服务
 * @class
 */
class GljConversionInfoService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取单位下换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     */
    getUnitProjectConversionInfo(constructId, singleId, unitId) {
        // 获取单位下换算信息
        return this.service.gongLiaoJiProject.gsProjectCommonService.getConversionInfo(constructId, unitId);
    }

    /**
     * 获取单位下某定额的换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbFxDeId 分部分项定额id
     */
    async getDeConversionInfo(constructId, singleId, unitId, fbFxDeId) {
        /* // 自测参数
        global.constructProject = [];
        // 有操作记录数据
        global.constructProject[1] = {"proJectData":{"biddingType":"0","constructName":"ysb测试项目","fileCode":"1660814140593278976","path":"C:\\Users\\<USER>\\.xilidata\\ysb测试项目.ysf","singleProjects":[{"sequenceNbr":"1","unitProjects":[{"conversionRuleOperationRecordList":[{"fbFxDeId":"0","ruleDetailId":"1659383061407444994","sequenceNbr":"1"},{"fbFxDeId":"0","ruleDetailId":"1659383061407444993","sequenceNbr":"2"}],"itemBillProjects":[{"bdName":"单位工程","kind":"0","sequenceNbr":"1659383061407444994","unitId":"1658367326819016727"},{"bdName":"分部","kind":"01","parentId":"1659383061407444994","sequenceNbr":"1659384089636773889","unitId":"1658367326819016727"},{"bdName":"子分部","kind":"02","parentId":"1659384089636773889","sequenceNbr":"1659384255106260994","unitId":"1658367326819016727"},{"bdName":"清单1","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660579763674288128","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660579763674288128","sequenceNbr":"1660579957178503168","unitId":"1658367326819016727"},{"bdName":"清单2","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660580315070074880","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581320901922816","unitId":"1658367326819016727"},{"bdName":"定额2","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581517463785472","unitId":"1658367326819016727"}],"sequenceNbr":"1658367326819016727"}]}]}};
        fbFxDeId="0";
        constructId = "1";
        singleId = "1";
        unitId = "1658367326819016727";*/

        return await this.conversionInfoList(constructId, unitId, fbFxDeId)
        // 获取单位下换算信息
        let unitProjectConversionInfo = this.getUnitProjectConversionInfo(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unitProjectConversionInfo)) {
            return [];
        }
        // 返回单位下某定额的换算信息
        return unitProjectConversionInfo.filter(i => i.deId === fbFxDeId);
    }

    async conversionInfoList(constructId, unitId, fbFxDeId) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        let conversionInfo = unitConversionMap?.get(fbFxDeId)?.conversionInfo;
        return conversionInfo? conversionInfo : []
    }

    /**
     * 添加换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbFxDeId 分部分项定额id
     * @param baseRuleDetails 规则详情
     */
    addConversionInfo(constructId, singleId, unitId, fbFxDeId, baseRuleDetails) {
        // 组装换算信息model
        let conversionInfo = new GsConversionInfoItem();
        conversionInfo.sequenceNbr = Snowflake.nextId();
        conversionInfo.deId = fbFxDeId;
        conversionInfo.conversionString = baseRuleDetails.math;
        conversionInfo.conversionExplain = baseRuleDetails.relation;
        // 先写死来源
        conversionInfo.sourceCode = ConversionSourceEnum.STANDARD_CONVERSION.code;
        conversionInfo.source = ConversionSourceEnum.STANDARD_CONVERSION.desc;
        // 换算算法

        // 获取单位工程
        let unitProject = this.service.gongLiaoJiProject.gsProjectCommonService.getConversionInfo(constructId, unitId);
        // 单位下是否有换算信息
        if (ObjectUtils.isEmpty(unitProject.conversionInfoList)) {
            // 无
            unitProject.conversionInfoList = [];
            unitProject.conversionInfoList.push(conversionInfo);
        } else {
            // 有
            unitProject.conversionInfoList.push(conversionInfo);
        }
        return true;
    }

    /**
     * 删除换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbFxDeId 分部分项定额id
     * @param ruleId 规则id
     */
    deleteConversionInfo(constructId, singleId, unitId, fbFxDeId, ruleId) {
        // 获取单位换算信息
        let unitProjectConversionInfo = this.getUnitProjectConversionInfo(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unitProjectConversionInfo)) {
            // 单位下无换算信息，删除失败
            console.error("单位下无换算信息，删除失败");
            return false;
        }
        let deleteIndex = unitProjectConversionInfo.findIndex(i => i.deId === fbFxDeId && i.ruleId === ruleId);
        unitProjectConversionInfo.splice(deleteIndex, 1);
        return true;
    }

    /**
     * 批量删除换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param deIds 分部分项/措施项目定额idList
     */
    delConversionInfoBatch(deIds, constructId, singleId, unitId) {
        // 获取单位换算信息
        let unitProjectConversionInfo = this.getUnitProjectConversionInfo(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unitProjectConversionInfo)) {
            // 单位下无换算信息，删除失败
            console.error("单位下无换算信息，删除失败");
            return false;
        }
        for (let i = unitProjectConversionInfo.length - 1; i >= 0; i--) {
            if (deIds.indexOf(unitProjectConversionInfo[i].deId) !== -1) {
                unitProjectConversionInfo.splice(i, 1);
            }
        }
        return true;
    }

    /**
     * 新增换算信息
     * @returns {Promise<void>}
     */
    async addConversionInfoByRcj(constructId, singleId, unitId, deId) {

    }

    /**
     * 刪除换算信息
     * @returns {Promise<void>}
     */
    async deleteConversionInfoByRcj() {

    }

    /**
     * 人材机操作同步换算信息
     */
    async updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcj, type, replaceRcj, oldRcj){
        let de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        if (ObjectUtils.isEmpty(de) || de.type === DeTypeConstants.DE_TYPE_USER_DE) {
            return
        }
        if(ObjectUtils.isEmpty(de.conversionInfo)){
            de.conversionInfo=[];
        }

        if(rcj){
            rcj.resQty = (rcj.resQty || 0)
        }

        if(replaceRcj){
            replaceRcj.resQty = (replaceRcj.resQty || 0)
        }

        let conversionInfo;
        switch (type) {
            case  "add":
                conversionInfo = await this.addRcj(de,rcj);
                break;
            case  "update":
                conversionInfo = await this.updateRcj(de,rcj,oldRcj);
                break;
            case  "updateQty":
                conversionInfo = await this.updateRcjQty(de, rcj, oldRcj.resQty);
                break;
            case  "del":
                conversionInfo = await this.delRcj(de,rcj);
                break;
            case  "replace":
                conversionInfo = await this.replaceRcj(de,rcj,replaceRcj);
                break;
            case  "addMerge":
                conversionInfo = await this.addMerge(de,rcj);
                break;

        }
        return conversionInfo ;
    }

    async addMerge(de,rcj){
        if (ObjectUtils.isEmpty(rcj)) {
            // 修改定额单价至原来，删除rcj调整的情况
            let rgftzConversionInfoFinal = de.conversionInfo.reduce((acc, item) => {
                if (item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ) {
                    acc[0] = item;
                } else if (item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ) {
                    acc[1] = item;
                } else if (item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ) {
                    acc[2] = item;
                }
                return acc;
            }, [null, null]).filter(Boolean); // 过滤掉 null
            for (let item of rgftzConversionInfoFinal) {
                let rgftzConversionInfo = ConvertUtil.deepCopy(item)
                rgftzConversionInfo.conversionString = "H"+ rgftzConversionInfo.materialCode+" " + rgftzConversionInfo.materialCode+" " + (0 - rgftzConversionInfo.rcj.resQty)
                rgftzConversionInfo.conversionExplain = `人材机${rcj.materialCode}(${DeCommonConstants.getTZNameByCode(rcj.materialCode)})消耗量改为${0 - rgftzConversionInfo.rcj.resQty}`,//"Upd "+ rgftzConversionInfo.materialCode+" " + (0 - rgftzConversionInfo.rcj.resQty)
                rgftzConversionInfo.rcjType = "updateQty";
                de.conversionInfo.push(ConvertUtil.deepCopy(rgftzConversionInfo));
            }
            return;
        }
        let resQty = rcj.resQty?rcj.resQty:0;
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.OTHER_CONVERSION_SOURCE ,
            "conversionString": "H "+rcj.materialCode+" "+resQty,
            "conversionExplain": `插入人材机${rcj.materialCode}(${DeCommonConstants.getTZNameByCode(rcj.materialCode)})`,
            "sortNo":de.conversionInfo.length,
            "materialCode": rcj.materialCode,
            "kind": 5, //人材机类型
            "rcjType": "add",  //添加操作类型
            "rcjId": rcj.sequenceNbr,
            "originalRcjId": rcj.rcjId,
            // 'rcj': ConvertUtil.deepCopy(rcj)
        };
        let tzRcj = de.conversionInfo.find(item => item.materialCode === rcj.materialCode)
        if (ObjectUtils.isNotEmpty(tzRcj)) {
            initConversionInfo.rcjType = 'updateQty'
            initConversionInfo.conversionString = "H"+ rcj.materialCode+" " + rcj.materialCode+" " + resQty
            initConversionInfo.conversionExplain = `人材机${rcj.materialCode}(${DeCommonConstants.getTZNameByCode(rcj.materialCode)})消耗量改为${resQty}`,
            de.conversionInfo.push(initConversionInfo);
        } else {
            de.conversionInfo.push(initConversionInfo);
        }

        return ;
    }


    async addRcj(de,rcj){
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.RCJ_DETAIL_SOURCE ,
            "conversionString": "H "+rcj.materialCode+" "+ (rcj.resQty || 0),
            "conversionExplain": `插入人材机${rcj.materialCode}(${rcj.materialName}${rcj.specification ? rcj.specification : ""})`,
            "sortNo":de.conversionInfo.length,
            "kind": 5, //人材机类型
            "libraryCode": rcj.libraryCode,
            "rcjType": "add",  //添加操作类型
            "rcjId": rcj.sequenceNbr,
            "originalRcjId": rcj.rcjId
        };

        de.conversionInfo.push(initConversionInfo);

        return initConversionInfo;
    }

    async delRcj(de,rcj){
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.RCJ_DETAIL_SOURCE ,
            "conversionString": "D "+rcj.materialCode+" "+rcj.resQty,
            "conversionExplain": `删除人材机${rcj.materialCode}(${rcj.materialName}${rcj.specification ? rcj.specification : ""})`,
            "sortNo":de.conversionInfo.length,
            "kind": 5,
            "rcjType": "del",
            "rcjId": rcj.sequenceNbr,
            "originalRcjId": rcj.rcjId

        };

        de.conversionInfo.push(initConversionInfo);
        return initConversionInfo;
    }

    //引起材料编码变化的修改
    async updateRcj(de,rcj,oldRcj){
        let newNameSpecification = `${rcj.materialName}` + (rcj.specification ? `(${rcj.specification})` : "");
        let oldNameSpecification = `${oldRcj.materialName}` + (oldRcj.specification ? `(${oldRcj.specification})` : "");
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.RCJ_DETAIL_SOURCE ,
            "conversionString": "H"+oldRcj.materialCode+" "+rcj.materialCode,
            "conversionExplain": `把人材机${oldRcj.materialCode}${oldNameSpecification}替换为${rcj.materialCode}${newNameSpecification}`,
            "conversionNameExplain": `换为【${newNameSpecification}】`,
            "sortNo":de.conversionInfo.length,
            "kind": 5,
            "rcjType": "update",
            "rcjId": rcj.sequenceNbr,
            "originalRcjId": rcj.rcjId
        };

        de.conversionInfo.push(initConversionInfo);
        return initConversionInfo;
    }

    async updateRcjQty(de,rcj, oldResQty){
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.RCJ_DETAIL_SOURCE ,
            // "conversionString": "H"+rcj.materialCode+" "+rcj.materialCode+" "+rcj.resQty,
            "conversionString": "L"+rcj.materialCode+" "+oldResQty+" "+(rcj.isTempRemove == 1 ? rcj.changeResQty : rcj.resQty),
            "conversionExplain": `人材机${rcj.materialCode}(${rcj.materialName}${rcj.specification ? rcj.specification : ""})消耗量改为${rcj.resQty}`,
            "sortNo":de.conversionInfo.length,
            "kind": 5,
            "rcjType": "updateQty",
            "rcjId": rcj.sequenceNbr,
            "originalRcjId": rcj.rcjId,
            "resQty": rcj.resQty
        };

        de.conversionInfo.push(initConversionInfo);
        return initConversionInfo;
    }

    async replaceRcj(de,newRcj,oldRcj){
        let oldNameSpecification = `${oldRcj.materialName}` + (oldRcj.specification ? `(${oldRcj.specification})` : "");
        let newNameSpecification = `${newRcj.materialName}` + (newRcj.specification ? `(${newRcj.specification})` : "");
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.RCJ_DETAIL_SOURCE ,
            // "conversionExplain": conversionExplain,
            // "conversionString": conversionString,
            "conversionNameExplain": `换为【${newNameSpecification}】`,
            "sortNo":de.conversionInfo.length,
            "kind": 5,
            "rcjType": "replace",
            "rcjId": oldRcj.sequenceNbr,
            "originalRcjId": oldRcj.rcjId,
            "replaceRcjId": newRcj.sequenceNbr,
            "replaceOriginalRcjId": newRcj.rcjId
        };
        let conversionString;
        let conversionExplain;
        if(newRcj.resQty==oldRcj.resQty){
            conversionString="H"+oldRcj.materialCode+" "+newRcj.materialCode;
            conversionExplain=`消耗量不变 把人材机${oldRcj.materialCode}(${oldNameSpecification})替换为${newRcj.materialCode}(${newNameSpecification})`;
            initConversionInfo.conversionExplain=conversionExplain;
            initConversionInfo.conversionString=conversionString;
            de.conversionInfo.push(initConversionInfo);
        }else {
            conversionString="H"+oldRcj.materialCode+" "+newRcj.materialCode + " "+newRcj.resQty;
            conversionExplain=`消耗变化 把人材机${oldRcj.materialCode}(${oldNameSpecification})替换为${newRcj.materialCode}(${newNameSpecification})`;
            initConversionInfo.conversionExplain=conversionExplain;
            initConversionInfo.conversionString=conversionString;
            de.conversionInfo.push(initConversionInfo);
            //追加一条消耗量的换算信息
            // initConversionInfo.sequenceNbr= Snowflake.nextId();
            // conversionString="H "+replaceRcj.materialCode+" "+replaceRcj.materialCode;
            // conversionExplain="消耗量变化 H "+rcj.materialCode+" "+replaceRcj.materialCode+" "+replaceRcj.resQty;
            // initConversionInfo.conversionExplain=conversionExplain;
            // initConversionInfo.conversionString=conversionString;
            // de.conversionInfo.push(initConversionInfo);
        }
        return initConversionInfo;
    }

    /**
     * 修改单位下某定额的换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbFxDeId 分部分项定额id
     */
    async updateDeConversionInfoOld(constructId, singleId, unitId, fbFxDeId,selectId,operateAction) {

        let patram = ParamUtils.getPatram("commonParam");
        patram.unitId=unitId;
        patram.constructId=constructId;
        patram.singleId=singleId;
        // const { line, belong: type } =
        //     this.service.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
        let line = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, fbFxDeId);

        if (!line) throw new Error('找不到定额行')
        let conversionInfo = line.conversionInfo;
        if (!Array.isArray(conversionInfo)){
            conversionInfo = []
        } else {
            // 获取索引
            let index = conversionInfo.findIndex(item => item.sequenceNbr === selectId);
            if(index===-1){
                // 标准换算处理
                let conversionInfos = conversionInfo.find(v => v.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE)
                let children = conversionInfos.children;
                index = children.findIndex(item => item.sequenceNbr === selectId);
                if (operateAction === "delete"&&index!==-1) {
                    // 删除标准换算同时处理标准换算信息
                    let conversionList = line.conversionList;
                    let t = conversionList.find(a=>a.sequenceNbr===children[index].sequenceNbr);
                    if (t.kind === "1") {
                        t.selected=0
                    }else if (t.kind === "2"){
                        t.ruleInfo=t.defaultValue
                    }else if (t.kind === "3"){
                        if (t.type === "e2") {
                            // 删除同组的标准换算
                            for (let i = 0; i < children.length; i++) {
                                if (children[i].deCode === t.deCode && children[i].sequenceNbr!==t.sequenceNbr) {
                                    children.splice(i, 1);
                                    i--;
                                }
                            }
                            // 重新获取删除的换算信息索引
                            index = children.findIndex(item => item.sequenceNbr === selectId);
                            //     修改同组的标准换算信息为默认值
                            for (let i = 0; i < conversionList.length; i++) {
                                if (conversionList[i].deCode === t.deCode){
                                    conversionList[i].selectedRule=conversionList[i].defaultValue
                                }
                            }

                        }else {
                            t.selectedRule=t.defaultValue
                        }
                    }

                    children.splice(index, 1);

                    // 判断标准换算最后一条进行删除标准换算结构
                    if (children.length === 0) {
                        index = conversionInfo.findIndex(item => item.sequenceNbr === conversionInfos.sequenceNbr);
                        // index = conversionInfo.findIndex(v => v.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE)
                        conversionInfo.splice(index, 1);
                    }
                }else {
                    if (index!==-1) this.updateIndex(operateAction, index, conversionInfos.children);
                }
                // 更新排序
                for (let i = 0; i < conversionInfos.length; i++) {
                    conversionInfos.children[i].sortNo = i
                }
            }else {
                if (operateAction === "delete"&&index!==-1) {
                    //  判断是否为统一换算信息,并处理统一换算数据
                    let t = conversionInfo.find(a=>a.sequenceNbr===conversionInfo[index].sequenceNbr);
                    if (t.source === ConversionInfoUtil.UNITE_CONVERSION_SOURCE) {

                        const res = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getDefDonversion(constructId, unitId, fbFxDeId);
                        // const res = this.service.conversionDeService.getDefDonversion(
                        //     {constructId:constructId,
                        //         singleId:singleId,
                        //         unitId:unitId,
                        //         deId:fbFxDeId});
                        let find = res.find(a=>a.sequenceNbr=t.sequenceNbr);
                        find.val=1
                    }

                    conversionInfo.splice(index, 1);
                }else {
                    this.updateIndex(operateAction, index, conversionInfo);
                }
                // 更新排序
                for (let i = 0; i < conversionInfo.length; i++) {
                    conversionInfo[i].sortNo = i
                }
            }
        }
        // 重新计算换算信息
        await this.service.gongLiaoJiProject.gljConversionDeService.conversionRule(
            constructId,
            singleId,
            unitId,
            fbFxDeId,
            [],
            line.defaultConcersions
        );
        return conversionInfo;
    }


    /**
     * 修改单位下某定额的换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbFxDeId 分部分项定额id
     */
    async updateDeConversionInfo(constructId, singleId, unitId, fbFxDeId,selectId,operateAction) {

        const line = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, fbFxDeId);
        if(ObjectUtils.isEmpty(line)){
            throw new Error('找不到定额行')
        }

        let {operateState, conversionInfo} = await this._updateOneInfo(constructId, singleId, unitId,line, selectId, operateAction);
        if(operateState){
            let conversionInfoStrategy = new ConversionInfoStrategy();
            await conversionInfoStrategy.init(constructId, singleId, unitId, fbFxDeId);
            await conversionInfoStrategy.execute();
        }

        return conversionInfo;
    }

    /**
     * 执行换算信息
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbFxDeId 分部分项定额id
     */
    async executeConversionInfo(constructId, singleId, unitId, fbFxDeId) {
        const line = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, fbFxDeId);
        if(ObjectUtils.isEmpty(line)){
            throw new Error('找不到定额行')
        }
        if(true){
            let conversionInfoStrategy = new ConversionInfoStrategy();
            await conversionInfoStrategy.init(constructId, singleId, unitId, fbFxDeId);
            await conversionInfoStrategy.execute();
        }
    }

    /**
     * 清除换算
     * @param constructId
     * @param singleId
     * @param unitId
     * @param deId
     * @return {Promise<void>}
     */
    async clearConversionInfo(constructId, singleId, unitId, deId) {
        // const { line:de, belong: type } = this.service.baseBranchProjectOptionService.findLineOnlyById(deId);
        let de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        if(ObjectUtils.isEmpty(de)){
            throw new Error('找不到定额行')
        }

        let conversionInfo = de.conversionInfo || [];
        let conversionInfoExcludeKind6 = conversionInfo.filter(item => item.source != ConversionInfoUtil.SZSSWXYHZXZX22);
        let infoIndexes = conversionInfoExcludeKind6.map(item => item.sequenceNbr);
        // 保留中修换算信息： 22定额标准市政工程专业，且主定额册是“河北省建设工程消耗量标准（2025）-市政设施维修养护工程”的市政维修养护的标准定额
        let kind6InfoArray = conversionInfo.filter(item => item.source == ConversionInfoUtil.SZSSWXYHZXZX22);

        for(let index of infoIndexes){
            await this._updateOneInfo(constructId, singleId, unitId,de, index, "delete");
        }

        de.conversionInfo = kind6InfoArray || [];
        let conversionInfoStrategy = new ConversionInfoStrategy();
        await conversionInfoStrategy.init(constructId, singleId, unitId, deId);
        await conversionInfoStrategy.execute();

        // await new ConversionInfoStrategy(constructId, singleId, unitId, deId, true).execute();
    }


    async _updateOneInfo(constructId, singleId, unitId, de, selectId, operateAction){
        let conversionInfo = de.conversionInfo;
        let operateState = false;
        if (!Array.isArray(conversionInfo)){
            return {operateState, conversionInfo: []};
        }

        let selectInfoIndex = conversionInfo.findIndex(item => item.sequenceNbr === selectId);

        let selectInfo = selectInfoIndex == -1 ? null : conversionInfo[selectInfoIndex];

        if(ObjectUtils.isEmpty(selectInfo)) {
            let standardInfoRootIndex = conversionInfo.findIndex(v => v.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE);
            let standardInfoRoot = standardInfoRootIndex == -1 ?  null : conversionInfo[standardInfoRootIndex];
            if (ObjectUtils.isNotEmpty(standardInfoRoot) && ObjectUtils.isNotEmpty(standardInfoRoot.children)) {
                let standardInfos = standardInfoRoot.children;
                selectInfoIndex = standardInfos.findIndex(item => item.sequenceNbr === selectId);
                selectInfo = selectInfoIndex == -1 ? null : standardInfos[selectInfoIndex];

                if (ObjectUtils.isNotEmpty(selectInfo)) {
                    if(operateAction === "delete"){
                        this.deleteOneStandardInfo([selectInfo], standardInfoRoot, constructId, singleId, unitId, de);
                        if(ObjectUtils.isEmpty(standardInfoRoot.children)){
                            conversionInfo.splice(standardInfoRootIndex, 1);
                        }
                    }else{
                        this.updateIndex(operateAction, selectInfoIndex, standardInfoRoot.children);
                    }

                    // 更新排序
                    for (let i = 0; i < standardInfoRoot?.children?.length; i++) {
                        standardInfoRoot.children[i].sortNo = i
                    }

                    operateState = true;
                }
            }
        } else {
            if (operateAction === "delete") {
                // 如果选中的是标准换算的组节点
                if(selectInfo.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE){
                    await this.deleteOneStandardInfo(selectInfo.children || [], selectInfo, constructId, singleId, unitId, de);
                }

                conversionInfo.splice(selectInfoIndex, 1);
            }else {
                this.updateIndex(operateAction, selectInfoIndex, conversionInfo);
            }
            // 更新排序
            for (let i = 0; i < conversionInfo.length; i++) {
                conversionInfo[i].sortNo = i
            }
            operateState = true;
        }

        return {operateState,conversionInfo}
    }


    async deleteOneStandardInfo(deleteInfos, standardInfoRoot, constructId, singleId, unitId, de){
        // 删除标准换算同时处理标准换算信息
        let defaultConcersions = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getDefDonversion(constructId, unitId, de.sequenceNbr);
        let conversionRuleList = [];
        conversionRuleList.push(...de.conversionList);
        conversionRuleList.push(...defaultConcersions);
        let standardInfos = standardInfoRoot.children;
        for(let i = deleteInfos.length - 1; i >= 0; i--){
            let info = deleteInfos[i];
            let index = standardInfos.findIndex(item => item.sequenceNbr === info.sequenceNbr);
            let rule = conversionRuleList.find(a => a.sequenceNbr === info.sequenceNbr);
            if (rule.kind === "1") {
                rule.selected = 0
            } else if (rule.kind === "2") {
                rule.ruleInfo = rule.defaultValue;
                rule.currentRcjCode = rule.defaultRcjCode;
                rule.currentRcjLibraryCode = rule.defaultRcjLibraryCode;
                rule.clpb = undefined;
            } else if (rule.kind === "3") {
                if (rule.type === "e2") {
                    // 删除同组的标准换算
                    for (let i = 0; i < standardInfos.length; i++) {
                        if (standardInfos[i].deCode === rule.deCode && standardInfos[i].sequenceNbr !== rule.sequenceNbr) {
                            standardInfos.splice(i, 1);
                            i--;
                        }
                    }
                    // 重新获取删除的换算信息索引
                    index = standardInfos.findIndex(item => item.sequenceNbr === info.sequenceNbr);
                    //     修改同组的标准换算信息为默认值
                    for (let i = 0; i < conversionRuleList.length; i++) {
                        if (conversionRuleList[i].deCode === rule.deCode) {
                            conversionRuleList[i].selectedRule = conversionRuleList[i].defaultValue
                        }
                    }

                } else {
                    rule.selectedRule = rule.defaultValue
                }
            } else if (rule.kind === "4") {
                rule.val = "1";
            }

            standardInfos.splice(index, 1);
        }

    }


    updateIndex(operateAction, index, conversionInfo) {
        const newIndex = operateAction === 'up' ? index - 1 : index + 1;
        if (newIndex >= 0 && newIndex < conversionInfo.length) {
            conversionInfo.splice(newIndex, 0, conversionInfo.splice(index, 1)[0]);
        }
    }


    /**
     * 添加中修换算信息
     * 前提定额是：22定额标准市政工程专业，且主定额册是“河北省建设工程消耗量标准（2025）-市政设施维修养护工程”的市政维修养护的标准定额
     */
    async addInfoForSzsswxyhzxzx22(deLine){
        let {constructId, singleId, unitId, sequenceNbr} = deLine;
        let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let deConversion = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, sequenceNbr);
        let info = this.addInfoOnlyForSzsswxyhzxzx22(unitProject, deConversion);

        if(ObjectUtils.isNotEmpty(info)){
            // 执行换算换算信息
            await this.executeConversionInfo(constructId, singleId, unitId, sequenceNbr);
        }
    }

    addInfoOnlyForSzsswxyhzxzx22(unitProject, de){
        let isSzsswxyhzxzx22 = this._isSzsswxyhzxzxDe22(unitProject, de);
        if(!isSzsswxyhzxzx22){
            return null;
        }
        de.conversionInfo = de.conversionInfo || [];
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "source": ConversionInfoUtil.SZSSWXYHZXZX22 ,
            "conversionString": "R*0.9,J*0.9",
            "conversionExplain": "中修工程按本标准人工、机械消耗量乘以系数0.9",
            "libraryCode": de.libraryCode,
            "sortNo": de.conversionInfo.length,
            "kind": 6, //人材机类型
        };
        de.conversionInfo = de.conversionInfo || [];
        de.conversionInfo.push(initConversionInfo);
        return initConversionInfo;
    }

    // 判断是否是22标准市政工程，且主定额册是“河北省建设工程消耗量标准（2025）-市政设施维修养护工程”的市政维修养护的标准定额
    _isSzsswxyhzxzxDe22(unitProject, de){
        let businessMap = ProjectDomain.getDomain(de.constructId).functionDataMap;

        let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
        let szssMediumRepair = setting.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);
        // let constructConfig = this.service.globalConfigurationService.getProjectConfig(unitProject.constructId);
        return unitProject.constructMajorType === "2025-SZSS-DEX"
            && unitProject.deLibrary === "2025-SZSS-DEX"
            && de.libraryCode === "2025-SZSS-DEX"
            && ObjectUtils.isNotEmpty(de.standardId)
            && szssMediumRepair;
    }

    // 判断是否是22标准市政工程，且主定额册是“河北省建设工程消耗量标准（2025）-市政设施维修养护工程”的市政维修养护的标准定额，影响的人材机
    async getSyxSzsswxyhzxzxDe22Rcj(constructId, unitId, deId){
        let result = [];
        let de = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
        let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
        let szssMediumRepair = setting.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);
        let isTrue = unitProject.constructMajorType === "2025-SZSS-DEX"
            && unitProject.deLibrary === "2025-SZSS-DEX"
            && de.libraryCode === "2025-SZSS-DEX"
            && ObjectUtils.isNotEmpty(de.standardId)
            && szssMediumRepair;
        if (isTrue) {
            let rcjKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
            let rcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
            result = rcjs.filter(rcj =>
                rcj.libraryCode === "2025-SZSS-DEX" &&
                (rcj.kind === 1 || rcj.kind === 3) &&
                !!rcj.standardId &&
                rcj.isSupplement != 1)
            ;
        }
        return result;
    }

    /**
     * 删除中修换算信息
     * 前提定额是：22定额标准市政工程专业，且主定额册是“河北省建设工程消耗量标准（2025）-市政设施维修养护工程”的市政维修养护的标准定额
     */
    async deleteInfoForSzsswxyhzxzx22(deLine){
        let {constructId, singleId, unitId, sequenceNbr} = deLine;
        let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, sequenceNbr);
        de.conversionInfo = de.conversionInfo || [];
        let lengthBefore = de.conversionInfo.length;
        de.conversionInfo = de.conversionInfo.filter(item => item.source != ConversionInfoUtil.SZSSWXYHZXZX22);
        let lengthAfter = de.conversionInfo.length;

        if(lengthBefore > lengthAfter){
            // 执行换算换算信息
            await this.executeConversionInfo(de.constructId, de.singleId, de.unitId, de.sequenceNbr);
        }
    }
}

GljConversionInfoService.toString = () => '[class GljConversionInfoService]';
module.exports = GljConversionInfoService;
