<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-10-20 14:22:58
 * @LastEditors: sunchen
 * @LastEditTime: 2025-02-20 11:36:38
-->
<template>
  <div class="standard-type-table">
    <vxe-table
      ref="vexTable"
      :class="[
        'standard-type-table',
        props.isSetStandard ? 'table-edit-common' : '',
      ]"
      border
      height="auto"
      :scroll-y="{ enabled: false }"
      :data="tableData"
      keep-source
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :column-config="{ resizable: true }"
      @current-change="currentChangeEvent"
      @edit-closed="editClosedEvent"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :cell-class-name="selectedClassName"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData, null, ['val']);
        }
      "
    >
      <vxe-column :min-width="columnWidth(80)" title="序号" >
        <template #default="{ row, $rowIndex }">
          {{ $rowIndex + 1 }}
        </template>
      </vxe-column>
      <vxe-column field="type" title="类型"  :min-width="columnWidth(80)"> </vxe-column>
      <vxe-column
        field="val"
        title="换算处理"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
        :min-width="columnWidth(120)"
      >
        <template #edit="{ row }">
          <vxe-input
            v-model="row.val"
            type="text"
            placeholder="请修改换算处理值"
            @keyup="
              row.val = (row.val.match(/\d{0,8}(\.\d{0,2}|100)?/) || [''])[0]
            "
          ></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import api from '@gongLiaoJi/api/projectDetail';
import { onMounted, ref, watch } from 'vue';
import csProject from '@gongLiaoJi/api/csProject.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick';
import {columnWidth} from "@gongLiaoJi/hooks/useSystemConfig.js";
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();

const props = defineProps(['currentInfo', 'isSetStandard']);
const emits = defineEmits(['saveData']);

let tableData = ref([]);
const projectStore = projectDetailStore();

const currentInfo = ref(null);
const vexTable = ref();

watch(
  () => props.currentInfo,
  () => {
    getDefDonversion();
  }
);

onMounted(() => {
  getDefDonversion();
});

const getDefDonversion = () => {
  if (
    props.currentInfo.kind == '06' ||
    props.currentInfo.kind == '07' ||
    props.currentInfo.kind == '08'
  ) {
    tableData.value = [];
    return;
  }
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
  };
  console.info('标准换算右侧列表参数：' + apiData);
  csProject.getDefDonversion(apiData).then(res => {
    console.log('标准换算右侧列表返回结果：', res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
      emits('saveData', res.result);
    } else {
      tableData.value = [];
    }
  });
};

// 选中单条预算书数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};

// 单元格退出编辑事件
const editClosedEvent = ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  if ($table.isUpdateByRow(row, field)) {
    row.nowChange = true;
    if (row.val == '') {
      row.val = '1';
    } else {
      row.val = Number(row.val);
    }

    if (props.isSetStandard) {
      emits('saveData', tableData.value);
    } else {
      updateDefDonversion();
    }
  }
};

const updateDefDonversion = () => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    deId: props.currentInfo?.sequenceNbr,
    donversions: JSON.parse(JSON.stringify(tableData.value)),
  };
  console.log('右侧表格数据更新参数api', apiData);
  api.updateDefDonversion(apiData).then(res => {
    console.log('右侧表格数据更新', res);
    if (res.status === 200 && res.result) {
      message.success('数据更新成功');
      emits('updateData', 1);
      getDefDonversion();
    }
  });
};
</script>

<style lang="scss" scoped>
.standard-type-table {
  width: 100%;
  height: 100% !important;
  :deep(.vxe-table) {
    width: 97% !important;
    height: 100%;
    .vxe-table--render-wrapper {
      height: 100%;
      .vxe-table--main-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        .vxe-table--body-wrapper {
          flex: 1;
          height: auto !important;
          min-height: auto !important;
        }
      }
    }
  }
  .table-edit-common {
    width: 100% !important;
  }
}
</style>
