const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");
class createWordShenHeController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 创建word分析文档
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async createWordData(args) {
        const res = await this.service.shenHeYuSuanProject.createWordShenHeService.createWordData(args);
        return ResponseData.success(res);
    }

    /**
     * 读取word文件二进制流
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async readCreateWordData(args) {
        const res = await this.service.shenHeYuSuanProject.createWordShenHeService.readCreateWordData(args);
        return ResponseData.success(res);
    }


    /**
     * 导出word文档
     */
    async downloadWordFile(args) {
        const res = await this.service.shenHeYuSuanProject.createWordShenHeService.downloadWordFile(args);
        return res;
    }


}

createWordShenHeController.toString = () => '[class createWordShenHeController]';
module.exports = createWordShenHeController;
