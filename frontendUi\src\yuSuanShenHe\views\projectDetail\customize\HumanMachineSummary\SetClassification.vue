<!--
 * @Descripttion: 人材机设置分类
 * @Author: renmingming
 * @Date: 2024-04-23 14:19:01
 * @LastEditors: renmingming
 * @LastEditTime: 2024-04-25 14:52:53
-->
<template>
  <common-modal
    className="dialog-comm resizeClass"
    title="人材机分类表设置"
    width="630"
    v-model:modelValue="props.visible"
    :mask="false"
    @close="cancel"
    @show="open"
  >
    <div class="input-box">
      <span class="name">分类表名称：</span>
      <a-input type="text" v-model:value="formData.name"></a-input>
    </div>
    <div class="type-list">
      <div class="list" v-for="item of typeList">
        <div class="title">
          <icon-font :type="item.icon"></icon-font
          ><span class="name">{{ item.title }}</span>
        </div>
        <a-checkbox-group
          v-model:value="formData[item.type]"
          @change="checkboxChange($event, item.type)"
          style="width: 100%"
          :disabled="item.type === 'rcjDetailType' && !isCheckCLF"
        >
          <a-row>
            <a-col
              :span="24"
              style="padding: 6px 0"
              v-for="opt of item.options"
            >
              <a-checkbox :value="opt.value">{{ opt.label }}</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>
    </div>
    <div class="footer-list">
      <a-checkbox-group
        @change="checkChange"
        v-model:value="checkedList"
        :options="plainOptions"
      />
      <div style="flex: 1; text-align: right">
        <a-space :size="10">
          <a-button @click="cancel">取消</a-button>
          <a-button type="primary" :loading="loading" @click="saveHandler"
            >确定</a-button
          >
        </a-space>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { reactive, ref, toRaw, computed } from 'vue';
import api from '@/api/projectDetail.js';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail';
const props = defineProps(['visible', 'asideMenuList']);
const emits = defineEmits(['update:visible', 'successCallback']);

const projectStore = projectDetailStore();
let formData = reactive({
  name: '',
  rcjType: [],
  rcjDetailType: [],
  donorMaterial: [],
  includeYuan: 0,
  includeZanGu: 0,
  outputReport: 0,
});
const open = () => {
  Object.assign(formData, {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  });
  console.log(formData);
};
let checkedList = ref([]);
const checkChange = () => {
  formData.includeYuan = Number(checkedList.value.includes(1));
  formData.includeZanGu = Number(checkedList.value.includes(2));
  formData.outputReport = Number(checkedList.value.includes(3));
};
const plainOptions = ref([
  {
    label: '包含单位为元的材料',
    value: 1,
  },
  {
    label: '包含暂估材料',
    value: 2,
  },
  {
    label: '输出报表',
    value: 3,
  },
]);
let typeList = ref([
  {
    type: 'rcjType',
    title: '费用类别',
    icon: 'icon-feiyongleibie',
    options: [
      {
        label: '人工费',
        value: 1,
      },
      {
        label: '材料费',
        value: 2,
      },
      {
        label: '机械费',
        value: 3,
      },
      {
        label: '设备费',
        value: 4,
      },
      {
        label: '主材费',
        value: 5,
      },
      {
        label: '管理费',
        value: 6,
      },
      {
        label: '利润',
        value: 7,
      },
      {
        label: '其他费',
        value: 8,
      },
    ],
  },
  {
    title: '配比类别',
    type: 'rcjDetailType',
    icon: 'icon-peibileibie',
    options: [
      {
        label: '普通材料',
        value: 1,
      },
      {
        label: '现浇砼',
        value: 2,
      },
      {
        label: '商品砼',
        value: 3,
      },
      {
        label: '现浇砂浆',
        value: 4,
      },
      {
        label: '商品砂浆',
        value: 5,
      },
      {
        label: '其他配比',
        value: 6,
      },
    ],
  },
  {
    title: '供货方式',
    type: 'donorMaterial',
    icon: 'icon-gonghuofangshi',
    options: [
      {
        label: '自行采购',
        value: 1,
      },
      {
        label: '甲方供应',
        value: 2,
      },
      {
        label: '甲定乙供',
        value: 3,
      },
    ],
  },
]);

/**
 * 获取对应type的所有value
 * @param {*} type
 */
const getTypeValues = type => {
  const info = typeList.value.find(item => item.type === type);
  if (!info) return [];
  return info.options.map(item => item.value);
};
/**
 * 是否选中材料费
 */
const isCheckCLF = computed(() => {
  return formData.rcjType.includes(21);
});
const checkboxChange = (checkVal, type) => {
  const clfVal = 21; // 材料费val
  if (type === 'rcjType') {
    // 选中材料费，配比类别全选
    formData.rcjDetailType = checkVal.includes(clfVal)
      ? getTypeValues('rcjDetailType')
      : [];
  }
  if (type === 'rcjDetailType' && !checkVal.length) {
    const index = formData.rcjType.findIndex(item => item === clfVal);
    if (index >= 0) formData.rcjType.splice(index, 1);
  }
};

/**
 * 默认全部选中
 */
const defaultAllCheck = () => {
  ['rcjDetailType', 'rcjType', 'donorMaterial'].forEach(item => {
    formData[item] = getTypeValues(item);
  });
};

const getConfigList = () => {
  api.getRcjUnitClassification().then(res => {
    console.log(res);
    const { code, result } = res;
    if (code === 200) {
      typeList.value.forEach(item => {
        item.options = result[item.type].map(item => {
          return {
            label: item.name,
            value: item.kind === undefined ? item.code : item.kind,
          };
        });
      });
      defaultAllCheck();
    }
  });
};
const isValidName = () => {
  if (!formData.name) return true;
  return props.asideMenuList?.some(item => item.name === formData.name);
};
const saveHandler = () => {
  if (isValidName()) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '分类表名称不能为空,并且不能重复，请重新输入',
      confirm: () => {
        infoMode.hide();
      },
    });
    return;
  }
  api.addRcjClassificationTable(toRaw(formData)).then(res => {
    console.log(res);
    if (res.status === 200) {
      emits('successCallback');
      cancel();
    }
  });
};
const cancel = () => {
  emits('update:visible', false);
};

getConfigList();
</script>
<style lang="scss" scoped>
.footer-list {
  margin-top: 19px;
  display: flex;
  align-items: center;
}
.type-list {
  padding: 12px 0;
  border: 1px solid #d9d9d9;
  display: flex;
  .list {
    width: 33.33%;
    padding: 0 17px;
    border-right: 1px solid #eaeaea;
    &:last-child {
      border-right: none;
    }
    .title {
      display: flex;
      align-items: center;
      padding-bottom: 10px;
      .name {
        padding-left: 4px;
        font-size: 12px;
        color: #287cfa;
      }
    }
  }
}
.input-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .name {
    white-space: nowrap;
    font-size: 14px;
    color: #000000;
  }
}
</style>
