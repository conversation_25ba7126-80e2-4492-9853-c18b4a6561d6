<template>
  <div class="self-check">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.visible"
      title="切换计税方式"
      :mask="spinning"
      :lockView="false"
      :lockScroll="false"
      width="450"
      @close="emits('update:visible', false)"
      :loading="loading"
    >
      <div class="content">
        <p class="taxMode">
          <span>
            计税方式：
          </span>
        <p>
          {{ Number(inputData.taxCalculationMethod)===0?'一般计税法':'简易计税法' }}
        </p>
        </p>
        <p
          class="taxModeFile"
          v-if="Number(inputData.taxCalculationMethod)===0&&Number(store.deType)===12"
        >
          <span>
            税改文件：
          </span>

          <a-select
            v-model:value="inputData.taxReformDocumentsId"
            placeholder="请选择税改文件"
          >
            <a-select-option
              :value="item.code"
              v-for="item in lists.fileList"
            >{{ item.value }}</a-select-option>
          </a-select>
        </p>
        <div class="info">
          <span><icon-font
              type="icon-querenshanchu"
              class="iconFont"
            ></icon-font></span>
          <div>
            <p>
              1. {{ Number(store.deType)===12?'修改会调整进项税、增值税、附加税计算方式':'人材机基价将随计税方式自动切换' }}
            </p>
            <p>
              2. {{ Number(store.deType)===12?'修改会调整费用汇总页面数据':'【取费表】中各项费率将随费用条件刷新' }}
            </p>
          </div>
        </div>
        <p class="footer">
          <a-button
            class="cancel"
            @click="emits('update:visible', false)"
          >取消</a-button>
          <a-button
            type="primary"
            html-type="submit"
            @click="saveData"
          >确定</a-button>
        </p>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive, ref, defineEmits, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import infoMode from '@/plugins/infoMode.js';
import csProject from '@/api/csProject';
const deOutputTaxRate = ref(); //默认销项税费率
const deAdditionalTaxRate = ref(); //默认附加税费税率
const deSimpleRate = ref(); //默认税率
const store = projectDetailStore();
const form = ref();
const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'refreshTableList']);
let oldValues = {}; //查询到默认初始数据
let loading = ref(false);
let lists = reactive({
  fileList: [], //文件列表
});
let inputData = reactive({});
const init = () => {
  lists = reactive({
    fileList: [], //文件列表
  });
  inputData = reactive({
    taxCalculationMethod: null, //计税方式
    taxReformDocumentsId: null, //文件
    constructId: store.currentTreeGroupInfo?.constructId,
  });
  getTaxMethods();
};
watch(
  () => props.visible,
  val => {
    if (val) {
      init();
    }
  }
);

const getTaxMethods = () => {
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  let key_value;
  feePro.getTaxCalculation(apiData).then(res => {
    console.log('res================getTaxCalculation', res, store.taxMade);
    if (res.status === 200) {
      oldValues = res.result;
      res.result &&
        res.result.taxReformDocumentsOption &&
        res.result.taxReformDocumentsOption.map(item => {
          key_value = getKeyValue(item);
          lists.fileList.push(key_value);
        });
      store.taxMade = res.result?.taxCalculationMethod;
      inputData.taxCalculationMethod = res.result?.taxCalculationMethod + '';
      inputData.taxReformDocumentsId = res.result?.taxReformDocumentsId;
    }
  });
};
const getKeyValue = item => {
  let key = Object.keys(item)[0];
  let value = Object.values(item)[0];
  return { value: value, code: key };
};

const saveData = () => {
  let apiData = {
    taxCalculationMethod:
      Number(inputData.taxCalculationMethod) === 0 ? '1' : '0',
    taxReformDocumentsId: inputData.taxReformDocumentsId,
    constructId: inputData.constructId,
  };
  loading.value = true;
  console.log('更新计税方式数据传参', apiData);
  feePro
    .updateTaxCalculationMethod(apiData)
    .then(res => {
      console.log('更新计税方式数据', apiData, res);
      if (res.status === 200) {
        store.taxMade = apiData.taxCalculationMethod;
      }
    })
    .finally(() => {
      emits('update:visible', false);
      emits('refreshTableList');
      loading.value = false;
    });
};
</script>
<style lang="scss" scoped>
::v-deep .ant-button {
  margin: auto !important;
}
::v-deep .ant-select {
  text-align: initial !important;
}
::v-deep .ant-form-item .ant-upload {
  width: 100% !important;
  button {
    width: 100% !important;
    text-align: left !important;
    color: #c0c0c0;
  }
}
::v-deep .ant-input-number {
  width: 100% !important;
}
::v-deep .ant-form-item-with-help .ant-form-item-explain {
  text-align: left;
}
.content {
  margin-top: 15px;
  font-size: 14px;
  text-align: center;
  .taxMode,
  .taxModeFile {
    padding-right: 30px;
    display: flex;
    height: 30px;
    line-height: 30px;
    span {
      width: 100px;
    }
    p {
      width: 270px;
      text-align: left;
      height: 35px;
      line-height: 35px;
      margin: 0;
      padding-left: 10px;
      color: gray;
      border: 1px solid #e7e7e7;
    }
    .ant-select,
    .ant-select-selector {
      width: 270px;
    }
  }
  .info {
    display: flex;
    padding: 0 0 0 20px;
    margin-top: 30px;

    span {
      width: 30px;
    }
    div {
      text-align: left;
      margin-left: 5px;
    }
  }
  .footer {
    margin-top: 10px;
    .cancel {
      margin: 0 30px 0 150px;
    }
  }
}
</style>
