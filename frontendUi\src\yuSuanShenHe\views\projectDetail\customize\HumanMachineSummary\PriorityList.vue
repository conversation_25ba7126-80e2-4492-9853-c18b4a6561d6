<!--
 * @Descripttion:批量载价
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-01-08 16:47:55
-->
<template>
  <div class="priority">
    <span>载价优先级</span>
    <a-form class="selectList">
      <a-form-item v-for="(item, index) in priorityData" :key="index">
        <a-select
          v-model:value="priorityData[index]"
          placeholder="请选择"
          @change="priorityChange($event, item, index)"
          :options="
            priorityData[index] !== '空'
              ? selectData.filter(i => i.value !== priorityData[index])
              : selectData
          "
          :fieldNames="{ label: 'value', value: 'value' }"
        >
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>
<script setup>
import {
  ref,
  watch,
  onMounted,
  getCurrentInstance,
  reactive,
  inject,
} from 'vue';
const props = defineProps(['priorityData']);
let priorityData = reactive(['信息价', '市场价', '推荐价']);
const emits = defineEmits(['getFinList']);
const selectData = [
  {
    value: '信息价',
  },
  {
    value: '市场价',
  },
  {
    value: '推荐价',
  },
  {
    value: '空',
  },
];
let cityName = ref(null); //默认展示的市区
const priorityChange = (eve, item, idx) => {
  //载价优先级变化
  if (eve !== '空') {
    let nowList = JSON.parse(JSON.stringify(priorityData));
    nowList.splice(idx, 1);
    const arr = ['信息价', '市场价', '推荐价'];
    let isRepeat = nowList.includes(eve);
    let target = nowList.find(t => t !== eve && t !== '空');
    let newT = arr.filter(a => a !== eve && a !== target);
    isRepeat &&
      priorityData.map((a, i) => {
        if (a === eve && i !== idx) {
          priorityData[i] = newT[0];
        }
      });
  }

  let priorty = [];
  priorityData.map(item => {
    item === '信息价'
      ? priorty.push(1)
      : item === '市场价'
      ? priorty.push(2)
      : item === '推荐价'
      ? priorty.push(3)
      : priorty.push(0);
  });
  let data = {
    priorityData,
    priorty,
  };
  console.log('askfjaflsd-0--------------priorty', priorty);
  emits('getFinList', data);
};

onMounted(() => {
  console.log('ijashiodaiod', props.priorityData);
  priorityData = props.priorityData;
});
</script>
<style lang="scss" scoped>
.priority {
  height: 30px;
  // width: 70%;
  width: 600px;
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  span {
    font-weight: 700;
  }
  .selectList {
    width: 535px;
    display: flex;
    justify-content: space-around;
    position: relative;

    .ant-form-item {
      width: 20%;
      :deep(.ant-select .ant-select-selector) {
        border-radius: 5px;
      }
    }
    :deep(.ant-row) {
      position: relative;
    }
    :deep(.ant-row):nth-of-type(1)::after,
    :deep(.ant-row):nth-of-type(2)::after {
      content: '————>';
      right: -70px;
      position: absolute;
      color: #d9d9d9;
    }
  }
}
</style>
