<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-05 10:33:29
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-27 10:33:54
 * @Introduce:
-->
<template>
  <common-modal
    v-model:modelValue="showType.pmVisible"
    className="dialog-comm"
    title="项目匹配"
    @close="cancelModal"
    width="800"
    height="700"
    :loadingModal="loadingModal"
  >
    <a-row>
      <a-col :span="24">
        <vxe-table
          class="pmtable"
          ref="PMTable"
          :row-config="{ isCurrent: true }"
          :tree-config="{
            transform: true,
            rowField: 'id',
            parentField: 'parentId',
            expandAll: true,
            showIcon: true,
            indent: 30,
          }"
          :data="projectMatchData"
          @cell-click="STcellClick"
        >
          <vxe-column field="ssName" title="送审" tree-node></vxe-column>
          <vxe-column field="sdName" title="审定" tree-node></vxe-column>
        </vxe-table>
      </a-col>
    </a-row>
    <div class="submissionTtemsTitle">匹配送审项:</div>
    <a-row>
      <a-col :span="14">
        <vxe-table
          class="pmtable"
          ref="STTable"
          :row-config="{ isCurrent: true }"
          :tree-config="{
            transform: true,
            rowField: 'ssId',
            parentField: 'parentId',
            expandAll: true,
            showIcon: false,
          }"
          :data="submissionData"
          :cell-class-name="cellClassName"
          @cell-dblclick="STcellDblclick"
        >
          <vxe-column field="ssName" title="送审" tree-node></vxe-column>
        </vxe-table>
      </a-col>
      <a-col :span="10">
        <div class="screen">
          <a-checkbox
            @change="
              screenPressEnter(
                isFilterByCondition ? screenPressInput : '',
                true
              )
            "
            v-model:checked="isFilterByCondition"
            >按条件过滤</a-checkbox
          >
          <div class="query">
            <label for="">名称含有</label>
            <a-input
              style="width: 200px"
              placeholder="请输入"
              v-model:value="screenPressInput"
              :disabled="!isFilterByCondition"
              @pressEnter="screenPressEnter($event)"
            />
          </div>
        </div>
      </a-col>
    </a-row>
    <div class="aciton">
      <a-button type="primary" @click="submitData(false)">确定</a-button>
      <a-button style="margin-left: 10px" type="info" @click="cancelModal"
        >取消</a-button
      >
    </div>
  </common-modal>
  <prompted
    v-model:visible="isShowModel"
    title="系统提示"
    descTextLeft="项目匹配："
    descTextRight="当前审定已经匹配，是否要取消原匹配重新执行？"
    :isCancel="true"
    @determine="determine"
    @cancel="isShowModel = false"
  ></prompted>
</template>

<script setup>
import {
  getCurrentInstance,
  onMounted,
  computed,
  reactive,
  toRaw,
  watch,
  ref,
  useAttrs,
  nextTick,
} from 'vue';
import { ipcApiRoute } from '@/api/main';
import prompted from './prompted.vue';
import budgetReview from '../../api/budgetReview';
import { message } from 'ant-design-vue';
const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const emit = defineEmits(['update:pmVisible']);
const isFilterByCondition = ref(true);
const PMTable = ref();
const STTable = ref();
const searchQuery = ref(null);
const isShowModel = ref(false);
const queryList = ref([]);
const screenPressInput = ref('');

const showType = defineProps({
  pmVisible: {
    type: Boolean,
    default: false,
  },
  constructId: {
    type: String,
    default: '',
  },
  matchingSettings: {
    type: Array,
    default: [],
  },
});
const projectMatchData = ref([]);
const projectMatchDataCopy = ref([]);
const submissionData = ref([]);
const submissionDataCopy = ref([]);
const submissionNode = ref(null);
const projectMatchNode = ref(null);
const cellClassName = ({ $columnIndex, column, row }) => {
  if (row.identifying) {
    return 'sh-code-color';
  }
};
onMounted(() => {
  getprojectMatchList(true);
});
// 获取项目匹配列表
const getprojectMatchList = isFirst => {
  $ipc
    .invoke(ipcApiRoute.shQueryDetail, { constructId: showType.constructId })
    .then(function (response) {
      console.log(response, '项目匹配列表');
      if (response.status === 200) {
        projectMatchData.value = response.result.list;
        if (isFirst) {
          projectMatchDataCopy.value = response.result.list;
        }
        submissionData.value = response.result.ssh;
        submissionDataCopy.value = response.result.ssh;
        nextTick(() => {
          PMTable.value.setAllTreeExpand(true);
          STTable.value.setAllTreeExpand(true);
        });
      } else {
        message.error(response.message);
      }
    })
    .finally(() => {});
};
// 送审list单元格双击
const STcellDblclick = (val, determine) => {
  submissionNode.value = val;
  console.log(
    '111111111111',
    val.row,
    projectMatchNode.value,
    val.row.ssId === projectMatchNode.value.sdId
  );
  console.log('222222222222', projectMatchNode.value.ssId && !determine);
  // if (val.row.ssId === projectMatchNode.value.sdId) {
  //   return;
  // }
  if (projectMatchNode.value.ssId && !determine) {
    isShowModel.value = true;
    return;
  }
  let formData = {
    detailId: projectMatchNode.value.sdId,
    detailLevel: projectMatchNode.value.levelType,
    matchingId: val.row.ssId,
    matchLevel: val.row.levelType,
    constructId: showType.constructId,
  };
  console.log('formData', formData);
  budgetReview['bindingProRelation'](formData)
    .then(function (response) {
      console.log('response', response);
      if (response.status === 200) {
        getprojectMatchList();
      } else {
        message.error(response.message);
      }
    })
    .finally(err => {
      console.log(err, 'err');
    });
};
// 送审list单元格单击
const STcellClick = val => {
  projectMatchNode.value = val.row;
};
const cancelModal = () => {
  // emit('update:pmVisible'); //关闭弹框
  submitData(true);
};
let loadingModal = ref(false);
const submitData = isCancel => {
  console.log(projectMatchData.value, '确认——列表');
  console.log(projectMatchDataCopy.value, '取消——列表');
  let formData = {
    constructId: showType.constructId,
    list: JSON.stringify(
      isCancel ? projectMatchDataCopy.value : projectMatchData.value
    ),
    matchingSettings: showType.matchingSettings.join(','),
  };
  console.log(formData, '提交项目匹配列表入参');
  loadingModal.value = true;

  budgetReview['shSaveDetail'](formData)
    .then(function (response) {
      if (response.status === 200) {
        emit(
          'pmSubmitData',
          response.result ? response.result.constructId : showType.constructId
        );
      } else {
        message.error(response.message);
      }
    })
    .finally(err => {
      console.log(err, 'err');
      loadingModal.value = true;
    });
};
const filteredList = computed(() => {
  return submissionDataCopy.value.filter(item =>
    item.ssName?.toLowerCase().includes(searchQuery.value?.toLowerCase())
  );
});
const screenPressEnter = (val, type) => {
  let value = '';
  if (type) {
    value = val;
  } else {
    value = val.target.value;
  }
  if (value === '') {
    submissionData.value = submissionDataCopy.value;
  } else {
    if (isFilterByCondition.value) {
      searchQuery.value = value;
      queryList.value = [];
      recursion(JSON.parse(JSON.stringify(filteredList.value)));
      const uniqueArray = queryList.value.filter(
        (
          s => a =>
            !s.has(a.ssId) && s.add(a.ssId)
        )(new Set(queryList.value))
      );
      for (const item of filteredList.value) {
        for (const iterator of uniqueArray) {
          if (item.ssId === iterator.ssId) {
            iterator.identifying = true;
          }
        }
      }
      submissionData.value = uniqueArray;
    }
  }
  nextTick(() => {
    STTable.value.setAllTreeExpand(true);
  });
};
// 递归检索identifying
const recursion = (list, type) => {
  for (const item of list) {
    // 循环检索出来的list
    if (item.levelType === 2) {
      // 如果是2级
      queryList.value.push(item); // 如果是2级则push到数组中

      for (const iterator of item.children) {
        // 如果存在自己则将自己push到新数组中
        queryList.value.push(iterator);
      }
    }
    if (item.levelType === 3) {
      // 如果是3级
      if (item.parentId) {
        // 是否存在父级，如果存在父级则将父级list复制到新数组
        let obj = submissionDataCopy.value.filter(item1 => {
          return item1.ssId === item.parentId;
        });
        recursion(JSON.parse(JSON.stringify(obj)));
      }
    }
  }
};
const determine = () => {
  isShowModel.value = false;
  STcellDblclick(submissionNode.value, true);
};
</script>

<style lang="scss" scoped>
.submissionTtemsTitle {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0px;
}
.screen {
  height: 100%;
  border: 2px solid #e2e2e2;
  border-left: 1px;
  padding: 20px;
  .query {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    label {
      font-size: 15px;
      font-weight: bold;
    }
  }
}
.aciton {
  margin-top: 15px;
  text-align: end;
}
::v-deep(.vxe-table .sh-code-color) {
  color: red;
}
</style>
