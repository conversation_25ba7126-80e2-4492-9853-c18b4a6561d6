const Kind3RuleHandler = require("./Kind3RuleHandler");
const Kind3TypeCMathHandler = require("../math_item_handler/Kind3TypeCMathHandler");

class Kind3TypeCRuleHandler extends Kind3RuleHandler {

    async initEffectRCJ(){
        let rule = this.rule;
        this.relationDe = await this.getRelationDe(rule.libraryCode, rule.relationDeId);
        let relationDe = this.relationDe;

        let deRcjRelationList = null;
        let deRcjs = null;

        deRcjRelationList = await this.ctx.service.gongLiaoJiProject.gljBaseDeRcjRelationService.getDeRcjRelationByDeId(relationDe.sequenceNbr);
        let rcjIdList = deRcjRelationList.map(i => i.rcjId);
        //获取材料数据
        deRcjs = await this.ctx.service.gongLiaoJiProject.gljBaseRcjService.getRcjListByRcjIdList(rcjIdList);

        let deRcjMapById = deRcjs?.reduce((map, element) => {
            map.set(element.sequenceNbr, element);
            return map;
        }, new Map());

        return deRcjRelationList.map((r) => {
            let rcj = deRcjMapById?.get(r.rcjId);
            return {
                resQty: r.resQty,
                libraryCode: r.libraryCode,
                materialCode: r.materialCode,
                kind: r.kind,
                unit: rcj?.unit,
                isFyrcj: rcj?.isFyrcj
            }
        });
    }

    analysisRule(){
        let formulaStandard = this.formulaStandardizationConversion(this.rule.math);
        return [new Kind3TypeCMathHandler(this, formulaStandard)];
    }

    deCodeUpdateInfo() {
        let mathAfterCalcu = this.mathAfterCalculation(this.rule.mathHandlers[0].formatMath)
        return {redStr: `[${this.rule.relationDeCode}${mathAfterCalcu}]`, blackStr: null}
    }
}
module.exports = Kind3TypeCRuleHandler;
