const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");


class JieSuanCsxmController extends Controller{

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 修改结算相关的措施项目数据
     *   1、修改措施项目结算方式
     *   2、修改调整系数
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateJieSuanCsxmDataColl(args){
        const result = await this.service.jieSuanProject.jieSuanMeasureProjectTableService.updateJieSuanCsxmData(args);
        //费用代码记取
        await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        return ResponseData.success(result);
    }




    /**
     * 批量切换结算方式
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdList  修改后的清单集合  isAll为true 时为空
     * @param isAll  true 所有清单
     * @param settlementMethodValue  结算方式类型 1 可调措施（默认）  2 总价包干 3 按实际发生
     */
    async batchSettlementMethodUpdateController(args) {
        await this.service.jieSuanProject.jieSuanMeasureProjectTableService.batchSettlementMethodUpdate(args);
        //费用代码记取
        await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        return ResponseData.success(true);
    }


    /**
     * 措施项目层级展示查询
     * @return
     */
    async queryHierarchyCs(args) {
        //  入参返参跟分部分项查询一致
        //  hierachy显示层级: all：展示所有层级；1-n:展示n级子部门，qd：展示到清单；de：展示到定额；zc：展示到主材、设备（产品沟通暂时不做）;none不处理层级展开逻辑（默认打开和展开层级时传入）
        let {constructId, singleId, unitId, pageNum, pageSize, sequenceNbr,isAllFlag,hierachy} = args;
        const result = await this.service.jieSuanProject.jieSuanMeasureProjectTableService.showCsxmCjjg(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag, hierachy);
        return result;
    }













}

JieSuanCsxmController.toString = () => '[class JieSuanCsxmController]';
module.exports = JieSuanCsxmController;

