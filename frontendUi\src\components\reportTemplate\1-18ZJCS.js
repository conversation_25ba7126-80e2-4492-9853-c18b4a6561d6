/*
 * @Descripttion: 表1-18 总价措施项目费分析表
 * @Author: sunchen
 * @Date: 2024-07-06 10:32:39
 * @LastEditors: sunchen
 * @LastEditTime: 2024-10-22 16:17:19
 */

export const ZJCSXM18BookData = [
  {
    name: '表1-18 总价措施项目费分析表',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表', '其他'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        vAPOtb: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
            r: null,
          },
        },
        NjMt1T: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        vywspv: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        STrcb_: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        cPCDaI: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        h3yr8s: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        aE8gev: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        LGNAgA: {
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        EmSjJc: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        '-ShqIT': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        lcZqg2: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        gFFbV4: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        KysUya: {
          ff: 'SimSun',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
          },
        },
        nZSE2l: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
        },
        mZl_zb: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        '4AgI0d': {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        FJ1AKj: {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
          },
        },
        tj5hAp: {
          bd: {
            l: null,
          },
        },
        k4S2CY: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        'GZa-HH': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        ukpMLs: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        '7tmmas': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        '3uin96': {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            r: null,
          },
        },
        sn87Fm: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        '4pWGUH': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: '3uin96',
                p: '',
              },
              1: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              2: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              3: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              4: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              5: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              6: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              7: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              8: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              9: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              10: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
              11: {
                s: 'vAPOtb',
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`总价措施项目工程量清单综合单价分析表`',
                t: 1,
                s: 'FJ1AKj',
                custom: {},
                p: '',
              },
              1: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              2: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              3: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              4: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              5: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              6: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              7: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              8: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              9: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              10: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
              11: {
                s: 'FJ1AKj',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'k4S2CY',
                p: '',
              },
              1: {
                s: 'k4S2CY',
                v: '',
                p: '',
              },
              2: {
                s: 'k4S2CY',
                v: '',
                p: '',
              },
              3: {
                s: 'k4S2CY',
                v: '',
                p: '',
              },
              4: {
                s: 'k4S2CY',
                v: '',
                p: '',
              },
              5: {
                s: 'k4S2CY',
                v: '',
                p: '',
              },
              6: {
                v: '`第`+{页码}+`页 `+` 共 `+{总页数}+`页`',
                t: 1,
                s: 'GZa-HH',
                p: '',
              },
              7: {
                s: 'GZa-HH',
                v: '',
                p: '',
              },
              8: {
                s: 'GZa-HH',
                v: '',
                p: '',
              },
              9: {
                s: 'GZa-HH',
                v: '',
                p: '',
              },
              10: {
                s: 'GZa-HH',
                v: '',
                p: '',
              },
              11: {
                s: 'GZa-HH',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                p: '',
                v: '`序号`',
                t: 1,
                s: 'sn87Fm',
                custom: {},
              },
              1: {
                p: '',
                v: '`项目编码(定额编号)`',
                t: 1,
                s: 'sn87Fm',
                custom: {},
              },
              2: {
                p: '',
                v: '`项目名称`',
                t: 1,
                s: 'sn87Fm',
              },
              3: {
                p: '',
                v: '`单位`',
                t: 1,
                s: 'sn87Fm',
              },
              4: {
                p: '',
                v: '`计算基数(元)`',
                t: 1,
                s: 'sn87Fm',
                custom: {},
              },
              5: {
                v: '`费率 `',
                t: 1,
                s: 'sn87Fm',
                custom: {},
                p: '',
              },
              6: {
                p: '',
                v: '`金额(元)`',
                t: 1,
                s: 'sn87Fm',
              },
              7: {
                v: '`其中：(元)`',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              8: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              9: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              10: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              11: {
                v: '`人工单价(元/工日)`',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
            },
            4: {
              0: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              1: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              2: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              3: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              4: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              5: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              6: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
              7: {
                v: '`人工费`',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              8: {
                v: '`材料费`',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              9: {
                v: '`机械费`',
                t: 1,
                s: 'sn87Fm',
                p: '',
                custom: {},
              },
              10: {
                v: '`管理费和利润`',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              11: {
                s: 'sn87Fm',
                v: '',
                p: '',
              },
            },
            5: {
              0: {
                v: '[记录号]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              1: {
                v: '[BM]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              2: {
                v: '[MC]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'sn87Fm',
                p: '',
                custom: {},
              },
              4: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              6: {
                v: '[ZHHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              9: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              11: {
                v: '',
                t: 1,
                s: '4pWGUH',
                p: '',
              },
            },
            6: {
              0: {
                v: '安全文明施工费.[记录号]+`.`+[记录号]',
                t: 1,
                s: 'sn87Fm',
                p: '',
                custom: {},
              },
              1: {
                v: '[BM]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              2: {
                v: '[MC]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              6: {
                v: '[ZHHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              9: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              11: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
            },
            7: {
              0: {
                v: '[记录号]+1',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              1: {
                v: '[BM]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              2: {
                v: '[MC]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              6: {
                v: '[ZHHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              7: {
                v: '[RGHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              8: {
                v: '[CLHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              9: {
                v: '[JXHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              10: {
                v: '[GLFHJ]+[LRHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              11: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
            },
            8: {
              0: {
                v: '其他总价措施清单.[记录号]+1+`.`+[记录号]',
                t: 1,
                s: 'sn87Fm',
                custom: {},
                p: '',
              },
              1: {
                v: '[BM]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              2: {
                v: '[MC]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'sn87Fm',
                p: '',
                custom: {},
              },
              4: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              5: {
                v: '',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              6: {
                v: '[ZHHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              7: {
                v: '[RGHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              8: {
                v: '[CLHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              9: {
                v: '[JXHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
              },
              10: {
                v: '[GLFHJ]+[LRHJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
                custom: {},
              },
              11: {
                v: '[GRDJ]',
                t: 1,
                s: 'sn87Fm',
                p: '',
                custom: {},
              },
            },
            9: {
              0: {
                v: '',
                t: 1,
                s: '3uin96',
                p: '',
              },
              1: {
                s: '3uin96',
                v: '',
                p: '',
              },
              2: {
                s: '3uin96',
                v: '',
                p: '',
              },
              3: {
                s: '3uin96',
                v: '',
                p: '',
              },
              4: {
                s: '3uin96',
                v: '',
                p: '',
              },
              5: {
                s: '3uin96',
                v: '',
                p: '',
              },
              6: {
                s: '3uin96',
                v: '',
                p: '',
              },
              7: {
                s: '3uin96',
                v: '',
                p: '',
              },
              8: {
                s: '3uin96',
                v: '',
                p: '',
              },
              9: {
                s: '3uin96',
                v: '',
                p: '',
              },
              10: {
                s: '3uin96',
                v: '',
                p: '',
              },
              11: {
                s: '3uin96',
                v: '',
                p: '',
              },
            },
            10: {
              0: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              1: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              2: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              3: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              4: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              5: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              6: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              7: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              8: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              9: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              10: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
              11: {
                s: 'tj5hAp',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 10,
          columnCount: 12,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 0,
              endRow: 0,
              startColumn: 0,
              endColumn: 11,
            },
            {
              startRow: 1,
              endRow: 1,
              startColumn: 0,
              endColumn: 11,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 0,
              endColumn: 5,
            },
            {
              startRow: 2,
              endRow: 2,
              startColumn: 6,
              endColumn: 11,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 0,
              endColumn: 0,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 1,
              endColumn: 1,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 2,
              endColumn: 2,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 3,
              endColumn: 3,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 4,
              endColumn: 4,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 5,
              endColumn: 5,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 6,
              endColumn: 6,
            },
            {
              startRow: 3,
              endRow: 3,
              startColumn: 7,
              endColumn: 10,
            },
            {
              startRow: 3,
              endRow: 4,
              startColumn: 11,
              endColumn: 11,
            },
            {
              startRow: 9,
              endRow: 9,
              startColumn: 0,
              endColumn: 11,
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ia: 0,
            },
            3: {
              h: 50,
              hd: 0,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '安全文明施工费',
            },
            4: {
              h: 50,
              hd: 0,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '安全文明施工费',
            },
            5: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              rowType: '细节行',
              dataSourceType: '安全文明施工费',
            },
            6: {
              h: 30,
              hd: 0,
              field: '',
              parentName: '安全文明施工费',
              rowType: '细节行',
              dataSourceType: '安全文明施工费定额',
              ah: 30,
            },
            7: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '细节行',
              dataSourceType: '其他总价措施清单',
              ah: 30,
              ia: 0,
            },
            8: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              parentName: '其他总价措施清单',
              rowType: '细节行',
              dataSourceType: '其他总价措施定额',
            },
            9: {
              h: 30,
              hd: 0,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 48,
              hd: 0,
            },
            1: {
              w: 125,
              hd: 0,
            },
            2: {
              w: 165,
              hd: 0,
            },
            3: {
              w: 40,
              hd: 0,
            },
            4: {
              w: 48,
              hd: 0,
            },
            5: {
              w: 49,
              hd: 0,
            },
            6: {
              w: 53,
              hd: 0,
            },
            7: {
              w: 53,
              hd: 0,
            },
            8: {
              w: 77,
              hd: 0,
            },
            9: {
              w: 77,
              hd: 0,
            },
            10: {
              w: 77,
              hd: 0,
            },
            11: {
              w: 64,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
    },
  },
];
