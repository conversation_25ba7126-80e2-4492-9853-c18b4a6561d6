<!--
 * @Author: wangru
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: wangru
 * @LastEditTime: 2023-09-12 14:36:59
-->
<template>
  <div class="common-flex-upAndDown">
    <keep-alive>
      <div class="flex-auto">
        <item-analysis v-if="!isUnit"></item-analysis>
        <unit-analysis
          class="item"
          v-if="isUnit"
        ></unit-analysis>
      </div>
    </keep-alive>
  </div>
</template>

<script setup>
import { onMounted, ref, watch, getCurrentInstance } from 'vue';
import ItemAnalysis from './ItemAnalysis.vue';
import UnitAnalysis from './UnitAnalysis.vue';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';

const store = projectDetailStore();
let isUnit = ref(true);
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
bus.on('export-table', data => {
  if (store.componentId === 'CostAnalysis') exportTable();
});

watch(
  () => store.currentTreeInfo,
  () => {
    if (store.currentTreeInfo?.levelType === 3) {
      isUnit.value = true;
    } else {
      isUnit.value = false;
    }
  }
);
onMounted(() => {
  if (store.currentTreeInfo?.levelType === 3) {
    isUnit.value = true;
  } else {
    isUnit.value = false;
  }
});
const exportTable = () => {
  message.info('功能建设中...');
};
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
}
</style>
