
const { app: electronApp, BrowserWindow } = require('electron');
const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");


class JieSuanImportExportController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 选择性导入YJS文件
     * @param arg
     * @returns {*}
     */
    importYjsFile(arg){
        return this.service.jieSuanProject.jieSuanImportExportService.importYjsFile(arg);
    }

    /**
     * 保存导入后的YJS文件
     * @param arg
     * @returns {*}
     */
    saveImportProject(arg){
        this.service.jieSuanProject.jieSuanImportExportService.saveImportProject(arg);
        return ResponseData.success();
    }



    /**
     * 导入签证，索赔.....
     * @param arg
     * @returns {*}
     */
    importSingle(arg){
        this.service.jieSuanProject.jieSuanImportExportService.importSingle(arg);
        return ResponseData.success();
    }

    /**
     * ysf文件选择性导出
     * @param arg
     * @return {*}
     */
    exportYjsFile(arg){
        return this.service.jieSuanProject.jieSuanImportExportService.exportYjsFile(arg);

    }



}

JieSuanImportExportController.toString = () => '[class JieSuanImportExportController]';
module.exports = JieSuanImportExportController;
