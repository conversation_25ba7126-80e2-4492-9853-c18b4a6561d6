<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:00:41
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-27 15:09:45
-->
<template>
  <vxe-table
    border
    align="center"
    auto-resize
    :column-config="{ resizable: true }"
    :row-config="{ isHover: true , keyField: 'sequenceNbr'}"
    height="100%"
    :tree-config="
          {
            rowField: 'dispNo',
            children: 'childrenList',
            expandAll: false,
            reserve: true,
          }
    "
    :data="tableData"
    ref="itemTable"
    keep-source
    @edit-closed="editClosedEvent"
    class="table-edit-common"
    @cell-click="
      cellData => {
        useCellClickEvent(cellData);
      }
    "
    :edit-config="{
      trigger: 'click',
      mode: 'cell',
       beforeEditMethod({ rowIndex }) {
        if (rowIndex === tableData.length - 1) {
          //最后一行不可编辑
          return false;
        }
        return true;
      }
    }"
    :cell-class-name="selectedClassName"
  >
    <vxe-column
      field="dispNo"
      width="100"
      title="序号"
      tree-node
    ></vxe-column>
    <vxe-column
      field="projectName"
      width="180"
      title="名称"
    ></vxe-column>
    <vxe-colgroup title="送审">
      <vxe-column
        field=""
        width="180"
        title="金额(不含设备费及税金)"
      >
        <template #default="{ row }">
          <span v-if="row.ysshSysj">{{ row.ysshSysj.gczj }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field=""
        width="120"
        title="建筑面积(m、m²)"
      >
        <template #default="{ row }">
          <span v-if="row.ysshSysj">{{ row.ysshSysj.average }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field=""
        width="180"
        title="单位造价(元/m²或元/m)"
      >
        <template #default="{ row }">
          <span v-if="row.ysshSysj">{{ row.ysshSysj.unitcost }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field=""
        width="120"
        title="设备费及其税金"
      >
        <template #default="{ row }">
          <span v-if="row.ysshSysj">{{ row.ysshSysj.sbfsj }}</span>
        </template>
      </vxe-column>
    </vxe-colgroup>
    <vxe-colgroup title="审定">
      <vxe-column
        field="gczj"
        width="180"
        title="金额(不含设备费及税金)"
      ></vxe-column>
      <vxe-column
        field="average"
        width="150"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
        title="建筑面积(m、m²)"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model="row.average"
            @blur="(row.average = pureNumber(row.average, 2)), clear()"
            :disabled="row.projectName === '合计(不含设备费及税金)' ? true : false"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="unitcost"
        width="180"
        title="单位造价(元/m²或元/m)"
      ></vxe-column>
      <vxe-column
        field="sbfsj"
        width="120"
        title="设备费及其税金"
      ></vxe-column>
    </vxe-colgroup>
    <vxe-column
      field=""
      width="180"
      title="增减金额"
    >
      <template #default="{ row }">
        <span v-if="row.ysshSysj">{{ row.ysshSysj.changeTotal? row.ysshSysj.changeTotal:'0.00'}}</span>
      </template>
    </vxe-column>
    <vxe-column
      field=""
      width="180"
      title="增减比例（%）"
    >
      <template #default="{ row }">
        <span v-if="row.ysshSysj">{{ row.ysshSysj.changeRatio?row.ysshSysj.changeRatio:'0.00' }}</span>
      </template>
    </vxe-column>
  </vxe-table>
</template>
<script setup>
import { watch, ref, onMounted, reactive } from 'vue';
import { Modal } from 'ant-design-vue';
import feePro from '@/api/shApi';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: 'dispNo' });
import { pureNumber } from '@/utils/index';

const store = projectDetailStore();
const itemTable = ref();
let tableData = ref([]);
let getData = [];
let averageRow = ref();
let average = ref();
let taxMode = ref(); //1-一般计税，0-简易计税
let flag = ref(false); //修改单项建筑面积是否联动修改单位建筑面积
const getTableData = () => {
  taxMode.value = store.taxMode;
  getData = [];
  let apiData = {
    levelType: store.currentTreeInfo?.levelType,
  };
  if (store.currentTreeInfo?.levelType === 1) {
    apiData.constructId = store.currentTreeInfo?.id;
    apiData.ssConstructId = store.currentTreeInfo?.ysshConstructId;
  } else if (store.currentTreeInfo?.levelType === 2) {
    apiData.constructId = store.currentTreeGroupInfo?.constructId;
    apiData.ssConstructId = store.currentTreeGroupInfo?.ssConstructId;
    apiData.singleId = store.currentTreeInfo?.id;
    apiData.ssSingleId = store.currentTreeInfo?.ysshSingleId;
  }
  if (!apiData.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  console.log('获取造价分析', apiData);
  apiData.levelType &&
    feePro.getCostAnalysisData(apiData).then(res => {
      console.log('获取造价分析返回数据', res);
      if (res.status === 200) {
        if (store.currentTreeInfo?.levelType === 1) {
          getData = res.result.costAnalysisConstructVOList;
        } else if (store.currentTreeInfo?.levelType === 2) {
          getData = res.result.costAnalysisSingleVOList;
        }
        if (!getData) {
          tableData.value = [];
          return;
        }
        let data = getTotal(getData);
        getDsposeData(data);
      }
    });
};
const getDsposeData = data => {
  if (!data) {
    data = [];
  }
  data &&
    data.map(item => {
      const filedList = ['gczj', 'average', 'unitcost', 'sbfsj'];
      if (!item.hasOwnProperty('ysshSysj')) {
        item.ysshSysj = {};
        filedList.map(i => {
          item.ysshSysj[i] = 0;
        });
      } else {
        if (item.ysshSysj === 'null') item.ysshSysj = {};
        filedList.map(key => {
          if (
            (item.ysshSysj.hasOwnProperty(key) &&
              [undefined, NaN, null, ''].includes(item.ysshSysj[key])) ||
            !item.ysshSysj.hasOwnProperty(key)
          ) {
            item.ysshSysj[key] = '0';
          }
        });
      }
      if (
        item.projectName === '合计(不含设备费及税金)' &&
        store.currentTreeInfo?.levelType === 2
      ) {
        disposeAverge(item, false);
      } else {
        disposeAverge(item, true);
      }
      if (item.childrenList && item.childrenList.length === 0) return;
      item.childrenList && item.childrenList.length > 0
        ? getDsposeData(item.childrenList)
        : '';
    });
  tableData.value = data;
};

const disposeAverge = (item, isSetZero = true) => {
  item.average =
    Number(item.average) > 0
      ? Number(item.average).toFixed(2)
      : isSetZero
      ? '0.00'
      : '/';
  item.averageOld = item.average;
  item.unitcost =
    Number(item.average) > 0
      ? Number(item.gczj / item.average).toFixed(2)
      : item.projectName === '合计(不含设备费及税金)'
      ? '/'
      : '0.00';
  if (item.ysshSysj) {
    item.ysshSysj.unitcost =
      Number(item.ysshSysj.average) > 0
        ? Number(item.ysshSysj.gczj / item.ysshSysj.average).toFixed(2)
        : '0.00';
  }
};
const getTotal = list => {
  let totalLast = {
    ysshSysj: {
      gczj: 0,
      average: 0,
      unitcost: 0,
      sbfsj: 0,
      changeTotal: 0,
      changeRatio: 0,
    },
    gczj: 0,
    average: 0,
    unitcost: 0,
    sbfsj: 0,
  };
  if (!list) return;
  if (store.currentTreeInfo?.levelType === 2) {
    for (let key in totalLast) {
      if (key === 'ysshSysj') {
        totalLast[key] = list[key];
      } else {
        totalLast[key] = Number(list[key]).toFixed(2);
      }
    }
  } else if (store.currentTreeInfo?.levelType === 1) {
    let childrenList = [];
    let newList = list.filter(item => item.levelType === 2);
    for (let key in totalLast) {
      let flag = true; //fallse-合計的unicost可以計算
      newList.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(totalLast, key)) {
          if (key === 'ysshSysj' && item.hasOwnProperty(key)) {
            totalLast[key].gczj = item[key].gczj
              ? floatAdd(totalLast[key].gczj, item[key].gczj)
              : totalLast[key].gczj;
            return;
          }
          totalLast[key] = item[key]
            ? floatAdd(totalLast[key], item[key])
            : totalLast[key];
        }
      });
    }
    totalLast.ysshSysj.changeTotal = (
      totalLast.gczj - totalLast.ysshSysj.gczj
    ).toFixed(2);

    totalLast.ysshSysj.changeRatio = (
      (totalLast.ysshSysj.changeTotal / totalLast.ysshSysj.gczj) *
      100
    ).toFixed(2);
    if (!totalLast.ysshSysj.changeRatio) {
      totalLast.ysshSysj.changeRatio = '0.00';
    }
  }
  totalLast.projectName = '合计(不含设备费及税金)';
  totalLast.dispNo = '';
  totalLast.childrenList = [];
  if (list instanceof Array) {
    return [...list, totalLast];
  } else {
    return [list, totalLast];
  }
};

const floatAdd = (arg1, arg2) => {
  var r1, r2, m;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
};
watch(
  () => store.tabSelectName,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.levelType !== 3
    ) {
      getTableData();
    }
  }
);
watch(
  () => store.currentTreeInfo,
  () => {
    if (
      store.tabSelectName === '造价分析' &&
      store.currentTreeInfo?.levelType !== 3
    ) {
      getTableData();
    }
  }
);
onMounted(() => {
  if (
    store.tabSelectName === '造价分析' &&
    store.currentTreeInfo?.levelType !== 3
  ) {
    getTableData();
  }
});
const clear = () => {
  //清除编辑状态
  const $table = itemTable.value;
  $table.clearEdit();
};
const upDateAverage = () => {
  const $table = itemTable.value;
  let apiData = {
    levelType: averageRow.value.levelType,
    average: average.value,
    constructId: store.currentTreeGroupInfo?.constructId,
    flag: flag.value,
  };
  if (averageRow.value.levelType === 2) {
    apiData.singleId = averageRow.value.sequenceNbr;
  } else if (averageRow.value.levelType === 3) {
    findParent(tableData.value);
    apiData.singleId = parent?.sequenceNbr;
    apiData.unitId = averageRow.value.sequenceNbr;
  }
  feePro.updateCostAnalysis(apiData).then(res => {
    console.log('修改成功造价分析', res, apiData);
    if (res.status === 200) {
      averageRow.value.averageOld = averageRow.value.average;
      getTableData();
    }
  });
};
let parent = reactive(null);
const findParent = (list, tar = null) => {
  list.some(i => {
    if (i.sequenceNbr === averageRow.value.sequenceNbr) {
      parent = tar;
      return true;
    } else if (!parent && i.sequenceNbr !== averageRow.value.sequenceNbr) {
      i.childrenList ? findParent(i.childrenList, i) : '';
    }
    return true;
  });
};
let expandedList = ref();
const getExpandList = list => {
  list.map(item => {
    expandedList.value.push(item);
    item.childrenList ? getExpandList(item.childrenList) : '';
  });
};
const editClosedEvent = ({ row, column }) => {
  const $table = itemTable.value;
  const field = column.field;
  averageRow.value = row;
  average.value = row[field];
  if (!row[field]) {
    row[field] = 0;
    average.value = 0;
  }
  if (Number(row.averageOld) === Number(row.average)) {
    row.average = Number(row.average).toFixed(2);
  }
  // 判断单元格值是否被修改
  if (
    $table.isUpdateByRow(row, field) &&
    Number(row.averageOld) !== Number(row.average)
  ) {
    flag.value = false;
    console.log('row-isUpdateByRow', row);
    const hasChildren =
      row.childrenList && row.childrenList.length > 0 ? true : false;
    averageRow.value.levelType === 2 && hasChildren
      ? Modal.confirm({
          title: '是否联动修改其下所有项目层级建筑面积数据？',
          zIndex: '99999',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            flag.value = true;
            expandedList.value = [row];
            getExpandList(row.childrenList);
            itemTable.value.setTreeExpand(expandedList.value, true);
            upDateAverage();
          },
          onCancel() {
            upDateAverage();
          },
        })
      : upDateAverage();
  }
};
</script>
<style lang="scss" scoped>
.table-content {
  max-width: 95%;
  max-height: calc(100% - 40px);
}
</style>
