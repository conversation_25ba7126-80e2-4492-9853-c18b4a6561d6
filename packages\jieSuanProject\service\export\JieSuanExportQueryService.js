const { Service } = require('../../../../core');
const { ResponseData } = require('../../../../electron/utils/ResponseData');
const JieSuanExportSheetNameEnum = require('../../enum/JieSuanExportSheetNameEnum');
const TaxCalculationMethodEnum = require('../../../../electron/enum/TaxCalculationMethodEnum');
const { PricingFileFindUtils } = require('../../../../electron/utils/PricingFileFindUtils');
const { ObjectUtils } = require('../../../../electron/utils/ObjectUtils');
const UtilsPs = require('../../../../core/ps');
const { JieSuanExcelUtil } = require('../../utils/JieSuanExcelUtil.js');
const { ZhaoBiaoUtil } = require('../../../../electron/utils/ZhaoBiaoUtil.js');
const { ConvertUtil } = require('../../../../electron/utils/ConvertUtils');
const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const BranchProjectLevelConstant = require('../../../../electron/enum/BranchProjectLevelConstant');
const JieSuanSingleTypeEnum = require('../../enum/JieSuanSingleTypeEnum');
const {
  app: electronApp,
  dialog, shell, BrowserView, Notification,
  powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const { ExcelUtil } = require('../../../../electron/utils/ExcelUtil');
const OtherProjectDayWorkRcjConstant = require('../../../../electron/enum/OtherProjectDayWorkRcjConstant');

class JieSuanExportQueryService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  //展示报表查看的表名列表
  async jieSuanShowExportHeadLine(itemLevel, args) {
    let { constructId, singleId, unitId } = args;
    let result = [];

    //定义一个存放数据值和cell的对象
    function HeadLineList(desc, baoBiaoList) {
      this.desc = desc;//存放大标题
      this.baoBiaoList = baoBiaoList;
    }

    //如果是一般计税方式
    // itemLevel = "single";
    //存放大栏目下的表的表名
    let changYongBaoBiao = JieSuanExportSheetNameEnum.常用报表.filter(function(element) {
      if (element.projectLevel == itemLevel)
        return element;
    });
    let guiFanBaoBiao13 = JieSuanExportSheetNameEnum.规范报表.filter(function(element) {
      if (element.projectLevel == itemLevel)
        return element;
    });
    args['levelType'] = 1;
    // 计税方式获取
    let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
    let simple = false;
    //简易计税
    if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
      simple = true;
    }

    //是合同内还是合同外   单项  单位有
    let htn = false;

    if (itemLevel == 'project') {
      //地区获取
      let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
      // //是不是沧州
      // let isCz = projectObjById.ssCityName.includes('沧州') ? true : false;
      // if (!isCz) {
      //   changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isEmpty(cy.city));
      //   guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isEmpty(gf.city));
      // }

    } else {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
      if (ObjectUtils.isNotEmpty(singleProject.originalFlag) && singleProject.originalFlag == true) {
        htn = true;
      }
    }


    if (itemLevel == 'unit') {
      //如果是合同内
      if (htn) {
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag == 1);
        guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isNotEmpty(gf.htFlag) && gf.htFlag == 1);
        if (simple) {
          guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isEmpty(gf.jsfs));
        }

      } else {
        changYongBaoBiao = changYongBaoBiao.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag == 0);
        guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isNotEmpty(gf.htFlag) && gf.htFlag == 0);
        if (simple) {
          guiFanBaoBiao13 = guiFanBaoBiao13.filter(gf => ObjectUtils.isEmpty(gf.jsfs));
        }
      }
    }

    if (itemLevel == 'single') {
      if (htn){
        //合同内 【单项】
        guiFanBaoBiao13=guiFanBaoBiao13.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag == 1);
      }else {
        guiFanBaoBiao13=guiFanBaoBiao13.filter(cy => ObjectUtils.isNotEmpty(cy.htFlag) && cy.htFlag == 0);
      }
    }
    let headLineList1 = new HeadLineList('常用报表', changYongBaoBiao);
    let headLineList2 = new HeadLineList('13规范报表', guiFanBaoBiao13);
    //是单项 同时是合同外  不要单项的常用报表
    if (itemLevel == 'single' && !htn) {
      //合同外 【单项】
    } else {
      result.push(headLineList1);

    }
    result.push(headLineList2);
    return result;
  }


  async jieSuanShowSheetStyle(itemLevel, lanMuName, sheetName, args) {
    let { constructId, singleId, unitId } = args;
    args.levelType = 1;
    let guiFanBiao = this.getProjectRootPath() + '\\excelTemplate\\jiesuan';
    let changyongBiao = this.getProjectRootPath() + '\\excelTemplate\\jiesuan';
    let temp = this.getProjectRootPath() + '\\excelTemplate\\jiesuan\\一般计税\\常用报表\\合同内单项工程\\单位工程\\单位工程.xlsx';

    //计税方式   simple表示简易计税
    let simple = false;
    // 计税方式获取
    let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
    //简易计税
    if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
      simple = true;
      guiFanBiao = guiFanBiao + '\\简易计税';
      changyongBiao = changyongBiao + '\\简易计税';
    } else {
      guiFanBiao = guiFanBiao + '\\一般计税';
      changyongBiao = changyongBiao + '\\一般计税';
    }


    //合同内外
    let htn = false;
    //单项类型
    let singleType;
    //如果是工程项目  不需要考虑合同内外
    if (itemLevel != 'project') {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
      if (ObjectUtils.isNotEmpty(singleProject.originalFlag) && singleProject.originalFlag == true) {
        htn = true;
        singleType = '\\合同内单项工程';

      } else {
        //如果是合同外，并且是单项、单位  需要知道该单项的类型（签证索赔）
        let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
        singleType = '\\' + this.findJieSuanSingleTypeEnumByCode(singleProject.type).value;
      }
    }
    //组装Excel加载路径
    if (itemLevel == 'project') {
      guiFanBiao = guiFanBiao + '\\13规范报表\\工程项目.xlsx';
      changyongBiao = changyongBiao + '\\常用报表\\工程项目.xlsx';
    } else if (itemLevel == 'single') {
      guiFanBiao = guiFanBiao + '\\13规范报表' + singleType + '\\单项工程.xlsx';
      changyongBiao = changyongBiao + '\\常用报表' + singleType + '\\单项工程.xlsx';
    } else if (itemLevel == 'unit') {
      guiFanBiao = guiFanBiao + '\\13规范报表' + singleType + '\\单位工程\\单位工程.xlsx';
      changyongBiao = changyongBiao + '\\常用报表' + singleType + '\\单位工程\\单位工程.xlsx';
    }
    let loadPath = '';
    if (lanMuName == '13规范报表') {
      //TODO 与产品沟通  合同内的 1-4表样式是一样的   但是在处理13规范报表的时候样式一直不正确， 临时使用合同内的1-4表
      if (sheetName == '表1-4 总价措施项目清单与计价表'){
        loadPath = temp;
      }else {
        loadPath = guiFanBiao;
      }
    }
    if (lanMuName == '常用报表') {
      loadPath = changyongBiao;
    }
    let workbook = await JieSuanExcelUtil.readToWorkBook(loadPath);
    args['workbook'] = workbook;
    //simple =true 简易计税
    args['jsfs'] = simple;
    //合同内
    args['htn'] = htn;
    try {
      await this.switchWorkSheet(itemLevel, lanMuName, workbook.getWorksheet(sheetName), args);
    } catch (e) {
      console.log(e.stack);
      console.log('报表填充数据异常');
    }
    let result;
    try {
      // if (sheetName == '表1-4 总价措施项目清单与计价表' && loadPath.includes('13规范报表')) {
      //   if (htn) {
      //     sheetName = sheetName + '14htn';
      //   } else {
      //     sheetName = sheetName + '14htw';
      //   }
      // }
      result = await JieSuanExcelUtil.findCellStyleList(workbook.getWorksheet(sheetName));
    } catch (e) {
      console.error(e.stack);
      console.log('报表填充数据异常');
    }
    return result;
  }


  async switchWorkSheet(projectType, lanMuName, worksheet, args) {
    let { constructId, unitId, singleId, workbook, jsfs, htn } = args;
    let unit = {};
    if (constructId != null && singleId != null && unitId != null) {
      unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    }
    if (lanMuName == '常用报表') {
      if (projectType == 'project') {
        switch (worksheet.name) {
          //工程项目层级
          case '封面2 招标控制价（标底）':
            let param1 = {};
            //是招标还是投标 1招标 2投标
            param1.ifZbOrTb = 1;

            //工程项目ID
            param1.constructId = constructId;
            let constructProjectJBXX = await this.getconstructProjectJBXX(param1);
            await ZhaoBiaoUtil.writeDataToCover1(constructProjectJBXX, worksheet);
            let headArgs = {};
            headArgs['headStartNum'] = 1;
            headArgs['headEndNum'] = 9;
            headArgs['titlePage'] = true;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgs);
            break;

          case '表1-1 建设项目费用汇总表':
            let array = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary(args);
            await this.writeDataToUnitFbfx(array, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;

          case '表1-2 建设项目费用汇总表(沧州)':
            let array1_2 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_2(args);
            await this.writeDataToUnitFbfx(array1_2, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
          case '表1-3 工程项目竣工结算汇总表':
            let array1_3 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_3(args);
            await this.writeDataToUnitFbfx(array1_3, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
          case '表1-4 工程项目竣工结算汇总表(沧州)':
            let array1_4 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_4(args);
            await this.writeDataToUnitFbfx(array1_4, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
        }
      }
      if (projectType == 'single') {
        switch (worksheet.name) {
          //单项工程层级
          case '表1-5 单项工程费汇总表':
            let summaryData = await this.getSingleSummaryData(constructId, singleId);
            await ZhaoBiaoUtil.writeDataToSheet5(summaryData, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;

          case '表1-1 单项工程竣工结算汇总表':
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectSingle1_1(args);
            await this.writeDataToUnitFbfx(array1_1, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);

        }
      }
      if (projectType == 'unit') {
        let headArgsBot = {};
        headArgsBot['headStartNum'] = 1;
        headArgsBot['headEndNum'] = 9;
        headArgsBot['titlePage'] = false;
        let rcjBaoBiao  = {};
        rcjBaoBiao['headStartNum'] = 1;
        rcjBaoBiao['headEndNum'] = 3;
        rcjBaoBiao['titlePage'] = false;
        switch (worksheet.name) {


          case '表1-1 单位工程竣工结算汇总表':
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_1(args);
            await this.writeDataToUnitFbfx(array1_1, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;
          case '表1-8 单位工程人材机汇总表':

            let cgrcj = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitCgRcjHzb(args);
            await this.writeDataToUnitRcj(cgrcj, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;

          case '表1-9 工程议价材料表':
            let gcyjclb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitCgGcclyjb(args);
            await this.writeDataToUnitRcj(gcyjclb, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);

            break;
          case '表1-10 人材机调整明细表-1':
            let rcjtzmxb1 = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjtzmx1(args);
            await this.writeDataToUnitRcj(rcjtzmxb1, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);

            break;
          case '表1-11 人材机调整明细表-2':
            let rcjtzmxb2 = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjtzmx2(args);
            await this.writeDataToUnitRcj(rcjtzmxb2, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);

            break;
          case '表1-12 主材汇总表':
            let zchzb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitZchzb(args);
            await this.writeDataToUnitRcj(zchzb, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);

            break;
          case '表1-13 甲方供应材料表':
            let jfgyclb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitJgclb(args);
            await this.writeDataToUnitRcj(jfgyclb, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);

            break;

          case '表1-14 甲供材料汇总表':
            let jgclhzb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitJgclhzb(args);
            await this.writeDataToUnitRcj(jgclhzb, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);

            break;
          case '表1-1 单位工程人材机汇总表':
            let rcjhzbhtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjhzbhtw(args);
            await this.writeDataToUnitRcj(rcjhzbhtw, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;
          case '表1-2 单位工程人材机价差汇总表':
            let rcjjchzbhtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjjchzbhtw(args);
            await this.writeDataToUnitRcj(rcjjchzbhtw, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;

          case '表1-3 人材机价差调整表':
            let rcjcjtzbhtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitRcjcjtzbhtw(args);
            await this.writeDataToUnitRcj(rcjcjtzbhtw, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;
          case '表1-4 主材汇总表':
            let zchzbHtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitZchzbHtw(args);
            await this.writeDataToUnitRcj(zchzbHtw, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;

          /*case '表1-12 主材汇总表':
            await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;*/
          //单位工程层级
          case '表1-2 分部分项合同清单工程量及结算工程量对比表':
            await this.getUnitProjectFbfxHtQdQuantityDbBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          //单位工程层级
          case '表1-3 分部分项工程和单价措施项目清单与计价表':
            await this.getUnitProjectFbfxDjcsHtQdJjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-4 总价措施项目清单与计价表':
            await this.getUnitProjectZjcsHtQdJjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-5 其他项目清单与计价汇总表':
            await this.getUnitOtherProjectBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-6 材料暂估单价及调整表':
            await this.getUnitOtherProjectClzgjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-7 计日工表':
            await this.getUnitOtherProjectDayWorkBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-8 计日工表':
            await this.getUnitOtherProjectDayWorkBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;

          default:
        }
      }
    }
    if (lanMuName == '13规范报表') {
      if (projectType == 'project') {
        switch (worksheet.name) {
          case "表1-1 总说明":
            // 当 expression 的值与 value3 匹配时执行的代码
            let param5 ={};
            //是招标还是投标 1招标 2投标
            param5.ifZbOrTb = 1;

            //工程项目ID
            param5.constructId = constructId;

            let organization =await this.getOrganization(param5);
            if (organization[1].remark != null) {
              let remark = await ExcelUtil.removeTags(organization[1].remark);
              await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
            }
            break;

          //工程项目层级
          case '表1-1 竣工结算书封面':
            let obj = await this.service.exportQueryService.getconstructProjectJBXX({ constructId: constructId });

            await ZhaoBiaoUtil.writeDataToCover1(obj, worksheet);
            break;
          //工程项目层级
          case '表1-2 建设项目竣工结算汇总表':
            let array1_2gf = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_2gf(args);
            await this.writeDataToUnitFbfx(array1_2gf, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
          case '表-1-3 建设项目竣工结算汇总表(沧州)）':
            let array1_3gf = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectsCostSummary1_3gf(args);
            await this.writeDataToUnitFbfx(array1_3gf, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
          case '封面3 投标总价':
            let param1 = {};
            //是招标还是投标 1招标 2投标
            param1.ifZbOrTb = 2;

            //工程项目ID
            param1.constructId = constructId;
            let constructProjectJBXX = await this.getconstructProjectJBXX(param1);
            await TouBiaoUtil.writeDataToCover1(constructProjectJBXX, worksheet);
            let headArgsTb = {};
            headArgsTb['headStartNum'] = 1;
            headArgsTb['headEndNum'] = 6;
            headArgsTb['titlePage'] = true;
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsTb);
            break;
        }
      }
      if (projectType == 'single') {
        switch (worksheet.name) {
          case '表1-1 单项工程竣工结算汇总表':
            worksheet.lanMuName = '13规范报表'
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectSingle1_1(args);
            await this.writeDataToUnitFbfx(array1_1, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);

          case '表1-1 合同外单项工程竣工结算汇总表':
            let array_htw_1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectSingle1_1(args);
            await this.writeDataToUnitFbfx(array_htw_1_1, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
          //单项工程层级
          case '表1-5 单项工程费汇总表':
            let summaryData = await this.getSingleSummaryData(constructId, singleId);
            await ZhaoBiaoUtil.writeDataToSheet5(summaryData, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;
        }
      }
      if (projectType == 'unit') {
        let headArgsBot = {};
        headArgsBot['headStartNum'] = 1;
        headArgsBot['headEndNum'] = 9;
        headArgsBot['titlePage'] = false;

        let rcjBaoBiao  = {};
        rcjBaoBiao['headStartNum'] = 1;
        rcjBaoBiao['headEndNum'] = 3;
        rcjBaoBiao['titlePage'] = false;

        switch (worksheet.name) {

          case '表1-1 单位工程费用汇总表':
            let array1_1 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingUnitCostSummarys1_1(args);
            await this.writeDataToUnitFbfx(array1_1, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;
          case '表1-1 单位工程竣工结算汇总表（含价差）':
            let array1_1_jc = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_1(args);
            await this.writeDataToUnitFbfx(array1_1_jc, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;
          case '表1-10 规费、税金项目结算表（不含价差）':
            let array1_10 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_10(args);
            await this.writeDataToUnitFbfx(array1_10, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;
          case '表1-11 规费、税金项目结算表（含价差）':
            let array1_11_jc = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_11_jc(args);
            await this.writeDataToUnitFbfx(array1_11_jc, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;

          case '表1-11 规费、税金项目清单与计价表':
            let array1_11 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_10(args);
            await this.writeDataToUnitFbfx(array1_11, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;
          case '表1-12 发包人提供材料和工程设备一览表':
            let clhgcsbb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitFbrtgclhgcsbb(args);
            await this.writeDataToUnitRcj(clhgcsbb, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;

          case '表1-15 材料、机械、设备增值税计算表':
            let clJxSbzzsb = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitClJxSbzzsb(args);
            await this.writeDataToUnitRcj(clJxSbzzsb, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;
          case '表1-12 材料、机械、设备增值税计算表':
            let clJxSbzzsbHtw = await this.service.jieSuanProject.jieSuanExportQueryRcjService.getUnitClJxSbzzsbHtw(args);
            await this.writeDataToUnitRcj(clJxSbzzsbHtw, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, rcjBaoBiao);
            break;
          case '表1-16 增值税进项税额计算汇总表':
            let array1_16 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_16(args);
            await this.writeDataToUnitFbfx(array1_16, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);
            break;

          case '表1-13 增值税进项税额计算汇总表':
            let array1_13 = await this.service.jieSuanProject.jieSuanExportQueryOtherService.buildingProjectUnit1_16(args);
            await this.writeDataToUnitFbfx(array1_13, worksheet,2);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook);

            // await worksheet.workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");

            break;



          case '表1-13 承包人提供主要材料和工程设备一览表':
            await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-14 承包人提供主要材料和工程设备一览表':
            await this.getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-2 分部分项工程和单价措施项目清单与计价表':
            await this.getUnitProjectFbfxDjcsHtQdJjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-3 综合单价分析表':
            await this.getUnitProjectFbfxHtQdQuantityDbBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-4 总价措施项目清单与计价表':
            await this.getUnitProjectZjcsHtQdJjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-5 综合单价调整表':
            await this.getUnitProjectZhdjTzBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-5 其他项目清单与计价汇总表':
            await this.getUnitOtherProjectBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-6 材料(工程设备)暂估价及调整表':
            await this.getUnitOtherProjectClzgjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-6 其他项目清单与计价汇总表':
            await this.getUnitOtherProjectBiaoHtw(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-7 专业工程结算价表':
            await this.getUnitOtherProjectZygczgjBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-7 暂列金额表':
            await this.getUnitOtherProjectProvisionalBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-8 计日工表':
            await this.getUnitOtherProjectDayWorkBiaoGf(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-8 专业工程结算价表':
            await this.getUnitOtherProjectZygczgjBiaoHtw(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-9 计日工表':
            await this.getUnitOtherProjectDayWorkBiaoGfHtw(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-9 总承包服务费计价表':
            await this.getUnitOtherProjectServiceCostBiao(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          case '表1-10 总承包服务费计价表':
            await this.getUnitOtherProjectServiceCostBiaoHtw(constructId, singleId, unitId, worksheet);
            await JieSuanExcelUtil.dealWithPage(worksheet, workbook, headArgsBot);
            break;
          default:
        }
      }
    }


    //填充工程项目名称
    if (projectType == 'project' && !(worksheet.name.includes('封面') || worksheet.name.includes('扉页'))) {
      //填充 项目名称
      let project = PricingFileFindUtils.getProjectObjById(constructId);
      ZhaoBiaoUtil.fillSheetProjectName(worksheet, project.constructName, '工程名称：');
      //填充 工程项目总价表的工程名称
      ZhaoBiaoUtil.fillSheetProjectName(worksheet, project.constructName, '项目名称：');
    }
    if (projectType == 'single') {
      let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
      ZhaoBiaoUtil.fillSheetProjectName(worksheet, singleProject.projectName, '工程名称：');
    }
    if (projectType == 'unit' && !(worksheet.name.includes('封面') || worksheet.name.includes('扉页'))) {
      let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      let single = PricingFileFindUtils.getSingleProject(constructId, singleId);
      let sheetProjectName = '';
      if (single != null) {
        sheetProjectName = single.projectName + unitProject.upName;
      } else {
        sheetProjectName = unitProject.upName;
      }
      ZhaoBiaoUtil.fillSheetProjectName(worksheet, sheetProjectName, '项目名称：');
      ZhaoBiaoUtil.fillSheetProjectName(worksheet, sheetProjectName, '工程名称：');
    }

  }


  getProjectRootPath() {
    // let relativePath = __filename;
    // let index = relativePath.indexOf("pricing-cs");
    // let prefix = relativePath.substring(0,index);
    return UtilsPs.getExtraResourcesDir();
    // return prefix+"pricing-cs";
  }


  async getUnitProjectRcjBiao(constructId, singleId, unitId, worksheet) {
    let data = null;
    /*if (worksheet.name == '表1-12 主材汇总表') {
      let unitRcjQuery = this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        kind: 0
      });
      data = unitRcjQuery.filter(k => k.kind == 7);
    }*/
    if (worksheet.name == '表1-13 承包人提供主要材料和工程设备一览表') {
      data = this.service.jieSuanProject.jieSuanRcjProcess.getJieSuanRcjBBzyclgc({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        methodType: 1
      });
    }

    if (worksheet.name == '表1-14 承包人提供主要材料和工程设备一览表') {
      data = this.service.jieSuanProject.jieSuanRcjProcess.getJieSuanRcjBBzyclgc({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        methodType: 4
      });

    }
    await this.writeDataToUnitFbfx(data, worksheet);
  }

  /**
   * 表1-2 分部分项合同清单工程量及结算工程量对比表
   * @param constructId
   * @param singleId
   * @param unitId
   * @returns {any[]}
   */
  async getUnitProjectFbfxHtQdQuantityDbBiao(constructId, singleId, unitId, worksheet) {

    //单位工程对象
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取分部分项清单数据
    let allNodes = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    await this.writeDataToUnitFbfx(allNodes, worksheet);

  }

  //表1-3 分部分项工程和单价措施项目清单与计价表
  async getUnitProjectFbfxDjcsHtQdJjBiao(constructId, singleId, unitId, worksheet) {

    //单位工程对象
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

    //获取分部分项数据
    let itemBillProjects = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).filter(bt=>bt.kind==BranchProjectLevelConstant.top);
    let qdByfbfx = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    //获取措施项目数据
    let measure = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).filter(bt=>bt.kind==BranchProjectLevelConstant.top);
    let qdByDjcs = PricingFileFindUtils.getQdByDjcs(constructId, singleId, unitId);
    let allNodes = [];
    allNodes.push(...itemBillProjects);
    allNodes.push(...qdByfbfx);
    allNodes.push(...measure);
    allNodes.push(...qdByDjcs);
    let copyList = ConvertUtil.deepCopy(allNodes);
    this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx(copyList, worksheet);

  }


  //表1-4 总价措施项目清单与计价表
  async getUnitProjectZjcsHtQdJjBiaoHtn(constructId, singleId, unitId, worksheet) {

    //获取措施项目数据
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    let copyList = ConvertUtil.deepCopy(qdByZjcs);
    this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx14Htn(copyList, worksheet, 2);

  }

  //表1-4 总价措施项目清单与计价表
  async getUnitProjectZjcsHtQdJjBiao(constructId, singleId, unitId, worksheet) {

    //获取措施项目数据
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    let copyList = ConvertUtil.deepCopy(qdByZjcs);
    this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx(copyList, worksheet);

  }

  //表1-4 总价措施项目清单与计价表  合同外
  async getUnitProjectZjcsHtQdJjBiaoHtw(constructId, singleId, unitId, worksheet) {

    //获取措施项目数据
    let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    let copyList = ConvertUtil.deepCopy(qdByZjcs);
    this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx14Htw(copyList, worksheet);

  }


  //表1-5 其他项目清单与计价汇总表
  async getUnitOtherProjectBiao(constructId, singleId, unitId, worksheet) {
    let otherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
    otherProject = ConvertUtil.deepCopy(otherProject);
    await this.writeDataToUnitOtherProject(otherProject, worksheet);
  }

  async writeDataToUnitOtherProject(data, worksheet) {
    let countRow = -1;
    let htnPriceTotal = 0;
    let priceTotal = 0;
    for (let i = 0; i < worksheet._rows.length; i++) {
      let rowNum = worksheet._rows[i];
      if (rowNum.number >= 4) {  //从第四行开始填充
        countRow++;//从索引零开始 填充
        if (countRow >= data.length) break;
        for (let j = 0; j < rowNum._cells.length; j++) {
          let cell = rowNum._cells[j];
          if (cell.col == 1) {
            cell.value = data[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = data[countRow].extraName;
          }
          if (cell.col == 4) {
            // 合同金额
            if (data[countRow].extraName.includes('材料暂估价') || data[countRow].extraName.includes('设备暂估价')) {
              cell.value = '/';
            } else {
              cell.value = data[countRow].jieSuanTotal;
              if (ObjectUtils.isNotEmpty(data[countRow].dispNo) && !data[countRow].dispNo.includes('.') && cell.value != null) {
                htnPriceTotal += Number.parseFloat(cell.value);
              }
            }
          }
          if (cell.col == 6) {
            if (data[countRow].extraName.includes('材料暂估价') || data[countRow].extraName.includes('设备暂估价')) {
              cell.value = '/';
            } else {
              cell.value = data[countRow].total;
              if (ObjectUtils.isNotEmpty(data[countRow].dispNo) && !data[countRow].dispNo.includes('.') && cell.value != null) {
                priceTotal += Number.parseFloat(cell.value);
              }
            }
          }
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[3].value = htnPriceTotal.toFixed(2);
    row._cells[5].value = priceTotal.toFixed(2);
  }

  //表1-6 其他项目清单与计价汇总表
  async getUnitOtherProjectBiaoHtw(constructId, singleId, unitId, worksheet) {
    let otherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
    otherProject = ConvertUtil.deepCopy(otherProject);
    await this.writeDataToUnitOtherProjectHtw(otherProject, worksheet);
  }

  async writeDataToUnitOtherProjectHtw(data, worksheet) {
    let countRow = -1;
    let priceTotal = 0;
    for (let i = 0; i < worksheet._rows.length; i++) {
      let rowNum = worksheet._rows[i];
      if (rowNum.number >= 4) {  //从第四行开始填充
        countRow++;//从索引零开始 填充
        if (countRow >= data.length) break;
        for (let j = 0; j < rowNum._cells.length; j++) {
          let cell = rowNum._cells[j];
          if (cell.col == 1) {
            cell.value = data[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = data[countRow].extraName;
          }
          if (cell.col == 4) {
            // 合同金额
            if (data[countRow].extraName.includes('材料暂估价') || data[countRow].extraName.includes('设备暂估价')) {
              cell.value = '/';
            } else {
              cell.value = data[countRow].total;
              if (ObjectUtils.isNotEmpty(data[countRow].dispNo) && !data[countRow].dispNo.includes('.') && cell.value != null) {
                priceTotal += Number.parseFloat(cell.value);
              }
            }
          }
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[3].value = priceTotal.toFixed(2);
  }

  //表1-6 材料暂估单价及调整表
  async getUnitOtherProjectClzgjBiao(constructId, singleId, unitId, worksheet) {
    // 目前预算中没有这个表  暂时不处理
  }

  //表1-7 计日工表
  async getUnitOtherProjectDayWorkBiao(constructId, singleId, unitId, worksheet) {
    let otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    otherProjectDayWork = ConvertUtil.deepCopy(otherProjectDayWork);
    await this.writeDataToUnitOtherProjectDayWork(otherProjectDayWork, worksheet);
  }

  async writeDataToUnitOtherProjectDayWork(data, worksheet) {
    //数据行 dataType 2  标题行 1
    let dataList = [];
    let headLines = data.filter(item => item.dataType == 1);
    let total = 0;
    let jieSuanTotal = 0;
    for (let i = 0; i < headLines.length; i++) {
      dataList.push(headLines[i]);
      let children = data.filter(item => item.parentId == headLines[i].sequenceNbr);
      dataList.push(...children);
      let xiaoJi = {};
      xiaoJi.xiaojiFlag = 1;  // 小计行标识
      if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.rg) {
        xiaoJi.worksName = '人工小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.cl) {
        xiaoJi.worksName = '材料小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.jx) {
        xiaoJi.worksName = '机械小计';
      }
      xiaoJi.total = headLines[i].total;
      xiaoJi.jieSuanTotal = headLines[i].jieSuanTotal;
      if (xiaoJi.total != null) {
        total += Number.parseFloat(xiaoJi.total);
      }
      if (xiaoJi.jieSuanTotal != null) {
        jieSuanTotal += Number.parseFloat(xiaoJi.jieSuanTotal);
      }
      dataList.push(xiaoJi);
    }

    let headCount = 2;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < dataList.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (ObjectUtils.isNotEmpty(dataList[countRow].xiaojiFlag) && dataList[countRow].xiaojiFlag == 1) {
          if (cell.col == 1) {
            cell.value = dataList[countRow].worksName;//名称
          }
          worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 8]);
          worksheet.mergeCells([rowObject.number, 1, rowObject.number, 8]);
        } else {
          if (cell.col == 1) {
            cell.value = dataList[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = dataList[countRow].worksName;//名称
          }
          if (cell.col == 3) {
            cell.value = dataList[countRow].unit;//计量单位
          }
          if (cell.col == 4) {
            cell.value = dataList[countRow].jieSuanTentativeQuantity;//暂定数量
          }
          if (cell.col == 5) {
            cell.value = dataList[countRow].tentativeQuantity;//结算数量
          }
          if (cell.col == 7) {
            cell.value = dataList[countRow].jieSuanPrice;//合同综合单价
          }
          if (cell.col == 8) {
            cell.value = dataList[countRow].price;//综合单价
          }
        }
        if (cell.col == 10) {
          cell.value = dataList[countRow].jieSuanTotal;//暂定合价
        }
        if (cell.col == 11) {
          cell.value = dataList[countRow].total;//结算合价
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '总    计');
    //确定合计行的行数
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[9].value = jieSuanTotal.toFixed(2);
    row._cells[10].value = total.toFixed(2);
  }

  // 表1-8 计日工表   13规范
  async getUnitOtherProjectDayWorkBiaoGf(constructId, singleId, unitId, worksheet) {
    let otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    otherProjectDayWork = ConvertUtil.deepCopy(otherProjectDayWork);
    await this.writeDataToUnitOtherProjectDayWorkGf(otherProjectDayWork, worksheet);
  }

  async writeDataToUnitOtherProjectDayWorkGf(data, worksheet) {
    //数据行 dataType 2  标题行 1
    let dataList = [];
    let headLines = data.filter(item => item.dataType == 1);
    let total = 0;
    let jieSuanTotal = 0;
    for (let i = 0; i < headLines.length; i++) {
      dataList.push(headLines[i]);
      let children = data.filter(item => item.parentId == headLines[i].sequenceNbr);
      dataList.push(...children);
      let xiaoJi = {};
      xiaoJi.xiaojiFlag = 1;  // 小计行标识
      if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.rg) {
        xiaoJi.worksName = '人工小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.cl) {
        xiaoJi.worksName = '材料小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.jx) {
        xiaoJi.worksName = '机械小计';
      }
      xiaoJi.total = headLines[i].total;
      xiaoJi.jieSuanTotal = headLines[i].jieSuanTotal;
      if (xiaoJi.total != null) {
        total += Number.parseFloat(xiaoJi.total);
      }
      if (xiaoJi.jieSuanTotal != null) {
        jieSuanTotal += Number.parseFloat(xiaoJi.jieSuanTotal);
      }
      dataList.push(xiaoJi);
    }

    let headCount = 3;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < dataList.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (ObjectUtils.isNotEmpty(dataList[countRow].xiaojiFlag) && dataList[countRow].xiaojiFlag == 1) {
          if (cell.col == 1) {
            cell.value = dataList[countRow].worksName;//名称
          }
          worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 8]);
          worksheet.mergeCells([rowObject.number, 1, rowObject.number, 8]);
        } else {
          if (cell.col == 1) {
            cell.value = dataList[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = dataList[countRow].worksName;//名称
          }
          if (cell.col == 3) {
            cell.value = dataList[countRow].unit;//计量单位
          }
          if (cell.col == 4) {
            cell.value = dataList[countRow].jieSuanTentativeQuantity;//暂定数量
          }
          if (cell.col == 5) {
            cell.value = dataList[countRow].tentativeQuantity;//结算数量
          }
          if (cell.col == 7) {
            cell.value = dataList[countRow].jieSuanPrice;//合同综合单价
          }
          if (cell.col == 8) {
            cell.value = dataList[countRow].price;//综合单价
          }
        }
        if (cell.col == 9) {
          cell.value = dataList[countRow].jieSuanTotal;//暂定合价
        }
        if (cell.col == 10) {
          cell.value = dataList[countRow].total;//结算合价
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '总    计');
    //确定合计行的行数
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[8].value = jieSuanTotal.toFixed(2);
    row._cells[9].value = total.toFixed(2);
  }

  // 表1-9 计日工表
  async getUnitOtherProjectDayWorkBiaoGfHtw(constructId, singleId, unitId, worksheet) {
    let otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    otherProjectDayWork = ConvertUtil.deepCopy(otherProjectDayWork);
    await this.writeDataToUnitOtherProjectDayWorkGfHtw(otherProjectDayWork, worksheet);
  }

  async writeDataToUnitOtherProjectDayWorkGfHtw(data, worksheet) {
    //数据行 dataType 2  标题行 1
    let dataList = [];
    let headLines = data.filter(item => item.dataType == 1);
    let total = 0;
    let jieSuanTotal = 0;
    for (let i = 0; i < headLines.length; i++) {
      dataList.push(headLines[i]);
      let children = data.filter(item => item.parentId == headLines[i].sequenceNbr);
      dataList.push(...children);
      let xiaoJi = {};
      xiaoJi.xiaojiFlag = 1;  // 小计行标识
      if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.rg) {
        xiaoJi.worksName = '人工小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.cl) {
        xiaoJi.worksName = '材料小计';
      } else if (headLines[i].rcjFlag == OtherProjectDayWorkRcjConstant.jx) {
        xiaoJi.worksName = '机械小计';
      }
      xiaoJi.total = headLines[i].total;
      xiaoJi.jieSuanTotal = headLines[i].jieSuanTotal;
      if (xiaoJi.total != null) {
        total += Number.parseFloat(xiaoJi.total);
      }
      if (xiaoJi.jieSuanTotal != null) {
        jieSuanTotal += Number.parseFloat(xiaoJi.jieSuanTotal);
      }
      dataList.push(xiaoJi);
    }

    let headCount = 4;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < dataList.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (ObjectUtils.isNotEmpty(dataList[countRow].xiaojiFlag) && dataList[countRow].xiaojiFlag == 1) {
          if (cell.col == 1) {
            cell.value = dataList[countRow].worksName;//名称
          }
          worksheet.unMergeCells([rowObject.number, 1, rowObject.number, 7]);
          worksheet.mergeCells([rowObject.number, 1, rowObject.number, 7]);
        } else {
          if (cell.col == 1) {
            cell.value = dataList[countRow].dispNo;
          }
          if (cell.col == 2) {
            cell.value = dataList[countRow].worksName;//名称
          }
          if (cell.col == 3) {
            cell.value = dataList[countRow].unit;//单位
          }
          if (cell.col == 5) {
            cell.value = dataList[countRow].tentativeQuantity;//结算数量
          }
          if (cell.col == 7) {
            cell.value = dataList[countRow].price;//综合单价
          }
        }
        if (cell.col == 9) {
          cell.value = dataList[countRow].total;//结算合价
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '总    计');
    //确定合计行的行数
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[8].value = total.toFixed(2);
  }

  //表1-7 暂列金额表
  async getUnitOtherProjectProvisionalBiao(constructId, singleId, unitId, worksheet) {
    let otherProjectProvisional = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);
    otherProjectProvisional = ConvertUtil.deepCopy(otherProjectProvisional);
    await this.writeDataToUnitOtherProjectProvisional(otherProjectProvisional, worksheet);
  }

  async writeDataToUnitOtherProjectProvisional(data, worksheet) {
    let total = 0;
    let headCount = 3;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].name;
        }
        if (cell.col == 4) {
          cell.value = data[countRow].unit;
        }
        if (cell.col == 5) {
          cell.value = data[countRow].provisionalSum;//暂列金额
          if (cell.value != null) {
            total += Number.parseFloat(cell.value);
          }
        }
        if (cell.col == 7) {
          cell.value = data[countRow].description;//备注
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[5].value = total.toFixed(2);
  }

  // 表1-7 专业工程结算价表
  async getUnitOtherProjectZygczgjBiao(constructId, singleId, unitId, worksheet) {
    let unitOtherProjectZygczgj = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    unitOtherProjectZygczgj = ObjectUtils.cloneDeep(unitOtherProjectZygczgj);
    await this.writeDataToUnitOtherProjectZygczgj(unitOtherProjectZygczgj, worksheet);
  }

  async writeDataToUnitOtherProjectZygczgj(data, worksheet) {
    let total = 0;
    let htnTotal = 0;
    let differenceTotal = 0;
    let headCount = 3;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].name;
        }
        if (cell.col == 3) {
          cell.value = data[countRow].content;
        }
        if (cell.col == 5) {
          cell.value = data[countRow].jieSuanTotal;//暂估金额
          if (cell.value != null) {
            htnTotal += Number.parseFloat(cell.value);
          }
        }
        if (cell.col == 6) {
          cell.value = data[countRow].total;//结算金额
          if (cell.value != null) {
            total += Number.parseFloat(cell.value);
          }
        }
        if (cell.col == 8) {
          cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].jieSuanTotal);//差额±(元）
          if (cell.value != null) {
            differenceTotal += Number.parseFloat(cell.value);
          }
        }
        if (cell.col == 9) {
          cell.value = data[countRow].description;//备注
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[4].value = htnTotal.toFixed(2);
    row._cells[5].value = total.toFixed(2);
    row._cells[7].value = differenceTotal.toFixed(2);
  }

  // 表1-8 专业工程结算价表
  async getUnitOtherProjectZygczgjBiaoHtw(constructId, singleId, unitId, worksheet) {
    let unitOtherProjectZygczgj = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    unitOtherProjectZygczgj = ObjectUtils.cloneDeep(unitOtherProjectZygczgj);
    await this.writeDataToUnitOtherProjectZygczgjHtw(unitOtherProjectZygczgj, worksheet);
  }

  async writeDataToUnitOtherProjectZygczgjHtw(data, worksheet) {
    let total = 0;
    let headCount = 2;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].name; //工程名称
        }
        if (cell.col == 3) {
          cell.value = data[countRow].content;//工程内容
        }
        if (cell.col == 5) {
          cell.value = data[countRow].total;//结算金额(元）
          if (cell.value != null) {
            total += Number.parseFloat(cell.value);
          }
        }
        if (cell.col == 7) {
          cell.value = data[countRow].description;//备注
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[5].value = total.toFixed(2);
  }

  // 表1-9 总承包服务费计价表
  async getUnitOtherProjectServiceCostBiao(constructId, singleId, unitId, worksheet) {
    let otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    otherProjectServiceCost = ObjectUtils.cloneDeep(otherProjectServiceCost);
    await this.writeDataToUnitOtherProjectServiceCost(otherProjectServiceCost, worksheet);
  }

  async writeDataToUnitOtherProjectServiceCost(data, worksheet) {
    let total = 0;
    let headCount = 2;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].fxName;
        }
        if (cell.col == 3) {
          cell.value = data[countRow].xmje; //项目价值(元)
        }
        if (cell.col == 4) {
          cell.value = data[countRow].serviceContent; //服务内容
        }
        if (cell.col == 6) {
          cell.value = data[countRow].xmje;//计算基础
        }
        if (cell.col == 8) {
          cell.value = data[countRow].rate;//费率
        }
        if (cell.col == 9) {
          cell.value = data[countRow].jieSuanFwje;//金额（元）
          if (cell.value != null && data[countRow].dataType == 2) {
            total += Number.parseFloat(cell.value);
          }
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[8].value = total.toFixed(2);
  }

  // 表1-10 总承包服务费计价表
  async getUnitOtherProjectServiceCostBiaoHtw(constructId, singleId, unitId, worksheet) {
    let otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    otherProjectServiceCost = ObjectUtils.cloneDeep(otherProjectServiceCost);
    await this.writeDataToUnitOtherProjectServiceCostHtw(otherProjectServiceCost, worksheet);
  }

  async writeDataToUnitOtherProjectServiceCostHtw(data, worksheet) {
    let total = 0;
    let headCount = 2;//表示表头行索引的最大值
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];
      countRow = i;
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) {
          cell.value = data[countRow].dispNo;
        }
        if (cell.col == 2) {
          cell.value = data[countRow].fxName;
        }
        if (cell.col == 3) {
          cell.value = data[countRow].xmje; //项目价值(元)
        }
        if (cell.col == 4) {
          cell.value = data[countRow].serviceContent; //服务内容
        }
        if (cell.col == 5) {
          cell.value = data[countRow].xmje;//计算基础
        }
        if (cell.col == 7) {
          cell.value = data[countRow].rate;//费率
        }
        if (cell.col == 8) {
          cell.value = data[countRow].fwje;//金额（元）
          if (cell.value != null && data[countRow].dataType == 2) {
            total += Number.parseFloat(cell.value);
          }
        }
      }
    }
    //填充合计行
    let heJiCell = ExcelUtil.findValueCell(worksheet, '合    计');
    let row = worksheet.getRow(heJiCell.cell._row._number);
    row._cells[7].value = total.toFixed(2);
  }


  //表1-3 综合单价分析表
  async getUnitProjectZhdjFxBiao(constructId, singleId, unitId, worksheet) {

    //获取措施项目数据
    // let qdByZjcs = PricingFileFindUtils.getQdByZjcs(constructId,singleId, unitId);
    // let copyList = ConvertUtil.deepCopy(qdByZjcs);
    // this.sortDispNo(copyList);
    await this.writeDataToUnitFbfx(copyList, worksheet);

  }


  //表1-5 综合单价调整表
  async getUnitProjectZhdjTzBiao(constructId, singleId, unitId, worksheet) {

    //获取分部分项清单数据
    let allNodes = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    await this.writeDataToUnitFbfx(allNodes, worksheet, 5);

  }


  //序号重排
  sortDispNo(list) {
    if (ObjectUtils.isNotEmpty(list)) {
      let disp = 1;
      list.forEach(qd => {
        qd.dispNo = disp;
        disp++;
      });

    }

  }


  /**
   * 分部分项数据填充格式化
   * @param data
   * @param worksheet
   * @param arg
   * @returns {Promise<void>}
   */
  async writeDataToUnitFbfx(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetData(data, rowObject, worksheet, countRow);
    }
  }


  async writeDataToUnitFbfx14Htw(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetDataHtw(data, rowObject, worksheet, countRow);
    }
  }

  async writeDataToUnitFbfx14Htn(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetDataHtn(data, rowObject, worksheet, countRow);
    }
  }

  async writeDataToUnitFbfx13(data, worksheet, num) {
    let headCount = 3;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetData(data, rowObject, worksheet, countRow);
    }
  }


  // async writeData13计价表(data, worksheet,arg) {
  //     let headCount = 3;//表示表头行索引的最大值
  //     let countRow = 0;//索引  记录当前数据写入的游标
  //     for (let i = 0; i < data.length; i++) {
  //         let rowObject;
  //         headCount++;//记录当前数据插入行的索引
  //         rowObject = worksheet._rows[headCount];//从第五行开始写入数据
  //         let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
  //         // let rowNext = worksheet._rows[headCount+copyDistance];
  //         let rowNext = worksheet._rows[headCount];
  //         if (rowNext == null) {
  //             //插入新行后最后一行的合并单元格丢失
  //             /****插入一条新行**************/
  //             let list=[];
  //             //复制当前数据插入行的格式到增加行
  //             // for (let m = 0; m < rowObject._cells.length; m++) {
  //             //     list.push("");
  //             // }
  //             rowNext = worksheet.insertRow(headCount+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
  //             await JieSuanExcelUtil.resetMerges(worksheet,headCount+2);
  //             let mergeMaps = new Map(Object.entries(worksheet._merges));
  //             for (let m = 0;m<rowNext._cells.length;m++){
  //                 //获取模板行的合并单元格
  //                 let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
  //                 if (mergeName!=null){
  //                     let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
  //                     worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
  //                     worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
  //                 }
  //                 rowNext._cells[m].style = rowObject._cells[m].style;
  //             }
  //             /**end**插入一条新行**************/
  //         }
  //         countRow = i;
  //         this.insertSheetData(data,rowObject,worksheet);
  //     }
  // }

  async writeDataToUnitRcj(data, worksheet, num) {
    let headCount = 2;//表示表头行索引的最大值
    if (ObjectUtils.isNotEmpty(num)) {
      headCount = num;
    }
    let countRow = 0;//索引  记录当前数据写入的游标
    for (let i = 0; i < data.length; i++) {
      let rowObject;
      headCount++;//记录当前数据插入行的索引
      rowObject = worksheet._rows[headCount];//从第五行开始写入数据
      let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
      // let rowNext = worksheet._rows[headCount+copyDistance];
      let rowNext = worksheet._rows[headCount];
      if (rowNext == null) {
        //插入新行后最后一行的合并单元格丢失
        /****插入一条新行**************/
        let list = [];
        //复制当前数据插入行的格式到增加行
        for (let m = 0; m < rowObject._cells.length; m++) {
          list.push('');
        }
        rowNext = worksheet.insertRow(headCount + 1, list, 'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
        await JieSuanExcelUtil.resetMerges(worksheet, headCount + 2);
        let mergeMaps = new Map(Object.entries(worksheet._merges));
        for (let m = 0; m < rowNext._cells.length; m++) {
          //获取模板行的合并单元格
          let mergeName = JieSuanExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
          if (mergeName != null) {
            let { top, left, bottom, right } = mergeMaps.get(mergeName).model;
            worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
            worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
          }
          rowNext._cells[m].style = rowObject._cells[m].style;
        }
        /**end**插入一条新行**************/
      }
      countRow = i;
      await this.insertSheetData(data, rowObject, worksheet, countRow);
    }
  }


  //插入数据
  async insertSheetData(data, rowObject, worksheet, countRow) {
    if (worksheet.name == '表1-1 建设项目费用汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = data[countRow].jck;
        if (cell.col == 4) cell.value = NumberUtil.add(data[countRow].qtxmzlje, data[countRow].qtxmzygczgj);//暂列金额 及专业工程暂估价 lzh
        if (cell.col == 5) cell.value = '';//材料暂估价
        if (cell.col == 6) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj))
        if (cell.col == 7) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj))
        if (cell.col == 9) cell.value = data[countRow].jsjc;//人材机调整合计
      }
    }
    if (worksheet.name == '表1-2 建设项目费用汇总表(沧州)') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = data[countRow].jck;
        if (cell.col == 4) cell.value = NumberUtil.add(data[countRow].qtxmzlje, data[countRow].qtxmzygczgj);//暂列金额 及专业工程暂估价
        if (cell.col == 5) cell.value = '';//材料暂估价
        if (cell.col == 6) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj))
        if (cell.col == 7) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj))
        if (cell.col == 8) cell.value = data[countRow].jsjc;//人材机调整合计
      }
    }

    if (worksheet.name == '表1-3 工程项目竣工结算汇总表' || worksheet.name == '表1-4 工程项目竣工结算汇总表(沧州)') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = data[countRow].total;
        if (cell.col == 4) cell.value = data[countRow].jck

      }
    }
    if (worksheet.name == '表1-2 建设项目竣工结算汇总表' || worksheet.name == '表-1-3 建设项目竣工结算汇总表(沧州)' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].projectName;
        if (cell.col == 3) cell.value = data[countRow].jck;
        if (cell.col == 5) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj))
        if (cell.col == 6) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj))

      }
    }

    if (worksheet.name == '表1-1 单项工程竣工结算汇总表' ) {

      if('13规范报表' == worksheet.lanMuName  ){
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          if (cell.col == 1) cell.value = countRow + 1;
          if (cell.col == 2) cell.value = data[countRow].upName;
          if (cell.col == 3) cell.value = data[countRow].gczj;
          if (cell.col == 5) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].safeFee,data[countRow].jcaqwmsgfhj))
          if (cell.col == 6) cell.value = NumberUtil.numberScale2(NumberUtil.add(data[countRow].gfee,data[countRow].jcgfhj))

        }
      }else {
        for (let j = 0; j < rowObject._cells.length; j++) {
          let cell = rowObject._cells[j];
          if (cell.col == 1) cell.value = countRow + 1;
          if (cell.col == 2) cell.value = data[countRow].upName;
          if (cell.col == 4) cell.value = data[countRow].gczj;
          if (cell.col == 5) cell.value = data[countRow].jck

        }
      }

    }
    if (worksheet.name == '表1-1 合同外单项工程竣工结算汇总表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].upName;
        if (cell.col == 3) cell.value = data[countRow].gczj;
        if (cell.col == 5) cell.value = data[countRow].safeFee
        if (cell.col == 6) cell.value = data[countRow].gfee

      }
    }
    if (worksheet.name == '表1-1 单位工程竣工结算汇总表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 6) cell.value = data[countRow].price

      }
    }

    if (worksheet.name == '表1-1 单位工程竣工结算汇总表（含价差）'   ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 5) cell.value = data[countRow].jieSuanPrice;

      }
    }
    if (worksheet.name == '表1-1 单位工程费用汇总表'   ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 5) cell.value = data[countRow].price;

      }
    }
    if (worksheet.name == '表1-10 规费、税金项目结算表（不含价差）'|| worksheet.name == '表1-11 规费、税金项目结算表（含价差）' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].name;
        if (cell.col == 3) cell.value = data[countRow].instructions;
        if (cell.col == 6) cell.value = data[countRow].calculateFormula;
        if (cell.col == 7) cell.value = data[countRow].rate;
        if (cell.col == 8) cell.value = data[countRow].price;

      }
    }

    if ( worksheet.name == '表1-11 规费、税金项目清单与计价表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 7) cell.value = data[countRow].instructions;
        if (cell.col == 8) cell.value = data[countRow].rate
        if (cell.col == 10) cell.value = data[countRow].price;

      }
    }

    if (worksheet.name == '表1-16 增值税进项税额计算汇总表'||worksheet.name == '表1-13 增值税进项税额计算汇总表' ) {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 5) cell.value = data[countRow].price;

      }
    }





    if (worksheet.name == '表1-13 承包人提供主要材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;

        let temp = '';
        if (ObjectUtils.isNotEmpty(data[countRow].specification)) {
          temp = temp + '、' + data[countRow].specification;
        }
        if (cell.col == 2) cell.value = data[countRow].materialName + temp;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanDifferenceQuantity;
        if (cell.col == 5) cell.value = data[countRow].jieSuanPriceLimit;
        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;
        if (cell.col == 8) cell.value = data[countRow].marketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }
    if (worksheet.name == '表1-14 承包人提供主要材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanMarketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }
    /*if (worksheet.name == '表1-12 主材汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = countRow + 1;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanMarketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;

      }
    }*/

    if (worksheet.name == '表1-2 分部分项合同清单工程量及结算工程量对比表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].bdName;
        if (cell.col == 4) cell.value = data[countRow].unit;
        if (cell.col == 5) cell.value = data[countRow].backQuantity;
        if (cell.col == 7) cell.value = data[countRow].quantity;
        if (cell.col == 9) cell.value = data[countRow].quantityDifference;
        if (cell.col == 10) cell.value = data[countRow].quantityDifferenceProportion;

      }
    }


    if (worksheet.name == '表1-3 分部分项工程和单价措施项目清单与计价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].projectAttr;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].backQuantity;
        if (cell.col == 7) cell.value = data[countRow].quantity;
        if (cell.col == 9) cell.value = data[countRow].quantityDifference;
        if (cell.col == 10) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].backTotal;
        if (cell.col == 13) cell.value = data[countRow].total;
        if (cell.col == 14) cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].backTotal);

      }
    }

    if (worksheet.name == '表1-4 总价措施项目清单与计价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].backTotal;
        if (cell.col == 8) cell.value = data[countRow].total;
        if (cell.col == 9) cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].backTotal);
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }

    if (worksheet.name == '表1-4 总价措施项目清单与计价表13') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }

    if (worksheet.name == '表1-2 分部分项工程和单价措施项目清单与计价表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].projectAttr;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].backQuantity;
        if (cell.col == 7) cell.value = data[countRow].quantity;
        if (cell.col == 9) cell.value = data[countRow].quantityDifference;
        if (cell.col == 10) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].backTotal;
        if (cell.col == 13) cell.value = data[countRow].total;
        if (cell.col == 14) cell.value = NumberUtil.subtract(data[countRow].total, data[countRow].backTotal);

      }
    }


    if (worksheet.name == '表1-5 综合单价调整表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].bdName;
        if (cell.col == 4) cell.value = data[countRow].price;
        if (cell.col == 5) cell.value = data[countRow].rfee;
        if (cell.col == 6) cell.value = data[countRow].cfee;
        if (cell.col == 8) cell.value = data[countRow].jfee;
        if (cell.col == 9) cell.value = NumberUtil.add(data[countRow].managerFee, data[countRow].profitFee);

      }
    }

    if (worksheet.name == '表1-8 单位工程人材机汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].marketPrice;
        if (cell.col == 5) cell.value = data[countRow].totalNumber;
        if (cell.col == 6) cell.value = data[countRow].total;

        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPriceDifferencSum;
        if (cell.col == 12) cell.value = data[countRow].remark;
      }
    }


    if (worksheet.name == '表1-9 工程议价材料表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].unit;

        if (cell.col == 5) cell.value = data[countRow].jieSuanTotalNumber;

        if (cell.col == 7) cell.value = data[countRow].jieSuanPrice;

        if (cell.col == 8) cell.value = data[countRow].marketPrice;

        if (cell.col == 9) cell.value = !ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMin)&&!ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMax)
            ?data[countRow].riskAmplitudeRangeMin + "~" + data[countRow].riskAmplitudeRangeMax:null;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPriceDifferenc;
        if (cell.col == 12) cell.value = data[countRow].jieSuanPriceDifferencSum;
      }
    }


    if (worksheet.name == '表1-10 人材机调整明细表-1') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].specification;
        if (cell.col == 4) cell.value = data[countRow].unit;

        if (cell.col == 6) cell.value = data[countRow].jieSuanDifferenceQuantity;

        if (cell.col == 8) cell.value = data[countRow].jieSuanStagePriceDifferencSum;

      }
    }


    if (worksheet.name == '表1-11 人材机调整明细表-2') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].specification;
        if (cell.col == 4) cell.value = data[countRow].unit;
        if (cell.col == 5) cell.value = data[countRow].marketPrice;
        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;
        if (cell.col == 8) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 9) cell.value = data[countRow].jieSuanPriceDifferenc;
        if (cell.col == 10) cell.value = data[countRow].jieSuanDifferenceQuantity;
        if (cell.col == 12) cell.value = data[countRow].jieSuanStagePriceDifferencSum;
      }
    }

    if (worksheet.name == '表1-12 主材汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].marketPrice;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].jieSuanStagePriceDifferencSum;

      }

    }

    if (worksheet.name == '表1-13 甲方供应材料表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].unit;
        if (cell.col == 5) cell.value = data[countRow].marketPrice;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 9) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 10) cell.value = data[countRow].jieSuanAdminRate;
        if (cell.col == 12) cell.value =!ObjectUtils.isEmpty(data[countRow].jieSuanAdminRate)?NumberUtil.numberScale2(NumberUtil.subtract(
            NumberUtil.subtract(data[countRow].jieSuanTotal,data[countRow].jieSuanAdminRate),0.01)):null ;
        if (cell.col == 13) cell.value = !ObjectUtils.isEmpty(data[countRow].jieSuanAdminRate)?NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanTotal,NumberUtil.
        numberScale2(NumberUtil.subtract(
            NumberUtil.subtract(data[countRow].jieSuanTotal,data[countRow].jieSuanAdminRate),0.01)))):null;

      }
    }


    if (worksheet.name == '表1-14 甲供材料汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;
        if (cell.col == 6) cell.value = data[countRow].totalNumber;
        if (cell.col == 8) cell.value = data[countRow].marketPrice;
        if (cell.col == 9) cell.value = data[countRow].donorMaterialNumber;
        if (cell.col == 10) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 12) cell.value = data[countRow].remark;

      }
    }


    if (worksheet.name == '表1-12 发包人提供材料和工程设备一览表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].donorMaterialNumber;
        if (cell.col == 5) cell.value = data[countRow].marketPrice;
        if (cell.col == 7) cell.value = null;
        if (cell.col == 8) cell.value = data[countRow].deliveryLocation;
        if (cell.col == 9) cell.value = data[countRow].remark;

      }
    }

    if (worksheet.name == '表1-15 材料、机械、设备增值税计算表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 5) cell.value = data[countRow].taxRemoval;
        if (cell.col == 6) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 7) cell.value = data[countRow].jieSuanTotal;

        if (cell.col == 9) cell.value = NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPrice,
            data[countRow].taxRemoval),0.01)));
        if (cell.col == 10) cell.value = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPrice,
            data[countRow].taxRemoval),0.01))),data[countRow].jieSuanTotalNumber));
        if (cell.col == 11) cell.value = data[countRow].jieSunJxTotal;
        if (cell.col == 13) cell.value = null;

      }
    }

    if (worksheet.name == '表1-12 材料、机械、设备增值税计算表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 5) cell.value = data[countRow].taxRemoval;
        if (cell.col == 6) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 7) cell.value = data[countRow].jieSuanTotal;

        if (cell.col == 9) cell.value = NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPrice,
            data[countRow].taxRemoval),0.01)));
        if (cell.col == 10) cell.value = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.numberScale2(NumberUtil.subtract(data[countRow].jieSuanPrice,NumberUtil.multiply(NumberUtil.multiply(data[countRow].jieSuanPrice,
            data[countRow].taxRemoval),0.01))),data[countRow].jieSuanTotalNumber));
        if (cell.col == 11) cell.value = data[countRow].jieSunJxTotal;
        if (cell.col == 13) cell.value = null;

      }
    }


    if (worksheet.name == '表1-1 单位工程人材机汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanTotalNumber;

        if (cell.col == 6) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotal;
      }
    }


    if (worksheet.name == '表1-2 单位工程人材机价差汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;

        if (cell.col == 4) cell.value = data[countRow].jieSuanPrice;

        if (cell.col == 6) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotal;
        if (cell.col == 9) cell.value = data[countRow].jieSuanStagePriceDifferencSum;
        if (cell.col == 10) cell.value = data[countRow].remark;
      }
    }


    if (worksheet.name == '表1-3 人材机价差调整表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = !ObjectUtils.isEmpty(data[countRow].specification) ?data[countRow].materialName +
            data[countRow].specification:data[countRow].materialName;
        if (cell.col == 3) cell.value = data[countRow].unit;
        if (cell.col == 4) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 5) cell.value = data[countRow].marketPrice;


        if (cell.col == 6) cell.value = data[countRow].jieSuanBasePrice;
        if (cell.col == 8) cell.value = data[countRow].jieSuanPrice;
        if (cell.col == 9) cell.value = !ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMin)&&!ObjectUtils.isEmpty(data[countRow].riskAmplitudeRangeMax)
            ?data[countRow].riskAmplitudeRangeMin + "~" + data[countRow].riskAmplitudeRangeMax:null;;
        if (cell.col == 10) cell.value = data[countRow].jieSuanPriceDifferenc;
        if (cell.col == 12) cell.value = data[countRow].jieSuanStagePriceDifferencSum;
        if (cell.col == 13) cell.value = data[countRow].remark;
      }
    }

    if (worksheet.name == '表1-4 主材汇总表') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];

        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].materialCode;
        if (cell.col == 3) cell.value = data[countRow].materialName;
        if (cell.col == 4) cell.value = data[countRow].specification;
        if (cell.col == 5) cell.value = data[countRow].unit;


        if (cell.col == 6) cell.value = data[countRow].jieSuanTotalNumber;
        if (cell.col == 8) cell.value = data[countRow].jieSuanTotal;

        if (cell.col == 10) cell.value = data[countRow].jieSuanStagePriceDifferencSum;

      }
    }

  }



  async insertSheetDataHtw(data, rowObject, worksheet, countRow) {
    if (worksheet.name == '表1-4 总价措施项目清单与计价表14htw') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].total;
        if (cell.col == 9) cell.value = data[countRow].description;

      }
    }
  }


  async insertSheetDataHtn(data, rowObject, worksheet, countRow) {

    if (worksheet.name == '表1-4 总价措施项目清单与计价表14htn') {
      for (let j = 0; j < rowObject._cells.length; j++) {
        let cell = rowObject._cells[j];
        if (cell.col == 1) cell.value = data[countRow].dispNo;
        if (cell.col == 2) cell.value = data[countRow].bdCode;
        if (cell.col == 3) cell.value = data[countRow].name;
        if (cell.col == 4) cell.value = data[countRow].formulaRemark;
        if (cell.col == 7) cell.value = data[countRow].price;
        if (cell.col == 11) cell.value = data[countRow].description;

      }
    }
  }


  findJieSuanSingleTypeEnumByCode(code) {
    let value;
    for (let singleTypeEnum in JieSuanSingleTypeEnum) {
      if (JieSuanSingleTypeEnum[singleTypeEnum].code == code) {
        value = JieSuanSingleTypeEnum[singleTypeEnum];
        return value;
      }
    }
  }
  async getOrganization(param){

    //获取工程项目对象
    let projectObjById = PricingFileFindUtils.getProjectObjById(param.constructId);
    //获取基本信息
    let constructProjectJBXX = projectObjById.constructProjectJBXX;
    if (constructProjectJBXX == null) {
      return ;
    }
    let t = constructProjectJBXX.find(i=>i.name === "工程名称");
    let array = new Array();
    let project = {};
    project.name = t.name;
    project.remark = t.remark;
    array.push(project) ;


    let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(1,param.constructId,null,null);
    let project1 = {};
    project1.name = "总说明";
    project1.remark = organizationInstructions.context;
    array.push(project1) ;

    return array;

  }

}


JieSuanExportQueryService.toString = () => '[class JieSuanExportQueryService]';
module.exports = JieSuanExportQueryService;
