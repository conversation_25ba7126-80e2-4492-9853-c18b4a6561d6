<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-08-09 17:50:28
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-12 16:26:25
-->
<template>
  <common-modal
    className="dialog-comm jzModal"
    width="auto"
    v-model:modelValue="dialogVisible"
    title="载入模板"
  >
    <div class="content-wrap">
      <div class="list-wrap">
        <div class="menus">
          <div
            class="menu-item"
            :class="{ active: i.val === useMenu.val }"
            @click="handClickMenu(i)"
            v-for="i of AsideMenuList"
          >
            <a-tooltip
              placement="right"
              :title="i.label"
            >
              <icon-font
                type="icon-wenjianmoban"
                class="iconFont"
              ></icon-font>
              <span>{{ i.label }}</span>
            </a-tooltip>
          </div>
        </div>
        <div class="table-content">
          <vxe-grid
            class="trends-table-column"
            v-bind="gridOptions"
            ref="gridRef"
            height="auto"
          >
            <template #csxm_default="{ row }">
              <i
                @click="changeStatus(row)"
                v-if="row.displaySign === 1"
                class="vxe-icon-caret-down"
              ></i>
              <i
                @click="changeStatus(row)"
                v-if="row.displaySign === 2"
                class="vxe-icon-caret-right"
              ></i>
              <span>{{ row.bdCodeLevel04 }} </span>
            </template>
          </vxe-grid>
        </div>
      </div>
      <div class="footer-btn-list">
        <span>
          <a-button @click="save(false)">恢复默认</a-button>
          <a-button
            type="primary"
            @click="upFileVisible = true"
            ghost
            v-if="pagesType === 'djgc'"
          >上传</a-button>
        </span>

        <div class="footer-right">
          <a-button
            type="primary"
            @click="close"
            ghost
          >取消</a-button>

          <a-button
            type="primary"
            :loading="submitLoading"
            @click="save(true)"
          >确定</a-button>
        </div>
      </div>
    </div>
  </common-modal>
  <common-modal
    className="dialog-comm"
    width="auto"
    v-model:modelValue="upFileVisible"
    title="上传模板"
  >
    <div class="clearfix">
      <a-upload
        name="file"
        :maxCount="1"
        :before-upload="beforeUpload"
        @remove="handleRemove"
        accept=".json,.JSON"
        @change="upFileChange($event)"
      >
        <a-button>
          <upload-outlined></upload-outlined>
          选择文件
        </a-button>
      </a-upload>
      <a-button
        type="primary"
        :disabled="!upFileInfo"
        :loading="uploading"
        style="margin-top: 16px"
        @click="handleUpload"
      >
        {{ uploading ? '上传中...' : '开始上传' }}
      </a-button>
    </div>
  </common-modal>
</template>
<script>
export default {
  name: 'zjMould',
};
</script>
<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, markRaw, nextTick, toRaw, defineExpose } from 'vue';
import csProject from '@/api/csProject';
import jieSuanApi from '@/api/jiesuanApi';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail';
const props = defineProps(['saveData']);
const projectStore = projectDetailStore();
const route = useRoute();
const emit = defineEmits(['onSuccess']);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const tableData = ref([]);
let pagesType = ref('fyhz');
const gridRef = ref(null);
let isReset = ref(false); //是否恢复默认
// let API = {
//   ys: csProject,
//   yssh: csProject,
//   jieSuan: jieSuanApi,
// };
const initData = () => {
  isReset.value = false;
  submitLoading.value = false;
  switch (pagesType.value) {
    case 'djgc':
      // 单价构成
      gridOptions.columns = [
        { field: 'sort', title: '序号', minWidth: 50 },
        { field: 'code', title: '费用代号', minWidth: 70 },
        {
          field: 'name',
          title: '名称',
          minWidth: 70,
          showOverflow: 'ellipsis',
        },
        { field: 'caculateBase', title: '计算基数', minWidth: 70 },
        { field: 'desc', title: '基数说明', minWidth: 70 },
        { field: 'rate', title: '费率', minWidth: 50 },
        { field: 'type', title: '费用类别', minWidth: 70 },
      ];
      break;
    case 'fyhz':
      // 费用汇总
      gridOptions.columns = [
        { field: 'dispNo', title: '序号', minWidth: 50 },
        { field: 'code', title: '费用代号', minWidth: 70 },
        { field: 'calculateFormula', title: '计算基数', minWidth: 110 },
        {
          field: 'instructions',
          title: '基数说明',
          align: 'left',
          minWidth: 110,
        },
        { field: 'rate', title: '费率', minWidth: 70 },
      ];
      break;
    case 'csxm':
      // 措施项目
      //
      gridOptions.columns = [
        {
          field: 'bdCodeLevel04',
          title: '项目编码',
          width: 120,
          treeNode: true,
        },
        { field: 'bdNameLevel04', title: '名称', align: 'left' },
        { field: 'unit', title: '单位', width: 40 },
        { field: 'calculateFormula', title: '计算基数', width: 110 },
        { field: 'rate', title: '费率', width: 40 },
      ];
      break;
    case 'qtxm':
      //其他项目
      gridOptions.columns = [
        {
          field: 'dispNo',
          title: '序号',
          minWidth: 120,
          treeNode: true,
        },
        { field: 'extraName', title: '名称', minWidth: 120 },
        { field: 'unit', title: '单位', minWidth: 40 },
        // { field: 'amount', title: '数量', minWidth: 40 },
        { field: 'calculationBase', title: '计算基数', minWidth: 100 },
        { field: 'instructions', title: '基数说明', minWidth: 100 },
        {
          field: 'taxRemoval',
          visible:
            Number(projectStore.deType) === 12 &&
            Number(projectStore.taxMade) === 1,
          title: '除税系数',
          minWidth: 90,
        },
        { field: 'extraName', title: '费用类别', minWidth: 90 },
      ];
      break;
  }
};
const changeStatus = row => {
  if (row.displaySign === 1) {
    row.displaySign = 2;
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
  }
};

const gridOptions = reactive({
  headerAlign: 'center',
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  columns: [],
  data: [],
  align: 'center',
  treeConfig: {
    line: true,
    showIcon: true,
    expandAll: true,
    iconOpen: 'vxe-icon-caret-down',
    iconClose: 'vxe-icon-caret-right',
  },
});

/**
 * type false, 恢复默认
 */
const save = type => {
  let data = allPostData.value;
  if (!type) {
    data.template = AsideMenuList.value[0].val;
  }
  console.log('allPostData.value', allPostData.value);
  if (!data.template) {
    message.error('请选择模板！');
    return;
  }

  let apiName = 'selectCostSummaryTemplate';
  if (pagesType.value == 'fyhz' && projectStore.type === 'jieSuan') {
    apiName = 'selectCostSummaryTemplateJS';
  }
  if (pagesType.value == 'csxm') {
    // 措施项目
    apiName = 'applyMeasureTemplate';
    data.templateName = data.template;
    if (!type) {
      data.templateName = null;
    }
  } else if (pagesType.value == 'djgc') {
    // 措施项目
    apiName = type ? 'loadUPCtemplateDJGC' : 'cancelEditorDJGC';
    data = type
      ? { ...props.saveData, qfCode: useMenu.val }
      : { ...props.saveData };
  } else if (pagesType.value == 'qtxm') {
    //其他项目
    apiName = 'settingsTemplateData';
  }
  console.log('🚀 ~ save ~ data:', data, apiName, type);
  csProject[apiName](JSON.parse(JSON.stringify(data))).then(res => {
    delete data.template;
    // console.log('载入模板确定活恢复', res);
    // if (res.status === 200) {
    console.log('载入模板确定', res);
    if (pagesType.value == 'djgc') {
      isReset.value = !type; //   type true载入   false恢复默认
      emit('onSuccess');
    } else {
      if (res.status === 200) {
        emit('onSuccess');
      } else if (res?.message) {
        message.error(res.message);
      }
    }
    message.success(`${type ? '载入' : '恢复'}模板成功！`);
    // } else if (res.status === 500) {
    //   message.error(res.message);
    // }
  });
};

const close = () => {
  initData();
  tableData.value = [];
  dialogVisible.value = false;
};

let allPostData = ref({});

// 获取左侧模板
let AsideMenuList = ref([]);
let useMenu = reactive({
  label: '',
  val: '',
});
const getTemplate = () => {
  const { taxMode, deStandardReleaseYear } = projectStore.constructConfigInfo;
  let apiData;
  let apiName;
  switch (pagesType.value) {
    case 'fyhz':
      // 费用汇总模板数据
      apiData = {
        libraryCodeVersion: deStandardReleaseYear,
        taxMode,
      };
      apiName = 'getCostSummaryTemplate';
      break;
    case 'djgc':
      // 单价构成模板数据
      apiData = {
        constructId: route.query?.constructSequenceNbr,
        singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
        unitId: projectStore.currentTreeInfo?.id, //单位ID
      };
      apiName = 'upcTemplatesDJGC';
      break;

    case 'csxm':
      // 措施项目
      apiData = {
        constructId: route.query?.constructSequenceNbr,
      };
      apiName = 'getMeasureTemplates';
      break;
    case 'qtxm':
      //其他项目
      apiData = {
        constructId: route.query?.constructSequenceNbr,
        singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
        unitId: projectStore.currentTreeInfo?.id, //单位ID
        libraryCodeVersion: projectStore.deType,
        taxMode: projectStore.taxMade,
      };
      apiName = 'getOtherProjectTemplate';
      break;
  }
  console.log('左侧数据', apiData);
  csProject[apiName](apiData).then(res => {
    let list = [];
    res.result.forEach(item => {
      if (pagesType.value !== 'djgc') {
        list.push({
          val:
            pagesType.value === 'csxm'
              ? Object.values(item)[0]
              : Object.keys(item)[0],
          label: Object.values(item)[0],
        });
      } else {
        list.push({
          val: item.value,
          label: item.label,
        });
      }
    });
    AsideMenuList.value = markRaw(list);
    handClickMenu(list[0]);
    console.log('pagesType.value', pagesType.value, res, AsideMenuList.value);
  });
};

//点击了左侧，获取右侧数据
const handClickMenu = v => {
  useMenu.label = v.label;
  useMenu.val = v.val;
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: null,
    unitId: null,
    template: useMenu.val,
  };
  if (projectStore.currentTreeInfo) {
    const { levelType } = projectStore.currentTreeInfo;
    switch (levelType) {
      case 1:
        postData.constructId = route.query?.constructSequenceNbr;
        postData.singleId = null;
        break;
      case 2:
        postData.singleId = projectStore.currentTreeInfo.id;
        postData.unitId = null;
        break;
      case 3:
        postData.singleId = projectStore.currentTreeInfo?.parentId;
        postData.unitId = projectStore.currentTreeInfo.id;
        break;
    }
  }

  let apiName = 'getTemplateData';
  if (pagesType.value == 'fyhz' && projectStore.type === 'jieSuan') {
    apiName = 'getTemplateDataJS';
  }
  if (pagesType.value == 'csxm') {
    // 措施项目
    apiName = 'getBaseListByTemplate';
    postData.templateName = useMenu.val;
  } else if (pagesType.value == 'djgc') {
    postData = {
      constructId: route.query?.constructSequenceNbr,
      singleId: projectStore.currentTreeGroupInfo?.singleId, //单项ID
      unitId: projectStore.currentTreeInfo?.id, //单位ID
      qfCode: useMenu.val,
    };
    apiName = 'upcTemplatesByCodeDJGC';
  } else if (pagesType.value == 'qtxm') {
    postData.taxMode = projectStore.taxMade;
    apiName = 'getOtherProjectTemplateData';
  }

  allPostData.value = postData;
  allPostData.value.template = useMenu.val;

  csProject[apiName](postData).then(res => {
    console.log('右侧数据1', res, apiName);

    if (pagesType.value == 'csxm') {
      // 展示树形
      let treeListAwf = {
        id: 1,
        bdCodeLevel04: 1,
        bdNameLevel04: '安全生产、文明施工费',
        children: res.result.get(2),
      };
      let treeListQt = {
        id: 2,
        bdCodeLevel04: 2,
        bdNameLevel04: '其他总价措施项目',
        children: res.result.get(3),
      };

      gridOptions.data = [treeListAwf, treeListQt];
      nextTick(() => {
        gridRef.value.setAllTreeExpand(true);
      });
    } else {
      gridOptions.data = res.result;
    }
  });
};

const open = async (type = 'fyhz') => {
  console.log('载入模板---页签', type);
  pagesType.value = type;
  if (type === 'djgc') {
    upFileVisible.value = true;
    uploading.value = false;
    upFileInfo.value = null;
  } else {
    dialogVisible.value = true;
    initData();
    getTemplate();
  }
};
const getIsReset = () => {
  return isReset.value;
};
let upFileVisible = ref(false); //上传模板
const uploading = ref(false);
const upFileInfo = ref([]);
const upFileChange = eve => {
  upFileInfo.value = eve.fileList[0]?.originFileObj;
  console.log('upFileChange', upFileInfo.value);
};
const handleRemove = file => {
  upFileInfo.value = null;
};
const beforeUpload = file => {
  // upFileInfo.value = [...(upFileInfo.value || []), file];
  return false;
};
/**
 * @description: 文件上传
 * @param {*} params
 * @return {*}
 */
const handleUpload = async () => {
  uploading.value = true;
  const { path } = upFileInfo.value;
  let apiData = { ...props.saveData, path };
  // debugger;
  console.log('handleUpload', apiData, upFileInfo.value);
  csProject['loadUPCtemplateDJGC'](apiData)
    .then(res => {
      console.log('载入模板确定活恢复', res);
      if (res.status === 200) {
        console.log('单价构成上传模板', res);
        if (pagesType.value == 'djgc') {
          //isReset.value = !type; //   type true载入   false恢复默认
          emit('onSuccess');
        }
        message.success(`上传模板成功！`);
        isEdit.value = res.result;
      } else {
        isEdit.value = true;
        message.error(res.message);
      }
    })
    .finally(() => {
      upFileVisible.value = false;
    });
};
let isEdit = ref(true);
const getIsEdit = () => {
  return isEdit.value;
};
defineExpose({
  open,
  close,
  getIsReset,
  getIsEdit,
});
</script>

<style lang="scss">
.jzModal {
  .content-wrap {
    width: 60vw;
    min-width: 500px;
    .list-wrap {
      display: flex;
      height: 60vh;
      margin-bottom: 15px;
      .menus {
        width: 200px;
        padding: 10px 20px;
        border: 1px solid rgba(185, 185, 185, 0.5);
        height: 100%;
        overflow-y: auto;
        .menu-item {
          margin-bottom: 2px;
          line-height: 1.6;
          opacity: 0.8;
          padding: 4px 0;
          cursor: pointer;
          &:hover {
            opacity: 1;
          }
          .iconFont {
            margin-right: 4px;
          }
          &.active {
            opacity: 1;
            color: #000;
          }
        }
      }
      .table-content {
        flex: 1;
        height: 100%;
        margin-left: 12px;
      }
    }

    .footer-btn-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .vxe-table {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
}
</style>
