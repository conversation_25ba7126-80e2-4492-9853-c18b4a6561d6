import { projectDetailStore } from '@/store/projectDetail';
import { ref, toRaw, reactive, onMounted } from 'vue';
import { useCellClick } from '@/hooks/useCellClick';
const projectStore = projectDetailStore();
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const pureListLong = reactive({
  //其他项目子页面使用pureNumber保留的小数位数长度
  amount: 6,
  price: 6,
  taxRemoval: 4,
  xmje: 2,
  rate: 4,
});
const childSqList = [
  {
    type: 'zlje',
    seq: 'qtxm01',
  },
  {
    type: 'jrg',
    seq: 'qtxm06',
  },
  {
    type: 'zygczgj',
    seq: 'qtxm04',
  },
  {
    type: 'zcbfwf',
    seq: 'qtxm05',
  },
];
const columnsList = reactive([
  {
    field: 'dispNo',
    minWidth: 60,
    // title: '序号',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'worksName',
    // title: '项目名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'more_edit' },
  },
  {
    field: 'specification',
    // title: '规格型号',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dataType_more_edit' },
  },
  {
    field: 'name',
    // title: '名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'more_edit' },
  },
  // {
  //   field: 'name',
  //   title: '工程名称',
  //   minWidth: 180,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'more_edit' },
  // },
  {
    field: 'content',
    // title: '工程名称内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'more_edit' },
  },
  {
    field: 'unit',
    // title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dataType_unit_edit' },
  },
  {
    field: 'unit',
    // title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'quantitativeExpression',
    // title: '数量表达式',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dataType_quaExp_edit' },
  },
  {
    field: 'amount',
    // title: '数量',
    minWidth: 80,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'pure_edit' },
  },
  {
    field: 'price',
    // title: '单价',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'pure_edit' },
  },
  {
    field: 'xmje',
    // title: '项目价值',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dataType_pure_edit' },
  },
  {
    field: 'serviceContent',
    // title: '服务内容',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dataType_more_edit' },
  },
  {
    field: 'rate',
    // title: '费率(%)',
    minWidth: 80,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'dataType_pure_edit' },
  },
  {
    field: 'tentativeQuantity',
    minWidth: 180,
    // title: '暂定数量',
  },
  {
    field: 'total',
    minWidth: 100,
    // title: '金额',
  },
  {
    field: 'provisionalSum',
    minWidth: 180,
    // title: '暂定金额',
  },
  {
    field: 'price',
    minWidth: 180,
    // title: '综合单价',
    slots: { edit: 'dataType_more_edit' },
  },
  {
    field: 'taxRemoval',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    // title: '除税系数(%)',
    slots: { edit: 'dataType_pure_edit' },
  },
  {
    field: 'taxRemoval',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    // title: '除税系数(%)',
    slots: { edit: 'pure_edit' },
  },
  // {
  //   field: 'total',
  //   minWidth: 60,
  //   // title: '合价',
  // },
  {
    field: 'jxTotal',
    minWidth: 100,
    // title: '进项合计',
  },
  {
    field: 'csPrice',
    minWidth: 100,
    // title: '除税单价',
  },
  {
    field: 'csTotal',
    minWidth: 100,
    // title: '除税合价',
  },
  {
    field: 'description',
    // title: '备注',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'more_edit' },
  },
]);
export default {
  requestParams() {
    let levelType = projectStore.currentTreeInfo?.levelType;
    return {
      levelType: projectStore.currentTreeInfo?.levelType,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: levelType === 3 ? projectStore.currentTreeInfo?.id : null,
    };
  },
  isOtherProChild(type) {
    // debugger;
    let flag;
    let target = childSqList.find(item => item.type === type);
    target.seq === projectStore.asideMenuCurrentInfo?.sequenceNbr
      ? (flag = true)
      : (flag = false);
    return flag;
  },
  getComId(type) {
    let id;
    switch (type) {
      case 'zlje':
        id = 'qtxmZlje';
        break;
      case 'zygczgj':
        id = 'qtxmZygczgj';
        break;
      case 'jrg':
        id = 'qtxmJrg';
        break;
      case 'zcbfwf':
        id = 'qtxmZcbfwf';
        break;
    }
    return id;
  },
};
