<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-29 09:13:12
-->
<template>
  <div>
    <common-modal
      className="dialog-comm noMask"
      title="费用代码明细"
      width="900"
      height="400"
      v-model:modelValue="isFeiyong"
      @cancel="cancel"
      @close="isFeiyong = false"
      :mask="false"
      :show-zoom="true"
    >
      <content-down></content-down>
    </common-modal>
    <common-modal
      className="dialog-comm noMask"
      title="安全生产、文明施工费明细"
      width="700"
      v-model:modelValue="isAWF"
      @cancel="cancel"
      @close="isAWF = false"
      :mask="false"
    >
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="awfData"
        height="400"
      >
        <vxe-column
          field="sortNo"
          min-width="80"
          title="序号"
        > </vxe-column>
        <vxe-colgroup
          field="b"
          title="送审"
        >
          <vxe-column
            field="costMajorName"
            min-width="100"
            title="取费专业"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.costMajorName }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="costFeeBase"
            min-width="100"
            title="取费基数（元）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.costFeeBase }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="basicRate"
            min-width="100"
            title="基本费率（%）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.basicRate }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="addRate"
            min-width="100"
            title="增加费率（%）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.addRate }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="feeAmount"
            min-width="100"
            title="费用金额（元）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.feeAmount }}</span>
            </template>
          </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup
          field="b"
          title="审定"
        >
          <vxe-column
            field="costMajorName"
            min-width="100"
            title="取费专业"
          >
          </vxe-column>
          <vxe-column
            field="costFeeBase"
            min-width="100"
            title="取费基数（元）"
          >
          </vxe-column>
          <vxe-column
            field="basicRate"
            min-width="100"
            title="基本费率（%）"
          >
          </vxe-column>
          <vxe-column
            field="addRate"
            min-width="100"
            title="增加费率（%）"
          >
          </vxe-column>
          <vxe-column
            field="feeAmount"
            min-width="100"
            title="费用金额（元）"
          >
          </vxe-column>
        </vxe-colgroup>
        <vxe-column
          field="changeTotal"
          title="增减金额"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.ysshSysj.changeTotal }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="changeRatio"
          title="增减比例"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.ysshSysj.changeRatio }}</span>
          </template>
        </vxe-column>
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </common-modal>
    <common-modal
      className="dialog-comm noMask"
      title="规费明细"
      width="700"
      v-model:modelValue="isGF"
      @cancel="cancel"
      @close="isGF = false"
      :mask="false"
    >
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :data="gfData"
        height="400"
      >

        <vxe-colgroup
          field="b"
          title="送审"
        >
          <vxe-column
            field="sortNo"
            min-width="80"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="costMajorName"
            min-width="100"
            title="取费专业"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj.costMajorName }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="costFeeBase"
            min-width="100"
            title="取费基数（元）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj.costFeeBase }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="gfeeRate"
            min-width="100"
            title="规费费率（%）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj.gfeeRate }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="feeAmount"
            min-width="100"
            title="费用金额（元）"
          >
            <template #default="{ row }">
              <span>{{ row.ysshSysj.feeAmount }}</span>
            </template>
          </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup
          field="b"
          title="审定"
        >
          <vxe-column
            field="sortNo"
            min-width="80"
            title="序号"
          > </vxe-column>
          <vxe-column
            field="costMajorName"
            min-width="100"
            title="取费专业"
          >
          </vxe-column>
          <vxe-column
            field="costFeeBase"
            min-width="100"
            title="取费基数（元）"
          >
          </vxe-column>
          <vxe-column
            field="gfeeRate"
            min-width="100"
            title="规费费率（%）"
          >
          </vxe-column>
          <vxe-column
            field="feeAmount"
            min-width="100"
            title="费用金额（元）"
          >
          </vxe-column>
        </vxe-colgroup>

        <vxe-column
          field="changeTotal"
          title="增减金额"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.ysshSysj.changeTotal }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="changeRatio"
          title="增减比例"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.ysshSysj.changeRatio }}</span>
          </template>
        </vxe-column>
        <template #empty>
          <span style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            ">
            <icon-font
              style="font-size: 22px"
              type="icon-zanwushuju"
            ></icon-font>
            暂无数据
          </span>
        </template>
      </vxe-table>
    </common-modal>
  </div>
</template>
<script setup>
import { reactive, ref, onMounted, onActivated, getCurrentInstance } from 'vue';
import { getUrl } from '@/utils/index';
import { insetBus } from '@/hooks/insetBus';
import feePro from '@/api/shApi';
import { projectDetailStore } from '@/store/projectDetail';
import ContentDown from '@/views/projectDetail/customize/summaryExpense/ContentDown.vue';
const store = projectDetailStore();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let isFeiyong = ref(false);
let isAWF = ref(false);
let isGF = ref(false);
const emit = defineEmits(['clickAdd', 'getCommonModel']);
let awfData = ref([]);
let gfData = ref([]);
let checkVisible = ref(false); // 项目自检弹框是否展示

const getAWFdate = () => {
  awfData.value = [];
  let apiData = {
    ssConstructId: store.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: store.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: store.currentTreeInfo?.ysshUnitId,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  feePro.getSafeFee(apiData).then(res => {
    console.log('getAWFdate', res);
    if (res.status === 200) {
      res.result && res.result.map((item, index) => (item.sortNo = index + 1));
      awfData.value = res.result;
    } else {
      awfData.value = [];
    }
  });
};
const getGFdate = () => {
  gfData.value = [];
  let apiData = {
    ssConstructId: store.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: store.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: store.currentTreeInfo?.ysshUnitId,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  feePro.getGfeeFee(apiData).then(res => {
    console.log('getGFdate', res);
    if (res.status === 200) {
      res.result && res.result.map((item, index) => (item.sortNo = index + 1));
      gfData.value = res.result;
    } else {
      gfData.value = [];
    }
  });
};
const tabList = reactive([
  {
    id: 'charu',
    title: '插入',
    url: getUrl('detailImg/charuhang.png'),
  },
  {
    id: 'feiyong',
    title: '费用代码明细',
    url: getUrl('detailImg/dianfei.png'),
  },
  {
    id: 'anwenfei',
    title: '安全生产、文明施工费明细',
    url: getUrl('detailImg/anwenfei.png'),
  },
  {
    id: 'guifei',
    title: '规费明细',
    url: getUrl('detailImg/guifei.png'),
  },
  // {
  // 	id: 3,
  // 	title: '记取工程水电费',
  // 	url: getUrl('detailImg/dianfei.png'),
  // },
]);
const openModel = item => {
  // console.log('点击item', item);
  switch (item.id) {
    case 'charu':
      emit('clickAdd', true);
      break;
    case 'anwenfei':
      getAWFdate();
      isAWF.value = true;
      break;
    case 'guifei':
      getGFdate();
      isGF.value = true;
      break;
    case 'feiyong':
      isFeiyong.value = true;
      break;
  }
};
onMounted(() => {
  initBus();
});
onActivated(() => {
  initBus();
});
const initBus = () => {
  insetBus(bus, store.componentId, 'summaryExpense', async data => {
    if (data.name === 'insert') emit('clickAdd', true);
    if (data.name === 'charge-code') isFeiyong.value = true;
    if (data.name === 'anwen-fee') getAWFdate(), (isAWF.value = true);
    if (data.name === 'fees') getGFdate(), (isGF.value = true);
    if (data.name === 'selfCheck') getGFdate(), (checkVisible.value = true);
    if (data.name === 'retrieval-fee')
      emit('getCommonModel', { type: data.name });
    if (data.name === 'comparative-match')
      emit('getCommonModel', { type: data.name });
    if (data.name === 'modify-submission-for-review')
      emit('getCommonModel', { type: data.name });
    if (data.name === 'convert-to') {
      // convertToTitle.value=data.activeKind==='01'?'审定转预算文件':'送审转预算文件'
      emit('getCommonModel', { type: data.name, activeKind: data.activeKind });
    }
    if (data.name === 'data-conversion') {
      emit('getCommonModel', { type: data.name, activeKind: data.activeKind });
    }
  });
};
// const tabList = reactive([
//   {
//     id: 'charu',
//     title: '插入',
//     url: getUrl('detailImg/charuhang.png'),
//   },
//   {
//     id: 'feiyong',
//     title: '费用代码明细',
//     url: getUrl('detailImg/dianfei.png'),
//   },
//   {
//     id: 'anwenfei',
//     title: '安文费明细',
//     url: getUrl('detailImg/anwenfei.png'),
//   },
//   {
//     id: 'guifei',
//     title: '规费明细',
//     url: getUrl('detailImg/guifei.png'),
//   },
//   // {
//   // 	id: 3,
//   // 	title: '记取工程水电费',
//   // 	url: getUrl('detailImg/dianfei.png'),
//   // },
// ]);
// const openModel = item => {
//   // console.log('点击item', item);
//   switch (item.id) {
//     case 'charu':
//       emit('clickAdd', true);
//       break;
//     case 'anwenfei':
//       isAWF.value = true;
//       break;
//     case 'guifei':
//       isGF.value = true;
//       break;
//     case 'feiyong':
//       isFeiyong.value = true;
//       break;
//   }
// };
</script>
<style lang="scss" scoped>
.head {
  // display: flex;
  // align-items: center;
  // height: 40px;
  // padding: 0 10px;
  span img {
    margin-right: -10px;
    margin-bottom: 3px;
  }
}
</style>
