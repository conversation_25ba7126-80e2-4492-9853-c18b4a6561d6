<!--
 * @Descripttion: 公共左侧树结构组件
 * @Author: sunchen
 * @Date: 2024-02-02 09:19:19
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-23 10:02:10
-->
<template>
  <div>
    <div
      class="aside-tree-container"
      :class="leftIsExpand ? '' : 'expand-tree-container'"
    >
      <vxe-table
        v-show="leftIsExpand"
        ref="vexTable"
        border="none"
        align="left"
        :show-header="false"
        :showIcon="false"
        :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'id' }"
        :tree-config="{
          children: 'children',
          transform: true,
        }"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'cell',
        }"
        :data="props.treeData"
        @current-change="currentChangeEvent"
        :menu-config="rightClickInfo.tableMenu"
        @menu-click="contextMenuClickEvent"
      >
        <vxe-column
          tree-node
          field="name"
          class-name="sc-tree"
          :edit-render="{ autofocus: '.my-input' }"
        >
          <template #default="{ row }">
            <a-tooltip
              placement="right"
              v-if="!props.leftIsExpand"
            >
              <template #title>{{ row.name }}</template>
              <span class="expand-span"><icon-font
                  style="font-size: 14px"
                  :type="getIconType(row.levelType)"
                /></span>
            </a-tooltip>
            <span v-else><icon-font :type="getIconType(row.levelType)" />&nbsp;{{
                row.name
              }}</span>
          </template>
          <template #edit="{ row }">
            <span v-if="row.originalFlag">{{ row.name }}</span>
            <a-input
              :placeholder="getPlaceholder(row.levelType)"
              v-else
              v-model:value="row.name"
              type="text"
              @keyup="row.name = removeSpecialChars(row.name)"
              @blur="saveInfo(row)"
              class="my-input"
            />
          </template>
        </vxe-column>
      </vxe-table>
      <vxe-table
        v-show="!leftIsExpand"
        ref="expandVexTable"
        border="none"
        align="left"
        :show-header="false"
        :showIcon="false"
        :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'id' }"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'cell',
        }"
        :data="props.treeData"
        @current-change="currentChangeEvent"
        :menu-config="rightClickInfo.tableMenu"
        @menu-click="contextMenuClickEvent"
      >
        <vxe-column
          tree-node
          field="name"
          class-name="sc-tree"
          :edit-render="{ autofocus: '.my-input' }"
        >
          <template #default="{ row }">
            <a-tooltip
              placement="right"
              v-if="!props.leftIsExpand"
            >
              <template #title>{{ row.name }}</template>
              <span class="expand-span"><icon-font
                  style="font-size: 14px"
                  :type="getIconType(row.levelType)"
                /></span>
            </a-tooltip>

            <span v-else><icon-font :type="getIconType(row.levelType)" />&nbsp;{{
                row.name
              }}</span>
          </template>
          <template #edit="{ row }">
            <span v-if="row.originalFlag">{{ row.name }}</span>
            <a-input
              :placeholder="getPlaceholder(row.levelType)"
              v-else
              v-model:value="row.name"
              type="text"
              @keyup="row.name = removeSpecialChars(row.name)"
              @blur="saveInfo(row)"
              class="my-input"
            />
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <common-modal
      className="dialog-comm"
      width="530px"
      v-model:modelValue="dialogVisible"
      :title="dialogTitle"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 7 }"
        :wrapper-col="{ span: 15 }"
        autocomplete="off"
        @finish="handleOk"
      >
        <template v-if="
            dialogType === ConstructMenuOperator.unitLevel ||
            dialogType === ConstructMenuOperator.addVisa ||
            dialogType === ConstructMenuOperator.addOmission ||
            dialogType === ConstructMenuOperator.addClaimant ||
            dialogType === ConstructMenuOperator.addOther ||
            dialogType === ConstructMenuOperator.addUnit
          ">
          <a-form-item
            label="单位工程名称"
            name="name"
            :rules="[{ required: true, message: '请输入单位工程名称!' }]"
          >
            <a-input
              :maxlength="50"
              v-model:value.trim="formData.name"
              @input="
                () => {
                  formData.name = removeSpecialChars(formData.name);
                }
              "
              placeholder="请输入单位工程名称"
            />
          </a-form-item>
          <a-form-item
            label="清单专业"
            name="type"
            :rules="[{ required: true, message: '请选择清单专业!' }]"
          >
            <a-select
              v-model:value="formData.type"
              placeholder="请选择清单专业"
              :options="engineerMajorList"
              @change="typeChange"
            >
            </a-select>
          </a-form-item>
          <a-form-item
            label="主定额册名称"
            name="type"
            v-if="formData.type"
          >
            <a-select
              v-model:value="formData.libraryCode"
              placeholder="请选择主定额册名称"
              :options="majorTypeDropdownList"
              :fieldNames="{
                label: 'libraryName',
                value: 'libraryCode',
              }"
            >
            </a-select>
          </a-form-item>
          <a-form-item
            label="定额专业"
            name="type"
            :rules="[{ required: true, message: '请选择定额专业!' }]"
          >
            <a-select
              v-model:value="formData.secondInstallationProjectName"
              :placeholder="`请选择${
                formData.secondInstallationProjectName
                  ? formData.secondInstallationProjectName
                  : ''
              }定额专业`"
              :options="secondDropdownList"
              :fieldNames="{ label: 'cslbName', value: 'cslbName' }"
            >
            </a-select>
          </a-form-item>
        </template>
        <a-form-item
          v-if="dialogType === ConstructMenuOperator.singleChildLevel"
          label="子单项工程名称"
          name="name"
          :rules="[{ required: true, message: '请输入子单项工程名称!' }]"
        >
          <a-input
            :maxlength="50"
            v-model:value.trim="formData.name"
            @input="
              () => {
                formData.name = inputName(formData.name);
              }
            "
            placeholder="请输入子单项工程名称"
          />
        </a-form-item>
        <a-form-item
          v-if="[18, 19, 20, 21, 22, 2].includes(dialogType)"
          label="单项工程名称"
          name="name"
          :rules="[{ required: true, message: '请输入单项工程名称!' }]"
        >
          <a-input
            :maxlength="50"
            v-model:value.trim="formData.name"
            @input="
              () => {
                formData.name = removeSpecialChars(formData.name);
              }
            "
            placeholder="请输入单项工程名称"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 7, span: 15 }">
          <a-button @click="dialogVisible = false">取消</a-button>
          <a-button
            style="margin-left: 10px"
            type="primary"
            html-type="submit"
            :loading="submitLoading"
          >确定</a-button>
        </a-form-item>
      </a-form>
    </common-modal>
  </div>
</template>
<script>
export default {
  name: 'commonAsideTreeJiesuan',
};
</script>
<script setup>
import {
  ref,
  reactive,
  onMounted,
  defineAsyncComponent,
  watch,
  watchEffect,
  nextTick,
  computed,
  toRaw,
} from 'vue';
import { useRoute } from 'vue-router';
import { ConstructMenuOperator } from '@/components/editJsProjectStructure/ConstructMenuOperator';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { removeSpecialChars } from '@/utils/index';
import { VXETable } from 'vxe-table';
import { proModelStore } from '@/store/proModel.js';
import operateList from '@/views/projectDetail/customize/operate';
import { useCheckProjectBefore } from '@/hooks/useCheckProjectBefore';
import jiesuanApi from '@/api/jiesuanApi';
const route = useRoute();
const constructSequenceNbr = route.query.constructSequenceNbr;
const store = projectDetailStore();
const vexTable = ref();
const expandVexTable = ref();
let visible = ref(false);
let submitLoading = ref(false);
const proStore = proModelStore();
let oldTreeData = ref([]);
// 弹框操作

const dialogInfo = {
  [ConstructMenuOperator.singleLevel]: {
    title: '添加单项工程',
  },
  [ConstructMenuOperator.addOutProject]: {
    title: '添加单项工程',
  },
  [ConstructMenuOperator.addOutChange]: {
    title: '添加变更单项工程',
  },
  [ConstructMenuOperator.addOutVisa]: {
    title: '添加签证单项工程',
  },
  [ConstructMenuOperator.addOutOmission]: {
    title: '添加漏项单项工程',
  },
  [ConstructMenuOperator.addOutClaimant]: {
    title: '添加索赔单项工程',
  },
  [ConstructMenuOperator.singleChildLevel]: {
    title: '添加子单项工程',
  },
  [ConstructMenuOperator.addOutOther]: {
    title: '添加其他单项工程',
  },
  [ConstructMenuOperator.unitLevel]: {
    title: '添加变更单位工程',
  },
  [ConstructMenuOperator.addVisa]: {
    title: '添加签证单位工程',
  },
  [ConstructMenuOperator.addOmission]: {
    title: '添加漏项单位工程',
  },
  [ConstructMenuOperator.addClaimant]: {
    title: '添加索赔单位工程',
  },
  [ConstructMenuOperator.addOther]: {
    title: '添加其他单位工程',
  },
  [ConstructMenuOperator.addUnit]: {
    title: '添加单位工程',
  },
};
let dialogVisible = ref(false);
let dialogTitle = ref('');
let dialogType = ref('');
let engineerMajorList = ref([]);
let majorTypeDropdownList = ref([]);
let secondDropdownList = ref([]);
let currentInfo = ref(null);
let rightClickObj = ref({});
const props = defineProps(['treeData', 'leftIsExpand', 'pageFrom']);
const emit = defineEmits(['getTreeList']);
let unifyData = ref(); //统一应用按钮是否禁用
const { showInfo } = useCheckProjectBefore({
  type: props.pageFrom === 'jiesuan' ? 2 : 1,
});
const openVisible = () => {
  visible.value = true;
};
watchEffect(() => {
  if (proStore.openEditModalStatus) {
    openVisible();
    // 编辑弹窗还是自动打开，则关闭
    proStore.onEditModal(false);
  }
});

watch(
  () => props.leftIsExpand,
  (newVal, oldVal) => {
    if (vexTable.value || expandVexTable.value) {
      vexTable.value.setCurrentRow(store.$state.currentTreeInfo);
      expandVexTable.value.setCurrentRow(store.$state.currentTreeInfo);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

watch(
  () => props.treeData,
  (newVal, oldVal) => {
    oldTreeData.value = JSON.parse(JSON.stringify(props.treeData));
    xeUtils.clone(props.treeData).map(i => {
      if (i.name) {
        i.copyName = i.name;
      }
      return i;
    });
    store.projectTree = newVal;
    let change;
    if (newVal && oldVal && newVal.length > oldVal.length) {
      let change = newVal.find(x => !oldVal.some(y => y.id === x.id));
      if (change) {
        if (store.isAddUnit) {
          //如果左侧树添加了单位  需要执行handleAddUnit
          let apiData = {
            constructId: route.query.constructSequenceNbr,
            singleId: change.parentId,
            unitId: change.id,
          };
          jiesuanApi.handleAddUnit(apiData);
          store.isAddUnit = false;
        }
        change && setCheckRow(change);
        //默认展开一级节点，所以此处只需要判断添加的是单位展开其父节点
        setTimeout(() => {
          const $table = vexTable.value;
          let parant = newVal.find(x => x.id === change.parentId);
          ininGetList(parant); //展开第一个有children的单项
          $table.setTreeExpand(expandList.value, true);
        }, 0);
      }
    } else if (newVal && oldVal && newVal.length < oldVal.length) {
      change = oldVal.filter(x => !newVal.some(y => y.id === x.id))[0];
      const select = newVal.filter(item => item.id === change.parentId);
      select && setCheckRow(select[0]);
    } else {
      // 没有新增或删减就默认选中以前选中的数据
      console.log('currentInfo.value', currentInfo.value, newVal);
      if (currentInfo.value) {
        let target = newVal.find(a => a.id === currentInfo.value.id);
        setCheckRow(target);
      } else {
        setCheckRow(newVal[0]);
      }
    }
  }
);
let expandList = ref([]);
const ininGetList = data => {
  //获取需要展开的列表
  expandList.value = [props.treeData[0]];
  getExpandList(data);
};
const getExpandList = data => {
  expandList.value.push(data);
  if (data.children.length > 0) {
    data.children.map(i => {
      getExpandList(i);
    });
  }
};

// 表单
let formData = reactive({
  name: null,
  type: null,
  clickId: null,
  clickLevelType: null,
  libraryCode: null,
  secondInstallationProjectName: null,
});
let addItem = reactive({
  // levelId: null,
  levelType: null,
  parentLevel: null,
  parentId: null,
});
const formRef = ref(null);
const setDialogInfo = async (type, row) => {
  formData = reactive({
    name: null,
    type: null,
    libraryCode: null,
    secondInstallationProjectName: null,
  });
  dialogTitle.value = dialogInfo[type].title;
  dialogType.value = type;
  if (menuOperator.singleLevelList.includes(type)) {
    addItem.levelType = 2;
  } else if (menuOperator.unitLevelList.includes(type)) {
    addItem.levelType = 3;
  }

  if (type === 24 || menuOperator.unitLevelList.includes(type)) {
    addItem.parentId = currentInfo.value.id;
  } else if (menuOperator.singleLevelList.includes(type)) {
    addItem.parentId = row.id;
  }
  addItem.parentLevel = currentInfo.value.levelType;
  dialogVisible.value = true;
  formRef.value?.resetFields();
  // 如果为工程项目
  if (row.levelType === 1) {
    currentInfo.value = await handleRightClick(row);
  } else {
    currentInfo.value = row;
  }
  secondDropdownList.value = [];
  queryEngineerMajorList();
};
const handleOk = async () => {
  let rightArr = [];
  let rightObj = currentInfo.value;
  let childArr = rightObj.children;
  // 如果是变更等新建单项或者点击的是建筑工程里面的
  if (
    dialogType.value === ConstructMenuOperator.addOutProject ||
    dialogType.value === ConstructMenuOperator.addOutChange ||
    dialogType.value === ConstructMenuOperator.addOutVisa ||
    dialogType.value === ConstructMenuOperator.addOutOmission ||
    dialogType.value === ConstructMenuOperator.addOutClaimant ||
    dialogType.value === ConstructMenuOperator.addOutOther
  ) {
    rightArr = rightObj.parent.children;
  } else if (dialogType.value === ConstructMenuOperator.singleChildLevel) {
    rightArr = rightObj.parent.children;
  } else {
    if (childArr.length > 0) {
      rightArr = childArr;
    } else {
      rightArr = rightObj.parent.children;
    }
  }
  let targetId;
  if ([1, 2].includes(currentInfo.value.levelType)) {
    targetId = currentInfo.value.id;
  } else {
    targetId = currentInfo.value.parentId;
  }
  if (isRepeat(formData.name, targetId)) {
    formData.name = addItenNewName.value;
  }
  if (
    dialogType.value === ConstructMenuOperator.singleLevel ||
    dialogType.value === ConstructMenuOperator.addOutProject ||
    dialogType.value === ConstructMenuOperator.addOutChange ||
    dialogType.value === ConstructMenuOperator.addOutVisa ||
    dialogType.value === ConstructMenuOperator.addOutOmission ||
    dialogType.value === ConstructMenuOperator.addOutClaimant ||
    dialogType.value === ConstructMenuOperator.addOutOther
  ) {
    addSingle();
  } else if (dialogType.value === ConstructMenuOperator.singleChildLevel) {
    addChildSingle();
  } else {
    addUnit();
  }
};
// 移开鼠标清除编辑状态
const moveMouse = () => {
  const $table = vexTable.value;
  $table.clearEdit();
};
const saveInfo = row => {
  const field = 'name';
  moveMouse();
  const oldRow = oldTreeData.value.find(i => i.id === row.id);
  row.name = removeSpecialChars(row.name);
  row.name = row.name.trim();
  let value = row.name;
  if (!row.name) {
    row.name = row.copyName;
    return;
  }
  //判断值是否变化
  if (row.name === oldRow.name) {
    return;
  }
  //字符长度50限制
  if (row.name.length > 50) {
    row.name = value.slice(0, 50);
  }
  if (isRepeat(row.name, row.parentId, row.id)) {
    row.name = addItenNewName.value;
  }
  if (row.levelType === 2) {
    updateSingleProject(row);
  } else if (row.levelType === 3) {
    updateUnitProject(row);
  } else {
    updateConstructProject(row);
  }
  row.copyName = row.name;
};
let addItenNewName = ref();
//判断同级名称是否重复  重复拼接_1
const isRepeat = (name, parentId, tarId = null) => {
  console.log('addItem', addItem);

  //判断统计添加的单位/单项名称是否重复出现
  let flag = props.treeData.some(
    item =>
      item.levelType === addItem.levelType && //leveltype不同不作判断
      item.id !== tarId &&
      item.parentId === parentId &&
      item.name.trim() === name.trim()
  );
  if (flag) {
    let siblingsNodes = props.treeData.filter(
      item => item.levelType === addItem.levelType && item.parentId === parentId
    );
    const test = handleName([...siblingsNodes], {
      type: 'add',
      checkName: name,
      name: name,
    });
    addItenNewName.value = test.name;
    console.log('test', test, addItenNewName.value);
  }
  return flag; //true--名称重复，flse---名称不重复
};
const handleName = (initList, current) => {
  //initList---同级列表数据   current--增加的名称数据
  function getNameCount(namesMap, name) {
    return (namesMap.get(name) || 0) + 1;
  }

  function setName(item) {
    let NAME = '';
    let count = 0;
    let nameList = [];
    for (let [k, v] of namesMap) {
      nameList.push(k.trim());
    }

    for (let [index, name] of nameList.entries()) {
      let lastName = index > 0 ? `${item.name}_${index + 1}` : `${item.name}`;
      let currentName = index > 0 ? `${item.name}_${index}` : `${item.name}`;
      if (
        !nameList.includes(lastName.trim()) &&
        nameList.includes(currentName.trim())
      ) {
        NAME = lastName.trim();
        namesMap.set(lastName.trim(), 0);
        break;
      } else if (
        nameList.includes(lastName.trim()) &&
        !nameList.includes(currentName.trim())
      ) {
        NAME = currentName.trim();
        namesMap.set(currentName.trim(), 0);
        break;
      }
    }

    if (namesMap.has(item.checkName)) {
      NAME = NAME || `${item.name}_${nameList.length}`;
    } else {
      NAME = item.name;
    }

    return NAME;
  }

  // 初始化一个映射存储每个名称出现的次数
  let namesMap = new Map();

  initList.forEach(item => {
    const count = getNameCount(namesMap, item.name);
    namesMap.set(item.name, count);
  });
  const currentList = [current];
  const renamedItems = currentList.reduce((acc, item, index) => {
    const count = getNameCount(namesMap, item.checkName);

    const newName =
      count > 1 ? `${item.checkName}_${count - 1}` : `${item.checkName}`;

    let newItem = { ...item, name: newName, num: count };
    if (namesMap.has(newName)) {
      const handleName = setName({ ...item });
      newItem = { ...item, name: handleName, num: count };
    }

    namesMap.set(item.checkName, count);
    acc.push(newItem);

    return acc;
  }, []);

  for (let i of renamedItems) {
    const count = getNameCount(namesMap, i.checkName);
    i.num = count;
  }
  return renamedItems[0];
};
// 修改工程项目名称
const updateConstructProject = row => {
  let apiData = {
    constructId: row.id,
    constructName: row.name,
  };
  csProject.editConstructProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      // emit('getTreeList')
      store.SET_IS_REFRESH_BASE_INFO(!store.isRefreshBaseInfo);
      store.SET_PROJECT_NAME(row.name);
    } else {
      message.error('修改失败');
    }
  });
};
// 修改单项名称
const updateSingleProject = row => {
  let apiData = {
    constructId: constructSequenceNbr,
    singleId: row.id,
    singleName: row.name,
  };
  csProject.updateSingleProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
    } else {
      message.error('修改失败');
    }
  });
};
// 修改单位名称
const updateUnitProject = row => {
  let apiData = {
    constructId: constructSequenceNbr,
    singleId: row.parentId,
    unitId: row.id,
    unitName: row.name,
  };
  csProject.updateUnitProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      store.SET_IS_REFRESH_BASE_INFO(!store.isRefreshBaseInfo);
    } else {
      message.error('修改失败');
    }
  });
};

const deleteTip = async (title, content) => {
  new Promise((resolve, reject) => {
    VXETable.modal
      .confirm({
        content: content,
        className: 'dialog-comm confirm-dialog',
        status: 'error',
        title,
        iconStatus: 'vxe-icon-info-circle',
      })
      .then(res => {
        if (res === 'confirm') {
          if (currentInfo.value.levelType === 2) {
            deleteSingleProject();
          } else {
            deleteUnitProject();
          }
          resolve(true);
        } else {
          resolve(false);
        }
      });
  });
};

const getIconType = levelType => {
  const map = {
    1: 'icon-gongchengxiangmu',
    2: 'icon-danxianggongcheng',
    3: 'icon-danweigongcheng1',
  };
  return map[levelType];
};
const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : '人材机数据已修改，是否应用整个工程项目?';
  Modal.confirm({
    title: `${infoText}`,
    onOk() {
      store.humanUpdataData.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
    },
    onCancel() {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
    },
  });
};
// const humanSave = oldVal => {
//   let apiData = {
//     constructId: store.currentTreeInfo?.id,
//     constructProjectRcjList: JSON.parse(
//       JSON.stringify(store.humanUpdataData.updataData)
//     ),
//   };
//   csProject.changeRcjConstructProject(apiData).then(res => {
//     if (res.status === 200) {
//       resetHumanData(oldVal);
//       unifyData.value.disabled = true;
//     }
//   });
// };

// const feeTotalSave = oldVal => {
//   if (store.humanUpdataData.updataData.policy) {
// feePro
//   .checkPolicyDocument(
//     JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
//   )
// .then(res => {});
// }
// if (store.humanUpdataData.updataData.feeTotal) {
// feePro
//   .unifiedUse(
//     JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
//   )
//   .then(res => {});
//   }
//   resetHumanData(oldVal);
//   unifyData.value.disabled = true;
// };
const resetHumanData = oldVal => {
  store.SET_HUMAN_UPDATA_DATA(null);
  vexTable.value?.setCurrentRow(oldVal.newValue);
  currentChangeEvent(oldVal);
};
// 点击左侧树节点
const currentChangeEvent = newValue => {
  moveMouse();
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    vexTable.value?.setCurrentRow(newValue.oldValue); //人材机数据有需要保存的，先确定是否要先保存
    saveHumanData(newValue);
    return;
  }
  let obj = {
    constructId: '',
    name: '',
    singleId: '',
    singleName: '',
  };
  if (newValue.row.levelType === 3) {
    props.treeData.forEach(item => {
      if (item.levelType === 1) {
        obj.constructId = item.id;
        obj.name = item.name;
      } else if (item.id === newValue.row.parentId) {
        obj.singleId = item.id;
        obj.singleName = item.name;
      }
    });
  } else if (newValue.row.levelType === 2) {
    props.treeData.forEach(item => {
      if (item.levelType === 1) {
        obj.constructId = item.id;
      }
      // obj.name = item.name;
    });
    obj.singleId = newValue.row.id;
    obj.name = newValue.row.name;
  } else {
    obj.constructId = newValue.row.id;
    obj.name = newValue.row.name;
  }
  currentInfo.value = newValue.row;
  store.SET_CURRENT_TREE_INFO(newValue.row);
  store.SET_CURRENT_TREE_GROUP_INFO(obj);
  if (newValue.row.levelType === 3) {
    getUnitStage();
  }
};

const getUnitStage = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  jiesuanApi.getUnitStage(apiData).then(res => {
    if (res.status === 200 && res.result) {
      store.SET_STAGE_COUNT(res.result);
    } else {
      store.SET_STAGE_COUNT(0);
    }
  });
};

const getPlaceholder = levelType => {
  const map = {
    1: '项目工程',
    2: '单项工程',
    3: '单位工程',
  };
  return `请输入${map[levelType]}名称`;
};

const menuOperator = new ConstructMenuOperator();

// 右键操作
const rightClickInfo = reactive({
  tableMenu: {
    body: {
      options: [[]],
    },
    visibleMethod({ row, type, options }) {
      setCheckRow(row);
      if (type === 'body') {
        let parent = vexTable.value?.getParentRow(row) || {};
        row.parent = parent;
        menuOperator.resetMenus(row, props.treeData);
        menuOperator.menusJson();
        // isValid: 1:不受其他影响直接显示，2：需要同时满足合同外数据才显示，3：不显示
        console.log('options', options);
        options.forEach(list => {
          list.forEach(item => {
            item.disabled = item.isValid === 4 ? true : false;
            if (item.isValid === 1) {
              item.visible = true;
            } else if (item.isValid === 2) {
              item.visible = !row.originalFlag;
            } else if (item.isValid === 3) {
              item.visible = false;
            }
            if (item.isValid === 4) {
              item.disabled = true;
              item.visible = !row.originalFlag;
            }
          });
        });
      }
      return true;
    },
  },
});
rightClickInfo.tableMenu.body.options = [menuOperator.menus];

const contextMenuClickEvent = async ({ menu, row }) => {
  setCheckRow(row);
  const menuLevel = menu.menuLevel;
  if (menuLevel === ConstructMenuOperator.delete) {
    const title = '提示';
    const content =
      row.levelType === 2
        ? '是否确定删除，删除后将会将单项工程下关联的所有的数据删除？'
        : '是否删除该单位工程？';
    await deleteTip(title, content);
    return;
    // 如果点击为导入结算项目
  } else if (menuLevel === ConstructMenuOperator.importFile) {
    emit('handleChange', { key: 'importJs' });
    return;
    // 如果点击为导出结算项目
  } else if (menuLevel === ConstructMenuOperator.exportFile) {
    emit('handleChange', { key: 'exportJs' });
    return;
    // 如果点击为工程归属
  } else if (menuLevel === ConstructMenuOperator.projectBelong) {
    emit('handleChange', { key: 'projectBelong' }, 1, row);
    return;
  } else if (
    menuLevel === ConstructMenuOperator.importUnit ||
    menuLevel === ConstructMenuOperator.importVisa ||
    menuLevel === ConstructMenuOperator.importOmission ||
    menuLevel === ConstructMenuOperator.importClaimant ||
    menuLevel === ConstructMenuOperator.importOther ||
    menuLevel === ConstructMenuOperator.importUnity
  ) {
    emit('handleChange', { key: 'other' }, 2, row);
    return;
  }
  setDialogInfo(menuLevel, row);
};
onMounted(() => {
  console.log('结算左侧树');
  initHandler();
});

const initHandler = () => {
  queryEngineerMajorList();
  nextTick(() => {
    setCheckRow(props.treeData[0]); //默认选中第一条数据
  });
  //设置侧边树每一项的copyName
  xeUtils.clone(props.treeData)?.map(i => {
    if (i.name) {
      i.copyName = i.name;
    }
    return i;
  });

  //默认展开第一项
  setTimeout(() => {
    const $table = vexTable.value;
    let single = props.treeData.find(
      item => item.levelType === 2 && item.children && item.children.length > 0
    );
    single
      ? $table.setTreeExpand([props.treeData[0], single], true)
      : $table.setTreeExpand(props.treeData[0], true); //展开第一个有children的单项
  }, 0);
};

//设置默认选中数据
const setCheckRow = checkRow => {
  let newValue = { row: { ...checkRow } };
  currentChangeEvent(newValue); //将选中的当前行存储信息
  vexTable.value?.setCurrentRow(checkRow);
  expandVexTable.value.setCurrentRow(checkRow);
};
//添加子单项工程
const addChildSingle = async () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleName: formData.name,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
    type: currentInfo.value.type,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  await csProject
    .addSubSingleProject(apiData)
    .then(async res => {
      console.log('添加子单项', apiData, res);
      if (res.status === 200) {
        let data = {
          ...apiData,
          singleId: res.result.sequenceNbr,
        };
        console.log('handleSingleProject', data);
        await jiesuanApi.handleSingleProject(data);
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
// 添加单项工程
const addSingle = async () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleName: formData.name,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  // 如果是新建变更单项
  if (dialogType.value === ConstructMenuOperator.addOutChange) {
    apiData.type = 1;
    // 如果是新建签证单项
  } else if (dialogType.value === ConstructMenuOperator.addOutVisa) {
    apiData.type = 2;
    // 如果是新建漏项单项
  } else if (dialogType.value === ConstructMenuOperator.addOutOmission) {
    apiData.type = 3;
    // 如果是新建索赔单项
  } else if (dialogType.value === ConstructMenuOperator.addOutClaimant) {
    apiData.type = 4;
    // 如果是新建其他单项
  } else if (dialogType.value === ConstructMenuOperator.addOutOther) {
    apiData.type = 5;
  }
  await csProject
    .addSingleProject(apiData)
    .then(async res => {
      console.log('添加单项', res, apiData);
      if (res.status === 200) {
        let data = {
          ...apiData,
          singleId: res.result.sequenceNbr,
        };
        console.log('handleSingleProject', data);
        await jiesuanApi.handleSingleProject(data);
        submitLoading.value = false;
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
const init = () => {
  dialogVisible.value = false;
  emit('getTreeList');
};
// 添加单位工程
const addUnit = async () => {
  let nowCurrentInfo = currentInfo.value;
  let singleId = '';
  // 如果是单项工程
  if (nowCurrentInfo.levelType === 2) {
    singleId = nowCurrentInfo.id;
  } else {
    singleId = nowCurrentInfo.parentId;
  }
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleId,
    unitName: formData.name,
    constructMajorType: formData.type,
    clickId: nowCurrentInfo.id,
    clickLevelType: nowCurrentInfo.levelType,
    libraryCode: formData.libraryCode,
    secondInstallationProjectName: formData.secondInstallationProjectName,
  };
  if (nowCurrentInfo.levelType === 2) {
    apiData.oldSingleId = nowCurrentInfo.id;
  }
  if (nowCurrentInfo.levelType === 3) {
    apiData.oldUnitId = nowCurrentInfo.id;
  }
  csProject
    .addUnitProject(apiData)
    .then(res => {
      // console.log('添加单位', res);
      if (res.status === 200) {
        submitLoading.value = false;
        store.isAddUnit = true;
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
// 处理右键点击的是工程项目
const handleRightClick = async treeData => {
  let childArr = treeData.children;
  let nowCurrentInfo = {};
  for (let i in childArr) {
    // 如果是新建变更
    if (
      dialogType.value === ConstructMenuOperator.addOutChange &&
      childArr[i].type === 1
    ) {
      childArr[i]['parent'] = treeData;
      nowCurrentInfo = childArr[i];
      // 如果是新建签证
    } else if (
      dialogType.value === ConstructMenuOperator.addOutVisa &&
      childArr[i].type === 2
    ) {
      childArr[i]['parent'] = treeData;
      nowCurrentInfo = childArr[i];
      // 如果是新建漏项
    } else if (
      dialogType.value === ConstructMenuOperator.addOutOmission &&
      childArr[i].type === 3
    ) {
      childArr[i]['parent'] = treeData;
      nowCurrentInfo = childArr[i];
      // 如果是新建索赔
    } else if (
      dialogType.value === ConstructMenuOperator.addOutClaimant &&
      childArr[i].type === 4
    ) {
      childArr[i]['parent'] = treeData;
      nowCurrentInfo = childArr[i];
      // 如果是新建其他
    } else if (
      dialogType.value === ConstructMenuOperator.addOutOther &&
      childArr[i].type === 5
    ) {
      childArr[i]['parent'] = treeData;
      nowCurrentInfo = childArr[i];
    }
  }
  return nowCurrentInfo;
};

//获取专业列表下拉列表
const queryEngineerMajorList = () => {
  csProject
    .getEngineerMajorList({ deStandard: store.deType })
    .then(function (response) {
      console.log('🚀 ~ 获取专业列表下拉列表:', response);
      if (response.status === 200) {
        response.result.map(item => {
          engineerMajorList.value.push({
            key: item.sequenceNbr,
            value: item.unitProjectName,
          });
        });
      }
    });
};

// 删除单项工程
const deleteSingleProject = () => {
  console.log('删除单项', {
    constructId: constructSequenceNbr,
    singleId: currentInfo.value.id,
  });
  csProject
    .delSingleProject({
      constructId: constructSequenceNbr,
      singleId: currentInfo.value.id,
    })
    .then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        emit('getTreeList');
      }
    });
};

// 删除单位工程
const deleteUnitProject = () => {
  csProject
    .delUnitProject({
      constructId: constructSequenceNbr,
      singleId: currentInfo.value.parentId,
      unitId: currentInfo.value.id,
    })
    .then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        emit('getTreeList');
      }
    });
};

const typeChange = value => {
  formData.secondInstallationProjectName = null;
  secondInstallationProjectNameByDropdownList();
  constructMajorTypeDropdownList();
};

const constructMajorTypeDropdownList = () => {
  csProject
    .getMainDeLibrary({
      constructMajorType: formData.type,
    })
    .then(res => {
      if (res.status === 200) {
        majorTypeDropdownList.value = res.result;
        majorTypeDropdownList.value.forEach(item => {
          if (item.defaultDeFlag === 1) {
            formData.libraryCode = item.libraryCode;
          }
        });
      }
    });
};

const secondInstallationProjectNameByDropdownList = () => {
  csProject
    .getSecondInstallationProjectName({
      constructMajorType: formData.type,
      deStandard: store.deType,
    })
    .then(res => {
      if (res.status === 200) {
        secondDropdownList.value = res.result;
        const hasDq = res.result.some(i => {
          return i.cslbName === '电气设备安装工程';
        });
        formData.secondInstallationProjectName =
          hasDq && formData.type === '安装工程'
            ? '电气设备安装工程'
            : secondDropdownList.value[0].cslbName;
      }
    });
};

defineExpose({ setCheckRow });
</script>
<style lang="scss" scoped>
.expand-tree-container {
  width: 47px;

  ::v-deep(.vxe-table) {
    width: 47px;

    .vxe-body--column {
      height: 30px !important;
    }

    .vxe-tree--btn-wrapper {
      display: none;
    }

    .vxe-cell--tree-node {
      .vxe-tree-cell {
        position: relative;

        .expand-span,
        input {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .vxe-table--render-default .vxe-tree-cell {
      padding-left: 0;
      position: relative;
    }
  }
}
</style>
