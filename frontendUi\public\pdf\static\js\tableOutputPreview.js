/**
 * <AUTHOR>
 * @Description 表格输出预览
 * @Date 11:05 2020/7/16
 **/
const { ipcRenderer: ipc } =
  (window.top.require && window.top.require('electron')) ||
  window.top.electron ||
  {};

var spid = GetQueryString('spid'); //项目id
var supid = GetQueryString('supid'); //单位id
var sppid = GetQueryString('sppid'); //专业id
var isProject = GetQueryString('isProject'); //1：表示项目 2单位 3专业
var pageNum = 0; //计算页数
var selectTableSubTypeTdid = parent.selectTableSubTypeTdid; //选中表格子类型tdid

var columnNum = 0; //记录 表格列数，用于打印空白填充，合并列
var markRowsapnNumAry = []; //用于记录 需要合并行 数据，每列默认1行
var markTemporaryRowsapnNumAry = []; //用于换页 临时记录 需要合并行 数据，每列默认1行
var tableWidth = 0; //记录 表格宽度
var pageBreakRowNumAry = []; //记录 需要换页的 行数

var tableDivLimitHei = 0; //725;//用于拆分表的限制高度 默认是横
var fillContentLimitHei = 0; //用于填充表限制高度 默认是横626 纵
var serialNumberSign = 0; //序号td标志 0表示不是，1表示是，一页只允许出现1个是

var contentnRowsNumber = 0; //内容行 条数 默认从0开始获取,用于分页请求
var rowsAddedNumber = 0; //记录已经添加的行数，用于下一次请求合并列数据

var paperOrientationHorizontal = false; //纸张方向 true横向 false纵向
var dataAcquisitionComplete = false; //数据获取完毕 默认 false没有

var pageSpacing = 3;
var specifyPage = false; //是否正在指定页面

var pageRichtext = '';

$(function () {
  //还原顶部页数
  // parent.improveTopMenuInformation('1');
  changeBodyHei();
  listenPageScrolling();

  //  //获取数据
  //  getTableOutData();
  const reportFormData = window.top.localStorage.getItem('reportForm');
  if (reportFormData) {
    pageRichtext = '';
    let d = { ...JSON.parse(reportFormData) }.rows.reduce(
      (previousValue, currentValue) => {
        return previousValue + currentValue.height;
      },
      0
    );
    console.log('总高度', d);

    getTableOutData({ data: { ...JSON.parse(reportFormData) } });
    $('#htmlContent').html(pageRichtext);
  }

  weaterMaskFn({
    text: '云算房计价软件', // 水印文案
    opacity: '.5', // 透明度
    fontSize: '18', // 字体大小
    color: '#333', // 字体颜色
    rotate: '-30', // 旋转角度deg
    len: 5, // 相邻文字间距
  });
});

//获取项目结构数据
function getTableOutData(data) {
  console.log('🚀 ~:64 ~ getTableOutData ~ data:', data);
  if (data) {
    if (contentnRowsNumber == 0) {
      //判断是横向还是纵向
      paperOrientation(data.data.print?.landSpace || false);
      //表格
      //记录列数与初始化每列rowsapn
      columnNum = data.data.columns.length; //列数，用于填充
      if ($.isArray(data.data.columns)) {
        data.data.columns.forEach(function (value) {
          if (markRowsapnNumAry) {
            markRowsapnNumAry.push(1); //初始化每列 rowspan值默认是1
          }
          if (pageNum == 0) {
            tableWidth += parseFloat(
              excelWidthOrHeightExchange(value.width, true)
            );
          }
        });
      }
      if (data.data.pageResult) {
        pageBreakRowNumAry = data.data.pageResult.sheetBreak; //记录需要强制换页行数
      }
      //添加表头
      addTableTitleHtml(data);
      //添加 tbody 内容
      addTbodyContent(data);
    } else {
      //表格
      //记录列数与初始化每列rowsapn
      columnNum = data.data.columns.length; //列数，用于填充
      if ($.isArray(data.data.columns)) {
        data.data.columns.forEach(function (value) {
          console.log(
            '🚀 ~ file: tableOutputPreview.js:89 ~ data.data.columns.forEach ~ markRowsapnNumAry:',
            markRowsapnNumAry
          );
          if (markRowsapnNumAry) {
            markRowsapnNumAry.push(1); //初始化每列 rowspan值默认是1
          }
          if (pageNum == 0) {
            tableWidth += parseFloat(
              excelWidthOrHeightExchange(value.width, true)
            );
          }
        });
      }
      if (data.data.pageResult) {
        pageBreakRowNumAry = data.data.pageResult.sheetBreak; //记录需要强制换页行数
        if (pageBreakRowNumAry) {
          //代表按页 分的
          fillInEmptyTrContent();
          //添加表头
          addTableTitleHtml(data);
        }
      }
      //添加 tbody 内容
      addTbodyContent(data);
    }

    //当前页
    // var currentPageTd = $(".currentFillTbody td[serialSignA='1']")
    //   .parents('tr')
    //   .prev()
    //   .find('td')
    //   .last();
    // $(currentPageTd).css('text-align', 'right');
    // $(currentPageTd)
    //   .find('.showSpanPre')
    //   .html('第' + pageNum + '页&nbsp;共' + pageNum + '页');
    // //所有页
    // var allPageTd = $("td[serialSignA='1']")
    //   .parents('tr')
    //   .prev()
    //   .find('td:last-child');
    // allPageTd.each(function (index, singlePageTd) {
    //   var showSpanPre = $(singlePageTd).find('.showSpanPre');
    //   if (showSpanPre.length > 0) {
    //     var contentAry = $(showSpanPre).html().split('共');
    //     $(showSpanPre).html(contentAry[0] + '共' + pageNum + '页');
    //   }
    // });
    if (data.data.pageResult) {
      contentnRowsNumber = data.data.pageResult.start;
      if (data.data.pageResult.total == contentnRowsNumber) {
        dataAcquisitionComplete = true;
        changePartentMenuInfo();
      } else {
        if (data.data.type != 'QUANTITY') {
          //表示不等于时候，需要加上上次添加的行数
          rowsAddedNumber = rowsAddedNumber + data.data.rows.length;
        }
        getTableOutData();
      }
    } else {
      dataAcquisitionComplete = true;
      changePartentMenuInfo();
    }
  }
}
//纸张方向
function paperOrientation(isHorizontal) {
  if (isHorizontal) {
    //true 横向
    paperOrientationHorizontal = true;
    tableDivLimitHei = 741;
    fillContentLimitHei = 680.38;
    $('.container').css('width', '1103px');
    var html =
      "<style media='print'>" +
      '@page{' +
      'size:landscape;' +
      'margin:0;' +
      '}' +
      '</style>';
    $('head').append(html);
  } else {
    //纵向
    paperOrientationHorizontal = false;
    tableDivLimitHei = 1089;
    // fillContentLimitHei = 1043.62;
    fillContentLimitHei = 1089;

    $('.container').css('width', '770px');
    var html =
      "<style media='print'>" +
      '@page{' +
      'size:portrait;' +
      'margin:0;' +
      '}' +
      '</style>';
    $('head').append(html);
  }
}
//修改父类顶部菜单信息
function changePartentMenuInfo() {
  fillInEmptyTrContent();
  //完善父类顶部菜单信息
  // parent.improveTopMenuInformation && parent.improveTopMenuInformation(pageNum);
  $('.tableOutputContainer').getNiceScroll().resize();
  //判断是否循环打印
  parent.checkWhetherMultipleSelectionPrinting &&
    parent.checkWhetherMultipleSelectionPrinting();
}
//添加表头基本信息
//lineBreak true 行中断
function addTableTitleHtml(data) {
  var headhtml = '';
  if ($('.currentFillTbody')) {
    var headTrAry = $('.currentFillTbody').find('tr[head=0]');
    headTrAry.each(function (index, val) {
      headhtml += val.outerHTML;
    });
  }
  pageNum++;
  //添加页面页面
  //当前页
  var currentPageTd = $(".currentFillTbody td[serialSignA='1']")
    .parents('tr')
    .prev()
    .find('td')
    .last();
  $(currentPageTd).css('text-align', 'right');
  var currentPages = pageNum - 1;
  $(currentPageTd)
    .find('.showSpanPre')
    .html('第' + currentPages + '页&nbsp;共' + currentPages + '页');
  //所有页
  var allPageTd = $("td[serialSignA='1']")
    .parents('tr')
    .prev()
    .find('td:last-child');
  allPageTd.each(function (index, singlePageTd) {
    var showSpanPre = $(singlePageTd).find('.showSpanPre');
    if (showSpanPre.length > 0) {
      var contentAry = $(showSpanPre).html().split('共');
      $(showSpanPre).html(contentAry[0] + '共' + pageNum + '页');
    }
  });
  serialNumberSign = 0;
  $('.currentFillTbody').removeClass('currentFillTbody');
  var html = '';

  let colHtml = '';
  if (data?.data.columns) {
    colHtml += '<colgroup>';
    data.data.columns.forEach(e => {
      colHtml += `<col span="1" style="width:${e.width}px;">`;
    });
    colHtml += '</colgroup>';
  }

  if (paperOrientationHorizontal) {
    //横向 printBottomBlank用于打印填充空白
    html =
      '<div class="tableDivBox pdfpage page3" style="width:1087.5px;">' +
      '<table class="horizontalTable" cellspacing="0" cellpadding="6"  style="word-wrap: break-word; word-break: break-all;table-layout:fixed;">' +
      colHtml +
      '<tbody class="currentFillTbody">';
    // html +=
    //   '<tr style="border:0;height:40px;"><td style="border: 0;height:0;" colspan="' +
    //   columnNum +
    //   '"></td></tr>';
    if (headhtml.length > 0) {
      html += headhtml;
    }
    html += '</tbody>' + '</table>' + '</div>';
    $('.container').append(html);
  } else {
    //纵向
    html =
      '<div class="tableDivBox pdfpage page2" style="width:770px;">' +
      '<table class="verticalTable" cellspacing="0" cellpadding="6"  style="word-wrap: break-word; word-break: break-all; table-layout:fixed;">' +
      colHtml +
      '<tbody class="currentFillTbody" >';
    // html +=
    //   '<tr style="border:0;height:26px;"><td style="border: 0;height:0;" colspan="' +
    //   columnNum +
    //   '"></td></tr>';
    if (headhtml.length > 0) {
      html += headhtml;
    }
    html += '</tbody>' + '</table>' + '</div>';
    $('.container').append(html);
  }
}
//添加表格内容
//data 数据 isPageBreak是换页
function addTbodyContent(data, isPageBreak) {
  var rowspanNumAry;
  if (isPageBreak) {
    rowspanNumAry = markTemporaryRowsapnNumAry; //临时 换页
  } else {
    rowspanNumAry = markRowsapnNumAry;
  }

  if ($.isArray(data.data.rows)) {
    //表格添加
    data.data.rows.forEach(function (row, rowIndex) {
      //遍历 添加行
      var trHtml = '';

      trHtml +=
        '<tr head=' +
        row.header +
        ' style="height:' +
        excelWidthOrHeightExchange(row.height, false) +
        'px;" >';
      var currentTrMerges = [];
      if ($.isArray(data.data.merges)) {
        //用于获取当前行 每列td需要的 colsapn、rowsapn合并数据
        data.data.merges.forEach(function (merge, index) {
          if (merge.firstRow == rowIndex + rowsAddedNumber) {
            currentTrMerges.push(merge);
          } else {
            return;
          }
        });
      }
      for (var cloIndex = 0; cloIndex < data.data.columns.length; cloIndex++) {
        //遍历 添加列
        var colsapnNum = 1;
        var rowsapnNum = rowspanNumAry[cloIndex]; //用于检验 前面的行 进行 合并行 是否完成
        if (rowsapnNum == 1) {
          //表示前面的行， 合并行 已完成，可以添加td
          var colValue = JSON.parse(
            JSON.stringify(data.data.columns[cloIndex])
          );
          currentTrMerges.forEach(function (value) {
            //用于计算当前td，合并行数rowsapn与合并列数
            if (value.firstCol == cloIndex) {
              colsapnNum = value.lastCol - value.firstCol + 1;
              rowsapnNum = value.lastRow - value.firstRow + 1;

              if (colsapnNum > 1) {
                // 合并行单元格时，合并后的单元格宽度累加
                for (
                  var colSpanIndex = 1;
                  colSpanIndex < colsapnNum;
                  colSpanIndex++
                ) {
                  var currentColVal =
                    data.data.columns[cloIndex + colSpanIndex];
                  colValue.width += currentColVal.width;
                }
              }
              // data.data.merges.splice($.isArray(value, data.data.merges), 1); //已经计算后，删除，调高运行速度
              return;
            }
          });
          if ($.isArray(data.data.cells)) {
            var contentIndex = rowIndex * data.data.columns.length + cloIndex; //因为合并行，所以确定下一个数据index
            // console.log('🚀 ~ cloIndex:', cloIndex);
            // console.log('🚀 ~ data.data.columns:', data.data.columns);
            // console.log('🚀 ~ rowIndex:', rowIndex);
            var content = data.data.cells[contentIndex];
            var contentStr = '';
            if (content) {
              contentStr = content.content;
            }

            var alignment = fontStyle(content, colValue); //添加样式
            if (
              serialNumberSign == 0 &&
              (contentStr == '序号' ||
                contentStr == '编号' ||
                contentStr == '项目编号' ||
                contentStr == '清单\n' + '序号')
            ) {
              serialNumberSign = 1;
              trHtml +=
                '<td  serialSignA=1 colspan=' +
                colsapnNum +
                ' rowspan=' +
                rowsapnNum +
                ' style="' +
                alignment +
                '" ><pre class="showSpanPre" style="margin: 0px 3px 0px 3px;">' +
                contentStr +
                '</pre></td>';
            } else {
              if (!!contentStr?.richText) {
                pageRichtext = contentStr?.richText;
                trHtml +=
                  '<td test2 colspan=' +
                  colsapnNum +
                  ' rowspan=' +
                  rowsapnNum +
                  ' style="' +
                  alignment +
                  '" ><pre class="showSpanPre" style="margin: 0px 3px 0px 4px;" id="htmlContent"> </pre></td>';
              } else {
                trHtml +=
                  '<td test2 colspan=' +
                  colsapnNum +
                  ' rowspan=' +
                  rowsapnNum +
                  ' style="' +
                  alignment +
                  '" ><pre class="showSpanPre" style="margin: 0px 2px;">' +
                  contentStr +
                  '</pre></td>';
              }
            }
          }
          if (rowsapnNum > 1) {
            //合并行控制td
            rowspanNumAry.splice(cloIndex, 1, rowsapnNum); //替换原先记录td的rowspan数据
          }
          if (colsapnNum > 1) {
            //合并列控制td
            if (rowsapnNum > 1) {
              //列合并的同时，如果 行也需要合并就要把rowspanNumAry数据替换
              for (var i = 1; i < colsapnNum; i++) {
                cloIndex = cloIndex + 1;
                rowspanNumAry.splice(cloIndex, 1, rowsapnNum); //替换
              }
            } else {
              cloIndex = cloIndex + colsapnNum - 1;
            }
          }
        } else {
          rowsapnNum--;
          rowspanNumAry.splice(cloIndex, 1, rowsapnNum); //替换
        }
      }
      trHtml += '</tr>';

      $('.currentFillTbody').append(trHtml);
      addSplitTableLogicTwo(trHtml);

      //用于主动换页，杭州表2-3
      if (
        $.isArray(pageBreakRowNumAry) &&
        pageBreakRowNumAry.length > 0 &&
        !isPageBreak
      ) {
        var linebreakKey = pageBreakRowNumAry[0]; //获取数组第一个换页的 触发行数
        if (linebreakKey == rowIndex && rowIndex != data.data.rows.length - 1) {
          //确定换页行，并判断不是最后一页
          fillInEmptyTrContent(); //填充上一页空白
          addTableTitleHtml(data);
          pageBreakRowNumAry.splice(0, 1); //删除第一个数据，提高运行效率
        }
      }
    });
  }
}
//拆分表格
function addSplitTableLogicTwo(trHtml) {
  var perPageHeight;
  if (paperOrientationHorizontal) {
    // 横向
    perPageHeight =
      $('.currentFillTbody').parents('.tableDivBox').height() - 40;
  } else {
    // - 20
    perPageHeight = $('.currentFillTbody').parents('.tableDivBox').height();
  }
  // console.log('🚀拆分表格', perPageHeight);
  if (perPageHeight >= fillContentLimitHei) {
    //大于
    $('.currentFillTbody tr').last().remove();
    fillInEmptyTrContent();
    addTableTitleHtml();
    $('.currentFillTbody').append(trHtml);
  }
}
//填充空白Tr数据,用于打印，撑大单页区域
function fillInEmptyTrContent() {
  //用于解决打印填充 （横向）
  if ($('.tableDivBox').length > 0) {
    $('.currentFillTbody').parents('.tableDivBox').height(tableDivLimitHei);
    var fillTrHei;
    if (paperOrientationHorizontal) {
      fillTrHei =
        fillContentLimitHei -
        $('.currentFillTbody').parents('table').outerHeight() -
        40;
    } else {
      fillTrHei =
        fillContentLimitHei -
        $('.currentFillTbody').parents('table').outerHeight() -
        20;
    }
    var fillHtml =
      '<tr class="last" style="border:0;height:' +
      fillTrHei +
      'px;"><td style="border: 0;height:0;" colspan="' +
      columnNum +
      '"></td></tr>';
    // $('.currentFillTbody').append(fillHtml);
  }
}

//以下是编制说明代码
function addDescriptionTitle(data) {
  pageNum++;
  $('.totalPages').html('共' + pageNum + '页');
  $('.currentFillTbody').removeClass('currentFillTbody');
  var html =
    '<div class="descriptionBox pdfpage page1" style="display: inline-block;line-height: 1.5em;background-color: #FFFFFF;text-align: left;overflow: hidden">' +
    '<div style="height: 70px;"></div>' +
    '<table cellspacing="0" cellpadding="0">' +
    '<tbody class="currentFillTbody">' +
    '<tr>' +
    '<td colspan="8" class="tableName" style="border-top: 0;text-align: center;font-size: 24px;height: 62px;line-height: 1.5em;font-weight: 700;font-family: 黑体;vertical-align: center;"><span></span></td>' +
    '</tr>' +
    '<tr class="projectNameTr" style="height: 30px;line-height: 1.5em;text-align: left;">' +
    '<td colspan="6" style="width: 460px;text-align:left;font-weight:400;font-family:宋体, SimSun;font-size:12px;vertical-align:center;"><span class="projectNameSpan"></span></td>' +
    '<td colspan="2" style="width: 80px;text-align: right; font-weight: 400; font-family: 宋体, SimSun; font-size: 12px;">' +
    '<span class="currentPages">第' +
    pageNum +
    '页 </span>' +
    '<span class="totalPages">共' +
    pageNum +
    '页</span>' +
    '</td>' +
    '<tr>' +
    '<td colspan="8" class="descriptionContent" style="padding-left: 4px;padding-right: 4px;border-top: 1px solid #888888;border-left: 1px solid #888888;border-right: 1px solid #888888;border-bottom: 1px solid #888888;width: 552px;font-size:12px;line-height: 1.5em;vertical-align: top;">' +
    '<span style="display: inline-block;width: 544px;font-family: 宋体, SimSun;word-break: break-all;word-wrap: break-word;"></span>' +
    '</td>' +
    '</tr>' +
    '</tbody>' +
    '</table>' +
    '</div>';
  $('.container').append(html);
  if (data.data.bottom && data.data.bottom.length > 0) {
    var html =
      '<tr>' +
      '<td colspan="8"  style="border-top: 0;border-left: 0;border-right: 0;border-bottom: 0;height: 35px;">' +
      '<pre class="bottomStamp"  style="display: inline-block;font-size: 12px;font-family: 宋体, SimSun;">' +
      data.data.bottom +
      '</pre>' +
      '</td>' +
      '</tr>';
    $('.currentFillTbody').append(html);
  }
}
//拆分说明
function addSplitDescriptionLogic(data) {
  //拆分 需要补齐元素
  addDescriptionTitle(data);
  var pageContentAry = [];
  if (data.data.content && data.data.content.length > 0) {
    pageContentAry = data.data.content.split('</p>');
  }
  $('.currentFillTbody')
    .find('.tableName')
    .children('span')
    .html(data.data.title);
  $('.currentFillTbody')
    .find('.projectNameSpan')
    .html('工程名称：' + data.data.project);
  var tableNameHei = $('.currentFillTbody').find('.tableName').outerHeight();
  var projectNameTrHei = $('.currentFillTbody')
    .find('.projectNameTr')
    .outerHeight();
  var contentLimitHei = 817 + 62 + 30 - tableNameHei - projectNameTrHei;
  if (data.data.bottom && data.data.bottom.length > 0) {
    contentLimitHei = contentLimitHei - 35;
  }
  $('#container').css('letter-spacing', '0.26px');

  for (var i = 0; i < pageContentAry.length; i++) {
    var singleP = pageContentAry[i] + '</p>';
    $('.currentFillTbody').find('.descriptionContent>span').append(singleP);
    $('#container p').css('display', 'block');
    $('#container p').css('margin-top', '5px');
    $('#container p').css('margin-bottom', '5px');
    $('.currentFillTbody').find('span').css('line-height', '15.75pt'); //适配word
    var contentHei = $('.currentFillTbody')
      .find('.descriptionContent')
      .parent('tr')
      .outerHeight();
    if (contentHei >= contentLimitHei) {
      //大于
      $('.currentFillTbody').find('.descriptionContent>span p').last().remove();
      $('.currentFillTbody')
        .parents('.descriptionBox')
        .height(tableDivLimitHei);
      $('.currentFillTbody')
        .find('.descriptionContent')
        .height(contentLimitHei);
      addDescriptionTitle(data);
      $('.currentFillTbody')
        .find('.tableName')
        .children('span')
        .html(data.data.title);
      $('.currentFillTbody')
        .find('.projectNameSpan')
        .html('工程名称：' + data.data.project);
      $('.currentFillTbody').find('.descriptionContent>span').append(singleP);
    }
  }

  $('.currentFillTbody').find('.descriptionContent').height(contentLimitHei);
  $('.currentFillTbody').parents('.descriptionBox').height(tableDivLimitHei);

  //把所有的px换成pt，word需要
  var html = $('#container').html();
  var newhtml = html.replace(/(\d+)px/g, function (a, b) {
    var num = (b * 72) / 96;
    return num + 'pt';
  });
  //把行高都转换成em
  newhtml = newhtml.replace(/(\d+)%/g, function (a, b) {
    var num = b / 100;
    return num + 'em';
  });
  $('#container').html(newhtml);
  //表示已全部加载完了
  if (parent.tableDescriptionExport) {
    parent.loopTriggersSingleTablePrinting();
  }
}

//监听页面滚动
function listenPageScrolling() {
  $(document).scroll(function () {
    if (!specifyPage) {
      pageSpacing =
        ($('.container').height() - pageNum * tableDivLimitHei) / pageNum;
      if (pageSpacing <= 0) {
        pageSpacing = 0;
      }
      var scrollTop = $(this).scrollTop();
      var ceilNum = Math.ceil(scrollTop / (tableDivLimitHei + pageSpacing));
      if (ceilNum <= pageNum) {
        if (ceilNum == 0) {
          parent.currentPage = 1;
        } else {
          parent.currentPage = ceilNum;
        }
        // parent.improveTopMenuInformation(pageNum);
      }
    } else {
      specifyPage = false;
    }
  });
}
//滚动到指定元素位置
function scrollToTheSpecifiedElementPosition(currentPage) {
  specifyPage = true;
  var currentTableDivBox = $('.tableDivBox,.descriptionBox')[currentPage - 1];
  var offset = $(currentTableDivBox).offset();
  var scrollTop = offset.top;
  $('.tableOutputContainer').getNiceScroll(0).doScrollTop(scrollTop);
}

//单页打印
function print() {
  if (dataAcquisitionComplete) {
    //http://www.ruanyifeng.com/blog/2011/08/a_detailed_explanation_of_jquery_deferred_object.html
    var printDtd = $.Deferred();

    if (paperOrientationHorizontal) {
      $('.tableDivBox').css('height', '730px');
    } else {
      $('.tableDivBox').css('height', '1075px');
      $('.descriptionBox').css('height', '1075px');
    }

    if (myBrowserType() == 'Safari') {
      //解决safari浏览器只能打印1次问题
      $('.container').print({
        //
        iframe: false,
        deferred: printDtd, //回调函数
      });
    } else {
      $('.container').print({
        deferred: printDtd, //回调函数
      });
    }
    // 上面属性设置
    // globalStyles:true,//是否包含父文档的样式，默认为true
    // mediaPrint:false,//是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
    // stylesheet:null,//外部样式表的URL地址，默认为null
    // noPrintSelector:".no-print",//不想打印的元素的jQuery选择器，默认为".no-print"
    // iframe:true,//是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
    // append:null,//将内容添加到打印内容的后面
    // prepend:null,//将内容添加到打印内容的前面，可以用来作为要打印内容
    // deferred: $.Deferred()//回调函数
    $.when(printDtd)
      .done(function () {
        //表示执行成功
        if (paperOrientationHorizontal) {
          $('.tableDivBox').css('height', '741px');
        } else {
          $('.tableDivBox').css('height', '1089px');
        }
        //打印取消或者完成后去点击下一个表格
        parent.loopTriggersSingleTablePrinting &&
          parent.loopTriggersSingleTablePrinting();
      })
      .fail(function () {});

    // $.print(".container");
    pagesetup_null();
  } else {
    zdalert('温馨提示！', '正在获取数据中，请稍后打印');
  }
}
//打印去掉页眉页脚
function pagesetup_null() {
  try {
    var RegWsh = new ActiveXObject('WScript.Shell');
    hkey_key = 'header';
    RegWsh.RegWrite(hkey_root + hkey_path + hkey_key, '');
    hkey_key = 'footer';
    RegWsh.RegWrite(hkey_root + hkey_path + hkey_key, '');
  } catch (e) {}
}
//字体css样式
function fontStyle(val, colValue) {
  var style = '';
  if (val) {
    if (val.alignment == 1) {
      style = 'text-align:left;';
    } else if (val.alignment == 2) {
      style = 'text-align:center;';
    } else if (val.alignment == 3) {
      style = 'text-align:right;';
    } else {
      style = 'text-align:left;';
    }
    style += 'font-weight:' + val.fontBold + ';';
    style += "font-family:'" + val.fontName + "';";
    style += 'font-size:' + val.fontSize * 1.1 + 'px;';
    if (val.verticalAlignment == 3) {
      style += 'vertical-align:top;';
    } else if (val.verticalAlignment == 1) {
      style += 'vertical-align:middle;';
    } else if (val.verticalAlignment == 2) {
      style += 'vertical-align:bottom;';
    }
    if (val.borderTop == 0) {
      style += 'border-top:0;';
    }
    if (val.borderBottom == 0) {
      style += 'border-bottom:0;';
    }
    if (val.borderLeft == 0) {
      style += 'border-left:0;';
    }
    if (val.borderRight == 0) {
      style += 'border-right:0;';
    }
  }
  style += 'width:' + excelWidthOrHeightExchange(colValue.width, true) + 'px;';
  // debugger
  return style;
}
//excel宽高转换成 前端
function excelWidthOrHeightExchange(value, isWidth) {
  if (isWidth) {
    return value?.toFixed(4);
  } else {
    return value?.toFixed(4);
  }
}
//word导出
function wordExport(tableName) {
  $('#container').wordExport(tableName);
}

//改变body高度
function changeBodyHei() {
  var bodyhtml = $('#showSelectTable', window.parent.document).height();
  $('.tableOutputContainer').height(bodyhtml);
  $('.tableOutputContainer').niceScroll({
    cursorborder: '',
    cursorcolor: '#ccc',
    autohidemode: false,
    scrollspeed: 0,
    cursorwidth: '4px',
  });
  scrollStyle();
}
