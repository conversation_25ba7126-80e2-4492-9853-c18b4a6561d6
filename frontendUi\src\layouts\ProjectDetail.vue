<!--
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-16 10:30:47
 * @LastEditors: wangru
 * @LastEditTime: 2023-12-07 09:39:54
-->
<template>
  <a-layout class="layout">
    <a-layout-header>
      <project-header ref="headerMenu"/>
    </a-layout-header>
    <a-layout-content><router-view :commands="commands" /></a-layout-content>
  </a-layout>
</template>

<script setup>
import ProjectHeader from '@/components/Header/ProjectHeader.vue';
import {ref, watch} from "vue";
import {projectDetailStore} from "@/store/projectDetail.js";
const projectStore = projectDetailStore();
let headerMenu = ref(null);
watch(
    () => headerMenu.value,
    val => {
      projectStore.headerMenu = val;
    },
    { deep: true }
);
</script>
<style lang="scss" scoped>
:deep(.ant-layout-header) {
  background-color: #fff;
  padding: 0;
  height: 56px;
  line-height: 50px;
}
</style>
