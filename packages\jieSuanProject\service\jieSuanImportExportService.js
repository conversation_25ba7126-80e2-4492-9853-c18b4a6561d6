
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const {Service} = require("../../../core");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {JieSuanWinManageUtils} = require("../utils/JieSuanWinManageUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {JieSuanFileUtils} = require("../utils/JieSuanFileUtils");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {ConstructOperationUtil} = require("../../../electron/utils/ConstructOperationUtil");
const {ArrayUtil} = require("../../../electron/utils/ArrayUtil");
const {BaseFeeFile} = require("../../../electron/model/BaseFeeFile");
const {BaseFeeFileRelation} = require("../../../electron/model/BaseFeeFileRelation");
const {BaseManageRate} = require("../../../electron/model/BaseManageRate");
const {BaseGsjRate} = require("../../../electron/model/BaseGsjRate");
const {BaseAnwenRate} = require("../../../electron/model/BaseAnwenRate");
const {arrayToTree} = require("../../../electron/main_editor/tree");
const CalculationTool = require("../../../electron/unit_price_composition/compute/CalculationTool");

class JieSuanImportExportService extends Service {
    constructor(ctx) {
        super(ctx);
    }


    /**
     *  结算导入文件，并验证规则
     * @param args
     * @returns {Promise<any[]|ResponseData>}
     */
    async importYjsFile(args) {
        //保存
        let {constructId} = args;
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: ['YJS']} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail("未选中任何文件");
        }
        //获取选中的路径
        let filePath = result[0];
        if (!JieSuanFileUtils.checkFileExistence(filePath)) {
            console.log("路径有误");
            return;
        }
        //当前的结算项目
        let currentProjectObj = await PricingFileFindUtils.getProjectObjById(constructId);
        //导入后的结算项目
        let importProjectObj = await PricingFileFindUtils.getProjectObjByPath(filePath);


        //结算导入限制1.不能为本地打开状态
        if (JieSuanWinManageUtils.projectIsOpen(importProjectObj.sequenceNbr)) {
            return ResponseData.fail("导入的结算项目正在使用中")
        }
        //结算导入限制2.导入的目标结算文件必须与当前结算文件为同一预算文件生成
        if (currentProjectObj.ysConstructId != importProjectObj.ysConstructId) {
            return ResponseData.fail("导入的目标结算文件必须与当前结算文件为同一预算文件生成")
        }

        //重刷ID
        let projectId = Snowflake.nextId();
        importProjectObj.sequenceNbr=projectId;

        ObjectUtils.updatePropertyValue(importProjectObj, 'constructId', projectId);

        // let  singleProjects=importProjectObj.singleProjects;
        // for(let i =0 ;i<singleProjects.length;i++){
        //     let singleProjectId = Snowflake.nextId();
        //     let  singleProject=singleProjects[i];
        //     singleProject.sequenceNbr=singleProjectId;
        //     let  unitProjects=singleProject.unitProjects
        //     if(!ObjectUtils.isEmpty(unitProjects)){
        //     for(let j =0 ;j<unitProjects.length;j++){
        //         let unitProjectId = Snowflake.nextId();
        //         let unitProject=unitProjects[j];
        //         unitProject.sequenceNbr=unitProjectId;
        //     }
        //  }
        // }
        PricingFileWriteUtils.writeToMemory(importProjectObj);
        let tree = await this.service.jieSuanProject.jieSuanProjectService.generateLevelTreeNodeStructure({sequenceNbr:importProjectObj.sequenceNbr});
        //导入后的项目临时存放在当前项目中
        currentProjectObj.importProjectObj = importProjectObj;
        //await this.service.projectOverviewService.updateYsfFile(projectObjById);
        return tree;
    }


    _treeToArray(tree,array){
        if (ObjectUtils.isNotEmpty(tree.children)){
            array.push(...tree.children);
            tree.children.forEach(k =>{
                this._treeToArray(k,array);
            })
        }
    }




    /**
     * 导入签证索赔
     * @param args
     * @return {Promise<void>}
     */
    async importSingle(args) {
        //当前结算项目
        let currentProjectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let currentProjectObjMap = ConstructOperationUtil.flatConstructTreeToMapById(args.constructId);


        //获取导入后的结算项目
        let importProjectObj = currentProjectObj.importProjectObj;

        //获取导入后所有单位
        let unitList = await this.getImportProjectUnitList(importProjectObj);
        let importProjectObjMap = ConstructOperationUtil.flatConstructTreeToMapByObj(importProjectObj);


        //获取页面选择的结果树
        let projectStructureTree = args.projectStructureTree;

        let array = new Array();
        this._treeToArray(args.projectStructureTree,array);


        let newUnitList = array.filter(k =>k.levelType == 3 && k.whetherNew);

        if (ObjectUtils.isEmpty(newUnitList))return ;


        for (let i = 0; i < newUnitList.length; i++) {
            let {id,parentId,costType} = newUnitList[i];
            let importUnit = importProjectObjMap.get(id);
            importUnit.spId = parentId;
            importUnit.constructId = currentProjectObj.sequenceNbr;
            ObjectUtils.updatePropertyValue(importUnit, 'constructId', currentProjectObj.sequenceNbr);
            importUnit.itemBillProjects = arrayToTree(importUnit.itemBillProjects);
            importUnit.measureProjectTables = arrayToTree(importUnit.measureProjectTables);
            let singleProject = PricingFileFindUtils.getSingleProject(currentProjectObj.sequenceNbr,parentId);
            if (ObjectUtils.isEmpty(singleProject.unitProjects))singleProject.unitProjects = [];
            singleProject.unitProjects.push(importUnit);



            //使用當前工程费率
            //导入费率后按当前项目
            if (costType === "a") {
                //1.取当前项目费率
                let argsModel = {};
                argsModel.levelType = 1;
                argsModel.constructId = importUnit.constructId;
                argsModel.singleId = importUnit.spId;
                argsModel.unitId = importUnit.sequenceNbr;
                let feeCollectionList = this.service.baseFeeFileService.getFeeCollectionList(argsModel)
                //对于项目的取费文件进行去重
                let feeFiles = ArrayUtil.distinctList(feeCollectionList, "feeFileId");
                //2.替换取费费率（无费率，需查询默认库）,并替换单项单位的id
                for (let y = 0; y < importUnit.feeFiles.length; y++) {
                    let currentFeeFile = importUnit.feeFiles[y]
                    let feeFileId = currentFeeFile.feeFileId
                    let feeFile = feeFiles.find((item) => item.feeFileId == feeFileId);
                    currentFeeFile.constructId = importUnit.constructId;
                    currentFeeFile.singleId = importUnit.spId;
                    currentFeeFile.unitId = importUnit.sequenceNbr;
                    if (!ObjectUtils.isEmpty(feeFile)) {
                        currentFeeFile.managementFee = feeFile.managementFee
                        currentFeeFile.profit = feeFile.profit
                        currentFeeFile.fees = feeFile.fees
                        currentFeeFile.anwenRateBase = feeFile.anwenRateBase
                        currentFeeFile.anwenRateAdd = feeFile.anwenRateAdd
                    } else {
                        let baseFeeFileDTO = await this.app.appDataSource.getRepository(BaseFeeFile).findOne({
                            where:{
                                qfCode:currentFeeFile.feeFileCode
                            }
                        });
                        let baseFeeFileRelation = await this.app.appDataSource.getRepository(BaseFeeFileRelation).findOne({
                            where: {
                                qfCode: baseFeeFileDTO.qfCode
                            }
                        });
                        //管理费、利润费率查询
                        let baseManageRates = await this.app.appDataSource.getRepository(BaseManageRate).findOne({
                            where: {
                                rateCode: baseFeeFileRelation.rateCode,
                            },
                        })
                        //规费费率查询
                        let baseGsjRate = await this.app.appDataSource.getRepository(BaseGsjRate).findOne({
                            where: {
                                code: baseFeeFileRelation.projectType,
                                kind: "1"
                            }
                        });
                        //安文费费率查询
                        let baseAnwenRate = await this.app.appDataSource.getRepository(BaseAnwenRate).findOne({
                            where:{
                                libraryCode:baseFeeFileRelation.libraryCode
                            }
                        });
                        currentFeeFile.managementFee = baseManageRates.managementFee3
                        currentFeeFile.profit = baseManageRates.profit3
                        currentFeeFile.fees = baseGsjRate.rate
                        currentFeeFile.anwenRateBase = baseAnwenRate.anwenRate
                        //currentFeeFile.anwenRateAdd = feeFile.anwenRateAdd
                    }
                }

                let calculationTool =new CalculationTool({constructId:argsModel.constructId, singleId:argsModel.singleId, unitId:argsModel.unitId, allData:importUnit.measureProjectTables});
                importUnit.measureProjectTables.filter(item=>item.kind=="04").forEach(item=>{
                    calculationTool.calculationChian(item)
                });
                let calculationToolCsxm =new CalculationTool({constructId:argsModel.constructId, singleId:argsModel.singleId, unitId:argsModel.unitId, allData:importUnit.itemBillProjects});
                importUnit.itemBillProjects.filter(item=>item.kind=="04").forEach(item=>{
                    calculationToolCsxm.calculationChian(item)
                });
            }
            //导入费率按导入项目
            if (costType === "b") {
                //替换单项单位的id
                for (let y = 0; y < importUnit.feeFiles.length; y++) {
                    let currentFeeFile = importUnit.feeFiles[y]
                    currentFeeFile.constructId = importUnit.constructId;
                    currentFeeFile.singleId = importUnit.spId;
                    currentFeeFile.unitId = importUnit.sequenceNbr;
                }
            }




        }

        delete currentProjectObj.importProjectObj;

        //计算工作台底部价格汇总计算
        // PricingFileWriteUtils.countConstructProject(projectObjById.sequenceNbr)

        await this.service.ysfHandlerService.updateYsfFile(currentProjectObj);
    }
    /**
     * 保存导入的结算文件
     * @param args
     * @returns {Promise<void>}
     */
    async saveImportProject(args) {
        //当前结算项目
        let currentProjectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        let currentProjectObjMap = ConstructOperationUtil.flatConstructTreeToMapById(args.constructId);


        //获取导入后的结算项目
        let importProjectObj = currentProjectObj.importProjectObj;

        //获取导入后所有单位
        let unitList = await this.getImportProjectUnitList(importProjectObj);
        let importProjectObjMap = ConstructOperationUtil.flatConstructTreeToMapByObj(importProjectObj);


        //获取页面选择的结果树
        let projectStructureTree = args.projectStructureTree;

        let array = [];
        this._treeToArray(args.projectStructureTree,array);


        for (let i = 0; i < array.length; i++) {
            let {whetherNew,whetherReplace,id,parentId,levelType,oldId} = array[i];

            if (ObjectUtils.isEmpty(whetherNew))continue;

            let importItem = importProjectObjMap.get(id);
            importItem.constructId = currentProjectObj.sequenceNbr;
            importItem.sequenceNbr = Snowflake.nextId();
            ObjectUtils.updatePropertyValue(importItem, 'constructId', currentProjectObj.sequenceNbr);


            //单项
            if (levelType == 2){
                let singleProjectList = PricingFileFindUtils.getSingleProjectList(currentProjectObj.sequenceNbr);
                singleProjectList.push(importItem);
                //替换
                if (whetherReplace == true){
                    this.service.singleProjectService.delSingleProject({constructId:currentProjectObj.sequenceNbr,singleId:oldId});
                }
            }

            //单位
            if (levelType == 3){
                importItem.spId = parentId;
                importItem.itemBillProjects = arrayToTree(importItem.itemBillProjects);
                importItem.measureProjectTables = arrayToTree(importItem.measureProjectTables);
                let singleProject = PricingFileFindUtils.getSingleProject(currentProjectObj.sequenceNbr,parentId);
                if (ObjectUtils.isEmpty(singleProject.unitProjects))singleProject.unitProjects = [];
                singleProject.unitProjects.push(importItem);
                //替换
                if (whetherReplace == true){
                    this.service.singleProjectService.delUnitProject({constructId:currentProjectObj.sequenceNbr,singleId:parentId,unitId:oldId});
                }
            }
        }






        //
        // if (!ObjectUtils.isEmpty(projectStructureTree.children)) {
        //     for (let i = 0; i < projectStructureTree.children.length; i++) {
        //         let singleProjectChild = projectStructureTree.children[i];
        //
        //         if (singleProjectChild.levelType === 2) {
        //             //单项
        //             //原项目对应的单项
        //             let singleProject = currentProjectObjMap
        //             //先判断是否有新增的单项
        //             if (singleProjectChild.whetherNew === true) {
        //                 let newSingleProject = importProjectObjMap.get(singleProjectChild.id);
        //                 let copySingleProject = ConvertUtil.deepCopy(newSingleProject);
        //                 //重新刷新所有的ID
        //                 let spId = Snowflake.nextId();
        //                 copySingleProject.sequenceNbr = spId;
        //                 ObjectUtils.updatePropertyValue(copySingleProject, 'spId', spId);
        //                 //替换导入文件的单项名称
        //                 copySingleProject.projectName = singleProjectChild.name;
        //                 if (!ObjectUtils.isEmpty(copySingleProject.unitProjects)) {
        //                     for (let j = 0; j < copySingleProject.unitProjects.length; j++) {
        //                         let copyUnit = ConvertUtil.deepCopy(copySingleProject.unitProjects[j]);
        //                         //重新刷新所有的单位ID
        //                         let unitId = Snowflake.nextId();
        //                         copyUnit.sequenceNbr = unitId;
        //                         ObjectUtils.updatePropertyValue(copyUnit, 'unitId', unitId);
        //                         //替换导入文件的单位名称
        //                         if (undefined == singleProjectChild.children.find(item => item.id === copySingleProject.unitProjects[j].sequenceNbr)) {
        //                             delete copySingleProject.unitProjects[j];
        //                             continue;
        //                         }
        //                         copyUnit.upName = singleProjectChild.children.find(item => item.id === copySingleProject.unitProjects[j].sequenceNbr).name
        //                         let itemBillProjects = copyUnit.itemBillProjects;
        //                         let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === "03");
        //                         if (!ObjectUtils.isEmpty(itemBillProjectsQd)) {
        //                             for (let k = 0; k < itemBillProjectsQd.length; k++) {
        //                                 itemBillProjectsQd[k].isLocked = 1;
        //                             }
        //                         }
        //                         let measureProjectTables = copyUnit.measureProjectTables;
        //                         let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
        //                         if (!ObjectUtils.isEmpty(measureProjectTablesQd)) {
        //                             for (let k = 0; k < measureProjectTablesQd.length; k++) {
        //                                 measureProjectTablesQd[k].isLocked = 1;
        //                             }
        //                         }
        //                         copySingleProject.unitProjects[j] = copyUnit;
        //                     }
        //                 }
        //                 copySingleProject.unitProjects = copySingleProject.unitProjects.filter(x => x !== undefined)
        //                 //  合同内外是否是 替换
        //                 if(singleProjectChild.whetherReplace===true){
        //                     //projectObjById.singleProjects[i]=copySingleProject
        //                 }else {
        //                     currentProjectObj.singleProjects.push(copySingleProject);
        //                 }
        //
        //             } else {
        //                 if (!ObjectUtils.isEmpty(singleProjectChild.children)) {
        //                     for (let j = 0; j < singleProjectChild.children.length; j++) {
        //                         let unitProjectChild = singleProjectChild.children[j];
        //                         if (unitProjectChild.whetherNew === true) {
        //                             //let newUnit = unitList.find(item => item.sequenceNbr === unitProjectChild.id);
        //                             let newUnit = importProjectObjMap.get(unitProjectChild.id)
        //                             newUnit.spId = singleProject.sequenceNbr;
        //                             newUnit.upName = unitProjectChild.name;
        //                             newUnit.constructId = currentProjectObj.sequenceNbr;
        //                             if (ObjectUtils.isEmpty(singleProject.unitProjects)) {
        //                                 singleProject.unitProjects = new Array();
        //                             }
        //                             let copyUnit = ConvertUtil.deepCopy(newUnit);
        //                             //重新刷新所有的单位ID
        //                             let unitId = Snowflake.nextId();
        //                             copyUnit.sequenceNbr = unitId;
        //                             ObjectUtils.updatePropertyValue(copyUnit, 'unitId', unitId);
        //
        //                             let itemBillProjects = copyUnit.itemBillProjects;
        //                             let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === "03");
        //                             if (!ObjectUtils.isEmpty(itemBillProjectsQd)) {
        //                                 for (let k = 0; k < itemBillProjectsQd.length; k++) {
        //                                     itemBillProjectsQd[k].isLocked = 1;
        //                                 }
        //                             }
        //                             let measureProjectTables = copyUnit.measureProjectTables;
        //                             let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
        //                             if (!ObjectUtils.isEmpty(measureProjectTablesQd)) {
        //                                 for (let k = 0; k < measureProjectTablesQd.length; k++) {
        //                                     measureProjectTablesQd[k].isLocked = 1;
        //                                 }
        //                             }
        //                             //使用當前工程费率
        //                             //导入费率后按当前项目
        //                             if (unitProjectChild.costType === "a") {
        //                                 //1.取当前项目费率
        //                                 let argsModel = {};
        //                                 argsModel.levelType = 1;
        //                                 argsModel.constructId = newUnit.constructId;
        //                                 argsModel.singleId = copyUnit.spId;
        //                                 argsModel.unitId = copyUnit.sequenceNbr;
        //                                 let feeCollectionList = this.service.baseFeeFileService.getFeeCollectionList(argsModel)
        //                                 //对于项目的取费文件进行去重
        //                                 let feeFiles = ArrayUtil.distinctList(feeCollectionList, "feeFileId");
        //                                 //2.替换取费费率（无费率，需查询默认库）,并替换单项单位的id
        //                                 for (let y = 0; y < copyUnit.feeFiles.length; y++) {
        //                                     let currentFeeFile = copyUnit.feeFiles[y]
        //                                     let feeFileId = currentFeeFile.feeFileId
        //                                     let feeFile = feeFiles.find((item) => item.feeFileId == feeFileId);
        //                                     currentFeeFile.constructId = copyUnit.constructId;
        //                                     currentFeeFile.singleId = copyUnit.spId;
        //                                     currentFeeFile.unitId = copyUnit.sequenceNbr;
        //                                     if (!ObjectUtils.isEmpty(feeFile)) {
        //                                         currentFeeFile.managementFee = feeFile.managementFee
        //                                         currentFeeFile.profit = feeFile.profit
        //                                         currentFeeFile.fees = feeFile.fees
        //                                         currentFeeFile.anwenRateBase = feeFile.anwenRateBase
        //                                         currentFeeFile.anwenRateAdd = feeFile.anwenRateAdd
        //                                     } else {
        //                                         let baseFeeFileDTO = await this.app.appDataSource.getRepository(BaseFeeFile).findOne({
        //                                             where:{
        //                                                 qfCode:currentFeeFile.feeFileCode
        //                                             }
        //                                         });
        //                                         let baseFeeFileRelation = await this.app.appDataSource.getRepository(BaseFeeFileRelation).findOne({
        //                                             where: {
        //                                                 qfCode: baseFeeFileDTO.qfCode
        //                                             }
        //                                         });
        //                                         //管理费、利润费率查询
        //                                         let baseManageRates = await this.app.appDataSource.getRepository(BaseManageRate).findOne({
        //                                             where: {
        //                                                 rateCode: baseFeeFileRelation.rateCode,
        //                                             },
        //                                         })
        //                                         //规费费率查询
        //                                         let baseGsjRate = await this.app.appDataSource.getRepository(BaseGsjRate).findOne({
        //                                             where: {
        //                                                 code: baseFeeFileRelation.projectType,
        //                                                 kind: "1"
        //                                             }
        //                                         });
        //                                         //安文费费率查询
        //                                         let baseAnwenRate = await this.app.appDataSource.getRepository(BaseAnwenRate).findOne({
        //                                             where:{
        //                                                 libraryCode:baseFeeFileRelation.libraryCode
        //                                             }
        //                                         });
        //                                         currentFeeFile.managementFee = baseManageRates.managementFee3
        //                                         currentFeeFile.profit = baseManageRates.profit3
        //                                         currentFeeFile.fees = baseGsjRate.rate
        //                                         currentFeeFile.anwenRateBase = baseAnwenRate.anwenRate
        //                                         //currentFeeFile.anwenRateAdd = feeFile.anwenRateAdd
        //                                     }
        //                                 }
        //                             }
        //                             //导入费率按导入项目
        //                             if (unitProjectChild.costType === "b") {
        //                                 //替换单项单位的id
        //                                 for (let y = 0; y < copyUnit.feeFiles.length; y++) {
        //                                     let currentFeeFile = copyUnit.feeFiles[y]
        //                                     currentFeeFile.constructId = copyUnit.constructId;
        //                                     currentFeeFile.singleId = copyUnit.spId;
        //                                     currentFeeFile.unitId = copyUnit.sequenceNbr;
        //
        //                                 }
        //
        //                             }
        //                             //  合同内外是否是 替换
        //                             if(unitProjectChild.whetherReplace===true){
        //                                 singleProject.unitProjects[j]=copyUnit
        //                             }else {
        //                                 singleProject.unitProjects.push(copyUnit);
        //                             }
        //
        //                             if (args.importType === 1) {
        //                                 // 初始化與更新
        //                                 // if (ObjectUtils.isEmpty(copyUnit.unitCostCodePrices)) {
        //                                 //初始化费用代码
        //                                 this.constructProjectService.service.initUnitCostCodePrice(copyUnit);
        //                                 // }
        //                                 // if (ObjectUtils.isEmpty(copyUnit.unitCostSummarys)) {
        //                                 //初始化费用汇总
        //                                 this.constructProjectService.service.initUnitCostSummary(copyUnit);
        //                                 // }
        //                                 if (ObjectUtils.isEmpty(copyUnit.unitInputTaxAmounts)) {
        //                                     //一般计税
        //                                     let projectTaxCalculation = copyUnit.projectTaxCalculation;
        //                                     if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {
        //                                         // 初始化进项税额明细
        //                                         this.constructProjectService.service.initUnitInputTaxAmount(copyUnit);
        //                                     }
        //                                 }
        //                             }
        //
        //
        //                         }
        //                     }
        //                 }
        //             }
        //         } else if (singleProjectChild.levelType === 3) {
        //             if (singleProjectChild.whetherNew === true) {
        //                 let newUnit = unitList.find(item => item.sequenceNbr === singleProjectChild.id);
        //                 newUnit.constructId = currentProjectObj.sequenceNbr;
        //
        //                 let copyUnit = ConvertUtil.deepCopy(newUnit);
        //                 //重新刷新所有的单位ID
        //                 let unitId = Snowflake.nextId();
        //                 copyUnit.sequenceNbr = unitId;
        //                 ObjectUtils.updatePropertyValue(copyUnit, 'unitId', unitId);
        //
        //                 let itemBillProjects = copyUnit.itemBillProjects;
        //                 let itemBillProjectsQd = itemBillProjects.filter(item => item.kind === "03");
        //                 if (!ObjectUtils.isEmpty(itemBillProjectsQd)) {
        //                     for (let k = 0; k < itemBillProjectsQd.length; k++) {
        //                         itemBillProjectsQd[k].isLocked = 1;
        //                     }
        //                 }
        //                 let measureProjectTables = copyUnit.measureProjectTables;
        //                 let measureProjectTablesQd = measureProjectTables.filter(item => item.kind === "03");
        //                 if (!ObjectUtils.isEmpty(measureProjectTablesQd)) {
        //                     for (let k = 0; k < measureProjectTablesQd.length; k++) {
        //                         measureProjectTablesQd[k].isLocked = 1;
        //                     }
        //                 }
        //
        //                 currentProjectObj.unitProjectArray.push(copyUnit);
        //             }
        //
        //         }
        //     }
        // }
        delete currentProjectObj.importProjectObj;

        //计算工作台底部价格汇总计算
        // PricingFileWriteUtils.countConstructProject(projectObjById.sequenceNbr)

        await this.service.projectOverviewService.updateYsfFile(currentProjectObj);
    }


    /**
     * 结算导出文件
     * @param args
     * @return {Promise<ResponseData>}
     */
    async exportYjsFile(args) {
        let {name, id} = args;
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(name);

        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.replace('.YSF','.YJS').toString(),
            filters: [{name: '云算房文件', extensions: ['YJS']}],
        };
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (ObjectUtils.isEmpty(filePath)) {
            console.log("未选中任何文件");
            return ResponseData.fail("未选中任何文件");
        }
        if (filePath && !filePath.canceled) {
            if (!filePath.endsWith(".YJS")) {
                filePath += ".YJS";
            }
            //查询选择的路径是否已经有被打开的文件
            let result = await this.service.constructProjectFileService.getOneProDataByPath(filePath);

            if (!ObjectUtils.isEmpty(result)) {
                let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
                if (!ObjectUtils.isEmpty(projectObj)) {
                    return ResponseData.success(2);
                }
            }

            //导出的ysf
            let copyObj
            try {
                copyObj = this.exportYsfHandler(args);
            } catch (error) {
                console.error(error);
                return ResponseData.fail('参数异常');
            }
            copyObj.path = filePath;
            //重新刷新所有的项目ID
            let constructId = Snowflake.nextId();
            copyObj.sequenceNbr = constructId;
            ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);
            //PricingFileWriteUtils.creatYsfFile(copyObj);
            PricingFileWriteUtils.writeToMemory(copyObj);
            await this.service.ysfHandlerService.creatYsfFile(copyObj);
            global.constructProject[copyObj.sequenceNbr] = null;
            return ResponseData.success(1);
        }
        return ResponseData.success(0);
    }


    //移除字段
    removeField(obj, fieldName) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }
        // 如果当前对象是数组，则遍历数组中的每个元素
        if (Array.isArray(obj)) {
            obj.forEach((item, index) => {
                obj[index] = this.removeField(item, fieldName);
            });
        } else {
            // 否则，遍历对象中的每个键
            Object.keys(obj).forEach(key => {
                // 如果键的值是对象，则递归调用 removeField 函数
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    obj[key] = this.removeField(obj[key], fieldName);
                }
                // 如果键与指定的字段名称相同，则删除该键值对
                if (key === fieldName) {
                    delete obj[key];
                }
                if (obj[key] === null) {
                    delete obj[key];
                }
                if (obj[key] === undefined) {
                    delete obj[key];
                }
                if (isNaN( NaN)) {
                    delete obj[key];
                }
            });
        }
        return obj;
    }


    async getImportProjectUnitList(proJectData) {
        let array = new Array();
        if (2 == proJectData.biddingType) {
            //单位工程
            let unitProject = proJectData.unitProject;
            array.push(unitProject);
            return array;
        } else {
            //单项
            let singleProjectList = proJectData.singleProjects;
            if (ObjectUtils.isEmpty(singleProjectList)) {
                if (!ObjectUtils.isEmpty(proJectData.unitProjectArray)) {
                    let unitProject = proJectData.unitProjectArray;
                    array = array.concat(unitProject);
                    return array;
                }
            } else {
                for (const single of singleProjectList) {
                    if (!ObjectUtils.isEmpty(single.unitProjects)) {
                        array = array.concat(single.unitProjects);
                    }
                }
                return array;
            }
        }

        return array;
    }


//======================================================================================================
    /**
     * 返回传入的数据
     * @param projectObj
     * @returns {*}
     */
    importYsfHandler(projectObj) {
        //复制一份原始数据
        let copyObj = ConvertUtil.deepCopy(projectObj);

        let res = {};
        res.sequenceNbr = projectObj.sequenceNbr;
        res.constructName = projectObj.constructName;
        //无单项，只有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.unitProjectArray)) {
            let unitProjects = new Array();
            let unitProjectArray = copyObj.unitProjectArray;
            for (let i = 0; i < unitProjectArray.length; i++) {
                let unitProject = {};
                let unitProjectArrayElement = unitProjectArray[i];
                unitProject.sequenceNbr = unitProjectArrayElement.sequenceNbr;
                unitProject.upName = unitProjectArrayElement.upName;
                unitProjects.push(unitProject);
            }
            res.unitProjects = unitProjects;
        }

        //既有单项又有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.singleProjects)) {


            let singleProjects = new Array();
            let singleProjectsArray = copyObj.singleProjects;
            for (let i = 0; i < singleProjectsArray.length; i++) {
                let singleProject = {};

                let singleProjectElement = singleProjectsArray[i];
                singleProject.sequenceNbr = singleProjectElement.sequenceNbr;
                singleProject.projectName = singleProjectElement.projectName;

                if (!ObjectUtils.isEmpty(singleProjectElement.unitProjects)) {
                    let unitProjectArray = singleProjectElement.unitProjects;
                    let unitProjects = new Array();
                    for (let j = 0; j < unitProjectArray.length; j++) {
                        let unitProjectElement = unitProjectArray[j];
                        let unitProject = {};
                        unitProject.sequenceNbr = unitProjectElement.sequenceNbr;
                        unitProject.upName = unitProjectElement.upName;
                        unitProjects.push(unitProject);
                    }
                    singleProject.unitProjects = unitProjects;
                }
                singleProjects.push(singleProject);

            }
            res.singleProjects = singleProjects
        }
        return res;
    }




    exportYsfHandler(args) {
        //获取到内存中的原始项目
        let projectObj = PricingFileFindUtils.getProjectObjById(args.id);
        //复制一份原始数据
        let copyObj = ConvertUtil.deepCopy(projectObj);
        let arr = [];
        let arr2 = [];
        this.getUnitList(arr, arr2, args);
        //无单项，只有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.unitProjectArray)) {
            let unitIdList = arr.map(k => k.id);
            let unitProjectArray = copyObj.unitProjectArray;
            for (let i = unitProjectArray.length - 1; i >= 0; i--) {
                let item = unitProjectArray[i];
                if (!unitIdList.includes(item.sequenceNbr)) {
                    unitProjectArray.splice(i, 1);
                }
            }
        }

        //既有单项又有单位的情况
        if (!ObjectUtils.isEmpty(copyObj.singleProjects)) {
            let group = ArrayUtil.group(arr, "parentId");
            let group2 = ArrayUtil.group(arr2, "parentId");
            let singleProjects = copyObj.singleProjects;
            for (let i = singleProjects.length - 1; i >= 0; i--) {
                let item = singleProjects[i];
                if (ObjectUtils.isEmpty(group.get(item.sequenceNbr)) && arr2.filter(x => x.id === item.sequenceNbr).length == 0) {
                    singleProjects.splice(i, 1);
                    continue;
                }
                //获取单项中的单位
                let unitProjects = item.unitProjects;
                if (ObjectUtils.isNotEmpty(unitProjects)) {
                    for (let i = unitProjects.length - 1; i >= 0; i--) {
                        let unit = unitProjects[i];
                        let unitIdList = group.get(item.sequenceNbr);
                        unitIdList = unitIdList.map(k => k.id);
                        if (!unitIdList.includes(unit.sequenceNbr)) {
                            unitProjects.splice(i, 1);
                        }
                    }
                }

            }
        }
        return copyObj;
    }


    getUnitList(arr, arr2, args) {
        let children = args.children;
        if (ObjectUtils.isEmpty(children)) {
            return;
        }
        for (const item of children) {
            if (item.levelType === 2 && item.selected) {
                arr2.push(item);
            }
            if (item.levelType === 3 && item.selected) {
                arr.push(item);
            } else {
                this.getUnitList(arr, arr2, item);
            }
        }
    }












}

JieSuanImportExportService.toString = () => '[class JieSuanImportExportService]';
module.exports = JieSuanImportExportService;
