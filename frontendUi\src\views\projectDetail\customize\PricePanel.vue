<!--
 * @@Descripttion:
 * @Author: wangru
 * @Date: 2023-05-18 17:56:43
 * @LastEditors: liuxia
 * @LastEditTime: 2024-11-12 10:58:43
-->
<!-- 底部价格面板 -->
<template>
  <div class="foot-content">
    <div class="title">
      河北13国标清单规范-河北{{ store.deStandardReleaseYear }}定额标准<span
        v-if="store.currentTreeInfo?.constructMajorType"
        >-</span
      >{{ store.currentTreeInfo?.constructMajorType }}
      <!-- <span>自动计算</span> -->
    </div>
    <span v-for="item in showList" :key="item.name"
      >{{ item.name }}={{ item.value }}</span
    >
  </div>
</template>

<script setup>
import { watch, ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const defaultList = [
  {
    name: '工程造价',
    value: 0,
  },
  {
    name: '人工费',
    value: 0,
  },
  {
    name: '机械费',
    value: 0,
  },
  {
    name: '主材费',
    value: 0,
  },
  {
    name: '材料费',
    value: 0,
  },
  {
    name: '管理费',
    value: 0,
  },
  {
    name: '利润',
    value: 0,
  },
  {
    name: '直接工程费',
    value: 0,
  },
];
const props = defineProps({
  priceList: {
    type: Array,
  },
});
let showList = ref([]);
watch(() => {
  if (props.priceList.length === 0) {
    showList.value = defaultList;
  } else {
    showList.value = props.priceList;
  }
});
</script>
<style lang="scss" scoped>
.foot-content {
  padding: 0 30px;
  display: flex;
  width: 100%;
  align-items: center;
  font-size: 12px;
  height: 33px;
  color: #214d8f;
  span {
    padding-left: 1.5%;
  }
}
.title {
  // padding-right: 20px;
  line-height: 33px;
  span {
    padding-left: 0px;
    margin: 0 10px 0 5px;
  }
}
</style>
