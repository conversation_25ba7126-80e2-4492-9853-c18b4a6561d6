<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-23 16:17:43
-->
<template>
  <div class="table-content">
    <child-page-table
      v-if="originalFlag"
      :pageType="'zcbfwf'"
      :columnList="columnListInner"
    ></child-page-table>
    <child-page-table
      v-else
      :pageType="'zcbfwf'"
      :columnList="columnListOut"
    ></child-page-table>
  </div>
</template>
<script setup>
import { ref, onActivated } from 'vue';
import ChildPageTable from './childPageTable.vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const originalFlag = ref(false);
const columnListInner = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'fxName',
    title: '项目名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'fxName_edit' },
  },
  // {
  //   field: 'amount',
  //   title: '数量',
  //   minWidth: 80,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'amount_edit' },
  // },
  {
    field: 'xmje',
    title: '项目价值',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'xmje_edit' },
  },
  {
    field: 'serviceContent',
    title: '服务内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'service_edit' },
  },

  {
    field: 'rate',
    title: '费率(%)',
    minWidth: 80,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'rate_edit' },
  },
  {
    field: 'fwje',
    minWidth: 100,
    title: '合同金额',
  },
  {
    field: 'jiesuanModeName',
    minWidth: 100,
    title: '结算方式',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { default: 'jiesuanMode_default', edit: 'jiesuanMode_edit' },
  },
  {
    field: 'jieSuanFwje',
    minWidth: 100,
    title: '结算金额',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jieSuanFwje_edit' },
  },
];
const columnListOut = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'fxName',
    title: '项目名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'fxName_edit' },
  },
  // {
  //   field: 'amount',
  //   title: '数量',
  //   minWidth: 80,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'amount_edit' },
  // },
  {
    field: 'xmje',
    title: '项目价值',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'xmje_edit' },
  },
  {
    field: 'serviceContent',
    title: '服务内容',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'service_edit' },
  },

  {
    field: 'rate',
    title: '费率(%)',
    minWidth: 80,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'rate_edit' },
  },
  {
    field: 'jiesuanModeName',
    minWidth: 100,
    title: '结算方式',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'jiesuanMode_edit' },
  },
  {
    field: 'jieSuanFwje',
    minWidth: 100,
    title: '结算金额',
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'jieSuanFwje_edit' },
  },
];
onActivated(() => {
  originalFlag.value = projectStore.currentTreeInfo.originalFlag;
});
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
