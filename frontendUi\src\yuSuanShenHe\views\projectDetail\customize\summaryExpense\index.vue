<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:53:42
 * @LastEditors: wangru
 * @LastEditTime: 2024-08-13 14:59:07
-->
<template>
  <div class="feeTotal">
    <summary-header
      class="head"
      @clickAdd="clickAdd"
      @getCommonModel="data => emit('getCommonModel', data)"
    ></summary-header>
    <content-up
      class="content"
      :isCharu="isCharu"
      @getMoveInfo="getMoveInfo"
    ></content-up>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import ContentUp from './ContentUp.vue';
import SummaryHeader from './SummaryHeader.vue';
let isCharu = ref(false);
const clickAdd = bol => {
  if (bol) {
    isCharu.value = !isCharu.value;
  }
};
const emit = defineEmits(['getCommonModel', 'getMoveInfo']);
const getMoveInfo = value => {
  emit('getMoveInfo', value);
};

// 费用汇总
</script>
<style lang="scss" scoped>
.feeTotal {
  height: 100%;
  .head {
    display: flex;
    align-items: center;
  }
  .content {
    height: calc(100%);
  }
}
</style>
