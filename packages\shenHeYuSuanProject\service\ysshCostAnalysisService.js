'use strict';

const { ResponseData } = require('../../../common/ResponseData');
const { Service } = require('../../../core');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const YsshssConstant = require('../enum/YsshssConstant');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const ConstructBiddingTypeConstant = require('../../../electron/enum/ConstructBiddingTypeConstant');
const ProjectLevelConstant = require('../../../electron/enum/ProjectLevelConstant');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const { ProcessFluctuateUtil } = require('../utils/ProcessFluctuateUtil');

/**
 * 造价分析Service
 */
class YsshCostAnalysisService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取造价分析的审核对比数据
   */
  async getCostAnalysisData(args) {
    const { ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, levelType } = args;
    let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
    // 审定的造价分析数据
    const sdCostAnalysisData = await this.service.unitProjectService.getCostAnalysisData(args);
    if (ConstructBiddingTypeConstant.unitProject === projectObj.biddingType) {
      //单位工程项目
      return ResponseData.success(sdCostAnalysisData);
    } else {
      if (ProjectLevelConstant.unit === args.levelType) {
        // 单位层级
        return ResponseData.success(sdCostAnalysisData);
      }
      // 工程项目级别的目录树
      // const constructTree = await this.service.shenHeYuSuanProject.shenHeProjectService.generateLevelTreeNodeStructure({ sequenceNbr: constructId });
      const ssCostAnalysisDataMap = new Map();
      //招标和投标项目
      if (ProjectLevelConstant.construct === args.levelType) {
        // 送审的造价分析数据
        let ssCostAnalysisData = await this.service.unitProjectService.getCostAnalysisData({
          constructId: ssConstructId,
          singleId: ssSingleId,
          unitId: ssUnitId,
          levelType: levelType
        });
        await this.treeToMap(ssCostAnalysisData.costAnalysisConstructVOList, ssCostAnalysisDataMap);
        // 工程项目层级
        await this.sdFillSs(sdCostAnalysisData.costAnalysisConstructVOList, ssCostAnalysisDataMap, constructId, null);
        // 到此处sdCostAnalysisData中就是完整的造价分析数据结构了，但是由于送审的单位可以跨单项匹配审定的单位，所以需要重新统计送审级别的父级数据   并且计算增减金额和比例
        await this.summarySsTotalAndSetChange(sdCostAnalysisData.costAnalysisConstructVOList);
      } else if (ProjectLevelConstant.single === args.levelType) {
        // 单项层级

        // 送审的造价分析数据
        let ssCostAnalysisData = null;
        if (ObjectUtil.isNotEmpty(ssSingleId)) {
          ssCostAnalysisData = await this.service.unitProjectService.getCostAnalysisData({
            constructId: ssConstructId,
            singleId: ssSingleId,
            unitId: ssUnitId,
            levelType: levelType
          });
          ssCostAnalysisData = ObjectUtil.cloneDeep(ssCostAnalysisData);
          await this.treeToMap(ssCostAnalysisData.costAnalysisSingleVOList, ssCostAnalysisDataMap);
        }
        await this.sdFillSs(sdCostAnalysisData.costAnalysisSingleVOList, ssCostAnalysisDataMap, constructId, null);
        // 到此处sdCostAnalysisData中就是完整的造价分析数据结构了，但是由于送审的单位可以跨单项匹配审定的单位，所以需要重新统计送审级别的父级数据   并且计算增减金额和比例
        await this.summarySsTotalAndSetChange(sdCostAnalysisData.costAnalysisSingleVOList);

      }
      return ResponseData.success(sdCostAnalysisData);
    }
  }

  async summarySsTotalAndSetChange(treeData) {
    if (ObjectUtil.isEmpty(treeData)) {
      return;
    }
    let dataArray = [];
    if (Array.isArray(treeData)) {
      dataArray = treeData;
    } else {
      dataArray.push(treeData);
    }
    for (const item of dataArray) {
      if (ObjectUtil.isEmpty(item.childrenList)) {
        continue;
      }
      let gczj = 0;
      let average = 0;
      let unitcost = 0;
      let sbfsj = 0;
      for (const child of item.childrenList) {
        await this.summarySsTotalAndSetChange(child.childrenList);
        if (ObjectUtil.isNotEmpty(child[YsshssConstant.ysshSysj])) {
          gczj = NumberUtil.add(gczj, child[YsshssConstant.ysshSysj].gczj);
          // average = NumberUtil.add(average, child[YsshssConstant.ysshSysj].average);
          // unitcost = NumberUtil.add(unitcost, child[YsshssConstant.ysshSysj].unitcost);
          sbfsj = NumberUtil.add(sbfsj, child[YsshssConstant.ysshSysj].sbfsj);
        }
      }
      if (ObjectUtil.isNotEmpty(item[YsshssConstant.ysshSysj])) {
        item[YsshssConstant.ysshSysj].gczj = gczj;
        // item[YsshssConstant.ysshSysj].average = average;
        // item[YsshssConstant.ysshSysj].unitcost = unitcost;
        item[YsshssConstant.ysshSysj].sbfsj = sbfsj;
      } else {
        item[YsshssConstant.ysshSysj] = {
          gczj: gczj,
          average: average,
          unitcost: unitcost,
          sbfsj: sbfsj
        };
      }
      item[YsshssConstant.ysshSysj] = { ...item[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(item[YsshssConstant.ysshSysj], item, 'gczj') };
    }
  }

  async sdFillSs(sdTree, ssMap, constructId, singleId) {
    if (ObjectUtil.isEmpty(sdTree)) {
      return;
    }
    let dataArray = [];
    if (Array.isArray(sdTree)) {
      dataArray = sdTree;
    } else {
      dataArray.push(sdTree);
    }
    for (const item of dataArray) {
      if (item.levelType == ProjectLevelConstant.single) {
        const singleProject = PricingFileFindUtils.getSingleProject(constructId, item.sequenceNbr);
        if (ObjectUtil.isNotEmpty(singleProject)) {
          if (ObjectUtil.isNotEmpty(singleProject.ysshSingleId)) {
            // 有关联的送审单项
            const ssSingle = ssMap.get(singleProject.ysshSingleId);
            if (ObjectUtil.isNotEmpty(ssSingle)) {
              ssSingle.childrenList = null;
              item[YsshssConstant.ysshSysj] = { ...ssSingle };
            }
          }
        }
        if (ObjectUtil.isNotEmpty(item.childrenList)) {
          await this.sdFillSs(item.childrenList, ssMap, constructId, item.sequenceNbr);
        }
      } else if (item.levelType == ProjectLevelConstant.unit) {
        const unitProject = PricingFileFindUtils.getUnit(constructId, singleId, item.sequenceNbr);
        if (ObjectUtil.isNotEmpty(unitProject)) {
          // 单位级别直接计算好增减比例和增减金额
          if (ObjectUtil.isNotEmpty(unitProject.ysshUnitId)) {
            // 有关联的送审单位
            const ssUnit = ssMap.get(unitProject.ysshUnitId);
            if (ObjectUtil.isNotEmpty(ssUnit)) {
              // 填充送审数据  并设置单位级别的增减金额和比例
              item[YsshssConstant.ysshSysj] = { ...ssUnit, ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssUnit, item, 'gczj') };
            } else {
              // 单位级别的增减金额和比例
              item[YsshssConstant.ysshSysj] = {
                [YsshssConstant.changeRatio]: item.gczj == 0 ? 0 : 100,
                [YsshssConstant.changeTotal]: item.gczj
              };
            }
          } else {
            // 单位级别的增减金额和比例
            item[YsshssConstant.ysshSysj] = {
              [YsshssConstant.changeRatio]: item.gczj == 0 ? 0 : 100,
              [YsshssConstant.changeTotal]: item.gczj
            };
          }
        }
      }
    }
  }

  async treeToMap(treeData, map) {
    if (ObjectUtil.isEmpty(treeData)) {
      return;
    }
    let dataArray = [];
    if (Array.isArray(treeData)) {
      dataArray = treeData;
    } else {
      dataArray.push(treeData);
    }
    for (const item of dataArray) {
      map.set(item.sequenceNbr, item);
      if (ObjectUtil.isNotEmpty(item.childrenList)) {
        await this.treeToMap(item.childrenList, map);
      }
    }

  }

}


YsshCostAnalysisService.toString = () => '[class YsshCostAnalysisService]';
module.exports = YsshCostAnalysisService;

