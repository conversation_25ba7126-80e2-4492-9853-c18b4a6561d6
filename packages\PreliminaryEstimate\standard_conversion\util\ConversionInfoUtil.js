class ConversionInfoUtil {

    constructor() {
        this.STARDARD_CONVERSION_SOURCE = "标准换算";
        this.UNITE_CONVERSION_SOURCE = "统一换算";
        this.RCJ_DETAIL_SOURCE = "人材机明细";
    }

    initConversionInfo(rule,source,conversionString,sortNo = null){
        return {
            ...rule,
            ruleId: rule.sequenceNbr,
            source,
            conversionExplain: null,
            conversionString,
            kind: rule.kind,
            type: rule.type,
            sortNo,
            children: []
        };
    }

}

module.exports = {
    ConversionInfoUtil: new ConversionInfoUtil()
}