<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2024-03-11 19:29:20
 * @LastEditors: liuxia
 * @LastEditTime: 2024-09-18 16:31:38
-->
<template>
  <common-modal
    v-model:modelValue="show"
    className="dialog-comm"
    :title="title"
    :width="1200"
    height="auto"
    @close="close"
  >
    <div class="head-action" v-if="decData">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <a-tab-pane key="1" tab="单价构成">
          <unitPrice
            ref="unitPriceRef"
            v-model:pushData="pushData"
            :decData="decData"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="费用汇总">
          <allPrice v-model:pushData="pushData" :decData="decData" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="footer">
      <a-button type="primary" :loading="btnLoading" @click="saveData"
        >确定</a-button
      >
      <a-button @click="close">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import unitPrice from './unitPrice.vue';
import allPrice from './allPrice.vue';
import { message } from 'ant-design-vue';
import shApi from '@/api/shApi';

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const activeKey = ref('1');
const upTableAll = ref();
const unitPriceRef = ref();
const decData = ref(null);
const btnLoading = ref(false);
const pushData = ref({
  djgc: [],
  summary: [],
});
const props = defineProps({
  visible: {
    type: Boolean,
  },
  title: {
    type: String,
  },
});
const emit = defineEmits(['update:visible', 'refresh']);
const show = computed({
  get: () => props.visible,
  set: val => {
    if (!val) {
      activeKey.value = '1';
    }
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      activeKey.value = '1';
      getTotalAndDesData();
    }
  }
);

const clear = (type, row) => {
  if (activeKey.value === '1') {
    // pushData.value.djgc.push()
    let rowData = decData.value.djgc[categoryIndex.value];
    console.log(store, 'store');
    let djgcData = {
      sequenceNbr: rowData.sequenceNbr,
      managementFee: Number(rowData.djgc.find(a => a.type === 7).rate),
      profit: Number(rowData.djgc.find(a => a.type === 8).rate),
      fees: Number(rowData.fees),
      anwenRateBase: Number(rowData.anwenRateBase),
      anwenRateAdd: Number(rowData.anwenRateAdd),
      levelType: store.currentTreeInfo.levelType,
      constructId: store.currentTreeGroupInfo?.constructId,
      feeFileId: rowData.feeFileId,
      singleId: store.currentTreeInfo?.parentId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
    };
    if (pushData.value.djgc.find(a => a.sequenceNbr === rowData.sequenceNbr)) {
      Object.keys(djgcData).forEach(key => {
        pushData.value.djgc.find(a => a.sequenceNbr === rowData.sequenceNbr)[
          key
        ] = djgcData[key];
      });
    } else {
      pushData.value.djgc.push(djgcData);
    }
    console.log(pushData.value, '提交数据');
  } else {
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      unitCostSummary: { ...row },
    };
    if (
      pushData.value.summary.find(
        a => a.unitCostSummary.sequenceNbr === row.sequenceNbr
      )
    ) {
      Object.keys(apiData).forEach(key => {
        pushData.value.summary.find(
          a => a.unitCostSummary.sequenceNbr === row.sequenceNbr
        )[key] = apiData[key];
      });
    } else {
      pushData.value.summary.push(apiData);
    }
  }

  //清除编辑状态
  const $table = type === 1 ? upTable.value : upTableAll.value;
  $table.clearEdit();
};
const close = () => {
  pushData.value = {
    djgc: [],
    summary: [],
  };
  console.log(unitPriceRef.value);
  if (unitPriceRef.value) {
    unitPriceRef.value.categoryIndex = 0;
  }
  show.value = false;
};
const saveData = () => {
  console.log('saveData', '保存审取费', pushData.value);
  btnLoading.value = true;
  shApi.updataYjsqf(JSON.parse(JSON.stringify(pushData.value))).then(res => {
    btnLoading.value = false;
    if (res.code === 200) {
      emit('successCallback');
      close();
      message.success('保存成功');
    }
  });
};
const getTotalAndDesData = () => {
  if (!store.currentTreeGroupInfo || !store.currentTreeInfo.levelType) {
    decData.value = [];
    return; //取费表没有费用文件
  }
  let apiData = {
    levelType: store.currentTreeInfo.levelType,
    constructId:
      store.currentTreeInfo.levelType === 1
        ? store.currentTreeInfo?.id
        : store.currentTreeGroupInfo?.constructId,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  console.log(apiData);
  shApi.getYjsqf(apiData).then(res => {
    console.log('getYjsqf 结果-----获取', 'apiData', apiData, res);

    if (res.status === 200 && res.result) {
      decData.value = res.result;
    } else {
      decData.value = [];
      message.error(response.message);
    }
  });
};
</script>
<style lang="scss" scoped>
.head-action {
  margin-bottom: 5px;
  // height: 35px;
  flex: 1;
  :deep(.ant-tabs-tab) {
    // height: 35px;
    background: transparent;
    border: none;
    color: #7c7c7c;
  }
  :deep(.ant-tabs-nav) {
    background: #e7e7e7;
  }
  :deep(.ant-tabs-tab-active) {
    background: #ffffff;
    border-top: 2px solid #4786ff;
    .ant-tabs-tab-btn {
      color: #000000;
    }
  }
  button {
    float: right;
    margin-right: 15px;
  }
  .singleCon {
    display: flex;

    .menu-list {
      width: 170px;
      list-style: none;
      padding: 1px 0px 9px;
      li {
        text-align: left;
        // margin: 0 0 6px 6px;
        margin: 0 0 1px 6px;

        cursor: pointer;
        .name-content {
          display: block;
          padding: 0 20px;

          line-height: 1.6;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
        }
        &:hover {
          background-color: #dae7f4;
        }
      }
      .on {
        background-color: #deeaff;
      }
    }
    .table {
      height: 60vh;
      margin-left: 10px;
    }
  }
}
.table {
  height: 60vh;
}
.footer {
  display: flex;
  justify-content: center;
  position: relative;
  top: 15px;
  :deep(.ant-btn) {
    margin-right: 15px;
  }
}
</style>
