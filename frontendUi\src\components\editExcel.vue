<!--
 * @selectValueripttion:
 * @Author: sunchen
 * @Date: 2024-06-20 19:27:58
 * @LastEditors: sunchen
 * @LastEditTime: 2025-03-10 15:53:45
-->
<template>
  <teleport :to="teleportTo">
    <div
      class="editExcel-wraps"
      :class="teleportTo == 'body' ? '' : 'dialog-comms'"
    >
      <div class="content-box" :class="isEditTable ? 'editTable-box' : ''">
        <div class="univer-container" v-show="previewStatus">
          <iframe
            v-if="fileUrl"
            id="myIframe"
            ref="iframeRef"
            :src="fileUrl"
            style="width: 100%; height: 100%; border: 2px solid #e8e8e7"
          />
        </div>
        <!-- 表格编辑 -->
        <div class="editTable-toolbar" v-if="isEditTable">
          <div class="custom-bar-nav">
            <div class="btn-list" style="margin: 6px 3px">
              <a-button
                size="small"
                type="primary"
                @click="exportData('excel', true)"
                style="margin-left: 0"
                >导出EXCEL</a-button
              >
              <a-button
                size="small"
                type="primary"
                @click="exportData('pdf')"
                style="margin-left: 20px"
                >导出PDF</a-button
              >
            </div>
          </div>
        </div>
        <div
          v-show="!previewStatus"
          ref="container"
          id="univer-container"
          class="univer-container"
        ></div>

        <div class="univer-custom-toolbar" v-if="!isEditTable">
          <div class="custom-bar-nav">
            <span class="cus-tip">报表配置项</span>
            <div class="btn-list">
              <!--              <a-button size="small"  @click="devSave">更新模板</a-button>-->
              <a-button
                size="small"
                type="primary"
                @click="getData()"
                :disabled="saveDisabled"
                >保存</a-button
              >
              <!-- <a-button  type="primary" @click="preview" style="margin-left: 20px;" >预览</a-button> -->
              <a-button size="small" @click="close" style="margin-left: 20px"
                >取消</a-button
              >
            </div>
          </div>
          <span class="fn-tip">暂不支持函数设计，如需修改请联系业务人员</span>

          <div class="custom-content-nav">
            <a-button v-for="i of tableNav" @click="activeTableNav = i">{{
              i
            }}</a-button>
          </div>

          <div
            class="custom-content-list"
            v-show="['表格属性'].includes(activeTableNav)"
          >
            <div class="list-wrap">
              <div
                class="items select-title"
                @click="showList.showChilde0 = !showList.showChilde0"
              >
                <icon-font
                  type="icon-zhankai"
                  class="icon"
                  v-if="showList.showChilde0"
                />
                <icon-font type="icon-shouqi" class="icon" v-else />
                行类型
              </div>
              <div class="select-list" v-if="showList.showChilde0">
                <span class="items">{{ rowType }}</span>
              </div>
            </div>

            <div class="list-wrap">
              <div
                class="items select-title"
                @click="showList.showChilde1 = !showList.showChilde1"
              >
                <icon-font
                  type="icon-zhankai"
                  class="icon"
                  v-if="showList.showChilde1"
                />
                <icon-font type="icon-shouqi" class="icon" v-else />
                数据源类型
              </div>
              <div class="select-list" v-if="showList.showChilde1">
                <span class="items">{{ dataSourceType }}</span>
              </div>
            </div>

            <div class="list-wrap">
              <div
                class="items select-title"
                @click="showList.showChilde2 = !showList.showChilde2"
              >
                <icon-font
                  type="icon-zhankai"
                  class="icon"
                  v-if="showList.showChilde2"
                />
                <icon-font type="icon-shouqi" class="icon" v-else />
                精度设置
              </div>
              <div class="select-list" v-if="showList.showChilde2">
                <div class="items precision-wraps">
                  <span class="tips">小数位数</span>
                  <a-input
                    type="number"
                    size="small"
                    v-model:value="precision"
                    @change="changePrecision"
                  ></a-input>
                  <!-- <span class="desc">示例：12，12.00</span> -->
                </div>
              </div>
            </div>
          </div>

          <div
            class="custom-content-list"
            v-show="['业务变量'].includes(activeTableNav)"
          >
            <div class="list-wrap" v-if="options?.length">
              <div
                class="items select-title"
                @click="showList.showChilde3 = !showList.showChilde3"
              >
                <icon-font
                  type="icon-zhankai"
                  class="icon"
                  v-if="showList.showChilde3"
                />
                <icon-font type="icon-shouqi" class="icon" v-else />
                业务变量
              </div>
              <div class="select-list" v-if="showList.showChilde3">
                <span
                  class="items"
                  v-for="(i, k) of options"
                  @dblclick="onChange(i.name)"
                  >{{ `${i.explain}${i.name}` }}</span
                >
              </div>
            </div>

            <div class="list-wrap">
              <div
                class="items select-title"
                @click="showList.showChilde4 = !showList.showChilde4"
              >
                <icon-font
                  type="icon-zhankai"
                  class="icon"
                  v-if="showList.showChilde4"
                />
                <icon-font type="icon-shouqi" class="icon" v-else />
                宏变量
              </div>
              <div class="select-list" v-if="showList.showChilde4">
                <span
                  class="items"
                  v-for="(i, k) of macroOptions"
                  @dblclick="onChangeMacro(i.showValue)"
                  >{{ i.label }}</span
                >
              </div>
            </div>

            <div class="list-wrap funList-wrap">
              <div class="sub-nav-list">
                <!-- <div class="title" :class="{'active':useindex == 0 }" @click="useindex = 0" >编辑表达式</div> -->
                <div
                  class="title"
                  :class="{ active: useindex == 1 }"
                  @click="useindex = 1"
                >
                  函数
                </div>
              </div>
              <div class="nav-content-wrap">
                <div
                  contenteditable="true"
                  @input="changeExpression"
                  ref="expressionRef"
                  v-if="useindex == 0"
                  class="edit-box nav-content"
                >
                  {{ optionsName }}
                </div>
                <div class="fn-list nav-content" v-if="useindex == 1">
                  <span
                    v-for="i of fnOptions"
                    @dblclick="application(i.value)"
                    >{{ i.label }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 插入细节 -->
    <addChilde ref="addChildeRef" @handleOk="handleOkRowZone"></addChilde>
    <!-- 插入宏变量 -->
    <addMacro ref="addMacroRef" @handleOk="handleOkMacro"></addMacro>
  </teleport>

  <common-modal
    v-model:modelValue="SettingStatus"
    className="dialog-comm"
    title="导出Excel"
    width="auto"
    @close="CancelDialog()"
  >
    <div class="radio-list">
      <div class="type-box">
        <div class="radio-title">
          <icon-font type="icon-daochushujumoshi" class="icon" />
          <span>批量导出Excel选项</span>
        </div>
        <a-checkbox-group v-model:value="editOut" style="width: 100%">
          <a-row>
            <a-col :span="24">
              <a-checkbox value="A">导出页眉页脚</a-checkbox>
            </a-col>
            <a-col :span="24" style="margin-top: 10px">
              <a-checkbox value="B">导出包含计税方式</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>
    </div>
    <div class="footer-btn-list">
      <a-button @click="CancelDialog()">取消</a-button>
      <a-button type="primary" @click="saveSetting()">确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import {
  ref,
  toRaw,
  nextTick,
  markRaw,
  watchEffect,
  getCurrentInstance,
  reactive,
  computed,
} from 'vue';

import { FUniver } from '@univerjs/facade';

import '@univerjs/design/lib/index.css';
import '@univerjs/ui/lib/index.css';
import '@univerjs/docs-ui/lib/index.css';
import '@univerjs/sheets-ui/lib/index.css';
import '@univerjs/sheets-formula-ui/lib/index.css';

import {
  LocaleType,
  Tools,
  Univer,
  UniverInstanceType,
  Disposable,
  ICommandService,
  IUniverInstanceService,
  IPermissionService,
} from '@univerjs/core';
import { defaultTheme } from '@univerjs/design';

import { UniverFormulaEnginePlugin } from '@univerjs/engine-formula';
import { UniverRenderEnginePlugin } from '@univerjs/engine-render';

import { UniverUIPlugin } from '@univerjs/ui';

import { UniverDocsPlugin } from '@univerjs/docs';
import { UniverDocsUIPlugin } from '@univerjs/docs-ui';

import {
  UniverSheetsPlugin,
  RangeProtectionPermissionEditPoint,
  WorkbookEditablePermission,
  SheetsSelectionsService,
  getSheetCommandTarget,
  AddRangeProtectionMutation,
} from '@univerjs/sheets';
import { UniverSheetsFormulaPlugin } from '@univerjs/sheets-formula';
import { UniverSheetsFormulaUIPlugin } from '@univerjs/sheets-formula-ui';
import {
  UniverSheetsUIPlugin,
  SetRangeBoldCommand,
  AddRangeProtectionFromToolbarCommand,
  AddRangeProtectionFromContextMenuCommand,
} from '@univerjs/sheets-ui';

import DesignZhCN from '@univerjs/design/locale/zh-CN';
import UIZhCN from '@univerjs/ui/locale/zh-CN';
import DocsUIZhCN from '@univerjs/docs-ui/locale/zh-CN';
import SheetsZhCN from '@univerjs/sheets/locale/zh-CN';
import SheetsUIZhCN from '@univerjs/sheets-ui/locale/zh-CN';
import SheetsFormulaUIZhCN from '@univerjs/sheets-formula-ui/locale/zh-CN';
// import { UniverSheetsCustomMenuPlugin } from './univer/plugin/plugin.js';

import { globalData } from '@/views/projectDetail/reportForm/reportFrom.js';
import csYsProject from '@/api/csProject';
import csGljProject from '@gongLiaoJi/api/csProject';
import xeUtils from 'xe-utils';
import { projectDetailStore } from '@/store/projectDetail';
import { useRoute } from 'vue-router';
import { workBookData } from '@/components/reportTemplate/index.js';
import {
  setLocalStorage,
  getLocalStorage,
} from '@/views/projectDetail/reportForm/reportFrom';
import infoMode from '@/plugins/infoMode.js';
import addChilde from '@/components/reportFormModal/addChilde.vue';
import addMacro from '@/components/reportFormModal/addMacro.vue';
import { message } from 'ant-design-vue';
const projectStore = projectDetailStore();

let csProject = csYsProject;
if (projectStore.type == 'glj') {
  csProject = csGljProject;
  console.log('🚀 ~ csProject.value:', csProject.value);
}

let data = getLocalStorage();
const { ipcRenderer, webFrame } = require('electron');

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
bus.on('reportFormEdit', ({ data }) => {
  closeConfirm();
});

let univerRef = ref();
const emits = defineEmits(['getData', 'onClose']);
let fileUrl = ref(`/pdf/index.html`);

let previewStatus = ref(false);
const preview = () => {
  previewStatus.value = true;
  // setLocalStorage('')
};

const props = defineProps({
  teleportTo: {
    type: String,
    default: 'body',
  },
  lanMuName: {
    type: String,
    default: '',
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

const closeConfirm = () => {
  infoMode.show({
    iconType: 'icon-qiangtixing',
    infoText: '当前设计未保存，请确认保存？',
    confirm: () => {
      getData(true);
    },
    close: () => {
      infoMode.hide();
      ipcRenderer.send('window-close-child', {
        id: route.query.constructSequenceNbr,
      });
    },
  });
};

const univer = new Univer({
  theme: defaultTheme,
  locale: LocaleType.ZH_CN,
  locales: {
    [LocaleType.ZH_CN]: Tools.deepMerge(
      SheetsZhCN,
      DocsUIZhCN,
      SheetsUIZhCN,
      SheetsFormulaUIZhCN,
      UIZhCN,
      DesignZhCN
    ),
  },
});
univerRef.value = univer;

const isEditTable = computed(() => {
  return props.data?.openType == 'editTable';
});

// 初始化，处理数据，每个单元格添加行属性
const init = () => {
  univer.registerPlugin(UniverRenderEnginePlugin);
  univer.registerPlugin(UniverFormulaEnginePlugin);

  univer.registerPlugin(UniverUIPlugin, {
    container: 'univer-container',
    footer: false,
  });

  univer.registerPlugin(UniverDocsPlugin, {
    hasScroll: false,
  });
  univer.registerPlugin(UniverDocsUIPlugin);

  univer.registerPlugin(UniverSheetsPlugin);
  univer.registerPlugin(UniverSheetsUIPlugin, {
    menu: {
      // ['sheet.command.remove-row-confirm']: {
      //   hidden: true,
      // },
      // ['sheet.command.insert-row-before']:{
      //   hidden: true,
      // },
      // ['sheet.menu.col-insert']:{
      //   hidden: true,
      // },
      ['sheet.menu.cell-insert']: {
        hidden: true, // 右键的插入
      },
      ['sheet.menu.col-insert']: {
        hidden: true, //列上的插入
      },
      ['sheet.menu.row-insert']: {
        hidden: true, //行上的插入
      },
      [AddRangeProtectionFromToolbarCommand.id]: {
        // 设置保护权限
        hidden: true,
      },
      [AddRangeProtectionFromContextMenuCommand.id]: {
        hidden: true,
      },
      ['sheet.contextMenu.permission']: {
        hidden: true,
      },
    },
  });
  univer.registerPlugin(UniverSheetsFormulaPlugin);

  //univer.registerPlugin(UniverSheetsCustomMenuPlugin);

  const createdData = props.data;
  const { cellData, rowData, styles } = createdData.sheets.sheet1;
  // console.log("🚀 ~ init ~ styles:", styles)

  // Object.entries(styles).forEach(item => {
  //   console.log("🚀 ~ Object.entries ~ item:", item)
  //   // const [key,value] = item
  //   // if(key.indexOf('border') > -1){
  //   //   value.border?.forEach(item => {
  //   //     item.style = 'solid'
  //   //   })
  //   // }
  // })

  // 处理行custom属性
  for (let i = 0; i < Object.entries(rowData).length; i++) {
    let {
      parentZone,
      Zone,
      field,
      rowType,
      dataSourceType,
      parentName,
      ...data
    } = { ...Object.entries(rowData)[i][1] };
    Object.entries(rowData)[i][1].custom = {
      parentZone,
      Zone,
      field,
      rowType,
      dataSourceType,
      parentName,
    };
  }
  univer.createUnit(UniverInstanceType.UNIVER_SHEET, createdData);

  // handlePermission()
};

nextTick(() => {
  init();
});

const univerAPI = FUniver.newAPI(univer);

let rangArea = ref([]);
let sheetName = ref('');
let selectValue = ref([]);
let optionsName = ref('');
let prevValue = ref('');
let rowType = ref(''); // 行字段
let dataSourceType = ref('');
// 是否可以保存
let saveDisabled = ref(false);

let precision = ref(0); // 精度
let tableNav = ref(['表格属性', '业务变量']);
let activeTableNav = ref('业务变量');
let showList = reactive({});

for (let i = 0; i < 10; i++) {
  showList[`showChilde${i}`] = ref(true);
}

let history = reactive({
  rang: null,
  v: null,
  rowType: null,
});
// 交互操作
univerAPI.onCommandExecuted(command => {
  const { id, type, params } = command;
  // 如果是编辑表格，直接返回
  if (isEditTable.value) return;

  const cellData = univerAPI.getActiveWorkbook().save()?.sheets
    ?.sheet1.cellData;
  const rowData = univerAPI.getActiveWorkbook().save()?.sheets?.sheet1.rowData;
  if (id === 'doc.command.insert-text' || id === 'doc.command.delete-text') {
    const doc = univerAPI.getActiveDocument();
    if (doc) {
      const snapshot = doc.getSnapshot();
      console.log(snapshot.body?.dataStream);
    }
  }

  // 单元格进入编辑
  if (
    command.id === 'sheet.operation.set-cell-edit-visible' &&
    command.params.visible
  ) {
    saveDisabled.value = true;
  }

  //单元格退出编辑
  if (
    command.id === 'sheet.operation.set-cell-edit-visible' &&
    !command.params.visible
  ) {
    saveDisabled.value = false;
  }

  if (id == 'sheet.operation.set-selections' && params.type == 2) {
    selectValue.value = [];
    optionsName.value = '';
    prevValue.value = '';
    let { startRow, endRow, startColumn, endColumn } =
      params.selections[0].range;
    rangArea.value = [startRow, startColumn, 1, 1];
    let rang = activeSheet.value.getRange(
      rangArea.value[0],
      rangArea.value[1],
      1,
      1
    );
    let cellValue = rang.getCellData();
    if (cellValue?.v) {
      optionsName.value = cellValue.v;
    }
    console.log('🚀 ~ univerAPI.onCommandExecuted ~ cellValue:', cellValue);

    /*** start 因为现在组件官方暂未支持行列自定义属性，所以拿单元格的custom****/
    const rowConfig = rowData[startRow].custom;
    rowType.value = rowConfig?.rowType;
    dataSourceType.value = rowConfig?.dataSourceType;
    precision.value = cellValue?.custom?.precision;
    setVariable(rowConfig?.dataSourceType);
    /*** end 因为现在组件官方暂未支持行列自定义属性，所以拿单元格的custom****/
    // rowType.value = cellValue?.custom?.rowType
    // dataSourceType.value = cellValue?.custom?.dataSourceType
    // precision.value = cellValue?.custom?.precision
    // setVariable(dataSourceType.value)
  }

  if (id == 'sheet.command.insert-col') {
    // console.log("🚀 ~ univerAPI.onCommandExecuted ~ params:", params)
    // let rowData =  univerAPI.getActiveWorkbook().save()?.sheets?.sheet1.rowData
    // let result = Object.values(rowData).map(row =>
    //   row.hasOwnProperty('filed') ? row.filed : ''
    // );
    // const col =Object.keys( Object.values(params.cellValue)[0])[0]
    // console.log("🚀 ~ univerAPI.onCommandExecuted ~ col:", col)
    // Object.values(cellData).forEach((item, index) => {
    //     if(result[index]){
    //       console.log("🚀 ~ Object.values ~ index:", index)
    //       Object.values(item).forEach((j,k) => {
    //         setCustom(index,j,result[index])
    //       })
    //       // setValue
    //     }
    // })
  }
});

const activeSheet = ref(null);
nextTick(() => {
  activeSheet.value = univerAPI.getActiveWorkbook().getActiveSheet();
});

let container = ref(null);

const itemLevelList = markRaw(['project', 'single', 'unit']);
const store = projectDetailStore();
const route = useRoute();

/**
 * 处理报表数据，
 */
const getUniverForm = (type = '') => {
  const { resources, ...savedData } = univerAPI.getActiveWorkbook().save();
  const initData = xeUtils.clone(savedData, true);
  const formData = initData?.sheets?.sheet1;
  const cellData = formData.cellData;
  const rowData = formData.rowData;
  let newRowData = handleRows(rowData);
  // 处理数据表格数据
  Object.values(cellData).forEach((item, index) => {
    if (Object.values(item).length > formData.columnCount - 1) {
      // 如果列数大于表格列数，则删除后面所有的对象
      Object.values(item).forEach((j, k) => {
        if (k > formData.columnCount - 1) {
          delete item[k];
        }
      });
    }

    // 处理字符串值，将富文本转换成v，以及添加``
    Object.values(item).forEach((j, k) => {
      handleSaveData(j);

      // 过滤数据中的 ``字符串为空
      // j.v = j.v?.replace(/`/g, '')
    });

    // if(rowData[index]?.field == 'eyeBrow'){
    //   // 判断eyeBrow.页眉，最大不能超过3列
    //   let s = Object.values(item).filter(i=>{
    //     return Reflect.has(i, 'v') || (!Reflect.has(i, 't') && Reflect.has(i, 's'))
    //   })
    //   if(s.length > 3){
    //     alert('最大不能超过3列')
    //   }
    // }
  });

  /*** 处理合并单元格，univer 不支持，需要自己处理  ，合并的所有的右边线框样式默认取合并单元格的最后一个  */
  formData.mergeData.forEach(i => {
    let { startRow, endRow, startColumn, endColumn } = i;
    const cellDataList = Object.values(cellData);
    let endColumnBorder = -1;
    if (cellDataList[startRow] && cellDataList[startRow][endColumn]) {
      endColumnBorder = cellDataList[startRow][endColumn]?.s;
    }

    let styles = savedData?.styles;
    let endBorderRight = styles[endColumnBorder]?.bd?.r; // 最后一个右边线的样式

    for (let i = startColumn; i < endColumn; i++) {
      for (let j = startRow; j <= endRow; j++) {
        if (cellDataList[j] && cellDataList[j][i]) {
          let style = cellDataList[j][i]?.s;
          if (style && styles[style] && styles[style]['bd']) {
            styles[style]['bd']['r'] = endBorderRight;
          }
        }
      }
    }
  });

  // 处理带区
  let zoneData = [];
  if (['save'].includes(type)) {
    Object.values(newRowData).forEach((e, k) => {
      if (e.field == 'pageFoot') {
        // 页脚行，自动创建带区，并par
        let pervZone = zoneData.at(-1);
        zoneData.push({
          Zone: generateRandomString(),
          dataSourceType: e.dataSourceType,
          field: e.field,
          parentZone: pervZone?.Zone,
          rows: [{ ...e }],
        });
      } else {
        if (!e?.Zone) {
          // 没有带区的，新建一个带区
          zoneData.push({
            Zone: generateRandomString(),
            dataSourceType: e.dataSourceType,
            field: e.field,
            rows: [{ ...e }],
          });
        } else if (e?.Zone) {
          let hasZone = zoneData.find(i => i.Zone == e.Zone);
          if (hasZone) {
            hasZone.rows.push({ ...e });
          } else {
            if (e.parentZone) {
              // 如果有父级的带区
              zoneData.push({
                Zone: e.Zone,
                dataSourceType: e.dataSourceType,
                field: e.field,
                parentZone: e.parentZone,
                rows: [{ ...e }],
              });
            } else {
              // 判断带区是否已经存在,存在的话，就是同一个带区里面多个行
              // 新建带区
              zoneData.push({
                Zone: e.Zone,
                dataSourceType: e.dataSourceType,
                field: e.field,
                rows: [{ ...e }],
              });
            }
          }
        }
      }
    });
  }
  savedData.sheets.sheet1.rowData = newRowData;

  let params = {
    constructId: route.query.constructSequenceNbr,
    singleId: null,
    unitId: null,
    // jsonData: JSON.parse(JSON.stringify(savedData)),
    jsonData: xeUtils.clone(savedData, true),
    itemLevel: '',
    lanMuName: props.lanMuName,
    headLine: ['save'].includes(type)
      ? props.data?.headLine
      : props.data?.updateName,
  };

  // 新增处理带区
  if (['save'].includes(type)) {
    params.belt = zoneData;
  }

  if (store.currentTreeInfo) {
    const { levelType } = store.currentTreeInfo;
    params.itemLevel = itemLevelList[levelType - 1];

    switch (levelType) {
      case 1:
        params.constructId = route.query?.constructSequenceNbr;
        params.singleId = null;
        break;
      case 2:
        params.singleId = store.currentTreeInfo.id;
        params.unitId = null;
        break;
      case 3:
        params.singleId = store.currentTreeInfo?.parentId;
        params.unitId = store.currentTreeInfo.id;
        break;
    }
  }
  return params;
};

const handleRows = originalObject => {
  const transformedObject = {};

  for (const key in originalObject) {
    if (originalObject.hasOwnProperty(key)) {
      const innerObject = originalObject[key];
      const { custom, ...rest } = innerObject; // 使用解构分离 custom 和其他属性
      const newEntry = { ...rest, ...custom }; // 合并 rest 和 custom
      transformedObject[key] = newEntry;
    }
  }
  return transformedObject;
};

/**
 *
 * @param {*} isAuto 是否自动保存
 */
const getData = (isAuto = false) => {
  const params = getUniverForm('save');

  console.log('🚀发送的数据:', params);
  csProject.saveTemplate(params).then(res => {
    emits('getData', params);
    if (isAuto) {
      saveYsfFile();
    }
  });
};

// 开发环境保存模板
const devSave = () => {
  const params = getUniverForm();

  console.log('🚀发送的数据:', params);
  let postData = {
    ...params,
    sheetName: globalData.headLine,
  };

  csProject.updateTemplateFile(postData).then(res => {
    emits('getData', params.savedData);
  });
};

/**
 * 处理字符串
 * @param {*} cellValue
 * @param {*} rang
 * @param {*} rowType
 */
const handleCellValue = (cellValue, rang, rowType) => {
  if (saveDisabled.value) return;
  const newStr = filterCell(cellValue);
  rang.setValue({
    v: newStr,
    p: '',
    custom: {
      ...toRaw(cellValue.custom),
    },
  });
};

/**
 * 处理字符串
 * @param {*} cellValue
 * @param {*} rang
 * @param {*} rowType
 */
const handleSaveData = value => {
  const newStr = filterCell(value);
  if (value) {
    value.v = newStr;
    value.p = '';
  }
};

//过滤字符串,制作模板时过滤字符串
const filterCellOld = cellValue => {
  let strV = cellValue?.v;
  let newStr = [];
  if (cellValue?.p?.body?.dataStream) {
    strV = cellValue.p?.body.dataStream;
  }

  let isFunction = false; //是否是函数表达式
  let isNumber = false; // 是不是数字
  if (strV) {
    if (typeof strV === 'number') {
      isNumber = true;
      strV = strV;
    } else {
      // 判断"IF([MC] = `招标人另行发包专业工程`,`/`,``)"长字符串是否包含不区分大小写的if, else or字符串，包含这个则认为是函数

      isFunction = containsKeywords(strV);
      if (!isFunction) {
        strV = strV.replaceAll('`', '');
      }
    }

    if (isNumber || isFunction) {
      // 数字类型,和函数，直接添加
      newStr.push(strV);
    } else {
      const regex = /^[\[\{].*[\]\}]$/;

      let strList = strV.split('+');

      strList.forEach(str => {
        if (!regex.test(str.trim())) {
          str = '`' + str + '`';
        }
        newStr.push(str);
      });
    }
  }
  return newStr;
};

const filterCell = cellValue => {
  let strV = cellValue?.v;
  if (cellValue?.p?.body?.dataStream) {
    strV = cellValue.p?.body.dataStream;
  }

  if (strV) {
    // 检查strV字符串里面包含的的if,else，or，不区分大小写，if,else，or全部转成大写
    const regex = /\b(if|else|or|sum|count)\b/gi;
    strV = (strV + '').replace(regex, match => match.toUpperCase());
  }
  return strV;
};

const containsKeywords = str => {
  const keywords = ['if', 'else', 'or', 'sum', 'count', 'average'];
  return keywords.some(keyword =>
    new RegExp(`\\b${keyword}\\b`, 'i').test(str)
  );
};

const saveYsfFile = () => {
  csProject.saveYsfFile(route.query.constructSequenceNbr).then(res => {
    ipcRenderer.send('window-close-child', {
      id: route.query.constructSequenceNbr,
    });
  });
};

let options = ref([]);
let initOp = ref();

let macroValue = ref([]);
const macroOptions = ref([
  { value: 'gcname', label: '项目名称', showValue: '{项目名称}' },
  { value: 'dxname', label: '单项名称', showValue: '{单项名称}' },
  { value: 'dwname', label: '单位名称', showValue: '{单位名称}' },
  { value: 'page', label: '页码', showValue: '`第`+{页码}+`页`' },
  { value: 'count', label: '总页数', showValue: '`总共`+{总页数}+`页`' },
  { value: 'type', label: '计税方式', showValue: '{计税方式}' },
  {
    value: 'bzsm',
    label: '编制说明\\编制说明',
    showValue: '{编制说明\\编制说明}',
  },
  {
    value: 'zbZbr',
    label: '招标信息\\招标人',
    showValue: '{招标信息\\招标人}',
  },
  {
    value: 'zbZbr1',
    label: '招标信息\\编制人',
    showValue: '{招标信息\\编制人}',
  },
  {
    value: 'zbZJZXR',
    label: '招标信息\\造价咨询人',
    showValue: '{招标信息\\造价咨询人}',
  },
  {
    value: 'zbBZSJ',
    label: '招标信息\\编制时间',
    showValue: '{招标信息\\编制时间}',
  },
  {
    value: 'zbZBFR',
    label: '招标信息\\招标人法人',
    showValue: '{招标信息\\招标人法人}',
  },
  {
    value: 'zbZJZXFR',
    label: '招标信息\\造价咨询人法人',
    showValue: '{招标信息\\造价咨询人法人}',
  },
  {
    value: 'zbFHR',
    label: '招标信息\\复核人',
    showValue: '{招标信息\\复核人}',
  },
  {
    value: 'zjGCZJ',
    label: '造价分析\\工程总造价',
    showValue: '{造价分析\\工程总造价}',
  },
  {
    value: 'zjGCZJdX',
    label: '造价分析\\工程总造价 大写',
    showValue: '{造价分析\\工程总造价 大写}',
  },
  {
    value: 'zjGCZJdX2',
    label: '造价分析\\工程总造价(不含设备费及其税金)',
    showValue: '{造价分析\\工程总造价(不含设备费及其税金)}',
  },
  {
    value: 'zjGCZJdX3',
    label: '造价分析\\工程总造价(不含设备费及其税金) 大写',
    showValue: '{造价分析\\工程总造价(不含设备费及其税金) 大写}',
  },
  {
    value: 'TBTBR',
    label: '投标信息\\投标人',
    showValue: '{投标信息\\投标人)}',
  },
  {
    value: 'TBBZ',
    label: '投标信息\\编制时间',
    showValue: '{投标信息\\编制时间)}',
  },
  {
    value: 'TBFD',
    label: '投标信息\\法定代表人',
    showValue: '{投标信息\\法定代表人)}',
  },
  {
    value: 'TBZJ',
    label: '投标信息\\造价工程师',
    showValue: '{投标信息\\造价工程师)}',
  },
  {
    value: 'TBJS',
    label: '投标信息\\计税方式',
    showValue: '{投标信息\\计税方式)}',
  },
]);

// 退出编辑
const outEdit = () => {
  univerAPI.executeCommand('sheet.operation.set-cell-edit-visible', {
    visible: false,
  });
  univerAPI.getActiveWorkbook().save();
};

/**
 * 业务变量修改
 * @param {*} v
 */
const onChange = v => {
  outEdit();
  nextTick(() => {
    const activeSelection = univerAPI
      .getActiveWorkbook()
      .getActiveSheet()
      .getSelection()
      .getActiveRange();
    const cellData = activeSelection.getCellData();
    optionsName.value = cellData.v;

    sheetName.value = v;
    let rang = activeSheet.value.getRange(
      rangArea.value[0],
      rangArea.value[1],
      1,
      1
    );
    let str = optionsName.value;
    if (str) {
      str += `+${sheetName.value}`;
    } else {
      str = `${sheetName.value}`;
    }
    optionsName.value = str;
    rang.setValue({
      v: str,
    });
  });
};

/**
 * 宏变量修改
 * @param {*} value
 */
const onChangeMacro = value => {
  outEdit();
  nextTick(() => {
    let rang = activeSheet.value.getRange(
      rangArea.value[0],
      rangArea.value[1],
      1,
      1
    );
    let data = getCustom(rangArea.value[0], rangArea.value[1]);
    let newStr = data?.v ? `${data?.v} + ${value}` : `${value}`;

    optionsName.value = newStr;
    rang.setValue({
      v: newStr,
      p: '',
    });
  });

  // let macroStr = ''
  // macroOptions.value.forEach(i => {
  //   if(macroValue.value.includes(i.value)){
  //     macroStr +=  `${i.showValue}`
  //   }
  // });

  // let customMacro = `${macroValue.value.join(',')}`

  // rang.setValue({
  //   v:macroStr,
  //   p:'',
  //   custom:{
  //     "macro": customMacro
  //   }
  // });
};

const destroyUniver = () => {
  toRaw(univerRef.value)?.dispose();
  univerRef.value = null;
};

/**
 * 设置下拉变量
 * @param v  dataSourceType数据来源
 */
const setVariable = (v = '') => {
  const custom = v || '';
  options.value = initOp.value.find(
    i => i.dataOriginName == custom
  )?.businessVariable;
};

// 获取custom
const getCustom = (row = 0, col = 0) => {
  const activeSheet = univerAPI.getActiveWorkbook().getActiveSheet();
  // 行，列
  return activeSheet.getRange(row, col, 1, 1)?.getCellData();
};

const setCustom = (row = 0, col = 0, v) => {
  const activeSheet = univerAPI.getActiveWorkbook().getActiveSheet();
  const range = activeSheet.getRange(row, col, 1, 1);
  range.setValue({
    custom: {
      v,
    },
  });
};

let useindex = ref(1);
const fnOptions = ref([
  {
    value: 'SUM()',
    label: 'SUM',
  },
  // {
  //   value:'Max()',
  //   label:'Max'
  // },{
  //   value:'Min()',
  //   label:'Min'
  // },{
  //   value:'Count()',
  //   label:'Count'
  // }
]);

const application = v => {
  let rang = activeSheet.value.getRange(
    rangArea.value[0],
    rangArea.value[1],
    1,
    1
  );
  (optionsName.value = `${optionsName.value}${v}`),
    rang.setValue({
      v: `${optionsName.value}`,
      p: '',
    });
};

let expressionRef = ref(null);
const changeExpression = () => {
  let value = expressionRef.value.innerText;
  optionsName.value = `${value}`;

  let rang = activeSheet.value.getRange(
    rangArea.value[0],
    rangArea.value[1],
    1,
    1
  );
  rang.setValue({
    v: `${optionsName.value}`,
    p: '',
  });
};

// 打开装口
const open = () => {
  csProject.getDataSourceList().then(res => {
    initOp.value = res.result;
  });
};
open();

// 设置精确
const changePrecision = () => {
  const data = getCustom(rangArea.value[0], rangArea.value[1]);
  let rang = activeSheet.value.getRange(
    rangArea.value[0],
    rangArea.value[1],
    1,
    1
  );
  rang.setValue({
    v: data.v,
    p: '',
    custom: {
      ...toRaw(data.custom),
      precision: precision.value,
    },
  });
};

const close = () => {
  store.customMenu = '';
  univerAPI.disposeUnit('sheet1');
  destroyUniver();
  emits('onClose');
};
/**
 * 插入行
 */
const insertRow = (startRow, startRowPos = null) => {
  let row = startRow == 'before' ? rangArea.value[0] - 1 : rangArea.value[0];
  const sheet = univerAPI.getActiveWorkbook().getActiveSheet();
  sheet.insertRowsAfter(startRowPos ? startRowPos : row, 1);
};

// 右键按钮
const addChildeRef = ref(null);
const addMacroRef = ref(null);
watchEffect(() => {
  if (store.customMenu == 'addMacro') {
    // 宏变量
    addMacroRef.value.open();
  }

  if (['addFatherRow', 'addChiderRow', 'addRow'].includes(store?.customMenu)) {
    // 插入父级， 插入子细节 ， 插入细节，
    addChildeRef.value.open(store?.customMenu);
  }

  if (['addRows'].includes(store?.customMenu)) {
    // 插入行
    handleOkRowsData();
    store.customMenu = '';
  }

  if (['addCell'].includes(store?.customMenu)) {
    // 插入列
    handleOkCellData();
    store.customMenu = '';
  }
});

const exportData = (_type, isAuto = false) => {
  // if (['excel'].includes(_type) && isAuto) {
  //   SettingStatus.value = true;
  //   return;
  // }
  let form = getUniverForm();

  let landSpace = getLocalStorage()?.print?.landSpace;

  let params = {
    // jsonData: JSON.parse(JSON.stringify(savedData)),
    jsonDataVO: form.jsonData,
    sheetName: props.lanMuName,
    headLine: props.data?.headLine,
    isLandScape: getLocalStorage().print?.landSpace,
    page: getLocalStorage().pageResult,
  };

  console.log('🚀发送的数据:', params);
  if ('excel' == _type) {
    csProject.exportDirectoryExcel(params).then(res => {
      if (res?.result) {
        message.success('导出成功！');
      }
    });
  }
  if ('pdf' == _type) {
    csProject.exportDirectoryPdf(params).then(res => {
      if (res?.result) {
        message.success('导出成功！');
      }
    });
  }
};

// 插入宏变量
const handleOkMacro = v => {
  onChangeMacro(v);
};

//插入带区父级
const handleOkRowZone = data => {
  let newCurrentRow = 0;
  let { resources, ...savedData } = univerAPI.getActiveWorkbook().save();
  let rowData = Object.values(savedData?.sheets?.sheet1.rowData);
  let currentRow = null; //插入完之后的选中行
  if (rangArea.value.length) {
    currentRow = rangArea.value[0];

    if (['addFatherRow', 'addChiderRow'].includes(data.btnType)) {
      insertRow(data.rowPos);
    } else if (['addRow'].includes(data.btnType)) {
      for (let i = currentRow; i < rowData.length; i++) {
        console.log('🚀 ~ handleOkRowZone ~ rowData[i+1]:', rowData[i + 1]);
        if (rowData[i + 1] && !rowData[i + 1]['custom']?.parentZone) {
          // 下一级和上一级没啥关联
          newCurrentRow = i;
          break;
        }
      }
      insertRow('', newCurrentRow);
    }

    setTimeout(() => {
      savedData = univerAPI.getActiveWorkbook().save();
      rowData = Object.values(savedData?.sheets?.sheet1.rowData);
      const Zone = generateRandomString(); // 新生成的带区的名字

      //addRow，addChiderRow，addFatherRow
      let insertRowField = {
        field: data.field,
        rowType: data.field,
        dataSourceType: data.dataSourceType,
      };
      let insertRowList = []; // 新增的行
      let currentRowList = []; // 选中的行

      let currentConfig = {}; // 以前选中的行配置
      let currentCustom = null;

      let insertConfig = {}; // 当前选中的行配置

      const sheet = univerAPI.getActiveWorkbook().getActiveSheet();
      let range = null; //插入的坐标
      if (data.btnType == 'addFatherRow') {
        //自动获取到新增的那一行
        currentCustom = rowData[currentRow + 1]?.custom;
        insertConfig[currentRow] = {
          ...insertRowField,
          Zone: generateRandomString(),
        };

        if (!currentCustom?.Zone) {
          // 判断以前的选中行有没有带区
          currentConfig[currentRow + 1] = {
            ...currentCustom,
            Zone: generateRandomString(),
            parentZone: insertConfig[currentRow].Zone,
          };
        } else {
          currentConfig[currentRow + 1] = {
            ...currentCustom,
          };
        }
      } else if (data.btnType == 'addChiderRow') {
        // 插入子细节
        currentCustom = rowData[currentRow]?.custom;

        if (!currentCustom?.Zone) {
          // 判断以前的选中行有没有带区
          currentConfig[currentRow] = {
            ...currentCustom,
            Zone: generateRandomString(),
          };
        } else {
          currentConfig[currentRow] = {
            ...currentCustom,
          };
        }

        insertConfig[currentRow + 1] = {
          ...insertRowField,
          Zone: generateRandomString(),
          parentZone: currentConfig[currentRow].Zone,
        };
      } else if (data.btnType == 'addRow') {
        // 插入细节
        // 不插入有父子关联的，插入到最后

        currentCustom = rowData[currentRow]?.custom;

        if (!currentCustom?.Zone) {
          // 判断以前的选中行有没有带区
          currentConfig[currentRow] = {
            ...currentCustom,
            Zone: generateRandomString(),
          };
        } else {
          currentConfig[currentRow] = {
            ...currentCustom,
          };
        }

        insertConfig[newCurrentRow + 1] = {
          ...insertRowField,
          Zone: generateRandomString(),
        };
      }

      if (Object.keys(insertConfig).length) {
        sheet.setRowCustom(insertConfig);
      }

      if (Object.keys(currentConfig).length) {
        sheet.setRowCustom(currentConfig);
      }
    }, 100);
  }
};

// 插入行，带区里加一行
const handleOkRowsData = () => {
  if (rangArea.value.length) {
    insertRow();
    setTimeout(() => {
      const { resources, ...savedData } = univerAPI.getActiveWorkbook().save();

      const rowData = Object.values(savedData?.sheets?.sheet1.rowData);
      let currentRow = rangArea.value[0];
      let currentConfig = {};
      let currentCustom = rowData[currentRow]?.custom;
      let insertConfig = {};
      insertConfig[currentRow + 1] = rowData[currentRow].custom;

      let Zone = null;
      if (!currentCustom?.Zone) {
        // 判断选中行有没有带区
        currentCustom['Zone'] = generateRandomString();
        currentConfig[currentRow] = currentCustom;
      }
      insertConfig[currentRow + 1] = currentCustom;

      univerAPI.getActiveWorkbook().getActiveSheet().setRowCustom(insertConfig);
      univerAPI
        .getActiveWorkbook()
        .getActiveSheet()
        .setRowCustom(currentConfig);

      // handlePermission()
    }, 100);
  }
};

// 插入行,更新两行数据
const updateHandleOkRowsData = () => {
  if (rangArea.value.length) {
    insertRow();
    setTimeout(() => {
      const { resources, ...savedData } = univerAPI.getActiveWorkbook().save();
      const cellData = Object.values(savedData?.sheets?.sheet1.cellData);
      let currentRow = rangArea.value[0];
      let currentConfig = null; //选中的行
      let insertRowList = []; // 新增的行
      let currentRowList = []; // 选中的行
      const sheet = univerAPI.getActiveWorkbook().getActiveSheet();
      const range = sheet.getRange(
        currentRow,
        0,
        2,
        savedData?.sheets?.sheet1.columnCount
      );

      currentRowList = Object.values(cellData[currentRow]);
      currentConfig = { ...cellData[currentRow][0]?.custom };
      let insertRowField = {
        ...currentConfig,
      };

      let Zone = null;
      if (!currentConfig.Zone) {
        // 判断选中行有没有带区
        Zone = generateRandomString();
        currentRowList.forEach(i => {
          i.custom.Zone = Zone;
        });
      }

      for (let i = 0; i <= savedData?.sheets?.sheet1.columnCount; i++) {
        insertRowList.push({
          ...insertRowField,
          v: '',
          s: currentConfig.s,
          custom: {
            Zone,
            ...currentConfig,
          },
        });
      }

      range.setValues([currentRowList, insertRowList]);

      univerAPI.getActiveWorkbook().save();

      // handlePermission()
    }, 100);
  }
};

// 插入列，带区里加一列
const handleOkCellData = () => {
  if (rangArea.value.length) {
    const sheet = univerAPI.getActiveWorkbook().getActiveSheet();
    const currentCol = rangArea.value[1];
    sheet.insertColumnsAfter(currentCol, 1);

    setTimeout(() => {
      const { resources, ...savedData } = univerAPI.getActiveWorkbook().save();
      const cellData = Object.values(savedData?.sheets?.sheet1.cellData);
      let insertColList = []; // 新增的列
      const sheet = univerAPI.getActiveWorkbook().getActiveSheet();
      const range = sheet.getRange(
        0,
        currentCol + 1,
        savedData?.sheets?.sheet1.rowCount - 1,
        1
      );

      for (let i = 0; i < savedData?.sheets?.sheet1.rowCount; i++) {
        insertColList.push([
          {
            ...cellData[i][currentCol],
            v: '',
            custom: cellData[i][currentCol].custom,
          },
        ]);
      }
      range.setValues([...insertColList]);
      univerAPI.getActiveWorkbook().save();
    }, 100);
  }
};

const generateRandomString = () => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// 设置保护权限
const handlePermission = async () => {
  const fWorkbook = univerAPI.getActiveWorkbook();

  const { resources, ...savedData } = univerAPI.getActiveWorkbook().save();

  const unitId = fWorkbook?.getId();
  const fSheet = fWorkbook.getActiveSheet();
  const subUnitId = fSheet?.getSheetId();
  const sheetName = fSheet?.getSheetName();
  const ranges = [
    {
      startRow: 0,
      startColumn: 0,
      endRow: savedData?.sheets?.sheet1.rowCount,
      endColumn: 0,
    },
  ];

  const permission = univerAPI.getPermission();
  const res = await permission.addRangeBaseProtection(
    unitId,
    subUnitId,
    ranges
  );
  const { permissionId, ruleId } = res;
  permission.setRangeProtectionPermissionPoint(
    unitId,
    subUnitId,
    permissionId,
    RangeProtectionPermissionEditPoint,
    false
  );
  permission.setPermissionDialogVisible(false);

  // const accessor = univer.__getInjector();
  // const commandService = accessor.get(ICommandService);
  // const sheetSelectionManagerService = accessor.get(SheetsSelectionsService);
  // const univerInstanceService = accessor.get(IUniverInstanceService);
  // const target = getSheetCommandTarget(univerInstanceService);
  // if (!target) {
  //   return;
  // }
  // const { unitId, subUnitId } = target;
  // // const ranges = sheetSelectionManagerService.getCurrentSelections().map(selection => selection.range);
  // const ranges = [
  //   { startRow: 5, startColumn: 0, endRow:7, endColumn: 0 }
  // ];
  // commandService.executeCommand(AddRangeProtectionMutation.id, {
  //   unitId,
  //   subUnitId,
  //   rules: [{
  //     permissionId: "3xtfxG1",
  //     name: "sheet1",
  //     unitType: 3,
  //     unitId,
  //     subUnitId,
  //     ranges,
  //     id: 'rule1'
  //   }],
  // });

  // const permissionService = accessor.get(IPermissionService);
  // permissionService.updatePermissionPoint(new RangeProtectionPermissionEditPoint(unitId, subUnitId, "3xtfxG1").id, false);
  // const permission = univerAPI.getPermission();
  // permission.setPermissionDialogVisible(false);
};

//  导出设置
let SettingStatus = ref(false);
let editOut = ref([]);
const setOutputSetting = () => {
  SettingStatus.value = true;
};
const CancelDialog = (isSave = false) => {
  SettingStatus.value = false;
  if (isSave) {
    exportData('excel');
  } else {
    editOut.value = [];
  }
};

const saveSetting = () => {
  CancelDialog(true);
};
</script>

<style lang="scss" scoped>
.editExcel-wraps {
  position: fixed;
  width: 100%;
  height: calc(100vh - 66px);
  top: 56px;
  left: 0;
  background-color: #fff;
  padding: 0;
  z-index: 99;
}
.univer-container {
  width: 80vw;
  height: 90vh;
  flex-shrink: 0;
  overflow: hidden;
}
.content-box {
  display: flex;
  width: 100vw;
  height: 100%;
}
.editTable-box {
  flex-direction: column;
  .univer-container {
    width: 100%;
    height: auto;
    flex: 1;
  }
  :deep(.univer-toolbar-container) {
    margin: inherit;
  }
}

.fn-tip {
  font-size: 12px;
  color: red;
  text-align: center;
  margin-bottom: 8px;
}

.univer-custom-toolbar {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
  padding-top: 0;
  flex-shrink: 0;
  background-color: #f0f0f0;
  .custom-bar-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0 8px 0;
    .cus-tip {
      font-weight: 400;
      font-size: 16px;
      color: #000000;
    }
    .btn-list {
      display: flex;
      align-items: center;
    }
  }

  .custom-content-nav {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    :deep(.ant-btn) {
      width: calc(50% - 2px);
    }
  }

  .custom-content-list {
    flex: 1;
    overflow-y: auto;
    .list-wrap {
      margin-top: 5px;
      border: 1px solid #e1e1e1;
      background-color: #fff;
      &:first-child {
        margin-top: 0;
      }
    }
    .items {
      position: relative;
      display: flex;
      border-bottom: 1px solid #e1e1e1;
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      padding: 7px 0;
      padding-left: 30px;
      cursor: pointer;
      &:hover {
        background-color: #f8fbff;
      }
      &::after {
        position: absolute;
        content: '';
        width: 0.5px;
        height: 100%;
        background-color: #c6c9cc;
        left: 18px;
        top: -50%;
      }
      &::before {
        position: absolute;
        content: '';
        width: 20px;
        height: 0.5px;
        background-color: #c6c9cc;
        left: 18px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .select-title {
      &::after {
        display: none;
      }
      &::before {
        display: none;
      }
      .icon {
        position: absolute;
        top: 50%;
        left: 12px;
        transform: translateY(-50%);
      }
    }
    .select-list {
      .items {
        font-weight: 400;
        font-size: 13px;
        color: #2a2a2a;
        padding-left: 40px;
      }
    }

    .precision-wraps {
      display: flex;
      align-items: center;
      .tips {
        font-size: 13px;
        white-space: nowrap;
      }
      .desc {
        color: rgba(222, 63, 63, 1);
        font-weight: 400;
        font-size: 12px;
        color: #de3f3f;
      }
      :deep(.ant-input) {
        flex: 1;
        max-width: 40%;
        margin: 0 4px;
      }
    }

    .funList-wrap {
      .sub-nav-list {
        display: flex;
        align-items: center;
        .title {
          flex: 1;
          border-bottom: 1px solid #e1e1e1;
          font-weight: 500;
          font-size: 14px;
          color: #000000;
          padding: 7px 0;
          cursor: pointer;
          opacity: 0.6;
          text-align: center;
          background-color: rgba($color: #fff, $alpha: 0.5);
          &.active {
            opacity: 1;
            color: #fff;
            background-color: rgba($color: #2867c7, $alpha: 1);
          }
        }
      }
      .edit-box {
        padding: 6px 4px;
        min-height: 100px;
        border: 1px solid #e1e1e1;
      }

      .fn-list {
        display: flex;
        flex-direction: column;
        span {
          font-weight: 400;
          font-size: 13px;
          padding: 7px 8px;
          color: #2a2a2a;
          cursor: pointer;
          border-bottom: 1px solid #e1e1e1;
          &:hover {
            background-color: #f8fbff;
          }
        }
      }
    }
  }
}

/* Also hide the menubar */
:global(.univer-menubar) {
  display: none;
}

.dialog-comms {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  top: auto;
  left: auto;

  .content-box {
    width: 100% !important;
  }
}

.radio-list {
  width: 600px;
  .type-box {
    background: #ffffff;
    border: 1px solid #d9d9d9;
    margin-bottom: 11px;
    border-radius: 2px;
    padding: 14px 13px;
  }
  .radio-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    span {
      font-weight: 400;
      font-size: 13px;
      color: #287cfa;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
}
</style>
