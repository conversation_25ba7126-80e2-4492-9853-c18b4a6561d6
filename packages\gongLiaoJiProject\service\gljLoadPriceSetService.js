const {Service} = require('../../../core');
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {BaseCarryingPriceInformation} = require("../models/BaseCarryingPriceInformation");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ConstructProjectRcj, GljConstructProjectRcj } = require("../models/GljConstructProjectRcj");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {RcjDetails} = require("../models/GsRcjDetails");
//const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const {NumberUtil} = require("../utils/NumberUtil");
const {HttpUtils} = require("../utils/HttpUtils");
const LoadPriceTypeEnum = require("../enums/LoadPriceTypeEnum");
const BsRemoteUrl = require("../enums/BsRemoteUrl");
const {LoadPriceComparison} = require("../models/LoadPriceComparison");
const ProjectDomain = require('../domains/ProjectDomain');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const WildcardMap = require('../core/container/WildcardMap');
const rcjTypeTree = require("../jsonData/gs_rcj_type_tree.json");
const ProjectTaxCalculationConstants = require("../constants/ProjectTaxCalculationConstants");
const RcjCommonConstants = require("../constants/RcjCommonConstants");


class GljLoadPriceSetService extends Service {
  constructor(ctx) {
    super(ctx);
  }



  //去重
  removeDuplicates(arr) {
    const seen = new Set();
    return arr.filter(item => {
      const key = JSON.stringify([item.cityName, item.fileDate, item.dataType]); // 将需要对比的多个属性组合成一个唯一的 key
      if (seen.has(key)) {
        return false;
      } else {
        seen.add(key);
        return true;
      }
    });
  }



  /**
   * 获取载价设置初始化数据
   * @param arg
   * @return {Promise<ResponseData>}
   */
  async queryLoadPriceAreaDate(arg) {
    let {constructId, singleId, unitId} = arg;
    let rgfId = null;
    //工程项目
    // if (ObjectUtils.isEmpty(unitId)) {
    //   let project = PricingFileFindUtils.getProjectObjById(constructId);
    //   rgfId = project.rgfId;
    // } else {
    //   let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //   rgfId = unit.rgfId;
    // }
    //查询人材机政策文件
    //let promise = await this.service.basePolicyDocumentService.querybase_policy_documentBySequenceNbr(rgfId);
    let allLoadPrice = await this.queryAllLoadPriceData();
    //冀外地区去重
    allLoadPrice = this.removeDuplicates(allLoadPrice);

    //根据价格类别分组
    let priceType = ArrayUtil.group(allLoadPrice, "dataTypeName");
    let keysArray = Array.from(priceType.keys());
    let result = {};
    for (const key of keysArray) {
      let areaMap = {};
      let priceMap = priceType.get(key);
      //根据地区分组
      let areaType = ArrayUtil.group(priceMap, "cityName");
      let areaKeysArray = Array.from(areaType.keys());
      if (key !== '推荐价'){
        //根据政策文件的地区展示默认
        // areaMap[promise.cityName] = areaType.get(promise.cityName).sort((a, b) => {
        areaMap['石家庄市'] = areaType.get('石家庄市').sort((a, b) => {
          const dateA = new Date(a.fileDate.replace('年', '-').replace('月', ''));
          const dateB = new Date(b.fileDate.replace('年', '-').replace('月', ''));
          return dateB - dateA;
        }).map(k => k.fileDate);
      }
      for (const areaKey of areaKeysArray) {
        // if (areaKey === '冀外地区' && ['推荐价','信息价'].includes(key)){
        //     continue;
        // }
        //if (areaKey === promise.cityName) {
        if (areaKey === '石家庄市') {
          continue;
        }
        let v = areaType.get(areaKey);
        //需要根据日期进行倒序排列
        v.sort((a, b) => {
          const dateA = new Date(a.fileDate.replace('年', '-').replace('月', ''));
          const dateB = new Date(b.fileDate.replace('年', '-').replace('月', ''));
          return dateB - dateA;
        });
        areaMap[areaKey] = v.map(k => k.fileDate);
      }
      result[key] = areaMap;
    }
    return result;
  }

  /**
   * 点击批量载价 返回载价编辑弹窗数据
   * @returns {Promise<void>}
   */
  async loadingPrice(args){
    //type 1 = 工程项目  2 单位工程
    //batchAllFlag,是否批量调整所有价格
    //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
    //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
    let {constructId, singleId,unitId,type,batchAllFlag,loadPriortyList,laodPriceConditionList} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    //远程获取的人材机数据
    //let remoteRcjData = await this.getRemoteRcjData(args);
    let rcjData = await this.loadPriceRcjData(type, 0, constructId, singleId, unitId, batchAllFlag);

    for (const item of rcjData) {
      //let filter = remoteRcjData.filter(itemRemote => itemRemote.id== item.standardId)[0];
      let  filters= await this.smartLoadPrice( {constructId: constructId, rcjId:item.rcjId, materialName:item.materialName, laodPriceConditionList: laodPriceConditionList} );
      let filter = filters[0];
      item.marketPriceBeforeLoading = this._getMarketPrice(item);
      this._setMarketPrice(item, filter.marketPrice)

      item.marketSourcePriceBeforeLoading = item.sourcePrice;

      item.informationPrice = filter.informationPrice;
      item.recommendPrice = filter.recommendPrice;
      item.informationSourcePrice = filter.informationSourcePrice;
      item.marketSourcePrice = filter.marketSourcePrice;
      item.recommendSourcePrice = filter.recommendSourcePrice;

      //原始的精准数据备份  用于优先级调整后
      item.marketPriceOrigin = filter.marketPrice;
      item.marketSourcePriceOrigin = filter.marketSourcePrice;//市场价价格来源
      item.recommendPriceOrigin = filter.recommendPrice;
      item.recommendSourcePriceOrigin = filter.recommendSourcePrice;
      item.informationPriceOrigin = filter.informationPrice;
      item.informationSourcePriceOrigin = filter.informationSourcePrice;

      item.informationPriceList = filter.informationPriceList;
      item.marketPriceList = filter.marketPriceList;
      item.recommendPriceList = filter.recommendPriceList;

      //挂待载价格
      await this.updateLoadPriceByLevel(loadPriortyList,item,0);

      //以下处理用于 有精准匹配 且匹配数据只有一条时 前端的放大镜不进行展示
      if (item.highlight) {
        if (item.informationPriceList!=null && item.informationPriceList.length == 1) {
          item.informationPriceList = null;
        }
        if (item.marketPriceList !=null && item.marketPriceList.length == 1) {
          item.marketPriceList = null;
        }
        if (item.recommendPriceList !=null && item.recommendPriceList.length == 1) {
          item.recommendPriceList = null;
        }
      }
      item.isExecuteLoadPrice = true;
    }
    let rcjMap= businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (type == 2) {  //单位工程
      rcjMap.set(FunctionTypeConstants.UNIT_LOAD_PRICE  + FunctionTypeConstants.SEPARATOR + unitId  ,rcjData);
    }else if (type == 1) {
      rcjMap.set(FunctionTypeConstants.PROJECT_LOAD_PRICE  ,rcjData);
    } else if(type === 3){
      rcjMap.set(FunctionTypeConstants.SINGLE_LOAD_PRICE  + FunctionTypeConstants.SEPARATOR + singleId  ,rcjData);
    }
    return rcjData.map(item => item.sequenceNbr);
    //查询人材机数据
    //调用接口得到精准、模糊数据
    //根据条件  更新人材机的待载价格、价格来源、信息价、市场价、推荐价、是否勾选全部置1
    //统计载入前后价格
  }


  /**
   * 单条载价应用
   * @returns {Promise<void>}
   */
  async loadingPriceOneUse(args) {
    //拿到所有人材机  进行数据更新
    let rcjKey = WildcardMap.generateKey(args.unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(args.constructId).resourceDomain.getResource(rcjKey);
    let taxMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let operatorRcj = [];
    if (ObjectUtils.isNotEmpty(rcjList)) {
      for (let o of rcjList) {
        if (o.sequenceNbr === args.sequenceNbr && o.isDataTaxRate !== 0) {
          o.sourcePrice = args.loadPrice.sourcePrice;
          o.loadPrice = parseFloat(args.loadPrice.marketPrice);
          o.marketPriceBeforeLoading = this._getMarketPrice(o);
          o.isExecuteLoadPrice = true;
          o.highlight = true;
          await this.applyLoadingForUnit(rcjList, o, operatorRcj);
        } else if (ObjectUtils.isNotEmpty(o.pbs)) {
          for (let p of o.pbs) {
            if (p.sequenceNbr === args.sequenceNbr && p.isDataTaxRate !== 0) {
              p.sourcePrice = args.loadPrice.sourcePrice;
              p.loadPrice = parseFloat(args.loadPrice.marketPrice);
              p.marketPriceBeforeLoading = this._getMarketPrice(p);
              p.isExecuteLoadPrice = true;
              p.highlight = true;
              await this.applyLoadingForUnit(rcjList, p, operatorRcj);
            }
          }
        }
      }
    }
    // let filter = rcjList.filter(o => o.sequenceNbr === args.sequenceNbr);
    // for (let i = 0; i < filter.length; i++) {
    //   let filterElement = filter[i];
    //   filterElement.sourcePrice = args.loadPrice.sourcePrice;
    //   filterElement.loadPrice = parseFloat(args.loadPrice.marketPrice);
    //   filterElement.marketPriceBeforeLoading = filterElement.marketPrice;
    //
    //   filterElement.isExecuteLoadPrice = true;
    //   filterElement.highlight = true;
    //   await this.applyLoadingForUnit(rcjList, filterElement);
    // }

    let uniqueUnitIds = [...new Set(operatorRcj.map(item => item.unitId))];
    for (let unitId of uniqueUnitIds) {
      // 自动计算=各种记取+费用汇总通知
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: args.constructId
      });
    }
    return true;
  }


  /**
   *  工程项目单条载价应用
   * @returns {Promise<void>}
   */
  async projectLoadingPriceOneUse(args) {
    let {constructId,singleId} = args;
    //拿到所有人材机  进行数据更新
    // let rcjKey = WildcardMap.WILDCARD;
    // let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    let  constructProjectRcjList=new Array();
    Number(args.loadPrice.marketPrice);
    let taxMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      let rcj = {"marketTaxPrice":Number(args.loadPrice.marketPrice),"donorMaterialPrice":null , "sourcePrice":args.loadPrice.sourcePrice ,"sequenceNbr":args.sequenceNbr,"highlight": true}
      rcj.isDataTaxRate = 1
      rcj.taxRate = args.taxRate
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj, 0);
      constructProjectRcjList.push(rcj);
    }else {
      let rcj = {"marketPrice":Number(args.loadPrice.marketPrice),"donorMaterialPrice":null , "sourcePrice":args.loadPrice.sourcePrice ,"sequenceNbr":args.sequenceNbr,"highlight": true}
      rcj.taxRate = args.taxRate
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj, 1);
      constructProjectRcjList.push(rcj);
    }

    args.constructProjectRcjList =constructProjectRcjList;
    if(ObjectUtils.isNotEmpty(singleId)){
      //单项应用
      args.levelType = ProjectTypeConstants.PROJECT_TYPE_SINGLE;
      await  this.service.gongLiaoJiProject.gljRcjCollectService.updateProjectRcjCellect(args);
    } else {
      //工程项目应用
      await  this.service.gongLiaoJiProject.gljRcjCollectService.updateProjectRcjCellect(args);
    }
    return true;
  }


  /**
   * 返回载价编辑弹窗数据
   * @param args
   * @returns {Promise<void>}
   */
  async loadPriceEditPage(args) {
    //rcjIdList 为勾选的人材机id   kindList 为勾选的类型
    let {constructId, singleId,unitId,type,pageNum,pageSize,kindList,rcjIdList,loadPriortyList} = args;
    let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);

    //按照优先级进行挂载
    if (loadPriortyList != null) {
      for (let i = 0; i < rcjDatas.length; i++) {
        await this.updateLoadPriceByLevel(loadPriortyList,rcjDatas[i],0);
      }
    }
    let rcjDataWant = rcjDatas.filter(item => kindList.includes(item.kind));
    for (let i = 0; i < rcjDataWant.length; i++) {
      rcjDataWant[i].isExecuteLoadPrice = true;
    }
    //同时对过滤掉的标识置为false
    let rcjDataNotWant = rcjDatas.filter(item => !kindList.includes(item.kind));
    for (let i = 0; i < rcjDataNotWant.length; i++) {
      rcjDataNotWant[i].isExecuteLoadPrice = false;
    }
    //对筛选出来的数据未执行勾选的 标识置为false
    for (let i = 0; i < rcjDataWant.length; i++) {
      if (!rcjIdList.includes(rcjDataWant[i].sequenceNbr)) {
        rcjDataWant[i].isExecuteLoadPrice = false;
      }
    }
    let start = (pageNum-1)*pageSize;
    let end = pageNum*pageSize;
    let total = rcjDataWant.length;
    let dataWant = rcjDataWant.slice(start,end);
    //同时返回统计信息
    let rcjDataOrigin = await this.queryConstructRcjByDeIdNew(type, 0, constructId, singleId, unitId);

    //不要二次解析的父级材料
    rcjDataOrigin = rcjDataOrigin.filter(k => !(k.markSum == 1 && (k.levelMark == 1 || k.levelMark == 2)));
    let result = await this.getLoadPriceVariety(rcjDatas,rcjDataOrigin);
    if (taxMethod === 0) {
      result.totalAfter = result.totalTaxAfter;
      result.totalBefore = result.totalTaxBefore;
    }
    result['total'] = total;
    result['data'] = dataWant;
    return result;
  }

  /**
   * 双击价格选择弹窗的数据行 更新待载价格
   * @param rcjId
   * @param popUpDataId
   * @param updatePrice
   * @returns {Promise<void>}
   */
  async updateLoadPrice(args){
    let {constructId, singleId,unitId,type,rcjId,popUpDataId,updatePrice,sourcePrice, loadPrice} = args;
    let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);
    let data = rcjDatas.filter(item => item.sequenceNbr==rcjId);
    if (ObjectUtils.isEmpty(data) || data[0].isDataTaxRate === 0) {
      return;
    }
    updatePrice= Number(updatePrice);
    if (popUpDataId == null && sourcePrice != null && updatePrice != null) { //表示双击价格
      data[0].loadPrice = updatePrice;
      data[0].sourcePrice = sourcePrice;
      data[0].highlight = true;
      return ;
    }
    if (popUpDataId == null && loadPrice != null) { //直接修改价格
      data[0].loadPrice = loadPrice;
      data[0].sourcePrice = "自行询价";
      data[0].highlight = false;
      return ;
    }
    let recommendList = [];
    let informationList = [];
    let marketList = [];
    if (data[0].recommendPriceList != null) {
      recommendList = data[0].recommendPriceList.map(item => item.sequenceNbr);
    }
    if (data[0].informationPriceList!=null) {
      informationList = data[0].informationPriceList.map(item => item.sequenceNbr);
    }
    if (data[0].marketPriceList != null) {
      marketList = data[0].marketPriceList.map(item => item.sequenceNbr);
    }
    if (popUpDataId != null && updatePrice==null) {
      //根据id判断修改的是市场价还是信息价等  修改待载价格、信息价等及价格来源
      if (recommendList.includes(popUpDataId)) {
        let filter = data[0].recommendPriceList.filter(item => item.sequenceNbr==popUpDataId);
        data[0].loadPrice = filter[0].marketPrice;
        data[0].recommendPrice = filter[0].marketPrice;
        data[0].recommendSourcePrice = filter[0].sourcePrice;
        data[0].sourcePrice = filter[0].sourcePrice;
      }
      if (informationList.includes(popUpDataId)) {
        let filter = data[0].informationPriceList.filter(item => item.sequenceNbr==popUpDataId);
        data[0].loadPrice = filter[0].marketPrice;
        data[0].informationPrice = filter[0].marketPrice;
        data[0].informationSourcePrice = filter[0].sourcePrice;
        data[0].sourcePrice = filter[0].sourcePrice;
      }
      if (marketList.includes(popUpDataId)) {
        let filter = data[0].marketPriceList.filter(item => item.sequenceNbr==popUpDataId);
        data[0].loadPrice = filter[0].marketPrice;
        data[0].marketSourcePrice = filter[0].sourcePrice;
        data[0].sourcePrice = filter[0].sourcePrice;
        this._setMarketPrice(data[0], filter[0].marketPrice)
      }
      data[0].highlight = true;
    }else if (popUpDataId != null && updatePrice!=null) {
      //根据id判断修改的是市场价还是信息价等  修改待载价格、信息价等及价格来源
      if (recommendList.includes(popUpDataId)) {
        let filter = data[0].recommendPriceList.filter(item => item.sequenceNbr==popUpDataId);
        data[0].loadPrice = updatePrice;
        data[0].recommendPrice = updatePrice;
        data[0].recommendSourcePrice = filter[0].sourcePrice;
        data[0].sourcePrice = filter[0].sourcePrice;
      }
      if (informationList.includes(popUpDataId)) {
        let filter = data[0].informationPriceList.filter(item => item.sequenceNbr==popUpDataId);
        data[0].loadPrice = updatePrice;
        data[0].informationPrice = updatePrice;
        data[0].informationSourcePrice = filter[0].sourcePrice;
        data[0].sourcePrice = filter[0].sourcePrice;
      }
      if (marketList.includes(popUpDataId)) {
        let filter = data[0].marketPriceList.filter(item => item.sequenceNbr==popUpDataId);
        data[0].loadPrice = updatePrice;
        data[0].marketSourcePrice = filter[0].sourcePrice;
        data[0].sourcePrice = filter[0].sourcePrice;
        this._setMarketPrice(data[0], updatePrice)
      }
      data[0].highlight = true;
    }
  }


  /**
   * 批量应用接口更新市场价、价格来源
   * @param args
   * @returns {Promise<void>}
   */
  async applyLoadingPriceInRcjDetails(args) {
    let {constructId, singleId,unitId,type} = args;
    let operatorRcj = [];
    let rcjDatas = (await this.getCurrentLoadingRcjs(constructId, singleId, unitId, type)).filter(item => item.isExecuteLoadPrice);
    //拿到所有人材机  进行数据更新
    if (type == 2) {
      let rcjKey = WildcardMap.generateKey( unitId) + WildcardMap.WILDCARD;
      let  rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
      for (let i = 0; i < rcjDatas.length; i++) {
        await this.applyLoadingForUnit(rcjList,rcjDatas[i], operatorRcj);
      }
      // //费用定额自动记取计算
      // await this.service.autoCostMathService.autoCostMath({
      //   constructId:constructId,
      //   singleId:singleId,
      //   unitId:unitId});
      // //计算费用代码和更新费用汇总
      // this.service.unitCostCodePriceService.countCostCodePrice({
      //   constructId: constructId,
      //   singleId: singleId,
      //   unitId: unitId,
      // });
    }
    if (type == 1) { //工程项目层级
      // let unitList = PricingFileFindUtils.getUnitList(constructId);
      let rcjKey =  WildcardMap.WILDCARD;
      let  rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
      let rcjListDetails = [];
      let ts1 = rcjList.filter(i => i.markSum === 1);
      if (!ObjectUtils.isEmpty(ts1)) {
        for (let t of ts1) {
          let ts2 = t.pbs;
          if (!ObjectUtils.isEmpty(ts2)) {
            for (let t1 of ts2) {
              if(t1.totalNumber !== 0) {
                rcjListDetails.push(t1);
              }
            }
          }
        }
      }

      let unitList=ProjectDomain.getDomain(constructId).getProjectTree().filter(item=>item.type===3);
      unitList.forEach((unit)=>{
        unit.sequenceNbr;
        // //费用定额自动记取计算
        // await this.service.autoCostMathService.autoCostMath({
        //   constructId:constructId,
        //   singleId:singleId,
        //   unitId:unitId});
        // //计算费用代码和更新费用汇总
        // this.service.unitCostCodePriceService.countCostCodePrice({
        //   constructId: constructId,
        //   singleId: singleId,
        //   unitId: unitId,
        // });
      });

      for (let i = 0; i < rcjDatas.length; i++) {
        await this.applyLoadingForConstruct(rcjList,rcjDatas[i], operatorRcj);
      }
      //清除单位的本次载价标识
      if (!ObjectUtils.isEmpty(rcjList)) {
        for (let i = 0; i < rcjList.length; i++) {
          rcjList[i].currentLoadingFinished =null;
        }
      }
      if (!ObjectUtils.isEmpty(rcjListDetails)) {
        for (let i = 0; i < rcjListDetails.length; i++) {
          rcjListDetails[i].currentLoadingFinished = null;
        }
      }

    }


    if (type == 3) { //单项层级
      let rcjList = [];
      let units = [];
      units = await this.service.gongLiaoJiProject.gljProjectService.calProjectSingleUnits(constructId, singleId, units);
      for (let unit of units) {
        let rcjKey = WildcardMap.generateKey(unit) + WildcardMap.WILDCARD;
        let unitRcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        if (ObjectUtils.isNotEmpty(unitRcjList)) {
          rcjList = rcjList.concat(unitRcjList);
        }
      }

      let rcjListDetails = [];
      let ts1 = rcjList.filter(i => i.markSum === 1);
      if (!ObjectUtils.isEmpty(ts1)) {
        for (let t of ts1) {
          let ts2 = t.pbs;
          if (!ObjectUtils.isEmpty(ts2)) {
            for (let t1 of ts2) {
              if(t1.totalNumber !== 0) {
                rcjListDetails.push(t1);
              }
            }
          }
        }
      }

      units.forEach((unit)=>{
        unit.sequenceNbr;
        // //费用定额自动记取计算
        // await this.service.autoCostMathService.autoCostMath({
        //   constructId:constructId,
        //   singleId:singleId,
        //   unitId:unitId});
        // //计算费用代码和更新费用汇总
        // this.service.unitCostCodePriceService.countCostCodePrice({
        //   constructId: constructId,
        //   singleId: singleId,
        //   unitId: unitId,
        // });
      });

      for (let i = 0; i < rcjDatas.length; i++) {
        await this.applyLoadingForConstruct(rcjList,rcjDatas[i], operatorRcj);
      }
      //清除单位的本次载价标识
      if (!ObjectUtils.isEmpty(rcjList)) {
        for (let i = 0; i < rcjList.length; i++) {
          rcjList[i].currentLoadingFinished =null;
        }
      }
      if (!ObjectUtils.isEmpty(rcjListDetails)) {
        for (let i = 0; i < rcjListDetails.length; i++) {
          rcjListDetails[i].currentLoadingFinished = null;
        }
      }

    }

    let uniqueUnitIds = [...new Set(operatorRcj.map(item => item.unitId))];
    for (let unitId of uniqueUnitIds) {
      // 自动计算=各种记取+费用汇总通知
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
    }

  }

  /**
   * 查询载价报告明细列表
   * @param args
   * @returns {Promise<void>}
   */
  async loadPriceList(args){
    //type 1 = 工程项目  2 单位工程
    let {constructId, singleId,unitId,type,kind} = args;
    let queryConstructRcjByDeIdNew1 =await this.queryConstructRcjByDeIdNew(type,kind,constructId, singleId,unitId);
    if (ObjectUtils.isEmpty(queryConstructRcjByDeIdNew1)){
      return null;
    }
    let ts = queryConstructRcjByDeIdNew1.filter(i=>!ObjectUtils.isEmpty(i.highlight) && i.highlight );
    return ts;
  }


  /**
   * 查询载价报告 -扇形图
   * @param args
   * @returns {Promise<void>}
   */
  async queryLoadPriceReportRcj(args){
    //type 1 = 工程项目  2 单位工程
    let {constructId, singleId,unitId,type,kind} = args;
    let rcjList = await this.queryConstructRcjByDeIdNew(type,0,constructId, singleId,unitId);
    if (ObjectUtils.isEmpty(rcjList)){
      return null;
    }
    let result = rcjList.reduce((acc, item) => {
      const key = item.type;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});

    let array = new Array();
    for (const type of Object.keys(result)) {
      let loadPriceComparison = new LoadPriceComparison();
      loadPriceComparison.type = type;
      let total = 0;
      for (const item of result[type]) {
        total = NumberUtil.add(total, parseFloat(item.total));
      }
      loadPriceComparison.total = total;
      array.push(loadPriceComparison);
    }

    return array;

  }

  /**
   * 查询载价报告 -柱状图
   * @param args
   * @returns {Promise<void>}
   */
  async queryLoadPriceReportTarget(args){
    //type 1 = 工程项目  2 单位工程
    let {constructId, singleId,unitId,type,kind} = args;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId).RCJ_COLLECT;
    let rcjList = await this.queryConstructRcjByDeIdNew(type,kind,constructId, singleId,unitId);
    if (ObjectUtils.isEmpty(rcjList)){
      return null;
    }

    let result = rcjList.reduce((acc, item) => {
      const key = item.type;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});

    let array = new Array();
    for (const type of Object.keys(result)) {
      let loadPriceComparison = new LoadPriceComparison();
      loadPriceComparison.type = type;
      let beforePrice = 0;
      let afterPrice = 0;
      for (const item of result[type]) {
        if (!ObjectUtils.isEmpty(item.highlight) && item.highlight==true){
          //进行过载价
          beforePrice = NumberUtil.addJd( beforePrice ,NumberUtil.multiplyJd(item.marketPriceBeforeLoading,parseFloat(item.totalNumber), precision.marketPrice, precision.totalNumber, null), precision.total, precision.total, null);
          afterPrice = NumberUtil.addJd(afterPrice ,NumberUtil.multiplyJd(this._getMarketPrice(item),parseFloat(item.totalNumber), precision.marketPrice, precision.totalNumber, null), precision.total, precision.total, null);
        }else {
          //没有进行过载价
          afterPrice = NumberUtil.addJd(afterPrice ,NumberUtil.multiplyJd(this._getMarketPrice(item),parseFloat(item.totalNumber), precision.marketPrice, precision.totalNumber, null), precision.total, precision.total, null);
          beforePrice =NumberUtil.addJd(beforePrice ,NumberUtil.multiplyJd(this._getMarketPrice(item),parseFloat(item.totalNumber), precision.marketPrice, precision.totalNumber, null), precision.total, precision.total, null);
        }

      }
      loadPriceComparison.beforePrice = beforePrice;
      loadPriceComparison.afterPrice = afterPrice;
      array.push(loadPriceComparison);
    }

    return array;

  }

  /**
   * 查询载价状态
   * @return {Promise<void>}
   */
  async loadPriceStatus(args) {
    //type 1 = 工程项目  2 单位工程
    let {constructId, singleId, unitId, type} = args;
    let result = [];
    let flag = false;
    if (type === 1) {
      //查询工程项目汇总
      let rcjKey =  WildcardMap.WILDCARD;
      let  rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
      if (!ObjectUtils.isEmpty(rcjList)) {
        flag = true;
      }
    } else if (type === 3) {
      let units = [];
      units = await this.service.gongLiaoJiProject.gljProjectService.calProjectSingleUnits(constructId, singleId, units);
      if (ObjectUtils.isNotEmpty(units)) {
        let rcjList = [];
        for (let item of units) {
          let rcjKey = WildcardMap.generateKey(item) + WildcardMap.WILDCARD;
          let rcjList1 = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
          if (ObjectUtils.isNotEmpty(rcjList1)) {
            rcjList = rcjList.concat(rcjList1);
          }
        }
        if (!ObjectUtils.isEmpty(rcjList)) {
          flag = true;
        }
      }
    } else if (type === 2) {
      let rcjKey = WildcardMap.generateKey( unitId) + WildcardMap.WILDCARD;
      let  rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
      if (!ObjectUtils.isEmpty(rcjList)){
        flag = true;
      }
    }
    if (flag){
      result.push(LoadPriceTypeEnum.LoadType4.code);
    }else {
      result.push(LoadPriceTypeEnum.LoadType5.code);
    }
    return result;
  }

  /**
   * 清除载价
   * @param args
   * @returns {Promise<void>}
   */
  async clearLoadPriceUse(args){
    //type 1 = 工程项目  2 单位工程
    let {constructId, singleId, unitId,type,rcj} = args;
    let operatorRcj = [];
    if (type === 1){
      let rcjKey =  WildcardMap.WILDCARD;
      let  rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
      await this.clearLoadPriceUseUnit(rcjList,rcj, operatorRcj);
    } else if(type === 3){
      let units = [];
      units = await this.service.gongLiaoJiProject.gljProjectService.calProjectSingleUnits(constructId, singleId, units);
      if (ObjectUtils.isNotEmpty(units)) {
        for (let item of units) {
          let rcjKey = WildcardMap.generateKey(item) + WildcardMap.WILDCARD;
          let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
          await this.clearLoadPriceUseUnit(rcjList,rcj, operatorRcj);
        }
      }
    }else if(type === 2){
      let rcjKey = WildcardMap.generateKey( unitId) + WildcardMap.WILDCARD;
      let  rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
      await this.clearLoadPriceUseUnit(rcjList,rcj, operatorRcj);
    }
    let uniqueUnitIds = [...new Set(operatorRcj.map(item => item.unitId))];
    for (let unitId of uniqueUnitIds) {
      // 自动计算=各种记取+费用汇总通知
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
    }
    return true;
  }











  /**
   * 查询所有的载价信息表
   * @param arg
   * @return {Promise<void>}
   */
  async queryAllLoadPriceData() {
    let result =await this.app.db.gongLiaoJiProject.manager.getRepository(BaseCarryingPriceInformation).find();
    return result;
  }



  /**
   * 根据条件查询所有的载价信息表
   * @param arg
   * @return {Promise<void>}
   */
  async queryLoadPriceDataByCityName(cityName) {
    let loadPrice = await this.app.gljAppDataSource.getRepository(BaseCarryingPriceInformation).find({
      where: {
        cityName: cityName
      }
    });
    //根据价格类别分组
    let priceType = ArrayUtil.group(loadPrice, "dataTypeName");
    let keysArray = Array.from(priceType.keys());
    let result = {};
    for (const key of keysArray) {
      let priceList = priceType.get(key);
      //需要根据日期进行倒序排列
      priceList.sort((a, b) => {
        const dateA = new Date(a.fileDate.replace('年', '-').replace('月', ''));
        const dateB = new Date(b.fileDate.replace('年', '-').replace('月', ''));
        return dateB - dateA;
      });
      let {fileDate} = priceList[0];
      if (key === '推荐价'){
        result.recommend = fileDate;
      }
      if (key === '信息价'){
        result.information = fileDate;
      }
      if (key === '市场价'){
        result.market = fileDate;
      }
    }
    return result;
  }



  /**
   * 获取远程的人材机数据
   */
  async getRemoteRcjData(args) {
    //type 1 = 工程项目  2 单位工程
    //batchAllFlag,是否批量调整所有价格
    //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
    //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
    let {constructId, singleId, unitId, type, batchAllFlag, loadPriortyList, laodPriceConditionList} = args;
    //type 1 = 工程项目  2 单位工程
    let rcjData = await this.loadPriceRcjData(type, 0, constructId, singleId, unitId, batchAllFlag);
    //人材机list
    let rcjs = [];
    if (!ObjectUtils.isEmpty(rcjData)) {
      for (const rcj of rcjData) {
        let {standardId, materialName} = rcj;
        rcjs.push({id: standardId, name: materialName});
      }
    }
    //信息价
    let informationPriceList = laodPriceConditionList[0];
    let informationPriceListNew = [];
    for (const item of informationPriceList) {
      for (const key in item) {
        informationPriceListNew.push({areaName: key, yearMonths: item[key]})
      }
    }
    //市场价
    let marketPriceList = laodPriceConditionList[1];
    let marketPriceListNew = [];
    for (const item of marketPriceList) {
      for (const key in item) {
        marketPriceListNew.push({areaName: key, yearMonths: item[key]})
      }
    }

    //推荐价
    let recommendPriceList = laodPriceConditionList[2];
    let recommendPriceListNew = [];
    for (const item of recommendPriceList) {
      for (const key in item) {
        recommendPriceListNew.push({areaName: key, yearMonths: item[key]})
      }
    }
    let params = {
      rcjs: rcjs,
      informationPriceList: informationPriceListNew,
      marketPriceList: marketPriceListNew,
      recommendPriceList: recommendPriceListNew
    };
    //调用远程接口
    let promise = await HttpUtils.POST(BsRemoteUrl.gljLoadPriceAcquire, params);
    let result = JSON.parse(promise.result);
    return result;
  }


  /**
   * 批量载价前的人材机数据查询
   * @param type
   * @param kind
   * @param constructId
   * @param singleId
   * @param unitId
   * @param isCheck 是否勾选批量调整所有价格
   */
  async loadPriceRcjData(type, kind, constructId, singleId, unitId, isCheck) {
    let rcjData =await  this.queryConstructRcjByDeIdNew(type, kind, constructId, singleId, unitId);
    if (ObjectUtils.isEmpty(rcjData)) {
      return null;
    }
    //除单位为元、单位为%，已锁定市场价、已勾选是否汇总/二次解析的父级材料以外的
    let  rcjData2 = rcjData.filter(k => k.unit !== '元' && k.unit !== '%').filter(k => k.ifLockStandardPrice !== 1)
        .filter(k=>k.supplementDeRcjFlag !== 1)
        .filter(k => (k.markSum == 1 && (k.levelMark != 1 && k.levelMark != 2)) ||(k.markSum == 0 && (k.levelMark == 1 || k.levelMark == 2)))

    if (!isCheck){
      //没勾的时候过滤  加上价格来源为自行询价条件
      rcjData2 = rcjData2.filter(k => k.sourcePrice !== "自行询价");
    }

    return rcjData2;
  }


  /**
   * 查询人材机汇总 载价查询专用版
   * @param type 数据类型 1 工程项目层级  2 单位层级
   * @param kind 人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土 7 主要材料.设备
   * @param constructId 工程项目id
   * @param singleId 单项工程id
   * @param unitId 单位工程id
   */
  async queryConstructRcjByDeIdNew(type, kind, constructId, singleId, unitId) {
    //let { levelType, kind, constructId, singleId, unitId } = args;
    if (type === 2) {
      let  levelType=3
      let args={ kind, constructId, singleId, unitId ,levelType }
      return await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
    } else if(type === 1) {
      let  levelType=1
      let args={ kind, constructId, singleId, unitId ,levelType}
      return await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
    } else if(type === 3){
      let  levelType=2
      let args={ kind, constructId, singleId, unitId ,levelType}
      return await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
    }
  }




  /**
   * 根据载价优先级的设置 更改待载价格
   * @returns {Promise<void>}
   */
  //1信息价 2市场价  3推荐价
  async updateLoadPriceByLevel(loadPriortyList,rcjData,index) {

    if (loadPriortyList[index] == 0 && index !=2) {  //前两个选项都为空 走这
      return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
    };
    if ((loadPriortyList[index] == 0 && index == 2)||index==3) { //如果走到这里  取原来人材机的市场价
      if (rcjData.informationPrice!=null) {
        if (loadPriortyList[0] == 1 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 1) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 1)) {
          rcjData.loadPrice = rcjData.informationPrice;
          rcjData.sourcePrice = rcjData.informationSourcePrice;
          rcjData['highlight'] = true;
          return ;
        }
      }
      if (rcjData.recommendPrice!=null) {
        if (loadPriortyList[0] == 3 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 3) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 3)) {
          rcjData.loadPrice = rcjData.recommendPrice;
          rcjData.sourcePrice = rcjData.recommendSourcePrice;
          rcjData['highlight'] = true;
          return ;
        }
      }
      if (rcjData.marketPrice!=null) {
        if (loadPriortyList[0] == 2 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 2) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 2)) {
          rcjData.loadPrice = this._getMarketPrice(rcjData);
          rcjData.sourcePrice = rcjData.marketSourcePrice;
          rcjData['highlight'] = true;
          return ;
        }
      }
      rcjData.loadPrice = rcjData.marketPriceBeforeLoading;
      rcjData.sourcePrice = rcjData.marketSourcePriceBeforeLoading;
      rcjData['highlight'] = false;
      return;
    }
    if (loadPriortyList[index] == 1) { //如果为信息价
      if (rcjData.informationPriceOrigin != null) {
        rcjData.loadPrice = rcjData.informationPriceOrigin;
        rcjData.sourcePrice = rcjData.informationSourcePriceOrigin;

        //--STRAT---数据初始化--------------------------
        // rcjData.informationPrice = rcjData.informationPriceOrigin;
        // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
        // rcjData.marketPrice = rcjData.marketPriceOrigin;
        //
        // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
        // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
        // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
        //--END---数据初始化--------------------------
        rcjData['highlight'] = true;
        return ;
      }else {
        return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
      }
    }
    if (loadPriortyList[index] == 2) {  //如果为市场价
      if (rcjData.marketPriceOrigin != null) {
        rcjData.loadPrice = rcjData.marketPriceOrigin;
        rcjData.sourcePrice = rcjData.marketSourcePriceOrigin;

        //--STRAT---数据初始化--------------------------
        // rcjData.informationPrice = rcjData.informationPriceOrigin;
        // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
        // rcjData.marketPrice = rcjData.marketPriceOrigin;
        //
        // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
        // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
        // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
        //--END---数据初始化--------------------------
        rcjData['highlight'] = true;
        return ;
      }else {
        return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
      }
    }
    if (loadPriortyList[index] == 3) {  //如果为推荐价
      if (rcjData.recommendPriceOrigin != null) {
        rcjData.loadPrice = rcjData.recommendPriceOrigin;
        rcjData.sourcePrice = rcjData.recommendSourcePriceOrigin;

        //--STRAT---数据初始化--------------------------
        // rcjData.informationPrice = rcjData.informationPriceOrigin;
        // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
        // rcjData.marketPrice = rcjData.marketPriceOrigin;
        //
        // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
        // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
        // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
        //--END---数据初始化--------------------------
        rcjData['highlight'] = true;
        return ;
      }else {
        return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
      }
    }
  }



  /**
   * 取消勾选及类型 更新载入前后的人材机总价 及执行载价的条数
   * @param args
   * @returns {Promise<void>}
   */
  async cancelCheckOrType(args){
    let {constructId, singleId,unitId,rcjId,rcjType,type} = args;

    let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);
    //对对应的人材机数据的标识进行更改
    if (rcjId != null) {
      let filter = rcjDatas.filter(item => item.sequenceNbr == rcjId);
      filter.isExecuteLoadPrice = false;
    }
    if (rcjType != null) {
      let rcjList = rcjDatas.filter(item => item.kind == rcjType);
      for (let i = 0; i < rcjList.length; i++) {
        rcjList[i].isExecuteLoadPrice = false;
      }
    }

  }



  async applyLoadingForUnit(rcjList, rcjData, operatorRcj) {
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(rcjData.constructId).RCJ_COLLECT;
    let rcjListDetails = [];
    let ts1 = rcjList.filter(i => i.markSum === 1);
    if (!ObjectUtils.isEmpty(ts1)) {
      for (let t of ts1) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          for (let t1 of ts2) {
            if(t1.totalNumber !== 0) {
              rcjListDetails.push(t1);
            }
          }
        }
      }
    }

    //工程项目层级的相同材料划分粒度更细
    let rcjs = rcjList.filter(item => {

      return item.materialCode === rcjData.materialCode
          && item.materialName === rcjData.materialName
          && item.specification === rcjData.specification
          && item.unit === rcjData.unit
          && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcjData)
          && item.markSum === rcjData.markSum
          && this._getMarketPrice(item) === rcjData.marketPriceBeforeLoading
    });
    //二级材料的子级材料
    let rcjDetails = [];
    if (!ObjectUtils.isEmpty(rcjListDetails)) {
      rcjDetails = rcjListDetails.filter(item => {
        return item.materialCode === rcjData.materialCode
            && item.materialName === rcjData.materialName
            && item.specification === rcjData.specification
            && item.unit === rcjData.unit
            && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcjData)
            && item.markSum === rcjData.markSum
            && this._getMarketPrice(item) === rcjData.marketPriceBeforeLoading
      });
    }

    if (rcjs.length > 0) {
      operatorRcj.push(...ConvertUtil.deepCopy(rcjs));
      //修改市场价、价格来源等
      for (let j = 0; j < rcjs.length; j++) {
        let  currentRcj =rcjs[j]
        rcjs[j].marketPriceBeforeLoading = this._getMarketPrice(rcjs[j]);
        if(this._getMarketPrice(rcjs[j]) !==  rcjData.loadPrice){
          rcjs[j].donorMaterialPrice = null;
        }
        this._setMarketPrice(rcjs[j], rcjData.loadPrice)

        rcjs[j].sourcePrice = rcjData.sourcePrice;
        rcjs[j].total = NumberUtil.multiplyJd(rcjs[j].marketPrice, rcjs[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjs[j].totalTax = NumberUtil.multiplyJd(rcjs[j].marketTaxPrice, rcjs[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjs[j].baseJournalTotal = NumberUtil.multiplyJd(rcjs[j].baseJournalPrice, rcjs[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjs[j].baseJournalTotalTax = NumberUtil.multiplyJd(rcjs[j].baseJournalTaxPrice, rcjs[j].totalNumber, precision.marketPrice, precision.totalNumber, null);

        rcjs[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

        // 主材和设备，修改市场价，同步定额价
        rcjs[j].kind = rcjs[j].kind ? Number(rcjs[j].kind) : rcjs[j].kind;
        if (this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(rcjs[j])) {
          rcjs[j].baseJournalPrice = rcjs[j].marketPrice;
          rcjs[j].baseJournalTaxPrice = rcjs[j].marketTaxPrice;
        }

        if (rcjs[j].highlight!=null && rcjs[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过
          // 说明进行过匹配 已匹配的数据保留原来的高亮状态
        }else {
          rcjs[j].highlight = rcjData.highlight;
        }
        let  constructId=currentRcj.constructId;
        let unitId =  currentRcj.unitId;
        let deRowId=  currentRcj.deRowId;
        let  unitAllMemory=this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId,unitId);
        //处理内存
        if(ObjectUtils.isNotEmpty(unitAllMemory)){
          let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode === rcjs[j].materialCode);
          if(ObjectUtils.isNotEmpty(rcjMemorys)){
            rcjMemorys.forEach(item=>{
              item.sourcePrice=  rcjs[j].sourcePrice;

              //工料机
              item.marketPrice= rcjs[j].marketPrice;
              item.marketTaxPrice= rcjs[j].marketTaxPrice;
              item.baseJournalPrice= rcjs[j].baseJournalPrice;
              item.baseJournalTaxPrice= rcjs[j].baseJournalTaxPrice;
              item.isDataTaxRate = rcjs[j].isDataTaxRate ;
              item.taxRate= rcjs[j].taxRate;
            });
          }
        }
        await  ProjectDomain.getDomain(currentRcj.constructId).getDeDomain().notify({constructId,unitId,deRowId},false);
      }
      let constructProjectRcj = new GljConstructProjectRcj();
      constructProjectRcj.type = rcjData.type;
      constructProjectRcj.materialName = rcjData.materialName;
      constructProjectRcj.specification = rcjData.specification;
      constructProjectRcj.unit = rcjData.unit;
      constructProjectRcj.dePrice = rcjData.dePrice;
      //await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
    }
    if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
      for (let j = 0; j < rcjDetails.length; j++) {
        let  currentRcj =  rcjs.find(item=>item.sequenceNbr ===rcjDetails[j].parentId)
        rcjDetails[j].marketPriceBeforeLoading = this._getMarketPrice(rcjDetails[j]);
        if(this._getMarketPrice(rcjDetails[j]) !==  rcjData.loadPrice){
          rcjDetails[j].donorMaterialPrice = null;
        }
        this._setMarketPrice(rcjDetails[j], rcjData.loadPrice)

        rcjDetails[j].marketTaxPrice = ObjectUtils.isEmpty(rcjData.taxRate)? rcjData.loadPrice : NumberUtil.numberScale(NumberUtil.accMul(rcjData.loadPrice, (1 + rcjData.taxRate * 0.01)), precision.marketTaxPrice);
        rcjDetails[j].sourcePrice = rcjData.sourcePrice;
        if (this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(rcjDetails[j])) {
          rcjDetails[j].baseJournalPrice = rcjDetails[j].marketPrice;
          rcjDetails[j].baseJournalTaxPrice = rcjDetails[j].marketTaxPrice;
        }
        rcjDetails[j].total = NumberUtil.multiplyJd(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].totalTax = NumberUtil.multiplyJd(rcjDetails[j].marketTaxPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].baseJournalTotal = NumberUtil.multiplyJd(rcjDetails[j].baseJournalPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].baseJournalTotalTax = NumberUtil.multiplyJd(rcjDetails[j].baseJournalTaxPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);

        rcjDetails[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

        if (rcjDetails[j].highlight!=null && rcjDetails[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过  说明进行过匹配
        }else {
          rcjDetails[j].highlight = rcjData.highlight;
        }
        let parent = rcjList.find(i=>i.rcjId === rcjDetails[j].rcjId);
        await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialPrice(rcjListDetails,parent);
        let constructProjectRcj = new GljConstructProjectRcj();
        constructProjectRcj.type = parent.type;
        constructProjectRcj.materialName = parent.materialName;
        constructProjectRcj.specification = parent.specification;
        constructProjectRcj.unit = parent.unit;
        constructProjectRcj.dePrice = parent.dePrice;
        let  unitAllMemory=this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(rcjDetails[j].constructId,rcjDetails[j].unitId);
        //处理内存
        if(ObjectUtils.isNotEmpty(unitAllMemory)){
          let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode ===rcjDetails[j].materialCode);
          if(ObjectUtils.isNotEmpty(rcjMemorys)){
            rcjMemorys.forEach(item=>{
              item.sourcePrice=  rcjDetails[j].sourcePrice;

              //工料机
              item.marketPrice= rcjDetails[j].marketPrice;
              item.marketTaxPrice= rcjDetails[j].marketTaxPrice;
              item.baseJournalPrice= rcjDetails[j].baseJournalPrice;
              item.baseJournalTaxPrice= rcjDetails[j].baseJournalTaxPrice;
              item.isDataTaxRate = rcjDetails[j].isDataTaxRate ;
              item.taxRate= rcjDetails[j].taxRate;
            });
          }
        }
        if(ObjectUtils.isNotEmpty(currentRcj)){
          let  constructId=currentRcj.constructId;
          let unitId =  currentRcj.unitId;
          let deRowId=  currentRcj.deRowId;
          await  ProjectDomain.getDomain(currentRcj.constructId).getDeDomain().notify({constructId,unitId,deRowId},false);
        }
        //await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
      }
    }
  }


  async applyLoadingForConstruct(rcjList, rcjData, operatorRcj) {
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(rcjData.constructId).RCJ_COLLECT;
    let rcjListDetails = [];
    let ts1 = rcjList.filter(i => i.markSum === 1);
    if (!ObjectUtils.isEmpty(ts1)) {
      for (let t of ts1) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          for (let t1 of ts2) {
            if(t1.totalNumber !== 0) {
              rcjListDetails.push(t1);
            }
          }
        }
      }
    }

    //对于工程项目级别的载价  存在多找多的情况 即一对多改完的价格可能是另一个 一对多要找的价格  此时数据就发生错乱  添加本次载价的唯一标识
    let concatString = rcjData.materialCode.concat(!ObjectUtils.isEmpty(rcjData.materialName)?rcjData.materialName:"")
        .concat(!ObjectUtils.isEmpty(rcjData.specification)?rcjData.specification:"")
        .concat(!ObjectUtils.isEmpty(rcjData.unit)?rcjData.unit:"")
        .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(rcjData))?this._getBaseJournalPrice(rcjData):"")
        .concat(!ObjectUtils.isEmpty(rcjData.markSum)?rcjData.markSum:"")
        .concat(!ObjectUtils.isEmpty(rcjData.ifDonorMaterial)?rcjData.ifDonorMaterial:"")
        .concat(!ObjectUtils.isEmpty(rcjData.ifProvisionalEstimate)?rcjData.ifProvisionalEstimate:"")
        .concat(!ObjectUtils.isEmpty(rcjData.marketPriceBeforeLoading)?rcjData.marketPriceBeforeLoading:"")
        .concat(!ObjectUtils.isEmpty(rcjData.ifLockStandardPrice)?rcjData.ifLockStandardPrice:"");
    //工程项目层级的相同材料划分粒度更细
    let rcjs = [];
    if (!ObjectUtils.isEmpty(rcjList)) {
      rcjs = rcjList.filter(item => {

        let itemConcat = item.materialCode.concat(!ObjectUtils.isEmpty(item.materialName)?item.materialName:"")
            .concat(!ObjectUtils.isEmpty(item.specification)?item.specification:"")
            .concat(!ObjectUtils.isEmpty(item.unit)?item.unit:"")
            .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(item))?this._getBaseJournalPrice(item):"")
            .concat(!ObjectUtils.isEmpty(item.markSum)?item.markSum:"")
            .concat(!ObjectUtils.isEmpty(item.ifDonorMaterial)?item.ifDonorMaterial:"")
            .concat(!ObjectUtils.isEmpty(item.ifProvisionalEstimate)?item.ifProvisionalEstimate:"")
            .concat(!ObjectUtils.isEmpty(this._getMarketPrice(item))?this._getMarketPrice(item):"")
            .concat(!ObjectUtils.isEmpty(item.ifLockStandardPrice)?item.ifLockStandardPrice:"");

        return itemConcat == concatString && (!item.currentLoadingFinished||item.currentLoadingFinished==null);
      });
    }

    //二级材料的子级材料
    let rcjDetails = [];
    if (!ObjectUtils.isEmpty(rcjListDetails)) {
      rcjDetails = rcjListDetails.filter(item => {
        let itemConcat = item.materialCode.concat(!ObjectUtils.isEmpty(item.materialName)?item.materialName:"")
            .concat(!ObjectUtils.isEmpty(item.specification)?item.specification:"")
            .concat(!ObjectUtils.isEmpty(item.unit)?item.unit:"")
            .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(item))?this._getBaseJournalPrice(item):"")
            .concat(!ObjectUtils.isEmpty(item.markSum)?item.markSum:"")
            .concat(!ObjectUtils.isEmpty(item.ifDonorMaterial)?item.ifDonorMaterial:"")
            .concat(!ObjectUtils.isEmpty(item.ifProvisionalEstimate)?item.ifProvisionalEstimate:"")
            .concat(!ObjectUtils.isEmpty(this._getMarketPrice(item))?this._getMarketPrice(item):"")
            .concat(!ObjectUtils.isEmpty(item.ifLockStandardPrice)?item.ifLockStandardPrice:"");

        return itemConcat == concatString && (!item.currentLoadingFinished||item.currentLoadingFinished==null);
      });
    }

    if (rcjs.length > 0) {
      operatorRcj.push(...ConvertUtil.deepCopy(rcjs));
      //修改市场价、价格来源等
      for (let j = 0; j < rcjs.length; j++) {
        rcjs[j].marketPriceBeforeLoading = this._getMarketPrice(rcjs[j]);
        if(this._getMarketPrice(rcjs[j])  !==  rcjData.loadPrice){
          rcjs[j].donorMaterialPrice = null;
        }
        this._setMarketPrice(rcjs[j], rcjData.loadPrice);

        rcjs[j].sourcePrice = rcjData.sourcePrice;
        rcjs[j].total = NumberUtil.multiplyJd(rcjs[j].marketPrice, rcjs[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjs[j].totalTax = NumberUtil.multiplyJd(rcjs[j].marketTaxPrice, rcjs[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjs[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

        if (rcjs[j].highlight!=null && rcjs[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过
          // 说明进行过匹配 已匹配的数据保留原来的高亮状态
        }else {
          rcjs[j].highlight = rcjData.highlight;
        }
        rcjs[j].currentLoadingFinished = true;
      }
      let constructProjectRcj = new GljConstructProjectRcj();
      constructProjectRcj.type = rcjData.type;
      constructProjectRcj.materialName = rcjData.materialName;
      constructProjectRcj.specification = rcjData.specification;
      constructProjectRcj.unit = rcjData.unit;
      constructProjectRcj.dePrice = rcjData.dePrice;
      //await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
    }
    if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
      for (let j = 0; j < rcjDetails.length; j++) {
        rcjDetails[j].marketPriceBeforeLoading = this._getMarketPrice(rcjDetails[j]);
        if(this._getMarketPrice(rcjDetails[j])  !==  rcjData.loadPrice){
          rcjDetails[j].donorMaterialPrice = null;
        }
        this._setMarketPrice(rcjDetails[j], rcjData.loadPrice);

        rcjDetails[j].sourcePrice = rcjData.sourcePrice;
        rcjDetails[j].total = NumberUtil.multiplyJd(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].totalTax = NumberUtil.multiplyJd(rcjDetails[j].marketTaxPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

        if (rcjDetails[j].highlight!=null && rcjDetails[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过  说明进行过匹配
        }else {
          rcjDetails[j].highlight = rcjData.highlight;
        }
        rcjDetails[j].currentLoadingFinished = true;
        let parent = rcjList.find(i=>i.rcjId === rcjDetails[j].rcjId);
        await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialPrice(rcjListDetails,parent);
        let constructProjectRcj = new GljConstructProjectRcj();
        constructProjectRcj.type = parent.type;
        constructProjectRcj.materialName = parent.materialName;
        constructProjectRcj.specification = parent.specification;
        constructProjectRcj.unit = parent.unit;
        constructProjectRcj.dePrice = parent.dePrice;
        //await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
      }
    }
  }

  /**
   * 获取执行载价前后的人材机总价变化及载价条数的变化
   * @param rcjListPage 载价编辑页面的人材机数据
   * @param rcjDataTotal
   */
  async getLoadPriceVariety(rcjListPage,rcjDataTotal) {
    let totalBefore = 0;
    let totalAfter = 0;
    let totalTaxBefore = 0;
    let totalTaxAfter = 0;
    let loadNumber = 0;//载价条数统计
    for (let i = 0; i < rcjDataTotal.length; i++) {
      totalBefore +=parseFloat(rcjDataTotal[i].total);
      totalTaxBefore +=parseFloat(rcjDataTotal[i].totalTax);
      //拿到载价页面的数据
      let filter = rcjListPage.filter(item => item.sequenceNbr==rcjDataTotal[i].sequenceNbr);
      if (filter[0] !=null) {
        if (filter[0].isExecuteLoadPrice) {
          totalAfter += parseFloat((filter[0].loadPrice * filter[0].totalNumber).toFixed(2));
          totalTaxAfter += parseFloat((filter[0].loadPrice * filter[0].totalNumber).toFixed(2));
          loadNumber+=1;
        }else {
          totalAfter += parseFloat(rcjDataTotal[i].total);
          totalTaxAfter += parseFloat(rcjDataTotal[i].totalTax);
        }
      }else {
        totalAfter += parseFloat(rcjDataTotal[i].total);
        totalTaxAfter += parseFloat(rcjDataTotal[i].totalTax);
      }
    }
    return {
      "loadNumber":loadNumber,
      "totalAfter":totalAfter.toFixed(2),//载价后的人材机总价
      "totalBefore":totalBefore.toFixed(2),
      "totalTaxAfter":totalTaxAfter.toFixed(2),//载价后的人材机总价
      "totalTaxBefore":totalTaxBefore.toFixed(2)
    };
  }

  async getCurrentLoadingRcjs(constructId,singleId,unitId,type) {
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjMap= businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (type == 2) {  //单位工程
      return rcjMap.get(FunctionTypeConstants.UNIT_LOAD_PRICE  + FunctionTypeConstants.SEPARATOR + unitId  );
    }else if (type == 1) {
      return  rcjMap.get(FunctionTypeConstants.PROJECT_LOAD_PRICE  );
    } else if (type == 3) {
      return  rcjMap.get(FunctionTypeConstants.SINGLE_LOAD_PRICE  + FunctionTypeConstants.SEPARATOR + singleId );
    }
    return null;
  }





  /**
   * 智能询价
   * @param args
   * @returns {Promise<void>}
   */
  async smartLoadPrice(args){
    //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
    //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
    let {constructId, singleId, unitId, rcjId, materialName, laodPriceConditionList=[]} = args;
    if (ObjectUtils.isEmpty(rcjId) || ObjectUtils.isEmpty(materialName)){
      return null;
    }
    let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

    let ssCityName = "石家庄市";
    if (ObjectUtils.isEmpty(ssCityName)){
      ssCityName = "石家庄市";
    }
    //根据 城市名称 获取 最新 期刊日期
    let promise1 = await this.queryLoadPriceDataByCityName(ssCityName);

    //获取推荐价最新期刊
    let promise2 = await this.queryLoadPriceDataByCityName("推荐价数据");

    //跟新请求数据
    // let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:rcjId});
    // if (materialName !== baseRCJ.materialName){
    //   let  jxRcj =  await this.service.gongLiaoJiProject.gljBaseRcjService.getOneJxpbByName({rcjId:rcjId,materialName:materialName});
    //   let  clRcj =  await this.service.gongLiaoJiProject.gljBaseRcjService.getOneClpbByName({rcjId:rcjId,materialName:materialName});
    //   let  resultRcj ;
    //   if(ObjectUtils.isNotEmpty(jxRcj)){
    //     resultRcj =  await this.service.gongLiaoJiProject.gljBaseRcjService.getOneRcjByCode({libraryCode:jxRcj.libraryCode,materialCode:jxRcj.materialCode});
    //     rcjId = resultRcj.sequenceNbr;
    //   }
    //   if(ObjectUtils.isNotEmpty(clRcj)){
    //     resultRcj =  await this.service.gongLiaoJiProject.gljBaseRcjService.getOneRcjByCode({libraryCode:clRcj.libraryCode,materialCode:clRcj.materialCode});
    //     rcjId = resultRcj.sequenceNbr;
    //   }
    // }

    //人材机list
    let rcjs = [];
    rcjs.push(
        {id: rcjId,
          name: materialName}
    );

    //信息价
    let informationPriceListNew = [];
    informationPriceListNew.push({areaName: ssCityName, yearMonths: promise1.information})
    //市场价
    let marketPriceListNew = [];
    marketPriceListNew.push({areaName: ssCityName, yearMonths: promise1.market })
    //推荐价
    let recommendPriceListNew = [];
    recommendPriceListNew.push({areaName: "推荐价数据", yearMonths: promise2.recommend })

    // 用户过滤
    for (let i = 0; i < laodPriceConditionList.length; i++) {
      let conditionList = laodPriceConditionList[i]
      let newConditionList = conditionList.map(item => {
        let areaName = Object.keys(item)[0]; // 获取对象的键名
        return {
          areaName: areaName,
          yearMonths: item[areaName] // 获取对应键名的值
        };
      });
      if (i === 0) {
        informationPriceListNew = newConditionList
      } else if (i === 1){
        marketPriceListNew = newConditionList
      } else if (i === 2){
        recommendPriceListNew = newConditionList
      }
    }
    let params = {
      rcjs: rcjs,
      informationPriceList: informationPriceListNew,
      marketPriceList: marketPriceListNew,
      recommendPriceList: recommendPriceListNew,
      simpleMethod: taxMethod === 1? false : null
    };
    //调用远程接口
    let promise = await HttpUtils.POST(BsRemoteUrl.gljLoadPriceAcquire, params);
    let result = JSON.parse(promise.result);
    return result;

  }

  /**
   * 智能询价后应用
   * @param args
   * @returns {Promise<void>}
   */
  async smartLoadPriceUse(args) {
    let {constructId, singleId, unitId, deId, sequenceNbr, loadPrice, marketPrice} = args;

    let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
      let param = {
        constructId,
        singleId,
        unitId,
        deId,
        rcjDetailId: sequenceNbr,
        constructRcj: {
          marketTaxPrice: Number(loadPrice.marketPrice),
          sourcePrice: loadPrice.sourcePrice,
          highlight: true,
        },
      }
      await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(param);
    } else {
      let param = {
        constructId,
        singleId,
        unitId,
        deId,
        rcjDetailId: sequenceNbr,
        constructRcj: {
          marketPrice: Number(loadPrice.marketPrice),
          sourcePrice: loadPrice.sourcePrice,
          highlight: true,
        },
      }
      await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(param);
    }

    return true;
  }



  /**
   * 汇总 清除载价
   * @param rcjData 人材机对象
   * @returns {Promise<void>}
   */
  async clearLoadPriceUseUnit(constructProjectRcjs,rcjData, operatorRcj){
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(rcjData.constructId).RCJ_COLLECT;
    //人材机二级
    let rcjDetailList = [];
    let ts1 = constructProjectRcjs.filter(i => i.markSum === 1);
    if (!ObjectUtils.isEmpty(ts1)) {
      for (let t of ts1) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          for (let t1 of ts2) {
            if(t1.totalNumber !== 0) {
            }
            rcjDetailList.push(t1);
          }
        }
      }
    }
    //查询人材机政策文件
    // let promise =await this.service.basePolicyDocumentService.queryBySequenceNbr(unitProject.rgfId);
    let rcjs = constructProjectRcjs.filter(item => {
      return item.materialCode === rcjData.materialCode //.replace(/[^\w-]/g, '')
          && item.materialName === rcjData.materialName
          && item.specification === rcjData.specification
          && item.unit === rcjData.unit
          && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcjData)
          && item.markSum === rcjData.markSum
          && this._getMarketPrice(item) === this._getMarketPrice(rcjData)
    });
    //二级材料的子级材料
    let rcjDetails = rcjDetailList.filter(item => {
      return item.materialCode === rcjData.materialCode //.replace(/[^\w-]/g, '')
          && item.materialName === rcjData.materialName
          && item.specification === rcjData.specification
          && item.unit === rcjData.unit
          && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcjData)
          && item.markSum === rcjData.markSum
          && this._getMarketPrice(item) === this._getMarketPrice(rcjData)
    });

    if (rcjs.length > 0) {
      operatorRcj.push(...ConvertUtil.deepCopy(rcjs));
      //修改市场价、价格来源等
      for (let j = 0; j < rcjs.length; j++) {
        let rcj = rcjs[j];
        rcjs[j].marketPriceBeforeLoading = null;
        // rcjs[j].marketPrice = rcjs[j].baseJournalPrice;
        // rcjs[j].marketTaxPrice = rcjs[j].baseJournalTaxPrice;
        this._marketPriceRecover(rcjs[j])
        rcjs[j].sourcePrice = null;
        if(rcjs[j].kind==4 || rcjs[j].kind==5){
          rcjs[j].marketPrice= 0;
          rcjs[j].marketTaxPrice= 0;
          rcjs[j].baseJournalPrice= 0;
          rcjs[j].baseJournalTaxPrice= 0;
        }
        rcjs[j].total = NumberUtil.numberScale(rcjs[j].marketPrice * rcjs[j].totalNumber, precision.total);
        rcjs[j].totalTax = NumberUtil.numberScale(rcjs[j].marketTaxPrice * rcjs[j].totalNumber, precision.totalTax);
        rcjs[j].highlight = false;

        let resource = {
          constructId: rcj.constructId,
          unitId: rcj.unitId,
          deRowId: rcj.deId? rcj.deId: rcj.deRowId
        }
        await ProjectDomain.getDomain(rcj.constructId).getResourceDomain().notify(resource);
      }

    }
    if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
      for (let j = 0; j < rcjDetails.length; j++) {
        let rcjDetail = rcjDetails[j];
        rcjDetails[j].marketPriceBeforeLoading = null;

        // rcjDetails[j].marketPrice = rcjDetails[j].baseJournalPrice;
        // rcjDetails[j].marketTaxPrice = rcjDetails[j].baseJournalTaxPrice;
        this._marketPriceRecover(rcjDetails[j])
        rcjDetails[j].sourcePrice =null;
        if(rcjDetails[j].kind==4 || rcjDetails[j].kind==5){
          rcjDetails[j].marketPrice= 0;
          rcjDetails[j].marketTaxPrice= 0;
          rcjDetails[j].baseJournalPrice= 0;
          rcjDetails[j].baseJournalTaxPrice= 0;
        }
        rcjDetails[j].total = NumberUtil.multiplyJd(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].totalTax = NumberUtil.multiplyJd(rcjDetails[j].marketTaxPrice, rcjDetails[j].totalNumber, precision.marketPrice, precision.totalNumber, null);
        rcjDetails[j].highlight = false;
        let parent = constructProjectRcjs.find(i=>i.rcjId === rcjDetails[j].rcjId);
        // await this.service.rcjProcess.parentMaterialPrice(rcjDetails,parent);
        //await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialPrice(rcjDetails, parent);

        let resource = {
          constructId: rcjDetail.constructId,
          unitId: rcjDetail.unitId,
          deRowId: rcjDetail.deId? rcjDetail.deId: rcjDetail.deRowId
        }
        await ProjectDomain.getDomain(rcjDetail.constructId).getResourceDomain().notify(resource);

      }
    }
  }

  /**
   * 鼠标右键查询人材机关联定额
   * @param args
   * @returns {Promise<void>}
   */
  async getRcjDe(args){
    let list = [];
    let {constructId, singleId, unitId,rcj} = args;
    //获取单位工程
    let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

    let constructProjectRcjs = unit.constructProjectRcjs;

    let rcjDetailList = unit.rcjDetailList;

    //分部分项
    let itemBillProjects = unit.itemBillProjects;

    //措施项目
    let measureProjectTables = unit.measureProjectTables;

    //一级材料
    let rcjs = constructProjectRcjs.filter(item => {
      return item.materialCode === rcj.materialCode
          && item.materialName === rcj.materialName
          && item.specification === rcj.specification
          && item.unit === rcj.unit
          && item.dePrice === rcj.dePrice
          && item.markSum === rcj.markSum
          && item.marketPrice === rcj.marketPrice
    });
    //二级材料的子级材料
    let rcjDetails =null;
    if (!ObjectUtils.isEmpty(rcjDetailList)) {
      rcjDetails = rcjDetailList.filter(item => {
        return item.materialCode === rcj.materialCode
            && item.materialName === rcj.materialName
            && item.specification === rcj.specification
            && item.unit === rcj.unit
            && item.dePrice === rcj.dePrice
            && item.markSum === rcj.markSum
            && item.marketPrice === rcj.marketPrice
      });
    }

    if (ObjectUtils.isEmpty(rcjs) && ObjectUtils.isEmpty(rcjDetails)){
      return null;
    }
    let set = new Set();
    //添加包含2级材料 定额id
    if (!ObjectUtils.isEmpty(rcjDetails)){
      for (let rcjDetail of rcjDetails) {
        let t = constructProjectRcjs.find(i=>i.sequenceNbr == rcjDetail.rcjId);
        set.add(t.deId);
      }
    }
    //添加包含1级材料 定额id
    if (!ObjectUtils.isEmpty(rcjs)){
      for (let rcj1 of rcjs) {
        set.add(rcj1.deId);
      }
    }
    let a =[];
    //查询所有包含人材机定额
    if (!ObjectUtils.isEmpty(set)){
      let set1 = new Set();

      //递归获取上级所有id
      for (let setElement of set) {
        await this.recursionGetDeParentId(setElement,set1,itemBillProjects,measureProjectTables);
      }

      let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
      let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

      let itemBillProjects1 = fbFx.filter(i=>set1.has(i.sequenceNbr));
      let measureProjectTables1 = csxm.filter(i=>set1.has(i.sequenceNbr));


      if (!ObjectUtils.isEmpty(itemBillProjects1)){
        a.push(...itemBillProjects1);
      }

      if (!ObjectUtils.isEmpty(measureProjectTables1)){
        a.push(...measureProjectTables1);
      }
    }

    let label = false;
    let label1 = false;
    for (let itemBillProject of a) {
      if (itemBillProject.kind == "03"){
        if (!label){
          let name =null;
          let promise = await this.recursionGetQdParentName(itemBillProject.parentId,name,itemBillProjects,measureProjectTables);
          let fb = {};
          fb.sequenceNbr = itemBillProject.parentId;
          fb.name = promise;
          fb.bdName = promise;
          fb.kind = "02";
          list.push(fb);
          label1 = true;
          label =true;
        }
        let b = ConvertUtil.deepCopy(itemBillProject);
        if (!b.hasOwnProperty('bdCode')){
          b.bdCode = b.fxCode;
        }
        list.push(b);
      }
      if (itemBillProject.kind == "04"){
        let b = ConvertUtil.deepCopy(itemBillProject);
        list.push(b);
      }

      if ((itemBillProject.kind == "01"|| itemBillProject.kind == "02") && label1 == true){
        label = false
      }
    }

    let qdTotal  = 0;
    let fbTotal  = 0;
    for (let i = list.length - 1; i >= 0; i--) {
      if (list[i].kind == "04"){
        let total =  await this.getDeRcjTotal(list[i].sequenceNbr,constructProjectRcjs,rcjDetailList,rcj);
        list[i].total = total;
        qdTotal = NumberUtil.add(qdTotal,total);
      }

      if (list[i].kind == "03"){
        let total =  await this.getDeRcjTotal(list[i].sequenceNbr,constructProjectRcjs,rcjDetailList,rcj);
        qdTotal = NumberUtil.add(qdTotal,total);
        list[i].total = qdTotal;
        fbTotal = NumberUtil.add(fbTotal,qdTotal);
        qdTotal = 0;
      }

      if (list[i].kind == "02"){
        list[i].total = fbTotal;
        fbTotal = 0;
      }
    }

    return list;
  }

  /**
   * 获取定额下某一种材料的 数量汇总
   * @param id
   * @param constructProjectRcjs
   * @param rcjDetailList
   * @param rcj
   * @returns {Promise<void>}
   */
  async getDeRcjTotal(id,constructProjectRcjs,rcjDetailList,rcj){

    let totalNumber = 0;
    for (let constructProjectRcj of constructProjectRcjs) {
      if (constructProjectRcj.deId == id){
        if (constructProjectRcj.materialCode === rcj.materialCode
            && constructProjectRcj.materialName === rcj.materialName
            && constructProjectRcj.specification === rcj.specification
            && constructProjectRcj.unit === rcj.unit
            && constructProjectRcj.dePrice === rcj.dePrice
            && constructProjectRcj.markSum === rcj.markSum){
          totalNumber = NumberUtil.add(totalNumber,constructProjectRcj.totalNumber)
        }
        let rcjDetaiFilter =[];
        if (!ObjectUtils.isEmpty(rcjDetailList)) {
          rcjDetaiFilter= rcjDetailList.filter(i => i.rcjId == constructProjectRcj.sequenceNbr);
        }


        for (let rcjDetaiFilterElement of rcjDetaiFilter) {

          if (rcjDetaiFilterElement.materialCode === rcj.materialCode
              && rcjDetaiFilterElement.materialName === rcj.materialName
              && rcjDetaiFilterElement.specification === rcj.specification
              && rcjDetaiFilterElement.unit === rcj.unit
              && rcjDetaiFilterElement.dePrice === rcj.dePrice
              && rcjDetaiFilterElement.markSum === rcj.markSum){

            totalNumber = NumberUtil.add(totalNumber,rcjDetaiFilterElement.totalNumber)

          }
        }
      }
    }
    return totalNumber;
  }

  /**
   * 递归把父级名字拼出来
   * @param id
   * @param name
   * @param itemBillProjects
   * @param measureProjectTables
   * @returns {Promise<void>}
   */
  async recursionGetQdParentName(id,name,itemBillProjects,measureProjectTables){
    let find =null;
    if (!ObjectUtils.isEmpty(itemBillProjects)){
      find = itemBillProjects.find(i=>i.sequenceNbr == id);
    }
    if (ObjectUtils.isEmpty(find) && !ObjectUtils.isEmpty(measureProjectTables)){
      find = measureProjectTables.find(i=>i.sequenceNbr == id);
    }

    if (!ObjectUtils.isEmpty(find)){
      let bdName =null;
      if (!ObjectUtils.isEmpty(find.bdName)){
        bdName = find.bdName;
      }
      if (!ObjectUtils.isEmpty(find.name)){
        bdName = find.name;
      }

      if (bdName == null){
        bdName = " ";
      }

      if (ObjectUtils.isEmpty(name)){
        name = bdName;
      }else {
        if (!ObjectUtils.isEmpty(bdName)) {
          name = bdName + "/" + name;
        }else {
          name = " " + "/" + name;
        }
      }
      if (!ObjectUtils.isEmpty(find.parentId) && find.parentId !="0"){
        return await this.recursionGetQdParentName(find.parentId,name,itemBillProjects,measureProjectTables);
      }else {
        return name;
      }
    }

  }

  /**
   * 递归获取定额父id
   * @param id
   * @param set
   * @param itemBillProjects
   * @param measureProjectTables
   * @returns {Promise<void>}
   */
  async recursionGetDeParentId(id,set,itemBillProjects,measureProjectTables){
    let find =null;
    if (!ObjectUtils.isEmpty(itemBillProjects)){
      find = itemBillProjects.find(i=>i.sequenceNbr == id);
    }
    if (ObjectUtils.isEmpty(find) && !ObjectUtils.isEmpty(measureProjectTables)){
      find = measureProjectTables.find(i=>i.sequenceNbr == id);
    }


    if (!ObjectUtils.isEmpty(find)){
      set.add(find.sequenceNbr);
      if (!ObjectUtils.isEmpty(find.parentId) && find.parentId !="0"){
        await this.recursionGetDeParentId(find.parentId,set,itemBillProjects,measureProjectTables);
      }
    }

  }


  /**
   * 鼠标右键查询人材机关联定额树结构
   * @param args
   * @returns {Promise<void>}
   */
  async getConstructIdTree(args){
    let {constructId,rcj} = args;

    let unitList = PricingFileFindUtils.getUnitList(constructId);
    let set = new Set();
    set.add(constructId);
    for (let unit of unitList) {

      let constructProjectRcjs = unit.constructProjectRcjs;

      let rcjDetailList = unit.rcjDetailList;

      //一级材料
      let rcjs =[];
      let rcjDetails = [];
      if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
        rcjs = constructProjectRcjs.filter(item => {
          return item.materialCode === rcj.materialCode
              && item.materialName === rcj.materialName
              && item.specification === rcj.specification
              && item.unit === rcj.unit
              && item.dePrice === rcj.dePrice
              && item.markSum === rcj.markSum
              && item.marketPrice === rcj.marketPrice
        });
      }
      //二级材料的子级材料
      if (!ObjectUtils.isEmpty(rcjDetailList)) {
        rcjDetails = rcjDetailList.filter(item => {
          return item.materialCode === rcj.materialCode
              && item.materialName === rcj.materialName
              && item.specification === rcj.specification
              && item.unit === rcj.unit
              && item.dePrice === rcj.dePrice
              && item.markSum === rcj.markSum
              && item.marketPrice === rcj.marketPrice
        });
      }


      if (rcjs.length!=0 || rcjDetails.length!=0 ){
        set.add(unit.sequenceNbr);
        set.add(unit.spId);
      }
    }

    let result = new Array();
    //获取项目结构树
    let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);
    let generateLevelTreeNode = await this.service.constructProjectService.generateLevelTreeNode(projectObj, result);
    let filter = generateLevelTreeNode.filter(i=>set.has(i.id));

    return filter;
  }

  /**
   * 鼠标右键 查询定额是否存在 以及 是分部分项还是措施项目
   * @param args
   * @returns {Promise<void>}
   */
  async existDe(args){
    let {constructId, singleId, unitId,sequenceNbr} = args;

    // 预算书
    let yssDes = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
    let t = yssDes.find(i => i.sequenceNbr === sequenceNbr);
    if (!ObjectUtils.isEmpty(t)){
      return {
        "exist":true,
        "type":"yss"
      }
    }

    // 措施项目
    let csxmDes = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId);
    let t1 = csxmDes.find(i => i.sequenceNbr === sequenceNbr);
    if (!ObjectUtils.isEmpty(t1)){
      return {
        "exist":true,
        "type":"csxm"
      }
    }

    return {
      "exist":false
    };

  }

  /**
   * 人工费使用 取费文件
   * @param rcj
   * @param promise
   * @returns {Promise<void>}
   */
  async useRgf(rcj,promise) {

    if (rcj.materialCode == "10000001" ||
        rcj.materialCode == "10000002" ||
        rcj.materialCode == "10000003" ||
        rcj.materialCode == "JXPB-005") {

      switch (rcj.materialCode) {
        case "10000001":
          rcj.marketPrice = promise.zhygLevel1;
          rcj.sourcePrice = promise.cityName + promise.pricesource;
          break;
        case "10000002":
          rcj.marketPrice = promise.zhygLevel2;
          rcj.sourcePrice = promise.cityName + promise.pricesource;
          break;
        case "10000003":
          rcj.marketPrice = promise.zhygLevel3;
          rcj.sourcePrice = promise.cityName + promise.pricesource;
          break;
        case "JXPB-005":
          rcj.marketPrice = promise.zhygLevel2;
          rcj.sourcePrice = promise.cityName + promise.pricesource;
          break;
      }
    }
  }

  /**
   * 人材机询价
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getRcjXj(args) {
    //type 1 = 工程项目  2 单位工程
    //batchAllFlag,是否批量调整所有价格
    //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
    //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
    let {constructId, singleId, unitId, type, batchAllFlag, loadPriortyList, laodPriceConditionList} = args;

    //type 1 = 工程项目  2 单位工程
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    //远程获取的人材机数据
    let remoteRcjData = await this.getRemoteRcjData(args);


    return ;
  }




  /**
   * 逐条载价 查询人材机
   * @param args
   * @returns {Promise<void>}
   */
  async getGljZtzjRcj(args){

    let params = {
      materialName: args.materialName,
      classlevel01: args.classlevel01,
      classlevel02: args.classlevel02,
      classlevel03: args.classlevel03,
      classlevel04: args.classlevel04,
      areaName: args.areaName,
      yearMonths: args.yearMonths,
      type: args.type
    };

    let promise;
    try {
      //调用远程接口
      promise = await HttpUtils.POST(BsRemoteUrl.loadPriceGetZtzj, params);
      if (!ObjectUtils.isEmpty(promise)){
        if (promise.hasOwnProperty("result")){
          promise = promise.result;
        }

      }
    } catch (e) {
      return null;
    }

    return promise
  }


  /**
   * 获取逐条载价 人材机类型树
   * @param
   * @returns
   */
  async getGljRcjTypeTree(){
    return rcjTypeTree;
  }

  /**
   * 获取信息价 地区列表 城市下面带着地区
   * @param args
   * @returns {Promise<void>}
   */
  async getGljDimRegion(args){



    let promise;
    try {
      //调用远程接口
      promise = await HttpUtils.GET(BsRemoteUrl.sfb_Dim_Region);
      if (!ObjectUtils.isEmpty(promise)){
        if (promise.hasOwnProperty("result")){
          promise = promise.result;
        }

      }
    } catch (e) {
      return null;
    }

    return promise
  }



  /**
   * 获取基期价
   * @param rcj
   * @returns {Promise<*>}
   * @private
   */
  _getBaseJournalPrice(rcj) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      return rcj.baseJournalTaxPrice
    } else {
      return rcj.baseJournalPrice
    }
  }

  /**
   * 设置基期价
   * @param rcj
   * @param marketPrice
   * @private
   */
  _setBaseJournalPrice(rcj, baseJournalPrice) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      rcj.baseJournalTaxPrice = baseJournalPrice
    } else {
      rcj.baseJournalPrice = baseJournalPrice
    }
  }


  /**
   * 获取市场价
   * @param rcj
   * @returns {Promise<*>}
   * @private
   */
  _getMarketPrice(rcj) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      return rcj.marketTaxPrice
    } else {
      return rcj.marketPrice
    }
  }

  /**
   * 设置市场价
   * @param rcj
   * @param marketPrice
   * @private
   */
  _setMarketPrice(rcj, marketPrice) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      rcj.marketTaxPrice = marketPrice
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,0);
    } else {
      rcj.marketPrice = marketPrice
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,1);
    }
  }

  _marketPriceRecover(rcj) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if(taxMethod === RcjCommonConstants.SIMPLE_REVERSE){
      rcj.marketPrice=rcj.baseJournalPriceOriginalReverse;
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,0);
      // rcj.marketTaxPrice=rcj.baseJournalTaxPriceOriginalReverse;
    }
    if(taxMethod === RcjCommonConstants.GENERAL_FORWARD){
      rcj.marketPrice=rcj.baseJournalPriceOriginalForward;
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,1);
      // rcj.marketTaxPrice=rcj.baseJournalTaxPriceOriginalForward;
    }
  }

}
GljLoadPriceSetService.toString = () => '[class GljLoadPriceSetService]';
module.exports = GljLoadPriceSetService;
