<!--
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-18 19:16:28
 * @LastEditors: renmingming
 * @LastEditTime: 2024-06-26 09:34:07
-->
<template>
  <common-modal
    v-model:modelValue="show"
    className="dialog-comm"
    :title="props.convertToType === '01' ? '审定转预算文件' : '送审转预算文件'"
    :width="700"
    height="auto"
    @close="close"
  >
    <div class="convertTo">
      <div class="convertTo-preview">
        <span>预算文件存储位置：</span>
        <a-input style="width: 400px" v-model:value="data.saveLocation" />
        <a-button type="link" @click="preview">预览</a-button>
      </div>
      <div class="convertTo-checkbox">
        <a-checkbox
          class="checkbox"
          v-if="props.convertToType === '01'"
          v-model:checked="data.isContainDelete"
          >转预算后，保留审核中审删的清单/定额</a-checkbox
        ><br />
        <a-checkbox class="checkbox" v-model:checked="data.isOpenYsf"
          >生成的预算文件自动打开预算计价应用</a-checkbox
        ><br />
        <a-checkbox class="checkbox" v-model:checked="isClose"
          >是否关闭审核计价应用</a-checkbox
        >
      </div>
    </div>
    <div class="footer">
      <a-button type="primary" :loading="btnLoading" @click="saveData"
        >确定</a-button
      >
      <a-button @click="close">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch, nextTick, toRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import shApi from '@/api/shApi';
import csProject from '@/api/csProject';
import { useRoute } from 'vue-router';
const { ipcRenderer, webFrame } = require('electron');
import { useCellClick } from '@/hooks/useCellClick';
const route = useRoute();
import infoMode from '@/plugins/infoMode.js';
let saveStatus = ref(false);

import { message, Modal } from 'ant-design-vue';
import { pureNumber, isNumericExpression } from '@/utils/index';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const btnLoading = ref(false);
const address = ref('');
const data = ref({
  saveLocation: '',
  isContainDelete: false,
  isOpenYsf: true,
});
const isClose = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
  },
  title: {
    type: String,
  },
  convertToType: {
    type: String,
  },
});
const emit = defineEmits(['update:visible']);
const show = computed({
  get: () => props.visible,
  set: val => {
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    console.log(show.value);
    if (val) {
      init();
    }
  }
);
const init = () => {
  data.value.saveLocation = '';
  data.value.isContainDelete = false;
  data.value.isOpenYsf = true;
};
const preview = () => {
  shApi.shYsfSaveLocation().then(res => {
    if (res.status === 200 && res.result) {
      data.value.saveLocation = res.result + '\\' + store.projectName + '.YSF';
    }
  });
};

const close = () => {
  show.value = false;
};
const saveShfFile = () => {
  csProject.saveShfFile(route.query.constructSequenceNbr).then(res => {
    console.log(res);
    message.success('保存成功');
  });
};
const saveData = () => {
  if (!data.value.saveLocation) {
    message.error('请选择预算文件存储位置！');
    return;
  }
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    type: props.convertToType === '01' ? 1 : 2,
    ...data.value,
  };
  console.log(apiData, 'value123');
  shApi
    .shProjectToBudget(apiData)
    .then(res => {
      console.log(res, 'res');
      if (res.status === 200) {
        message.success(res.message);
        // 关闭
        if (isClose.value) {
          csProject
            .removeCheck({ constructId: route.query.constructSequenceNbr })
            .then(res => {
              console.log('removeCheck', res);
            });
          csProject
            .diffProject({ constructId: route.query.constructSequenceNbr })
            .then(res => {
              console.log(res, 'res');
              if (res) {
                saveStatus.value = !res.result;
              }
              if (!saveStatus.value) {
                close();
                infoMode.show({
                  iconType: 'icon-qiangtixing',
                  infoText: '当前文件未保存，是否保存？',
                  confirm: () => {
                    saveShfFile();
                    ipcRenderer.send('window-close-child', {
                      id: route.query.constructSequenceNbr,
                    });
                    infoMode.hide();
                  },
                  close: () => {
                    infoMode.hide();
                    ipcRenderer.send('window-close-child', {
                      id: route.query.constructSequenceNbr,
                    });
                  },
                });
              } else {
                ipcRenderer.send('window-close-child', {
                  id: route.query.constructSequenceNbr,
                });
              }
            });
        } else {
          close();
        }
      }
    })
    .catch(err => {
      console.log(err, 'err');
    });
};
</script>
<style lang="scss" scoped>
.convertTo {
  .convertTo-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      font-size: 14px;
    }
  }
  .convertTo-checkbox {
    margin: 15px 0px 0px 45px;
    .checkbox {
      margin-top: 20px;
    }
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
  position: relative;
  top: 15px;
  :deep(.ant-btn) {
    margin-right: 15px;
  }
}
</style>
