<template>
  <div class="bd-info">
    <vxe-table
      :data="props.tableData?.get('bdxx')"
      :column-config="{ resizable: true }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      class="table-scrollbar table-edit-common"
      :cell-class-name="cellClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      ref="vexTable"
      show-overflow
      height="500"
      @cell-click="useCellClickEvent"
    >
      <vxe-column width="60" field="dispNo"></vxe-column>
      <vxe-column title="名称" field="name"></vxe-column>
      <vxe-column
        title="内容"
        field="remark"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            placeholder="请输入内容"
            v-model="row.remark"
            type="text"
            name="name"
          ></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { useCellClick } from '@/hooks/useCellClick.js';

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const props = defineProps(['tableData']);
const cellClassName = ({ row, column, $columnIndex }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (row.requiredFlag === 1 && column.field === 'name') {
    return 'required-fields';
  }
  return selectName;
};
</script>

<style lang="scss" scoped>
::v-deep(.vxe-table .vxe-body--row .required-fields) {
  color: #de3f3f;
}
</style>
