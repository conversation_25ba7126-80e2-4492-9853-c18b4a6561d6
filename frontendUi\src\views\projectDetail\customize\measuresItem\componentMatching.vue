<!--
 * @Author: wangru
 * @Date: 2023-08-04 10:39:12
 * @LastEditors: wangru
 * @LastEditTime: 2024-05-31 14:13:26
 *组价方案匹配
-->
<template>
  <div class="centerCon">
    <div class="asideTree">
      <vxe-table
        ref="unitTree"
        :column-config="{ resizable: true }"
        :tree-config="{
          transform: true,
          rowField: 'id',
          parentField: 'parentId',
          expandAll: true,
          showLine: true,
        }"
        height="auto"
        :data="treeData"
        border="none"
        :show-header="false"
        :row-config="{
          keyField: 'id',
        }"
        :checkbox-config="{
          checkRowKeys: checkRowKeys,
          checkMethod: checkMethod,
        }"
      >
        <vxe-column
          type="checkbox"
          title="ID"
          tree-node
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template></vxe-column>

        <!-- <vxe-column field="name" title="Name" min-width="100"></vxe-column> -->
      </vxe-table>
    </div>
    <div class="rules">
      <div class="ruleItem">
        <p class="title">匹配规则</p>
        <a-radio-group
          v-model:value="matchRule"
          :options="matchRuleList"
        />
      </div>
      <div class="ruleItem">
        <p class="title">影响范围</p>
        <a-radio-group
          v-model:value="influenceScope"
          :options="influenceScopeList"
        />
      </div>
    </div>
  </div>
  <p class="footer">
    <span><icon-font
        type="icon-querenshanchu"
        class="iconFont"
      ></icon-font>安装费用、装饰超高、装饰垂运相关清单数据不会参与组价方案匹配</span>
    <a-button
      type="primary"
      @click="startMatch()"
    >开始组价</a-button>
  </p>
</template>
<script setup>
import { defineAsyncComponent, ref, watch, reactive, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import api from '../../../../api/projectDetail.js';
import { constructLevelTreeStructureList } from '@/api/csProject.js';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import infoMode from '../../../../plugins/infoMode';
const scheduleFile = defineAsyncComponent(() =>
  import('@/components/schedule/schedule.vue')
);
const route = useRoute();

const emits = defineEmits(['closeComMatch']);
const store = projectDetailStore();
let matchRule = ref('1');
let influenceScope = ref('1');
let showSchedule = ref(false);
const matchRuleList = reactive([
  {
    label: '精准组价',
    value: '1',
  },
  {
    label: '精准+近似组价',
    value: '2',
  },
]);
const influenceScopeList = reactive([
  {
    label: '仅匹配未组价清单',
    value: '1',
  },
  {
    label: '全量清单',
    value: '2',
  },
]);
let unitTree = ref(); //单位树
let checkRowKeys = ref([]);
let treeData = ref([]); // 设置检查范围数据
let checkedKeys = ref([]); // 设置检查范围选中数据
onMounted(() => {
  getTreeList();
});
const startMatch = () => {
  //开始组价
  let unitIdSelect = unitTree.value
    .getCheckboxRecords()
    .filter(item => item.levelType === 3);
  if (unitIdSelect.length === 0) {
    message.error('请勾选要执行组价方案匹配的单位工程');
    return;
  }
  let selectedUnitIdList = [];
  unitIdSelect.map(item => selectedUnitIdList.push(item.id));
  let hasSortIdlist = []; //有顺序的勾选中单位列表
  treeData.value.map(unit => {
    if (unit.levelType === 3 && selectedUnitIdList.includes(unit.id)) {
      hasSortIdlist.push(unit.id);
    }
  });
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    matchRule: matchRule.value,
    influenceScope: influenceScope.value,
    selectedUnitIdList: hasSortIdlist,
  };
  // console.log('selectedUnitIdList', hasSortIdlist);
  emits('closeComMatch', formData);
};
const getTreeList = () => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      console.log('详情res', res.result, store.currentTreeInfo?.id);
      if (res.status === 200) {
        treeData.value = res.result;
        checkRowKeys.value = [store.currentTreeInfo?.id];
      }
    }
  );
};
</script>
<style lang="scss" scoped>
.centerCon {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: space-between;
  .asideTree {
    width: 40%;
    height: 100%;

    border: 1px solid #b9b9b9;
    ::v-deep(.vxe-table) {
      .vxe-tree-cell {
        white-space: nowrap !important;
        // overflow: hidden;
        // text-overflow: ellipsis;
      }
      .vxe-table--render-default .vxe-tree-cell {
        white-space: nowrap;
      }
    }
  }
  .rules {
    width: 55%;
    height: 100%;
    border: 1px solid #b9b9b9;
    .ruleItem {
      margin: 20px 15px 0;
      .title {
        color: #287cfa;
      }
    }
  }
}
.footer {
  position: absolute;
  bottom: -30px;
  width: 94%;
  display: flex;
  justify-content: space-between;
}
:deep(.vxe-table .vxe-body--column) {
  text-align: left;
}
.iconFont {
  margin-right: 5px;
  font-size: 14px;
}
.reCheck {
  margin-top: 10px;
}
</style>
