<!--
 * @Descripttion: 合并相似材料
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: renmingming
 * @LastEditTime: 2024-10-30 10:53:18
-->
<template>
  <common-modal
    className="dialog-comm mergeMaterials-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="合并相似材料"
    width="900px"
    height="600px"
    min-width="900px"
    min-height="80vh"
    :mask="false"
    show-zoom
    resize
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div class="group-content-wrap">
      <div class="group-title-box">
        <div class="range-title">
          <div>
            合并条件： <a-radio-group
              v-model:value="prodType"
              @change="handleChangeProdType"
              :options="prodOptions"
            />
          </div>
          <div class="right-box">
            <a-input
              v-model:value="searchKey"
              @pressEnter="getTableList"
              :placeholder="'请输入名称/编码/关键字'"
            >
              <template #suffix>
                <i
                  class="vxe-icon-search"
                  style="color: rgba(191, 191, 191, 1)"
                ></i>
              </template>
            </a-input>
          </div>
        </div>
        <div class="condition-wrap">
          <div class="left-box">
            <a-checkbox
              v-model:checked="filterList.bm"
              @change="filterChange('bm')"
            >全选</a-checkbox>
          </div>
        </div>
      </div>
      <div class="content content-table">
        <vxe-table
          border
          ref="vexTable"
          align="center"
          :column-config="{ resizable: true }"
          :data="TableData"
          :checkStrictly="false"
          :row-class-name="rowClassName"
          height="98%"
          :row-config="{
                isCurrent: true,
                keyField: 'sequenceNbr',
              }"
          :checkbox-config="{
                showHeader: false,
                visibleMethod: ({ row }) => {
                  return !['04'].includes(row.kind);
                },
                checkMethod: ({ row }) => {
                  return true;
                },
              }"
          :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
          :tree-config="{
                transform: true,
                rowField: 'sequenceNbr',
                parentField: 'parentId',
                showLine: true,
                showIcon: true,
                expandAll: true,
                iconOpen: 'vxe-icon-caret-down',
                iconClose: 'vxe-icon-caret-right',
              }"
          @cell-click="cellClick"
          @cell-dblclick="dbClick"
          :show-overflow="true"
        >
          <vxe-column
            field="sort"
            width="40"
            title="序号"
          />
          <vxe-column
            field="bdCode"
            tree-node
            width="80"
            title="项目编码"
          >
          </vxe-column>
          <vxe-column
            field="type"
            width="60"
            title="类别"
          />
          <vxe-column
            field="quotaName"
            width="80"
            title="名称"
          >
            <template #default="{ row }">
              <span>{{ row.name }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="ggxh"
            width="100"
            title="规格型号"
          />
          <vxe-column
            field="unit"
            width="80"
            title="单位"
          > </vxe-column>
          <vxe-column
            field="ysj"
            width="80"
            title="预算价"
          >
          </vxe-column>
          <vxe-column
            field="price"
            width="80"
            title="市场价"
          >
          </vxe-column>
          <vxe-column
            field="rate"
            width="100"
            title="除税系数（%）"
          >
          </vxe-column>
          <vxe-column
            field="merge"
            min-width="60"
            title="参与合并"
            :cell-render="{}"
          >
            <template #default="{ row, rowIndex }">
              <vxe-checkbox
                v-model="row.cyhb"
                name="参与合并"
                @change="bringChange(row)"
                v-if="row.children?.length===0"
              ></vxe-checkbox>
            </template>
          </vxe-column>
          <vxe-column
            field="mergeMer"
            min-width="60"
            title="合并后材料"
            :cell-render="{}"
          >
            <template #default="{ row, rowIndex }">
              <vxe-checkbox
                v-model="row.hbhcl"
                name="合并后材料"
                @change="bringChange(row)"
                v-if="row.children?.length===0"
              ></vxe-checkbox>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="footer-box">
        <span class="handle-tips">
          <icon-font type="icon-querenshanchu"></icon-font>
          1、如需调整人材机，请先关闭本功能 2、配比材料合并时，建议优先合并配比明细材料
        </span>
        <p>
          <a-button
            type="primary"
            ghost
            @click="cancel()"
          >取消</a-button>
          <a-button
            type="primary"
            style="margin-left: 14px"
            @click="handleOk()"
          >确认合并</a-button>
        </p>
      </div>

    </div>
  </common-modal>
</template>
<script setup>
import {
  ref,
  toRaw,
  nextTick,
  reactive,
  computed,
  shallowRef,
  shallowReactive,
  defineExpose,
  getCurrentInstance,
  watch,
} from 'vue';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
const props = defineProps(['indexVisible']);
watch(
  () => props.indexVisible,
  () => {
    if (props.indexVisible) {
      open();
    }
  }
);
const route = useRoute();
const emits = defineEmits(['closeDialog', 'refresh']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);

let searchKey = ref('');
let prodType = ref(0);
let loading = ref(false);

let filterList = reactive({
  bm: true,
  num: 0,
  name: true,
  projectAttr: true,
  type: 0,
});
let prodOptions = [
  { label: '编码', value: 0 },
  { label: '名称', value: 1 },
  { label: '自定义', value: 2 },
];

const cancel = (refresh = false) => {
  if (refresh) {
    emits('refresh');
    return;
  }
  dialogVisible.value = false;
  emits('closeDialog');
};

const open = k => {
  dialogVisible.value = true;
};

const filterChange = type => {
  let { bm, name, projectAttr } = filterList;
  if (!bm && !name && !projectAttr) {
    filterList[type] = true;
  }
};

const rowClassName = ({ row }) => {
  if (row.parentId == 1) {
    return 'row-tree-title';
  }
};

// 确认合并
const handleOk = addOrReplace => {
  infoMode.show({
    isSureModal: true,
    iconType: 'icon-querenshanchu',
    infoText: '材料类别不同不支持合并！',
    confirm: () => {
      infoMode.hide();
    },
  });
};

defineExpose({
  open,
});
</script>

<style lang="scss">
.mergeMaterials-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .check-labels {
    white-space: nowrap;
  }
  .content-table {
    height: 80%;
    padding: 0 0px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .group-title-box {
    .range-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
    }
    .right-box {
      margin-bottom: 5px;
      width: 240px;
    }
  }
  .condition-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    font-size: 14px;
    margin-top: 0px;
    border-top: 1px solid rgba(185, 185, 185, 1);
    .left-box {
      display: flex;
      flex-wrap: wrap;
    }
    .items {
      margin-left: 13px;
      display: flex;
      align-items: center;
    }
    .list {
      display: flex;
      align-items: center;
    }
  }

  .footer-box {
    display: flex;
    justify-content: space-between;
    .footer-handle {
      display: flex;
      justify-content: space-between;
      .rang-tips {
        font-size: 14px;
      }
    }
  }

  .handle-tips {
    font-weight: 400;
    font-size: 12px;
    color: #2a2a2a;
  }

  .vxe-table {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
}
</style>
