/*
 * @Descripttion: 表1-14 招标人供应材料、设备明细表
 * @Author: sunchen
 * @Date: 2024-07-06 10:32:39
 * @LastEditors: sunchen
 * @LastEditTime: 2024-10-19 10:29:12
 */

export const ZBR114BookData = [
  {
    name: '表1-14 招标人供应材料、设备明细表',
    deType: [12], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表', '其他'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        EVf0W5: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        JYIwIz: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        dkrCNC: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        '6PNgad': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        Zx6VTU: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        jHudht: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        p6wfsK: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        KVtwJh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        AdyBgj: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        teJ78k: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        't-wFRB': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        wkPdoP: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        FTs3FH: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        Bn6F8s: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        zrIUeQ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        VcI9T8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        qONrQE: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        AAjndM: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        h48flX: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        ImISuO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        e3Wtvh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        kqWtAA: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        xMxJ7h: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rktHys: {
          bd: {
            t: null,
            l: null,
          },
        },
        RnYN3F: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        y6kLGr: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        dKsCGo: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        wDsUnc: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        mnflxa: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '9xuMxI': {
          ff: 'SimSun',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        hQD2Ny: {
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        tpbOV1: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IQZ5WD: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IX_7Xq: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        'DarG-n': {
          ff: 'SimSun',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        KKltN5: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        UzxRZI: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        'YDsxm-': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        poXCWb: {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        ZdSRmo: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        _jFe7t: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              1: {
                s: 'KKltN5',
                v: '',
                p: '',
              },
              2: {
                v: '',
                p: '',
              },
              3: {
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              5: {
                v: '',
                p: '',
              },
              6: {
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              8: {
                v: '',
                p: '',
              },
              9: {
                v: '',
                p: '',
              },
              10: {
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`招标人供应材料、设备明细表`',
                t: 1,
                s: 'poXCWb',
                p: '',
                custom: {},
              },
              1: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              2: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              3: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              4: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              5: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              6: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              7: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              8: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              9: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              10: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'tpbOV1',
                p: '',
                custom: {},
              },
              1: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              2: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              3: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              4: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              5: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              6: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              7: {
                v: '`第`+{页码}+`页 `+` 共 `+{总页数}+`页`',
                t: 1,
                s: 'IQZ5WD',
                p: '',
                custom: {},
              },
              8: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              9: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              10: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`规格型号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`单位`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`数量`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`单价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`合价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`质量等级`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`供应时间`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`送达地点`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '`备注`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            4: {
              0: {
                v: '`1`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`材料`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: '_jFe7t',
                p: '',
              },
            },
            5: {
              0: {
                v: '`1.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '[JGSL] * [SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '[ZLDJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            6: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: 'SUM([JGSL] * [SCJ])',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            7: {
              0: {
                v: '`2`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                p: '',
                v: '`设备`',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            8: {
              0: {
                v: '`2.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '[JGSL] * [SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '[ZLDJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            9: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: 'SUM([JGSL] * [SCJ])',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            10: {
              0: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              1: {
                s: 'xMxJ7h',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
            11: {
              0: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              1: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 11,
          columnCount: 11,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 4,
              endRow: 0,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 7,
              endRow: 0,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 0,
              endRow: 10,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 4,
              endRow: 10,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 7,
              endRow: 10,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 7,
              endRow: 2,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ia: 0,
            },
            3: {
              h: 47,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '人材机汇总/甲供/材料',
            },
            4: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            6: {
              h: 30,
              hd: 0,
              field: 'footNote',
              rowType: '明细脚注行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            7: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
              ah: 30,
            },
            8: {
              hd: 0,
              h: 30,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/设备',
              ah: 30,
            },
            9: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'footNote',
              rowType: '脚注行',
              dataSourceType: '人材机汇总/甲供/设备',
            },
            10: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 47,
              hd: 0,
            },
            1: {
              w: 160,
              hd: 0,
            },
            2: {
              w: 100,
              hd: 0,
            },
            3: {
              w: 64,
              hd: 0,
            },
            4: {
              w: 73,
              hd: 0,
            },
            5: {
              w: 67,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 75,
              hd: 0,
            },
            8: {
              w: 76,
              hd: 0,
            },
            9: {
              w: 76,
              hd: 0,
            },
            10: {
              w: 76,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
    },
  },
  {
    name: '表1-14 招标人供应材料、设备明细表',
    deType: [12, 22], //12.22都展示
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['工程量清单报表'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        EVf0W5: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        JYIwIz: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        dkrCNC: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        '6PNgad': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        Zx6VTU: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        jHudht: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        p6wfsK: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        KVtwJh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        AdyBgj: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        teJ78k: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        't-wFRB': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        wkPdoP: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        FTs3FH: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        Bn6F8s: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        zrIUeQ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        VcI9T8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        qONrQE: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        AAjndM: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        h48flX: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        ImISuO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        e3Wtvh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        kqWtAA: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        xMxJ7h: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rktHys: {
          bd: {
            t: null,
            l: null,
          },
        },
        RnYN3F: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        y6kLGr: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        dKsCGo: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        wDsUnc: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        mnflxa: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '9xuMxI': {
          ff: 'SimSun',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        hQD2Ny: {
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        tpbOV1: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IQZ5WD: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IX_7Xq: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        'DarG-n': {
          ff: 'SimSun',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        KKltN5: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        UzxRZI: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        'YDsxm-': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        poXCWb: {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        ZdSRmo: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        _jFe7t: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              1: {
                s: 'KKltN5',
                v: '',
                p: '',
              },
              2: {
                v: '',
                p: '',
              },
              3: {
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              5: {
                v: '',
                p: '',
              },
              6: {
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              8: {
                v: '',
                p: '',
              },
              9: {
                v: '',
                p: '',
              },
              10: {
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`招标人供应材料、设备明细表`',
                t: 1,
                s: 'poXCWb',
                p: '',
                custom: {},
              },
              1: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              2: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              3: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              4: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              5: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              6: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              7: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              8: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              9: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              10: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'tpbOV1',
                p: '',
                custom: {},
              },
              1: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              2: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              3: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              4: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              5: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              6: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              7: {
                v: '`第`+{页码}+`页 `+` 共 `+{总页数}+`页`',
                t: 1,
                s: 'IQZ5WD',
                p: '',
                custom: {},
              },
              8: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              9: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              10: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`规格型号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`单位`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`数量`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`单价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`合价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`质量等级`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`供应时间`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`送达地点`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '`备注`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            4: {
              0: {
                v: '`1`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`材料`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: '_jFe7t',
                p: '',
              },
            },
            5: {
              0: {
                v: '`1.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                s: 'ZdSRmo',
                v: '',
                p: '',
              },
              7: {
                s: 'ZdSRmo',
                v: '',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            6: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                s: 'ZdSRmo',
                v: '',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            7: {
              0: {
                v: '`2`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                p: '',
                v: '`设备`',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            8: {
              0: {
                v: '`2.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                s: 'ZdSRmo',
                v: '',
                p: '',
              },
              7: {
                s: 'ZdSRmo',
                v: '',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            9: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                s: 'ZdSRmo',
                v: '',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            10: {
              0: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              1: {
                s: 'xMxJ7h',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
            11: {
              0: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              1: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 11,
          columnCount: 11,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 4,
              endRow: 0,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 7,
              endRow: 0,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 0,
              endRow: 10,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 4,
              endRow: 10,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 7,
              endRow: 10,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 7,
              endRow: 2,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ia: 0,
            },
            3: {
              h: 47,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '人材机汇总/甲供/材料',
            },
            4: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            6: {
              h: 30,
              hd: 0,
              field: 'footNote',
              rowType: '明细脚注行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            7: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
              ah: 30,
            },
            8: {
              hd: 0,
              h: 30,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/设备',
              ah: 30,
            },
            9: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'footNote',
              rowType: '脚注行',
              dataSourceType: '人材机汇总/甲供/设备',
            },
            10: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 47,
              hd: 0,
            },
            1: {
              w: 160,
              hd: 0,
            },
            2: {
              w: 100,
              hd: 0,
            },
            3: {
              w: 64,
              hd: 0,
            },
            4: {
              w: 73,
              hd: 0,
            },
            5: {
              w: 67,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 75,
              hd: 0,
            },
            8: {
              w: 76,
              hd: 0,
            },
            9: {
              w: 76,
              hd: 0,
            },
            10: {
              w: 76,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
    },
  },
  {
    name: '表1-14 招标人供应材料、设备明细表',
    deType: [22], //12.22都展示
    taxMade: '1', //计税方式   1-一般计税  0-简易计税
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表', '其他'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        EVf0W5: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        JYIwIz: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        dkrCNC: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        '6PNgad': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        Zx6VTU: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        jHudht: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        p6wfsK: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        KVtwJh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        AdyBgj: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        teJ78k: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        't-wFRB': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        wkPdoP: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        FTs3FH: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        Bn6F8s: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        zrIUeQ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        VcI9T8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        qONrQE: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        AAjndM: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        h48flX: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        ImISuO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        e3Wtvh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        kqWtAA: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        xMxJ7h: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rktHys: {
          bd: {
            t: null,
            l: null,
          },
        },
        RnYN3F: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        y6kLGr: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        dKsCGo: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        wDsUnc: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        mnflxa: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '9xuMxI': {
          ff: 'SimSun',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        hQD2Ny: {
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        tpbOV1: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IQZ5WD: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IX_7Xq: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        'DarG-n': {
          ff: 'SimSun',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        KKltN5: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        UzxRZI: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        'YDsxm-': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        poXCWb: {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        ZdSRmo: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        _jFe7t: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              1: {
                s: 'KKltN5',
                v: '',
                p: '',
              },
              2: {
                v: '',
                p: '',
              },
              3: {
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              5: {
                v: '',
                p: '',
              },
              6: {
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              8: {
                v: '',
                p: '',
              },
              9: {
                v: '',
                p: '',
              },
              10: {
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`招标人供应材料、设备明细表`',
                t: 1,
                s: 'poXCWb',
                p: '',
                custom: {},
              },
              1: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              2: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              3: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              4: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              5: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              6: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              7: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              8: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              9: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              10: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'tpbOV1',
                p: '',
                custom: {},
              },
              1: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              2: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              3: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              4: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              5: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              6: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              7: {
                v: '`第`+{页码}+`页 `+` 共 `+{总页数}+`页`',
                t: 1,
                s: 'IQZ5WD',
                p: '',
                custom: {},
              },
              8: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              9: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              10: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`规格型号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`单位`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`数量`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`单价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`合价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`质量等级`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`供应时间`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`送达地点`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '`备注`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            4: {
              0: {
                v: '`1`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`材料`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: '_jFe7t',
                p: '',
              },
            },
            5: {
              0: {
                v: '`1.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '[JGSL] * [SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '[ZLDJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            6: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: 'SUM([JGSL] * [SCJ])',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            7: {
              0: {
                v: '`2`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                p: '',
                v: '`设备`',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            8: {
              0: {
                v: '`2.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '[JGSL] * [SCJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '[ZLDJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            9: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: 'SUM([JGSL] * [SCJ])',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            10: {
              0: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              1: {
                s: 'xMxJ7h',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
            11: {
              0: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              1: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 11,
          columnCount: 11,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 4,
              endRow: 0,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 7,
              endRow: 0,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 0,
              endRow: 10,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 4,
              endRow: 10,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 7,
              endRow: 10,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 7,
              endRow: 2,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
            },
            3: {
              h: 47,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '人材机汇总/甲供/材料',
            },
            4: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            6: {
              h: 30,
              hd: 0,
              field: 'footNote',
              rowType: '明细脚注行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            7: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
              ah: 30,
            },
            8: {
              hd: 0,
              h: 30,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/设备',
              ah: 30,
            },
            9: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'footNote',
              rowType: '脚注行',
              dataSourceType: '人材机汇总/甲供/设备',
            },
            10: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 47,
              hd: 0,
            },
            1: {
              w: 160,
              hd: 0,
            },
            2: {
              w: 100,
              hd: 0,
            },
            3: {
              w: 64,
              hd: 0,
            },
            4: {
              w: 73,
              hd: 0,
            },
            5: {
              w: 67,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 75,
              hd: 0,
            },
            8: {
              w: 76,
              hd: 0,
            },
            9: {
              w: 76,
              hd: 0,
            },
            10: {
              w: 76,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
    },
  },
  {
    name: '表1-14 招标人供应材料、设备明细表',
    deType: [22], //12.22都展示
    taxMade: '0', //计税方式   1-一般计税  0-简易计税
    levelType: [3], // 1工程，2 单项，3，单位
    lanMuName: ['招标项目报表', '投标项目报表', '其他'], // 招标项目报表, 投标项目报表,工程量清单报表,其他
    data: {
      id: 'workbook-01',
      sheetOrder: ['sheet1'],
      name: 'universheet',
      appVersion: '0.2.9',
      locale: 'zhCN',
      styles: {
        EVf0W5: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        JYIwIz: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
          },
        },
        dkrCNC: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        '6PNgad': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        Zx6VTU: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        jHudht: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        p6wfsK: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
          },
        },
        KVtwJh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        AdyBgj: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            t: null,
          },
        },
        teJ78k: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
            t: null,
          },
        },
        't-wFRB': {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            t: null,
          },
        },
        wkPdoP: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            t: null,
          },
        },
        FTs3FH: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            t: null,
          },
        },
        Bn6F8s: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        zrIUeQ: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
          },
        },
        VcI9T8: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            b: null,
            t: null,
          },
        },
        qONrQE: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        AAjndM: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
          },
        },
        h48flX: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
            l: null,
          },
        },
        ImISuO: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            b: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            r: null,
          },
        },
        e3Wtvh: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            t: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            l: null,
            b: null,
          },
        },
        kqWtAA: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            l: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 8,
            },
            r: {
              cl: {
                rgb: 'rgb(0,0,0)',
              },
              s: 1,
            },
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
            b: null,
            t: null,
          },
        },
        xMxJ7h: {
          ff: '微软雅黑 Light',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            b: null,
            l: null,
            tl_br: null,
            tl_bc: null,
            tl_mr: null,
            bl_tr: null,
            ml_tr: null,
            bc_tr: null,
          },
        },
        rktHys: {
          bd: {
            t: null,
            l: null,
          },
        },
        RnYN3F: {
          ff: '宋体',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        y6kLGr: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        dKsCGo: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        wDsUnc: {
          ff: '宋体',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        mnflxa: {
          ff: '等线',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        '9xuMxI': {
          ff: 'SimSun',
          fs: 11,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        hQD2Ny: {
          ff: 'SimSun',
          fs: 18,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        tpbOV1: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 1,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IQZ5WD: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 3,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            b: null,
          },
        },
        IX_7Xq: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: null,
            l: null,
            r: null,
            b: null,
          },
        },
        'DarG-n': {
          ff: 'SimSun',
          fs: 12,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        KKltN5: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(208,208,208)',
          },
          ht: 2,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        UzxRZI: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        'YDsxm-': {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: null,
            l: null,
            b: null,
            t: null,
          },
        },
        poXCWb: {
          ff: 'SimSun',
          fs: 16,
          it: 0,
          bl: 1,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {},
        },
        ZdSRmo: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          bg: {
            rgb: 'rgb(255,255,255)',
          },
          ht: 2,
          vt: 2,
          tb: 3,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
        _jFe7t: {
          ff: 'SimSun',
          fs: 10,
          it: 0,
          bl: 0,
          ul: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          st: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          ol: {
            s: 0,
            cl: {
              rgb: 'rgb(0,0,0)',
            },
          },
          tr: {
            a: 0,
            v: 0,
          },
          td: 0,
          cl: {
            rgb: 'rgb(0,0,0)',
          },
          ht: 0,
          vt: 2,
          tb: 1,
          pd: {
            t: 0,
            b: 1,
            l: 2,
            r: 2,
          },
          bd: {
            r: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            l: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            b: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
            t: {
              s: 1,
              cl: {
                rgb: '#000000',
              },
            },
          },
        },
      },
      sheets: {
        sheet1: {
          id: 'sheet1',
          cellData: {
            0: {
              0: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              1: {
                s: 'KKltN5',
                v: '',
                p: '',
              },
              2: {
                v: '',
                p: '',
              },
              3: {
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              5: {
                v: '',
                p: '',
              },
              6: {
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'KKltN5',
                p: '',
              },
              8: {
                v: '',
                p: '',
              },
              9: {
                v: '',
                p: '',
              },
              10: {
                v: '',
                p: '',
              },
            },
            1: {
              0: {
                v: '`招标人供应材料、设备明细表`',
                t: 1,
                s: 'poXCWb',
                p: '',
                custom: {},
              },
              1: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              2: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              3: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              4: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              5: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              6: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              7: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              8: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              9: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
              10: {
                s: 'poXCWb',
                v: '',
                p: '',
              },
            },
            2: {
              0: {
                v: '`工程名称:`+{单项名称}+{单位名称}',
                t: 1,
                s: 'tpbOV1',
                p: '',
                custom: {},
              },
              1: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              2: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              3: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              4: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              5: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              6: {
                s: 'tpbOV1',
                v: '',
                p: '',
              },
              7: {
                v: '`第`+{页码}+`页 `+` 共 `+{总页数}+`页`',
                t: 1,
                s: 'IQZ5WD',
                p: '',
                custom: {},
              },
              8: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              9: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
              10: {
                s: 'IQZ5WD',
                v: '',
                p: '',
              },
            },
            3: {
              0: {
                v: '`序号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              1: {
                v: '`名称`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`规格型号`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`单位`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`数量`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`单价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`合价(元)`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`质量等级`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`供应时间`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`送达地点`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '`备注`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            4: {
              0: {
                v: '`1`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`材料`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: '_jFe7t',
                p: '',
              },
            },
            5: {
              0: {
                v: '`1.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ_HS]',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
                p: '',
              },
              6: {
                v: '[JGSL] * [SCJ_HS]',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
                p: '',
              },
              7: {
                v: '[ZLDJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            6: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: 'SUM([JGSL] * [SCJ_HS])',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            7: {
              0: {
                v: '`2`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                p: '',
                v: '`设备`',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
                custom: {},
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            8: {
              0: {
                v: '`2.`+[记录号]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '[MC]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '[GGXH]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '[DW]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '[JGSL]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '[SCJ_HS]',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
                p: '',
              },
              6: {
                v: '[JGSL] * [SCJ_HS]',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
                p: '',
              },
              7: {
                v: '[ZLDJ]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '[SDDD]',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            9: {
              0: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              1: {
                v: '`小计`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              2: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              3: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              4: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              5: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              6: {
                v: 'SUM([JGSL] * [SCJ_HS])',
                t: 1,
                s: 'ZdSRmo',
                custom: {},
                p: '',
              },
              7: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              8: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              9: {
                v: '`/`',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
              10: {
                v: '',
                t: 1,
                s: 'ZdSRmo',
                p: '',
              },
            },
            10: {
              0: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              1: {
                s: 'xMxJ7h',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                v: '',
                t: 1,
                s: 'xMxJ7h',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
            11: {
              0: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              1: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              2: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              3: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              4: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              5: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              6: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              7: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              8: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              9: {
                s: 'rktHys',
                v: '',
                p: '',
              },
              10: {
                s: 'rktHys',
                v: '',
                p: '',
              },
            },
          },
          name: 'Sheet1',
          hidden: 0,
          rowCount: 11,
          columnCount: 11,
          tabColor: '',
          zoomRatio: 1,
          freeze: {
            startRow: -1,
            startColumn: -1,
            ySplit: 0,
            xSplit: 0,
          },
          scrollTop: 0,
          scrollLeft: 0,
          defaultColumnWidth: 88,
          defaultRowHeight: 30,
          mergeData: [
            {
              startRow: 0,
              startColumn: 0,
              endRow: 0,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 4,
              endRow: 0,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 0,
              startColumn: 7,
              endRow: 0,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 0,
              endRow: 10,
              endColumn: 3,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 4,
              endRow: 10,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 10,
              startColumn: 7,
              endRow: 10,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 1,
              startColumn: 0,
              endRow: 1,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 0,
              endRow: 2,
              endColumn: 6,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
            {
              startRow: 2,
              startColumn: 7,
              endRow: 2,
              endColumn: 10,
              rangeType: 0,
              unitId: 'workbook-01',
              sheetId: 'sheet1',
            },
          ],
          rowData: {
            0: {
              h: 30,
              hd: 0,
              ah: 30,
              field: 'pageEyeBrow',
              rowType: '页眉行',
            },
            1: {
              h: 50,
              hd: 0,
              ia: 0,
              field: 'header',
              rowType: '报表标题行',
            },
            2: {
              h: 47,
              hd: 0,
              field: 'sheetEyeBrow',
              rowType: '表眉行',
              ia: 0,
            },
            3: {
              h: 47,
              field: 'headLine',
              rowType: '明细标题行',
              dataSourceType: '人材机汇总/甲供/材料',
            },
            4: {
              h: 30,
              hd: 0,
              ah: 30,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
            },
            5: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            6: {
              h: 30,
              hd: 0,
              field: 'footNote',
              rowType: '明细脚注行',
              dataSourceType: '人材机汇总/甲供/材料',
              ah: 30,
            },
            7: {
              h: 30,
              hd: 0,
              field: '',
              rowType: '明细标题行',
              dataSourceType: '',
              ah: 30,
            },
            8: {
              hd: 0,
              h: 30,
              field: '',
              rowType: '细节行',
              dataSourceType: '人材机汇总/甲供/设备',
              ah: 30,
            },
            9: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'footNote',
              rowType: '脚注行',
              dataSourceType: '人材机汇总/甲供/设备',
            },
            10: {
              hd: 0,
              h: 30,
              ah: 30,
              field: 'pageFoot',
              rowType: '页脚行',
            },
          },
          columnData: {
            0: {
              w: 47,
              hd: 0,
            },
            1: {
              w: 160,
              hd: 0,
            },
            2: {
              w: 100,
              hd: 0,
            },
            3: {
              w: 64,
              hd: 0,
            },
            4: {
              w: 73,
              hd: 0,
            },
            5: {
              w: 67,
              hd: 0,
            },
            6: {
              w: 88,
              hd: 0,
            },
            7: {
              w: 75,
              hd: 0,
            },
            8: {
              w: 76,
              hd: 0,
            },
            9: {
              w: 76,
              hd: 0,
            },
            10: {
              w: 76,
              hd: 0,
            },
          },
          showGridlines: 1,
          rowHeader: {
            width: 46,
            hidden: 0,
          },
          columnHeader: {
            height: 20,
            hidden: 0,
          },
          selections: ['A1'],
          rightToLeft: 0,
        },
      },
      headLine: '',
    },
  },
];
