<!--
 * @Descripttion: 重点项过滤
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-18 19:16:28
 * @LastEditors: liuxia
 * @LastEditTime: 2024-07-02 16:01:07
-->
<template>
  <common-modal
    v-model:modelValue="show"
    className="dialog-comm"
    :title="title"
    :width="800"
    height="auto"
    @close="close"
  >
    <div class="kif-table">
      <vxe-table
        border
        ref="childTable"
        keep-source
        height="350"
        :column-config="{ resizable: true }"
        :scroll-y="{ enabled: false }"
        :span-method="mergeRowMethod"
        :data="tableData"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
        }"
      >
        <vxe-column field="typeName" title=""> </vxe-column>
        <vxe-column field="name" title="条件名称">
          <template #default="{ row }">
            <span :style="{ color: row.number ? '#f29d5c' : '' }">{{
              row.name
            }}</span>
          </template>
        </vxe-column>
        <vxe-column field="value" title="过滤依据" :edit-render="{}">
          <template #default="{ row }">
            <span>{{ formatRole(row) }}</span>
          </template>
          <template #edit="{ row }">
            <vxe-select v-model="row.value" transfer>
              <vxe-option
                v-for="item in option"
                :key="item.value"
                :value="item.value"
                :label="item.key"
              ></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
        <vxe-column
          field="number"
          title="比较值"
          :edit-render="{ autofocus: '.vxe-textarea--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model.trim="row.number"
              type="text"
              @blur="inputBlur(row), clear()"
            >
            </vxe-input>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <div class="footer">
      <a-button type="link" @click="clearFilterConditions"
        >清除过滤条件</a-button
      >
      <div>
        <a-button type="primary" :loading="btnLoading" @click="saveData"
          >确定</a-button
        >
        <a-button @click="close">取消</a-button>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch, nextTick, toRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import shApi from '@/api/shApi';
import { useCellClick } from '@/hooks/useCellClick';
import { message, Modal } from 'ant-design-vue';
import operateList from '@/views/projectDetail/customize/operate';

import { pureNumber, isNumericExpression } from '@/utils/index';
// import { VxeTablePropTypes } from 'vxe-table'
let $table; //全局定义
const clear = () => {
  //清除编辑状态
  $table.clearEdit();
};
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const store = projectDetailStore();
const btnLoading = ref(false);
const childTable = ref();
const tableData = ref([]);
const option = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
  },
  title: {
    type: String,
  },
});
const emit = defineEmits(['update:visible']);
const show = computed({
  get: () => props.visible,
  set: val => {
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      init();
      nextTick(() => {
        $table = childTable.value;
      });
    }
  }
);
const init = () => {
  getDefaultFiltrationData();
  getUnitFiltrationData();
};
const formatRole = row => {
  if (!row.value) {
    return;
  }
  const item = option.value.find(item => item.value === row.value);
  return item ? item.key : '';
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
};
const getUnitFiltrationData = val => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  shApi.getUnitZdxglData(apiData).then(res => {
    console.log('getUnitZdxglData', res);
    if (res.status === 200) {
      if (!val && res.result && res.result.length > 0) {
        for (const item of res.result) {
          for (const item1 of tableData.value) {
            if (
              item.filtration === item1.filtration &&
              item.type === item1.type
            ) {
              item1.value = item.value;
              item1.number = item.number;
            }
          }
        }
      }
      for (const item of operateList.value) {
        if (item.name === 'key-item-filtering') {
          if (res.result && res.result.length > 0) {
            item.badgeDot = true;
            item.labelStyle = { color: 'red' };
          } else {
            item.badgeDot = false;
            item.labelStyle = {};
          }
        }
      }
    }
  });
};
const getDefaultFiltrationData = () => {
  shApi.getDefaultZdxglData().then(res => {
    if (res.status === 200 && res.result) {
      option.value = res.result.option;
      tableData.value = res.result.data;
      console.log(res.result, 'res.result');
      for (const item of tableData.value) {
        item.value = '>=';
      }
    }
  });
};
const close = () => {
  show.value = false;
  getUnitFiltrationData(true);
};
const mergeRowMethod = ({ row, _rowIndex, column, visibleData }) => {
  const fields = ['typeName'];
  const cellValue = row[column.field];
  if (cellValue && fields.includes(column.field)) {
    const prevRow = visibleData[_rowIndex - 1];
    let nextRow = visibleData[_rowIndex + 1];
    if (prevRow && prevRow[column.field] === cellValue) {
      return { rowspan: 0, colspan: 0 };
    } else {
      let countRowspan = 1;
      while (nextRow && nextRow[column.field] === cellValue) {
        nextRow = visibleData[++countRowspan + _rowIndex];
      }
      if (countRowspan > 1) {
        return { rowspan: countRowspan, colspan: 1 };
      }
    }
  }
};
const refreshParentList = () => {
  if (store.componentId === 'subItemProject') {
    store.subItemProjectAutoPosition?.posRow('', 'Refresh');
  }
  if (store.componentId === 'measuresItem') {
    store.measuresItemProjectAutoPosition?.posRow('', 'Refresh');
  }
};
const saveData = () => {
  btnLoading.value = true;
  let list = tableData.value.filter(item => item.number);
  let apiData = {
    zdxgls: JSON.parse(JSON.stringify(list)),
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  shApi
    .updateUnitZdxglData(apiData)
    .then(res => {
      if (res.status === 200) {
        message.success(res.message);
        refreshParentList();
        close();
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
const clearFilterConditions = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
  };
  shApi.deleteUnitZdxglData(apiData).then(res => {
    if (res.status === 200) {
      init();
      refreshParentList();
      message.success(res.message);
    }
  });
};
const inputBlur = val => {
  if (val.number && !/^([1-9]\d*|0)(\.\d+)?$/.test(val.number)) {
    Modal.warning({
      title: '输入不合法，请输入数字！',
      content: '',
      okText: '确定',
    });
    val.number = '';
  }
};
</script>
<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: space-between;
  position: relative;
  top: 15px;
  :deep(.ant-btn) {
    margin-right: 15px;
  }
}
</style>
