/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON>qiang
 * @Date: 2024-07-03 11:24:30
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-07-10 11:07:13
 */
import { ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const tableColumns = [
  {
    title: '序号',
    field: 'dispNo',
  },
  {
    title: '编码',
    field: 'materialCode',
  },
  {
    field: 'type',
    title: '类别',
  },
  {
    field: 'materialName',
    title: '名称',
  },
  {
    field: 'specification',
    title: '规格型号',
  },
  {
    field: 'unit',
    title: '单位',
  },
  {
    field: 'totalNumber',
    title: '数量',
  },
  {
    field: 'baseJournalPrice',
    title: '不含税基期价',
  },
  {
    field: 'baseJournalTaxPrice',
    title: '含税基期价',
  },
  {
    field: 'marketPrice',
    title: '不含税市场价',
  },
  {
    field: 'marketTaxPrice',
    title: '含税市场价',
  },
  {
    field: 'total',
    title: '不含税市场价合计',
  },
  {
    field: 'totalTax',
    title: '含税市场价合计',
  },
  {
    field: 'taxRate',
    title: '税率',
  },
  {
    field: 'sourcePrice',
    title: '价格来源',
  },
  {
    field: 'priceDifferenc',
    title: '价差',
  },
  {
    field: 'priceDifferencSum',
    title: '价差合计',
  },
  {
    field: 'ifDonorMaterial',
    title: '供应方式',
  },
  {
    field: 'donorMaterialPrice',
    title: '甲供价',
    visible: false,
  },
  {
    field: 'donorMaterialNumber',
    title: '甲供数量',
  },
  {
    field: 'kindSc',
    title: '三材类别',
    visible: false,
  },
  {
    field: 'transferFactor',
    title: '三材系数',
    visible: false,
  },
  {
    field: 'ifProvisionalEstimate',
    title: '是否暂估',
  },
  {
    field: 'ifLockStandardPrice',
    title: '市场价锁定',
  },
  {
    field: 'producer',
    title: '产地',
  },
  {
    field: 'manufactor',
    title: '厂家',
  },
  {
    field: 'brand',
    title: '品牌',
  },
  {
    field: 'deliveryLocation',
    title: '送达地点',
  },
  {
    field: 'qualityGrade',
    title: '质量等级',
  },
  {
    field: 'supplyTime',
    title: '供应时间',
    visible: false,
  },
  {
    field: 'remark',
    title: '备注',
    visible: false,
  },
];

let Columns = []
if(projectStore.taxMade == 1){
  // 含税相关的隐藏,baseJournalTaxPrice，totalTax
  Columns = tableColumns.filter(item => !['totalTax', 'baseJournalTaxPrice'].includes(item.field));
}else{
  // 不含税相关的隐藏baseJournalPrice，total
  Columns = tableColumns.filter(item => !['total', 'baseJournalPrice'].includes(item.field));
}

export default Columns;
