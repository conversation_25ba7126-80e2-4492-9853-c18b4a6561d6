<!--
 * @Descripttion: 换算信息
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52
 * @LastEditors: sunchen
 * @LastEditTime: 2025-02-07 10:52:55
-->
<template>
  <div class="head">
    <a-button
      type="text"
      @click="updateDeConversionInfo('up')"
      :disabled="
        !currentInfo || Number(currentInfo?.sortNumber.split('.')[0]) === 1 || [6].includes(currentInfo?.kind)
      "
      ><icon-font type="icon-biaodan-charu"></icon-font>上移</a-button
    >

    <a-button
      type="text"
      @click="updateDeConversionInfo('down')"
      :disabled="
        !currentInfo ||
        currentInfo?.sortNumber.indexOf('.') > -1 ||
        currentInfo?.sortNumber == props.tableData.length
        || [6].includes(currentInfo?.kind)
      "
      ><icon-font type="icon-biaodan-charu"></icon-font>下移</a-button
    >
    <a-button
      type="text"
      @click="updateDeConversionInfo('delete')"
      :disabled="!currentInfo || [6].includes(currentInfo?.kind)"
      ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button
    >
  </div>
  <div class="content">
    <vxe-table
      ref="vexTable"
      :data="props.tableData"
      height="auto"
      class="table-scrollbar"
      :tree-config="{
        children: 'children',
        expandAll: true,
      }"
      :row-config="{ isCurrent: true, keyField: 'sortNumber' }"
      @current-change="currentChangeEvent"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData, null);
        }
      "
    >
      <vxe-column title="序号" width="60" field="sortNumber"> </vxe-column>
      <vxe-column
        title="换算串"
        field="conversionString"
        tree-node
      ></vxe-column>
      <vxe-column title="说明" field="conversionExplain"></vxe-column>
      <vxe-column title="来源" field="source"></vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import api from '@/gongLiaoJiProject/api/projectDetail';
import { useCellClick } from '@gongLiaoJi/hooks/useCellClick.js';
import { projectDetailStore } from '@/store/projectDetail.js';
const { useCellClickEvent } = useCellClick();

const props = defineProps(['tableData', 'currentInfo']);
const emits = defineEmits(['updateData']);
const projectStore = projectDetailStore();
const currentInfo = ref(null);
let vexTable = ref(null);

watch(
  () => props.tableData,
  (newVal, oldVal) => {
    if (newVal) {
      if (currentInfo.value) {
        currentInfo.value = newVal.find(
          item => item.sequenceNbr === currentInfo.value.sequenceNbr
        );
        vexTable.value?.setCurrentRow(currentInfo.value);
      }
    }
  }
);

// 选中单条分部分项数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};

const updateDeConversionInfo = operate => {
  let apiData = {
    fbFxDeId: props.currentInfo?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    selectId: currentInfo.value.sequenceNbr,
    operateAction: operate,
  };
  console.log('apiData', apiData);
  api.updateDeConversionInfo(apiData).then(res => {
    console.log('res11111', res);
    if (res.status === 200 && res.result) {
      emits('updateData', operate == 'delete' ? 1 : '');
    }
  });
};
defineExpose({
  vexTable,
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: calc(100% - 40px);
}
</style>
