<!--
 * @Descripttion: 标准组价
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2024-05-31 11:12:30
-->
<template>

  <common-modal
    className="dialog-comm ReuseGroupPrice-dialog"
    @close="cancel"
    v-model:modelValue="dialogVisible"
    title="标准组价"
    width="750px"
    height="500px"
    max-width="900px"
    max-height="900px"
    min-height="460px"
    :mask="false"
    show-zoom
    resize
    :loadingModal="loadingModal"
  >
    <div class="group-content-wrap">
      <div class="group-title-box">
        <div class="range-title">
          <div>选择所要组价的单位工程：</div>
          <div class="right-box">
            <a-checkbox v-model:checked="selectValues.bringAll">带入全部单位工程组价</a-checkbox>
          </div>
        </div>
      </div>
      <splitpanes>
        <pane>
          <div class="content content-table">
            <vxe-table
              border
              ref="vexTable"
              align="center"
              :column-config="{ resizable: true }"
              :data="TableData"
              :checkStrictly="false"
              :row-class-name="rowClassName"
              height="98%"
              :row-config="{
                isCurrent: true,
                keyField: 'sequenceNbr',
              }"
              :checkbox-config="checkboxConfig"
              :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 25 }"
              :tree-config="{
                transform: false,
                rowField: 'sequenceNbr',
                parentField: 'parentId',
                line: true,
                showIcon: true,
                expandAll: true,
                iconOpen: 'vxe-icon-caret-down',
                iconClose: 'vxe-icon-caret-right',
              }"
              @checkbox-change="selectChangeEvent"
              show-overflow
              @cell-click="cellClick"
              @current-change="currentRowChange"
            >
              <vxe-column
                align="center"
                type="checkbox"
                min-width="50"
                title="选择"
              ></vxe-column>
              <vxe-column
                type="seq"
                min-width="40"
                title="序号"
              />
              <vxe-column
                field="name"
                tree-node
                min-width="150"
                title="名称"
              >
              </vxe-column>
              <vxe-column
                field="isBring"
                min-width="60"
                title="带入组价"
                :cell-render="{}"
              >
                <template #default="{ row, rowIndex }">
                  <vxe-checkbox
                    v-model="row.isBring"
                    name="带入组价"
                    @change="bringChange(row)"
                    v-if="row.levelType===3"
                  ></vxe-checkbox>
                </template>
              </vxe-column>
              <vxe-column
                field="constructMajorType"
                min-width="100"
                title="清单专业"
              >
              </vxe-column>
              <vxe-column
                field="secondInstallationProjectName"
                min-width="100"
                title="定额专业"
              >
              </vxe-column>
            </vxe-table>
          </div>
        </pane>
        <pane
          size="25"
          min-size="15"
          max-size="25"
        >
          <div class="content selectContent">
            <div class="selectBox">
              <p class="title">清单合并规则</p>
              <a-checkbox-group
                v-model:value="selectValues.qdMergeVal"
                :options="qdMergeRules"
              />
            </div>
            <div class="selectBox">
              <p class="title">合并范围</p>
              <a-radio-group
                v-model:value="selectValues.qdRangeVal"
                :options="qdRange"
              />
            </div>
            <div></div>
          </div>
        </pane>
      </splitpanes>

      <div class="footer-box">
        <!-- <a-checkbox
          v-model:checked="selectValues.samePro"
          @change="sameSpecialtyUnit"
        >选择同专业工程</a-checkbox> -->
        <p>
          <a-button
            @click="sameSpecialtyUnit"
            style="margin:0 10px 0 0"
            :disabled="currentRow?.levelType!==3"
          >选择同专业工程</a-button>
          <a-button
            @click="cancelSpecialtyUnit"
            :disabled="currentRow?.levelType!==3"
          >取消同专业工程</a-button>
        </p>
        <p>
          <a-button @click="cancel">取消</a-button>
          <a-button
            type="primary"
            @click="sureFn"
          >确定</a-button>
        </p>
      </div>
      <!-- <a-progress
        :percent="percentInfo.percent"
        v-if="percentInfo.show"
        status="active"
      /> -->
    </div>
  </common-modal>
  <common-modal
    className="titleNoColor noHeader "
    title=" "
    width="500"
    height="200"
    v-model:modelValue="percentInfo.show"
    :mask="false"
  >
    <div class="progressDiv">
      <p>组价中，请稍后…</p>
      <p class="noClose">请勿关闭当前页面</p>
      <a-progress
        :percent="percentInfo.percent"
        class="baseProgess"
      />
    </div>
  </common-modal>
</template>
<script setup>
import {
  ref,
  toRaw,
  watchEffect,
  nextTick,
  reactive,
  computed,
  shallowRef,
  shallowReactive,
  defineExpose,
  getCurrentInstance,
} from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { Splitpanes, Pane } from 'splitpanes';
import { constructLevelTreeStructureList } from '@/api/csProject';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
// import { setGlobalLoading } from '@/hooks/publicApiData';
import 'splitpanes/dist/splitpanes.css';
import { message } from 'ant-design-vue';
const cxt = getCurrentInstance();
const $ipc = cxt.appContext.config.globalProperties.$ipc;
const props = defineProps({
  currentInfo: {
    type: Object,
    default: null,
  },
  type: {
    type: String,
    default: 'fbfx',
  },
  lockBtnStatus: {
    type: Boolean,
    default: false,
  },
});
const checkboxConfig = reactive({
  showHeader: false,
  visibleMethod({ row }) {
    return row.levelType === 3;
  },
});
let currentRow = ref();
const currentRowChange = ({ row }) => {
  //选中行的费用代码自动连接到输入框内
  currentRow.value = row;
};
const selectChangeEvent = ({ checked, row }) => {
  console.log('selectChangeEvent', checked, row);
  if (!checked) {
    row.isBring = false;
  }
};
const route = useRoute();
const emits = defineEmits(['closeDialog', 'refresh']);
let dialogVisible = ref(false);
const projectStore = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);
const selectValues = reactive({
  qdMergeVal: ['bdCode', 'name', 'projectAttr', 'unit'], //清单合并规则value
  qdRangeVal: 'all', //清单合并范围value
  bringAll: false, //是否带入全部单位工程
  // samePro: false, //是否选择同专业
});
//清单合并规则选择项
const qdMergeRules = [
  {
    label: '编码（前9位）',
    value: 'bdCode',
  },
  {
    label: '名称',
    value: 'name',
  },
  {
    label: '项目特征',
    value: 'projectAttr',
  },
  {
    label: '综合单价',
    value: 'price',
  },
  {
    label: '单位',
    value: 'unit',
  },
];
//清单合并范围选择项
const qdRange = [
  {
    label: '全部清单',
    value: 'all',
  },
  {
    label: '未组价清单',
    value: 'part',
  },
];
let loading = ref(false);
let percentInfo = shallowReactive({
  percent: 0,
  show: false,
});
const startPercent = () => {
  dialogVisible.value = false;
  // percentInfo.show = true;
  // percentInfo.percent = 0;
  // $ipc.on(store.currentTreeGroupInfo?.constructId, (event, arg) => {
  //   console.log('🚀 ~ $ipc.on ~ arg:', arg);
  //   if (arg.percent >= percentInfo.percent) {
  //     percentInfo.percent = arg.percent;
  //   }
  //   if (arg.percent >= 100) {
  //     setTimeout(() => {
  //       percentInfo.show = false;
  //       //进度条走完需要打开子工作台
  //     }, 500);
  //   }
  // });
  openChildPage();
};
let loadingModal = ref(false);
const openChildPage = () => {
  //暂时使用loading，进度条未联调
  // setGlobalLoading(true, '正在进行组价，请稍后...');
  loadingModal.value = true;
  let { id } = projectStore.currentTreeInfo;
  let oldcurrentTreeInfo = {
    ...JSON.parse(JSON.stringify(projectStore.currentTreeInfo)),
  };
  let oldcurrentTreeGroupInfo = {
    ...JSON.parse(JSON.stringify(projectStore.currentTreeGroupInfo)),
  };
  projectStore.SET_STANDARD_GROUP_OPEN_INFO({
    isOpen: true,
    info: props,
    selectProjectId: id,
    type: 'childPage', //子窗口
    modalTip: childModalTip.value,
    treeGroup: treeGroup.value,
    oldTreeInfo: {
      currentTreeInfo: oldcurrentTreeInfo,
      currentTreeGroupInfo: oldcurrentTreeGroupInfo,
    },
  });
  projectStore.SET_CURRENT_TREE_INFO({
    ...projectStore.currentTreeInfo,
    id: treeGroup.value.unitId,
    parentId: treeGroup.value.singleId,
  });
  projectStore.SET_CURRENT_TREE_GROUP_INFO({
    ...projectStore.currentTreeGroupInfo,
    constructId: treeGroup.value.constructId,
    singleId: treeGroup.value.singleId,
  });
  console.log(
    'openChildPage',
    projectStore.currentTreeInfo,
    projectStore.currentTreeGroupInfo,
    treeGroup.value
  );
  loadingModal.value = false;
  // setGlobalLoading(false, '正在进行组价，请稍后...');
};
const cancel = (refresh = false) => {
  if (refresh) {
    emits('refresh');
    return;
  }
  dialogVisible.value = false;
  emits('closeDialog');
};
let tableTreeList = ref([]);
const getTableList = async () => {
  const res = await constructLevelTreeStructureList(
    route.query.constructSequenceNbr
  );
  res.result.map(i => {
    i.isBring = false;
    if (i.levelType === 3 && i.constructMajorType) {
      unitIdList.value.push(i.id);
    }
  });
  console.log('🚀 ~ getTableList ~ postData:', res);
  tableTreeList.value = res.result;
  TableData.value = xeUtils.toArrayTree(res.result);
  nextTick(() => {
    setTimeout(() => {
      // vexTable.value.setAllRowExpand(true);
      vexTable.value.setTreeExpand(tableTreeList.value, true);
      let target = tableTreeList.value.find(i => i.levelType === 3);
      if (!target) target == tableTreeList.value[0];
      vexTable.value.setCurrentRow(target);
      currentRow.value = target;
    }, 0);
  });
};
// 选择同专业单位工程
let unitIdList = ref([]);
const cancelSpecialtyUnit = () => {
  // setCheckboxRow(rows, checked)
  // vexTable.value.clearCheckboxRow();
  const $table = vexTable.value;
  let list = $table.getCheckboxRecords(true);
  if (list.length === 0) return;
  let targetList = list.filter(
    i =>
      i.secondInstallationProjectName ===
      currentRow.value.secondInstallationProjectName
  );
  $table.setCheckboxRow(targetList, false);
};
const sameSpecialtyUnit = async () => {
  const $table = vexTable.value;
  let selectRow = $table.getCheckboxRecords(true);
  console.log('selectRow', selectRow);
  if (!selectRow || !selectRow.find(i => i.levelType === 3)) {
    message.info('请选择单位工程');
    // selectValues.samePro = false;
    return;
  }
  let selectMajorList = [];
  selectRow.forEach(item => {
    if (unitIdList.value.includes(item.id)) {
      selectMajorList.push(item.secondInstallationProjectName);
    }
  });
  console.log('selectMajorList', selectMajorList);
  tableTreeList.value.forEach(item => {
    if (selectMajorList.includes(item.secondInstallationProjectName)) {
      $table.setCheckboxRow(item, true);
    }
  });
};
const bringChange = row => {
  //带入组价选中
  console.log('🚀 ~ 带入组价~row', row);
  if (row.isBring) {
    vexTable.value.setCheckboxRow(row, true);
  }
};
const sureFn = () => {
  console.log('🚀 ~ 确定组价~selectValues', selectValues);
  const $table = vexTable.value;
  let selectRow = $table
    .getCheckboxRecords(true)
    .filter(i => i.levelType === 3);

  let hasNoSame = false;
  if (selectRow.length > 1) {
    console.log('🚀 ~ 确定组价~selectRow', selectRow);
    let target = selectRow.find(
      i =>
        i.secondInstallationProjectName !==
        selectRow[0].secondInstallationProjectName
    );
    hasNoSame = target ? true : false; //false-都是同专业   true 有不同专业
    hasNoSame &&
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: `单位工程${target.name}的定额专业与其他单位工程不同，无法提取`,
        confirm: () => {
          infoMode.hide();
        },
      });
  }
  if (hasNoSame) return;
  if (!selectRow || !selectRow.find(i => i.levelType === 3)) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: '请选择需要提取的单位工程',
      confirm: () => {
        infoMode.hide();
      },
    });
    return;
  }
  console.log('selectRow', selectRow);
  if (selectValues.qdMergeVal.length === 0) {
    message.info('请至少选择一条清单合并规则');
    return;
  }
  let unitIds = [];
  selectRow.forEach(item => {
    if (item.levelType === 3) {
      unitIds.push(item.id);
    }
  });
  groupQd(unitIds);
  // startPercent(); //开始进度条
};
let childModalTip = ref('');
let treeGroup = ref({});
const groupQd = unitIds => {
  projectStore.SET_GLOBAL_LOADING({
    loading: true,
    info: '设置中，请稍后...',
  });
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitIds: unitIds,
    rules: JSON.parse(JSON.stringify(selectValues.qdMergeVal)),
    range: selectValues.qdRangeVal,
  };
  console.log('apiData', apiData);
  api
    .groupQd(apiData)
    .then(res => {
      console.log('apiData', res);
      if (res.status === 200) {
        childModalTip.value = res.result.tip;
        treeGroup.value = {
          constructId: res.result.constructId,
          singleId: res.result.singleId,
          unitId: res.result.unitId,
        };
        startPercent(); //开始进度条
      }
    })
    .finally(() => {
      projectStore.SET_GLOBAL_LOADING({
        loading: false,
        info: '设置完成',
      });
    });
};
const open = k => {
  dialogVisible.value = true;
  percentInfo.show = false;
  loading.value = false;
  selectValues.qdMergeVal = ['bdCode', 'name', 'projectAttr', 'unit'];
  selectValues.qdRangeVal = 'all';
  selectValues.bringAll = false;
  // selectValues.samePro = false;
  unitIdList.value = [];
  getTableList();
};

const rowClassName = ({ row }) => {
  // if (row.parentId == 1) {
  //   return 'row-tree-title';
  // }
};

const cellClick = ({ row }) => {};

defineExpose({
  open,
});
</script>

<style lang="scss">
.ReuseGroupPrice-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .check-labels {
    white-space: nowrap;
  }
  .content-table {
    padding: 0 10px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .group-title-box {
    .range-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      margin: -5px 0 5px;
    }
  }
  .footer-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 7px !important;
    p {
      width: 160px;
      display: flex;
      justify-content: space-between;
      margin: 0;
    }
  }
  .splitpanes {
    flex: 1;
    overflow: auto;
    .content {
      width: 100%;
      height: 100%;
      overflow: auto;
      padding: 2px;
      border: 1px solid #b9b9b9;
    }
  }

  .splitpanes__splitter {
    min-width: 6px;
    border-radius: 4px;
    margin: 0 3px;
    background: #fff;
    &:hover {
      background-color: rgba(24, 144, 255, 0.8);
    }
  }

  .vxe-table {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -4px !important;
      left: 0px !important;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 0.5),
        rgba(185, 185, 185, 0.5)
      ),
      linear-gradient(rgba(185, 185, 185, 0.5), rgba(185, 185, 185, 0.5));
  }
  .vxe-table--body {
    border-collapse: collapse;
  }
  .selectContent {
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    border: none !important;
    .selectBox {
      width: 100%;
      height: 49%;
      border: 1px solid #b9b9b9;
      padding: 10px;
      .title {
        font-size: 14px;
        color: #287cfa;
        line-height: 17px;
        font-weight: 400;
      }
    }
    .selectBox:nth-of-type(1) {
      margin-bottom: 10%;
    }
  }
  .progressDiv {
    width: 90%;
    margin: auto;
    font-size: 14px;
    color: #000000;
    .noClose {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: #ff9000;
    }
  }
  .ant-checkbox-group-item {
    display: flex;
    margin-left: 5px;
  }
  .ant-checkbox + span,
  .ant-radio-wrapper {
    font-size: 12px;
    padding-right: 0px;
  }
  .spin-yyy {
    z-index: 99 !important;
    height: 100% !important;
    background-color: transparent !important;
    // :deep(.ant-spin) {
    //   height: 100% !important;
    .ant-spin-container {
      height: 100% !important;
      // }
    }
  }
}
</style>
