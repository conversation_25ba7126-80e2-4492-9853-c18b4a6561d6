const {BaseRcj2022} = require("../../models/BaseRcj2022");

const {ObjectUtil} = require("../../../../common/ObjectUtil");
const EE = require('../../../../core/ee');
const { NumberUtil } = require("../../../../common/NumberUtil");
const { Snowflake } = require('../../utils/Snowflake');
const ProjectDomain = require("../../domains/ProjectDomain");
const WildcardMap = require("../../core/container/WildcardMap");
const CommonConstants = require("../../constants/CommonConstants");
const DeCommonConstants = require("../../constants/DeCommonConstants");
const RcjTypeEnum = require('../../../../electron/enum/RcjTypeEnum');
const RcjCommonConstants = require('../../constants/RcjCommonConstants');


class ConversionService {
    constructor() {
        this.app = EE.app;
        this.service = this.app.service;
    }

    getCacheRcjAndCopyOld(constructId, unitId, materialCode){

        let cacheRcj = this.service.gongLiaoJiProject.gljRcjService.getRcjByCode({
            constructId, unitId, materialCode
        });

        if(ObjectUtil.isEmpty(cacheRcj)){
            let  rcjList = this.service.gongLiaoJiProject.gljRcjService.getUserRcj(constructId);
            cacheRcj = rcjList.find(item=>item.unitId == unitId && item.materialCode === materialCode );
        }

        let copyRcj = null;
        if(ObjectUtil.isNotEmpty(cacheRcj)){
            copyRcj = ObjectUtil.cloneDeep(cacheRcj);
            if(copyRcj.supplementRcjFlag != 1 && ObjectUtil.isEmpty(copyRcj.standardId)){
                copyRcj.standardId = copyRcj.sequenceNbr;
            }
            copyRcj.sequenceNbr = Snowflake.nextId();
        }
        return copyRcj;
    }

    async getCacheRcjAndCopy(constructId, unitId, materialCode, baseRcj){
        let cacheRcj = null;

        if(ObjectUtil.isNotEmpty(baseRcj)){
            let tmpRcj = {
                constructId,
                ...baseRcj
            };
            let memoryRcj = await this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(
                constructId, unitId
            );

            if(ObjectUtil.isNotEmpty(memoryRcj)){
                cacheRcj = await this.service.gongLiaoJiProject.gljRcjCollectService.findAlikeRcj(
                    memoryRcj, tmpRcj
                );
            }

            if(ObjectUtil.isEmpty(cacheRcj)){
                cacheRcj = memoryRcj?.find(item=>item.unitId === unitId && item.materialCode === materialCode );
            }

        }

        if(ObjectUtil.isEmpty(cacheRcj)){
            let  rcjList = this.service.gongLiaoJiProject.gljRcjService.getUserRcj(constructId);
            cacheRcj = rcjList.find(item=>item.unitId == unitId && item.materialCode === materialCode );
        }

        let copyRcj = null;
        if(ObjectUtil.isNotEmpty(cacheRcj)){
            copyRcj = ObjectUtil.cloneDeep(cacheRcj);
            if(copyRcj.supplementRcjFlag != 1 && ObjectUtil.isEmpty(copyRcj.standardId)){
                copyRcj.standardId = copyRcj.sequenceNbr;
            }
            copyRcj.sequenceNbr = Snowflake.nextId();
        }
        return copyRcj;
    }

    /**
     * 获取RCJ(适用于父级人材机获取)，并处理standardId
     * @param unitProject
     * @param libraryCode
     * @param rcjCode
     * @param de
     * @return {Promise<BaseRcj2022|null|*>}
     */
    async getRCJ(constructId, unitId, libraryCode, rcjCode, de) {
        let materialCode = rcjCode.replace(/#\d+/g, '')
        let baseRcj = await this.getRcjByCodes(libraryCode, materialCode);


        let cacheRcj = await this.getCacheRcjAndCopy(constructId, unitId, rcjCode, baseRcj);
        if(ObjectUtil.isNotEmpty(cacheRcj)){
            return cacheRcj;
        }


        if(ObjectUtil.isNotEmpty(baseRcj)){
            if(ObjectUtil.isEmpty(baseRcj.standardId)){
                baseRcj.standardId = baseRcj.sequenceNbr;
            }
            baseRcj.ifDonorMaterial = 0;
            baseRcj.ifProvisionalEstimate = 0;
            baseRcj.sequenceNbr = Snowflake.nextId();
            baseRcj.rcjId = baseRcj.standardId;
            await this.app.service.gongLiaoJiProject.gljRcjService.rcjCalculateOriginalData(baseRcj,baseRcj);
        }

        if (rcjCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
            || rcjCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
            || rcjCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ) {
            // 设置为非费用人材机
            baseRcj.isFyrcj = 1;
        }

        // 此处注释是因为查询方法不应该更新人材机缓存
        //UnitRcjCacheUtil.add(unitProject, baseRcj, de.sequenceNbr)
        return baseRcj;
    }

    /**
     * 公式格式化：替换公式中RD(),RU(),MOD(),平方符号（^）,百分号；以及用户输入值V
     * @param mathStr
     * @param rule
     * @return {string|*}
     */
    mathFormat(mathStr, rule){
        if(ObjectUtil.isEmpty(mathStr)){
            return "";
        }
        //处理向上/向下取整、取余
        let tmpMath = mathStr.replace(/RU/g, "Math.ceil")
            .replace(/RD/g, "Math.floor")
            .replace(/MOD\(([^)]+),\s*([^)]+)\)/g, "(($1)%($2))")
            .replace(/\^/g, "**")
            .replace(/%/g, "*0.01")
        ;

        if(/\bV\b/.test(tmpMath)){
            let defaultValue = isNaN(+rule.defaultValue) ? null : +rule.defaultValue;
            let inputValue = isNaN(+rule.selectedRule) ? null : +rule.selectedRule;
            tmpMath = tmpMath.replace(/\bV\b/g, inputValue == null ? defaultValue : inputValue);
        }

        return tmpMath;
    }

    async editRcj(fromRcj, toRcj, param) {
        const {constructId, singleId, unitId, unitProject, de} = param;
        //保留消耗量
        toRcj.resQty = fromRcj.resQty;
        toRcj.initResQty = fromRcj.initResQty;

        toRcj.sequenceNbr  = toRcj.rcjId ;

        let replaceType = singleId;
        if(toRcj.supplementRcjFlag == 1 || toRcj.materialCode.includes("#")){
            replaceType = RcjCommonConstants.RCJ_MEMORY_REPLACE;
        }

        //人材机替换
        await this.app.service.gongLiaoJiProject.gljRcjService.replaceRcjData(de.sequenceNbr, toRcj, constructId, replaceType, unitId, de.sequenceNbr, fromRcj.sequenceNbr, undefined, null, {source: CommonConstants.BZHS, isConversionDeal: true});
        let unitProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);

        let resultRcj = unitProjectRcjs.find((v) => v.sequenceNbr == fromRcj.sequenceNbr);
        // 如果手动修改过消耗量，需要保留手动修改值
        if (fromRcj.hasOwnProperty("consumerResQty")) {
            resultRcj.consumerResQty = fromRcj.consumerResQty;
        }
        return resultRcj;
    }

    /**
     * 人材机替换
     * @param fromRcj
     * @param toRcj
     * @param param
     * @return {Promise<*>}
     */
    async editRcjBak(fromRcj, toRcj, param) {
        const {constructId, singleId, unitId, unitProject, de} = param;
        // 为了适配rcjReplaceStrategy对替换目标人材机的处理
        if(ObjectUtil.isNotEmpty(toRcj.standardId)){
            toRcj.sequenceNbr = toRcj.standardId;
        }
        //保留消耗量
        toRcj.resQty = fromRcj.resQty;
        toRcj.initResQty = fromRcj.initResQty;
        //人材机替换
        let rcjReplaceStrategy = new RcjReplaceStrategy({
            constructId,
            singleId,
            unitId,
            projectObj: PricingFileFindUtils.getProjectObjById(constructId)
        });
        let newRcj = await rcjReplaceStrategy.execute({
            selectLine: fromRcj,
            replaceLine: toRcj,
            de: de,
            conversionCoefficient: 1,
            conversionFlag: true
        });

        const {sequenceNbr: id} = newRcj;
        let resultRcj = unitProject.constructProjectRcjs.find((v) => v.sequenceNbr == id);
        // 如果手动修改过消耗量，需要保留手动修改值
        if (fromRcj.hasOwnProperty("consumerResQty")) {
            resultRcj.consumerResQty = fromRcj.consumerResQty;
        }
        return resultRcj;
    }

    /**
     * 根据定额册code，材料code查询材料
     * @param libraryCode
     * @param materialCode
     * @return {Promise<BaseRcj2022|null>}
     */
    async getRcjByCodes(libraryCode, materialCode){
        return await this.app.gljAppDataSource
            .getRepository(BaseRcj2022)
            .findOneBy({
                libraryCode: libraryCode,
                materialCode: materialCode,
            });
    }

    /**
     * 计算公式中的算式：
     *     R*(3-1*2)  => R*1
     *     *3  => *3
     *     +3*3 - (4/2)  => +7
     * @param numStr
     * @return {string}
     */
    mathAfterCalculation(numStr){
        let firstChar = numStr.charAt(0);
        let prefix = "";
        if(/[RCJ][-+*/]/.test(numStr)){
            prefix = numStr.substring(0,2);
            numStr = numStr.substring(2);
        }else if("-+*/".includes(firstChar)) {
            numStr = numStr.substring(1);
            prefix = firstChar;
        }
        let value = NumberUtil.numberScale(eval(numStr), 4);
        // let value = eval(numStr);
        if(value < 0){
            return `${prefix}(${value})`;
        }else{
            return prefix + value;
        }
    }

    async deInitialRcjs(params) {
        const {constructId, singleId, unitId, unitProject, de, adjustmentCoefficient, tempDeleteFlagMap, rcjPriceMap} = params;
        let deNewRcjs = [];

        let otherDeRcjs = [];

        let deRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId, de.sequenceNbr) + WildcardMap.WILDCARD);

        let deRcjRelationList = await this.service.gongLiaoJiProject.gljBaseDeRcjRelationService.getDeRcjRelationByDeId(de.standardId);

        for (let i = 0; i < deRcjRelationList.length; i++) {
            let code = deRcjRelationList[i].materialCode;
            let rcj = await this.getRCJ(constructId, unitId, deRcjRelationList[i].libraryCode,code,de);
            if(rcj){
                rcj.deId = de.sequenceNbr;
                rcj.parentId = de.sequenceNbr;
                rcj.deRowId = de.sequenceNbr;
                rcj.unitId = unitId;
                rcj.constructId = constructId;
                rcj.initResQty = deRcjRelationList[i].resQty;
                rcj.resQty = deRcjRelationList[i].resQty;
                rcj.originalQty = deRcjRelationList[i].resQty;
                rcj.marketPrice = rcj.baseJournalPrice ;
                rcj.marketTaxPrice = rcj.baseJournalTaxPrice ;
                rcj.adjustmentCoefficient = adjustmentCoefficient;
                rcj.taxRateInit = rcj.taxRate;

                for (let key in RcjTypeEnum) {
                    if (RcjTypeEnum[key].code == rcj.kind) {
                        rcj.type=   RcjTypeEnum[key].desc;
                    }
                }

                // 材料价格处理
                if(rcjPriceMap.has(rcj.materialCode)){
                    let oldRcj = rcjPriceMap.get(rcj.materialCode);
                    rcj.baseJournalPrice = oldRcj.baseJournalPrice;
                    rcj.baseJournalTaxPrice = oldRcj.baseJournalTaxPrice;
                    rcj.marketPrice = oldRcj.marketPrice;
                    rcj.marketTaxPrice = oldRcj.marketTaxPrice;
                    rcj.ifDonorMaterial = oldRcj.ifDonorMaterial || 0;
                    rcj.markSum = oldRcj.markSum;
                    rcj.baseJournalTaxPriceOriginalForward = oldRcj.baseJournalTaxPriceOriginalForward;
                    rcj.sourcePrice = oldRcj.sourcePrice;

                    rcj.originMarketPrice = oldRcj.originMarketPrice;
                    rcj.originMarketTaxPrice = oldRcj.originMarketTaxPrice;
                    rcj.originBaseJournalPrice = oldRcj.originBaseJournalPrice;
                    rcj.originBaseJournalTaxPrice = oldRcj.originBaseJournalTaxPrice;
                    rcj.originKind = oldRcj.originKind;
                    rcj.originalConsumeQty = oldRcj.originalConsumeQty;
                }

                await this.app.service.gongLiaoJiProject.gljRcjService.rcjCalculateOriginalData(rcj,rcj);

                await this.app.service.gongLiaoJiProject.gljRcjService.handleSzyhRcj(rcj);

                deNewRcjs.push(rcj);
            }
        }

        // 删除人材机
        this.deleteDeRcjs(constructId, unitId, de.sequenceNbr, deRcjs);
        // 新增人材机
        await this.addDeRcjs(constructId, singleId, unitId, de.sequenceNbr, deNewRcjs);

        return deNewRcjs;
    }

    deleteDeRcjs(constructId, unitId, deId, rcjs){
        let resourceDomain = ProjectDomain.getDomain(constructId).getResourceDomain();
        let resourceMap = resourceDomain.ctx.resourceMap
        for(let rcj of rcjs){
            let key = WildcardMap.generateKey(unitId, deId, rcj.sequenceNbr);
            let rcjRs = resourceMap.getValues(key);

            if (rcjRs.length >= 1) {
                resourceMap.removeByPattern(key);
            }
        }
    }

    async addDeRcjs(constructId, singleId, unitId, deId, rcjs){
        // let param = {
        //     isConversionDeal: true
        // };
        // for(let rcj of rcjs){
        //    await this.service.gongLiaoJiProject.gljRcjService.addRcjData(deId, rcj, constructId, singleId, unitId, deId, "", param);
        // }


        let resourceDomain = ProjectDomain.getDomain(constructId).getResourceDomain();

        for(let rcj of rcjs){
            await this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(rcj);
            resourceDomain.createResource(unitId, deId, rcj)
        }
    }

    addTempRemoveRCJResQty(rcj, addResQty){
        if(rcj.isTempRemove == CommonConstants.COMMON_YES){
            rcj.changeResQty = rcj.changeResQty + addResQty;
        }else{
            rcj.resQty = rcj.resQty + addResQty;
        }
    }

    updateTempRemoveRCJResQty(rcj, resQty){
        if(rcj.isTempRemove == CommonConstants.COMMON_YES){
            rcj.changeResQty = resQty;
        }else{
            rcj.resQty = resQty;
        }
    }
}
module.exports = ConversionService
