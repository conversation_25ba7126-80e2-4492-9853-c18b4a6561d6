<!--
 * @Descripttion:人材机汇总
 * @Author: liuxia
 * @Date: 2024-03-14 15:26:47
 * @LastEditors: liuxia
 * @LastEditTime: 2024-09-28 13:08:29
-->
<template>
  <div class="table-content">
    <Teleport
      to=".tab-menus"
      v-if="projectStore.tabSelectName === '人材机调整'"
    >
      <span class="price-list">
        <span
          v-if="
            (projectStore.currentTreeInfo?.levelType === 1 &&
              projectStore.asideMenuCurrentInfo?.key !== '21') ||
            (projectStore.currentTreeInfo?.levelType === 3 &&
              projectStore.currentTreeInfo?.originalFlag &&
              projectStore.asideMenuCurrentInfo?.key === '0') ||
            (projectStore.currentTreeInfo?.levelType === 3 &&
              !projectStore.currentTreeInfo?.originalFlag &&
              projectStore.asideMenuCurrentInfo?.key !== '21')
          "
          ><icon-font
            style="padding-right: 4px"
            type="icon-jiesuanhejia"
          ></icon-font
          >结算合价: {{ jieSuanPriceTotal.toFixed(2) }}</span
        >
        <span
          v-if="
            (projectStore.currentTreeInfo?.levelType === 1 &&
              projectStore.asideMenuCurrentInfo?.key !== '21') ||
            (projectStore.currentTreeInfo?.levelType === 3 &&
              projectStore.currentTreeInfo?.originalFlag) ||
            (projectStore.currentTreeInfo?.levelType === 3 &&
              !projectStore.currentTreeInfo?.originalFlag &&
              projectStore.asideMenuCurrentInfo?.key !== '21')
          "
          ><icon-font
            style="padding-right: 4px"
            type="icon-jiesuanhejia"
          ></icon-font
          >价差合计: {{ jieSuanPriceDifferencSum.toFixed(2) }}</span
        >
        <span
          v-if="
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList
              ?.length > 0 &&
            projectStore.currentStageInfo &&
            !Array.isArray(projectStore.currentStageInfo?.scope)
          "
          ><icon-font type="icon-tiaochazhouqi"></icon-font>调差周期: 第{{
            projectStore.currentStageInfo?.num
          }}期</span
        >
        <span
          v-if="
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.frequencyList
              ?.length > 0 &&
            Array.isArray(projectStore.currentStageInfo?.scope)
          "
          ><icon-font type="icon-tiaochazhouqi"></icon-font>调差周期: 第{{
            projectStore.currentStageInfo.scope[0]
          }}期 ~ 第{{ projectStore.currentStageInfo.scope[1] }}期</span
        >
        <icon-font
          @click="showPageColumnSetting"
          style="padding-right: 15px"
          type="icon-xianshilieshezhi"
        ></icon-font>
      </span>
    </Teleport>
    <frameSelect
      eventDom="multiple-select"
      ref="frameSelectRef"
      type="human"
      :tableData="tableData"
      @scrollTo="scrollTo"
      @selectData="getSelectData"
      class="table-content"
    >
      <vxe-table
        align="center"
        ref="humanTable"
        :loading="loading"
        height="auto"
        :menu-config="menuConfig"
        :column-config="{ resizable: true }"
        :row-config="{
          keyField: 'sequenceNbr',
          isHover: true,
          isCurrent: true,
        }"
        :data="tableData"
        :cell-style="
          projectStore.currentTreeInfo.levelType === 3
            ? cellStyle
            : cellTableStyle
        "
        :row-style="rowStyle"
        @edit-closed="editClosedEvent"
        keep-source
        @menu-click="contextMenuClickEvent"
        @cell-click="useCellClickEvent"
        class="table-edit-common"
        :cell-class-name="selectedClassName"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
          enabled: isEditEnabled,
        }"
        :scroll-y="{ enabled: true, gt: 30 }"
        @current-change="currentChange"
        show-overflow
        :header-cell-class-name="setHeaderCellClassName"
      >
        <vxe-column v-for="columns of handlerColumns" v-bind="columns">
          <template
            #header="{
              column,
              columnIndex,
              $columnIndex,
              _columnIndex,
              $rowIndex,
            }"
          >
            <span class="custom-header">
              <span
                v-if="
                  columns.field === 'jieSuanStagePrice' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次单价</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePrice' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期单价</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStageDifferenceQuantity' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次调差工程量</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStageDifferenceQuantity' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期调差工程量</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePriceSource' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次单价来源</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePriceSource' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期单价来源</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanPriceLimit' &&
                  projectStore.asideMenuCurrentInfo?.defaultFeeFlag
                    ?.frequencyList?.length > 0 &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{
                  projectStore.currentStageInfo?.num
                }}次单价涨/跌幅(%)</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanPriceLimit' &&
                  projectStore.asideMenuCurrentInfo?.defaultFeeFlag
                    ?.frequencyList?.length > 0 &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{
                  projectStore.currentStageInfo?.num
                }}期单价涨/跌幅(%)</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanUnitPriceDifferenc' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次单位价差</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanUnitPriceDifferenc' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期单位价差</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePriceDifferencSum' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次价差合计</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePriceDifferencSum' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期价差合计</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagetaxRemoval' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次除税系数(%)</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagetaxRemoval' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期除税系数(%)</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePriceDifferencInputTax' &&
                  Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}次价差进项税额</span
              >
              <span
                v-else-if="
                  columns.field === 'jieSuanStagePriceDifferencInputTax' &&
                  !Array.isArray(projectStore.currentStageInfo?.scope)
                "
                >第{{ projectStore.currentStageInfo?.num }}期价差进项税额</span
              >
              <span v-else>{{ column.title }}</span>
              <CloseOutlined
                class="icon-close"
                @click="closeColumn({ column })"
              />
            </span>
          </template>
          <template
            v-if="columns.slot"
            #default="{ column, row, $columnIndex }"
          >
            <template v-if="columns.field === 'markSum'">
              <vxe-checkbox
                v-model="row.markSum"
                size="small"
                content=""
                :checked-value="1"
                :unchecked-value="0"
                :disabled="row.isGray"
                @change="CheckboxChange(row, 'markSum')"
                v-if="[1, 2].includes(Number(row.levelMark))"
              ></vxe-checkbox>
            </template>
            <template v-else-if="columns.field === 'outputToken'">
              <vxe-checkbox
                v-model="row.outputToken"
                size="small"
                content=""
                :checked-value="1"
                :unchecked-value="2"
                @change="CheckboxChange(row, 'outputToken')"
              ></vxe-checkbox>
            </template>
            <!-- 风险幅度范围-->
            <template v-else-if="columns.field === 'riskAmplitudeRangeMin'">
              <icon-font
                type="icon-bianji"
                class="more-icon"
                v-if="
                  isSelectedCell({
                    $columnIndex,
                    column,
                    row,
                  })
                "
                @click="riskOpen(row)"
              ></icon-font>
              <span
                >{{ row.riskAmplitudeRangeMin }} ~
                {{ row.riskAmplitudeRangeMax }}</span
              >
            </template>
            <template v-else-if="columns.field === 'ifDonorMaterial'">
              <span>{{ getDonorMaterialText(row.ifDonorMaterial) }}</span>
            </template>
            <template v-else-if="columns.field === 'ifLockStandardPrice'">
              <vxe-checkbox
                v-model="row.ifLockStandardPrice"
                size="small"
                content=""
                :checked-value="1"
                :unchecked-value="0"
                @change="CheckboxChange(row, 'ifLockStandardPrice')"
                v-if="row.checkIsShow"
                :disabled="row.isGray"
              ></vxe-checkbox>
            </template>
            <template v-else-if="columns.field === 'isDifference'">
              <vxe-checkbox
                v-model="row.isDifference"
                size="small"
                content=""
                :checked-value="true"
                :unchecked-value="false"
                :disabled="
                  row.isGray ||
                  (!contractOriginalFlag && !isDifference) ||
                  ['10000001', '10000002', '10000003'].includes(
                    row.materialCode.includes('#')
                      ? row.materialCode.split('#')[0]
                      : row.materialCode
                  ) ||
                  row.ifProvisionalEstimate == 1 ||
                  row.ifDonorMaterial == 1
                "
                @change="CheckboxChange(row, 'isDifference')"
              ></vxe-checkbox>
            </template>
            <template v-else-if="columns.field === 'ifProvisionalEstimate'">
              <vxe-checkbox
                v-model="row.ifProvisionalEstimate"
                size="small"
                content=""
                :checked-value="1"
                :unchecked-value="0"
                :disabled="projectStore.currentTreeInfo?.originalFlag"
                @change="CheckboxChange(row, 'ifProvisionalEstimate')"
              ></vxe-checkbox>
            </template>
            <template v-else-if="columns.field === 'jieSuanFee'">
              {{ getFeeType(row.jieSuanFee) }}
            </template>
            <template v-else>
              <span>{{ row[columns.field] }}</span>
            </template>
          </template>
          <template v-if="columns.slot" #edit="{ row, $rowIndex }">
            <template v-if="columns.field === 'materialCode'">
              <vxe-select
                v-model="row.type"
                :clearable="false"
                transfer
                v-if="
                  (row.type === '主材费' ||
                    row.type === '材料费' ||
                    row.type === '设备费') &&
                  !(
                    row.markSum === 1 &&
                    (row.levelMark === 1 || row.levelMark === 2)
                  ) &&
                  Number(row.edit) !== 1
                "
              >
                <vxe-option
                  v-for="item in selectOptions"
                  :key="item.type"
                  :value="item.type"
                  :label="item.type"
                ></vxe-option>
              </vxe-select>
              <span v-else>{{ row.type }} </span>
            </template>
            <template v-if="columns.field === 'type'">
              <vxe-select
                v-model="row.type"
                :clearable="false"
                transfer
                v-if="
                  (row.type === '主材费' ||
                    row.type === '材料费' ||
                    row.type === '设备费') &&
                  !(
                    row.markSum === 1 &&
                    (row.levelMark === 1 || row.levelMark === 2)
                  ) &&
                  Number(row.edit) !== 1
                "
              >
                <vxe-option
                  v-for="item in selectOptions"
                  :key="item.type"
                  :value="item.type"
                  :label="item.type"
                ></vxe-option>
              </vxe-select>
              <span v-else>{{ row.type }} </span>
            </template>
            <template v-if="columns.field === 'materialName'">
              <vxe-input
                v-if="Number(row.edit) !== 1"
                :clearable="false"
                v-model.trim="row.materialName"
                type="text"
                @blur="clear()"
              ></vxe-input>
              <span v-else>{{ row.materialName }}</span>
            </template>
            <template v-if="columns.field === 'specification'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.specification"
                type="text"
                @blur="clear()"
                v-if="Number(row.edit) !== 1"
              ></vxe-input>
              <span v-else>{{ row.specification }}</span>
            </template>
            <template v-if="columns.field === 'unit'">
              <vxeTableEditSelect
                v-if="Number(row.edit) !== 1"
                :filedValue="row.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    saveCustomInput(newValue, row, 'unit', $rowIndex);
                  }
                "
              ></vxeTableEditSelect>
            </template>
            <template v-if="columns.field === 'marketPrice'">
              <vxe-input
                v-if="
                  row.ifLockStandardPrice !== 1 &&
                  !(
                    row.markSum === 1 &&
                    (row.levelMark === 1 || row.levelMark === 2)
                  ) &&
                  Number(row.edit) !== 1 &&
                  projectStore.asideMenuCurrentInfo?.key !== '20'
                "
                :clearable="false"
                v-model.trim="row.marketPrice"
                type="text"
                @blur="
                  row.marketPrice = pureNumber(row.marketPrice, 2);
                  clear();
                "
              ></vxe-input>
              <span v-else>{{ row.marketPrice }}</span>
            </template>
            <template v-if="columns.field === 'jieSuanPrice'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanPrice"
                type="text"
                @blur="
                  row.jieSuanPrice = pureNumber(row.jieSuanPrice, 2);
                  clear();
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'jieSuanStagePrice'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanStagePrice"
                type="text"
                @blur="
                  row.jieSuanStagePrice = pureNumber(row.jieSuanStagePrice, 2);
                  clear();
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'jieSuanBasePrice'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanBasePrice"
                type="text"
                @blur="
                  row.jieSuanBasePrice = pureNumber(row.jieSuanBasePrice, 2);
                  clear();
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'ifDonorMaterial'">
              <vxe-select
                v-if="row.checkIsShow"
                v-model="row.ifDonorMaterial"
                @change="CheckboxChange(row, 'ifDonorMaterial')"
                transfer
              >
                <vxe-option
                  v-for="item in donorMaterialList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                ></vxe-option>
              </vxe-select>
              <span v-else>{{
                getDonorMaterialText(row.ifDonorMaterial)
              }}</span>
            </template>
            <template v-if="columns.field === 'donorMaterialNumber'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.donorMaterialNumber"
                type="text"
                @blur="clear()"
                @keyup="
                  row.donorMaterialNumber = row.donorMaterialNumber.replace(
                    /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                    '$1$2.$3'
                  )
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'riskAmplitudeRangeMin'">
              <span @click="riskOpen(row)"
                >{{ row.riskAmplitudeRangeMin }} ~
                {{ row.riskAmplitudeRangeMax }}</span
              >
            </template>
            <template v-if="columns.field === 'jieSuanAdminRate'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanAdminRate"
                type="text"
                @blur="clear()"
                @keyup="
                  row.jieSuanAdminRate = row.jieSuanAdminRate.replace(
                    /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                    '$1$2.$3'
                  )
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'jieSuanStagetaxRemoval'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanStagetaxRemoval"
                type="text"
                @blur="clear()"
                @keyup="
                  row.jieSuanStagetaxRemoval =
                    row.jieSuanStagetaxRemoval.replace(
                      /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                      '$1$2.$3'
                    )
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'jieSuanTaxRemoval'">
              <vxe-input
                v-if="
                  row.type !== '人工费' &&
                  row.materialCode !== 'JXPB-005' &&
                  row.materialCode !== 'JXPB-006'
                "
                :clearable="false"
                v-model.trim="row.jieSuanTaxRemoval"
                type="text"
                @blur="clear()"
                @keyup="
                  row.jieSuanTaxRemoval = row.jieSuanTaxRemoval.replace(
                    /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                    '$1$2.$3'
                  )
                "
              ></vxe-input>
              <span v-else>{{ row.jieSuanTaxRemoval }}</span>
            </template>
            <template v-if="columns.field === 'jieSuanBasePriceF0'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanBasePriceF0"
                type="text"
                @blur="clear()"
                @keyup="
                  row.jieSuanBasePriceF0 = row.jieSuanBasePriceF0.replace(
                    /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                    '$1$2.$3'
                  )
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'jieSuanCurrentPriceF0'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.jieSuanCurrentPriceF0"
                type="text"
                @blur="clear()"
                @keyup="
                  row.jieSuanCurrentPriceF0 = row.jieSuanCurrentPriceF0.replace(
                    /^(\-)*(\d+)\.(\d\d\d\d).*$/,
                    '$1$2.$3'
                  )
                "
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'producer'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.producer"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'manufactor'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.manufactor"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'brand'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.brand"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'deliveryLocation'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.deliveryLocation"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'qualityGrade'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.qualityGrade"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
            <template v-if="columns.field === 'jieSuanFee'">
              <vxe-select v-model="row.jieSuanFee" transfer>
                <vxe-option
                  v-for="item in selectList"
                  :key="item.code"
                  :value="item.code"
                  :label="item.name"
                ></vxe-option>
              </vxe-select>
            </template>
            <template v-if="columns.field === 'remark'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.remark"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
          </template>
        </vxe-column>
        <template #empty>
          <span
            style="
              color: #898989;
              font-size: 14px;
              display: block;
              margin: 25px 0;
            "
          >
            <img :src="getUrl('newCsProject/none.png')" />
          </span>
        </template>
      </vxe-table>
    </frameSelect>
  </div>
  <common-modal
    className="dialog-comm"
    :title="typeModal"
    width="1020"
    :height="
      typeModal === '载价编辑' ? 560 : typeModal === '载价报告' ? 500 : 530
    "
    v-model:modelValue="reportModel"
    @cancel="cancel"
    @close="reportModel = false"
    :mask="true"
    :lockView="true"
  >
    <!--     :mask="typeModal === '载价编辑' ? false : true"
    :lockView="typeModal === '载价编辑' ? false : true" -->
    <batch-load-price
      v-if="typeModal === '批量载价'"
      :priceType="priceType"
      :isOriginalFlag="isOriginalFlag()"
      @close="close"
    ></batch-load-price>
    <edit-load-price
      v-if="typeModal === '载价编辑'"
      :isOriginalFlag="isOriginalFlag()"
      :propsData="propsData"
      :priceType="priceType"
      @close="close"
    ></edit-load-price>
    <report-load-price
      :priceType="priceType"
      :isOriginalFlag="isOriginalFlag()"
      v-if="typeModal === '载价报告'"
    ></report-load-price>
    <!-- 载价报告 -->
  </common-modal>

  <!-- 显示对应子目弹窗 -->
  <quota-popup
    v-if="quotaPopupVisible"
    :levelType="projectStore.currentTreeInfo.levelType"
    :HeaderData="quotaHeaderData"
    @closeDialog="quotaPopupVisible = false"
  ></quota-popup>

  <risk-range
    v-model:riskVisible="riskVisible"
    :currentInfo="currentInfo"
    :isEditCurrentData="isEditCurrentData"
    :selectData="selectData"
    @updateData="getInitList"
    @updateCurrentInfo="updateCurrentInfo"
  ></risk-range>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
  <material-machine-index
    pageFr="rcjSummary"
    v-model:indexVisible="indexVisible"
    :currentMaterialInfo="currentInfo"
    :indexLoading="indexLoading"
    @addChildrenRcjData="() => {}"
    @currentInfoReplace="currentInfoReplace"
  ></material-machine-index>
  <SetMainMaterials
    :tableData="allRCJTableData"
    @successCallback="getHumanMachineData"
    v-model:materialVisible="materialVisible"
  />
  <LookupFilter
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
  />
  <!-- 调整市场价系数 -->
  <common-modal
    className="dialog-comm"
    title="调整市场价系数"
    width="300"
    height="200"
    v-model:modelValue="adjustFactor"
    :mask="true"
  >
    <div class="adjustFactorMoadl">
      <!-- <p class="title">
        该功能针对所有选中行进行调整
      </p> -->
      <div>
        <span> 市场价系数： </span>
        <a-input
          v-model:value="marcketFactor"
          placeholder="请输入市场价系数"
          @blur="marcketFactor = selfCheck(marcketFactor, 2, 0, 1000)"
          @keyup="marcketFactor = marcketFactor.replace(/[^\d.]/g, '')"
        />
      </div>
      <p class="footor">
        <a-button @click="adjustFactor = false">取消</a-button>
        <a-button type="primary" @click="sureOrCancel()">确定</a-button>
      </p>
    </div>
  </common-modal>
  <batch-set-tax-removal
    v-model:visible="taxRemovalVisible"
    :selectData="selectData"
    @updateData="getInitList"
  ></batch-set-tax-removal>
</template>

<script setup>
import {
  onActivated,
  onMounted,
  onUpdated,
  ref,
  watch,
  getCurrentInstance,
  provide,
  reactive,
  defineAsyncComponent,
  nextTick,
  computed,
  onDeactivated,
} from 'vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import loadApi from '@/api/loadPrice';
import infoMode from '@/plugins/infoMode.js';
import csProject from '@/api/csProject';
import jieSuanDetail from '@/api/jiesuanApi';
import { getUrl, pureNumber } from '@/utils/index';
import HumanHeader from './HumanHeader.vue';
import { insetBus } from '@/hooks/insetBus';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
import operateList from '@/views/projectDetail/customize/operate';
import { updateOperateByName } from '@/views/projectDetail/customize/operate';
import BatchLoadPrice from '@/views/projectDetail/customize/HumanMachineSummary/BatchLoadPrice.vue';
import EditLoadPrice from '@/views/projectDetail/customize/HumanMachineSummary/EditLoadPrice.vue';
import ReportLoadPrice from '@/views/projectDetail/customize/HumanMachineSummary/ReportLoadPrice.vue';
import { isOriginalFlag } from './common.js';

let reportModel = ref(false);
let priceType = ref(1);
let typeModal = ref(null); //弹框类型
let propsData = ref(); //载价编辑的优先级
let jieSuanPriceTotal = ref(0); // 结算单价总计
let jieSuanPriceDifferencSum = ref(0); // 价差合计总计

import { useCellClick } from '@/hooks/useCellClick';
import RiskRange from './riskRange.vue';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import { humanOriginalTableColumns, humanTableColumns } from './columns.js';
import api from '@/api/projectDetail.js';
import { setGlobalLoading } from '@/hooks/publicApiData.js';

const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
} = useCellClick();

const quotaPopup = defineAsyncComponent(() =>
  import('@/components/SummaryPopup/index.vue')
);

// 是否合同内文件true是false否projectStore.currentTreeInfo?.originalFlag
let contractOriginalFlag = computed(
  () => projectStore.currentTreeInfo?.originalFlag
); // 暂时默认为true

let unifyData = operateList.value.find(
  item => item.name === 'unify-humanMachineSummary'
);
let isLoad = operateList.value.find(item => item.name === 'batch-loadprice');
// let isSeeReport = operateList.value.find(
//   item => item.name === 'loadprice-report'
// );
let quotaHeaderData = ref(null);
let isCurrent = ref(null);
let colorUnitList = ref([]);
let oldData = ref([]); //工程项目级别人材机汇总旧数据
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});
const emits = defineEmits(['updateMenuList']);
let humanTable = ref();
let loading = ref(false);
const projectStore = projectDetailStore();
let tableData = ref([]);
let upDateRow = ref();
let selectData = ref([]);
const selectOptions = [
  { type: '主材费' },
  { type: '材料费' },
  { type: '设备费' },
];
const selectList = reactive([
  {
    name: '计取税金',
    code: 1,
  },
  {
    name: '计取规费、税金',
    code: 2,
  },
  {
    name: '计取安文费、税金',
    code: 3,
  },
]);

const getFeeType = type => {
  let value;
  switch (type) {
    case 1:
      value = '计取税金';
      break;
    case 2:
      value = '计取规费、税金';
      break;
    case 3:
      value = '计取安文费、税金';
      break;
  }
  return value;
};
const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];
const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
let riskVisible = ref(false); // 风险幅度范围弹框
let isEditEnabled = ref(true); // 是否可编辑行
let currentInfo = ref(null); // 当前点击行信息
let isEditCurrentData = ref(false); // 用于判断风险幅度范围是批量修改还是单条修改
let isDifference = ref(false); // 是否参与人材机调差
const taxRemovalVisible = ref(false); // 批量设置结算除税系数弹框

const quotaPopupVisible = ref(false); // 显示对应子目弹窗
const menuConfig = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'search',
          name: '显示对应子目',
          visible: true,
          disabled: false,
        },
        {
          code: 'remove',
          name: '清除载价',
          visible: true,
          disabled: false,
        },
        {
          code: 'pageColumnSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
        {
          code: 'riskRange',
          name: '风险幅度范围',
          visible: true,
          disabled: false,
        },
        {
          code: 'batchSettingFactor',
          name: '批量设置结算除税系数',
          visible: true,
          disabled: false,
        },
        {
          code: 'closeDifference',
          name: '取消调差',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('options, column', options, row);
    if (!row) return;
    options[0][1].disabled = !row.highlight || row.sourcePrice == '自行载价';
    options.forEach(list => {
      list.forEach(item => {
        if (item.code === 'closeDifference') {
          if (row.isDifference && row.type !== '人工费') {
            item.visible = true;
          } else {
            item.visible = false;
          }
        } else if (item.code === 'riskRange') {
          if (
            (projectStore.currentTreeInfo.levelType === 1 &&
              ['1', '2', '3'].includes(
                projectStore.asideMenuCurrentInfo?.key
              )) ||
            (projectStore.currentTreeInfo.levelType === 3 &&
              projectStore.currentTreeInfo.originalFlag &&
              !projectStore.currentStageInfo &&
              ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key))
          ) {
            item.visible = true;
          } else {
            item.visible = false;
          }
        } else if (item.code === 'batchSettingFactor') {
          if (
            (projectStore.currentTreeInfo.levelType === 1 &&
              ['1', '2', '3', '8', '20'].includes(
                projectStore.asideMenuCurrentInfo?.key
              )) ||
            (projectStore.currentTreeInfo.levelType === 3 &&
              ((projectStore.currentTreeInfo.originalFlag &&
                !projectStore.currentStageInfo &&
                ['1', '2', '3', '8'].includes(
                  projectStore.asideMenuCurrentInfo?.key
                )) ||
                (!projectStore.currentTreeInfo.originalFlag &&
                  ['20'].includes(projectStore.asideMenuCurrentInfo?.key))))
          ) {
            item.visible = true;
          } else {
            item.visible = false;
          }
        }
      });
    });
    return true;
  },
});

// 右键点击事件
const contextMenuClickEvent = ({ menu, row }) => {
  console.log('menu, row', menu, row);
  if (!row) return;
  switch (menu.code) {
    case 'search':
      quotaHeaderData.value = row;
      quotaPopupVisible.value = true;
      break;
    case 'remove':
      // 掉接口恢复系统默认值
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '该条材料市场价已被锁定，'
            : '是否确定清除选中数据的载价数据？',
        descText:
          +row.ifLockStandardPrice || +row.cusTomIfLockStandardPrice
            ? '请取消勾选后再进行清除载价操作'
            : '删除后无法撤销恢复',
        isFunction: false,
        confirm: () => {
          if (!row.ifLockStandardPrice || !row.cusTomIfLockStandardPrice) {
            clearZaijia(row);
          }
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
      break;
    case 'pageColumnSetting':
      showPageColumnSetting();
      break;
    case 'batchSettingFactor':
      taxRemovalVisible.value = true;
      break;
    case 'riskRange':
      riskOpen(row);
      break;
    case 'closeDifference':
      row.isDifference = false;
      upDate(row, 'isDifference');
      break;
  }
};

// 清除载价格
const clearZaijia = data => {
  const {
    defaultFeeFlag: { frequencyList },
  } = projectStore.asideMenuCurrentInfo || {};
  const { num = null } = projectStore.currentStageInfo || {};
  let postData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    rcj: { ...JSON.parse(JSON.stringify(data)) },
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: data.sequenceNbr,
    num: frequencyList && frequencyList.length ? num : null,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    postData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    postData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }

  console.log(
    '🚀 ~ file: index.vue:768 ~ csProject.clearLoadPriceUse ~ postData:',
    postData
  );
  jieSuanDetail.jiesuanClearLoadPriceUse(postData).then(res => {
    console.log(
      '🚀 ~ file: index.vue:760 ~ csProject.clearLoadPriceUse ~ res:',
      res
    );
    if (res.result) {
      message.success('清除成功');
      if (projectStore.currentTreeInfo.levelType === 1) {
        data.isChange = true; //标识编辑行
        // getSameUnit();
        // let upDateList = getPropData();
        // console.log('upDateList', upDateList);
        // if (upDateList && upDateList.length > 0) {
        projectStore.SET_HUMAN_UPDATA_DATA({
          isEdit: true,
          name: 'unify-humanMachineSummary',
          // updataData: upDateList,
          updataData: [],
        });
        // }
        unifyData.disabled = false;
      }
      getInitList();
    }
  });
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};
const clear = () => {
  //清除编辑状态
  const $table = humanTable.value;
  $table.clearEdit();
};

const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = val => {
  console.log('selectData', val);
  selectData.value = val;
};

// 设置主要材料
let materialVisible = ref(false);
let allRCJTableData = ref([]);
const openSetMainMaterial = () => {
  materialVisible.value = true;
};
const closeSetMainMaterial = () => {
  materialVisible.value = false;
};

// 人材机索引
let indexVisible = ref(false);
// 人材机索引数据loading
let indexLoading = ref(false);
/**
 * 菜单右键替换数据处理
 */
const menuReplaceHandler = () => {
  indexVisible.value = true;
};
/**
 * 关闭替换人材机索引
 */
const closeReplaceRCJ = () => {
  indexVisible.value = false;
};

/**
 * 数据替换
 * replaceRcj 被替换的人材机
 * targetRcj 目标人材机
 */
const currentInfoReplace = targetInfo => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    replaceRcj: JSON.parse(JSON.stringify(currentInfo.value)),
    targetRcj: JSON.parse(JSON.stringify(targetInfo)),
  };
  api.replaceRcjToUnit(params).then(res => {
    console.log('人材机数据替换', params, res);
    if (res.status === 200) {
      message.success('替换成功!');
      getHumanMachineData();
      closeReplaceRCJ();
    }
  });
};

const editClosedEvent = e => {
  const { $table, row, column } = e;
  const field = column.field;

  // 市场价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (['marketPrice'].includes(field)) {
    row[field] = +row[field];
  }

  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  switch (field) {
    case 'unit':
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }

      break;
  }
  if (field === 'marketPrice' && value < 0) {
    $table.revertData(row, field);
  } else if (
    field === 'marketPrice' &&
    value > 0 &&
    row.marketPrice.length > 20
  ) {
    row.marketPrice = value.slice(0, 20);
  }
  if (
    field === 'marketPrice' &&
    value > 0 &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (
      Number(row.marketPrice) - Number(row.dePrice)
    ).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (field === 'donorMaterialNumber') {
    if (row.ifDonorMaterial === 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (row.ifDonorMaterial !== 1 && value > row.totalNumber) {
      $table.revertData(row, field);
      message.warn(`甲供数量>数量，请重新输入`);
    } else if (
      row.ifDonorMaterial !== 1 &&
      value <= row.totalNumber &&
      value > 0
    ) {
      row.ifDonorMaterial = 1;
    } else if (row.ifDonorMaterial === 1 && (value <= 0 || value === '')) {
      row.ifDonorMaterial = 0;
      row.donorMaterialNumber = '';
    }
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    console.log('🚀 ~ filnt ~ upDate:', row);
    upDate(row, field);
  } else {
    row.isChange = true; //标识编辑行
    getSameUnit();
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: 'unify-humanMachineSummary',
        updataData: upDateList,
      });
    }
  }
};
const getPropData = () => {
  let constructProjectRcjList = [];
  let upDateList = tableData.value.filter(item => item.isChange === true);
  console.log('upDateList', upDateList, oldData.value);
  upDateList.map(item => {
    let obj = {};
    let same = oldData.value.filter(l => l.sequenceNbr === item.sequenceNbr)[0];
    if (item.marketPrice !== same.marketPrice) {
      obj.marketPrice = item.marketPrice;
    }
    if (
      item.ifDonorMaterial != same.ifDonorMaterial ||
      item.donorMaterialNumber != same.donorMaterialNumber
    ) {
      obj.ifDonorMaterial = item.ifDonorMaterial;
      if (obj.ifDonorMaterial === 1) {
        obj.donorMaterialNumber = item.totalNumber;
      } else {
        obj.donorMaterialNumber = '';
      }
    }
    if (item.jieSuanBasePriceF0 != same.jieSuanBasePriceF0) {
      obj.jieSuanBasePriceF0 = item.jieSuanBasePriceF0;
    }
    if (item.jieSuanCurrentPriceF0 != same.jieSuanCurrentPriceF0) {
      obj.jieSuanCurrentPriceF0 = item.jieSuanCurrentPriceF0;
    }
    if (item.jieSuanBasePrice != same.jieSuanBasePrice) {
      obj.jieSuanBasePrice = item.jieSuanBasePrice;
    }
    if (item.jieSuanPrice != same.jieSuanPrice) {
      obj.jieSuanPrice = item.jieSuanPrice;
    }
    if (item.riskAmplitudeRangeMin != same.riskAmplitudeRangeMin) {
      obj.riskAmplitudeRangeMin = item.riskAmplitudeRangeMin;
    }
    if (item.jieSuanTaxRemoval != same.jieSuanTaxRemoval) {
      obj.jieSuanTaxRemoval = item.jieSuanTaxRemoval;
    }
    if (item.jieSuanAdminRate != same.jieSuanAdminRate) {
      obj.jieSuanAdminRate = item.jieSuanAdminRate;
    }
    if (item.jieSuanFee != same.jieSuanFee) {
      obj.jieSuanFee = item.jieSuanFee;
    }
    if (item.remark != same.remark) {
      obj.remark = item.remark;
    }
    if (item.ifLockStandardPrice != same.ifLockStandardPrice) {
      obj.ifLockStandardPrice = item.ifLockStandardPrice;
    }
    if (item.isDifference != same.isDifference) {
      obj.isDifference = item.isDifference;
    }
    if (
      obj.hasOwnProperty('ifDonorMaterial') &&
      !obj.hasOwnProperty('donorMaterialNumber')
    ) {
      obj.donorMaterialNumber =
        obj.ifDonorMaterial === 1 ? item.totalNumber : '';
    }
    obj.sequenceNbr = item.sequenceNbr;
    constructProjectRcjList.push(obj);
  });
  return constructProjectRcjList;
};
const isUse = async () => {
  //点击统一应用按钮
  if (!projectStore.humanUpdataData) {
    return;
  }

  // let tar = apiData.constructProjectRcjList[0];
  // if (
  //   Object.keys(tar).length === 1 &&
  //   apiData.constructProjectRcjList.length === 1
  // ) {
  //   apiData.constructProjectRcjList = [];
  // }

  //只是清除载价就传空值，清除载价+改市场价传修改数据
  setGlobalLoading(true, '统一应用中，请稍后...');
  if (projectStore.humanUpdataData.adjustFactor?.isEdit) {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      coefficient: projectStore.humanUpdataData.adjustFactor.marcketFactor * 1,
      rcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.adjustFactor.selectRows)
      ),
    };
    await csProject.constructAdjustmentCoefficient(apiData).then(res => {
      console.log('统一应用系数', res);
    });
  }
  if (projectStore.humanUpdataData.updataData) {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(projectStore.humanUpdataData.updataData)
      ),
    };
    await jieSuanDetail.changeRcjNewJieSuan(apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  message.success('应用成功!');
  projectStore.SET_HUMAN_UPDATA_DATA(null);
  // getHumanMachineData();
  projectRcjList();
  unifyData.disabled = true;
  setGlobalLoading(false);
};
const updateCurrentInfo = formData => {
  currentInfo.value.riskAmplitudeRangeMax = formData.riskAmplitudeRangeMax;
  currentInfo.value.riskAmplitudeRangeMin = formData.riskAmplitudeRangeMin;
  upDate(currentInfo.value, 'riskAmplitudeRangeMin');
};

const upDate = (row, field) => {
  let apiData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    sequenceNbr: row.sequenceNbr,
    constructProjectRcj: {},
    rcjDifferenceType:
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.rcjDifferenceType,
  };
  if (projectStore.currentStageInfo) {
    apiData.fqNum = projectStore.currentStageInfo?.num;
  }
  if (field === 'type') {
    apiData.constructProjectRcj.kind = getKind(row.type);
  } else if (field === 'ifDonorMaterial' && row.ifDonorMaterial === 1) {
    apiData.constructProjectRcj.ifDonorMaterial = row[field];
    apiData.constructProjectRcj.donorMaterialNumber = row.totalNumber;
  } else if (field === 'ifDonorMaterial' && row.ifDonorMaterial === 0) {
    apiData.constructProjectRcj.ifDonorMaterial = row[field];
  } else if (field === 'isDifference') {
    apiData.constructProjectRcj.isDifference = row[field];
  } else if (field === 'riskAmplitudeRangeMin') {
    apiData.constructProjectRcj.riskAmplitudeRangeMax =
      row.riskAmplitudeRangeMax;
    apiData.constructProjectRcj.riskAmplitudeRangeMin =
      row.riskAmplitudeRangeMin;
  } else {
    apiData.constructProjectRcj[field] = row[field];
  }
  console.log('修改人材机数据', apiData);
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  jieSuanDetail.changeRcjNewJieSuan(apiData).then(res => {
    if (res.status === 200) {
      console.log('修改人材机数据返回结果', res);
      isCurrent.value = row.sequenceNbr;
      if (field === 'riskAmplitudeRangeMin') {
        riskVisible.value = false;
        isEditCurrentData.value = false;
      }
      if (projectStore.currentTreeInfo?.originalFlag) {
        priceDifferenceAdjustmentMethodController(
          projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.rcjDifferenceType
        );
      }
      getInitList();
    }
  });
};

// =====================查找逻辑
let lookupVisible = ref(false);
const openLookup = event => {
  // console.log(event);
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};
let originalTableData = []; // 查找之前的源数据
const lookupConfig = reactive({
  columns: [
    {
      field: 'materialName',
      label: '名称',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'specification',
      label: '规格',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'materialCode',
      label: '编码',
      type: 'input',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'marketPrice',
      label: '市场价',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'totalNumber',
      label: '数量',
      type: 'input',
      value: '',
      showCondition: true,
      conditionVal: '',
      config: {
        allowClear: true,
      },
    },
  ],
  conditionType: '&&',
  tableData: tableData,
});
const lookupCallback = rows => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData, true);
  } else {
    tableData.value = rows;
  }
  nextTick(() => {
    const info = tableData.value && tableData.value[0];
    humanTable.value.setCurrentRow(info);
    currentInfo.value = info;
  });
};

const changeCurrentInfo = row => {
  if (row) {
    humanTable.value.setCurrentRow(row);
    currentInfo.value = row;
  }
};

const exportExcel = (dataType = '') => {
  const $table = humanTable.value;
  if (dataType !== 'all' && $table.getCheckboxRecords().length === 0) {
    message.info('请选择导出数据');
    return;
  }
  $table.exportData({
    filename: '人材机汇总导出报表',
    sheetName: 'Sheet1',
    type: 'xlsx',
    // types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
    // sheetMethod: sheetMethod, // 配置导出样式
    useStyle: true, //是否导出样式 // 如果没有设置这项 就不会调用sheetMethod方法
    isFooter: true, //是否导出表尾（比如合计）
    data: dataType === 'all' ? tableData.value : $table.getCheckboxRecords(),
    columnFilterMethod({ column, $columnIndex }) {
      return !($columnIndex === 0);
    },
  });
};

const loadPriceOperateHandler = () => {
  const { rcjDifferenceType, frequencyList } =
    projectStore.asideMenuCurrentInfo?.defaultFeeFlag || {};
  console.log(
    'projectStore.asideMenuCurrentInfo',
    projectStore?.asideMenuCurrentInfo,
    projectStore?.currentTreeInfo
  );
  const { num } = projectStore.currentStageInfo || {};
  const { key } = projectStore.asideMenuCurrentInfo || {};
  const { isStage, levelType, originalFlag } =
    projectStore.currentTreeInfo || {};

  updateOperateByName('batch-loadprice', info => {
    info.type = 'select';
    info.disabled = false;
    if (isOriginalFlag()) {
      info.options = [
        {
          type: 1,
          name: '批量载入结算价',
          kind: '1',
          isValid: true,
        },
        {
          type: 2,
          name: '基期价格批量载价',
          kind: '2',
          isValid: true,
        },
      ];
    } else {
      info.options = [
        {
          type: 3,
          name: '批量载入合同确认价',
          kind: '3',
          isValid: true,
        },
      ];
    }
    info.options.forEach(opt => {
      opt.isValid = true;
    });
    if (rcjDifferenceType === 3) {
      info.options[1].isValid = false;
    }
    if (
      rcjDifferenceType === 4 ||
      (frequencyList && frequencyList.length && !num) || // 有分期没有选择分期数
      (key === '0' && contractOriginalFlag.value) ||
      key === '20' || // 差价
      (isStage && levelType === 1 && key === '0') // 分期&&工程项目级别&合同内所有
    ) {
      info.options.forEach(opt => {
        opt.isValid = false;
      });
    }
  });
};
const loadPrice = item => {
  const { name, activeKind } = item;
  priceType.value = Number(activeKind);
  // reportModel.value = false;
  switch (name) {
    case 'batch-loadprice':
      typeModal.value = '批量载价';
      break;
    case 'loadprice-report':
      typeModal.value = '载价报告';
      break;
  }
  setTimeout(() => {
    reportModel.value = true;
    console.log('执行loadPrice', reportModel.value);
  }, 24);
};
const close = bol => {
  reportModel.value = false;
  console.log('执行close', reportModel.value);
};
const nextEdit = data => {
  propsData.value = data;
  typeModal.value = data.nextType;
  reportModel.value = true;
  if (typeModal.value === '载价报告') {
    getInitList();
  }
  console.log('执行nextEdit', reportModel.value);
};
provide('nextStep', nextEdit);
let adjustFactor = ref(false); //调整市场价系数
let marcketFactor = ref('1');
const selfCheck = (value, length, min, max) => {
  // length-小数点长度   min-最小值  max-最大值
  let newValue = value * 1 + '';
  if (newValue === '') return oldMarketFactor.value;
  if (newValue <= min || newValue > max) {
    newValue = oldMarketFactor.value;
    message.info('市场价系数输入范围为(0,1000])');
  }
  let after = newValue.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newValue = parseFloat(newValue).toFixed(length);
  }
  oldMarketFactor.value = newValue;
  return newValue;
};
const changeMarketFactor = () => {
  //调整市场价系数
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    coefficient: projectStore.humanUpdataData.adjustFactor.marcketFactor * 1,
    rcjList: JSON.parse(
      JSON.stringify(projectStore.humanUpdataData.adjustFactor.selectRows)
    ),
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  console.log('调整市场价系数', apiData);
  let apiName =
    projectStore.currentTreeInfo.levelType === 3
      ? 'unitAdjustmentCoefficient'
      : '';
  csProject[apiName](apiData).then(res => {
    if (res.status === 200) {
      console.log('调整市场价系数返回结果', res);
      getHumanMachineData();
    }
  });
};
const sureOrCancel = () => {
  let hasCheck =
    humanTable.value.getCheckboxRecords().length === 0 ? false : true;
  if (!hasCheck) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '请选中要调整的人材机数据行',
      confirm: () => {
        infoMode.hide();
      },
    });
  }
  if (!hasCheck) return;
  projectStore.SET_HUMAN_UPDATA_DATA({
    isEdit: true,
    name: 'unify-humanMachineSummary',
    updataData: projectStore.humanUpdataData?.updataData,
    adjustFactor: {
      isEdit: projectStore.currentTreeInfo.levelType === 1 ? true : false,
      marcketFactor: marcketFactor.value,
      selectRows: humanTable.value.getCheckboxRecords(),
    },
  });
  if (projectStore.currentTreeInfo.levelType === 3 && hasCheck) {
    changeMarketFactor();
  }
  adjustFactor.value = false;
};
let oldMarketFactor = ref('1'); //调整市场价系数旧值
const jsz = () => {
  marcketFactor.value = '1';
  oldMarketFactor.value = '1';
  adjustFactor.value = true;
};
/**
 * 设置主要材料显示隐藏
 */
const showSetMainMaterial = () => {
  let mainMaterial = operateList.value.find(
    item => item.name === 'set-main-materials'
  );
  mainMaterial.levelType =
    Number(projectStore.asideMenuCurrentInfo.key) === 7 ? [3] : [];
};

const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns();

let humanShowTableColumns = ref([]); // 选中子菜单数据需要展示的列
watch(
  () => [
    projectStore.asideMenuCurrentInfo,
    projectStore.currentTreeInfo.levelType,
  ],
  () => {
    if (
      projectStore.currentTreeInfo.levelType !== 2 &&
      projectStore.tabSelectName === '人材机调整' &&
      !projectStore.currentStageInfo
    ) {
      getInitColumns();
      initOperateList();
      if (projectStore.currentTreeInfo.levelType === 3) {
        findUnitProjectById();
      }
      if (projectStore.currentTreeInfo.levelType === 1) {
        jieSuanConstructProjectFq();
      }
      //侧边栏数据变化重新更新
      // getHumanMachineData();
      getInitList();
      selectData.value = [];
      if (['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo.key)) {
        updateValue();
      }
      // 动态表格列初始化设置
      // initColumns({
      //   columns: contractOriginalFlag.value
      //     ? humanShowTableColumns.value
      //     : humanTableColumns.value,
      //   pageName: 'htnrcjtz',
      // });
    } else if (
      projectStore.currentTreeInfo.levelType !== 2 &&
      projectStore.tabSelectName === '人材机调整'
    ) {
      initOperateList();
    }
  }
);
watch(
  () => projectStore.humanUpdataData,
  () => {
    if (projectStore.humanUpdataData && projectStore.humanUpdataData.isEdit) {
      unifyData.disabled = false;
    }
  }
);

watch(
  () => projectStore.currentStageInfo,
  () => {
    getInitColumns();
    initOperateList();
    unitRcjQuery();
  }
);
watch(
  () => projectStore.currentTreeInfo?.originalFlag,
  () => {
    initOperateList();
  }
);

onActivated(() => {
  loadPriceOperateHandler();
  initOperateList();
  insetBus(bus, projectStore.componentId, 'humanMachineSummary', async data => {
    operateList.value.find(item => item.name === 'rcj-adjustment').label =
      isDifference.value ? '人材机取消调差' : '人材机参与调差';
    if (data.name === 'batch-loadprice') {
      console.log('执行载价', data);
      if (data.activeKind) {
        loadPrice(data);
      } else {
        loadPriceOperateHandler();
      }
    }
    if (data.name === 'loadprice-report')
      console.log('载价报告'), loadPrice(data);
    if (data.name === 'market-price') console.log('调整市场价系数'), jsz();
    if (data.name === 'unify-humanMachineSummary') {
      isUse();
    }
    if (data.name === 'export-table') console.log('导出报表'), exportExcel();
    if (data.name === 'risk-range') riskVisible.value = true;
    if (data.name === 'batch-adjustment-material') materialVisible.value = true;
    if (data.name === 'set-main-materials') openSetMainMaterial();
    if (data.name === 'lookup') openLookup();
    if (data.name === 'mergeMaterials')
      console.log('合并材料'), openMergeMaterials();
    if (data.name === 'rcj-adjustment') {
      if (isDifference.value) {
        cancelRcjParticipateInAdjustment();
      } else {
        rcjParticipateInAdjustment();
      }
    }
    if (data.name === 'corresponding-item') {
      let isCurrentList = tableData.value.filter(
        item => isCurrent.value.sequenceNbr === item.sequenceNbr
      );
      console.log('sicurrentlist', isCurrentList);
      quotaHeaderData.value = isCurrentList[0];
      quotaPopupVisible.value = true;
    }
    if (data.name === 'settlement-adjustment-method') {
      console.log('结算调整法修改', data);
      if (data.activeKind) {
        if (projectStore.currentTreeInfo.levelType === 3) {
          priceDifferenceAdjustmentMethodController(data.activeKind);
        } else {
          constructPriceDifferenceAdjustmentMethodController(data.activeKind);
        }
      }
    }
  });
});
onMounted(() => {
  if (
    projectStore.currentTreeInfo.levelType !== 2 &&
    projectStore.tabSelectName === '人材机调整' &&
    projectStore.asideMenuCurrentInfo?.key === '0'
  ) {
    // tableData.value = [];
    // getHumanMachineData();
    getInitColumns();
    initOperateList();
    if (projectStore.currentTreeInfo.levelType === 3) {
      findUnitProjectById();
    }
    getInitList();
    if (['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo.key)) {
      updateValue();
    }
  }
  // getLoadStatus();
});

// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};

// 获取初始化表头列
const getInitColumns = () => {
  if (
    contractOriginalFlag.value &&
    projectStore.currentTreeInfo.levelType === 3
  ) {
    if (projectStore.asideMenuCurrentInfo.key === '0') {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(x =>
        x.type?.includes(0)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName: 'htnrcjtz0',
      });
    } else if (
      ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo.key) &&
      !projectStore?.currentStageInfo &&
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length ===
        0
    ) {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
        x =>
          (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
          x.adjustmentType?.includes(
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag.rcjDifferenceType
          ) &&
          x.stageType?.includes(0) &&
          x.titleType?.includes(0)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName:
          'htnrcjtz1_' +
          projectStore.asideMenuCurrentInfo?.defaultFeeFlag.rcjDifferenceType,
      });
    } else if (
      ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo.key) &&
      !projectStore?.currentStageInfo &&
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length > 0
    ) {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
        x =>
          (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
          x.adjustmentType?.includes(
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag.rcjDifferenceType
          ) &&
          x.stageType?.includes(1) &&
          x.titleType?.includes(1)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName:
          'htnrcjtz2_' +
          projectStore.asideMenuCurrentInfo?.defaultFeeFlag.rcjDifferenceType,
      });
      console.log('6666666666666', humanShowTableColumns);
    } else if (
      ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo.key) &&
      projectStore?.currentStageInfo &&
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length > 0
    ) {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
        x =>
          (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
          x.adjustmentType?.includes(
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag.rcjDifferenceType
          ) &&
          x.stageType?.includes(1) &&
          x.titleType?.includes(2)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName:
          'htnrcjtz3_' +
          projectStore.asideMenuCurrentInfo?.defaultFeeFlag.rcjDifferenceType,
      });
    } else if (
      ['8'].includes(projectStore.asideMenuCurrentInfo.key) &&
      !projectStore?.currentStageInfo &&
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length ===
        0
    ) {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
        x =>
          x.type?.includes(8) &&
          x.stageType?.includes(0) &&
          x.titleType?.includes(0)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName: 'htnrcjtz4',
      });
    } else if (
      ['8'].includes(projectStore.asideMenuCurrentInfo.key) &&
      !projectStore?.currentStageInfo &&
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length > 0
    ) {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
        x =>
          x.type?.includes(8) &&
          x.stageType?.includes(1) &&
          x.titleType?.includes(1)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName: 'htnrcjtz5',
      });
    } else if (
      ['8'].includes(projectStore.asideMenuCurrentInfo.key) &&
      projectStore?.currentStageInfo &&
      projectStore.asideMenuCurrentInfo?.defaultFeeFlag.frequencyList.length > 0
    ) {
      humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
        x =>
          x.type?.includes(8) &&
          x.stageType?.includes(1) &&
          x.titleType?.includes(2)
      );
      // 动态表格列初始化设置
      initColumns({
        columns: contractOriginalFlag.value
          ? humanShowTableColumns.value
          : humanTableColumns.value,
        pageName: 'htnrcjtz6',
      });
    }
  } else if (
    !contractOriginalFlag.value &&
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.asideMenuCurrentInfo.key !== '20'
  ) {
    humanShowTableColumns.value = humanTableColumns.value.filter(x =>
      x.classify?.includes(1)
    );
    console.log('5555555555555', humanShowTableColumns);
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz7',
    });
  } else if (
    !contractOriginalFlag.value &&
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.asideMenuCurrentInfo.key === '20'
  ) {
    humanShowTableColumns.value = humanTableColumns.value.filter(x =>
      x.classify?.includes(2)
    );
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz8',
    });
  } else if (
    projectStore.currentTreeInfo.levelType === 1 &&
    projectStore.asideMenuCurrentInfo.key === '0'
  ) {
    humanShowTableColumns.value = humanOriginalTableColumns.value.filter(x =>
      x.type?.includes(0)
    );
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz9',
    });
  } else if (
    ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo.key) &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    let JieSuanPriceAdjustmentMethodType =
      projectStore.currentTreeInfo.jieSuanRcjDifferenceTypeList?.filter(
        x =>
          x.rcjDifferenceType === Number(projectStore.asideMenuCurrentInfo.key)
      )[0]?.JieSuanPriceAdjustmentMethodType;
    humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
      x =>
        (x.type?.includes(1) || x.type?.includes(2) || x.type?.includes(3)) &&
        x.adjustmentType?.includes(JieSuanPriceAdjustmentMethodType) &&
        x.titleType?.includes(3)
    );
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz10_' + JieSuanPriceAdjustmentMethodType,
    });
  } else if (
    ['8'].includes(projectStore.asideMenuCurrentInfo.key) &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    let JieSuanPriceAdjustmentMethodType =
      projectStore.currentTreeInfo.jieSuanRcjDifferenceTypeList?.filter(
        x =>
          x.rcjDifferenceType === Number(projectStore.asideMenuCurrentInfo.key)
      )[0]?.JieSuanPriceAdjustmentMethodType;
    humanShowTableColumns.value = humanOriginalTableColumns.value.filter(
      x =>
        x.type?.includes(8) &&
        x.adjustmentType?.includes(JieSuanPriceAdjustmentMethodType) &&
        x.titleType?.includes(3)
    );
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz11_' + JieSuanPriceAdjustmentMethodType,
    });
  } else if (
    ['21'].includes(projectStore.asideMenuCurrentInfo.key) &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    humanShowTableColumns.value = humanTableColumns.value.filter(x =>
      x.classify?.includes(1)
    );
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz12',
    });
  } else if (
    ['20'].includes(projectStore.asideMenuCurrentInfo.key) &&
    projectStore.currentTreeInfo.levelType === 1
  ) {
    humanShowTableColumns.value = humanTableColumns.value.filter(x =>
      x.classify?.includes(2)
    );
    // 动态表格列初始化设置
    initColumns({
      columns: humanShowTableColumns.value,
      pageName: 'htnrcjtz13',
    });
  }
};

// 单期/多期设置成功回调
const updateAllData = () => {
  emits('updateMenuList');
  getInitList();
};

// 获取列表数据
const getInitList = () => {
  if (projectStore.currentTreeInfo.levelType === 3) {
    unitRcjQuery();
  } else {
    projectRcjList();
  }
};

onDeactivated(() => {
  console.log('onDeactivated');
  lookupVisible.value = false;
  window.removeEventListener('keydown', openLookup);
});

const getLoadStatus = () => {
  let apiData = {
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单项ID
  }
  let apiFun = loadApi.loadPriceStatus;
  if (isOriginalFlag()) {
    // 合同内特有参数
    apiFun = loadApi.loadPriceStatusOriginal;
  }
  apiFun(apiData).then(res => {
    console.log('++++++++++++++', res);
    // isSeeReport.disabled = false;
    if (res.result && res.result.includes(5)) {
      isLoad.disabled = true;
    } else if (res.result && res.result.includes(3)) {
      // isSeeReport.disabled = true;
    } else if (res.result && res.result.includes(4)) {
      feePro.isOnline().then(res => {
        console.log('判断是否有网------', res);
        if (res.result) {
          isLoad.disabled = false;
        } else {
          message.error('请连接网络后使用！');
        }
      });
    }
  });
};
const checkBoxIsShow = () => {
  tableData.value &&
    tableData.value.map(item => {
      //有父子级关系的父级二次解析，父级暂估，甲供，市场价锁定不可勾选
      if (item.markSum == 1 && (item.levelMark == 1 || item.levelMark == 2)) {
        item.checkIsShow = false;
      } else {
        item.checkIsShow = true;
      }
    });
};
const getKind = type => {
  let value;
  switch (type) {
    case '其他费':
      value = 0;
      break;
    case '人工费':
      value = 1;
      break;
    case '材料费':
      value = 2;
      break;
    case '机械费':
      value = 3;
      break;
    case '设备费':
      value = 4;
      break;
    case '主材费':
      value = 5;
      break;
    case '商砼':
      value = 6;
      break;
    case '砼':
      value = 7;
      break;
    case '浆':
      value = 8;
      break;
    case '商浆':
      value = 9;
      break;
    case '配比':
      value = 10;
      break;
  }
  return value;
};
const getType = type => {
  let value = '';
  switch (type) {
    case 0:
      value = '其他费';
      break;
    case 1:
      value = '人工费';
      break;
    case 2:
      value = '材料费';
      break;
    case 3:
      value = '机械费';
      break;
    case 4:
      value = '设备费';
      break;
    case 5:
      value = '主材费';
      break;
    case 6:
      value = '商砼';
      break;
    case 7:
      value = '砼';
      break;
    case 8:
      value = '浆';
      break;
    case 9:
      value = '商浆';
      break;
    case 10:
      value = '配比';
      break;
  }
  return value;
};
const getOldData = () => {
  tableData.value &&
    tableData.value.map(item => {
      oldData.value.push({
        sequenceNbr: item.sequenceNbr,
        marketPrice: item.marketPrice,
        ifDonorMaterial: item.ifDonorMaterial,
        donorMaterialNumber: item.donorMaterialNumber,
        ifProvisionalEstimate: item.ifProvisionalEstimate,
        ifLockStandardPrice: item.ifLockStandardPrice,
        jieSuanBasePriceF0: item.jieSuanBasePriceF0,
        jieSuanCurrentPriceF0: item.jieSuanCurrentPriceF0,
        jieSuanBasePrice: item.jieSuanBasePrice,
        jieSuanPrice: item.jieSuanPrice,
        riskAmplitudeRangeMin: item.riskAmplitudeRangeMin,
        jieSuanTaxRemoval: item.jieSuanTaxRemoval,
        jieSuanFee: item.jieSuanFee,
        remark: item.remark,
        materialName: item.materialName,
        type: item.type,
        specification: item.specification,
        unit: item.unit,
        totalNumber: item.totalNumber,
        dePrice: item.dePrice,
        isDifference: item.isDifference,
        jieSuanAdminRate: item.jieSuanAdminRate,
        producer: item.producer,
        manufactor: item.manufactor,
        markSum: item.markSum,
        qualityGrade: item.qualityGrade,
        brand: item.brand,
        deliveryLocation: item.deliveryLocation,
      });
    });
  console.log('getOldData', oldData.value);
};
const getHumanMachineData = () => {
  loading.value = true;
  let formData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    kind: Number(projectStore.asideMenuCurrentInfo?.key),
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    formData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    formData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  feePro
    .queryConstructRcjByDeId(formData)
    .then(res => {
      if (res.status === 200 && res.result && res.result.length > 0) {
        let num = 1;
        res.result &&
          res.result.map((item, index) => {
            item.dispNo = num++;
            item.type = getType(item.kind);
            item.donorMaterialNumber =
              Number(item.donorMaterialNumber) === 0
                ? ''
                : item.donorMaterialNumber;
            item.origindonorMaterialNum = item.donorMaterialNumber
              ? item.donorMaterialNumber
              : '0';
          });
        tableData.value = res.result;

        nextTick(() => {
          if (isCurrent.value && projectStore.currentTreeInfo.levelType === 3) {
            let isCurrentList = tableData.value.filter(
              item => isCurrent.value === item.sequenceNbr
            );
            humanTable.value.setCurrentRow(isCurrentList[0]);
          } else {
            humanTable.value.setCurrentRow(
              tableData.value && tableData.value[0]
            );
          }
        });

        checkBoxIsShow();
        if (projectStore.currentTreeInfo.levelType === 1) {
          getOldData();
          getSameUnit();
        }
        console.log('人材机汇总表格数据', tableData.value);
      } else {
        tableData.value = [];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const unitRcjQuery = () => {
  let apiData = {
    kind: Number(projectStore.asideMenuCurrentInfo?.key),
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId,
    unitId: projectStore.currentTreeInfo?.id,
    num: projectStore.currentStageInfo?.num,
  };
  console.log('获取列表数据参数', apiData);
  jieSuanDetail.unitRcjQuery(apiData).then(res => {
    console.log('res222222222', res);
    if (res.status === 200 && res.result) {
      let num = 1;
      jieSuanPriceTotal.value = 0;
      jieSuanPriceDifferencSum.value = 0;
      res.result &&
        res.result.map((item, index) => {
          jieSuanPriceTotal.value += Number(!item.isGray ? item.total || 0 : 0);
          jieSuanPriceDifferencSum.value += Number(
            item.jieSuanPriceDifferencSum || 0
          );
          item.dispNo = num++;
          item.type = getType(item.kind);
          // item.jieSuanFee = getFeeType(item.jieSuanFee);
          item.donorMaterialNumber =
            Number(item.donorMaterialNumber) === 0
              ? ''
              : item.donorMaterialNumber;
          item.origindonorMaterialNum = item.donorMaterialNumber
            ? item.donorMaterialNumber
            : '0';
        });
      tableData.value = res.result;
      isCurrent.value = res.result[0];
      checkBoxIsShow();
      originalTableData = xeUtils.clone(tableData.value, true);
    } else {
      tableData.value = [];
    }
  });
};

// 工程项目级别数据获取
const projectRcjList = () => {
  let apiData = {
    type:
      typeof projectStore.asideMenuCurrentInfo?.defaultFeeFlag === 'object'
        ? 1
        : projectStore.asideMenuCurrentInfo?.defaultFeeFlag === '0'
        ? 2
        : Number(projectStore.asideMenuCurrentInfo?.defaultFeeFlag),
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    kind: projectStore.asideMenuCurrentInfo?.key || null,
  };
  console.log('获取列表数据参数', apiData);
  jieSuanDetail.projectRcjList(apiData).then(res => {
    console.log('res33333333333', res);
    if (res.status === 200 && res.result) {
      let num = 1;
      jieSuanPriceTotal.value = 0;
      jieSuanPriceDifferencSum.value = 0;
      res.result &&
        res.result.map((item, index) => {
          jieSuanPriceTotal.value += Number(!item.isGray ? item.total || 0 : 0);
          jieSuanPriceDifferencSum.value += Number(
            item.jieSuanPriceDifferencSum || 0
          );
          item.dispNo = num++;
          item.type = getType(item.kind);
          // item.jieSuanFee = getFeeType(item.jieSuanFee);
          item.donorMaterialNumber =
            Number(item.donorMaterialNumber) === 0
              ? ''
              : item.donorMaterialNumber;
          item.origindonorMaterialNum = item.donorMaterialNumber
            ? item.donorMaterialNumber
            : '0';
        });
      tableData.value = res.result;
      nextTick(() => {
        if (isCurrent.value && projectStore.currentTreeInfo.levelType === 3) {
          let isCurrentList = tableData.value.filter(
            item => isCurrent.value === item.sequenceNbr
          );
          humanTable.value.setCurrentRow(isCurrentList[0]);
        } else {
          isCurrent.value = tableData.value[0];
          humanTable.value.setCurrentRow(tableData.value && tableData.value[0]);
        }
      });

      checkBoxIsShow();
      if (projectStore.currentTreeInfo.levelType === 1) {
        getOldData();
        getSameUnit();
      }
    }
  });
};
const getSameUnit = () => {
  let list = [];
  let addColorList = [];
  tableData.value &&
    tableData.value.map(item => {
      let otherSameUnit = tableData.value.filter(
        unit =>
          unit.materialCode === item.materialCode &&
          unit.materialName === item.materialName &&
          // unit.unitId === item.unitId &&
          unit.unit === item.unit &&
          unit.specification === item.specification &&
          Number(unit.dePrice) === Number(item.dePrice) &&
          unit.ifDonorMaterial == item.ifDonorMaterial &&
          unit.ifProvisionalEstimate == item.ifProvisionalEstimate &&
          unit.ifLockStandardPrice == item.ifLockStandardPrice &&
          unit.sequenceNbr !== item.sequenceNbr &&
          Number(unit.marketPrice) !== Number(item.marketPrice)
      );
      if (
        otherSameUnit &&
        otherSameUnit.length > 0 &&
        !addColorList.includes(item.sequenceNbr)
      ) {
        addColorList.push(item.sequenceNbr);
      }
    });
  tableData.value &&
    tableData.value.map(
      item =>
        (item.addColor = addColorList.includes(item.sequenceNbr) ? true : false)
    );
  console.log('addColorList', addColorList);
};
const cellTableStyle = ({ row, column }) => {
  if (column.field === 'marketPrice') {
    //工程项目级别人材机汇总八要素一致市场价不一致的数据标识不一样
    if (row.addColor) {
      return {
        color: '#059421',
        backgroundColor: 'rgba(22, 225, 83, 0.4)',
      };
    }
  }
};
const cellStyle = ({ row, column }) => {
  if (column.field === 'marketPrice') {
    //市场价高于定额价标红，低于的话标绿
    if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice > row.dePrice
    ) {
      return {
        color: '#D40C0C',
      };
    } else if (
      row.marketPrice >= 0 &&
      row.dePrice >= 0 &&
      row.marketPrice < row.dePrice
    ) {
      return {
        color: '#059421',
      };
    }
  }
};
const rowStyle = ({ row }) => {
  if (row.isGray) {
    if (row.field !== 'dispNo') {
      return {
        color: '#ACACAC',
      };
    }
  }
  if (row.priceDifferencSum > 0) {
    return {
      backgroundColor: '#EFEAFF',
    };
  }
  if (row.highlight) {
    return {
      backgroundColor: '#F9FFF8',
    };
  }
};
const CheckboxChange = (row, type) => {
  switch (type) {
    case 'markSum':
      row.checkIsShow = row.markSum === 1 ? false : true;
      break;
    case 'ifDonorMaterial':
      break;
    case 'ifProvisionalEstimate':
      break;
    case 'ifLockStandardPrice':
      row.cusTomIfLockStandardPrice = row.ifLockStandardPrice;
      break;
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    upDate(row, type);
  } else if (projectStore.currentTreeInfo.levelType === 1) {
    //工程项目级别
    row.isChange = true; //标识编辑行
    let upDateList = getPropData();
    if (upDateList && upDateList.length > 0) {
      projectStore.SET_HUMAN_UPDATA_DATA({
        isEdit: true,
        name: 'unify-humanMachineSummary',
        updataData: upDateList,
      });
    }
    if (type === 'ifDonorMaterial') {
      row.donorMaterialNumber =
        row.ifDonorMaterial === 1 ? row.totalNumber : '';
      humanTable.value.reloadRow(row, {});
    }
  }
};

const getCurrentIndex = (item, type) => {
  console.log('item', item, tableData.value);
  if (item) {
    tableData.value.map((n, index) => {
      if (n.sequenceNbr === item.sequenceNbr) {
        isCurrent.value = n;
      }
    });
  } else {
    isCurrent.value = tableData.value[0];
  }
  humanTable.value.setCurrentRow(isCurrent.value);
  console.log('==============', isCurrent);
};

const currentChange = ({ row }) => {
  //if (projectStore.currentTreeInfo.levelType === 1) {
  getCurrentIndex(row);
  setEditEnabled(row);
  //}
};

// 单位工程级别四种调整法修改
const priceDifferenceAdjustmentMethodController = methodType => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    clType: Number(projectStore.asideMenuCurrentInfo?.key),
    methodType: Number(methodType),
  };
  console.log('apiData', apiData);
  jieSuanDetail.priceDifferenceAdjustmentMethodController(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('调整成功');
      getInitList();
      emits('updateMenuList');
      setTimeout(() => {
        getInitColumns();
      }, 500);
      updateValue();
    }
  });
};

const updateValue = () => {
  updateOperateByName('settlement-adjustment-method', info => {
    if (projectStore.currentTreeInfo.levelType === 1) {
      info.value =
        projectStore.currentTreeInfo.jieSuanRcjDifferenceTypeList?.filter(
          x =>
            x.rcjDifferenceType ===
            Number(projectStore.asideMenuCurrentInfo.key)
        )[0]?.JieSuanPriceAdjustmentMethodType;
    } else {
      info.value =
        projectStore.asideMenuCurrentInfo?.defaultFeeFlag?.rcjDifferenceType;
    }
  });
};
// 工程项目级别四种调整法修改
const constructPriceDifferenceAdjustmentMethodController = methodType => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    clType: Number(projectStore.asideMenuCurrentInfo?.key),
    methodType: Number(methodType),
  };
  console.log('apiData', apiData);
  jieSuanDetail
    .constructPriceDifferenceAdjustmentMethodController(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        message.success('调整成功');
        projectStore.SET_IS_REFRESH_PROJECT_TREE(true);
        emits('updateMenuList');
        getInitColumns();
        getInitList();
        updateValue();
      }
    });
};

const rcjParticipateInAdjustment = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('人材机参与调差参数', apiData);
  jieSuanDetail.rcjParticipateInAdjustment(apiData).then(res => {
    console.log('人材机参与调差结果', res);
    if (res.status === 200 && res.result) {
      message.success('人材机参与调差设置成功');
      emits('updateMenuList');
    }
  });
};

// 是否编辑处理
const setEditEnabled = row => {
  if (row.isGray) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};

const riskOpen = row => {
  isEditCurrentData.value = true;
  riskVisible.value = true;
  currentInfo.value = row;
};

// 获取当前单位
const findUnitProjectById = () => {
  let apiData = {
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  jieSuanDetail.findUnitProjectById(apiData).then(res => {
    console.log('1111111111', res);
    isDifference.value = res.isDifference;
    operateList.value.find(item => item.name === 'rcj-adjustment').label =
      isDifference.value ? '人材机取消调差' : '人材机参与调差';
  });
};

// 取消人材机调差
const cancelRcjParticipateInAdjustment = () => {
  let apiData = {
    constructId:
      projectStore.currentTreeInfo.levelType === 1
        ? projectStore.currentTreeInfo?.id
        : projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  jieSuanDetail.cancelRcjParticipateInAdjustment(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('人材机取消调差设置成功');
      emits('updateMenuList');
    }
  });
};

const jieSuanConstructProjectFq = () => {
  let apiData = {
    constructId:
        projectStore.currentTreeInfo.levelType === 1
            ? projectStore.currentTreeInfo?.id
            : projectStore.currentTreeGroupInfo?.constructId,
  }
  console.log('新增接口', apiData)
  jieSuanDetail.jieSuanConstructProjectFq(apiData).then(res => {
    console.log('这儿是不是进来1111111。。。。', res)
    if (res.status === 200 && res.result) {
      operateList.value.find(item => item.name === 'rcj-adjustment').hidden = res.result;
    }
  })
}

const initOperateList = () => {
  if (projectStore.currentTreeInfo.levelType === 3) {
    operateList.value.forEach(item => {
      if (item.label === '统一应用') {
        item.hidden = true;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '统一应用') {
        item.hidden = false;
      }
    });
  }
  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      !projectStore.currentTreeInfo.isStage &&
      ['0', '1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.isStage &&
      projectStore.asideMenuCurrentInfo?.key === '0') ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      ((!projectStore.stageCount &&
        projectStore.asideMenuCurrentInfo?.key !== '8') ||
        (projectStore.stageCount &&
          ['0', '1', '2', '3'].includes(
            projectStore.asideMenuCurrentInfo?.key
          ) &&
          !projectStore.currentStageInfo)))
  ) {
    console.log('进来不5555555555555');
    operateList.value.forEach(item => {
      if (item.label === '批量选择调差材料') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '批量选择调差材料') {
        item.hidden = true;
      }
    });
  }
  // if (
  //   (projectStore.currentTreeInfo.levelType === 1 &&
  //     projectStore.asideMenuCurrentInfo?.key === '21') ||
  //   (projectStore.currentTreeInfo.levelType === 3 &&
  //     !projectStore.currentTreeInfo.originalFlag &&
  //     projectStore.asideMenuCurrentInfo?.key !== '20')
  // ) {
  //   operateList.value.forEach(item => {
  //     if (item.label === '调整市场价系数') {
  //       item.hidden = false;
  //     }
  //   });
  // } else {
  //   operateList.value.forEach(item => {
  //     if (item.label === '调整市场价系数') {
  //       item.hidden = true;
  //     }
  //   });
  // }
  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      ['20', '21'].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      !projectStore.currentTreeInfo.originalFlag)
  ) {
    operateList.value.forEach(item => {
      if (item.label === '人材机参与调差' || item.label === '人材机取消调差') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '人材机参与调差' || item.label === '人材机取消调差') {
        item.hidden = true;
      }
    });
  }

  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      ['0', '1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      !projectStore.currentStageInfo &&
      ['0', '1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key))
  ) {
    operateList.value.forEach(item => {
      if (item.label === '批量选择调差材料') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '批量选择调差材料') {
        item.hidden = true;
      }
    });
  }

  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      !projectStore.currentStageInfo &&
      ['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key))
  ) {
    operateList.value.forEach(item => {
      if (
        item.label === '风险幅度范围' ||
        item.label === '四种结算人材机调整法'
      ) {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (
        item.label === '风险幅度范围' ||
        item.label === '四种结算人材机调整法'
      ) {
        item.hidden = true;
      }
    });
  }

  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      ['1', '2', '3', '8', '20'].includes(
        projectStore.asideMenuCurrentInfo?.key
      )) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      ((projectStore.currentTreeInfo.originalFlag &&
        !projectStore.currentStageInfo &&
        (['8'].includes(projectStore.asideMenuCurrentInfo?.key) ||
          (['1', '2', '3'].includes(projectStore.asideMenuCurrentInfo?.key) &&
            projectStore.asideMenuCurrentInfo?.defaultFeeFlag
              .rcjDifferenceType !== 4))) ||
        (!projectStore.currentTreeInfo.originalFlag &&
          ['20'].includes(projectStore.asideMenuCurrentInfo?.key))))
  ) {
    operateList.value.forEach(item => {
      if (item.label === '价差取费设置') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '价差取费设置') {
        item.hidden = true;
      }
    });
  }

  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      ['1', '2', '3', '8', '20'].includes(
        projectStore.asideMenuCurrentInfo?.key
      )) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      ((projectStore.currentTreeInfo.originalFlag &&
        !projectStore.currentStageInfo &&
        ['1', '2', '3', '8'].includes(
          projectStore.asideMenuCurrentInfo?.key
        )) ||
        (!projectStore.currentTreeInfo.originalFlag &&
          ['20'].includes(projectStore.asideMenuCurrentInfo?.key))))
  ) {
    operateList.value.forEach(item => {
      if (item.label === '批量设置结算除税系数') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '批量设置结算除税系数') {
        item.hidden = true;
      }
    });
  }

  if (
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.currentTreeInfo.originalFlag &&
    !projectStore.currentStageInfo &&
    ['0', '2'].includes(projectStore.asideMenuCurrentInfo?.key)
  ) {
    operateList.value.forEach(item => {
      if (item.label === '自动过滤调差材料') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '自动过滤调差材料') {
        item.hidden = true;
      }
    });
  }

  if (
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.currentTreeInfo.originalFlag &&
    projectStore.stageCount &&
    !projectStore.currentStageInfo &&
    ['1', '2', '3', '8'].includes(projectStore.asideMenuCurrentInfo?.key)
  ) {
    operateList.value.forEach(item => {
      if (item.label === '单期/多期调差设置') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '单期/多期调差设置') {
        item.hidden = true;
      }
    });
  }

  if (
    projectStore.currentTreeInfo.levelType === 3 &&
    projectStore.currentTreeInfo.originalFlag &&
    projectStore.stageCount &&
    !projectStore.currentStageInfo &&
    ['0'].includes(projectStore.asideMenuCurrentInfo?.key)
  ) {
    operateList.value.forEach(item => {
      if (item.label === '分期量查看') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '分期量查看') {
        item.hidden = true;
      }
    });
  }

  if (
    (projectStore.currentTreeInfo.levelType === 1 &&
      !['0', '20'].includes(projectStore.asideMenuCurrentInfo?.key)) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      projectStore.currentTreeInfo.originalFlag &&
      ((projectStore.stageCount && projectStore.currentStageInfo) ||
        (!projectStore.stageCount &&
          projectStore.asideMenuCurrentInfo?.key !== '0'))) ||
    (projectStore.currentTreeInfo.levelType === 3 &&
      !projectStore.currentTreeInfo.originalFlag &&
      projectStore.asideMenuCurrentInfo?.key !== '20')
  ) {
    operateList.value.forEach(item => {
      if (item.label === '载价') {
        item.hidden = false;
      }
    });
  } else {
    operateList.value.forEach(item => {
      if (item.label === '载价') {
        item.hidden = true;
      }
    });
  }
};

// 定位方法
const posRow = sequenceNbr => {
  console.log('人材机汇总定位', sequenceNbr);
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  getInitList();
  setTimeout(() => {
    getCurrentIndex({ sequenceNbr });
  }, 800);
  // currentInfo.value = { sequenceNbr };
};

defineExpose({
  posRow,
  selectData,
  getInitList,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  // height: 40px;
  padding: 0 10px;
}
.table-content {
  width: 100%;
  // min-height: 450px !important;
  height: calc(100%);
  background: white;
  :deep(.background-red) {
    color: #2a2a2a;
    background: #de3f3f;
  }
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }
}

::v-deep(.ant-empty) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(25%, -25%);
}
.price-list {
  position: relative;
  margin-right: 18px;
  span {
    display: inline-block;
    font-size: 14px;
    color: #2a2a2a;
    padding: 0 10px;
  }
  .line {
    color: #007aff;
    width: 1px;
  }
}
.price-list {
  position: relative;
  margin-right: 18px;
  span {
    display: inline-block;
    font-size: 14px;
    color: #2a2a2a;
    padding: 0 10px;
  }
  .line {
    color: #007aff;
    width: 1px;
  }
}
.adjustFactorMoadl {
  .title {
    font-size: 14px;
  }
  div {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    // padding: 20px 0px 20px 5px;
    // border: 1px solid #6666;
    .ant-input {
      width: 78%;
    }
    span {
      width: 21%;
      margin: auto;
    }
  }
  .footor {
    display: flex;
    justify-content: space-between;
    width: 150px;
    margin: 10px auto 0;
  }
}
</style>
