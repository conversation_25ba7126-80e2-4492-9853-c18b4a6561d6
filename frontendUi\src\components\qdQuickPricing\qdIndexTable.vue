<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2024-04-15 16:51:03
 * @LastEditors: renmingming
 * @LastEditTime: 2024-05-29 15:10:16
-->
<template>
  <div class="table-container-qdIndex">
    <vxe-table
      ref="vexTable"
      :data="tableData.list"
      @cell-dblclick="dbClick"
      height="100%"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      @scroll="getScroll"
      :scroll-y="{ scrollToTopOnChange: false, enabled: true }"
      :column-config="{ resizable: true }"
      :checkStrictly="false"
      :row-class-name="rowClassName"
      :checkbox-config="{
        showHeader: false,
      }"
      :span-method="colspanMethod"
      :tree-config="{
        rowField: 'id',
        parentField: 'parentId',
        line: true,
        showIcon: true,
      }"
      show-overflow="title"
    >
      <!-- :tree-config="{
          rowField: 'id',
          parentField: 'parentId',
          line: true,
          showIcon: true,
          expandAll: true,
        }" -->
      <!-- tree-node  -->
      <vxe-column
        field="quotaCode"
        tree-node
        width="100"
        align="left"
        title="编码"
      >
        <template #default="{ row }">
          <span v-if="!row.quotaCode" class="tree-title">{{
            row.jobContent
          }}</span>
          <span v-else>{{ row.quotaCode }}</span>
        </template>
      </vxe-column>
      <vxe-column field="quotaName" align="left" title="名称"></vxe-column>
      <vxe-column field="unit" title="单位"></vxe-column>
      <vxe-column field="price" title="单价"></vxe-column>
    </vxe-table>
  </div>
</template>
<script setup>
import { globalData, dbClick } from './status.js';
import { watch, ref, reactive, computed, watchEffect, nextTick } from 'vue';
import csProject from '@/api/csProject';
import { projectDetailStore } from '@/store/projectDetail.js';

let vexTable = ref();
const store = projectDetailStore();
let tableData = reactive({
  list: [],
});

const getTableList = async () => {
  let postData = {
    guideLibraryModel: { releaseYear: store.deType },
    baseListModel: JSON.parse(
      JSON.stringify({
        ...globalData.editQD,
        sequenceNbr: globalData.editQD?.standardId,
      })
    ),
  };
  const res = await csProject.qdGuideDeList(postData);
  console.log('🚀 ~ 清单索引:', res);
  const list = addIdAndParentId(res.result, 1);
  console.log('🚀 ~ qdGuideDeList ~ list:', list);
  tableData.list = list;
  nextTick(() => {
    vexTable.value.setAllTreeExpand(true);
  });
};

const addIdAndParentId = (data, parentId = null) => {
  return data.map((node, index) => ({
    id: `${parentId ? parentId + '-' : ''}${index + 1}`, // 构造唯一的id
    parentId,
    ...node,
    children:
      node.children && node.children.length > 0
        ? addIdAndParentId(
            node.children,
            `${parentId ? parentId + '-' : ''}${index + 1}`
          )
        : [],
  }));
};

const rowClassName = ({ row }) => {
  if (row.parentId == 1) {
    return 'row-tree-title';
  }
};

const colspanMethod = ({ row, _rowIndex, columnIndex }) => {
  if (row.parentId == 1) {
    if (columnIndex === 0) {
      return { rowspan: 1, colspan: 5 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
};

watchEffect(() => {
  if (globalData.editQD?.sequenceNbr) {
    getTableList();
  } else {
    globalData.tableData = [];
  }
});
</script>
<style lang="scss">
.table-container-qdIndex {
  height: 100%;
  .vxe-table--render-default
    .vxe-body--column.col--ellipsis
    > .vxe-cell
    .vxe-tree-cell {
    display: flex;
    justify-content: flex-end;
    // .vxe-checkbox--icon{
    //   color:rgba(185, 185, 185, 1);
    // }
  }
  .row-tree-title .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    display: flex;
    justify-content: flex-start;
  }
  .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    color: rgba(185, 185, 185, 1);
  }
  .vxe-table--render-default
    .is--checked.vxe-cell--checkbox
    .vxe-checkbox--icon {
    color: var(--vxe-primary-color);
  }
  .vxe-table {
    .vxe-tree--btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      left: -4px;
    }
    .row-tree-title {
      background-color: rgba(232, 239, 255, 1);
    }
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }

    .vxe-cell--tree-node {
      .rotate90 {
        transform: rotate(0);
      }
      .vxe-icon-caret-right:before {
        content: '+';
        display: contents;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(50%, 50%);
      }
      &.is--active {
        .vxe-icon-caret-right:before {
          content: '-';
        }
      }
    }

    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 1),
        rgba(185, 185, 185, 1)
      ),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image: linear-gradient(
        rgba(185, 185, 185, 1),
        rgba(185, 185, 185, 1)
      ),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--body {
    // border-collapse: collapse;
  }
}
</style>
