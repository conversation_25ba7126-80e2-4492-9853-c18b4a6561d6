<!--
 * @Descripttion: 复用合同清单
 * @Author: renmingming
 * @Date: 2024-03-06 18:56:51
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-18 14:41:25
-->
<template>
  <commonModal
    className="dialog-comm"
    :width="992"
    v-model:modelValue="show"
    title="复用合同清单"
  >
    <div class="association">
      <div class="association-tree">
        <a-tree
          v-if="treeData.length"
          default-expand-all
          :tree-data="treeData"
          :fieldNames="{
            children: 'children',
            title: 'name',
            key: 'id',
          }"
          :selectedKeys="selectedKeys"
          @select="treeSelect"
        >
        </a-tree>
      </div>
      <div class="association-content">
        <div class="association-content-search">
          <a-checkbox v-model:checked="queryParams.difference" @change="getList"
            >量差范围超过：</a-checkbox
          >
          <div style="font-size: 14px; color: #000; flex: 1">
            -&nbsp;<a-input-number
              :min="0"
              :controls="false"
              style="width: 40px"
              v-model:value="quantityInfo.quantityDifferenceRangeMin"
            />
            % &nbsp;～ +&nbsp;<a-input-number
              :min="0"
              :controls="false"
              style="width: 40px"
              v-model:value="quantityInfo.quantityDifferenceRangeMax"
            />&nbsp;%
          </div>
          <a-input
            style="margin-right: 12px; width: 370px"
            v-model:value="queryParams.qdName"
            placeholder="请输入名称/编码/关键字"
          />
          <a-button type="primary" @click="getList">查询</a-button>
        </div>
        <div class="association-content-table">
          <div class="table-content">
            <vxe-table
              ref="vexTable"
              class="table-edit-common"
              keep-source
              :column-config="{ resizable: true }"
              :tree-config="{
                transform: true,
                rowField: 'sequenceNbr',
                parentField: 'parentId',
                line: true,
                showIcon: true,
                expandAll: true,
                iconOpen: 'vxe-icon-caret-down',
                iconClose: 'vxe-icon-caret-right',
              }"
              height="345"
              :data="tableData"
              :scroll-y="{ enabled: false }"
              :row-config="{ keyField: 'sequenceNbr' }"
              header-align="center"
              :checkbox-config="{
                checkRowKeys: checkRowKeys,
                reserve: true,
                checkStrictly: true,
                visibleMethod: ({ row }) => row.kind === '03',
              }"
              @checkbox-change="selectChangeEvent"
            >
              <vxe-column type="checkbox" width="40"></vxe-column>
              <vxe-column
                type="seq"
                field="index"
                title="序号"
                width="45"
                align="center"
              >
                <template #default="{ row, rowIndex, $rowIndex }">
                  <div class="multiple-select">
                    {{ $rowIndex + 1 }}
                  </div>
                </template>
              </vxe-column>
              <vxe-column
                field="bdCode"
                align="left"
                tree-node
                width="170"
                title="编码"
              >
                <template #default="{ row, column }">
                  <!-- <i
                    v-if="row.displaySign === 1 && tableData.length > 1"
                    class="vxe-icon-caret-down"
                  ></i>
                  <i
                    v-if="row.displaySign === 2"
                    class="vxe-icon-caret-right"
                  ></i> -->
                  <span class="code"
                    >{{ row.pageFr === 'fbfx' ? row.bdCode : row.fxCode }}
                    {{ row.redArray?.length > 0 ? row.redArray : '' }}</span
                  >
                </template>
              </vxe-column>
              <vxe-column
                field="bdName"
                width="100"
                title="名称"
                align="center"
              >
                <template #default="{ row, column }">
                  {{ row.pageFr === 'fbfx' ? row.bdName : row.name }}
                </template>
              </vxe-column>
              <vxe-column field="projectAttr" title="项目特征" align="center">
              </vxe-column>
              <vxe-column field="unit" title="单位" align="center">
              </vxe-column>
              <vxe-column field="quantity" title="结算工程量" align="center">
              </vxe-column>
              <vxe-column field="backPrice" title="合同单价" align="center">
              </vxe-column>
              <vxe-column
                field="backQuantity"
                title="合同工程量"
                align="center"
              >
              </vxe-column>
              <vxe-column
                field="quantityDifference"
                title="量差"
                align="center"
              >
              </vxe-column>
              <vxe-column
                field="quantityDifferenceProportion"
                title="量差比例"
                align="center"
              >
              </vxe-column>
            </vxe-table>
          </div>

          <!-- </frameSelect> -->
        </div>
        <div class="multiplex-rule">
          <div class="list">
            <span class="name">清单复用规则：</span>
            <a-radio-group v-model:value="quantityInfo.qdReuseRule">
              <a-radio
                :style="radioStyle"
                v-for="item in qdOptions"
                :value="item.value"
                >{{ item.name }}</a-radio
              >
            </a-radio-group>
          </div>
          <div class="list">
            <span class="name">工程量复用规则：</span>
            <a-radio-group
              v-model:value="quantityInfo.quantityReuseRule"
              @change="radioChange"
            >
              <a-radio
                :style="radioStyle"
                v-for="item in gclOptions"
                :value="item.value"
                :disabled="!queryParams.difference && item.value === 1"
                >{{ item.name }}</a-radio
              >
            </a-radio-group>
          </div>
        </div>
      </div>
    </div>
    <div class="list-btn">
      <a-button @click="close" style="margin-right: 26px">关闭</a-button>
      <a-button
        type="primary"
        :disabled="!checkRowKeys.length"
        @click="onHandleConfirm"
        >确认</a-button
      >
    </div>
  </commonModal>
</template>

<script setup>
import { ref, toRaw, reactive, computed, nextTick, watch } from 'vue';
import jsApi from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import xeUtils from 'xe-utils';
const projectStore = projectDetailStore();
const emit = defineEmits(['successCallback']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const show = computed({
  get: () => props.visible,
  set: val => {
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      open();
    }
  }
);
// 选中key
let checkRowKeys = computed(() => {
  return checkRow.value.map(item => item.sequenceNbr);
});
//选中数据行，虚拟滚动选择需要
let checkRow = ref([]);
// 选择事件
const selectChangeEvent = ({ checked, row, $table }) => {
  console.log(checked, row);
  if (checked) {
    checkRow.value.push(row);
  } else {
    const index = checkRow.value.findIndex(
      item => item.sequenceNbr === row.sequenceNbr
    );
    console.log(index);
    if (index >= 0) {
      checkRow.value.splice(index, 1);
    }
  }
  console.log(checkRowKeys.value);
};
const radioStyle = reactive({
  display: 'flex',
  height: '24px',
  lineHeight: '24px',
});
const qdOptions = [
  {
    value: 1,
    name: '只复制清单',
  },
  {
    value: 2,
    name: '清单和组价全部复制',
  },
];
const gclOptions = [
  {
    value: 1,
    name: '量差幅度以外的工程量',
  },
  {
    value: 2,
    name: '工程量全部复制',
  },
  {
    value: 3,
    name: '工程量为0',
  },
];
let treeData = ref([]);
let quantityInfo = ref({
  constructId: projectStore.currentTreeGroupInfo?.constructId,
  singleId: '',
  unitId: '',
  reuseSingleId: projectStore.currentTreeGroupInfo?.singleId,
  reuseUnitId: projectStore.currentTreeInfo?.id,
  qdReuseRule: 1,
  quantityReuseRule: 2,
  quantityDeductFlag: false,
  fbfxQdArr: [],
  csxmQdArr: [],
  quantityDifferenceRangeMin: 15,
  quantityDifferenceRangeMax: 15,
});
let queryParams = ref({
  constructId: projectStore.currentTreeGroupInfo?.constructId,
  singleId: '',
  unitId: '',
  qdName: '',
  quantityDifferenceRangeMin: 15,
  quantityDifferenceRangeMax: 15,
  difference: false,
});

let currentTreeNode = ref(null);
let selectedKeys = ref([]); //选中key

// 数选择
const treeSelect = (keys, e) => {
  currentTreeNode.value = e.node;
  selectedKeys.value = keys;

  getList();
};
const tableData = ref([]);
const showTipConfirm = () => {
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText: '是否将复用部分工程量在原清单中扣除',
    confirm: () => {
      quantityInfo.value.quantityDeductFlag = true;
      submitHandler();
      infoMode.hide();
    },
    close: () => {
      quantityInfo.value.quantityDeductFlag = false;
      submitHandler();
      infoMode.hide();
    },
  });
};
const showSuccessConfirm = () => {
  infoMode.show({
    isSureModal: true,
    iconType: 'icon-querenshanchu',
    infoText: '您已成功复用合同清单',
    confirmText: '继续复用',
    showCustom: {
      name: '关闭',
      callBack: () => {
        show.value = false;
        infoMode.hide();
      },
    },
    confirm: () => {
      infoMode.hide();
    },
    close: () => {
      show.value = false;
      infoMode.hide();
    },
  });
};
const showWarnConfirm = () => {
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText:
      '合同内采用的是分期调差，合同外复用部分工程量，如需在原清单中扣减，请手动操作',
    confirm: () => {
      quantityInfo.value.quantityDeductFlag = false;
      submitHandler();
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};

const radioChange = e => {
  if (e.target.value === 1) {
    // showTipConfirm();
  } else {
    quantityInfo.value.quantityDeductFlag = false;
  }
};

/**
 * 保存提交
 */
const onHandleConfirm = () => {
  if (
    quantityInfo.value.quantityReuseRule === 1 &&
    checkRow.value.find(item => item.kind === '03' && item.stageType === 2)
  ) {
    return showWarnConfirm();
  }
  if (
    quantityInfo.value.quantityReuseRule === 1 &&
    !quantityInfo.value.quantityDeductFlag
  ) {
    return showTipConfirm();
  }
  submitHandler();
};

const submitHandler = () => {
  let params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    reuseConstructId: projectStore.currentTreeGroupInfo?.constructId,
    reuseSingleId: currentTreeNode.value.parent.node.id,
    reuseUnitId: currentTreeNode.value.dataRef.id,
    csxmQdArr: checkRow.value
      .filter(item => item.frType === 'csxm')
      .map(item => item.sequenceNbr),
    fbfxQdArr: checkRow.value
      .filter(item => item.frType === 'fbfx')
      .map(item => item.sequenceNbr),
  };
  Object.assign(quantityInfo.value, params);
  params = JSON.parse(JSON.stringify(quantityInfo.value));
  params.quantityDifferenceRangeMin = queryParams.value.difference
    ? params.quantityDifferenceRangeMin * -1
    : '';
  params.quantityDifferenceRangeMax = queryParams.value.difference
    ? Number(params.quantityDifferenceRangeMax)
    : '';

  console.log(params);
  jsApi.handleReuseContractQd(params).then(res => {
    emit('successCallback');
    showSuccessConfirm();
  });
};
const vexTable = ref();

// 获取数数据
const getTreeData = async () => {
  const res = await jsApi.queryProjectTree({
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  });
  treeData.value = xeUtils.toArrayTree(res.result || []);
  let defineKey = defineKeyHandler(treeData.value);
  selectedKeys.value = defineKey ? [defineKey] : [];
};
// 处理默认选中key值
const defineKeyHandler = treeData => {
  for (let item of treeData) {
    if (item.levelType === 3) {
      currentTreeNode.value.dataRef = item;
      return item.id;
    }
    if (item.children) {
      currentTreeNode.value = { parent: { node: item }, dataRef: null };
      return defineKeyHandler(item.children);
    }
  }
  currentTreeNode.value = null;
  return '';
};
const getList = () => {
  checkRow.value = [];
  nextTick(() => {
    vexTable.value?.clearCheckboxRow();
    vexTable.value?.clearCheckboxReserve();
  });
  if (currentTreeNode.value.dataRef.levelType !== 3) {
    tableData.value = [];
    return;
  }
  queryParams.value.constructId =
    projectStore.currentTreeGroupInfo?.constructId;
  queryParams.value.singleId = currentTreeNode.value.parent.node.id;
  queryParams.value.unitId = currentTreeNode.value.dataRef.id;
  const params = JSON.parse(JSON.stringify(queryParams.value));
  params.quantityDifferenceRangeMin = queryParams.value.difference
    ? params.quantityDifferenceRangeMin * -1
    : '';
  params.quantityDifferenceRangeMax = queryParams.value.difference
    ? Number(params.quantityDifferenceRangeMax)
    : '';
  jsApi
    .queryReuseContractQdData(params)
    .then(res => {
      const { fbfx, csxm } = res.result;
      const fbfxList = fbfx.map(item => {
        item.frType = 'fbfx';
        return item;
      });
      const csxmList = csxm.map(item => {
        item.frType = 'csxm';
        return item;
      });
      tableData.value = [...fbfxList, ...csxmList].map((item, index) => {
        item.index = index + 1;
        delete item.children;
        delete item.parent;
        delete item.prev;
        delete item.next;
        return item;
      });
      nextTick(() => {
        vexTable.value?.setAllTreeExpand(true);
      });
      console.log(tableData.value, params);
    })
    .catch(err => {
      console.log(err);
    });
};
const rowClassName = ({ row }) => {
  const originalDataClass = '';
  if (row.kind === '0') {
    return `row-unit ${originalDataClass}`;
  } else if (row.kind === '01' || row.kind === '02') {
    return `row-sub ${originalDataClass}`;
  } else if (row.kind === '03') {
    return `row-qd ${originalDataClass}`;
  }
  return originalDataClass;
};

const reset = () => {
  Object.assign(quantityInfo.value, {
    qdReuseRule: 1,
    quantityReuseRule: 2,
    quantityDeductFlag: false,
    fbfxQdArr: [],
    csxmQdArr: [],
  });

  Object.assign(queryParams.value, {
    qdName: '',
    difference: false,
    quantityDifferenceRangeMin: 15,
    quantityDifferenceRangeMax: 15,
  });
};

const open = async () => {
  reset();
  await getTreeData();
  getList();
};
const close = () => {
  show.value = false;
};
</script>
<style lang="scss" scoped>
.multiplex-rule {
  display: flex;
  padding: 12px 25px;
  border: 1px solid #b9b9b9;
  border-top: none;
  .list {
    display: flex;
    &:first-child {
      margin-right: 70px;
    }
    span {
      display: inline-block;
      font-size: 14px;
      white-space: nowrap;
      color: #2a2a2a;
    }
  }
}
.association {
  display: flex;
  &-tree {
    width: 160px;
    padding: 10px;
    border: 1px solid #b9b9b9;
  }
  &-content {
    flex: 1;
    margin-left: 12px;
    &-search {
      display: flex;
      align-items: center;
      padding: 9px 8px;
      border: 1px solid #b9b9b9;
    }
    &-table {
      margin-top: 11px;
    }
  }
}
.list-btn {
  margin-top: 20px;
  text-align: center;
}
.table-content {
  height: 100%;
  .code {
    line-height: 16px;
    min-height: 16px;
    display: inline-block;
    width: 100%;
    padding-left: 13px;
  }
  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  :deep(.vxe-table--body) {
    border-collapse: collapse;
  }
  // :deep(.check-start){
  //   border-top:2px solid #287CFA ;
  // }
  // :deep(.check-end){
  //   border-bottom:2px solid #287CFA ;
  // }

  :deep(.vxe-table--border-line) {
    border: var(--vxe-table-border-width) solid #b9b9b9;
  }
  :deep(.vxe-table) {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: absolute;
      top: 0px;
      left: 17px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: '-';
    }
    .vxe-icon-caret-right:before {
      content: '+';
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }
}
</style>
