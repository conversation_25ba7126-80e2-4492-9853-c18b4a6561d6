/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-02-02 10:35:04
 * @LastEditors: renmingming
 * @LastEditTime: 2024-10-09 09:54:07
 */
import infoMode from '@/plugins/infoMode';
import gSdetailApi from '@/gaiSuanProject/api/projectDetail.js';
import detailApi from '@/api/projectDetail.js';
import shApi from '@/api/shApi.js';
import jsApi from '@/api/jiesuanApi.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useVirtualList } from '@/hooks/useVirtualList';
import { message } from 'ant-design-vue';
import {
  computed,
  nextTick,
  reactive,
  ref,
  watchEffect,
  watch,
  onDeactivated,
} from 'vue';
import {
  everyNumericHandler,
  quantityExpressionHandler,
  addHierarchy,
} from '@/utils/index';
import xeUtils from 'xe-utils';
import { globalData } from '@/components/qdQuickPricing/status.js';

// vexTable.value: 表格ref
// codeField：编码字段名
// nameField: 名称字段名
// operateList: 操作列表
// resetCellData 重置当前单元格方法

export const useSubItem = ({
  operateList,
  vexTable,
  codeField = 'bdCode',
  nameField = 'name',
  frameSelectRef = null,
  projectType = 'ys',
  pageType,
  resetCellData = () => {},
  checkUnit = () => {},
  emits = () => {},
  updateConstructRcj = () => {},
  api = {
    updateData: detailApi.updateFbData,
    getList: detailApi.queryBranchDataByFbIdV1,
  },
}) => {
  const isYSSH = projectType === 'yssh'; // 是否预算审核
  const isFBFX = codeField === 'bdCode'; // 是否分部分项
  const projectStore = projectDetailStore();
  const isJieSuan = projectStore.type === 'jieSuan'; // 是否结算
  let currentInfo = ref();
  let feeFileList = ref([]);
  let djgcFileList = ref([]); //单价构成文件列
  let tableData = ref([]);
  let originalTableData = ref([]); // 表格原始数据
  let loading = ref(false);
  let page = ref(1);
  let limit = ref(300000);
  let lockFlag = ref(0); // 整体锁定状态 0 不锁定 1 锁定
  let addDataSequenceNbr = ref('');
  let isIndexAddInfo = ref(false); // 是否从索引页面添加数据
  let selectData = ref(null);
  let AnnotationsRefList = ref({});
  let AnnotationsCurrent = ref(null);
  //多单位------
  let addCurrentInfo = ref(); // 多单位选择时选中的清单数据
  let showUnitTooltip = ref(false); // 是否多单位选择
  let selectUnit = ref(); // 多单位时选择单位
  //end----------
  // 提示信息框------
  let infoVisible = ref(false); // 提示信息框是否显示
  let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
  let iconType = ref(''); // 提示信息框的图标
  let isSureModal = ref(false); // 提示信息框是否为确认提示框
  //end------
  let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
  let isUpdateFile = ref(false);
  const indexVisible = ref(false);
  // 编辑弹框相关
  let editKey = ref(''); // 单独编辑弹框记录得当前编辑字段
  let isShowModel = ref(false);

  let qdVisible = ref(false);
  let deVisible = ref(false);
  let rcjVisible = ref(false);
  let bdCode = ref('');
  let isSortQdCode = ref(false); // 编码重复是否继续
  let isClearEdit = ref(false); // 是否手动清除编辑状态
  let ishasRCJList = ref(false); //人材机定额且有人材机明细数据---不可编辑单价

  let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
  let materialVisible = ref(false); // 是否设置主材市场价弹框
  let DJGCrefreshFeeFile = ref(false); //单价构成需要刷新取费文件列表
  let batchDeleteVisible = ref(false); // 批量删除弹框是否展示
  let codeType = ref(1); // 批量删除 1 工程项目   2 单位
  let isOpenLockedStatus = ref(false); // 批量删除时是否打开锁定数据处理

  let expandLevelValue = ref('none'); // 展开层级

  const radioStyle = reactive({
    display: 'flex',
    height: '30px',
    lineHeight: '30px',
  });
  let batchDataType = ref(1); // 批量删除类型 1 批量删除所有临时删除项  2 批量删除所有工程量为0项
  let currentUpdateData = ref(null); // 当前修改行数据，用于修改后请求接口多，当前行数据为点击到别的数据时使用

  let bdNamePulldownRef = ref(null); // 弹框选择框
  let showUnitTooltipType = ref('code');
  const {
    initVirtual,
    getScroll,
    renderedList,
    init,
    EnterType,
    onDragHeight,
    scrollToPosition,
    renderLine,
  } = useVirtualList();

  let bdNameTableList = ref([]); // 弹框选择框数据
  let isNameLock = ref(false);

  onDeactivated(() => {
    tableData.value = [];
    originalTableData.value = [];
  });

  // 清单名称搜索
  const bdNameKeyupEvent = (row, e) => {
    if (isNameLock.value) return;

    if (e?.value.length > 1 || (row?.onCompositionEnd && row.name)) {
      const $pulldown = bdNamePulldownRef.value;
      if ($pulldown) {
        $pulldown[0]?.showPanel();
      }
      let data = row?.onCompositionEnd && row.name ? row.name : e.value;
      searchTableCode(data);
    }
  };

  /**
   * 是否费用定额
   * 12的垂运，22的装饰超高、垂运不属于费用定额
   * isCostDe是否是费用定额 0不是  1 安文费 2 总价措施 3 超高 4 垂运 5 安装费'
   */
  const isNotCostDe = computed(() => {
    const { kind, isCostDe } = currentInfo.value || {};
    return (
      kind === '04' &&
      (!isCostDe ||
        isCostDe === 4 ||
        (projectStore.deType === '22' && isCostDe === 3))
    );
  });

  const onCompositionStart = row => {
    isNameLock.value = true;
  };

  const onCompositionEnd = row => {
    isNameLock.value = false;
    bdNameKeyupEvent({ ...row, onCompositionEnd: true });
  };

  // 根据清单名称模糊搜索标准清单
  const searchTableCode = name => {
    console.log('🚀 ~ searchTableCode ~ name:', name);
    bdNameTableList.value = [];
    detailApi.searchQdByName({ name: name }).then(res => {
      console.log('🚀 根据清单名称模糊搜索标准清单:', res);
      if (res.status === 200 && res.result) {
        bdNameTableList.value = res.result;
      }
    });
  };

  const dbNameCellClickEvent = ({ row }) => {
    console.log('🚀 ~ dbNameCellClickEvent ~ row:', row);
    addCurrentInfo.value = row;
    const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
    row.unit = unit;
    const $pulldown = bdNamePulldownRef.value;
    if ($pulldown) {
      const $table = vexTable.value;
      if ($table) {
        isClearEdit.value = true;
        $table.clearEdit();
      }
      if (row.unit && row.unit.length > 1) {
        showUnitTooltip.value = true;
        showUnitTooltipType.value = 'name';
      } else {
        updateQdByName(row.bdCodeLevel04, row.unit[0]);
      }
      $pulldown[0]?.hidePanel();
      isClearEdit.value = false;
    }
  };

  // 通过标准编码插入清单
  const updateQdByName = (code, unit) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    detailApi.updateQdByCode(apiData).then(res => {
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        message.success('清单插入成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 首字母大写 originalBdCode\originalFxCode
   */
  const originalCode = `original${codeField
    .charAt(0)
    .toUpperCase()}${codeField.slice(1)}`;
  const originalName =
    nameField === 'name'
      ? 'originalFxName'
      : `original${nameField.charAt(0).toUpperCase()}${nameField.slice(1)}`;

  /**
   * 编辑完成之后
   * @param {*} param0
   * @returns
   */
  const editClosedEvent = ({ row, column }) => {
    console.log('表格ref', vexTable.value);
    if (isClearEdit.value) return;
    let field = column.field;
    const codeValue = row[codeField]; // bdCode,fxCode
    // 判断单元格值是否被修改
    if (!vexTable.value.isUpdateByRow(row, field)) return;
    if (field === 'ysshSysj.changeExplain') {
      updateYsshChangeExplain(row)
      return;
    }
    if ([94, 95].includes(row.kind)) {
      // 走定额下人材机明细修改
      const mappingRow = getDeRcjFieldMappingRow(row, field)
      const mapField = getDeRcjMappingField(field)
      updateConstructRcj(mappingRow, mapField, null, queryBranchDataById)
      return;
    }
    if (field === 'quantity') {
      field = 'quantityExpression';
    }
    row.quantityExpression =
      column.field === 'quantity' ? row.quantity : row.quantityExpression;
    row.quantity = row.originalQuantity;
    if (field === codeField && !row[codeField]) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '编码不可为空',
        confirm: () => {
          infoMode.hide();
          currentInfo.value[codeField] = currentInfo.value[originalCode];
        },
      });
      return;
    }

    nextTick(() => {
      // 如果索引弹窗出现，则不出现补充弹窗
      // if ([codeField].includes(column.field) && indexVisible.value) return;

      costMajorNameEditEvent(field, row);
      currentUpdateData.value = row;
      if (row.kind === '03') {
        isStandQd(field, codeValue);
      } else if (row.kind === '04') {
        if (row.rcjFlag === 1) {
          isRcjCodeMainQuotaLibrary(field, codeValue);
        } else {
          isMainQuotaLibraryCode(field, codeValue);
        }
      }
      expressionEditEvent(field, row, () => {
        vexTable.value.revertData(row, 'quantityExpression');
        row.quantityExpression = row.originalQuantityExpression;
      });
      zjfPriceEditEvent(field, row);
      if (
        ![
          codeField,
          'quantityExpression',
          'zjfPrice',
          'ysshQdglValue',
        ].includes(field)
      ) {
        updateFbData(row, field);
      }
    });
  };

  // 修改预算审核增减说明
  const updateYsshChangeExplain = (row) => {
    const params = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: row.sequenceNbr,
      changeExplain: row.ysshSysj.changeExplain,
      changeType: isFBFX ? 1 : 2,
    }
    console.log('增减说明修改参数', params)
    shApi.updateChangeExplain(params).then(res => {
      console.log('增减说明修改', res)
      message.success('修改成功');
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      queryBranchDataById();
    })
    .finally(() => {
      setSelectCellFn();
    });
  }

  /**
   * 获取编辑区修改人材机明细，字段映射
   * @param {*} field
   * @returns
   */
  const getDeRcjMappingField = (field) => {
    const map = {
      'name': 'materialName',
      'fxCode': 'materialCode',
      'bdCode': 'materialCode',
      'quantity': 'totalNumber',
      'zjfPrice': 'marketPrice',
      'zjfTotal': 'total',
      'zcfee': 'marketPrice',
      'totalZcfee': 'total',
      'sbfPrice': 'marketPrice',
      'sbfTotal': 'total',
    }
    return map[field] || field;
  }
  /**
   * 获取编辑区修改人材机明细，字段映射后得数据
   */
  const getDeRcjFieldMappingRow = (row, field) => {
    // let copyRow = JSON.parse(JSON.stringify(row))
    const mapField = getDeRcjMappingField(field)
    if (mapField) {
      row[mapField] = row[field]
    }
    return row
  }
  let prevSelectedCell = ref();
  const tableKeydown = e => {
    const { code, key } = e.$event;
    console.log('selectedCell:', code, key);
    // const selectedCell = e.$table.getSelectedCell();
    // console.log("🚀键盘选中的", selectedCell)

    // const editRecord = e.$table.getEditRecord();
    // console.log("🚀键盘编辑的", editRecord)

    if (
      ![
        'ArrowRight',
        'ArrowLeft',
        'ArrowDown',
        'ArrowUp',
        'Tab',
        'Enter',
      ].includes(code)
    ) {
      setTimeout(() => {
        const selectedCell = e.$table.getSelectedCell();
        selectedCell?.cell?.click();
      }, 50);
      return;
    }
    setTimeout(() => {
      const selectedCell = e.$table.getSelectedCell();
      setTableKeydownEnd(e);

      const editRecordCell = e.$table.getSelectedCell();
      console.log('🚀键盘编辑的', editRecordCell);
      if (!selectedCell) {
        e.$table.clearEdit();
      } else {
        // selectedCell?.cell?.click();
      }
    }, 50);
  };

  const setTableKeydownEnd = e => {
    const selectedCell = e.$table.getSelectedCell();
    // e.$table.clearEdit();
    console.log('🚀键盘选中的', selectedCell);
    if (selectedCell) {
      prevSelectedCell.value = selectedCell;
    }
  };

  const setSelectCellFn = () => {
    console.log('设置选中的', prevSelectedCell.value);
    vexTable.value?.setSelectCell(
      prevSelectedCell.value?.row,
      prevSelectedCell.value?.column
    );
  };

  /**
   * 表达式处理
   * @param {*} field
   * @param {*} row
   * @param {*} revertDataCallback
   * @returns
   */
  const expressionEditEvent = (field, row, revertDataCallback) => {
    console.log('表达式处理', row);
    if (field !== 'quantityExpression') return;
    const expressionArr = row.quantityExpression.match(/[A-Za-z0-9]+(\.\d+)?/g);
    row.originalQuantityExpression = row.originalQuantityExpression ?? '';
    const orgExpressionArr = row.originalQuantityExpression
      .toString()
      ?.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const [isSuccess, msg] = quantityExpressionHandler(row);
    if (isSuccess) {
      revertDataCallback();
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = msg;
    } else if (
      !orgExpressionArr?.includes('HSGCL') &&
      expressionArr.includes('HSGCL')
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '计算式输入非法，请重新输入标准四则运算表达式或数值',
        confirm: () => {
          infoMode.hide();
          currentInfo.value.quantityExpression =
            currentInfo.value.originalQuantityExpression;
        },
      });
    } else if (
      !expressionArr.includes(row.quantityVariableName) &&
      orgExpressionArr?.includes(row.quantityVariableName)
    ) {
      infoMode.show({
        iconType: 'icon-qiangtixing',
        infoText: '工程量明细已被调用，是否清空工程量明细？',
        confirm: () => {
          updateFbData(currentInfo.value, 'quantityExpression');
          isUpdateQuantities.value = true;
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
          currentInfo.value.quantityExpression =
            currentInfo.value.originalQuantityExpression;
        },
      });
    } else {
      row.quantityExpression = everyNumericHandler(row.quantityExpression);
      updateFbData(row, field);
    }
  };

  /**
   * 判断输入的定额编码是否为主定额库编码
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isMainQuotaLibraryCode = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    console.log('判断是否为主定额册下的标准定额参数', apiData);
    detailApi.isMainQuotaLibraryCode(apiData).then(res => {
      console.log('判断是否为主定额册下的标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code);
        } else {
          isStandardDe(code);
        }
      }
    });
  };

  /**
   * 判断输入的定额编码是否是标准定额
   * @param {*} code
   */
  const isStandardDe = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isStandardDe(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额库下未找到该定额，是否补充定额？',
            confirm: () => {
              deVisible.value = true;
              bdCode.value = code;
              infoMode.hide();
            },
            close: () => {
              infoMode.hide();
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            },
          });
        }
        console.log('判断输入的定额编码是否为主定额库编码', res);
      }
    });
  };

  /**
   * 分部分项 措施项目 替换定额数据
   * @param {*} code
   */
  const updateDeReplaceData = code => {
    let apiData = {
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      type: pageType === 'fbfx' ? 1 : 2,
    };
    console.log('通过标准编码插入定额', apiData);
    detailApi.updateDeReplaceData(apiData).then(res => {
      console.log('通过标准编码插入定额结果', res);
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        deVisible.value = false;
        if (currentInfo.value.standardId) {
          message.success('定额替换成功');
        } else {
          message.success('定额插入成功');
        }

        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否与主定额库编码相同
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isRcjCodeMainQuotaLibrary = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isRcjCodeMainQuotaLibrary(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          // 输入的编码为主定额库编码
          updateBjqRcjReplaceData(code);
        } else {
          isStandardRcj(code);
        }
      }
    });
  };

  /**
   * 分部分项 措施项目 替换编辑区的人材机数据
   * @param {*} code
   */
  const updateBjqRcjReplaceData = code => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      region: 0,
    };
    detailApi.updateBjqRcjReplaceData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        message.success('人材机替换成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否标准人材机数据
   * @param {*} code
   */
  const isStandardRcj = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isStandardRcj(apiData).then(res => {
      console.log('=============');
      if (res.status === 200) {
        if (res.result) {
          updateBjqRcjReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额下不存在该材料编码,是否补充人材机？',
            confirm: () => {
              rcjVisible.value = true;
              bdCode.value = code;
              infoMode.hide();
            },
            close: () => {
              currentInfo.value.bdCode = currentInfo.value.originalBdCode;
              infoMode.hide();
            },
          });
        }
      }
    });
  };

  /**
   * 判断是否是标准清单
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isStandQd = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.isStandQd(apiData).then(res => {
      console.log('判断是否是标准清单', res);
      if (res.status === 200) {
        if (res.result) {
          const unit = res.result.unit;
          const unitArr = Array.isArray(unit) ? unit : unit?.split('/');
          res.result.unit = unitArr;
          addCurrentInfo.value = res.result;
          addCurrentInfo.value.bdCodeLevel04 = code;
          if (code.length === 9) {
            if (unitArr && unitArr.length > 1) {
              showUnitTooltip.value = true;
            } else {
              updateQdByCode(code, unitArr[0]);
            }
          } else {
            isQdCodeExist(code, res.result);
          }
        } else {
          isQdCodeExist(code, res.result);
        }
      }
    });
  };

  /**
   * 判断清单编码是否存在
   * @param {*} code
   * @param {*} obj
   */
  const isQdCodeExist = (code, obj) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.isQdCodeExist(apiData).then(res => {
      console.log('判断清单编码是否存在', res, obj);
      // if (res.status === 200) {
      if (res) {
        // 若存在,则弹框提示是否继续
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: obj ? '' : '是否补充清单？',
          descText: obj
            ? '当前单位工程有相同清单编码，是否自动排序清单编码？'
            : '当前单位工程有相同清单编码，是否继续?',
          confirm: () => {
            if (!obj) {
              bdCode.value = code;
              qdVisible.value = true;
              isSortQdCode.value = false;
            } else {
              isSortQdCode.value = true;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            if (obj) {
              isSortQdCode.value = false;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            } else {
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            }
          },
        });
      } else {
        // 根据是否为标准数据判断替换或补充
        if (!obj) {
          bdCode.value = code;
          qdVisible.value = true;
        } else {
          if (obj.unit && obj.unit.length > 1) {
            showUnitTooltip.value = true;
          } else {
            updateQdByCode(code, obj.unit ? obj.unit[0] : null);
          }
        }
      }
      // }
    });
  };

  /**
   * 通过标准编码插入清单
   * @param {*} code
   * @param {*} unit
   */
  const updateQdByCode = (code, unit) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    console.log('==============标准清单编码插入api参数', apiData);
    detailApi.updateQdByCode(apiData).then(res => {
      console.log('标准清单编码插入', res);
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        if (currentInfo.value.standardId) {
          message.success('清单替换成功');
        } else {
          message.success('清单插入成功');
        }
        queryBranchDataById();
      }
    });
  };

  const costMajorNameEditEvent = (field, row) => {
    if (field !== 'costMajorName') return;
    row.costFileCode = feeFileList.value.filter(
      x => x.qfName === row.costMajorName
    )[0].qfCode;
    isUpdateFile.value = false;
  };
  const zjfPriceEditEvent = (field, row) => {
    if (field !== 'zjfPrice') return;
    row.zjfPrice = Math.round(row.zjfPrice * 100) / 100;
    if (Number(row.zjfPrice) === 0) {
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = '定额单价不能为0';
      iconType.value = 'icon-qiangtixing';
      row.zjfPrice = row.originalZjfPrice;
      return;
    }
    updateFbData(row, field);
  };

  /**
   * 更新数据
   * @param {*} row
   * @param {*} field
   */
  const updateFbData = (row, field, params = null) => {
    isUpdateFile.value = true;
    let costMajorValue;
    //debugger;
    if (field === 'costMajorName') {
      costMajorValue = feeFileList.value.find(
        i => i.qfName === row.costMajorName
      )?.qfCode;
      field = 'costFileCode';
    }
    let column = null;
    let value = null;
    if (field) {
      if (field === 'name') {
        row[field] = row[field].trim();
      }
      column = field;
      value = field === 'costMajorName' ? costMajorValue : row[field]
    }
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitWorkId: projectStore.currentTreeInfo?.id,
      pointLineId: row.sequenceNbr,
      // upDateInfo: {
      //   [nameField]: row[nameField],
      //   projectAttr: row.projectAttr,
      //   unit: row.unit,
      //   costFile: {
      //     code: row.costFileCode,
      //     name: row.costMajorName,
      //   },
      //   itemCategory: row.itemCategory,
      //   measureType: row.measureType,
      //   description: row.description,
      //   quantityExpression: row.quantityExpression,
      //   zjfPrice: row.zjfPrice,
      //   index: row.index,
      //   coldResistantSuborder: row.coldResistantSuborder,
      // },
      column,
      value,
      params
    };
    let apiFun = api.updateData;
    if (
      isJieSuan &&
      projectStore.currentTreeInfo?.originalFlag &&
      ((row.originalFlag &&
        ['quantity', 'quantityExpression'].includes(field)) ||
        ['settlementMethodValue'].includes(field))
    ) {
      // 结算&&合同内&&原始数据&（结算工程量||工程量表达式）
      apiFun = isFBFX
        ? jsApi.updateJieSuanFbfxDataColl
        : jsApi.updateJieSuanCsxmDataColl;
    }
    console.log('数据更新参数', apiData, apiFun);
    apiFun(apiData)
      .then(async res => {
        console.log('数据更新结果', res);
        if (res.status === 200 && res.result) {
          if (res.result.enableUpdatePrice) {
            if (
              isJieSuan &&
              projectStore.currentTreeInfo?.originalFlag &&
              !row.originalFlag &&
              row.kind === '03' &&
              ['quantity', 'quantityExpression'].includes(field)
            ) {
              // 结算&&合同内&&非原始数据&&清单&（结算工程量||工程量表达式）
              await jsApi.htwQdQuantity({
                constructId: projectStore.currentTreeGroupInfo?.constructId,
                singleId: projectStore.currentTreeGroupInfo?.singleId,
                unitId: projectStore.currentTreeInfo?.id,
                qd: res.result?.lineInfo,
              });
            }
            if (isJieSuan) {
              await jsApi.countFeeCodeAndMathFeeColl({
                constructId: projectStore.currentTreeGroupInfo?.constructId,
                singleId: projectStore.currentTreeGroupInfo?.singleId,
                unitId: projectStore.currentTreeInfo?.id,
              })
            }
            if (field !== 'seq') {
              message.success('修改成功');
              if (editKey.value) {
                isShowModel.value = false;
              }
              // if (infoVisible.value) {
              //   isUpdateQuantities.value = true;
              //   infoVisible.value = false;
              // }
            }
            if (
              (row.kind === '01' || row.kind === '02' || row.kind === '0') &&
              field === nameField
            ) {
              emits('updateMenuList');
            }
            if (
              field === 'costMajorName' ||
              field === 'zjfPrice' ||
              field === 'quantityExpression' ||
              field === 'quantity'
            ) {
              isUpdateFile.value = true;
            }
            addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
            queryBranchDataById();
          } else {
            infoMode.show({
              isSureModal: true,
              iconType: 'icon-qiangtixing',
              infoText: '调整后存在人工/材料/机械单价为0，请重新输入',
              confirm: () => {
                infoMode.hide();
                currentInfo.value.zjfPrice = currentInfo.value.originalZjfPrice;
              },
            });
          }
          field === 'costFileCode' ? queryDjgcFileData() : '';
        }
      })
      .finally(() => {
        setSelectCellFn();
      });
  };

  const addLevelToTree = (data, parentLevel = 0, parent = null) => {
    return data.map(node => {
      node.customParent = parent; // 自定义字段父级数据
      const { children, ...other } = node;
      return {
        ...node,
        customLevel: parentLevel + 1,
        children:
          (node.children || []).length > 0
            ? addLevelToTree(node.children, parentLevel + 1, other)
            : [],
      };
    });
  };

  /**
   * 获取列表信息
   * @param {*} EnterType //other 从其他页面需要初始化数据 ，Refresh, 修改了刷新数据
   * clearSelect 是否清除页面多选中的，默认清除
   */
  const queryBranchDataById = (
    EnterTypes = 'Refresh',
    posId = '',
    clearSelect = true
  ) => {
    if (loading.value) return;
    if (isFBFX && !projectStore.asideMenuCurrentInfo?.sequenceNbr) return;
    if (!isFBFX && (!projectStore.currentTreeInfo?.id || loading.value)) return;
    if (projectStore.currentTreeInfo?.levelType === 2) return;
    checkUnit();
    loading.value = true;
    const { constructId, singleId, ssConstructId, ssSingleId } =
      projectStore.currentTreeGroupInfo;
    const { id, ysshUnitId } = projectStore.currentTreeInfo;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      pageSize: limit.value,
      pageNum: page.value,
      isAllFlag: !!posId,
    };
    if (isYSSH) {
      Object.assign(apiData, {
        ssConstructId,
        ssSingleId,
        ssUnitId: ysshUnitId,
      });
    }
    if (isJieSuan) {
      Object.assign(apiData, {
        hierachy: expandLevelValue?.value || 'none',
      });
    }
    console.log('apiData', apiData, api.getList);

    api.getList(apiData).then(res => {
      // debugger
      console.log('queryBranchDataById', res);
      if (res.status === 200) {
        if (!res.result) {
          loading.value = false;
          tableData.value = [];
          // 快速组价存的当前列表数据
          globalData.editAreaTable = [];
          return;
        }
        let resultData = res.result.data;

        let testTreeData = xeUtils.toArrayTree(resultData, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        resultData = xeUtils
          .toTreeArray(addLevelToTree(testTreeData))
          .map(i => {
            if (['04'].includes(i.kind) || i?.children.length) {
              i.children = flattenTreeToSequenceNbrs(i.children);
            }
            return i;
          });

        changeListHandler(resultData);
        tableData.value = resultData;
        console.log('🚀 获取的数据 :', tableData.value);
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));

        // 快速组价存的当前列表数据
        globalData.editAreaTable = originalTableData.value;

        // virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
          loading.value = false;

          if (posId) {
            const doSomething = () => {
              let postRow = tableData.value.find(i => i.sequenceNbr == posId);
              currentInfo.value = postRow;
              vexTable.value?.setCurrentRow(postRow);
              vexTable.value?.scrollToRow(postRow);
            };
            const idleCallbackId = requestIdleCallback(doSomething);
            clearTimeout(idleCallbackId);
          }
        });
        if (clearSelect) {
          frameSelectRef.value?.clearSelect();
        }
        lockFlagHandler();
        addDataHandler(posId);

        let currentRow1 = vexTable.value.getCurrentRecord();
        if (currentRow1) {
          let currentRow = tableData.value.find(
            i => i.sequenceNbr === currentRow1.sequenceNbr
          );
          if (currentRow) {
            currentInfo.value = currentRow;
          }
          console.log(
            '🚀 ~ nextTick ~ currentRow:',
            currentRow,
            currentRow?.isLocked
          );
          if (currentRow) {
            deleteStateFn();
          }
          emits('getCurrentInfo', currentRow);
        }
        if (DJGCrefreshFeeFile.value) {
          queryFeeFileData(true); //刷新当前行重新获取取费文件列表
          queryDjgcFileData(); //切换选中数据更新单价构成列
        }
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
          projectStore.isAutoPosition = false;
        }, 500);
        console.log('==========tableData', tableData.value);
      }
    });
  };

  const handleChilder = data => {
    if (data?.children) {
      data.children = data.children.map(j => j.sequenceNbr);
      for (let i of data.children) {
        handleChilder(i);
      }
    } else {
      return data;
    }
  };

  /**
   * 虚拟滚动处理
   * @param {*} type
   * @param {*} posId
   */
  const virtualListHandler = (type, posId) => {
    EnterType.value = type;
    const initList = init(tableData.value);
    setTimeout(() => {
      initList();
      if (posId) {
        console.log('🚀反向定位 ~ nextTick ~ posId:', posId);
        scrollToPosition(posId, tableData.value);
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId
        );
        vexTable.value?.setCurrentRow(currentInfo.value);
        projectStore.isAutoPosition = false;
      }
    }, 10);
  };

  function flattenTreeToSequenceNbrs(treeData, sequenceNbrKey = 'sequenceNbr') {
    const sequenceNbrs = [];

    function traverse(node) {
      sequenceNbrs.push(node[sequenceNbrKey]);
      if (node.children) {
        node.children.forEach(child => traverse(child));
      }
    }

    treeData.forEach(node => traverse(node));

    return sequenceNbrs;
  }

  /**
   * 插入数据逻辑处理
   * @returns
   */
  const addDataHandler = posId => {
    if (addDataSequenceNbr.value) {
      tableData.value.forEach(item => {
        if (isIndexAddInfo.value) {
          if (item.sequenceNbr === addDataSequenceNbr.value) {
            currentInfo.value = item;
            vexTable.value?.setCurrentRow(item);
            vexTable.value?.scrollToRow(item);
          }
        } else if (item.sequenceNbr === currentInfo.value.sequenceNbr) {
          currentInfo.value = item;
        }
      });
      nextTick(() => {
        frameSelectRef.value?.clearSelect();
        vexTable.value?.setCurrentRow(currentInfo.value);
        resetCellData();
        console.log('nextTick', currentInfo.value);
      });
    } else if (!isIndexAddInfo.value) {
      if (posId) {
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId
        );
        return;
      }
      let hasCurrentInfo = true; // 有点击选中的数据
      if (!currentInfo.value?.sequenceNbr) {
        // 没有点击选中的，则直接替换成数据第一个
        hasCurrentInfo = false;
      } else {
        // 有选中数据，但是在列表中没有找到
        if (
          tableData.value.every(
            item => item.sequenceNbr !== currentInfo.value.sequenceNbr
          )
        ) {
          hasCurrentInfo = false;
        } else {
          currentInfo.value = tableData.value.find(
            item => item.sequenceNbr === currentInfo.value.sequenceNbr
          );
        }
      }

      let handleCurrentInfo = currentInfo.value; // 重新赋值，为了人才机等子页面能监听到变化掉借口
      if (!hasCurrentInfo) {
        // 没有选中的数据，默认第一个选中，并清除所有数据
        handleCurrentInfo = tableData.value[0];
      }
      currentInfo.value = '';
      setTimeout(() => {
        currentInfo.value = handleCurrentInfo;
        // frameSelectRef?.value.setCurrentRow();
        vexTable.value?.setCurrentRow(currentInfo.value);
      }, 300);
    }
  };

  /**
   * 锁定处理
   */
  const lockFlagHandler = () => {
    lockFlag.value = tableData.value
      .filter(data => data.kind === '03')
      .some(item => item.isLocked);
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
  };

  /**
   * 组价方案匹配条件筛选
   * @param {*} val
   */
  const filterData = val => {
    let tempList = [];
    tableData.value = [];
    if (val.length === 0 || !val) {
      tableData.value = originalTableData.value;
    } else {
      originalTableData.value.forEach(item => {
        if (val.includes(item.matchStatus)) {
          tempList.push(item.sequenceNbr);
        }
      });
      for (let i = 0; i < originalTableData.value.length; i++) {
        if (
          tempList.includes(originalTableData.value[i].sequenceNbr) ||
          tempList.includes(originalTableData.value[i].parentId)
        ) {
          tableData.value.push(originalTableData.value[i]);
        }
      }
      tableData.value.forEach((item, index) => {
        item.index = (page.value - 1) * limit.value + (index + 1);
      });
    }
    // const initList = init(tableData.value);
    // nextTick(() => {
    //   initList();
    // });
  };

  /**
   * 对列表原数据做处理，对之前分开处理做合并一块处理，后续需要对数据做循环处理，统一在这里做处理
   * @param {} data
   */
  const changeListHandler = data => {
    for (let i = 0; i < data.length; ++i) {
      let item = data[i];
      if (item.defaultLine) {
        item.measureType = '';
      }
      if (item.appendType && item.appendType.length > 0) {
        if (item.appendType.includes('换')) {
          item.changeFlag = '换';
        }
        if (item.appendType.includes('借')) {
          item.borrowFlag = '借';
        }
      }
      item.index = (page.value - 1) * limit.value + (i + 1);
      item[originalCode] = item[codeField];
      item[originalName] = item[nameField];
      item.originalQuantityExpression = item.quantityExpression;
      item.originalQuantity = item.quantity;
      item.originalZjfPrice = item.zjfPrice;
    }
  };

  /**
   * 获取所有的取费文件列表
   */
  const queryFeeFileData = (isRefresh = false) => {
    console.log('queryFeeFileData', pageType);
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deStandard: projectStore.deStandardReleaseYear,
      type: pageType,
    };
    isRefresh ? (apiData.deId = currentInfo.value?.sequenceNbr) : '';
    console.log('queryFeeFileData', apiData);
    detailApi.queryFeeFileData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        feeFileList.value = res.result;
        DJGCrefreshFeeFile.value = false;
      }
    });
  };

  watchEffect(() => {
    if (
      currentInfo.value?.sequenceNbr &&
      ['04'].includes(currentInfo.value?.kind)
    ) {
      djgcFileList.value = [];
      queryDjgcFileData();
    }
  });
  /**
   * 获取所有的单价构成文件
   */
  const queryDjgcFileData = async (isRefresh = false) => {
    console.log('queryDjgcFileData-useSubItem', pageType);
    // debugger;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deStandard: projectStore.deStandardReleaseYear,
      type: pageType,
      deId: currentInfo.value?.sequenceNbr,
    };
    // isRefresh ? (apiData.deId = currentInfo.value?.sequenceNbr) : '';
    await detailApi.queryDjgcFileData(apiData).then(res => {
      // debugger;
      console.log(
        'queryDjgcFileData- djgcFileList.value',
        apiData,
        res,
        djgcFileList.value
      );
      if (res.status === 200 && res.result) {
        djgcFileList.value = res.result;
      }
    });
  };
  let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
  /**
   * 获取人材机明细数据
   * @param {*} bol
   * @param {*} deItem
   * @returns
   */
  const queryRcjDataByDeId = (bol = true, deItem = null) => {
    let apiData = {
      id: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      branchType: isFBFX ? 1 : 2,
    };
    if (!apiData.id) return;
    let apiFun = detailApi.queryRcjDataByDeId;
    if (isYSSH) {
      apiData = {
        ssId: bol
          ? addDeInfo.value?.ysshSysj?.sequenceNbr
          : deItem.ysshSysj?.sequenceNbr,
        sdId: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
        unitId: projectStore.currentTreeInfo?.id,
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
        ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
        ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
        branchType: isFBFX ? 1 : 2,
      };
      apiFun = shApi.rcjComparison;
    }
    // rcjComparison

    console.log('定额明细', apiData);
    detailApi.queryRcjDataByDeId(apiData).then(res => {
      // console.log('定额明细数据', res);
      if (res.status === 200) {
        if (res.result?.length > 0 && deItem) {
          ishasRCJList.value = true;
        }
        console.log('定额明细数据', res, ishasRCJList.value);

        if (bol) {
          // if (projectStore.globalSettingInfo.mainRcjShowFlag) {
          mainMaterialTableData.value = res.result.filter(
            x => x.kind === 5 && x.marketPrice === 0
          );
          if (mainMaterialTableData.value.length > 0) {
            materialVisible.value = true;
          } else {
            // if (projectStore.globalSettingInfo.standardConversionShowFlag) {
            queryRule();
            // }
          }
          // } else {
          // if (projectStore.globalSettingInfo.standardConversionShowFlag) {
          // queryRule();
          // }
          // }
        }
      }
    });
  };
  /**
   * 选中行数据根据锁定状态判断删除按钮是否可以点击
   */
  const deleteStateFn = () => {
    let isDetete = operateList.value.find(
      item => item.name === 'delete-subItem'
    );
    if (projectStore.tabSelectName === '措施项目') {
      if (
        (currentInfo.value?.optionMenu?.includes(4) ||
          currentInfo.value?.optionMenu?.includes(5)) &&
        ((currentInfo.value.kind === '03' &&
          currentInfo.value.hasOwnProperty('zjcsClassCode') &&
          currentInfo.value.zjcsClassCode !== null &&
          currentInfo.value.zjcsClassCode !== undefined &&
          Number(currentInfo.value.zjcsClassCode) === 0) ||
          currentInfo.value.constructionMeasureType === 2)
      ) {
        isDetete.disabled = true;
      } else {
        isDetete.disabled = currentInfo.value.isLocked;
      }
    } else {
      isDetete.disabled = currentInfo.value.isLocked;
      if (
        projectStore.currentTreeInfo?.originalFlag &&
        currentInfo.value?.originalFlag
      ) {
        // 合同内原始数据不可删除
        isDetete.disabled = true;
      } else {
        isDetete.disabled = false;
      }
    }
  };

  /**
   * 选中单条分部分项数据
   * @param {*} param0
   */
  const currentChangeEvent = async ({ row }) => {
    // const $table = vexTable.value;
    // 判断单元格值是否被修改
    // if ($table.isEditByRow(currentInfo.value)) return;
    if (row.kind === '04' && isJieSuan && isFBFX) {
      const deLockPrice = await jsApi.qdIFLockPriceByDeId({
        unitId: projectStore.currentTreeInfo?.id,
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        deId: row.sequenceNbr,
      });
      row.deLockPriceFlag = deLockPrice.result;
    }
    currentInfo.value = row;
    ishasRCJList.value = false;
    if (row.kind === '04' && row.rcjFlag === 1) {
      queryRcjDataByDeId(false, row);
    }
    projectStore.SET_SUB_CURRENT_INFO(row);
    deleteStateFn();
    // if (row.kind === '04') {
    //   queryDjgcFileData(); //切换选中数据更新单价构成列
    // }
    // emits('getCurrentInfo', currentInfo.value);
  };

  let editContent = ref('');
  // 编辑内容保存事件
  const saveContent = () => {
    const valueType = typeof editContent.value;
    if (valueType !== 'string' || editContent.value.trim() === '') {
      if (editKey.value === 'quantityExpression') {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-qiangtixing',
          infoText: '请输入工程量表达式',
          confirm: () => {
            infoMode.hide();
          },
        });
      }
      isShowModel.value = false; //空的话弹框编辑关闭
      return;
    }
    currentInfo.value[editKey.value] = editContent.value;

    expressionEditEvent(editKey.value, currentInfo.value, () => {
      editContent.value = currentInfo.value.originalQuantityExpression;
      vexTable.value.revertData(currentInfo.value, 'quantityExpression');
      currentInfo.value.quantityExpression =
        currentInfo.value.originalQuantityExpression;
    });
    if (editKey.value !== 'quantityExpression') {
      updateFbData(currentInfo.value, editKey.value);
    }
  };

  let showModelTitle = ref('清单名称编辑');
  /**
   * 打开编辑弹框方法
   * @param {*} field
   */
  const openEditDialog = field => {
    editKey.value = field;
    switch (field) {
      case nameField:
        showModelTitle.value =
          currentInfo.value.kind === '03' ? '清单名称编辑' : '定额名称编辑';
        break;
      case 'projectAttr':
        showModelTitle.value = '项目特征编辑';
        break;
      case 'quantityExpression':
        showModelTitle.value = '工程量表达式编辑';
        break;
    }
    isShowModel.value = true;
    editContent.value = currentInfo.value[field];
  };

  let standardVisible = ref(false);
  /**
   * 获取定额是否存在标准换算信息
   * @returns
   */
  const queryRule = () => {
    if (!addDeInfo.value?.standardId) {
      return;
    }
    let apiData = {
      standardDeId: addDeInfo.value?.standardId,
      fbFxDeId: addDeInfo.value?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      libraryCode: addDeInfo.value?.libraryCode,
    };
    console.log('标准换算列表参数', apiData);
    detailApi.queryRule(apiData).then(res => {
      console.log('标准换算列表数据', res);
      if (res.status === 200 && res.result) {
        if (res.result && res.result.length > 0) {
          standardVisible.value = true;
        }
      }
    });
  };

  // 临时删除
  const updateDelTempStatusColl = () => {
    let ids = [];
    if (selectData.value?.data.length > 1) {
      selectData.value.data.forEach(item => {
        ids.push(item.sequenceNbr);
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      idList: ids,
      modelType: pageType === 'fbfx' ? 1 : 2,
      dataType: currentInfo.value.kind === '03' ? 1 : 2,
      tempDeleteFlag: !currentInfo.value.tempDeleteFlag,
    };
    console.log('临时删除参数', apiData);
    detailApi.updateDelTempStatusColl(apiData).then(res => {
      console.log('res临时删除', res);
      if (res.status === 200 && res.result) {
        if (currentInfo.value.tempDeleteFlag) {
          message.success('数据已取消临时删除');
        } else {
          message.success('数据临时删除成功');
        }
        queryBranchDataById();
      }
    });
  };

  // 临时删除
  const batchDelByTypeOfColl = () => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      dataType: batchDataType.value,
      rangeType: codeType.value,
      lockFlag: isOpenLockedStatus.value,
      kindArr: [],
    };
    console.log('批量删除参数', apiData);
    detailApi.batchDelByTypeOfColl(apiData).then(res => {
      console.log('res批量删除参数', res);
      if (res.status === 200 && res.result) {
        if (currentInfo.value.tempDeleteFlag) {
          message.success('数据已取消临时删除');
        } else {
          message.success('数据临时删除成功');
        }
        batchDeleteVisible.value = false;
        queryBranchDataById();
      }
    });
  };

  const handleMainList = (item, row) => {
    if (item.children && item.code == 'MainList') {
      if (row.kind == '04') {
        item.visible = false;
        return;
      }
      item.visible = true;

      let showCode = ['set-list', 'del-all'];
      if (row.ifMainQd) {
        showCode = ['del-current', 'del-all'];
      }

      item.children.forEach(childItem => {
        childItem.fatherCode = 'MainList';
        childItem.visible = showCode.includes(childItem.code);
      });
    }
  };

  let areaStatus = ref(false);
  let areaVisibleType = ref('');
  let areaData = ref(null);

  // 鼠标批注右键操作
  const handleMainListClick = (item, row) => {
    console.log('🚀 ~ handleNoteClick ~ row:', row);
    console.log('🚀 ~ handleNoteClick ~ item:', item);
    // ifMainQd
    areaData.value = row;
    switch (item.code) {
      case 'set-list':
        row.ifMainQd = true;
        updateFbData(row, 'ifMainQd');
        break;
      case 'del-current':
        row.ifMainQd = false;
        updateFbData(row, 'ifMainQd');
        break;
      case 'del-all':
        areaStatus.value = true;
        areaVisibleType.value = 'MainList-all';
      default:
        break;
    }
  };

  const closeAreaModal = v => {
    areaStatus.value = false;
    if (v) {
      console.log(
        '🚀 ~ closeAreaModal ~ areaVisibleType.value:',
        areaVisibleType.value
      );
      let delName =
        areaVisibleType.value == 'note-all'
          ? 'delBatchAnnotationsController'
          : 'batchRmoveMainQdController';

      detailApi[delName]({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        type: +v,
      })
        .then(res => {
          queryBranchDataById();
        })
        .finally(() => {
          areaVisibleType.value = '';
        });
    }
  };

  //处理批注的右键
  const handleNote = (item, row) => {
    if (item.children && item.code == 'noteList') {
      let defineBtn = [1, 6]; // 默认展示的批注操作
      let hideNoteBtn = [2, 3, 4, 6]; // 隐藏的批注操作
      let showNoteBtn = [2, 3, 5, 6]; // 展示的批注操作
      let checkList = defineBtn;

      if (!row.annotations) {
        // 没有批注的
        checkList = defineBtn;
      } else {
        // 有批注，
        checkList = row?.isShowAnnotations ? showNoteBtn : hideNoteBtn;
      }

      item.children.forEach(childItem => {
        childItem.fatherCode = 'noteList';
        childItem.visible = checkList.includes(childItem.type);
      });
    }
  };

  // 鼠标批注右键操作
  const handleNoteClick = (item, row) => {
    console.log('🚀 ~ handleNoteClick ~ row:', row);
    console.log('🚀 ~ handleNoteClick ~ item:', item);
    switch (item.code) {
      case 'edit-note':
      case 'add-note':
        editAnnotations(row);
        break;
      case 'del-note':
        updateFbData({ ...row, annotations: '' }, 'annotations');
        break;
      case 'show-note':
        updateFbData({ ...row, isShowAnnotations: true }, 'isShowAnnotations');
        break;
      case 'hide-note':
        updateFbData({ ...row, isShowAnnotations: false }, 'isShowAnnotations');
        break;
      case 'del-all-note':
        areaStatus.value = true;
        areaVisibleType.value = 'note-all';
        break;
      default:
        break;
    }
  };

  const onFocusNode = row => {
    if (AnnotationsCurrent.value != row.sequenceNbr) {
      console.log('🚀 ~手动选中');
      editAnnotations(row);
    }
  };

  const editAnnotations = row => {
    row.noteEditVisible = true;
    AnnotationsCurrent.value = row.sequenceNbr;
    nextTick(() => {
      AnnotationsRefList.value[row.sequenceNbr]?.focusNode();
    });
  };

  // 关闭编辑的
  const closeAnnotations = (v, row) => {
    if (!row?.isShowAnnotations) {
      row.noteEditVisible = false;
      row.noteViewVisible = false;
    }
    if (v == row?.annotations) {
      return;
    }
    updateFbData({ ...row, annotations: v }, 'annotations');
  };

  const cellMouseEnterEvent = ({ row, rowIndex, column, $event }) => {
    if (['name', 'bdName'].includes(column.field) && row?.annotations) {
      row.noteViewVisible = true;
    }
  };

  const cellMouseLeaveEvent = ({ row, rowIndex, column, $event }) => {
    if (
      ['name', 'bdName'].includes(column.field) &&
      !row?.noteEditVisible &&
      row?.annotations &&
      !row?.isShowAnnotations
    ) {
      row.noteViewVisible = false;
    }
  };

  // 快速组价
  watchEffect(() => {
    globalData.editCurrentInfo = currentInfo.value;
  });
  watch(
    () => indexVisible.value,
    () => {
      projectStore.isOpenIndexModal = {
        open: indexVisible.value,
        tab: pageType,
      };
    }
  );
  const getAnnotationsRef = (el, row) => {
    if (el) {
      AnnotationsRefList.value[row.sequenceNbr] = el;
    } else {
      AnnotationsRefList.value[row.sequenceNbr] = null;
    }
  };
  const needAddQDandFB = item => {
    //打开标准组价子窗口是否可以添加清单和分部
    if (pageType === 'fbfx') {
      let addQD = item.children.find(i => i.name === '添加清单');
      let addFB = item.children.find(i => i.name === '添加分部');
      // let addZFB = item.children.find(i => i.name === '添加子分部');

      if (projectStore.standardGroupOpenInfo.isOpen) {
        //标准组价子窗口不可以添加清单和分部
        addQD.disabled = true;
        addFB.disabled = true;
        // addZFB.disabled = true;
      } else {
        addQD.disabled = false;
        addFB.disabled = false;
        // addZFB.disabled = false;
      }
    } else {
      let addQD = item.children.find(i => i.name === '添加清单');
      let addBT = item.children.find(i => i.name === '添加标题');
      let addZX = item.children.find(i => i.name === '添加子项');
      if (projectStore.standardGroupOpenInfo.isOpen) {
        //标准组价子窗口不可以添加清单和分部
        addQD.disabled = true;
        addBT.disabled = true;
        addZX.disabled = true;
      } else {
        // addQD.disabled = false;
        // addBT.disabled = false;
        // addZX.disabled = false;
        item.children.forEach(i => {
          currentInfo.value.optionMenu.forEach(child => {
            if (child === item.code) {
              item.disabled = false;
            }
          });
        });
      }
    }
  };

  /**
   * 编辑区类型字段处理
   * @param {*} row
   * @returns
   */
  const getTypeText = row => {
    let info = {
      text: '',
      className: '',
    };
    if (projectStore.combinedVisible) {
      if (row.matchStatus === '1') {
        info.text = '精';
        info.className = 'flag-green';
      }
      if (row.matchStatus === '2') {
        info.text = '近';
        info.className = 'flag-orange';
      }
      if (row.matchStatus === '0') {
        info.text = '未';
        info.className = 'flag-red';
      }
    }
    if ((!row.borrowFlag && !row.changeFlag) || row.type === '费') {
      info.text = row.type;
    } else if (row.type !== '费') {
      info.className = 'code-flag';
      info.text = row.changeFlag
        ? row.changeFlag
        : row.borrowFlag
        ? row.borrowFlag
        : '';
    }
    return info;
  };
  return {
    getTypeText,
    updateQdByName,
    dbNameCellClickEvent,
    bdNameTableList,
    bdNamePulldownRef,
    showUnitTooltipType,
    bdNameKeyupEvent,
    onCompositionEnd,
    onCompositionStart,
    getAnnotationsRef,

    queryBranchDataById,
    queryFeeFileData,
    queryDjgcFileData,
    editClosedEvent,
    currentChangeEvent,
    updateFbData,

    onDragHeight,
    initVirtual,
    getScroll,
    renderedList: tableData,
    init,
    EnterType,
    scrollToPosition,
    mainMaterialTableData,

    saveContent,
    openEditDialog,
    showModelTitle,

    currentInfo,
    isShowModel,
    editContent,
    editKey,
    infoVisible,
    infoText,
    iconType,
    isSureModal,

    ishasRCJList,
    isClearEdit,
    isSortQdCode,
    bdCode,
    rcjVisible,
    deVisible,
    qdVisible,
    DJGCrefreshFeeFile,
    isUpdateFile,
    indexVisible,
    isUpdateQuantities,
    selectUnit,
    showUnitTooltip,
    addCurrentInfo,
    isIndexAddInfo,
    addDataSequenceNbr,
    lockFlag,
    feeFileList,
    tableData,
    originalTableData,
    materialVisible,
    updateDelTempStatusColl,
    batchDelByTypeOfColl,
    batchDeleteVisible,
    codeType,
    radioStyle,
    isOpenLockedStatus,
    batchDataType,
    selectData,
    djgcFileList,
    handleNote,
    handleNoteClick,
    areaStatus,
    areaVisibleType,
    handleMainList,
    handleMainListClick,
    closeAreaModal,
    closeAnnotations,
    queryRcjDataByDeId,
    addDeInfo,
    AnnotationsRefList,
    cellMouseEnterEvent,
    cellMouseLeaveEvent,
    standardVisible,
    queryRule,
    onFocusNode,
    renderLine,
    loading,
    isNotCostDe,
    deleteStateFn,
    needAddQDandFB,
    currentUpdateData,

    tableKeydown,
    setTableKeydownEnd,
    expandLevelValue
  };
};
