import {BaseModel} from "./BaseModel";
import {RcjDetails} from "./GsRcjDetails";


/**
 * 工程项目人材机表DTO类
 * 与BS保持一致
 */
export class GljConstructProjectRcj extends BaseModel {


    /**
     * 编码
     */
    public materialCode: string;

    /**
     * base rcj ID
     */
    public standardId: string

    /**
     * 名称
     */
    public materialName: string;

    /**
     * 规格及型号
     */
    public specification: string;

    /**
     * 单位
     */
    public unit: string;

    /**
     * 单位id
     */
    public unitId: string;

    /**
     * 定额价
     */
    public dePrice: number;

    /**
     * 企业价、市场价
     */
    public marketPrice: number;

    /**
     * 信息价
     */
    public informationPrice:number;

    /**
     * 推荐价
     */
    public recommendPrice:number;

    /**
     * 精准市场价的备份 调整优先级后要用
     */
    public marketPriceOrigin: number;

    /**
     * 精准市场价的价格来源备份
     */
    public marketSourcePriceOrigin:number;

    /**
     * 精准信息价的备份
     */
    public informationPriceOrigin:number;

    /**
     * 精准信息价的价格来源备份
     */
    public informationSourcePriceOrigin:number;

    /**
     * 精准推荐价的备份
     */
    public recommendPriceOrigin:number;

    /**
     * 精准推荐价的价格来源备份
     */
    public recommendSourcePriceOrigin:number;

    /**
     * 载价前原来人材机的市场价
     */
    public marketPriceBeforeLoading:number;

    /**
     * 载价前原来人材机的市场价价格来源
     */
    public marketSourcePriceBeforeLoading:number;

    /**
     * 价格来源
     */
    public sourcePrice: string;

    /**
     * 是否汇总
     */
    public iscount: number;

    /**
     * 价差
     */
    public priceDifferenc: number;

    /**
     * 价差合计
     */
    public priceDifferencSum: number;

    /**
     * 品牌
     */
    public brand: string;

    /**
     * 厂家
     */
    public manufactor: string;

    /**
     * 产地
     */
    public producer: string;

    /**
     * 送达地点
     */
    public deliveryLocation: string;

    /**
     * 质量等级
     */
    public qualityGrade: string;

    /**
     * 除税系数
     */
    public taxRemoval: number;

    /**
     * 除税系数备份
     */
    public taxRemovalBackUp: number;

    /**
     * 企业除税价格
     */
    public csMarketPrice: number;

    /**
     * kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)
     */
    public kind: number;

    /**
     * 定额册编码
     */
    public libraryCode: string;

    /**
     * 材料类别(1：人工材料；2：计价材料；3：未计价材料；4：机械台班；5：仪器仪表；6：配合比材料；7：设备；8：子定额；9：自定义材料；10：其他；11：新补充材料；20：定额人工；21：定额材料；22：定额机械；23：定额设备；24：定额主材）
     */
    public materialCategory: string;

    /**
     * 三材类别
     */
    public threeMaterialCategories: string;

    /**
     * 地区
     */
    public area: string;

    /**
     * 类别
     */
    public type: string;

    /**
     * 材料价格年月
     */
    public priceDate: string;

    /**
     * 更新时间-记录时间
     */
    public updateDate: Date;

    /**
     * 创建者
     */
    public recUserId: string;

    /**
     * 工程id
     */
    public constructId: string;

    /**
     * 甲供价格
     */
    public donorMaterialPrice: number;

    /**
     * 甲供名称
     */
    public donorMaterialName: string;

    /**
     * 甲供规格型号
     */
    public donorSpecification: string;

    /**
     * 是否是甲供(非1:不是，1：是)
     * 概算:  "0": "自行采购"   "1": "甲方供应" "2":"甲定乙供"
     */
    public ifDonorMaterial: string;

    /**
     * 甲供数量
     */
    public donorMaterialNumber: number;


    /**
     * kind备份
     */
    public kindBackUp: number;

    /**
     * 甲供材料ID
     */
    public materialSequenceNbr: string;

    /**
     * 是否锁定材料价格(0：否  1：是)
     */
    public ifLockStandardPrice: number;


    /**
     * 是否锁定工程量(0:不是，1：是)
     */
    public ifLockQuantity: number;

    /**
     * 暂估名称
     */
    public provisionalEstimateName: string;

    /**
     * 暂估价格
     */
    public provisionalEstimatePrice: number;

    /**
     * 暂估型号
     */
    public provisionalEstimateSpecification: string;

    /**
     * 暂估ID
     */
    public provisionalEstimateSequenceNbr: string;

    /**
     * 是否是暂估(非1:不是，1：是)
     */
    public ifProvisionalEstimate: number;

    /**
     *所属定额id
     */
    public deId: string;
    /**
     * 材料消耗量
     */
    public resQty: number;
    /**
     * 原始含量
     */
    public initResQty: number;

    /**
     * 是否下沉标识（2：下沉机械；1：下沉配比；0：无需下沉）
     */
    public levelMark: string;

    /**
     * 合计数量
     */
    private totalNumber: number;

    /**
     * 合价
     */
    private total: number;

    /**
     * 人材机明细数据
     */
    public rcjDetailsDTOs: Array<RcjDetails>;



    /**
     * 是否汇总(解析) 1代表解析 0代表不解  默认是1 解析
     */
    private markSum: number;

    /**
     * 进项合计
     */
    public jxTotal: number;


    /**
     * 编辑记录
     */
    public referenceRecord: string;


    /**
     * 标记 是否是定额部分人材机 ture 表示是定额 人材机
     */
    public labelDe: boolean;


    /**
     * 1代表 费用定额人材机 (无法编辑 类型，名称，规格型号，单位，市场价，市场价锁定 )
     *
     */
    public edit: number;

    /**
     * 1代表 补充人材机
     *
     */
    public supplementDeRcjFlag: number;
    /**
     * 是否为定额人材机 1是0 否
     */
    public rcjFlag: number;


    /**
     * 1代表 添加的人材机
     *
     */
    public addRcjType: number;

    /**
     * 1代表 人材机消耗量进行过修改
     */
    public resQtyChangeType: number;


    /**
     * 1代表 人材机进行过kind3D的标准换算
     */
    public kind3dType: number;

    /**
     * 是否勾选执行载价  true为执行
     */
    public isExecuteLoadPrice:boolean;

    /**
     * 待载价格
     */
    public loadPrice:number;

    /**
     * 载价编辑弹窗的待载价格是否高亮
     */
    public highlight:boolean;

    /**
     * 信息价的价格来源  用于载价编辑页面手动双击价格时应用
     */
    public informationSourcePrice:string;

    /**
     * 市场价价格来源
     */
    public marketSourcePrice:string;

    /**
     * 推荐价价格来源
     */
    public recommendSourcePrice:string;


    /**
     * 不含税基期价 一般计税 (12 dePrice)
     */
    // public priceBaseJournal:number;

    /**
     * 含税基期价  简易计税 (12 dePrice)
     */
    // public priceBaseJournalTax:number;

    /**
     * 不含税市场价 一般计税 (12 市场价)
     */
    public priceMarket:number;

    /**
     * 含税市场价 简易计税
     */
    public priceMarketTax:number;

    /**
     * rcj颜色
     */
    public rcjColor: string;
    /**
     * 备注
     */
    public remark: string;
    /**
     *   工程项目不同 市场价rcj颜色标识    有差异：1
     */
    public marketPriceDiff: string;

    /**是否是由标准换算添加的人材机 */
    public addFromConversionRuleId?: string

    /**材料替换历史记录 用于勾选时替换材料 取消勾选后恢复 */
    public materialReplaceHistory?: {
        materialCode: string,
        materialName: string
        dePrice: number
    }

    public consumerResQty: string

    /**人材机被哪些规则ID参与换算 */
    public ruleIds?: string[]

    /**人材机修改消耗量后与哪些规则ID绑定并冻结消耗量 */
    public freezeRuleIds?: string[]

    // 批注
    public annotations: string

    /**
     * 清单批注是否显示 true显示  false 隐藏
     */
    public  isShowAnnotations: boolean;

    // 工程项目批注
    public annotationsPro: string;

    /**
     * 工程项目 清单批注是否显示 true显示  false 隐藏
     */
    public  isShowAnnotationsPro: boolean;

    // 单项工程批注
    public annotationsSingle: string;

    /**
     * 单项工程 清单批注是否显示 true显示  false 隐藏
     */
    public  isShowAnnotationsSingle: boolean;

    /**
     * 单项工程批注
     */
    public  annotationsSingleObj: object;

    /**
     *  修改标识
     */
    public  updateFalg: number;

    /**
     *  是否锁定
     */
    public  isNumLock: boolean;

    /**
     * 锁定数量
     */
    public  numLockNum: number;
    /**
     *  是否调差人材机
     */
    public  isDeCompensation: string;

    public  rcjId: string;

    public  parentId: string;


    public  supplementRcjFlag: number;

    public  supplyTime: string;

    public  isDeResource: string;

    public  kindSc: string;

    public  transferFactor: string;

    /**
     *    工料机新增含税字段
     */
    public  baseJournalPrice: number;
    public  baseJournalTaxPrice: number;
    public  marketTaxPrice: number;
    public  isDataTaxRate: number;
    public  taxRate: number;
    public  totalTax: number;
    public  baseJournalTotal: number;
    public  baseJournalTotalTax: number;
    public  isFyrcj: number;
    /**
     *      原始基期价前、后
     */
    public  baseJournalPriceOriginalForward : number ;
    public  baseJournalTaxPriceOriginalForward : number ;
    public  baseJournalPriceOriginalReverse  : number ;
    public  baseJournalTaxPriceOriginalReverse : number ;

    /**
     *  价差进行初始化价格备份
     */
    public originMarketPrice : number ;
    public originMarketTaxPrice : number ;
    public originBaseJournalPrice : number ;
    public originBaseJournalTaxPrice : number ;
    public originKind : number ;

    /**
     *  消耗量系数 、消耗量表达书、原始含量、 原始消耗量
     */
    public resQtyFactor : number ;
    public resqtyExp : number ;
    public originalQty : number ;
    public originalConsumeQty : number ;

    /**
     *  是否是 市政养护人材机-中修
     */
    public isMunicipal  : boolean ;

    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string, materialCode: string, standardId: string, materialName: string, specification: string, unit: string, unitId: string, dePrice: number, marketPrice: number, informationPrice: number, recommendPrice: number, marketPriceOrigin: number, marketSourcePriceOrigin: number, informationPriceOrigin: number, informationSourcePriceOrigin: number, recommendPriceOrigin: number, recommendSourcePriceOrigin: number, marketPriceBeforeLoading: number, marketSourcePriceBeforeLoading: number, sourcePrice: string, iscount: number, priceDifferenc: number, priceDifferencSum: number, brand: string, manufactor: string, producer: string, deliveryLocation: string, qualityGrade: string, taxRemoval: number, taxRemovalBackUp: number, csMarketPrice: number, kind: number, libraryCode: string, materialCategory: string, threeMaterialCategories: string, area: string, type: string, priceDate: string, updateDate: Date, recUserId: string, constructId: string, donorMaterialPrice: number, donorMaterialName: string, donorSpecification: string, ifDonorMaterial: string, donorMaterialNumber: number, kindBackUp: number, materialSequenceNbr: string, ifLockStandardPrice: number, ifLockQuantity: number, provisionalEstimateName: string, provisionalEstimatePrice: number, provisionalEstimateSpecification: string, provisionalEstimateSequenceNbr: string, ifProvisionalEstimate: number, deId: string, resQty: number, initResQty: number, levelMark: string, totalNumber: number, total: number, rcjDetailsDTOs: Array<RcjDetails>, markSum: number, jxTotal: number, referenceRecord: string, labelDe: boolean, edit: number, supplementDeRcjFlag: number, rcjFlag: number, addRcjType: number, resQtyChangeType: number, kind3dType: number, isExecuteLoadPrice: boolean, loadPrice: number, highlight: boolean, informationSourcePrice: string, marketSourcePrice: string, recommendSourcePrice: string, priceMarket: number, priceMarketTax: number, rcjColor: string, remark: string, marketPriceDiff: string, addFromConversionRuleId: string, materialReplaceHistory: {
        materialCode: string;
        materialName: string;
        dePrice: number
    }, consumerResQty: string, ruleIds: string[], freezeRuleIds: string[], annotations: string, isShowAnnotations: boolean, annotationsPro: string, isShowAnnotationsPro: boolean, annotationsSingle: string, isShowAnnotationsSingle: boolean, annotationsSingleObj: object, updateFalg: number, isNumLock: boolean, numLockNum: number, isDeCompensation: string, rcjId: string, parentId: string, supplementRcjFlag: number, supplyTime: string, isDeResource: string, kindSc: string, transferFactor: string, baseJournalPrice: number, baseJournalTaxPrice: number, marketTaxPrice: number, isDataTaxRate: number, taxRate: number, totalTax: number, baseJournalTotal: number, baseJournalTotalTax: number, isFyrcj: number, baseJournalPriceOriginalForward: number, baseJournalTaxPriceOriginalForward: number, baseJournalPriceOriginalReverse: number, baseJournalTaxPriceOriginalReverse: number,originMarketPrice : number ,originMarketTaxPrice : number,originBaseJournalPrice : number ,originBaseJournalTaxPrice : number,originKind :number,
                resQtyFactor : number , resqtyExp : number ,originalQty : number  ,originalConsumeQty : number,isMunicipal : boolean) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.materialCode = materialCode;
        this.standardId = standardId;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.unitId = unitId;
        this.dePrice = dePrice;
        this.marketPrice = marketPrice;
        this.informationPrice = informationPrice;
        this.recommendPrice = recommendPrice;
        this.marketPriceOrigin = marketPriceOrigin;
        this.marketSourcePriceOrigin = marketSourcePriceOrigin;
        this.informationPriceOrigin = informationPriceOrigin;
        this.informationSourcePriceOrigin = informationSourcePriceOrigin;
        this.recommendPriceOrigin = recommendPriceOrigin;
        this.recommendSourcePriceOrigin = recommendSourcePriceOrigin;
        this.marketPriceBeforeLoading = marketPriceBeforeLoading;
        this.marketSourcePriceBeforeLoading = marketSourcePriceBeforeLoading;
        this.sourcePrice = sourcePrice;
        this.iscount = iscount;
        this.priceDifferenc = priceDifferenc;
        this.priceDifferencSum = priceDifferencSum;
        this.brand = brand;
        this.manufactor = manufactor;
        this.producer = producer;
        this.deliveryLocation = deliveryLocation;
        this.qualityGrade = qualityGrade;
        this.taxRemoval = taxRemoval;
        this.taxRemovalBackUp = taxRemovalBackUp;
        this.csMarketPrice = csMarketPrice;
        this.kind = kind;
        this.libraryCode = libraryCode;
        this.materialCategory = materialCategory;
        this.threeMaterialCategories = threeMaterialCategories;
        this.area = area;
        this.type = type;
        this.priceDate = priceDate;
        this.updateDate = updateDate;
        this.recUserId = recUserId;
        this.constructId = constructId;
        this.donorMaterialPrice = donorMaterialPrice;
        this.donorMaterialName = donorMaterialName;
        this.donorSpecification = donorSpecification;
        this.ifDonorMaterial = ifDonorMaterial;
        this.donorMaterialNumber = donorMaterialNumber;
        this.kindBackUp = kindBackUp;
        this.materialSequenceNbr = materialSequenceNbr;
        this.ifLockStandardPrice = ifLockStandardPrice;
        this.ifLockQuantity = ifLockQuantity;
        this.provisionalEstimateName = provisionalEstimateName;
        this.provisionalEstimatePrice = provisionalEstimatePrice;
        this.provisionalEstimateSpecification = provisionalEstimateSpecification;
        this.provisionalEstimateSequenceNbr = provisionalEstimateSequenceNbr;
        this.ifProvisionalEstimate = ifProvisionalEstimate;
        this.deId = deId;
        this.resQty = resQty;
        this.initResQty = initResQty;
        this.levelMark = levelMark;
        this.totalNumber = totalNumber;
        this.total = total;
        this.rcjDetailsDTOs = rcjDetailsDTOs;
        this.markSum = markSum;
        this.jxTotal = jxTotal;
        this.referenceRecord = referenceRecord;
        this.labelDe = labelDe;
        this.edit = edit;
        this.supplementDeRcjFlag = supplementDeRcjFlag;
        this.rcjFlag = rcjFlag;
        this.addRcjType = addRcjType;
        this.resQtyChangeType = resQtyChangeType;
        this.kind3dType = kind3dType;
        this.isExecuteLoadPrice = isExecuteLoadPrice;
        this.loadPrice = loadPrice;
        this.highlight = highlight;
        this.informationSourcePrice = informationSourcePrice;
        this.marketSourcePrice = marketSourcePrice;
        this.recommendSourcePrice = recommendSourcePrice;
        this.priceMarket = priceMarket;
        this.priceMarketTax = priceMarketTax;
        this.rcjColor = rcjColor;
        this.remark = remark;
        this.marketPriceDiff = marketPriceDiff;
        this.addFromConversionRuleId = addFromConversionRuleId;
        this.materialReplaceHistory = materialReplaceHistory;
        this.consumerResQty = consumerResQty;
        this.ruleIds = ruleIds;
        this.freezeRuleIds = freezeRuleIds;
        this.annotations = annotations;
        this.isShowAnnotations = isShowAnnotations;
        this.annotationsPro = annotationsPro;
        this.isShowAnnotationsPro = isShowAnnotationsPro;
        this.annotationsSingle = annotationsSingle;
        this.isShowAnnotationsSingle = isShowAnnotationsSingle;
        this.annotationsSingleObj = annotationsSingleObj;
        this.updateFalg = updateFalg;
        this.isNumLock = isNumLock;
        this.numLockNum = numLockNum;
        this.isDeCompensation = isDeCompensation;
        this.rcjId = rcjId;
        this.parentId = parentId;
        this.supplementRcjFlag = supplementRcjFlag;
        this.supplyTime = supplyTime;
        this.isDeResource = isDeResource;
        this.kindSc = kindSc;
        this.transferFactor = transferFactor;
        this.baseJournalPrice = baseJournalPrice;
        this.baseJournalTaxPrice = baseJournalTaxPrice;
        this.marketTaxPrice = marketTaxPrice;
        this.isDataTaxRate = isDataTaxRate;
        this.taxRate = taxRate;
        this.totalTax = totalTax;
        this.baseJournalTotal = baseJournalTotal;
        this.baseJournalTotalTax = baseJournalTotalTax;
        this.isFyrcj = isFyrcj;
        this.baseJournalPriceOriginalForward = baseJournalPriceOriginalForward;
        this.baseJournalTaxPriceOriginalForward = baseJournalTaxPriceOriginalForward;
        this.baseJournalPriceOriginalReverse = baseJournalPriceOriginalReverse;
        this.baseJournalTaxPriceOriginalReverse = baseJournalTaxPriceOriginalReverse;
        this.originMarketPrice  = originMarketPrice;
        this.originMarketTaxPrice  = originMarketTaxPrice;
        this.originBaseJournalTaxPrice  = originBaseJournalTaxPrice;
        this.originBaseJournalPrice  = originBaseJournalPrice;
        this.originKind  = originKind;
        this.resQtyFactor  = resQtyFactor;
        this.resqtyExp  = resqtyExp;
        this.originalQty  = originalQty;
        this.originalConsumeQty  = originalConsumeQty;
        this.isMunicipal  = isMunicipal;
    }
}
