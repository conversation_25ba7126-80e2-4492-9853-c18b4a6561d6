<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-18 19:16:28
 * @LastEditors: renmingming
 * @LastEditTime: 2024-09-14 16:24:13
-->
<template>
  <common-modal
    v-model:modelValue="show"
    className="dialog-self dialog-selfMax modify-review-dialog"
    :title="title"
    width="100vw"
    height="100vh"
    :zIndex="1000"
    @close="removeSongShenDatas"
  >
    <div class="modifySubmissionForReview">
      <!-- projectComponentType 当组件使用时项目类型 -->
      <operate projectComponentType="ys" />
      <ys-index
        ref="mainRontentRef"
        v-if="store.mainContentComponentRefresh"
        projectComponentType="ys"
      ></ys-index>
    </div>
    <div class="footer">
      <a-button type="primary" :loading="btnLoading" @click="saveData"
        >确定</a-button
      >
      <a-button @click="removeSongShenDatas">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch, nextTick, toRaw } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useRoute } from 'vue-router';
import ysIndex from '@/views/projectDetail/customize/MainContent.vue';
import operate from '@/views/projectDetail/customize/operate.vue';

const { ipcRenderer, webFrame } = require('electron');
import feePro from '@/api/feePro';

const route = useRoute();

import { message, Modal } from 'ant-design-vue';
import shApi from '@/api/shApi.js';
const store = projectDetailStore();
const mainRontentRef = ref();
const btnLoading = ref(false);
const isClose = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
  },
  title: {
    type: String,
    default: '修改送审',
  },
  convertToType: {
    type: String,
  },
});
const emit = defineEmits(['update:visible', 'refresh']);
const show = computed({
  get: () => props.visible,
  set: val => {
    emit('update:visible', val);
  },
});
let ysIndexShow = ref(false);
watch(
  () => props.visible,
  val => {
    console.log('修改送审', val);
    if (val) {
      init();
    }
  }
);
const init = () => {
  store.updateSS = true;
  openSongShen();
};

const openSongShen = () => {
  let apiData = {
    ssConstructId: store.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: store.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: store.currentTreeInfo?.ysshUnitId,
    constructId: store.currentTreeGroupInfo?.constructIdYSSH,
    singleId: store.currentTreeGroupInfo?.singleIdYSSH,
    unitId: store.currentTreeInfo?.idYSSH,
  };
  shApi.openSongShen(apiData).then(res => {
    console.log(res, 'openSongShen');
    if (res.status === 200) {
      store.$state.mainContentComponentRefresh = true;
    }
  });
};

const resettingId = val => {
  store.$state.currentTreeGroupInfo.constructId = val.sdConstructId;
  store.$state.currentTreeGroupInfo.singleId = val.sdSingleId;
  store.$state.currentTreeInfo.id = val.sdUnitId;
};
const close = () => {
  store.SET_TYPE('yssh');
  const { constructIdYSSH, singleIdYSSH } = store.currentTreeGroupInfo;
  Object.assign(store.$state.currentTreeGroupInfo, {
    constructId: constructIdYSSH,
    singleId: singleIdYSSH,
  });
  const { idYSSH } = store.currentTreeInfo;
  Object.assign(store.$state.currentTreeInfo, {
    id: idYSSH,
  });
  store.$state.mainContentRefresh = false;
  show.value = false;
  setTimeout(() => {
    store.$state.mainContentRefresh = true;
    store.updateSS = false;
  }, 10);
};

const removeSongShenDatas = () => {
  close();
  let apiData = {
    ssConstructId: store.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: store.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: store.currentTreeInfo?.ysshUnitId,
  };
  shApi.removeSongShenDatas(apiData).then(res => {
    console.log(res, 'res关闭响应');
    if (res.status === 200) {
      // resettingId(res.result);
      store.SET_TYPE('yssh');
      show.value = false;
    }
  });
};
const saveData = () => {
  let apiData = {
    ssConstructId: store.currentTreeGroupInfo?.constructId,
    ssSingleId: store.currentTreeGroupInfo?.singleId,
    ssUnitId: store.currentTreeInfo?.id,
  };
  shApi
    .saveSongShenDatas(apiData)
    .then(res => {
      console.log(res, 'res');
      if (res.status === 200) {
        close();
        emit('refresh');
      }
    })
    .catch(err => {
      console.log(err, 'err');
    });
};
</script>
<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: flex-end;
  position: relative;
  top: 5px;
  :deep(.ant-btn) {
    margin-right: 15px;
  }
}
.modifySubmissionForReview {
  height: calc(100% - 45px);
}
@media (max-width: 1370px) {
  .modifySubmissionForReview {
    :deep(.main-content) {
      height: calc(100% - 96px) !important;
    }
  }
}
</style>
