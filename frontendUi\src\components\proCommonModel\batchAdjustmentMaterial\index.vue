<template>
  <div class="batch-adjustment-material">
    <common-modal
      className="dialog-comm resizeClass"
      title="批量选择调差材料"
      width="800"
      height="620"
      v-model:modelValue="props.visible"
      :mask="false"
      @close="cancel"
    >
      <div class="range-content">
        <div class="search-content">
          <a-checkbox
            v-model:checked="state.checkAll"
            :indeterminate="state.indeterminate"
            @change="onCheckAllChange"
            >所有人材机
          </a-checkbox>
          <a-checkbox-group
            @change="typeChange"
            v-model:value="state.typeSelectValue"
            name="checkboxgroup"
            :options="plainOptions"
          />
          <a-input
            placeholder="请输入名称/编码/关键字"
            @change="inputChange($event)"
            class="input"
            v-model:value="constructName"
          />
          <a-button type="primary" @click="unitRcjCollectSelect">查询</a-button>
        </div>
      </div>
      <vxe-table
        :data="tableData"
        height="400"
        class="table-scrollbar"
        ref="tableRef"
        :row-config="{
          keyField: 'sequenceNbr',
        }"
        :checkbox-config="{
          checkRowKeys: checkRowKeys,
          checkMethod: checkMethod,
          checkField: 'isDifference',
        }"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="checkboxAll"
      >
        <vxe-column type="checkbox" width="60"></vxe-column>
        <vxe-column width="60" title="序号">
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column title="项目编码" field="materialCode"></vxe-column>
        <vxe-column title="类别" field="type"></vxe-column>
        <vxe-column title="名称" field="materialName"></vxe-column>
        <vxe-column title="规格型号" field="specification"></vxe-column>
        <vxe-column title="单位" field="unit"></vxe-column>
        <vxe-column title="合同/确认单价" field="marketPrice"></vxe-column>
        <vxe-column title="合同合价" field="marketPrice"></vxe-column>
      </vxe-table>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="unitRcjCollectSelectNotarize"
          >确定</a-button
        >
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive, ref, watch } from 'vue';
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';

const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'updateData']);
const projectStore = projectDetailStore();

const tableRef = ref();
const state = reactive({
  indeterminate: true,
  checkAll: true,
  typeSelectValue: [],
});
const tableData = ref([]);
const constructName = ref(''); // 搜索字段
const allTypeList = [1, 2, 3, 5];
const checkRowKeys = ref([]); // 默认勾选的数据
const changeList = ref([]); // 状态发生改变的数据值
const plainOptions = [
  {
    label: '人工',
    value: 1,
  },
  {
    label: '材料',
    value: 2,
  },
  {
    label: '机械',
    value: 3,
  },
  {
    label: '主材',
    value: 5,
  },
];

watch(
  () => props.visible,
  () => {
    if (props.visible) {
      Object.assign(state, {
        typeSelectValue: allTypeList,
        indeterminate: false,
      });
      checkRowKeys.value = [];
      unitRcjCollectSelect();
    }
  }
);
watch(
  () => state.typeSelectValue,
  val => {
    state.indeterminate = !!val.length && val.length < plainOptions.length;
    state.checkAll = val.length === plainOptions.length;
    console.log('111111state', state);
  }
);
const cancel = () => {
  emits('update:visible', false);
};

const typeChange = () => {
  unitRcjCollectSelect();
};

const onCheckAllChange = e => {
  console.log('e', e.target);
  Object.assign(state, {
    typeSelectValue: e.target.checked ? allTypeList : [],
    indeterminate: false,
  });
  unitRcjCollectSelect();
  console.log('state', state);
};
const inputChange = () => {};

const unitRcjCollectSelect = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    kind: JSON.parse(JSON.stringify(state.typeSelectValue)),
    name: constructName.value,
    levelType: projectStore.currentTreeInfo.levelType,
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : null,
  };
  console.log('apiData', apiData);
  api.unitRcjCollectSelect(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('tableData', res.result);
      tableData.value = res.result;
      tableData.value.forEach(item => {
        if (item.isDifference) {
          checkRowKeys.value.push(item.sequenceNbr);
        }
      });
    }
  });
};

const selectChangeEvent = ({ checked, row, $table }) => {
  // changeList.value.forEach(item => {
  //   if (item.tempcol === row.tempcol) {
  //
  //   }
  // })
  changeList.value.push({
    tempcol: row.tempcol,
    type: checked,
  });
};

const checkboxAll = (checked, $event) => {
  console.log('checked444444444', checked, $event);
  if (checked.checked) {
    checked.records.forEach(item => {
      changeList.value.push({
        tempcol: item.tempcol,
        type: true,
      });
    });
  } else {
    tableData.value.forEach(item => {
      if (item.type === '人工费') {
        changeList.value.push({
          tempcol: item.tempcol,
          type: true,
        });
      } else {
        changeList.value.push({
          tempcol: item.tempcol,
          type: false,
        });
      }
    });
  }
};

const unitRcjCollectSelectNotarize = () => {
  // const $table = tableRef.value;
  // const selectRecords = $table.getCheckboxRecords();
  // selectRecords.forEach(item => {
  //   let obj = {
  //     tempcol: item.tempcol,
  //     type: item.isDifference
  //   }
  //   changeList.value.push(obj);
  // });
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    selectList: JSON.parse(JSON.stringify(changeList.value)),
    levelType: projectStore.currentTreeInfo.levelType,
  };
  console.log('APIData', apiData);
  api.unitRcjCollectSelectNotarize(apiData).then(res => {
    console.log('res666666666', res);
    if (res.status === 200 && res.result) {
      message.success('批量选择调差材料设置成功');
      changeList.value = [];
      cancel();
      emits('updateData');
    }
  });
};

// 设置是否可点击
const checkMethod = ({ row }) => {
  let tempMaterialCode = row.materialCode.includes('#')
    ? row.materialCode.split('#')[0]
    : row.materialCode;
  if (['10000001', '10000002', '10000003'].includes(tempMaterialCode)) {
    return false;
  } else {
    return true;
  }
};
</script>

<style lang="scss" scoped>
.range-content {
  .search-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    :deep(.ant-input) {
      width: 250px;
    }
  }
}
.footer-btn-list {
  margin-top: 25px;
}
</style>
