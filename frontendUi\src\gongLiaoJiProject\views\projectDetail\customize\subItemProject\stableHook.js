import {
  computed,
  toRaw,
  ref,
  reactive,
  nextTick,
  onActivated,
  onDeactivated,
  onUnmounted,
} from 'vue';
import api from '@gongLiaoJi/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import deMapFun from '../deMap';
import { message } from 'ant-design-vue';
import { eventBus } from '@gongLiaoJi/utils/eventBus.js'

const projectStore = projectDetailStore();

export const stableHook = (
  {
    selectState,
    stableRef,
    currentInfo,
    tableData,
    originalData,
    copyData,
    addRowVisible,
    addData,
    showColumns,
  },
  callBack
) => {
  const sTableState = reactive({
    prevDbTime: null,
    isOpenEditor: false,
  });
  const inputRefs = reactive({});
  const selAllData = reactive([]);
  const setInputRef = (el, field) => {
    inputRefs[field + 'Input'] = el;
  };
  let isBExecuted = ref(false);
  // 打开编辑自动获得焦点
  const openEditor = cellInfos => {
    sTableState.isOpenEditor = true;
    sTableState.prevDbTime = new Date().getTime();
    setTimeout(() => {
      // 清除sTableState.time
      sTableState.prevDbTime = null;
    }, 300);
    nextTick(() => {
      const inputRef = inputRefs[cellInfos.column.field + 'Input'];
      if (inputRef) {
        inputRef.focus();
        if (inputRef && typeof inputRef.select === 'function') {
          inputRef?.select();
        }
      }
    });
  };
  /**
   * @description: 关闭editor
   * @return: void
   */
  const closeEditor = cellInfos => {
    sTableState.isOpenEditor = false;
  };
  // 获取选择行
  const cellMouseup = e => {
    let selData = stableRef.value.getSelectedRange();
    let obj = {
      column: selData[0]?.columns?.[0],
      rowIndex: selData[0]?.endRow?.rowIndex,
    };
    projectStore.SET_LAST_CLICK_TABLE_REF('budgetEditingArea');
    projectStore.SET_LAST_CLICK_TABLE_OBJ(obj);
    let selectArray = [];
    let allData = [];
    stableRef.value.getSelectedRange().map((a, index) => {
      console.log('获取选择行', a);
      selectArray[index] = [a.startRow.rowIndex, a.endRow.rowIndex];
    });
    let selectData = Array.from(
      new Set(
        selectArray.flatMap(([start, end]) =>
          Array.from(
            { length: end > start ? end - start + 1 : start - end + 1 },
            (_, i) => (end > start ? start : end) + i,
          ),
        ),
      ),
    );
    if (selectData.length > 1) {
      let keys = [];
      let rows = [];
      selectData.forEach(a => {
        allData.push(tableData.value[a]);
        keys.push(tableData.value[a].sequenceNbr);
        rows.push(tableData.value[a]);
      });
      selectState.selectedRowKeys = Array.from(
        new Set([...(e.ctrlKey ? selectState.selectedRowKeys : []), ...keys]),
      );
      selectState.selectedRows = [
        ...(e.ctrlKey ? selectState.selectedRows : []),
        ...rows,
      ].filter(
        (item, index, arr) =>
          arr.findIndex(i => i.sequenceNbr === item.sequenceNbr) === index,
      );
      if (!selectState.selectedRowKeys.includes(currentInfo.key)) {
        currentInfo.value = tableData.value.find(
          a => a.sequenceNbr === selectState.selectedRowKeys[0],
        );
      }
    }
    selAllData.value = allData;
  };
  const selectRow = (record, e) => {
    if (e.ctrlKey) {
      const selectedRowKeys = [...selectState.selectedRowKeys];
      const selectRows = [...selectState.selectedRows];
      if (selectedRowKeys.indexOf(record.key) >= 0) {
        // selectedRowKeys.splice(selectedRowKeys.indexOf(record.key), 1);
        // selectRows.splice(selectedRowKeys.indexOf(record.key), 1);
      } else {
        selectedRowKeys.push(record.key);
        selectRows.push(record);
      }
      selectState.selectedRowKeys = selectedRowKeys;
      selectState.selectedRows = selectRows;
    } else {
      currentInfo.value = record;
      selectState.selectedRowKeys = [record.key];
      selectState.selectedRows = [record];
    }
  };
  const rowSelection = computed(() => {
    return {
      hideSelectAll: true,
      fixed: true,
      columnWidth: '28px',
      selectedRowKeys: selectState.selectedRowKeys,
      // onChange: (selectedRowKeys,selectedRows) => {
      //   selectState.selectedRowKeys = selectedRowKeys
      // },
      onSelect: (record, selected, selectedRows, nativeEvent) => {
        console.log('onSelect', record, selected, selectedRows, nativeEvent);
        stableRef.value.clearAllSelectedRange();
        currentInfo.value = record;
        // 选中值极其下级
        let data = findDescendants(tableData.value, record.sequenceNbr).map(
          a => {
            return a.sequenceNbr;
          },
        );
        let rows = findDescendants(tableData.value, record.sequenceNbr);
        // if (!nativeEvent.ctrlKey) {
        //   if (selected) {
        //     selectState.selectedRowKeys = Array.from(
        //       new Set([record.sequenceNbr, ...data])
        //     );
        //     selectState.selectedRows = [record, ...rows].filter(
        //       (item, index, arr) =>
        //         arr.findIndex(i => i.sequenceNbr === item.sequenceNbr) === index
        //     );
        //   } else {
        //     selectState.selectedRowKeys = Array.from(
        //       new Set(
        //         selectState.selectedRowKeys.filter(
        //           item => ![record.sequenceNbr, ...data].includes(item)
        //         )
        //       )
        //     );
        //     selectState.selectedRows = selectState.selectedRows
        //       .filter(item => {
        //         return ![record.sequenceNbr, ...data].includes(
        //           item.sequenceNbr
        //         );
        //       })
        //       .filter(
        //         (item, index, arr) =>
        //           arr.findIndex(i => i.sequenceNbr === item.sequenceNbr) ===
        //           index
        //       );
        //   }
        // } else {
        if (selected) {
          selectState.selectedRowKeys = Array.from(
            new Set([
              ...selectState.selectedRowKeys,
              record.sequenceNbr,
              ...data,
            ]),
          );
          // 检查并添加符合条件的父节点（自底向上）
          let current = record;
          while (current.parentId !== 0 && current.parentId) {
            const parentId = current.parentId;
            const parentNode = tableData.value.find(
              item => item.sequenceNbr === parentId,
            );
            if (!parentNode) break;

            // 获取所有同级节点（当前父节点的直接子节点）
            const siblings = tableData.value.filter(
              item => item.parentId === parentNode.sequenceNbr,
            );
            // 检查所有同级节点是否都已选中
            const allSiblingsSelected = siblings.every(sibling =>
              selectState.selectedRowKeys.includes(sibling.sequenceNbr),
            );
            // 如果所有同级都选中，添加父节点
            if (allSiblingsSelected) {
              selectState.selectedRowKeys = Array.from(
                new Set([
                  ...selectState.selectedRowKeys,
                  parentNode.sequenceNbr,
                ]),
              );
              current = parentNode; // 继续向上检查祖父节点
            } else {
              break; // 无需继续向上检查
            }
          }
        } else {
          selectState.selectedRowKeys = Array.from(
            new Set(
              selectState.selectedRowKeys.filter(
                item => ![record.sequenceNbr, ...data].includes(item),
              ),
            ),
          );
          // 检查并移除父节点（自底向上）
          let current = record;
          while (current.parentId !== 0 && current.parentId) {
            const parentId = current.parentId;
            const parentNode = tableData.value.find(
              item => item.sequenceNbr === parentId,
            );
            if (!parentNode) break;

            // 如果父节点原本是选中的，将其移除
            if (selectState.selectedRowKeys.includes(parentNode.sequenceNbr)) {
              selectState.selectedRowKeys = selectState.selectedRowKeys.filter(
                key => key !== parentNode.sequenceNbr,
              );
            }
            current = parentNode; // 继续向上检查祖父节点
          }
        }
        if(selectState.selectedRowKeys?.length === 0 ) {
          selectState.selectedRowKeys = [record.sequenceNbr];
        }
        selectState.selectedRows = tableData.value.filter(item =>
          selectState.selectedRowKeys.includes(item.sequenceNbr),
        );
        // }
      },
    };
  });
  function findDescendants(items, parentId) {
    const descendants = [];
    // 遍历数组中的每个元素
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      // 如果当前元素的父ID等于给定的parentId
      if (item.parentId === parentId) {
        // 将当前元素添加到后代数组中
        descendants.push(item);
        // 递归查找当前元素的所有后代
        const childDescendants = findDescendants(items, item.sequenceNbr);
        // 将后代的后代也添加到后代数组中
        descendants.push(...childDescendants);
      }
    }

    return descendants;
  }

  const customRow = record => {
    return {
      onClick: e => {
        selectRow(record, e);
      },
    };
  };

  const pasteRowVisible = async code => {
    //复制数据最高层级行
    const row = originalData.value.reduce((max, item) => {
      if (parseInt(item.kind) < parseInt(max)) {
        return item.kind;
      } else {
        return max;
      }
    });
    //查询子节点
    let res = await api.queryDeChildType({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      singleId: '',
      deRowId: currentInfo.value.deRowId,
    });
    let children = res.result;

    // 如果复制是分部行，判断子节点是否有定额
    let unitHasDe = false;
    if (row.kind === '00') {
      let unitRes = await api.queryDeChildType({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        unitId: row?.unitId,
        singleId: '',
        deRowId: row.deRowId,
      });
      unitHasDe = !unitRes.result.some(item =>
        ['00', '01', '02'].includes(item),
      );
    }
    if (code === 'paste') {
      console.log(
        'rowcode ',
        children.length === 0 ||
          !children.some(item => ['00', '01', '02'].includes(item)),
      );
      // 最高层级行是定额
      if (deMapFun.isDe(row.kind)) {
        // 目标层级行是定额
        if (deMapFun.isDe(currentInfo.value.kind)) {
          if (
            currentInfo.value.kind === '05' ||
            currentInfo.value.kind === '07'
          ) {
            return true;
          } else {
            return false;
          }
        } else if (!deMapFun.isDe(currentInfo.value.kind)) {
          return true;
        } else {
          return true;
        }
      }
      // 最高层级行是分部
      if (['01', '02'].includes(row.kind)) {
        // 如果没有子节点
        if (deMapFun.isDe(currentInfo.value.kind)) {
          return true;
        } else if (currentInfo.value.kind === '00') {
          return true;
        } else {
          return false;
        }
      }
      // 最高层级行是项目
      if (['00'].includes(row.kind)) {
        return true;
        // if (!deMapFun.isDe(currentInfo.value.kind) ) {
        //   return false;
        // } else {
        //   return true;
        // }
      }
    }
    if (code === 'pasteChild') {
      // 选中行为定额
      if (deMapFun.isDe(row.kind)) {
        // 目标层级行是定额
        if (
          deMapFun.isDe(currentInfo.value.kind) &&
          currentInfo.value.kind !== '03'
        ) {
          return true;
        } else if (
          children.length === 0 &&
          ['00', '01', '02'].includes(currentInfo.value.kind)
        ) {
          return false;
        } else if (children.length === 0) {
          return true;
        } else if (!children.some(item => ['00', '01', '02'].includes(item))) {
          return false;
        } else {
          return true;
        }
      }
      // 最高层级行是分部
      if (['01', '02'].includes(row.kind)) {
        // 如果没有子节点
        if (children.length === 0 && !deMapFun.isDe(currentInfo.value.kind)) {
          return false;
        } else if (children.some(item => ['00', '01', '02'].includes(item))) {
          return false;
        } else {
          return true;
        }
      }
      if (['00'].includes(row.kind)) {
        console.log('000', unitHasDe, children);
        // 如果没有子节点
        if (
          unitHasDe &&
          children.some(item => ['00', '01', '02'].includes(item))
        ) {
          return true;
        } else if (
          unitHasDe &&
          !children.some(item => ['00', '01', '02'].includes(item))
        ) {
          return false;
        } else if (
          !unitHasDe &&
          children.some(item => ['00', '01', '02'].includes(item))
        ) {
          return false;
        } else if (
          !unitHasDe &&
          !children.some(item => ['00', '01', '02'].includes(item))
        ) {
          return false;
        }
      }
    }
  };
  const cellKeydown = (event, { cellPosition, isEditing }) => {
    if (
      (event.code === 'ArrowUp' || event.code === 'ArrowDown') &&
      !sTableState.isOpenEditor
    ) {
      if(!cellPosition.column?.field) {
        arrowSelectCell();
        return;
      }
      let rowIndex =
        event.code === 'ArrowUp'
          ? cellPosition.rowIndex - 1
          : cellPosition.rowIndex + 1;
      currentInfo.value = tableData.value[rowIndex];
      if (currentInfo.value) {
        selectState.selectedRowKeys = [currentInfo?.value.sequenceNbr];
        projectStore.SET_LAST_CLICK_TABLE_OBJ({
          rowIndex: rowIndex,
          column: cellPosition.column,
        });
      }
    }

    if (
      ['ArrowLeft', 'ArrowRight'].includes(event.code) &&
      !sTableState.isOpenEditor
    ) {
      if(!cellPosition.column?.field) {
        arrowSelectCell();
        return;
      }
      let currentColumnIndex = showColumns.value?.findIndex(
        item => item.field == cellPosition.column.field
      );
      let columnIndex =
        event.code === 'ArrowLeft'
          ? currentColumnIndex - 1
          : currentColumnIndex + 1;
      let toColumn = showColumns.value?.[columnIndex];
      if (toColumn) {
        projectStore.SET_LAST_CLICK_TABLE_OBJ({
          rowIndex: cellPosition.rowIndex,
          column: toColumn,
        });
      }
    }
    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
      if(!cellPosition.column?.field) {
        enterToNextLine();
        return;
      }
      nextTick(async () => {
        // 判断当前单元格是否可编辑
        const canEdit = cellPosition.column?.editable &&
          typeof cellPosition.column.editable === 'function' &&
          cellPosition.column.editable({
          record: tableData.value?.[cellPosition.rowIndex],
        });
        // 如果当前单元格可编辑且未处于编辑状态
        if (!isEditing && canEdit && event.target.tagName !== 'INPUT') {
          // 打开编辑器
          stableRef.value.openEditor([
            {
              columnKey: cellPosition.column.dataIndex,
              rowKey: tableData.value[cellPosition.rowIndex].key,
            },
          ]);
          const cellInfos = { column: cellPosition.column };
          openEditor(cellInfos);
          setTimeout(() => {
            // 获取输入框的焦点
            const inputRef = inputRefs[cellPosition.column.dataIndex + 'Input'];
            console.log(inputRef);
            if (inputRef) {
              inputRef.focus();
              if (!['单位', '取费专业'].includes(cellPosition.column.title)) {
                if (inputRef && typeof inputRef.select === 'function') {
                  inputRef?.select();
                }
              }
            }
          }, 50);
        } else {
          // 当选择“工程量”单元格或已经跳转到“工程量”单元格后，
          // 单击回车进入编辑状态，并全选当前单元格内所有字段，
          // 再次单击回车结束编辑状态，当从编辑状态结束后，
          // 再次单击回车，将跳转到下一行“工程量”单元格位置。
          const deTypes = ['-1', '04', '06', '07', '08', '09', '10'];
          const toNextLine = toLine => {
            cellPosition.rowIndex = toLine
              ? toLine.index - 1
              : cellPosition.rowIndex + 1;
            currentInfo.value = tableData.value[cellPosition.rowIndex];
          };
          const addEmptyDe = (rowInfo) => {
            addData('-1', rowInfo);
          }

          // 通用函数：查找并处理主材设备后的下一行
          const handleAfterMaterial = startNode => {
            // 获取同一父节点的所有子节点，即所以主材设备按，index升序排序
            const siblings = tableData.value
              .filter(item => item.parentId === startNode.parentId)
              .sort((a, b) => a.index - b.index);
            // 找到最后一个主材设备
            const lastSibling = siblings[siblings.length - 1];
            // 这里是index， index 从1开始的
            const nextIndex = lastSibling.index;
            // 最后一个主材设备的下一行
            const nextNode = tableData.value[nextIndex];
            if (!nextNode) {
              // 没有下一行则添加
              addEmptyDe(lastSibling);
            } else if (['0', '01', '02'].includes(nextNode?.type)) {
              // 下一行是单位工程/分部/子分部
              addEmptyDe(lastSibling);
            } else if (deTypes.includes(nextNode?.type)) {
              // 下一行是定额
              toNextLine(nextNode);
            } else {
              // 默认跳转到下一行
              toNextLine();
            }
          };
          // 处理单位工程，分部，子分部
          const handleUnitProject = () => {
            const optionMenu = toRaw(currentInfo.value?.optionMenu);
            if(optionMenu?.includes(2)) {
              addEmptyDe(currentInfo.value);
            } else {
              toNextLine();
            }
          };
          // 处理主材设备
          const handleMaterialDevice = () => {
            handleAfterMaterial(currentInfo.value);
          };
          // 处理定额
          const handleQuota = () => {
            const nextNode = tableData.value[cellPosition.rowIndex + 1];
            if (!nextNode) {
              addEmptyDe(currentInfo.value);
            }
            switch (nextNode?.type) {
              case '05': // 下一行是主材设备
                // 查找主材设备后的下一行
                handleAfterMaterial(nextNode);
                break;
              case '0':
              case '01':
              case '02': // 下一行是单位工程/分部/子分部
                addEmptyDe(currentInfo.value);
                break;
              default:
                if (deTypes.includes(nextNode?.type)) {
                  // 下一行是定额
                  toNextLine();
                }
                break;
            }
          };

          const ExecuteMainProcess = async () => {
            // 查看是否可以添加定额
            await addRowVisible(currentInfo.value);
            if (['0', '01', '02'].includes(currentInfo.value?.type)) {
              handleUnitProject();
            } else if (currentInfo.value?.type == '05') {
              // // 主材设备
              handleMaterialDevice();
            } else if (deTypes.includes(currentInfo.value?.type)) {
              // 定额
              handleQuota();
            }
              stableRef.value.closeEditor();
              closeEditor();
              intoCell(cellPosition);
              projectStore.SET_LAST_CLICK_TABLE_OBJ({
                rowIndex: cellPosition.rowIndex,
                column: cellPosition.column,
              });
          };
          if(!canEdit) {
            ExecuteMainProcess();
          } else if (cellPosition.column?.field === 'deCode') {
            eventBus.$on('apiResult', res => {
              if (res == 1 || res == 2) {
                ExecuteMainProcess();
              }
              eventBus.$off('apiResult');
            });
            inputRefs.deCodeInput.blur();
          } else {
            ExecuteMainProcess();
          }
        }
      });
      return false;
    }
    // if (sTableState.isOpenEditor) {
    //   return false;
    // }
    // 删除
    if (event.code === 'Delete') {
      callBack('delete');
    }
    if (
      (event.ctrlKey && event.code === 'KeyC') ||
      (event.ctrlKey && event.code === 'KeyV')
    ) {
      copyAndPaste(event);
      return false;
    } else {
      return true;
    }
  };

  const intoCell = (cellPosition, dataIndex) => {
    // 获取对应点击的单元格
    setTimeout(() => {
      Array.from(document.querySelectorAll('.surely-table-row')).map(a => {
        if (a.dataset.rowKey === tableData.value[cellPosition.rowIndex]?.key) {
          Array.from(a.querySelectorAll('.surely-table-cell')).map(b => {
            // 如果当前单元格处于编辑状态
            if (
              b.dataset.columnKey ===
              (dataIndex || cellPosition.column.dataIndex)
            ) {
              // 注册点击事件，否则表格自动失去焦点，注：click事件不生效
              let event = new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
              });
              let event2 = new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
              });

              b.querySelector('.surely-table-cell-content').dispatchEvent(
                event,
              );
              b.querySelector('.surely-table-cell-content').dispatchEvent(
                event2,
              );
              // 自动横向滚动
              // stableRef.value.scrollTo(
              //   {
              //     columnKey: dataIndex || cellPosition.column.dataIndex,
              //   },
              //   'auto'
              // );
            }
          });
        }
      });
    }, 50);
  };
  /**
   *
   * @param {*} event
   * @param {*} isHandCopy 是否手动执行复制操作
   */
  const copyAndPaste = async (event, isHandCopy = false) => {
    if (event.ctrlKey && event.code === 'KeyC') {
      const copyTxt =  window.getSelection().toString().length
      if(sTableState.isOpenEditor){
        // 打开编辑器
        if(copyTxt){
          copyData.value = null
        }else{
          return 
        }
      }else{
        copyFun();
      }
      
    }
    if (event.ctrlKey && event.code === 'KeyV') {
      if (copyData.value?.legnth == 0) return; //
      if (await pasteRowVisible('paste')) return message.error('该行不可粘贴');
      pasteFun();
    }
  };

  const copyFun = args => {
    if (!selectState.selectedRowKeys.length === 0) {
      return message.error('暂无选中数据');
    }
    copyData.value = selectState.selectedRowKeys;
    originalData.value = tableData.value.filter(a =>
      copyData.value.includes(a.sequenceNbr),
    );
    if (
      toRaw(copyData.value).length == 1 &&
      toRaw(copyData.value)[0] == tableData.value[0].sequenceNbr && ['00','0'].includes(tableData.value[0]?.kind)
    ) {
      copyData.value = findDescendants(
        tableData.value,
        toRaw(copyData.value)[0],
      ).map(a => {
        return a.sequenceNbr;
      });
    } else if(['00','0'].includes(tableData.value[0]?.kind)) {
      copyData.value = toRaw(copyData.value).filter(
        a => a != tableData.value[0].sequenceNbr,
      );
    }
    console.log('复制成功', copyData.value);
    message.success('复制成功');
    args?.hidePopup();
  };
  const pasteFun = async (args, code) => {
    if (copyData.value.legnth == 0) return message.error('无复制数据');
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      oUnitId: projectStore.currentTreeInfo?.id,
      prevDeRowId: args?.record?.sequenceNbr || currentInfo.value.sequenceNbr,
      type: code === 'pasteChild' ? 'child' : '',
      idList: toRaw(copyData.value),
    };
    console.log('pasteDe', apiData, copyData.value);
    api.pasteDe(apiData).then(res => {
      if (res.status === 200) {
        stableRef.value.clearAllSelectedRange();
        // copyData.value = null;  //修复bug25829允许多次粘贴
        message.success('粘贴成功');
        // queryBranchDataById();
        callBack('refresh');
      } else {
        copyData.value = null;
        message.error(res.message);
      }
    });
    args?.hidePopup();
  };
  const selectedRowKeyFirst = () => {
    return tableData.value
      .filter(a => selectState.selectedRowKeys?.includes(a.sequenceNbr))
      .sort((a, b) => a?.index < b?.index)
      .map(a => a.sequenceNbr)?.[0];
  };
  function enterToNextLine() {
    let selectedSeq = selectedRowKeyFirst();
    if (selectedSeq) {
      const rowIndex = tableData.value.findIndex(
        item => item.sequenceNbr == selectedSeq
      );
      const nextRowIndex = rowIndex + 1;
      if (tableData.value?.[nextRowIndex]) {
        currentInfo.value = tableData.value[nextRowIndex];
        selectState.selectedRowKeys = [currentInfo.value.sequenceNbr];
      }
    }
    // stableRef.value?.scrollTo(
    //   {
    //     rowKey: currentInfo.value?.sequenceNbr,
    //   },
    //   'auto'
    // );
  }
  function arrowSelectCell() {
    let selectedSeq = selectedRowKeyFirst();
      let clickTableObj = {
        rowIndex: 0,
        column: showColumns.value?.[0],
      };
      if (selectedSeq) {
        // 如果有选中行，单元格选中定位到选中行的第一列
        const rowIndex = tableData.value?.findIndex(
          item => item.sequenceNbr == selectedSeq
        );
        clickTableObj.rowIndex = rowIndex;
      } else {
        // 如果没有选中行，设置一下选中行
        currentInfo.value = tableData.value?.[clickTableObj.rowIndex];
      }
      // stableRef.value?.scrollTo(
      //   {
      //     rowKey: currentInfo.value?.sequenceNbr,
      //   },
      //   'auto'
      // );
      intoCell(clickTableObj);
  }
  const resetSelectedRange = () => {
    setTimeout(() => {
      // 新插入数据或者列表刷新之后，根据上一条数据的选中列，选中当前行的对应列
      const selectedRange = stableRef.value.getSelectedRange();
      let columnKeyObj = {
        columnStartKey: '',
        columnEndKey: '',
      };
      if (selectedRange.length) {
        columnKeyObj.columnStartKey = selectedRange[0].startColumn.dataIndex;
        columnKeyObj.columnEndKey = selectedRange[0].startColumn.dataIndex;
      }
      stableRef.value.clearAllSelectedRange();
      const index = tableData.value.findIndex(
        item => item.sequenceNbr == currentInfo.value?.sequenceNbr
      );
      let selectRange = {
        rowStartIndex: index,
        rowEndIndex: index,
        ...columnKeyObj,
      };
      console.log('🚀 ~ resetSelectedRange ~ selectRange:', selectRange, selectedRange);
      stableRef.value.appendCellToSelectedRange(selectRange);
    }, 20);
  };
  const handleKeyDown = event => {
    if (
      ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.code)
    ) {
      arrowSelectCell();
    }
    if (['NumpadEnter', 'Enter'].includes(event.code)) {
      enterToNextLine();
    }
  };
  onActivated(() => {
    window.addEventListener('keydown', handleKeyDown);
  });
  onDeactivated(() => {
    window.removeEventListener('keydown', handleKeyDown);
  });
  // 确保组件卸载时也能清除监听（兜底逻辑）
  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown);
  });
  return {
    sTableState,
    inputRefs,
    setInputRef,
    openEditor,
    closeEditor,
    cellMouseup,
    rowSelection,
    customRow,
    intoCell,
    pasteRowVisible,
    cellKeydown,
    copyAndPaste,
    copyFun,
    pasteFun,
    selAllData,
    resetSelectedRange
  };
};
