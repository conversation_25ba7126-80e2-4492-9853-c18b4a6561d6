<template>
  <common-modal
    className="dialog-comm resizeClass"
    title="设置主要材料"
    width="900"
    v-model:modelValue="props.materialVisible"
    :mask="false"
    @close="cancel"
    @show="open"
  >
    <div class="setting-type">
      <strong>设置方式：</strong>
      <a-radio-group
        v-model:value="settingType"
        name="settingType"
      >
        <a-radio
          :value="item.value"
          v-for="item of settingOptions"
          :key="item.value"
        >{{ item.label }}</a-radio>
      </a-radio-group>
      <strong style="margin:0 0 0 50px">应用至：</strong>
      <a-radio-group
        v-model:value="appliedRange"
        name="appliedRange"
      >
        <a-radio
          :value="item.value"
          v-for="item of useOptions"
          :key="item.value"
        >{{ item.label }}</a-radio>
      </a-radio-group>
    </div>
    <div v-if="settingType === 1">
      <div class="search-content">
        <div class="checkbox">
          <a-checkbox
            style="margin-right: 8px"
            v-model:checked="state.checkAll"
            :indeterminate="state.indeterminate"
            @change="onCheckAllChange"
          >所有人材机
          </a-checkbox>
          <a-checkbox-group
            @change="typeChange"
            v-model:value="state.typeSelectValue"
            name="checkboxGroup"
            :options="plainOptions"
          />
        </div>

        <a-input
          placeholder="请输入名称/编码/关键字"
          class="input"
          v-model:value="constructName"
          @pressEnter="getSearchList"
        >
          <template #suffix>
            <search-outlined
              style="color: rgba(191, 191, 191, 1)"
              @click="getSearchList"
            />
          </template>
        </a-input>
      </div>
      <div class="all-check">
        <a-checkbox-group
          @change="allCheckChange"
          v-model:value="state.allCheckList"
          name="allCheckboxGroup"
          :options="allCheckOptions"
        />
      </div>
      <vxe-table
        :data="tableDataList"
        height="360"
        class="table-scrollbar"
        ref="tableRef"
        :row-config="{
          keyField: 'sequenceNbr',
        }"
        :checkbox-config="{
          checkRowKeys: defineCheckRowKeys,
          checkMethod: checkMethod,
          checkField: 'mainMaterial',
        }"
        :scroll-y="{ enabled: true, gt: 20 }"
        show-overflow
        @checkbox-change="selectChangeEvent"
        @checkbox-all="checkboxAll"
      >
        <vxe-column
          type="checkbox"
          width="40"
        ></vxe-column>
        <vxe-column
          width="60"
          title="序号"
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          title="项目编码"
          field="materialCode"
        ></vxe-column>
        <vxe-column
          title="类别"
          field="type"
        ></vxe-column>
        <vxe-column
          title="名称"
          width="100"
          field="materialName"
        ></vxe-column>
        <vxe-column
          title="规格型号"
          field="specification"
        ></vxe-column>
        <vxe-column
          title="单位"
          field="unit"
        ></vxe-column>
        <vxe-column
          title="数量"
          field="totalNumber"
        ></vxe-column>
        <vxe-column
          title="市场价"
          field="marketPrice"
        ></vxe-column>
        <vxe-column
          title="供货方式"
          field="ifDonorMaterial"
        >
          <template #default="{ row }">
            {{ getDonorMaterialText(row.ifDonorMaterial) }}
          </template>
        </vxe-column>
        <vxe-column
          title="是否暂估"
          field="ifProvisionalEstimate"
        >
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.ifProvisionalEstimate"
              size="small"
              content=""
              :checked-value="1"
              :unchecked-value="0"
              disabled
            ></vxe-checkbox>
          </template>
        </vxe-column>
        <vxe-column
          title="产地"
          field="producer"
        ></vxe-column>
        <vxe-column
          title="厂家"
          field="manufactor"
        ></vxe-column>
        <vxe-column
          title="备注"
          field="remark"
        ></vxe-column>
      </vxe-table>
      <div class="foot-info">
        <div class="view-filter-check">
          过滤查看：
          <a-radio-group
            v-model:value="checkFilterVal"
            @change="checkFilterChange"
            name="checkFilterVal"
          >
            <a-radio
              :value="item.value"
              v-for="item of checkFilterOptions"
              :key="item.value"
            >{{ item.label }}</a-radio>
          </a-radio-group>
        </div>
        <div class="footer-btn-list">
          <a-button @click="cancel">取消</a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="saveHandler"
          >确定</a-button>
        </div>
      </div>
    </div>
    <div
      v-else
      class="automatic-type"
    >
      <!-- 自动设置 -->
      <a-radio-group
        v-model:value="automaticType"
        name="automaticType"
      >
        <a-radio
          :value="item.key"
          v-for="item of automaticOptions"
          :key="item.key"
          :style="{ display: 'flex', padding: '5px 0' }"
        >
          {{ item.label }}
          <a-input
            style="width: 60px; margin: 0 9px"
            v-if="item.label2"
            @blur="inputBlur($event, item.key)"
            v-model:value="item.value"
          /><span v-if="item.label2">{{ item.label2 }}</span>
        </a-radio>
      </a-radio-group>
    </div>
    <div
      class="footer-btn-list"
      v-if="settingType === 0"
    >
      <a-button @click="cancel">取消</a-button>
      <a-button
        type="primary"
        :loading="loading"
        @click="saveHandler"
      >确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { reactive, ref, watch, computed } from 'vue';
import api from '@/api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import XEUtils from 'xe-utils';
import { SearchOutlined } from '@ant-design/icons-vue';
import { disposeDeTypeData } from '@/hooks/publicApiData';
import feePro from '@/api/feePro';
import { pureNumber } from '@/utils/index';

const props = defineProps(['materialVisible']);
const emits = defineEmits(['update:materialVisible', 'successCallback']);
const projectStore = projectDetailStore();
let loading = ref(false);

const donorMaterialList = [
  {
    label: '自行采购',
    value: 0,
  },
  {
    label: '甲方供应',
    value: 1,
  },
  {
    label: '甲定乙供',
    value: 2,
  },
];

const getDonorMaterialText = value => {
  const item = donorMaterialList.find(item => item.value === value);
  return item ? item.label : '';
};
// ====================设置方式
let settingType = ref(0);
let appliedRange = ref(1);
const settingOptions = [
  {
    value: 0,
    label: '自动设置',
  },
  {
    value: 1,
    label: '手动设置',
  },
];
const useOptions = [
  {
    value: 1,
    label: '仅应用当前单位工程',
  },
  {
    value: 2,
    label: '同专业单位工程',
  },
  {
    value: 3,
    label: '全局',
  },
];
let automaticType = ref(1);
const automaticOptions = ref([
  {
    value: 50,
    key: 1,
    label: '方式一：取材料、设备用量前',
    label2: '位的数据',
  },
  {
    value: 50,
    key: 2,
    label: '方式二：取材料、设备市场价前',
    label2: '位的数据',
  },
  {
    key: 3,
    label: '方式三：取主材和设备',
  },
]);

const inputBlur = (event, key) => {
  let num = Number(pureNumber(event.target.value, 0)).toFixed(0);
  setAutomationVal(key, num <= 0 ? 50 : num);
};

/**
 * 根据key设置value
 */
const setAutomationVal = (key, value) => {
  const infoIndex = automaticOptions.value.findIndex(item => item.key === key);
  if (infoIndex < 0) return;
  automaticOptions.value[infoIndex].value = value;
};
const proportionVal = computed(() => {
  const info = automaticOptions.value.find(
    item => item.key === automaticType.value
  );
  return Number(info?.value) || 0;
});
// -----------------------------过滤查看
let checkFilterVal = ref(0);
const checkFilterOptions = [
  {
    value: 0,
    label: '所有',
  },
  {
    value: 1,
    label: '已勾选',
  },
  {
    value: 2,
    label: '未勾选',
  },
];
const checkFilterChange = () => {
  getSearchList();
};

let allCheckOptions = ref([
  {
    value: 5,
    label: '全选主材',
  },
  {
    value: 4,
    label: '全选设备',
  },
  {
    value: -1,
    label: '全选价格调整数据', // 市场价和定额价不一致
  },
]);
const allCheckChange = () => {
  console.log(checkCodeKeys);
  tableDataList.value = tableDataList.value.map(item => {
    item.mainMaterial = allCheckFilter(item);
    const index = checkCodeKeys.findIndex(
      checkCode => item.materialCode === checkCode
    );
    if (index >= 0) {
      if (!item.mainMaterial) {
        checkCodeKeys.splice(index, 1);
      }
    } else {
      if (item.mainMaterial) {
        checkCodeKeys.push(item.materialCode);
      }
    }
    return item;
  });
};
const allCheckFilter = row => {
  const checkVal = state.allCheckList;
  return (
    checkVal.includes(row.kind) ||
    (checkVal.includes(-1) && row.dePrice !== row.marketPrice)
  );
};
const tableRef = ref();
const state = reactive({
  indeterminate: true,
  checkAll: true,
  typeSelectValue: [],
  allCheckList: [],
});
const tableDataList = ref([]);

const allTypeList = () => {
  return plainOptions.value.map(item => item.value);
};
const defineCheckRowKeys = ref([]); // 默认勾选的数据
const plainOptions = ref([
  {
    label: '人工',
    value: 1,
  },
  {
    label: '材料',
    value: 2,
  },
  {
    label: '机械',
    value: 3,
  },
  {
    label: '主材',
    value: 5,
  },
  {
    label: '设备',
    value: 4,
  },
]);

const init = () => {
  Object.assign(state, {
    typeSelectValue: allTypeList(),
    indeterminate: false,
    allCheckList: [],
  });
  constructName.value = '';
  defineCheckRowKeys.value = [];
  checkCodeKeys = [];
  appliedRange.value = 1;
};

const open = () => {
  init();
  getMaterialList();
  getHumanMachineData();
};

watch(
  () => state.typeSelectValue,
  val => {
    state.indeterminate =
      !!val.length && val.length < plainOptions.value.length;
    state.checkAll = val.length === plainOptions.value.length;
  }
);

// -----------------------搜索
let tableData = ref([]);
const constructName = ref(''); // 搜索字段
const getSearchList = () => {
  tableDataList.value = tableData.value.filter(item => {
    return checkFilter(item) && kindFilter(item.kind) && keywordFilter(item);
  });
};

const getHumanMachineData = () => {
  let formData = {
    type: projectStore.currentTreeInfo.levelType === 1 ? 1 : 2,
    kind: 0,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    formData.singleId = projectStore.currentTreeInfo?.parentId; //单项ID
    formData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  feePro
    .queryConstructRcjByDeId(formData)
    .then(res => {
      if (res.status === 200 && res.result && res.result.length > 0) {
        let num = 1;
        res.result &&
          res.result.map((item, index) => {
            item.dispNo = num++;
            item.type = getType(item.kind);
            item.donorMaterialNumber =
              Number(item.donorMaterialNumber) === 0
                ? ''
                : item.donorMaterialNumber;
            item.origindonorMaterialNum = item.donorMaterialNumber
              ? item.donorMaterialNumber
              : '0';
            if (item.mainMaterial) {
              checkCodeKeys.push(item.materialCode);
            }
          });
        tableData.value = disposeDeTypeData(res.result, true, true);
      } else {
        tableData.value = [];
      }
      tableDataList.value = XEUtils.clone(tableData.value, true);
      console.log(tableDataList.value);
    })
    .finally(() => {
      // loading.value = false;
    });
};
const getType = type => {
  let value = '';
  switch (type) {
    case 0:
      value = '其他费';
      break;
    case 1:
      value = '人工费';
      break;
    case 2:
      value = '材料费';
      break;
    case 3:
      value = '机械费';
      break;
    case 4:
      value = '设备费';
      break;
    case 5:
      value = '主材费';
      break;
    case 6:
      value = '商砼';
      break;
    case 7:
      value = '砼';
      break;
    case 8:
      value = '浆';
      break;
    case 9:
      value = '商浆';
      break;
    case 10:
      value = '配比';
      break;
  }
  return value;
};

/**
 * 勾选过滤
 * @param {*} item
 */
const checkFilter = item => {
  if (checkFilterVal.value === 0) return true;
  if (checkFilterVal.value === 1 && item.mainMaterial) return true;
  if (checkFilterVal.value === 2 && !item.mainMaterial) return true;
  return false;
};
/**
 * 类型筛选
 * @param {*} kind
 * kind为2材料包含“砼“、”商砼“、”浆“、”商浆“、”配比“
 */
const kindFilter = kind => {
  let kindList = state.typeSelectValue.includes(2)
    ? [...state.typeSelectValue, 6, 7, 8, 9, 10]
    : state.typeSelectValue;
  return kindList.includes(kind);
};
/**
 * 关键词筛选
 * @param {*} row
 */
const keywordFilter = row => {
  if (!constructName.value) return true;
  return (
    row.materialCode?.includes(constructName.value) ||
    row.materialName?.includes(constructName.value)
  );
};

const cancel = () => {
  emits('update:materialVisible', false);
};

const typeChange = e => {
  getSearchList();
};

const onCheckAllChange = e => {
  Object.assign(state, {
    typeSelectValue: e.target.checked ? allTypeList() : [],
    indeterminate: false,
  });
  getSearchList();
};

const getMaterialList = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiData', apiData);
  api.getUnitMainMaterialSetting(apiData).then(res => {
    if (res.status === 200) {
      console.log(res);
      const { pattern, proportion, type, materialCodeList } = res.result;
      settingType.value = type;
      // appliedRange.value = appliedRange;
      automaticType.value = pattern;
      setAutomationVal(pattern, proportion);
    }
  });
};
let checkCodeKeys = [];
const selectChangeEvent = e => {
  const {
    row: { materialCode },
    checked,
  } = e;
  setCheckCodeKeys(checked, materialCode);
};
const setCheckCodeKeys = (checked, materialCode) => {
  const index = checkCodeKeys.findIndex(item => item === materialCode);
  if (checked) {
    if (index < 0) checkCodeKeys.push(materialCode);
  } else {
    if (index >= 0) {
      checkCodeKeys.splice(index, 1);
    }
  }
};

const checkboxAll = (checked, $event) => {
  if (checked.checked) {
    checkCodeKeys = tableDataList.value.map(item => item.materialCode);
  } else {
    checkCodeKeys = [];
  }
};

const saveHandler = () => {
  loading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: settingType.value,
    appliedRange: appliedRange.value,
    pattern: automaticType.value,
    proportion: proportionVal.value,
    materialCodeList: [],
  };
  if (settingType.value === 1) {
    apiData.materialCodeList = checkCodeKeys;
  }
  console.log('APIData', apiData);
  api.updateUnitMainMaterialSetting(apiData).then(res => {
    console.log(res);
    if (res.status === 200) {
      emits('successCallback');
      loading.value = false;
      message.success('设置成功');
      cancel();
    }
  });
};

// 设置是否可点击
const checkMethod = ({ row }) => {
  return true;
};
</script>

<style lang="scss" scoped>
.foot-info {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .footer-btn-list {
    margin-top: 0;
  }
}
.automatic-type {
  padding: 10px 15px;
  border: 1px solid #d9d9d9;
}
.setting-type {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  color: #000;
  strong {
    font-weight: bold;
    font-size: 14px;
    color: #000000;
  }
}
.search-content {
  display: flex;
  align-items: center;
  margin-top: 5px;
  padding: 15px 0;
  border-top: 1px solid #b9b9b9;
  border-bottom: 1px solid #b9b9b9;
  .checkbox {
    display: flex;
    align-items: center;
    flex: 1;
  }
  :deep(.ant-input-affix-wrapper) {
    width: 250px;
  }
}
.all-check {
  margin: 16px 0;
}
.footer-btn-list {
  margin-top: 25px;
}
</style>
