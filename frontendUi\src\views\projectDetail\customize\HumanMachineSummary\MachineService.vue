<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-15 16:09:32
-->
<template>
  <div class="table-content">
    <!-- <p class="title"><span class="text">人材机服务</span></p> -->
    <p class="selectTab">
      <a-radio-group
        v-model:value="activeKey"
        :style="{ marginBottom: '8px' }"
      >
        <a-radio-button
          :value="item.key"
          v-for="item of activeOptions"
        >{{ item.tab }}</a-radio-button>
      </a-radio-group>
      <!-- <span class="showTitle">
        {{ showInfo?.materialName }}
      </span> -->
    </p>
    <p class="searchTab">
      <span class="label">
        {{activeKey===2?'推荐数据类型':'地区'}}

      </span>
      <!-- 信息价增加二级县区 -->
      <a-cascader
        v-if="activeKey===0"
        size="small"
        :allowClear="false"
        v-model:value="regionList[activeKey].selectValue.cityVal"
        placeholder="请选择地区"
        :options="regionList[activeKey].cityList"
        style="max-width: 181px"
        @change="handleChange('city',regionList[activeKey].selectValue.cityVal)"
      />
      <a-select
        v-else
        ref="select"
        size="small"
        v-model:value="regionList[activeKey].selectValue.cityVal"
        style="width: 120px"
        @change="handleChange('city',regionList[activeKey].selectValue.cityVal)"
      >
        <a-select-option
          v-for="item in regionList[activeKey].cityList"
          :value="item"
        >{{ item }}</a-select-option>
      </a-select>
      <span
        class="label"
        style="margin-left:20px"
      >
        期数
      </span>
      <a-select
        size="small"
        ref="select"
        v-model:value="regionList[activeKey].selectValue.dateVal"
        style="width: 120px"
        @change="handleChange('month')"
      >
        <a-select-option
          :value="data"
          v-for="data in regionList[activeKey].showDateList"
        >{{ data }}</a-select-option>
      </a-select>
      <a-input-search
        size="small"
        v-model:value="searchName"
        :maxlength="50"
        :placeholder="'请输入名称查询'"
        style="width: 250px; margin:auto 50px ;height: 25px;"
        :class="!searchName?'noAllow':''"
        @search="onSearch"
      />
    </p>
    <div
      class="content"
      v-if="isOnline"
    >
      <div
        class="content-leftTree"
        v-if="!isContract"
      >
        <p class="content-leftTree-title">
          所有材料类别
        </p>
        <a-tree
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeData"
          @select="selectChildren"
          style="z-index: 10;margin-left: 5px;"
          :show-line="true"
          :show-icon="false"
        >
          <template #icon><carry-out-outlined /></template>
          <template #title="{ dataRef ,key ,node}">
            <span :class="hisSelect===dataRef.name?'color_light':''">
              {{ dataRef.name }}
            </span>
          </template>
          <template #switcherIcon="{ dataRef, defaultIcon }">
            <component :is="defaultIcon" />
          </template>
        </a-tree>

      </div>
      <div
        class="content-leftTitle"
        v-if="isContract"
      >
        <p class="content-leftTitle-title">
          所有材料类别
        </p>
      </div>
      <div class="content-rightTable">
        <!-- <p class="content-rightTable-select">
          <a-radio-group
            v-model:value="selectTableType"
            :style="{ marginBottom: '8px' }"
            @change="changeGrid"
          >
            <a-radio-button
              :value="item.key"
              v-for="item of typeOptions"
            >{{ item.tab }}</a-radio-button>
          </a-radio-group>
        </p> -->
        <vxe-grid
          class="trends-table-column"
          v-bind="gridOptions"
          ref="rightTable"
          height="auto"
          v-on="gridEvents"
          :loading="tableLoading"
        >
          <template #empty>
            <span style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          ">
              <img
                :src="getUrl('newCsProject/none.png')"
                style="margin: auto"
              />
            </span>
          </template>
        </vxe-grid>
      </div>
      <div
        class="btnExpand"
        :style="{ left: isContract ? '2%' : '24%' }"
      >
        <div
          class="btn"
          @click.stop="contractHandle"
        >
          <img
            :src="isContract ? getUrl('expandnew.png') : getUrl('retractnew.png')"
            alt=""
          />
        </div>
      </div>
    </div>
    <div
      class="content"
      v-if="!isOnline"
    >
      <img
        :src="getUrl('newCsProject/none.png')"
        style="margin: auto;
"
      />
    </div>
  </div>
  <unitConvert
    v-if="unitConvertVisible"
    @closeDialog="closeUnitConvert"
    :priceInfo="postData"
    :isDiffDeType="true"
  />
</template>

<script setup>
import {
  defineAsyncComponent,
  ref,
  watch,
  onMounted,
  reactive,
  toRaw,
} from 'vue';
import feePro from '@/api/feePro';
import loadPrice from '@/api/loadPrice';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber, getUrl } from '@/utils/index';
import { message } from 'ant-design-vue';
import { CarryOutOutlined, SmileTwoTone } from '@ant-design/icons-vue';
import * as aes from '@/utils/aes/public.js';
import { nextTick } from 'process';
const unitConvert = defineAsyncComponent(() =>
  import('@/components/unitConvert.vue')
);
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const emits = defineEmits(['getUpList', 'upDateMarketPrice']);
const store = projectDetailStore();
let tableData = ref([]);
let activeKey = ref(0); //0-信息价   1-市场价   2-推荐价
let selectTableType = ref('1'); //1-显示本期价格   2-显示平均价格
let treeData = ref([]); //左侧树数据
let expandedKeys = ref(['0-0', '0-0-0']);
let unitConvertVisible = ref(false);
let selectedKeys = ref(['0-0']);
let isContract = ref(false); // 是否收缩
let selectInfo = reactive({
  city: '1',
  month: '1',
  cityList: [{ name: '西安', id: '1' }],
  monthList: [{ name: '2023-3', id: '1' }],
});
onMounted(async () => {
  console.log(props.showInfo, 'onMounted');
  searchName.value = props.showInfo?.materialName;
  await getCityList();
  await getMonthAndCity();
  // await getSomeData(false, true);
  await getTreeData();
  if (store.currentTreeInfo.levelType === 1) {
    changeGrid(searchName.value ? true : false);
  }
});
const activeOptions = reactive([
  {
    key: 0,
    tab: '信息价',
  },
  //暂时隐藏
  {
    key: 1,
    tab: '市场价',
  },
  {
    key: 2,
    tab: '推荐价',
  },
]);
const typeOptions = reactive([
  {
    key: '1',
    tab: '显示本期价格',
  },
  {
    key: '2',
    tab: '显示平均价格',
  },
]);
const props = defineProps(['showInfo']);
let isOnline = ref(true);
watch(
  () => props.showInfo,
  () => {
    if (props.showInfo?.materialName) {
      searchName.value = props.showInfo?.materialName;
      getSomeData(true, true);
    } else {
      searchName.value = null;
      getSomeData(true, false);
    }
  }
);
watch(
  () => activeKey.value,
  () => {
    if (store.tabSelectName === '人材机汇总') {
      //侧边栏数据变化重新更新
      expandedKeys.value = ['0-0', '0-0-0'];
      changeGrid(searchName.value ? true : false);
    }
  }
);
let cityAllList = reactive([]);
const getCityList = async () => {
  await loadPrice.getDimRegion({}).then(res => {
    if (res.status === 200) {
      let list = JSON.parse(aes.decrypt(res.result));
      list.map(a => {
        a.label = a.name;
        a.value = a.name;
        a?.children?.map(b => {
          b.label = b.name;
          b.value = b.name;
        });
      });
      //王浩和产品确认过-过滤河北省（河北省无日期数据）批量载价也是
      cityAllList = list.filter(a => a.code !== '130000');
      console.log('cityAllList', cityAllList);
    }
  });
};
const getSomeData = async (isChange = false, isSearch = false) => {
  //isChange = false   true时是切换日期的地区不需要重新获取列表
  //isSearch   true---首次加载默认查询选中数据
  nextTick(async () => {
    searchName.value = props.showInfo?.materialName;
    let isOnlineFlag = await feePro.isOnline();
    isOnline.value = isOnlineFlag.result ? true : false;
    if (isOnline.value) {
      //有网的话查询列表数据
      if (!isChange) await getTreeData();
      await changeGrid(isSearch);
    }
  });
};
let searchName = ref(null);
const onSearch = async () => {
  if (!searchName.value) return;
  let isOnlineFlag = await feePro.isOnline();
  if (isOnlineFlag.result) {
    getGridData(true);
  } else {
    gridOptions.data = [];
  }
  console.log('searchName', searchName.value);
};
const getMonthAndCity = async () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单项ID
  }
  await loadPrice.queryLoadPriceAreaDate(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('queryLoadPriceAreaDate', res.result);
      for (let citys in res.result) {
        if (citys === '信息价') {
          let cityAll = Object.keys(res.result[citys]);
          regionList.value[0].cityList = cityAllList;
          regionList.value[0].selectValue.cityVal = [
            regionList.value[0].cityList[0].value,
            regionList.value[0].cityList[0].children[0].value,
          ];
        }
        setSelectList(citys, res.result);
      }
    }
  });
};
let regionList = ref([
  {
    title: '信息价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: [],
      dateVal: null,
    },
  },
  {
    title: '市场价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: null,
      dateVal: null,
    },
  },
  {
    title: '推荐价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: null,
      dateVal: null,
    },
  },
]);
const setSelectList = (item, total) => {
  regionList.value.map(i => {
    if (i.title === item) {
      if (i.title !== '信息价') {
        i.cityList = Object.keys(total[item]);
      }
      i.dateList = total[item];
      if (i.title !== '信息价') {
        i.selectValue.cityVal = i.cityList[0];
        i.showDateList = i.dateList[i.selectValue.cityVal];
      } else {
        i.showDateList =
          i.dateList[i.selectValue.cityVal[i.selectValue.cityVal.length - 1]] ||
          [];
      }
      i.selectValue.dateVal = (i.showDateList && i.showDateList[0]) || null;
    }
  });
  console.log('regionList.value', regionList.value);
};
const handleChange = async (type, target) => {
  if (type === 'city') {
    if (activeKey.value === 0) {
      // debugger;
      console.log('target', target);
      console.log(regionList.value[0]);
      regionList.value[0].showDateList =
        regionList.value[0].dateList[target[target.length - 1]];
      regionList.value[0].selectValue.dateVal =
        regionList.value[0].showDateList && regionList.value[0].showDateList[0];
    } else {
      regionList.value[activeKey.value].showDateList =
        regionList.value[activeKey.value].dateList[target];
      regionList.value[activeKey.value].selectValue.dateVal =
        regionList.value[activeKey.value].showDateList[0];
    }
  }
  changeGrid(searchName.value ? true : false);
  console.log('regionList.value', regionList.value);
};
let tableLoading = ref(false);
const gridOptions = reactive({
  headerAlign: 'center',
  showOverflow: true,
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    height: 30,
  },
  columns: [],
  data: [],
  align: 'center',
});
let postData = ref(null);
const closeUnitConvert = val => {
  unitConvertVisible.value = false;
  if (val) {
    postData.value = {
      ...val,
      loadPrice: {
        ...val.loadPrice,
        marketPrice: val.marketPriceAfter,
        loadPriceFinally: val.marketPriceAfter,
      },
    };
    zjFun(postData.value.loadPrice);
  }
  console.log(postData.value);
};
const startZJ = ({ row }) => {
  if (props.showInfo?.noLoadPrice === 1) {
    message.warning('该行数据不支持载价，请重新选择');
    return;
  }
  console.log(row, props.showInfo, 'startZJ');
  if (row.unit !== props.showInfo.unit) {
    postData.value = {
      loadPrice: { ...row },
      beforeUnit: row.unit,
      nowUnit: props.showInfo.unit,
      marketPrice:
        activeKey.value === 0 &&
        Number(store.deStandardReleaseYear) === 22 &&
        Number(store?.taxMade) === 1
          ? row.notIncludingTaxMarketPrice
          : row.marketPrice,
      marketPriceAfter: '',
      sourcePrice: row.sourcePrice,
    };
    // debugger;
    unitConvertVisible.value = true;
    return;
  } else {
    row.loadPriceFinally =
      activeKey.value === 0 &&
      Number(store.deStandardReleaseYear) === 22 &&
      Number(store?.taxMade) === 1
        ? row.notIncludingTaxMarketPrice
        : row.marketPrice;
    zjFun(row);
  }
};
const zjFun = row => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    rcj: toRaw(props.showInfo),
    searchRcj: toRaw(row),
    type: store.currentTreeInfo.levelType === 1 ? 1 : 2,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单项ID
    apiData.searchRcj.loadPrice = row.loadPriceFinally;
  }
  console.log('startZJ---useZtzjRcj', apiData);
  loadPrice.useZtzjRcj(apiData).then(res => {
    if (res.status === 200) {
      console.log('startZJ---接收结果useZtzjRcj', res);
      if (store.currentTreeInfo.levelType === 3) {
        emits('getUpList');
      } else {
        // if (activeKey.value === 0 && Number(store?.taxMade) === 1) {
        //   row.marketPrice = row.notIncludingTaxMarketPrice;
        // }
        row.marketPrice = row.loadPriceFinally;
        emits('upDateMarketPrice', row);
      }
    }
  });
};
const gridEvents = ref({
  cellDblclick: startZJ,
});
const changeGrid = async isSearch => {
  switch (selectTableType.value) {
    case '1':
      gridOptions.columns = [
        { type: 'seq', title: '序号', width: 70 },
        { field: 'materialName', title: '材料名称', width: 200 },
        { field: 'specification', title: '规格型号', width: 120 },
        { field: 'unit', title: '单位', width: 70 },
        {
          field: 'notIncludingTaxMarketPrice',
          title: '不含税市场价',
          width: 120,
          visible: activeKey.value === 0,
        },
        {
          field: 'marketPrice',
          title:
            activeKey.value === 0
              ? '含税市场价'
              : activeKey.value === 1
              ? '工程价'
              : '推荐价',
          width: 120,
        },
        // { field: 'rate', title: '历史价', width: 100 },
        { field: 'priceDate', title: '报价时间', width: 120 },
      ];
      break;
    case '2':
      gridOptions.columns = [
        { field: 'sort', title: '序号', width: 50 },
        { field: 'code', title: '材料名称', width: 200 },
        { field: 'name', title: '规格型号', width: 120 },
        { field: 'unit', title: '单位', width: 70 },
        { field: 'price', title: '含税市场价', width: 70 },
        { field: 'rate', title: '历史价', width: 50 },
        { field: 'type', title: '报价时间', width: 100 },
      ];
      break;
  }
  getGridData(isSearch);
};
let hisSelect = ref();
const selectChildren = (selectedKeys, { node, event }) => {
  selectedKeys.value = [];
  if (selectedKeys.length == 0) {
    selectedKeys.value = [node.key];
    return;
  } else {
    hisSelect.value = node.dataRef.name;
    selectedKeys.value = selectedKeys;
  }
  getGridData();
};
const getGridData = (isSearch = false) => {
  let apiData = {};
  apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    type: activeKey.value + 1,
    areaName:
      activeKey.value === 0
        ? regionList.value[activeKey.value].selectValue.cityVal[
            regionList.value[activeKey.value].selectValue.cityVal.length - 1
          ]
        : regionList.value[activeKey.value].selectValue.cityVal,
    yearMonths: regionList.value[activeKey.value].selectValue.dateVal,
  };
  tableLoading.value = true;
  if (isSearch) {
    apiData.materialName = searchName.value;
  } else {
    console.log('selectedKeys', selectedKeys.value);
    if (selectedKeys.value.length > 0) {
      let str = selectedKeys.value[0].slice(2);
      let arr = str.split('-');
      let obj = [...treeData.value];
      let list = [];
      arr.map(a => {
        list.push(obj[a].name);
        obj = obj[a].children;
      });
      list.map((i, idx) => {
        apiData[`classlevel0${idx + 1}`] = i;
      });
    }
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  loadPrice
    .getZtzjRcj(apiData)
    .then(res => {
      console.log('getZtzjRcj---res', apiData, res);
      if (res.status === 200 && res.result) {
        gridOptions.data = JSON.parse(res.result);
      } else {
        gridOptions.data = [];
      }
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
const getTreeData = async () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    type: activeKey.value + 1,
    areaName:
      activeKey.value === 0
        ? regionList.value[activeKey.value].selectValue.cityVal[
            regionList.value[activeKey.value].selectValue.cityVal.length - 1
          ]
        : regionList.value[activeKey.value].selectValue.cityVal,
    yearMonths: regionList.value[activeKey.value].selectValue.dateVal,
    materialName: searchName.value,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeInfo?.parentId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  await loadPrice.getRcjTypeTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('getRcjTypeTree', res.result, apiData);
      for (let citys in res.result) {
        // setSelectList(citys, res.result);
        treeData.value = res.result;
      }
    }
  });
};
const contractHandle = () => {
  isContract.value = !isContract.value;
};
</script>
<style lang="scss" scoped>
.table-content {
  height: 100%;
  .title {
    width: 100%;
    background-color: #e7e7e7;
    height: 35px;
    text-align: left;
    margin: 2px 0 0px;
    .text {
      display: inline-block;
      width: 100px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: #f8fbff;
      border-top: 2px solid #4786ff;
    }
  }
  .selectTab {
    background-color: #e7e7e7;
    height: 32px;
    line-height: 30px;
    // padding-left: 20px;
    position: relative;
    border-bottom: 2px solid #e7e7e7;
    margin: 3px 0;
    .label {
      color: grey;
      font-size: 12px;
    }
    .showTitle {
      position: absolute;
      right: 0px;
      top: 0px;
      line-height: 30px;
      height: 30px;
      padding: 0 20px;
      font-size: 12px;
      // background-color: #e7e7e7;
      border-radius: 5px;
    }
    .ant-radio-button-wrapper {
      font-size: 12px;
      background-color: #e7e7e7;
      border: none;
      box-shadow: none;
      // border-radius: 5px;
    }
    .ant-radio-button-wrapper-checked {
      // border-color: none;
      background-color: white;
      border: none;
      border-top: 2px solid #4786ff;
      color: black;
      &:hover {
        color: black;
      }
    }
  }
  .searchTab {
    height: 30px;
    line-height: 30px;
    // padding-left: 20px;
    display: flex;
    border-bottom: 2px solid #e7e7e7;
    margin-bottom: 0px;
    .label {
      color: grey;
      font-size: 12px;
      margin: 0 10px;
    }
    .ant-select,
    .ant-input,
    .ant-input-group {
      height: 25px !important;
      font-size: 12px !important;
      color: gray;
      margin: auto 0;
    }
    :deep(.ant-input-group) {
      font-size: 12px !important;
      margin: auto 0;
    }
  }
  .content {
    width: 100%;
    position: relative;
    height: calc(100% - 70px);
    display: flex;
    // justify-content: space-between;
    &-leftTree {
      width: 25%;
      border-right: 2px solid #e7e7e7;
      &-title {
        height: 30px;
        background-color: #e7e7e7;
        font-size: 12px;
        font-weight: 700;
        line-height: 30px;
        padding-left: 10px;
      }
    }
    &-leftTitle {
      width: 30px;
      height: 100%;
      margin: 0;
      background-color: #e7e7e7;
      position: relative;
      &-title {
        position: absolute;
        top: 50%;
        left: 50%;
        font-size: 12px;
        font-weight: 700;
        transform: translate(-85%, -50%);
        width: 10px;
        padding: 0;
        height: auto;
        border-bottom: none;
        white-space: normal;
      }
    }
    &-rightTable {
      width: 75%;
      padding-left: 1px;
      &-select {
        height: 25px;
        margin-bottom: 0;
        .ant-radio-button-wrapper {
          height: 25px;
          line-height: 25px;
          font-size: 12px;
        }
        .ant-radio-button-wrapper-checked {
          background-color: #f8fbff;
          border-color: #e7e7e7;
        }
      }
    }
    &:hover .btnExpand .btn {
      display: block;
    }
    .btnExpand {
      position: absolute;
      // left: 24%;
      transition: transform 0.2s;
      top: calc(50% + 30px);
      transform: translateY(-50%);
      width: 14px;
      font-size: 12px;
      height: 80px;
      z-index: 100;
      text-align: center;
      transition: all 0.1s linear;
      cursor: pointer;
      user-select: none;
      .btn {
        img {
          width: 10px;
        }
        display: none;
        &:hover {
          display: block;
        }
      }
      span {
        display: inline-block;
        transform: translateX(-1px);
        transition: all 0.4s;
      }
    }
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
}
:deep(.ant-tree) {
  height: calc(100% - 40px);
  overflow-y: scroll;
  .ant-tree-node-content-wrapper {
    padding: 0 0px;
  }
  .color_light {
    height: 24px;
    padding: 0;
    display: inline-block;
    background-color: #bae7ff;
  }
}
:deep(.noAllow .ant-input-search-button) {
  pointer-events: none;
  cursor: not-allowed;
  background-color: #e7e7e7;
}
</style>
