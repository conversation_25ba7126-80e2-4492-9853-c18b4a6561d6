/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-06-14 19:33:42
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-09 19:29:33
 */
import { markRaw, defineAsyncComponent } from 'vue';
import { useRoute } from 'vue-router';
const ysshPrefix = '../../../yuSuanShenHe/views/projectDetail/customize';
const jieSuanPrefix = '../../../jieSuan/views/projectDetail/customize';
export const getComponents = (type = 'ys') => {
  const paths = {
    ys: [
      { name: 'basicInfo', path: './ProjectOverview/BasicInfo.vue' },
      {
        name: 'engineerFeature',
        path: './ProjectOverview/engineerFeature.vue',
      },
      { name: 'subItemProject', path: './subItemProject/index.vue' },
      { name: 'measuresItem', path: './measuresItem/index.vue' },
      { name: 'qtxmStatistics', path: './OtherProject/qtxmStatistics.vue' },
      { name: 'qtxmZlje', path: './OtherProject/zlje.vue' },
      { name: 'qtxmZygczgj', path: './OtherProject/zygczgj.vue' },
      { name: 'qtxmZcbfwf', path: './OtherProject/zcbfwf.vue' },
      { name: 'qtxmJrg', path: './OtherProject/jrg.vue' },
      { name: 'qtxmQzysp', path: './OtherProject/qzysp.vue' },
      { name: 'feeWithDrawalTable', path: './FeeWithDrawalTable/index.vue' },
      {
        name: 'PreparationOfInstructions',
        path: './PreparationOfInstructions/index.vue',
      },
      { name: 'CostAnalysis', path: './CostAnalysis/index.vue' },
      { name: 'summaryExpense', path: './SummaryExpense/index.vue' },
      { name: 'humanMachineSummary', path: './HumanMachineSummary/index.vue' },
    ],
    yssh: [
      {
        name: 'subItemProject',
        path: ysshPrefix + '/subItemProject/index.vue',
      },
      { name: 'measuresItem', path: ysshPrefix + '/measuresItem/index.vue' },
      {
        name: 'summaryExpense',
        path: ysshPrefix + '/summaryExpense/index.vue',
      },
      {
        name: 'qtxmStatistics',
        path: ysshPrefix + '/OtherProject/qtxmStatistics.vue',
      },
      { name: 'qtxmZlje', path: ysshPrefix + '/OtherProject/zlje.vue' },
      {
        name: 'qtxmZygczgj',
        path: ysshPrefix + '/OtherProject/zygczgj.vue',
      },
      { name: 'qtxmZcbfwf', path: ysshPrefix + '/OtherProject/zcbfwf.vue' },
      { name: 'qtxmJrg', path: ysshPrefix + '/OtherProject/jrg.vue' },
      { name: 'feeWithDrawalTable', path: './FeeWithDrawalTable/index.vue' },
      { name: 'basicInfo', path: './ProjectOverview/BasicInfo.vue' },
      {
        name: 'engineerFeature',
        path: './ProjectOverview/engineerFeature.vue',
      },
      {
        name: 'PreparationOfInstructions',
        path: './PreparationOfInstructions/index.vue',
      },
      {
        name: 'humanMachineSummary',
        path: ysshPrefix + '/HumanMachineSummary/index.vue',
      },
      { name: 'CostAnalysis', path: ysshPrefix + '/CostAnalysis/index.vue' },
    ],
    jieSuan: [
      { name: 'basicInfo', path: './ProjectOverview/BasicInfo.vue' },
      {
        name: 'engineerFeature',
        path: './ProjectOverview/engineerFeature.vue',
      },
      {
        name: 'subItemProject',
        path: jieSuanPrefix + '/subItemProject/index.vue',
      },
      { name: 'measuresItem', path: jieSuanPrefix + '/measuresItem/index.vue' },
      {
        name: 'qtxmStatistics',
        path: jieSuanPrefix + '/OtherProject/qtxmStatistics.vue',
      },
      { name: 'qtxmZlje', path: jieSuanPrefix + '/OtherProject/zlje.vue' },
      {
        name: 'qtxmZygczgj',
        path: jieSuanPrefix + '/OtherProject/zygczgj.vue',
      },
      { name: 'qtxmZcbfwf', path: jieSuanPrefix + '/OtherProject/zcbfwf.vue' },
      { name: 'qtxmJrg', path: jieSuanPrefix + '/OtherProject/jrg.vue' },
      {
        name: 'PreparationOfInstructions',
        path: './PreparationOfInstructions/index.vue',
      },
      { name: 'CostAnalysis', path: jieSuanPrefix + '/CostAnalysis/index.vue' },
      {
        name: 'summaryExpense',
        path: jieSuanPrefix + '/SummaryExpense/index.vue',
      },
      {
        name: 'humanMachineSummary',
        path: jieSuanPrefix + '/HumanMachineSummary/index.vue',
      },
    ],
  };
  let components = markRaw(new Map());
  // if (type === 'ys' || type === 'jieSuan') {
  //   components.set(
  //     'basicInfo',
  //     defineAsyncComponent(() => import('./ProjectOverview/BasicInfo.vue'))
  //   );
  //   components.set(
  //     'engineerFeature',
  //     defineAsyncComponent(() =>
  //       import('./ProjectOverview/engineerFeature.vue')
  //     )
  //   );
  //   components.set(
  //     'subItemProject',
  //     defineAsyncComponent(() => import('./subItemProject/index.vue'))
  //   );
  //   components.set(
  //     'measuresItem',
  //     defineAsyncComponent(() => import('./measuresItem/index.vue'))
  //   );
  //   components.set(
  //     'qtxmStatistics',
  //     defineAsyncComponent(() => import('./OtherProject/qtxmStatistics.vue'))
  //   );
  //   components.set(
  //     'qtxmZlje',
  //     defineAsyncComponent(() => import('./OtherProject/zlje.vue'))
  //   );
  //   components.set(
  //     'qtxmZygczgj',
  //     defineAsyncComponent(() => import('./OtherProject/zygczgj.vue'))
  //   );
  //   components.set(
  //     'qtxmZcbfwf',
  //     defineAsyncComponent(() => import('./OtherProject/zcbfwf.vue'))
  //   );
  //   components.set(
  //     'qtxmJrg',
  //     defineAsyncComponent(() => import('./OtherProject/jrg.vue'))
  //   );
  //   components.set(
  //     'qtxmQzysp',
  //     defineAsyncComponent(() => import('./OtherProject/qzysp.vue'))
  //   );
  //   components.set(
  //     'feeWithDrawalTable',
  //     defineAsyncComponent(() => import('./FeeWithDrawalTable/index.vue'))
  //   );
  //   components.set(
  //     'PreparationOfInstructions',
  //     defineAsyncComponent(() =>
  //       import('./PreparationOfInstructions/index.vue')
  //     )
  //   );
  //   components.set(
  //     'CostAnalysis',
  //     defineAsyncComponent(() => import('./CostAnalysis/index.vue'))
  //   );
  //   components.set(
  //     'summaryExpense',
  //     defineAsyncComponent(() => import('./SummaryExpense/index.vue'))
  //   );
  //   components.set(
  //     'humanMachineSummary',
  //     defineAsyncComponent(() => import('./HumanMachineSummary/index.vue'))
  //   );
  // }
  // if (type === 'yssh') {
  //   components.set(
  //     'basicInfo',
  //     defineAsyncComponent(() => import('./ProjectOverview/BasicInfo.vue'))
  //   );
  //   components.set(
  //     'engineerFeature',
  //     defineAsyncComponent(() =>
  //       import('./ProjectOverview/engineerFeature.vue')
  //     )
  //   );
  //   components.set(
  //     'PreparationOfInstructions',
  //     defineAsyncComponent(() =>
  //       import('./PreparationOfInstructions/index.vue')
  //     )
  //   );
  //   components.set(
  //     'subItemProject',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/subItemProject/index.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'measuresItem',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/measuresItem/index.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'summaryExpense',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/summaryExpense/index.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'qtxmStatistics',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/OtherProject/qtxmStatistics.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'qtxmZlje',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/OtherProject/zlje.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'qtxmZygczgj',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/OtherProject/zygczgj.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'qtxmZcbfwf',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/OtherProject/zcbfwf.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'qtxmJrg',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/OtherProject/jrg.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'feeWithDrawalTable',
  //     defineAsyncComponent(() => import('./FeeWithDrawalTable/index.vue'))
  //   );
  //   components.set(
  //     'humanMachineSummary',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/HumanMachineSummary/index.vue'
  //       )
  //     )
  //   );
  //   components.set(
  //     'CostAnalysis',
  //     defineAsyncComponent(() =>
  //       import(
  //         '../../../yuSuanShenHe/views/projectDetail/customize/CostAnalysis/index.vue'
  //       )
  //     )
  //   );
  // }
  const ysModules = import.meta.glob('../customize/*/*.vue');
  const ysshModules = import.meta.glob(
    '../../../yuSuanShenHe/views/projectDetail/customize/*/*.vue'
  );
  const jieSuanModules = import.meta.glob(
    '../../../jieSuan/views/projectDetail/customize/*/*.vue'
  );
  const allModules = Object.assign(ysModules, ysshModules, jieSuanModules);
  for (let item of paths[type]) {
    console.log(item.path);
    components.set(item.name, defineAsyncComponent(allModules[item.path]));
  }
  return components;
};
