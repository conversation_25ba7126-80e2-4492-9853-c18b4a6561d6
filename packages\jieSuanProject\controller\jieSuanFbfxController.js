const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");


class JieSuanFbfxController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    unitProjectService = this.service.jieSuanProject.unitProjectService;


    /**
     * 所选定额的清单是否为锁定综合单价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async qdIFLockPriceByDeIdController(args) {
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.qdIFLockPriceByDeId(args);
        return ResponseData.success(result);
    }


    /**
     * 修改结算相关的分部分项数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateJieSuanFbfxDataColl(args) {
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.updateJieSuanFbfxData(args);
        return ResponseData.success(result);
    }


    /**
     * 工程量批量乘以系数
     * @param constructId
     * @param singleId
     * @param unitId 单位id
     * @param coefficientValue 系数值
     * @param qdList 选中清单数据集合
     * @returns {Promise<void>}
     */
    async batchCoefficientAdjustment(args) {
        let {constructId, singleId, unitId, coefficientValue, qdList} = args;
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.batchMathCoefficientAdjustment(constructId, singleId, unitId, coefficientValue, qdList);
        //费用代码记取   费用计算
        await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        return ResponseData.success(true);
    }


    /**
     * 量差范围设置
     * @param  constructId
     * @param  singleId
     * @param  unitId
     * @param  max  最大值
     * @param  min  最小值
     * @param  type  类型   1 工程项目   2 单位工程
     * @return
     */
    async quantityDifferenceRange(args) {
        let {constructId, singleId, unitId, max, min, type} = args;
        //工程项目
        if (type == 1) {
            await this.service.jieSuanProject.jieSuanItemBillProjectService.updateAllUnitQuantityDifferenceRange(constructId, max, min);
        } else {
            //单位工程
            await this.service.jieSuanProject.jieSuanItemBillProjectService.updateUnitQuantityDifferenceRange(constructId, singleId, unitId, max, min);
        }
        return ResponseData.success(true);

    }


    /**
     * 分部分项层级展示查询
     * @return
     */
    async queryHierarchyFb(args) {
        //  入参返参跟分部分项查询一致
        //  hierachy显示层级: all：展示所有层级；1-n:展示n级子部门，qd：展示到清单；de：展示到定额；zc：展示到主材、设备（产品沟通暂时不做）;none不处理层级展开逻辑（默认打开和展开层级时传入）
        let {constructId, singleId, unitId, pageNum, pageSize, sequenceNbr, isAllFlag, hierachy} = args;
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.showFbfxCjJg(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag, hierachy);
        return ResponseData.success(result);
    }


    /**
     * 处理新增数据的合同数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateNewAddHtDataColl(args) {
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.updateNewAddHtData(args);
        //费用代码记取
        await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        return ResponseData.success(result);
    }


    /**
     * 处理粘贴数据的合同数据  复制后调用
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updatePasteLineHtDataColl(args) {
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.updatePasteLineHtData(args);
        return ResponseData.success(result);
    }


    /**
     * 处理粘贴数据  粘贴前调用
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updatePasteDataColl(args) {
        const result = await this.service.jieSuanProject.jieSuanItemBillProjectService.updatePasteData(args);
        return ResponseData.success(result);
    }


    /**
     * 上传依据文件
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async uploadAccordingFile(args) {
        return ResponseData.success(await this.service.jieSuanProject.jieSuanAccordingFileService.uploadAccordingFile(args));
    }

    /**
     * 删除依据文件
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteAccordingFile(args) {
        return ResponseData.success(await this.service.jieSuanProject.jieSuanAccordingFileService.deleteAccordingFile(args));
    }

    /**
     * 打开依据文件
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async openAccordingFile(args) {
        await this.service.jieSuanProject.jieSuanAccordingFileService.openAccordingFile(args);
        return ResponseData.success(true);
    }

    /**
     * 费用代码   费用记取计算接口
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async countFeeCodeAndMathFeeColl(args) {
        let {constructId, singleId, unitId} = args;
        //费用代码记取   费用计算
        await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        return ResponseData.success(true);
    }


    /**
     * 费用代码   费用记取计算接口
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async countFeeCodeAndMathFeeColl(args) {
        let {constructId, singleId, unitId} = args;
        //费用代码记取   费用计算
        await this.service.jieSuanProject.jieSuanProjectService.countFeeCodeAndMathFee(args);
        return ResponseData.success(true);
    }

}

JieSuanFbfxController.toString = () => '[class JieSuanFbfxController]';
module.exports = JieSuanFbfxController;

