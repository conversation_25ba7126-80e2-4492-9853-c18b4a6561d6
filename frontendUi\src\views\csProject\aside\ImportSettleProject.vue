<!--
 * @Descripttion: 新建结算项目
 * @Author: renmingming
 * @Date: 2023-05-22 15:36:24
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-24 15:37:04
-->

<template>
  <div>
    <common-modal
      className="dialog-comm"
      title="新建结算项目"
      width="auto"
      v-model:modelValue="props.visible"
      :title="dialogTitle"
      @cancel="settleCancel"
      @close="settleCancel"
    >
      <div class="file-wrap">
        <div
          class="file-upload"
          :class="{ disabled: loading || spinning }"
        >
          <!-- <img src="~@/assets/img/filetips.png" alt="" class="file-img" /> -->
          <div
            class="ft-tips"
            v-if="ysfProjectPath"
          >
            <div
              class="ft-tips-inner"
              style="border-right: 1px solid #D4D4D4;"
            >
              <div style="margin-bottom: 10px;"><img
                  style="width:40px;"
                  :src="getUrl('newJsPath.png')"
                  alt=""
                /></div>
              <div>{{ysfProjectPath}}</div>
            </div>
            <div
              class="ft-tips-inner"
              style="position: relative;top:10px;"
            >
              <div style="margin-bottom: 15px;">您可以选择&nbsp;&nbsp;<span
                  style="color:#DE3F3F;font-weight: bold;cursor: pointer;"
                  @click="upFile"
                >重新上传文件</span></div>
              <div>支持文件类型：仅支持上传YSF文件</div>
            </div>
          </div>
          <div
            class="ft-tips"
            v-else
          >
            <span>文件类型：仅支持上传YSF文件</span>
            <div
              class="upload-btn"
              @click="upFile"
            >
              <i class="vxe-icon-folder-open"></i>
              {{ loading ? '上传中' : '点击上传' }}
              <i
                class="vxe-icon-spinner roll"
                v-if="loading"
              ></i>
            </div>
          </div>
        </div>
        <!-- <div class="filePathBox">
          <span>文件路径：</span>
          <a-input
            v-model:value="ysfProjectPath"
            placeholder="请选择云算房文件"
            disabled
          />
        </div> -->
        <div class="fileTitleBox">
          <!-- <span>最近使用的项目</span> -->
          <!-- <span class="fileTitleText" @click="getAllProjectList">查看所有</span> -->
        </div>
        <div class="center">
          <!-- <a-empty
            v-if="projectContent.recentProList?.length === 0"
            :imageStyle="{ width: '271px', height: '100%', margin: '0 auto' }"
            :image="getUrl('newCsProject/none.png')"
            description=""
          />
          <p
            v-show="projectContent.recentProList?.length > 0"
            v-for="item in projectContent.recentProList"
            :key="item.id"
            @click="runProDetail(item)"
            @dblclick="onSubmit"
          >
            <span class="text"><img
                :src="workUrl"
                alt=""
              />{{ item.constructName }}.YSF</span>
            <span class="time">{{ item.openTime }}</span>
          </p> -->
        </div>
        <div class="footer-btn-list">
          <a-button
            type="primary"
            :loading="spinning || loading"
            @click="onSubmit"
          >新建</a-button>
        </div>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { getUrl } from '@/utils/index';
import { proModelStore } from '@/store/proModel';
import feePro from '@/api/jiesuanApi';
import {
  getCurrentInstance,
  ref,
  reactive,
  onMounted,
  toRaw,
  watch,
} from 'vue';
import { ipcApiRoute } from '@/api/main';

const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const form = ref();
const ysfProjectPath = ref('');
const loading = ref(false);
const spinning = ref(false);
const store = proModelStore();
const emit = defineEmits(['update:visible', 'onSuccess']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const inputData = reactive({
  filePath: null,
});
const fileInfo = reactive({
  name: '',
  type: '',
});
watch(
  () => props.visible,
  (val, oldVal) => {
    if (val) {
      reset();
      getRecentlyProgectList();
    }
  }
);
// 重置数据
const reset = () => {
  loading.value = false;
  ysfProjectPath.value = '';
  for (let key in inputData) {
    if (!['qdStandardId', 'deStandardId'].includes(key)) {
      inputData[key] = null;
    }
  }
  fileInfo.type = null;
};

const projectContent = reactive({
  recentProList: [],
});
const workUrl = getUrl('document.png');

const upFile = () => {
  feePro.openFileSelection().then(res => {
    console.log('打开本地项目===', res);
    let path = res.split('\\');
    ysfProjectPath.value = path[path.length - 1];
    inputData.filePath = res;
  });
};
// 获取最近项目列表
const getRecentlyProgectList = () => {
  $ipc.invoke(ipcApiRoute.recentlyOpenedProjectList).then(response => {
    console.log('结算最近打开项目列表', response);
    if (response.status !== 200) {
      return false;
    }
    let datas = response.result;
    let recentProList = [];
    for (let i in datas) {
      if (datas[i].path) {
        let path = datas[i].path;
        if (path.split('.')[1] === 'ysf') {
          recentProList.push(datas[i]);
        }
      }
    }
    recentProList.slice(0, 5);
    projectContent.recentProList = recentProList;
    store.SET_Refresh(false);
  });
};
// 点击最近使用文件
const runProDetail = item => {
  ysfProjectPath.value = item.constructName + '.ysf';
  inputData.filePath = item.path;
};
// 点击新建项目
const onSubmit = () => {
  if (spinning.value || loading.value) {
    message.error('正在发送中...');
    return;
  }
  if (!inputData.filePath) {
    message.error('请选择文件！');
    return;
  }
  spinning.value = true;
  console.log('参数：', toRaw(inputData));
  feePro
    .creatJieSuanProJect({
      ...toRaw(inputData),
    })
    .then(response => {
      console.log(response);
      if (response.status === 200) {
        settleCancel();
        emit(
          'onSuccess',
          {
            list: response.result,
            importUrl: inputData.filePath,
            sequenceNbr: response.result?.sequenceNbr,
          },
          'newJS'
        );
      } else {
        message.error(response.message);
      }
    })
    .finally(() => {
      spinning.value = false;
    });
};
const settleCancel = () => {
  emit('update:visible');
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.file-wrap {
  width: 50vw;
  min-width: 500px;
  max-width: 600px;

  .ft-tips {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .ft-tips-inner {
    text-align: center;
    width: 50%;
  }
}

.file-upload {
  background: #fff6f6;
  border: 1px dashed rgba(151, 151, 151, 0.37);
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:hover {
    background: rgba(251, 228, 228, 0.5);
  }

  .upload-btn {
    display: flex;
    align-items: center;
    padding: 6px 18px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    background: #dc3838;
    border-radius: 22px;
    margin-left: 6px;
    cursor: pointer;
    span {
      margin: 0 4px;
    }
  }
}

.fileTitleBox {
  display: flex;
  font-size: 14px;
  justify-content: space-between;
  padding: 10px 0 8px;
  border-bottom: 1px solid #e0e0e0;

  .fileTitleText {
    color: #187ec0;
    cursor: pointer;
  }
}

.disabled {
  cursor: no-drop;
  opacity: 0.4;
}

.form-wrap {
  margin-top: 44px;
}

.file-success {
  background-color: rgb(246, 246, 246);
  border: 1px dashed rgba(151, 151, 151, 0.37);
  flex-direction: row;
  align-items: center;

  .file-box {
    width: 45%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 10px;
    justify-content: center;

    .type-img {
      width: 38px;
      margin: 0 auto 10px;
    }

    .file-name {
      font-size: 14px;
      font-weight: 400;
      display: block;
      width: 100%;
      color: #2a2a2a;
      overflow: hidden;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
    }
  }

  .ft-tips {
    padding: 0 10px;
    display: flex;
    align-items: center;
    flex-direction: column;
    border-left: 1px solid rgba(151, 151, 151, 0.37);

    .upload-file {
      font-size: 12px;
      font-weight: 400;
      color: #454545;
      margin-bottom: 20px;

      .reset-file {
        font-size: 12px;
        font-weight: 600;
        color: #dc3838;
        margin-left: 5px;
        text-decoration: dashed;
      }
    }

    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #2a2a2a;
    }
  }
}

.center {
  padding: 10px;
  padding-top: 0;
  height: calc(100% - 56px);
  overflow-x: scroll;
  overflow-y: scroll;
  // box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);
  // border-radius: 4px;

  .time {
    width: 15%;
    min-width: 140px;
    text-align: right;
    margin-left: 5px;
  }

  .text {
    width: 85%;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  p {
    cursor: pointer;
    height: 38px;
    // line-height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    color: #606266;
    font-size: 14px;
    margin-bottom: 0px;

    img {
      margin-right: 10px;
      margin-top: -2px;
    }
  }

  p:nth-child(2n) {
    background: #f5f5f5;
  }
}

.filePathBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0 0;

  span {
    font-size: 14px;
  }

  input {
    width: calc(100% - 75px);
    background: white;
    color: black;
  }
}
</style>
