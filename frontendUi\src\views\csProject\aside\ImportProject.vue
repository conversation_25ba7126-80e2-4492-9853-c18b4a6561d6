<!--
 * @Descripttion: 编辑项目结构
 * @Author: renmingming
 * @Date: 2023-05-22 15:36:24
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-13 15:00:44
-->
<template>
  <common-modal
    className="dialog-comm"
    title="导入项目"
    width="auto"
    v-model:modelValue="props.visible"
    :title="dialogTitle"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="file-wrap">
      <div
        class="file-upload"
        @click="upFile"
        v-show="!inputData.importUrl"
        :class="{ disabled: loading || spinning }"
      >
        <img
          src="~@/assets/img/filetips.png"
          alt=""
          class="file-img"
        />
        <div class="ft-tips">
          <span>支持文件类型：zip/rar、xml电子标</span>
          <div class="upload-btn">
            <i class="vxe-icon-folder-open"></i>
            {{ loading ? '上传中' : '点击上传' }}
            <i
              class="vxe-icon-spinner roll"
              v-if="loading"
            ></i>
          </div>
        </div>
      </div>

      <div
        class="file-upload file-success"
        v-show="inputData.importUrl"
        :class="{ disabled: loading || spinning }"
      >
        <div class="file-box">
          <img
            src="~@/assets/img/import/rar.png"
            alt=""
            class="type-img"
            v-if="['rar'].includes(fileInfo.type)"
          />
          <img
            src="~@/assets/img/import/xls.png"
            alt=""
            class="type-img"
            v-if="['xls'].includes(fileInfo.type)"
          />
          <img
            src="~@/assets/img/import/xml.png"
            alt=""
            class="type-img"
            v-if="['xml','hebqtbx','hebqzbx'].includes(fileInfo.type)"
          />
          <img
            src="~@/assets/img/import/zip.png"
            alt=""
            class="type-img"
            v-if="['zip'].includes(fileInfo.type)"
          />
          <a-tooltip>
            <template #title>{{ fileInfo.name }}</template>
            <span class="file-name">{{ fileInfo.name }}</span>
          </a-tooltip>
        </div>
        <div class="ft-tips">
          <div
            class="upload-file"
            @click="upFile"
          >
            您可以选择
            <span class="reset-file">
              {{ loading ? '上传中' : '重新上传文件' }}
              <i
                class="vxe-icon-spinner roll"
                v-if="loading"
              ></i>
            </span>
          </div>
          <span class="tips">支持文件类型：zip/rar、xml电子标</span>
        </div>
      </div>

      <div class="form-wrap">
        <!-- <a-spin tip="解析中..." :spinning="spinning"> -->
        <a-form
          :model="inputData"
          :label-col="colStyle.labelCol"
          :wrapper-col="colStyle.wrapperCol"
          ref="form"
        >
          <a-form-item
            label="项目名称"
            name="constructName"
            :rules="[
                {
                  required: true,
                  message: '请输入项目名称!',
                },
                {
                  max: 50,
                  message: '项目名称长度不可超过50个字!',
                },
                {
                  pattern: /^[^`\^。>？!！￥~!@$^&*\=+[\]{}\\|;:<>/?]*$/,
                  message: '项目名称不可包含特殊字符!',
                },
              ]"
          >
            <a-input
              v-model:value.trim="inputData.constructName"
              placeholder="请输入项目名称"
            />
          </a-form-item>
          <a-form-item
            label="项目编码"
            name="constructCode"
            :rules="[
                {
                  max: 50,
                  message: '项目编码长度不可超过50个字!',
                },
                {
                  pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]{1,}$/,
                  message: '项目编码不可包含特殊字符!',
                },
              ]"
          >
            <a-input
              v-model:value.trim="inputData.constructCode"
              placeholder="请输入项目编码"
            />
          </a-form-item>
          <a-form-item
            label="清单标准"
            name="qdStandardId"
            :rules="[{ required: true, message: '请选择清单标准!' }]"
          >
            <a-select
              v-model:value="inputData.qdStandardId"
              :options="lists.mainfestList"
              placeholder="请选择"
              :size="colStyle.colSize"
            ></a-select>
          </a-form-item>
          <a-form-item
            label="定额标准"
            name="deStandardId"
            :rules="[{ required: true, message: '请选择定额标准!' }]"
          >
            <a-select
              v-model:value="inputData.deStandardId"
              :options="lists.rationList"
              placeholder="请选择"
              :size="colStyle.colSize"
            ></a-select>
          </a-form-item>
          <a-form-item
            label="招/投标类型"
            name="biddingType"
            :rules="[{ required: true, message: '请选择招/投标类型!' }]"
          >
            <a-select
              v-model:value="inputData.biddingType"
              :options="lists.biddingTypeList"
              placeholder="请选择"
              :size="colStyle.colSize"
            ></a-select>
          </a-form-item>
          <a-form-item
            label="xml厂家"
            name="xmlFactory"
            v-if="['xml','hebqtbx','hebqzbx'].includes(fileInfo.type)"
            :rules="[{ required: true, message: '请选择xml厂家!' }]"
          >
            <a-select
              v-model:value="inputData.xmlFactory"
              :options="lists.xmlList"
              placeholder="请选择"
              :size="colStyle.colSize"
            ></a-select>
          </a-form-item>

          <a-form-item
            label="计税方式"
            name="taxCalculationMethod"
            v-if="fileInfo.type && !['xml','hebqtbx','hebqzbx'].includes(fileInfo.type)"
            :rules="[{ required: true, message: '请选择计税方式!' }]"
          >
            <a-select
              v-model:value="inputData.taxCalculationMethod"
              placeholder="请选择计税方式"
              :options="lists.taxMathodList"
              :fieldNames="{ label: 'value', value: 'code' }"
              @change="chengeTaxMeathod($event)"
            >
            </a-select>
          </a-form-item>

          <a-form-item
            label="税改文件"
            name="taxReformDocumentsId"
            v-if="
                inputData.taxCalculationMethod === '1' &&
                fileInfo.type &&
                !['xml','hebqtbx','hebqzbx'].includes(fileInfo.type) &&
                lists.rationList
                  ?.find(item => item.value === inputData.deStandardId)
                  ?.label.indexOf('12') !== -1
              "
            :rules="[
                {
                  required: true,
                  message: '请选择税改文件!',
                },
              ]"
          >
            <a-select
              v-model:value="inputData.taxReformDocumentsId"
              placeholder="请选择税改文件"
              :options="lists.fileList"
              :fieldNames="{ label: 'value', value: 'code' }"
            >
            </a-select>
          </a-form-item>
        </a-form>
        <!-- </a-spin> -->
      </div>

      <div class="footer-btn-list">
        <a-button
          type="primary"
          :loading="spinning || loading"
          @click="onSubmit"
        >新建</a-button>
      </div>
    </div>
  </common-modal>

  <a-upload
    class="file-uploadFile-important"
    name="file"
    :maxCount="1"
    :showUploadList="false"
    :customRequest="uploadFile"
    :before-upload="beforeUpload"
    accept=".xml,.zip, .rar,.xml,.hebqzbx,.hebqtbx,pplication/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    v-show="false"
  >
  </a-upload>
</template>

<script setup>
import csProject from '@/api/csProject';
import { message } from 'ant-design-vue';
import {
  getCurrentInstance,
  ref,
  reactive,
  watch,
  nextTick,
  onMounted,
  toRaw,
  toRefs,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ipcApiRoute } from '@/api/main';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emit = defineEmits(['update:visible', 'onSuccess']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const router = useRouter();
const colStyle = reactive({
  colSize: null,
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
});

const inputData = reactive({
  constructName: null, //项目名称
  constructCode: null, //项目编码
  qdStandardId: null, //清单标准
  deStandardId: null, //定额标准
  importUrl: null,
  biddingType: null,
  xmlFactory: null,
  TaxReformDocumentsId: null,
  taxCalculationMethod: null,
});
const lists = reactive({
  mainfestList: [], //清单标准列表
  rationList: [], //定额标准列表
  xmlList: [], //工程专业列表
  biddingTypeList: [],
  taxMathodList: [], //计税方式
  fileList: [], //税改文件
});

const fileInfo = reactive({
  name: '',
  type: '',
});
const upFile = () => {
  if (loading.value || spinning.value) return;
  document
    .querySelector('.file-uploadFile-important .ant-upload input')
    .click();
};

/**
 * @description: 文件上传
 * @param {*} params
 * @return {*}
 */
const uploadFile = async params => {
  loading.value = true;
  const { file } = params;
  const name = file.name.substring(0, file.name.lastIndexOf('.'));
  loading.value = false;
  inputData.importUrl = file.path;
  if (!inputData.constructName) {
    inputData.constructName = name;
    form.value?.clearValidate('constructName');
    form.value?.validateFields('constructName');
  }
};

// 上传前置判断
const beforeUpload = file => {
  if (file.name.length > 100) {
    message.error('上传文件名不能超过100字!');
    return false;
  }

  const fileType = file.name
    .substring(file.name.lastIndexOf('.') + 1)
    .toLowerCase();
  if (
    !['zip', 'rar', 'xls', 'xlsx', 'xml', 'hebqzbx', 'hebqtbx'].includes(
      fileType
    )
  ) {
    message.error('上传文件格式不正确!');
    return false;
  }
  fileInfo.type = ['xls', 'xlsx'].includes(fileType) ? 'xls' : fileType;
  fileInfo.name = file.name;
  return true;
};

const onSubmit = () => {
  if (spinning.value || loading.value) {
    message.error('正在发送中...');
    return;
  }
  if (!inputData.importUrl) {
    message.error('请上传文件！');
    return;
  }

  let deType = lists.rationList.find(a => a.value === inputData.deStandardId);
  // if (
  //   ['xml', 'hebqtbx', 'hebqzbx'].includes(fileInfo.type) &&
  //   inputData.xmlFactory === '招标通' &&
  //   deType.label.indexOf('22') !== -1
  // ) {
  //   message.warning('该厂家当前仅支持12定额标准的xml文件');
  //   return;
  // }
  form.value.validateFields().then(() => {
    spinning.value = true;
    const isZip = ['zip', 'rar'].includes(fileInfo.type.toLowerCase());
    const isXml = ['xml'].includes(fileInfo.type.toLowerCase());
    const apiPath = isZip
      ? ipcApiRoute.importZipCheck
      : ipcApiRoute.importProject;
    if (isXml) {
      inputData.fileExtension = 'xml';
    }
    if (['hebqtbx', 'hebqzbx'].includes(fileInfo.type)) {
      inputData.fileExtension = fileInfo.type;
    }
    console.log('参数：', toRaw(inputData));
    $ipc
      .invoke(apiPath, { ...toRaw(inputData) })
      .then(response => {
        console.log(response);
        if (response.status === 200) {
          emit('onSuccess', {
            list: response.result,
            isZip,
            importUrl: inputData.importUrl,
            sequenceNbr: response.result?.sequenceNbr,
            deStandardId: inputData.deStandardId,
          });
          let flag = lists.rationList.find(
            item => item.value === inputData.deStandardId
          );
          store.deType = flag.label.indexOf('22') !== -1 ? '22' : '12';
        } else {
          message.error(response.message);
        }
      })
      .finally(() => {
        spinning.value = false;
      });
  });
};

// input框输入值置为空
const reset = () => {
  loading.value = false;
  for (let key in inputData) {
    if (!['qdStandardId', 'deStandardId'].includes(key)) {
      inputData[key] = null;
    }
  }
  fileInfo.type = null;

  inputData.taxCalculationMethod =
    lists.taxMathodList && lists.taxMathodList[0].code;
  chengeTaxMeathod(inputData.taxCalculationMethod);
};

const gettaxMethodList = () => {
  lists.taxMathodList = [];
  csProject.getDefaultTaxCalculation().then(function (res) {
    console.log('gettaxMethodList', res);
    if (res.status === 200) {
      for (var i of res.result.taxCalculationMethodOption) {
        for (var k in i) {
          lists.taxMathodList.push({
            code: k,
            value: i[k],
          });
        }
      }
      inputData.taxCalculationMethod =
        lists.taxMathodList && lists.taxMathodList[0].code;
      for (var i of res.result.taxReformDocumentsOption) {
        for (var k in i) {
          lists.fileList.push({
            code: k,
            value: i[k],
          });
        }
      }
      chengeTaxMeathod(inputData.taxCalculationMethod);
    }
  });
};
const chengeTaxMeathod = eve => {
  if (Number(eve) === 1) {
    inputData.taxReformDocumentsId = lists.fileList && lists.fileList[0]?.code;
  } else {
    inputData.taxReformDocumentsId = null;
  }
};

//获取清单标准下拉列表
const queryMainfestList = () => {
  const postData = {
    areaId: 130000,
    type: '1',
  };
  $ipc.invoke(ipcApiRoute.listStandardDropdownList, postData).then(result => {
    if (result.status === 200) {
      result.result.map(item => {
        lists.mainfestList.push({
          value: item.sequenceNbr,
          label: item.name,
        });
      });
      nextTick(() => {
        inputData.qdStandardId = lists.mainfestList[0]?.value;
      });
    }
  });
};
//获取定额标准下拉列表
const queryRationList = () => {
  const postData = {
    areaId: 130000,
    type: '2',
  };
  $ipc
    .invoke(ipcApiRoute.quotaStandardDropdownList, postData)
    .then(function (result) {
      if (result.status === 200) {
        result.result.map(item => {
          lists.rationList.push({
            value: item.sequenceNbr,
            label: item.name,
          });
        });

        nextTick(() => {
          inputData.deStandardId = lists.rationList[0]?.value;
        });
      }
    });
};

// 获取xml厂商
const getXml = () => {
  $ipc.invoke(ipcApiRoute.xmlFactroyDropdownList).then(res => {
    const list = [];
    res.map(item => {
      list.push({ value: item, label: item });
    });
    lists.xmlList = list;
  });
};

const getbiddingTypeList = () => {
  $ipc.invoke(ipcApiRoute.biddingTypeDropdownList).then(res => {
    res.map(item => {
      lists.biddingTypeList.push({ value: item.key, label: item.value });
    });
  });
};

onMounted(() => {
  queryMainfestList();
  queryRationList();
  getXml();
  getbiddingTypeList();
  gettaxMethodList();
});

const cancel = () => {
  emit('update:visible');
};

watch(
  () => props.visible,
  (val, oldVal) => {
    if (val) {
      form.value?.resetFields();
      reset();
    }
  }
);
</script>
<style lang="scss" scoped>
.file-wrap {
  width: 50vw;
  min-width: 500px;
  max-width: 600px;
  .ft-tips {
    display: flex;
    align-items: center;
  }
}
.file-upload {
  background: #fff6f6;
  border: 1px dashed rgba(151, 151, 151, 0.37);
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  cursor: pointer;
  &:hover {
    background: rgba(251, 228, 228, 0.5);
  }

  .upload-btn {
    display: flex;
    align-items: center;
    padding: 6px 18px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    background: #dc3838;
    border-radius: 22px;
    margin-left: 6px;
    span {
      margin: 0 4px;
    }
  }
}

.disabled {
  cursor: no-drop;
  opacity: 0.4;
}

.form-wrap {
  margin-top: 44px;
}

.file-success {
  background-color: rgb(246, 246, 246);
  border: 1px dashed rgba(151, 151, 151, 0.37);
  flex-direction: row;
  align-items: center;
  .file-box {
    width: 45%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 10px;
    justify-content: center;
    .type-img {
      width: 38px;
      margin: 0 auto 10px;
    }
    .file-name {
      font-size: 14px;
      font-weight: 400;
      display: block;
      width: 100%;
      color: #2a2a2a;
      overflow: hidden;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
    }
  }

  .ft-tips {
    padding: 0 10px;
    display: flex;
    align-items: center;
    flex-direction: column;
    border-left: 1px solid rgba(151, 151, 151, 0.37);
    .upload-file {
      font-size: 12px;
      font-weight: 400;
      color: #454545;
      margin-bottom: 20px;
      .reset-file {
        font-size: 12px;
        font-weight: 600;
        color: #dc3838;
        margin-left: 5px;
        text-decoration: dashed;
      }
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #2a2a2a;
    }
  }
}
</style>
