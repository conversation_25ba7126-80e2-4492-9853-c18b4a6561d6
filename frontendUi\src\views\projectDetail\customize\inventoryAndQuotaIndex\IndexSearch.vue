<template>
  <div class="search-history" ref="searchInputRef">
    <a-input-search
      v-model:value="name"
      :maxlength="50"
      :placeholder="props.type == 'qdzy' ? '搜索' : '请输入编码或名称'"
      style="width: 95%; margin: 10px 8px 10px"
      @focus="searchHistoryFocus"
      @search="onSearch"
      @click.stop="searchHistoryFocus"
    />
    <div class="search-history-menu" v-show="searchHistoryVisible">
      <a-menu @click="searchHistoryClick">
        <a-menu-item :key="item" v-for="item of searchHistoryList">{{
          item
        }}</a-menu-item>
      </a-menu>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onBeforeUnmount, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'qdzy',
  },
});
const emits = defineEmits(['update:value', 'search']);
const name = computed({
  get: () => {
    return props.value;
  },
  set: val => {
    emits('update:value', val);
  },
});
let searchHistoryVisible = ref(false);
let searchHistoryList = ref([]);
const searchHistoryBlur = () => {
  // setTimeout(() => {
  searchHistoryVisible.value = false;
  // }, 100);
};

const searchHistoryFocus = () => {
  let searchHistory = getCurrentHistory();
  searchHistoryList.value = searchHistory;
  searchHistoryVisible.value = true;
};
const getCurrentHistory = () => {
  const constructId = route.query?.constructSequenceNbr;
  let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '{}');
  return searchHistory?.[constructId]?.[props.type] || [];
};

/**
 * 存储搜索历史，app关闭时清空搜索历史
 */
const setCurrentHistory = list => {
  const constructId = route.query?.constructSequenceNbr;
  let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '{}');
  if (!searchHistory[constructId]) searchHistory[constructId] = {};
  if (!searchHistory[constructId][props.type])
    searchHistory[constructId][props.type] = [];
  searchHistory[constructId][props.type] = list;
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
};
const searchHistoryClick = item => {
  console.log(item.key);
  emits('update:value', item.key);
  onSearch();
};
const onSearch = () => {
  setSearchHistory();
  emits('search');
};
/**
 * 设置处理搜索历史
 */
const setSearchHistory = () => {
  searchHistoryVisible.value = false;
  if (!name.value.trim()) return;
  // 保存搜索历史(15条)
  let searchHistory = getCurrentHistory();
  const index = searchHistory.findIndex(item => item === name.value);
  if (index >= 0) {
    // 如果存在则删除以前的
    searchHistory.splice(index, 1);
  }
  if (searchHistory.length >= 15) {
    // 只存15条
    searchHistory.pop();
  }
  searchHistory.unshift(name.value);
  setCurrentHistory(searchHistory);
};
let searchInputRef = ref();
const hiddenHandler = e => {
  console.log(searchInputRef.value);
  if (!searchInputRef.value?.contains(e.target)) {
    console.log('之外');
    searchHistoryVisible.value = false;
  } else {
    console.log('之内');
  }
};
onMounted(() => {
  document.addEventListener('click', hiddenHandler);
});
onBeforeUnmount(() => {
  document.removeEventListener('click', hiddenHandler);
});
</script>
<style lang="scss" scoped>
.search-history {
  position: relative;
  z-index: 2;
  .search-history-menu {
    position: absolute;
    padding: 8px 0;
    top: calc(100% - 10px);
    left: 8px;
    right: 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 3;
    background: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    ::v-deep(.ant-menu-item) {
      height: 22px;
      line-height: 22px;
      &:hover {
        background: #bae7ff;
      }
      &:not(:last-child) {
        margin-bottom: 0;
      }
    }
  }
}
</style>
