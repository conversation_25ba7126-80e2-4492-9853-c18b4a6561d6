'use strict';

const { Service } = require('../../../../core');
const ProjectDomain = require('../../domains/ProjectDomain');
const FunctionTypeConstants = require('../../constants/FunctionTypeConstants');
const { ObjectUtil } = require('../../../../common/ObjectUtil');
const gljMajorLibraryCorrelation = require('../../jsonData/gljMajorLibraryCorrelation.json');
const gljWaterElectricTemplate2022 = require('../../jsonData/gljWaterElectricTemplate2022.json');
const DeTypeConstants = require('../../constants/DeTypeConstants');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const WildcardMap = require('../../core/container/WildcardMap');
const { NumberUtil } = require('../../../../common/NumberUtil');

/**
 * 水电费记取
 */
class GljWaterElectricCostMatchService extends Service {

  constructor(ctx) {
    super(ctx);
    this.baseDeTypeList = [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.SUB_DE_TYPE_DE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_USER_RESOURCE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE];
  }

  /**
   * 获取水电费列表数据
   */
  async getWaterElectricCostData(args) {
    const { unitId, singleId, constructId } = args;
    const sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
    return ObjectUtils.isNotEmpty(sdfData) ? sdfData[unitId] : null;
  }

  /**
   * 自动计算水电费接口
   * waterElectricCostData 表示为水电费总体数据
   *
   * 该方法会返回一个计算后的水电费数据  是否更新单位的水电费数据  需要在外部进行处理
   */
  async calculateWaterElectricCost(args) {
    let { unitId, singleId, constructId, waterElectricCostData } = args;
    //获取计税方式    1：一般计税    0：简易计税
    let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    // 查看是不是有水电费的初始化数据  没有的话需要先初始化单位的水电费数据
    this.initWaterElectricCostData(args);
    // 默认使用单位缓存的水电费数据
    if (ObjectUtil.isEmpty(waterElectricCostData)) {
      // 如果waterElectricCostData参数传了   那么使用参数传递的水电费数据  主要是用于页面上的临时修改计算
      waterElectricCostData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA)[unitId];
    }
    // 获取所有的水电费基数定额
    const deArr = await this.getWaterElectricBaseDeData(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(deArr)) {
      // 没有基数定额 所有的费用都要清零
      this.clearWaterElectricCost(args);
      return waterElectricCostData;
    }
    // 把水电费基数定额根据libraryCode进行分组
    const deMap = deArr.reduce((pre, cur) => {
      if (!pre[cur.libraryCode]) {
        pre[cur.libraryCode] = [cur];
      } else {
        pre[cur.libraryCode].push(cur);
      }
      return pre;
    }, {});
    // 定额的工程专业和定额册对应关系对象
    let majorLibrary = gljMajorLibraryCorrelation.majorLibrary2022;
    // 遍历水电费的列表，计算每一条数据行的相应值
    for (const costItem of waterElectricCostData.waterElectricData) {
      if (costItem.dataFlag !== 1) {
        // 不需要计算的行
        continue;
      }
      // 筛选出本条水电费列表数据行(costItem，也就是工程专业或者章节)对应的定额
      let deArr = this.filterDeArr(majorLibrary, costItem, deMap);
      // 到此处之后   本条水电费列表数据行(costItem)对应的基数定额就完全确定了
      const deIds = deArr.map(de => de.sequenceNbr);
      // 根据定额获取到定额对应的人材机数据
      const rcjList = await this.getDeRcjList(deIds, unitId, constructId);
      // 根据计算公式和人材机数据计算得到对应的值(基数)
      const baseValue = await this.getBaseValue(costItem.calculateBase, rcjList, deArr, taxCalculationMethod, constructId);
      this.calculateByBaseValue(constructId, costItem, baseValue, waterElectricCostData.waterElectricData);
    }
    // 计算最终的水电费
    this.summaryValue(waterElectricCostData);
    return waterElectricCostData;
  }

  summaryValue(data) {
    let totalWaterCost = 0;
    let totalElectricCost = 0;
    let waterElectricCost = 0;
    // 遍历每一行   计算水电费合计
    for (const costItem of data.waterElectricData) {
      if (costItem.dataFlag !== 1) {
        // 不需要计算的行
        continue;
      }
      if (ObjectUtil.isNotEmpty(costItem.selectOptionFlag) && costItem.selectOptionFlag === 0) {
        // 未选中的行不进行计算 selectOptionFlag不为空表示是有选项操作的 值为0才表示未选中
        continue;
      }
      if (ObjectUtil.isNotEmpty(costItem.waterCost)) {
        totalWaterCost = NumberUtil.add(totalWaterCost, Number(costItem.waterCost));
      }
      if (ObjectUtil.isNotEmpty(costItem.electricCost)) {
        totalElectricCost = NumberUtil.add(totalElectricCost, Number(costItem.electricCost));
      }
      if (ObjectUtil.isNotEmpty(costItem.totalCost)) {
        waterElectricCost = NumberUtil.add(waterElectricCost, Number(costItem.totalCost));
      }
    }
    data.totalWaterCost = totalWaterCost;
    data.totalElectricCost = totalElectricCost;
    data.waterElectricCost = waterElectricCost;
  }

  calculateByBaseValue(constructId, costItem, baseValue, allData) {
    let waterCost = null;
    let electricCost = null;
    let totalCost = null;

    // 小数点精度
    let precision = this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let jqsdf = precision.COST_SUMMARY.jqsdf;
    let kcRate = precision.COST_SUMMARY.JQSD.kcRate;

    // 根据基数计算这一行的水费
    if (ObjectUtil.isNumberStr(costItem.waterRate)) {
      waterCost = NumberUtil.removeEndZero(NumberUtil.multiply(NumberUtil.numberScale(Number(baseValue), jqsdf), NumberUtil.numberScale(NumberUtil.numberScale(Number(costItem.waterRate), kcRate)), 0.01));
    }
    // 根据基数计算这一行的电费
    if (ObjectUtil.isNumberStr(costItem.electricRate)) {
      electricCost = NumberUtil.removeEndZero(NumberUtil.multiply(NumberUtil.numberScale(Number(baseValue), jqsdf), NumberUtil.numberScale(NumberUtil.numberScale(Number(costItem.electricRate), kcRate)), 0.01));
    }
    // 根据基数计算这一行的合计
    if (ObjectUtil.isNumberStr(costItem.totalRate)) {
      totalCost = NumberUtil.removeEndZero(NumberUtil.multiply(NumberUtil.numberScale(Number(baseValue), jqsdf), NumberUtil.numberScale(Number(costItem.totalRate), kcRate), 0.01));
    }
    costItem.waterCost = waterCost;
    costItem.electricCost = electricCost;
    costItem.totalCost = totalCost;
    // 当前这一行计算完成之后，需要确认这一行是不是需要汇总到父级 如果需要汇总到父级，则需要计算父级的基数
    if (costItem.dataLevel === 2) {
      const equativeLevel = allData.filter(item => item.dataLevel === 2 && item.parentId === costItem.parentId);
      if (ObjectUtil.isNotEmpty(equativeLevel)) {
        const parent = allData.find(item => item.id === costItem.parentId);
        let waterCostValue = null;
        let electricCostValue = null;
        let totalCostValue = null;
        for (const item of equativeLevel) {
          if (ObjectUtil.isNotEmpty(item.waterCost)) {
            waterCostValue = NumberUtil.add(waterCostValue, Number(item.waterCost));
          }
          if (ObjectUtil.isNotEmpty(item.electricCost)) {
            electricCostValue = NumberUtil.add(electricCostValue, Number(item.electricCost));
          }
          if (ObjectUtil.isNotEmpty(item.totalCost)) {
            totalCostValue = NumberUtil.add(totalCostValue, Number(item.totalCost));
          }
        }
        if (ObjectUtil.isNotEmpty(waterCostValue)) {
          parent.waterCost = NumberUtil.removeEndZero(waterCostValue);
        }
        if (ObjectUtil.isNotEmpty(electricCostValue)) {
          parent.electricCost = NumberUtil.removeEndZero(electricCostValue);
        }
        if (ObjectUtil.isNotEmpty(totalCostValue)) {
          parent.totalCost = NumberUtil.removeEndZero(totalCostValue);
        }
      }
    }
  }

  /**
   * 根据计算公式和人材机数据计算结果
   */
  async getBaseValue(formula, rcjData, deArr, taxCalculationMethod, constructId) {
    let value = 0;
    if (ObjectUtil.isEmpty(rcjData)) {
      return value;
    }
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    const splitArr = formula.split('+');
    for (const de of deArr) {
      for (const str of splitArr) {
        if (str == '人工费基期价' || str == '人工费定额价') {
          value = NumberUtil.add(value, NumberUtil.numberScale(de.rdTotalSum, precision.EDIT.DE.rTotalSum));
        } else if (str == '材料费基期价' || str == '材料费定额价') {
          value = NumberUtil.add(value, NumberUtil.numberScale(de.cdTotalSum, precision.EDIT.DE.cTotalSum));
        } else if (str == '机械费基期价' || str == '机械费定额价') {
          value = NumberUtil.add(value, NumberUtil.numberScale(de.jdTotalSum, precision.EDIT.DE.jTotalSum));
        } else if (str == '人工费市场价') {
          value = NumberUtil.add(value, NumberUtil.numberScale(de.rTotalSum, precision.EDIT.DE.rTotalSum));
        } else if (str == '材料费市场价') {
          value = NumberUtil.add(value, NumberUtil.numberScale(de.cTotalSum, precision.EDIT.DE.cTotalSum));
        } else if (str == '机械费市场价') {
          value = NumberUtil.add(value, NumberUtil.numberScale(de.jTotalSum, precision.EDIT.DE.jTotalSum));
        }
      }
    }
    return value;
  }

  async getDeRcjList(deIds, unitId, constructId) {
    if (ObjectUtils.isEmpty(deIds)) {
      return [];
    }
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    return rcjList.filter(item => deIds.includes(item.parentId));
  }

  /**
   * 获取水电费的基数定额
   */
  async getWaterElectricBaseDeData(constructId, singleId, unitId) {
    let deArr = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId == unitId && this.baseDeTypeList.includes(item.type));
    let csxmArr = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId == unitId && this.baseDeTypeList.includes(item.type));
    if (ObjectUtil.isNotEmpty(csxmArr)) {
      deArr = deArr.concat(csxmArr);
    }
    const libraryCode = ['2022-JZGC-DEY', '2022-ZSZX-DEY', '2022-AZGC-DEK', '2022-SZGC-DEK', '2023-YLLH-DEK', '2025-FGJZ-DEG'];
    if (ObjectUtil.isNotEmpty(deArr)) {
      deArr = deArr.filter(de => libraryCode.includes(de.libraryCode));
    }
    return deArr;
  }

  filterDeArr(majorLibrary, costItem, deMap) {
    let deArr = [];
    // 根据模板中配置项的工程专业，获取对应的定额册集合
    const majorLibraryArr = majorLibrary[costItem.projectMajor];
    // 循环定额册  找出定额册对应的定额
    for (const majorLibrary of majorLibraryArr) {
      const deArrByLibraryCode = deMap[majorLibrary];
      if (ObjectUtil.isNotEmpty(deArrByLibraryCode)) {
        deArr = deArr.concat(deArrByLibraryCode);
      }
    }
    // 到此处之后  就确定了这个工程专业下的所有定额
    // 但是市政工程和安装工程还需要根据章节(classify_level1)再次分组
    if (costItem.projectMajor === '安装工程') {
      // 安装的章节(rule)和定额的classifyLevel1完全一致  所以用===
      // 22定额的要用classifyLevel2
      if (costItem.dataLevel === 2) {
        // 如果是子级专业  需要使用classifyLevel3
        if (costItem.rule == '其它') {
          deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classlevel03) && de.classlevel03 == '3.6 其它');
        } else {
          deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classlevel03) && de.classlevel03.includes(costItem.rule));
        }
      } else {
        deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classlevel02) && de.classlevel02 === costItem.rule);
      }

    } else if (costItem.projectMajor === '市政工程') {
      // 市政的章节(rule)是简写 所以用includes
      // 22定额的要用classifyLevel2
      if (costItem.dataLevel === 2) {
        // 如果是子级专业  需要使用classifyLevel3
        deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classlevel03) && de.classlevel03.includes(costItem.rule));
      } else {
        deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classlevel02) && de.classlevel02.includes(costItem.rule));
      }
    } else if (costItem.projectMajor === '仿古建筑工程') {
      // 25仿古建筑的基数定额需要过滤掉 其他措施项目和中小型机械使用费 下的定额
      deArr = deArr.filter(de => ObjectUtil.isNotEmpty(de.classlevel02) && de.classlevel02 != '其他措施项目' && de.classlevel02 != '中小型机械使用费');
    }
    return deArr;
  }

  /**
   * 初始化水电费数据
   */
  initWaterElectricCostData(args) {
    let sdfData = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
    if (ObjectUtil.isEmpty(sdfData) || ObjectUtils.isEmpty(sdfData[args.unitId])) {
      const waterElectricArray = [];
      for (const item of (gljWaterElectricTemplate2022)) {
        waterElectricArray.push(ObjectUtil.cloneDeep(item));
      }
      // waterElectricData： 水电费列表数据缓存
      // customWaterElectric： 独立计取水电费设置的值 customWaterElectricFlag 是否独立计取水电费 false 不独立计取 true 独立计取
      // totalWaterCost 水费  totalElectricCost 电费  waterElectricCost  水电费
      let waterElectricCostData = {
        waterElectricData: waterElectricArray,
        customWaterElectric: null,
        customWaterElectricFlag: false,
        totalWaterCost: 0,
        totalElectricCost: 0,
        waterElectricCost: 0
      };
      if (ObjectUtil.isEmpty(sdfData)) {
        ProjectDomain.getDomain(args.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA, {
          [args.unitId]: waterElectricCostData
        });
      } else {
        sdfData[args.unitId] = waterElectricCostData;
      }
    }
  }

  /**
   * 清零水电费数据
   */
  clearWaterElectricCost(args) {
    const { unitId, singleId, constructId } = args;
    const sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA)[unitId];
    for (const item of sdfData.waterElectricData) {
      this.calculateByBaseValue(constructId, item, 0, sdfData.waterElectricData);
    }
    sdfData.totalWaterCost = 0;
    sdfData.totalElectricCost = 0;
    sdfData.waterElectricCost = 0;
  }

  /**
   * 水电费页面临时更新水电费数据
   */
  async updateWaterElectricCostData(args) {
    // 添加临时编辑的标识参数
    return await this.calculateWaterElectricCost(args);
  }

  /**
   * 保存水电费数据
   */
  async saveWaterElectricCostData(args) {
    const { unitId, singleId, constructId, waterElectricCostData } = args;
    let newWaterElectricCostData = await this.calculateWaterElectricCost(args);
    const sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
    sdfData[unitId] = newWaterElectricCostData;
    // 触发费用代码计算
    await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      constructId: constructId,
      unitId: unitId,
      qfMajorType: null
    });
    return true;
  }

  /**
   * 获取水电费
   */
  async getWaterElectricCost(args) {
    const { unitId, singleId, constructId } = args;
    const result = { GCSF: 0, GCDF: 0, GCSDF: 0 };
    let sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
    if (ObjectUtil.isEmpty(sdfData) || ObjectUtils.isEmpty(sdfData[unitId])) {
      return result;
    }
    sdfData = sdfData[unitId];

    if (sdfData.customWaterElectricFlag === true) {
      // 勾选了独立记取水电费
      result.GCSDF = ObjectUtil.isEmpty(sdfData.customWaterElectric) ? 0 : sdfData.customWaterElectric;
    } else {
      result.GCDF = ObjectUtil.isEmpty(sdfData.totalElectricCost) ? 0 : sdfData.totalElectricCost;
      result.GCSF = ObjectUtil.isEmpty(sdfData.totalWaterCost) ? 0 : sdfData.totalWaterCost;
      result.GCSDF = ObjectUtil.isEmpty(sdfData.waterElectricCost) ? 0 : sdfData.waterElectricCost;
    }
    return result;
  }

  async autoCalculateWaterElectricCost(args) {
    const { unitId, singleId, constructId } = args;
    let newWaterElectricCostData = await this.calculateWaterElectricCost(args);
    const sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
    if (ObjectUtil.isNotEmpty(sdfData)) {
      sdfData[unitId] = newWaterElectricCostData;
    }
  }

}

GljWaterElectricCostMatchService.toString = () => '[class GljWaterElectricCostMatchService]';
module.exports = GljWaterElectricCostMatchService;
