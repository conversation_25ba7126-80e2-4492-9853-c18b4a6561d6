/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-02-02 10:35:04
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-07-13 23:28:59
 */
import infoMode from '@/plugins/infoMode';
import detailApi from '@gongLiaoJi/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useVirtualList } from '@gongLiaoJi/hooks/useGljVirtualList';
import { message } from 'ant-design-vue';
import {
  computed,
  nextTick,
  reactive,
  ref,
  watchEffect,
  watch,
  onDeactivated,
} from 'vue';
import {
  everyNumericHandler,
  quantityExpressionHandler,
  pureNumber,
  addHierarchy,
} from '@/utils/index';
import xeUtils from 'xe-utils';
import { globalData } from '@/components/qdQuickPricing/status.js';
import csProject from '@gongLiaoJi/api/csProject';

// vexTable.value: 表格ref
// codeField：编码字段名
// nameField: 名称字段名
// operateList: 操作列表
// resetCellData 重置当前单元格方法
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
let { gljCheckTab } = recordProjectData();
import { validateExpression } from '@/utils/index';
import deMapFun from '@/gongLiaoJiProject/views/projectDetail/customize/deMap';
import { validateQuantityExpression } from '@gongLiaoJi/utils/index';


export const decimalLimitationPathArray = (row, fieldArray = []) => {
  let fieldPathArray = [];
  if (row.type === '05') {
    // 定额下主材/设备
    fieldPathArray = [
      { property: 'baseJournalPrice', pathKey: 'EDIT_DEXZS_PRICE_PATH' }, // 单价
      { property: 'price', pathKey: 'EDIT_DEXZS_PRICE_PATH' }, // 单价
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_DEXZS_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_DEXZS_TOTALNUMBER_PATH' }, // 合价
      { property: 'ZSum', pathKey: 'EDIT_DEXZS_ZSUM_PATH' }, // 主材费单价
      { property: 'ZDSum', pathKey: 'EDIT_DEXZS_ZSUM_PATH' }, // 主材费单价
      { property: 'zdTotalSum', pathKey: 'EDIT_DEXZS_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_DEXZS_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'SSum', pathKey: 'EDIT_DEXZS_SSUM_PATH' }, // 设备费单价
      { property: 'SDSum', pathKey: 'EDIT_DEXZS_SSUM_PATH' }, // 设备费单价
      { property: 'sTotalSum', pathKey: 'EDIT_DEXZS_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_DEXZS_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else if (row.type === '06' || row.type === '09') {
    // 定额主材设备
    if (row.deResourceKind === 5 || row.deResourceKind === 4) {
      fieldPathArray = [
        { property: 'baseJournalPrice', pathKey: 'EDIT_DEZS_PRICE_PATH' }, // 单价
        { property: 'price', pathKey: 'EDIT_DEZS_PRICE_PATH' }, // 单价
        {
          property: 'baseJournalTotalNumber',
          pathKey: 'EDIT_DEZS_TOTALNUMBER_PATH',
        }, // 合价
        { property: 'totalNumber', pathKey: 'EDIT_DEZS_TOTALNUMBER_PATH' }, // 合价
        { property: 'ZSum', pathKey: 'EDIT_DEZS_ZSUM_PATH' }, // 主材费单价
        { property: 'ZDSum', pathKey: 'EDIT_DEZS_ZSUM_PATH' }, // 主材费单价
        { property: 'zdTotalSum', pathKey: 'EDIT_DEZS_ZTOTALSUM_PATH' }, // 主材费合价
        { property: 'zTotalSum', pathKey: 'EDIT_DEZS_ZTOTALSUM_PATH' }, // 主材费合价
        { property: 'SSum', pathKey: 'EDIT_DEZS_SSUM_PATH' }, // 设备费单价
        { property: 'SDSum', pathKey: 'EDIT_DEZS_SSUM_PATH' }, // 设备费单价
        { property: 'sTotalSum', pathKey: 'EDIT_DEZS_STOTALSUM_PATH' }, // 设备费合价
        { property: 'sdTotalSum', pathKey: 'EDIT_DEZS_STOTALSUM_PATH' }, // 设备费合价
      ];
    } else {
      // 定额人材机
      fieldPathArray = [
        { property: 'baseJournalPrice', pathKey: 'EDIT_DERCJ_PRICE_PATH' }, // 单价
        { property: 'price', pathKey: 'EDIT_DERCJ_PRICE_PATH' }, // 单价
        {
          property: 'baseJournalTotalNumber',
          pathKey: 'EDIT_DERCJ_TOTALNUMBER_PATH',
        }, // 合价
        { property: 'totalNumber', pathKey: 'EDIT_DERCJ_TOTALNUMBER_PATH' }, // 合价
        { property: 'RSum', pathKey: 'EDIT_DERCJ_RSUM_PATH' }, // 人工费单价
        { property: 'RDSum', pathKey: 'EDIT_DERCJ_RSUM_PATH' }, // 人工费单价
        { property: 'rTotalSum', pathKey: 'EDIT_DERCJ_RTOTALSUM_PATH' }, // 人工费合价
        { property: 'rdTotalSum', pathKey: 'EDIT_DERCJ_RTOTALSUM_PATH' }, // 人工费合价
        { property: 'CSum', pathKey: 'EDIT_DERCJ_CSUM_PATH' }, // 材料费单价
        { property: 'CDSum', pathKey: 'EDIT_DERCJ_CSUM_PATH' }, // 材料费单价
        { property: 'cTotalSum', pathKey: 'EDIT_DERCJ_CTOTALSUM_PATH' }, // 材料费合价
        { property: 'cdTotalSum', pathKey: 'EDIT_DERCJ_CTOTALSUM_PATH' }, // 材料费合价
        { property: 'JSum', pathKey: 'EDIT_DERCJ_JSUM_PATH' }, // 机械费单价
        { property: 'JDSum', pathKey: 'EDIT_DERCJ_JSUM_PATH' }, // 机械费单价
        { property: 'jTotalSum', pathKey: 'EDIT_DERCJ_JTOTALSUM_PATH' }, // 机械费合价
        { property: 'jdTotalSum', pathKey: 'EDIT_DERCJ_JTOTALSUM_PATH' }, // 机械费合价
      ];
    }
  } else if (row.type === '03') {
    fieldPathArray = [
      { property: 'baseJournalPrice', pathKey: 'EDIT_CSXM_CSX_PRICE_PATH' }, // 单价
      { property: 'price', pathKey: 'EDIT_CSXM_CSX_PRICE_PATH' }, // 单价
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_CSXM_CSX_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_CSXM_CSX_TOTALNUMBER_PATH' }, // 合价
      { property: 'RSum', pathKey: 'EDIT_CSXM_CSX_RSUM_PATH' }, // 人工费单价
      { property: 'RDSum', pathKey: 'EDIT_CSXM_CSX_RSUM_PATH' }, // 人工费单价
      { property: 'rTotalSum', pathKey: 'EDIT_CSXM_CSX_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_CSXM_CSX_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'CSum', pathKey: 'EDIT_CSXM_CSX_CSUM_PATH' }, // 材料费单价
      { property: 'CDSum', pathKey: 'EDIT_CSXM_CSX_CSUM_PATH' }, // 材料费单价
      { property: 'cTotalSum', pathKey: 'EDIT_CSXM_CSX_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_CSXM_CSX_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'JSum', pathKey: 'EDIT_CSXM_CSX_JSUM_PATH' }, // 机械费单价
      { property: 'JDSum', pathKey: 'EDIT_CSXM_CSX_JSUM_PATH' }, // 机械费单价
      { property: 'jTotalSum', pathKey: 'EDIT_CSXM_CSX_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_CSXM_CSX_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'ZSum', pathKey: 'EDIT_CSXM_CSX_ZSUM_PATH' }, // 主材费单价
      { property: 'ZDSum', pathKey: 'EDIT_CSXM_CSX_ZSUM_PATH' }, // 主材费单价
      { property: 'zdTotalSum', pathKey: 'EDIT_CSXM_CSX_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_CSXM_CSX_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'SSum', pathKey: 'EDIT_CSXM_CSX_SSUM_PATH' }, // 设备费单价
      { property: 'SDSum', pathKey: 'EDIT_CSXM_CSX_SSUM_PATH' }, // 设备费单价
      { property: 'sTotalSum', pathKey: 'EDIT_CSXM_CSX_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_CSXM_CSX_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else if (row.type === '0') {
    fieldPathArray = [
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_UNITLINE_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_UNITLINE_TOTALNUMBER_PATH' }, // 合价
      { property: 'rTotalSum', pathKey: 'EDIT_UNITLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_UNITLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'cTotalSum', pathKey: 'EDIT_UNITLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_UNITLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'jTotalSum', pathKey: 'EDIT_UNITLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_UNITLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'zdTotalSum', pathKey: 'EDIT_UNITLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_UNITLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'sTotalSum', pathKey: 'EDIT_UNITLINE_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_UNITLINE_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else if (row.type === '01' || row.type === '02') {
    fieldPathArray = [
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_FBLINE_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_FBLINE_TOTALNUMBER_PATH' }, // 合价
      { property: 'rTotalSum', pathKey: 'EDIT_FBLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_FBLINE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'cTotalSum', pathKey: 'EDIT_FBLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_FBLINE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'jTotalSum', pathKey: 'EDIT_FBLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_FBLINE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'zdTotalSum', pathKey: 'EDIT_FBLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_FBLINE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'sTotalSum', pathKey: 'EDIT_FBLINE_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_FBLINE_STOTALSUM_PATH' }, // 设备费合价
    ];
  } else {
    // 定额
    fieldPathArray = [
      { property: 'baseJournalPrice', pathKey: 'EDIT_DE_PRICE_PATH' }, // 单价
      { property: 'price', pathKey: 'EDIT_DE_PRICE_PATH' }, // 单价
      {
        property: 'baseJournalTotalNumber',
        pathKey: 'EDIT_DE_TOTALNUMBER_PATH',
      }, // 合价
      { property: 'totalNumber', pathKey: 'EDIT_DE_TOTALNUMBER_PATH' }, // 合价
      { property: 'RSum', pathKey: 'EDIT_DE_RSUM_PATH' }, // 人工费单价
      { property: 'RDSum', pathKey: 'EDIT_DE_RSUM_PATH' }, // 人工费单价
      { property: 'rTotalSum', pathKey: 'EDIT_DE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'rdTotalSum', pathKey: 'EDIT_DE_RTOTALSUM_PATH' }, // 人工费合价
      { property: 'CSum', pathKey: 'EDIT_DE_CSUM_PATH' }, // 材料费单价
      { property: 'CDSum', pathKey: 'EDIT_DE_CSUM_PATH' }, // 材料费单价
      { property: 'cTotalSum', pathKey: 'EDIT_DE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'cdTotalSum', pathKey: 'EDIT_DE_CTOTALSUM_PATH' }, // 材料费合价
      { property: 'JSum', pathKey: 'EDIT_DE_JSUM_PATH' }, // 机械费单价
      { property: 'JDSum', pathKey: 'EDIT_DE_JSUM_PATH' }, // 机械费单价
      { property: 'jTotalSum', pathKey: 'EDIT_DE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'jdTotalSum', pathKey: 'EDIT_DE_JTOTALSUM_PATH' }, // 机械费合价
      { property: 'ZSum', pathKey: 'EDIT_DE_ZSUM_PATH' }, // 主材费单价
      { property: 'ZDSum', pathKey: 'EDIT_DE_ZSUM_PATH' }, // 主材费单价
      { property: 'zdTotalSum', pathKey: 'EDIT_DE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'zTotalSum', pathKey: 'EDIT_DE_ZTOTALSUM_PATH' }, // 主材费合价
      { property: 'SSum', pathKey: 'EDIT_DE_SSUM_PATH' }, // 设备费单价
      { property: 'SDSum', pathKey: 'EDIT_DE_SSUM_PATH' }, // 设备费单价
      { property: 'sTotalSum', pathKey: 'EDIT_DE_STOTALSUM_PATH' }, // 设备费合价
      { property: 'sdTotalSum', pathKey: 'EDIT_DE_STOTALSUM_PATH' }, // 设备费合价
    ];
  }
  if (fieldArray.length > 0) {
    fieldPathArray = [...fieldArray, ...fieldPathArray];
  }
  return fieldPathArray;
};

export const useSubItem = ({
  operateList,
  vexTable,
  codeField = 'bdCode',
  nameField = 'deName',
  frameSelectRef = null,
  pageType,
  resetCellData = () => {},
  focusTable = () => {},
  checkUnit = () => {},
  emits = () => {},
  updateConstructRcj = () => {},
  api = pageType === 'fbfx'
    ? {
        updateData: detailApi.updateFbData,
        getList: detailApi.queryBranchDataByFbIdV1,
      }
    : {
        updateData: detailApi.itemUpdate,
        getList: detailApi.itemPage,
      },
  listCallback = () => {}, // 列表请求回调
}) => {
  const isFBFX = codeField === 'bdCode'; // 是否分部分项
  const projectStore = projectDetailStore();
  let currentInfo = ref();
  let feeFileList = ref([]);
  let pricingMethodList = ref([
    { label: '计算公式组价', value: 1 },
    { label: '定额组价', value: 2 },
    { label: '子措施组价', value: 3 },
  ]); // 组价方式下拉菜单
  let pricingMethodList2 = ref([
    { label: '计算公式组价', value: 1 },
    { label: '定额组价', value: 2 },
  ]); // 组价方式下拉菜单
  let djgcFileList = ref([]); //单价构成文件列
  let tableData = ref([]);
  let originalTableData = ref([]); // 表格原始数据
  let loading = ref(false);
  let page = ref(1);
  let limit = ref(300000);
  let lockFlag = ref(0); // 整体锁定状态 0 不锁定 1 锁定
  let addDataSequenceNbr = ref('');
  let isIndexAddInfo = ref(false); // 是否从索引页面添加数据
  let selectData = ref(null);
  let AnnotationsRefList = ref({});
  let AnnotationsCurrent = ref(null);
  //多单位------
  let addCurrentInfo = ref(); // 多单位选择时选中的清单数据
  let showUnitTooltip = ref(false); // 是否多单位选择
  let selectUnit = ref(); // 多单位时选择单位
  //end----------
  // 提示信息框------
  let infoVisible = ref(false); // 提示信息框是否显示
  let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
  let iconType = ref(''); // 提示信息框的图标
  let isSureModal = ref(false); // 提示信息框是否为确认提示框
  //end------
  let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
  let isUpdateFile = ref(false);
  const indexVisible = ref(false);
  // 编辑弹框相关
  let editKey = ref(''); // 单独编辑弹框记录得当前编辑字段
  let isShowModel = ref(false);

  let qdVisible = ref(false);
  let deVisible = ref(false);
  let bcDeRow = ref({});
  let rcjVisible = ref(false);
  let priceVisible = ref(false);
  let bdCode = ref('');
  let isSortQdCode = ref(false); // 编码重复是否继续
  let isClearEdit = ref(false); // 是否手动清除编辑状态
  let ishasRCJList = ref(false); //人材机定额且有人材机明细数据---不可编辑单价

  let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
  let materialVisible = ref(false); // 是否设置主材市场价弹框
  let materialType = ref('');
  let materialRow = ref({});
  let DJGCrefreshFeeFile = ref(false); //单价构成需要刷新取费文件列表
  let batchDeleteVisible = ref(false); // 批量删除弹框是否展示
  let batchALLDeleteVisible = ref(false); //批量删除所有子目
  let codeType = ref(1); // 批量删除 1 工程项目   2 单位
  let isOpenLockedStatus = ref(false); // 批量删除时是否打开锁定数据处理
  const radioStyle = reactive({
    display: 'flex',
    height: '30px',
    lineHeight: '30px',
  });
  let batchDataType = ref(1); // 批量删除类型 1 批量删除所有临时删除项  2 批量删除所有工程量为0项
  let currentUpdateData = ref(null); // 当前修改行数据，用于修改后请求接口多，当前行数据为点击到别的数据时使用

  let bdNamePulldownRef = ref(null); // 弹框选择框
  let showUnitTooltipType = ref('code');
  const {
    initVirtual,
    getScroll,
    renderedList,
    init,
    EnterType,
    onDragHeight,
    scrollToPosition,
    renderLine,
  } = useVirtualList();

  let bdNameTableList = ref([]); // 弹框选择框数据
  let isNameLock = ref(false);
  let isNameOpen = ref(false);
  let isNextOpen = ref(false); // 插入子目多定额时,是否进行下一条定额弹框展示
  let zmList = ref([]); // 插入子目列表数据
  let openLevelCheckList = ref();
  let associateSubQuotasVisible = ref(false); // 关联子目弹框是否展示
  let zmAssociateData = reactive({
    libraryNameRelation: '',
    zmDeList: [],
    zmPointList: [],
    zmVariableRuleList: [],
  });
  let isReplaceRow = ref(null); // 是替换行
  const currentZmDe = ref(null);
  const addZmlistRes = reactive({
    deList: [],
    conversionList: [],
  }); // 新增定额关联的子目

  onDeactivated(() => {
    // tableData.value = [];
    // originalTableData.value = [];
  });

  // 清单名称搜索
  const bdNameKeyupEvent = (row, value) => {
    // console.log('ae', value);
    // if (isNameLock.value) return;
    // if (value?.length > 1 || (row?.onCompositionEnd && row.name)) {
    //   const $pulldown = bdNamePulldownRef.value;
    //   console.log('$pulldown', $pulldown);
    //   if ($pulldown) {
    //     $pulldown?.showPanel();
    //   }
    // searchTableCode(value);
    // }
  };

  /**
   * 是否费用定额
   * 12的垂运，22的装饰超高、垂运不属于费用定额
   * isCostDe是否是费用定额 0不是  1 安文费 2 总价措施 3 超高 4 垂运 5 安装费'
   */
  const isNotCostDe = computed(() => {
    const { kind, isCostDe } = currentInfo.value || {};
    return (
      kind === '04' &&
      (!isCostDe ||
        isCostDe === 4 ||
        (projectStore.deStandardReleaseYear === '22' && isCostDe === 3))
    );
  });

  const onCompositionStart = row => {
    isNameLock.value = true;
  };

  const onCompositionEnd = (row, value) => {
    isNameLock.value = false;
    bdNameKeyupEvent({ ...row, onCompositionEnd: true }, value);
  };

  // 根据清单名称模糊搜索标准清单
  const searchTableCode = name => {
    console.log('🚀 ~ searchTableCode ~ name:', name);
    bdNameTableList.value = [];
    detailApi.searchQdByName({ name: name }).then(res => {
      console.log('🚀 根据清单名称模糊搜索标准清单:', res);
      if (res.status === 200 && res.result) {
        bdNameTableList.value = res.result;
      }
    });
  };

  const dbNameCellClickEvent = ({ row }) => {
    console.log('🚀 ~ dbNameCellClickEvent ~ row:', row);
    addCurrentInfo.value = row;
    const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
    row.unit = unit;
    const $pulldown = bdNamePulldownRef.value;
    if ($pulldown) {
      const $table = vexTable.value;
      if ($table) {
        isClearEdit.value = true;
        $table.clearEdit && $table.clearEdit();
      }
      if (row.unit && row.unit.length > 1) {
        showUnitTooltip.value = true;
        showUnitTooltipType.value = 'name';
      } else {
        updateQdByName(row.bdCodeLevel04, row.unit[0]);
      }
      $pulldown?.hidePanel();
      bdNameTableList.value = [];
      isClearEdit.value = false;
    }
  };

  // 通过标准编码插入清单
  const updateQdByName = (code, unit) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    detailApi.updateQdByCode(apiData).then(res => {
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        message.success('清单插入成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 首字母大写 originalBdCode\originalFxCode
   */
  const originalCode = `original${codeField
    .charAt(0)
    .toUpperCase()}${codeField.slice(1)}`;
  const originalName =
    nameField === 'deName'
      ? 'originalFxName'
      : `original${nameField.charAt(0).toUpperCase()}${nameField.slice(1)}`;
  const isUpdateByRow = (row, field, newValue, oldValue) => {
    return row[field] !== newValue;
  };
  // 修改人材机定额
  const updateRcj = (row, field) => {
    if (field === 'materialCode') return;
    let value;
    if (field === 'type') {
      value = row.deResourceKind;
    }

    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deId: row.deRowId,
      rcjDetailId: row.sequenceNbr,
      constructRcj: {
        [field === 'type' ? 'kind' : field]:
          field === 'type' ? value : row[field],
      },
    };
    console.log('修改人材机明细数据参数', apiData);
    detailApi.updateConstructRcj(apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      }
    });
  };
  const editRcj = ({ row, column }, newValue, oldValue) => {
    if (newValue == oldValue) {
      return;
    }
    let field = column.field;
    if (!row.deCode) {
      currentInfo.value.deCode = currentInfo.value.originalDeCode;
      return;
    }
    let rcjRow = xeUtils.clone(row, true);
    if (field === 'deCode') {
      field = 'materialCode';
      rcjRow.materialCode = newValue;
    }
    if (field === 'deName') {
      field = 'materialName';
      rcjRow.materialName = newValue;
    }
    if (field === 'originalQuantity') {
      field = 'totalNumber';
      rcjRow.totalNumber = newValue;
    }
    if (field === 'price' || field === 'baseJournalPrice') {
      if (projectStore?.taxMade == '1') {
        field = 'marketPrice';
        rcjRow.marketPrice = newValue;
      } else {
        field = 'marketTaxPrice';
        rcjRow.marketTaxPrice = newValue;
      }
    }
    if (field === 'type') {
      rcjRow.deResourceKind = newValue;
    }
    if (field === 'resQty') {
      rcjRow.resQty = newValue;
    }
    if (field === 'unit') {
      rcjRow.unit = newValue;
    }
    if (field === 'specification') {
      rcjRow.specification = newValue;
    }
    console.log(
      'editRcj({ row, column }, newValue, oldValue);',
      field,
      rcjRow,
      newValue,
      oldValue
    );
    updateRcj(rcjRow, field);
    isRcjCodeMainQuotaLibrary(field, row.materialCode);
  };

  /**
   * 编辑完成之后
   * @param {*} param0
   * @returns
   */
  const editClosedEvent = ({ row, column }, newValue, oldValue) => {
    console.log('--------feeFileList', feeFileList.value, newValue, oldValue);
    if (newValue == oldValue) return;
    if (['deName'].includes(column.field) && (newValue ?? '') === '') {
      row[column.field] = oldValue;
      return;
    }
    if (['01', '02', '03'].includes(row.kind)) {
      let nameMap = {
        costMajorName: 'costFileCode',
      };
      if (nameMap[column.field]) {
        row[nameMap[column.field]] = newValue;
      } else {
        row[column.field] = newValue;
      }
      updateFbData(row, column.field);
      return;
    }
    if (isClearEdit.value) return;
    if (row.kind === '05') {
      editRcj({ row, column }, newValue, oldValue);
      return;
    }
    let field = column.field;
    if (field == 'quantityExpression') {
      if(!validateQuantityExpression(newValue)){
          row[field] = oldValue;
          return message.error('您输入的工程量表达式非法，请修改！');
      }
      field = 'originalQuantity';
      row.originalQuantity = row.quantityExpression;
    }
    let codeValue = row[codeField]; // bdCode,fxCode
    if (field === 'deCode' && newValue != codeValue && codeValue) {
      isReplaceRow.value = row;
    }
    if (field === codeField) {
      codeValue = newValue;
    }
    // 判断单元格值是否被修改
    console.log(
      'row[field]',
      field,
      row[field],
      newValue,
      oldValue,
      isUpdateByRow(row, field, newValue, oldValue)
    );
    if (!newValue && !oldValue) return;
    // if (!vexTable.value.isUpdateByRow(row, field)) return;
    if (!isUpdateByRow(row, field, newValue, oldValue)) return;
    if (field === 'originalQuantity') {
      if (newValue == oldValue) {
        return;
      } else {
        if (['originalQuantity'].includes(field)) {
          function replaceChineseBrackets(str) {
            return str
              .replace(/[（（]/g, '(')
              .replace(/[））]/g, ')')
              .replace(/×/g, '*')
              .replace(/÷/g, '/');
          }
          let inputValue = replaceChineseBrackets(newValue);
          if (/[^0-9a-zA-Z_|\-|\*|\+|\/|\.|(|)|（|）]/g.test(inputValue)) {
            row[field] = oldValue;
            // vexTable.value.revertData(currentInfo.value, 'originalQuantity');
            return message.error('您输入的工程量表达式非法，请修改！');
          }
          const isNumber = /^-?\d+(\.\d+)?$/.test(inputValue);
          if (isNumber) {
            // 去掉前导零，保留小数，保留小数点后最多 8 位
            // inputValue = inputValue.replace(/^0+(?![$.])/, '');
            inputValue = inputValue
              .replace(/^0+(?=\d+\.?\d*$)/, '') // 去除非全零的前导零
              .replace(/^0+$/, '0') // 全零变单零
              .replace(/^\./, '0.'); // 修复 ".123" → "0.123"
            // inputValue = inputValue.replace(/(\.\d{8}).*$/, '$1');
          }
          newValue = inputValue || 0;
          row.originalQuantity = inputValue || 0;

          if (
            newValue?.indexOf('GCLMXHJ') == -1 &&
            (oldValue + '')?.indexOf('GCLMXHJ') > -1
          ) {
            isRun = false;
            infoMode.show({
              iconType: 'icon-qiangtixing',
              infoText:
                '已从工程量明细中引用，修改工程量将清空明细区数据，是否继续？',
              confirm: () => {
                // updateGclData(row, field, oldValue, () => {
                updateFbData(row, field, null, oldValue);
                // });
                infoMode.hide();
              },
              close: () => {
                row.originalQuantity = oldValue;
                infoMode.hide();
                isRun = true;
              },
            });
          } else {
            // updateGclData(row, field, oldValue);
            updateFbData(row, field, null, oldValue);
          }
        }
      }
    }

    if (!isRun) return;

    if ([94, 95].includes(row.kind)) {
      // 走定额下人材机明细修改
      const mappingRow = getDeRcjFieldMappingRow(row, field, newValue);
      const mapField = getDeRcjMappingField(field);
      updateConstructRcj(mappingRow, mapField, null, queryBranchDataById);
      return;
    }
    if (field === 'quantity') {
      field = 'quantityExpression';
    }
    row.quantityExpression =
      column.field === 'quantity' ? row.quantity : row.quantityExpression;
    if (field === codeField && !newValue) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '编码不可为空',
        confirm: () => {
          infoMode.hide();
          currentInfo.value[codeField] = currentInfo.value[originalCode];
        },
      });
      return;
    }
    nextTick(() => {
      // 如果索引弹窗出现，则不出现补充弹窗
      if ([codeField].includes(column.field) && indexVisible.value) return;

      currentUpdateData.value = row;
      // if (row.kind === '03') {
      //   changeQdByCode(field, codeValue, row);
      // } else
      // if (row.kind === '04'|| row.kind ==="-1") {
      if (row.rcjFlag === 1) {
        isRcjCodeMainQuotaLibrary(field, codeValue, row);
      } else {
        isMainQuotaLibraryCode(field, codeValue, row);
      }
      if (
        [
          'price',
          'baseJournalPrice',
          'RDSum',
          'CDSum',
          'JDSum',
          'ZDSum',
          'SDSum',
          'RSum',
          'CSum',
          'JSum',
          'ZSum',
          'SSum',
        ].includes(field)
      ) {
        if (!validateExpression(newValue)) {
          row[field] = oldValue;
          return message.error('您输入的值非法，请修改!');
        }
      }
      if (
        (['baseJournalPrice', 'price'].includes(field) &&
          row.CSum === 0 &&
          row.JSum === 0 &&
          row.RSum === 0 &&
          newValue != 0 &&
          ['03', '04'].includes(row.kind)) ||
        (['baseJournalPrice', 'price'].includes(field) &&
          ['08'].includes(row.kind))
      ) {
        row.originaPrice = oldValue;
        row[field] = newValue;
        priceVisible.value = true;
        return;
      }
      // else if (
      //   ['baseJournalPrice','price'].includes(field) &&
      //   row.CSum === 0 &&
      //   row.JSum === 0 &&
      //   row.RSum === 0 &&
      //   newValue == 0 &&
      //   !['06'].includes(row.kind)
      // ) {
      //   return;
      // }
      // }
      if (field === 'quantityExpression') {
        row.quantityExpression = newValue;
      }
      expressionEditEvent(field, row, () => {
        row.quantityExpression = row.originalQuantityExpression;
      });
      zjfPriceEditEvent(field, row, newValue, oldValue);
      if (![codeField, 'originalQuantity', 'zjfPrice'].includes(field)) {
        let nameMap = {
          costMajorName: 'costFileCode',
        };
        if (nameMap[field]) {
          row[nameMap[field]] = newValue;
        } else {
          row[field] = newValue;
        }
        updateFbData(row, field, null, oldValue);
      }
    });
  };

  // 更新工程量数据
  let isRun = true;
  const updateGclData = (row, field, oldValue, callBack = null) => {
    console.log('更新工程量数据');
    isUpdateFile.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deId: row.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      quantity: row.originalQuantity,
      type: 'cxxm',
    };
    console.log('apiDataupdateQuantity', apiData);
    detailApi.updateQuantity(apiData).then(res => {
      if (res.status === 200 && res.result && res.result.code !== 500) {
        // message.success('修改成功');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        console.log('🚀 ~ detailApi.updateQuantity ~ callBack:', callBack);
        if (callBack) {
          isRun = true;
          callBack();
        } else {
          queryBranchDataById('noPosition');
        }
      } else {
        row.originalQuantity = oldValue;
        message.error(res.result.message);
      }
    });
  };

  /**
   * 获取编辑区修改人材机明细，字段映射
   * @param {*} field
   * @returns
   */
  const getDeRcjMappingField = field => {
    const map = {
      name: 'materialName',
      fxCode: 'materialCode',
      bdCode: 'materialCode',
      quantity: 'totalNumber',
      zjfPrice: 'marketPrice',
      zjfTotal: 'total',
      zcfee: 'marketPrice',
      totalZcfee: 'total',
      sbfPrice: 'marketPrice',
      sbfTotal: 'total',
    };
    return map[field] || field;
  };
  /**
   * 获取编辑区修改人材机明细，字段映射后得数据
   */
  const getDeRcjFieldMappingRow = (row, field, newValue) => {
    // let copyRow = JSON.parse(JSON.stringify(row))
    const mapField = getDeRcjMappingField(field);
    if (mapField) {
      row[mapField] = newValue || row[field];
    }
    return row;
  };
  let prevSelectedCell = ref();
  const tableKeydown = e => {
    const { code, key } = e.$event;
    console.log('selectedCell:', code, key);
    // const selectedCell = e.$table.getSelectedCell();
    // console.log("🚀键盘选中的", selectedCell)

    // const editRecord = e.$table.getEditRecord();
    // console.log("🚀键盘编辑的", editRecord)

    if (
      ![
        'ArrowRight',
        'ArrowLeft',
        'ArrowDown',
        'ArrowUp',
        'Tab',
        'Enter',
      ].includes(code)
    ) {
      setTimeout(() => {
        const selectedCell = e.$table.getSelectedCell();
        selectedCell?.cell?.click();
      }, 50);
      return;
    }
    setTimeout(() => {
      const selectedCell = e.$table.getSelectedCell();
      setTableKeydownEnd(e);

      const editRecordCell = e.$table.getSelectedCell();
      console.log('🚀键盘编辑的', editRecordCell);
      if (!selectedCell) {
        e.$table.clearEdit();
      } else {
        // selectedCell?.cell?.click();
      }
    }, 50);
  };

  const setTableKeydownEnd = e => {
    const selectedCell = e.$table.getSelectedCell();
    e.$table.clearEdit();
    console.log('🚀键盘选中的', selectedCell);
    if (selectedCell) {
      prevSelectedCell.value = selectedCell;
    }
  };

  const setSelectCellFn = () => {
    console.log('设置选中的', prevSelectedCell.value);
    vexTable.value?.setSelectCell(
      prevSelectedCell.value?.row,
      prevSelectedCell.value?.column
    );
  };

  /**
   * 表达式处理
   * @param {*} field
   * @param {*} row
   * @param {*} revertDataCallback
   * @returns
   */
  const expressionEditEvent = (field, row, revertDataCallback) => {
    console.log('表达式处理', row);
    if (field !== 'quantityExpression') return;
    const expressionArr = row.quantityExpression.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const orgExpressionArr = row.originalQuantityExpression
      .toString()
      ?.match(/[A-Za-z0-9]+(\.\d+)?/g);
    const [isSuccess, msg] = quantityExpressionHandler(row);
    if (isSuccess) {
      revertDataCallback();
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = msg;
    } else if (
      !orgExpressionArr?.includes('HSGCL') &&
      expressionArr.includes('HSGCL')
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '计算式输入非法，请重新输入标准四则运算表达式或数值',
        confirm: () => {
          infoMode.hide();
          currentInfo.value.quantityExpression =
            currentInfo.value.originalQuantityExpression;
        },
      });
    } else if (
      !expressionArr.includes(row.quantityVariableName) &&
      orgExpressionArr?.includes(row.quantityVariableName)
    ) {
      infoMode.show({
        iconType: 'icon-qiangtixing',
        infoText: '工程量明细已被调用，是否清空工程量明细？',
        confirm: () => {
          updateFbData(currentInfo.value, 'quantityExpression');
          isUpdateQuantities.value = true;
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
          currentInfo.value.quantityExpression =
            currentInfo.value.originalQuantityExpression;
        },
      });
    } else {
      row.quantityExpression = everyNumericHandler(row.quantityExpression);
      updateFbData(row, field);
    }
  };

  /**
   * 判断输入的定额编码是否为主定额库编码
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isMainQuotaLibraryCode = (field, code, row) => {
    if (field !== codeField) return;
    // let apiData = {
    //   unitId: projectStore.currentTreeInfo?.id,
    //   constructId: projectStore.currentTreeGroupInfo?.constructId,
    //   singleId: projectStore.currentTreeGroupInfo?.singleId,
    //   code: code,
    // };
    // console.log('判断是否为主定额册下的标准定额参数', apiData);
    // detailApi.isMainQuotaLibraryCode(apiData).then(res => {
    //   console.log('判断是否为主定额册下的标准定额', res);
    //   if (res.status === 200) {
    //     if (res.result) {
    //       updateDeReplaceData(code);
    //     } else {
    //       isStandardDe(code);
    //     }
    //   }
    // });
    let apiData = {
      pointLine: JSON.parse(JSON.stringify(row)),
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      type: pageType === 'fbfx' ? 1 : 2,
    };
    console.log('判断是否为主定额册下的标准定额', apiData);
    detailApi.updateDeReplaceData(apiData).then(res => {
      console.log('判断是否为主定额册下的标准定额', res);
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        deVisible.value = false;
        addDeInfo.value = res.result;
        // getGlgSettingData(res.result.standardDeId);
        queryBranchDataById('queryRule');
      } else {
        isStandardDe(code, row);
      }
    });
  };

  /**
   * 判断输入的定额编码是否是标准定额
   * @param {*} code
   */
  const isStandardDe = (code, row) => {
    if (bcDeRow.value.kind) {
      return;
    }
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      libraryCode: projectStore.currentTreeInfo?.deLibrary,
    };
    detailApi.isStandardDe(apiData).then(res => {
      console.log('判断输入的定额编码是否是标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code, row);
        } else {
          bcDeRow.value = row;
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额库下未找到该子目，是否补充子目？',
            confirm: () => {
              console.info(88888888888, currentInfo.value[originalCode]);
              deVisible.value = true;
              bdCode.value = code;
              infoMode.hide();
            },
            close: () => {
              infoMode.hide();
              bcDeRow.value = {};
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            },
          });
        }
        console.log('判断输入的定额编码是否为主定额库编码', res);
      }
    });
  };

  /**
   * 分部分项 措施项目 替换定额数据
   * @param {*} code
   */
  const updateDeReplaceData = (code, row) => {
    let apiData = {
      pointLine: JSON.parse(JSON.stringify(row)),
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
      type: pageType === 'fbfx' ? 1 : 2,
    };
    console.log('通过标准编码插入定额', apiData);
    detailApi.updateDeReplaceData(apiData).then(res => {
      console.log('通过标准编码插入定额结果', res);
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = row.sequenceNbr;
        deVisible.value = false;
        if (row.standardId) {
          message.success('定额替换成功');
        } else {
          message.success('定额插入成功');
        }

        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否与主定额库编码相同
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isRcjCodeMainQuotaLibrary = (field, code, row) => {
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isRcjCodeMainQuotaLibrary(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          // 输入的编码为主定额库编码
          updateBjqRcjReplaceData(code, row);
        } else {
          isStandardRcj(code, row);
        }
      }
    });
  };

  /**
   * 分部分项 措施项目 替换编辑区的人材机数据
   * @param {*} code
   */
  const updateBjqRcjReplaceData = (code, row) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(row)),
      code: code,
      region: 0,
    };
    detailApi.updateBjqRcjReplaceData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = row.sequenceNbr;
        message.success('人材机替换成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否标准人材机数据
   * @param {*} code
   */
  const isStandardRcj = (code, row) => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    detailApi.isStandardRcj(apiData).then(res => {
      console.log('=============');
      if (res.status === 200) {
        if (res.result) {
          updateBjqRcjReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额下不存在该材料编码,是否补充人材机？',
            confirm: () => {
              rcjVisible.value = true;
              bdCode.value = code;
              infoMode.hide();
            },
            close: () => {
              currentInfo.value.bdCode = row.originalBdCode;
              infoMode.hide();
            },
          });
        }
      }
    });
  };

  const changeQdByCode = (field, code, row) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.changeQdByCode(apiData).then(res => {
      if (res.status === 200 && res.result) {
        isStandQd(field, res.result, row);
      }
    });
  };

  /**
   * 判断是否是标准清单
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isStandQd = (field, code, row) => {
    if (field !== codeField) return;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.isStandQd(apiData).then(res => {
      console.log('判断是否是标准清单', res);
      if (res.status === 200) {
        if (res.result) {
          const unit = res.result.unit;
          const unitArr = Array.isArray(unit) ? unit : unit?.split('/');
          res.result.unit = unitArr;
          addCurrentInfo.value = res.result;
          addCurrentInfo.value.bdCodeLevel04 = code;
          if (code.length === 9) {
            if (unitArr && unitArr.length > 1) {
              showUnitTooltip.value = true;
            } else {
              updateQdByCode(code, unitArr[0], row);
            }
          } else {
            isQdCodeExist(code, res.result, row);
          }
        } else {
          isQdCodeExist(code, res.result, row);
        }
      }
    });
  };

  /**
   * 判断清单编码是否存在
   * @param {*} code
   * @param {*} obj
   */
  const isQdCodeExist = (code, obj, row) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    detailApi.isQdCodeExist(apiData).then(res => {
      console.log('判断清单编码是否存在', res, obj);
      // if (res.status === 200) {
      if (res) {
        // 若存在,则弹框提示是否继续
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: obj ? '' : '是否补充清单？',
          descText: obj
            ? '当前单位工程有相同清单编码，是否自动排序清单编码？'
            : '当前单位工程有相同清单编码，是否继续?',
          confirm: () => {
            if (!obj) {
              bdCode.value = code;
              qdVisible.value = true;
              isSortQdCode.value = false;
            } else {
              isSortQdCode.value = true;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null, row);
              }
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            if (obj) {
              isSortQdCode.value = false;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null, row);
              }
            } else {
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            }
          },
        });
      } else {
        // 根据是否为标准数据判断替换或补充
        if (!obj) {
          bdCode.value = code;
          qdVisible.value = true;
        } else {
          if (obj.unit && obj.unit.length > 1) {
            showUnitTooltip.value = true;
          } else {
            updateQdByCode(code, obj.unit ? obj.unit[0] : null);
          }
        }
      }
      // }
    });
  };

  /**
   * 通过标准编码插入清单
   * @param {*} code
   * @param {*} unit
   */
  const updateQdByCode = (code, unit, row) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(row)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    console.log('==============标准清单编码插入api参数', apiData);
    detailApi.updateQdByCode(apiData).then(res => {
      console.log('标准清单编码插入', res);
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        if (row.standardId) {
          message.success('清单替换成功');
        } else {
          message.success('清单插入成功');
        }
        queryBranchDataById();
      }
    });
  };

  const costMajorNameEditEvent = (field, row) => {
    console.log(
      'costMajorNameEditEvent',
      field,
      JSON.parse(JSON.stringify(row))
    );
    if (field !== 'costMajorName') return;
    row.costFileCode = feeFileList.value.filter(
      x => x.qfName === row.costMajorName
    )[0].qfCode;
    isUpdateFile.value = false;
  };
  const zjfPriceEditEvent = (field, row, newValue, oldValue) => {
    if (field !== 'zjfPrice') return;
    row.zjfPrice = Math.round(newValue * 100) / 100;
    if (Number(row.zjfPrice) === 0) {
      infoVisible.value = true;
      isSureModal.value = true;
      infoText.value = '定额单价不能为0';
      iconType.value = 'icon-qiangtixing';
      row.zjfPrice = row.originalZjfPrice;
      return;
    }
    updateFbData(row, field, null, oldValue);
  };

  /**
   * 更新数据
   * @param {*} row
   * @param {*} field
   */
  const updateFbData = (row, field, params = null, oldValue = '') => {
    console.log('pageType', pageType);
    isUpdateFile.value = true;
    let costMajorValue;
    //debugger;
    console.log('updateFbData', field, JSON.parse(JSON.stringify(row)));
    if (field === 'costMajorName') {
      costMajorValue = feeFileList.value.find(
        i => i.qfCode === row.costFileCode
      )?.qfCode;
      field = 'costFileCode';
      row.costFileCode = costMajorValue;
    }
    let column = null;
    let value = null;
    if (field) {
      column = field;
      value = field === 'costMajorName' ? costMajorValue : row[field];
    }
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitWorkId: projectStore.currentTreeInfo?.id,
      pointLineId: row.sequenceNbr,
      column,
      value,
      params,
    };
    console.log('数据更新参数', apiData);
    api
      .updateData(apiData)
      .then(res => {
        console.log('数据更新结果', res);
        console.info(3243243243243, res);
        if (res.status === 200 && res.result) {
          if (res.result.enableUpdatePrice) {
            if (field !== 'seq') {
              message.success('修改成功');
              if (editKey.value) {
                isShowModel.value = false;
              }
            }
            if (
              (row.kind === '01' || row.kind === '02' || row.kind === '0') &&
              field === nameField
            ) {
              emits('updateMenuList');
            }
            if (
              field === 'costMajorName' ||
              field === 'zjfPrice' ||
              field === 'quantityExpression' ||
              field === 'quantity'
            ) {
              isUpdateFile.value = true;
            }
            addDataSequenceNbr.value = currentInfo.value?.sequenceNbr;
            queryBranchDataById('noPosition');
          } else {
            infoMode.show({
              isSureModal: true,
              iconType: 'icon-qiangtixing',
              infoText: '调整后存在人工/材料/机械单价为0，请重新输入',
              confirm: () => {
                infoMode.hide();
                queryBranchDataById('noPosition');
                currentInfo.value.zjfPrice = currentInfo.value.originalZjfPrice;
              },
            });
          }
          field === 'costFileCode' ? queryDjgcFileData() : '';
        } else if (
          (field === 'baseJournalPrice' || field === 'price') &&
          res?.result === 'T001'
        ) {
          priceVisible.value = true;
          row.originaPrice = oldValue;
          row[field] = newValue;
        } else {
          row[field] = oldValue;
          message.error(res.message);
        }
      })
      .finally(() => {
        // setSelectCellFn();
      });
  };

  const addLevelToTree = (data, parentLevel = 0, parent = null) => {
    return data.map(node => {
      node.customParent = parent; // 自定义字段父级数据
      const { children, ...other } = node;
      return {
        ...node,
        customLevel: parentLevel + 1,
        children:
          (node.children || []).length > 0
            ? addLevelToTree(node.children, parentLevel + 1, other)
            : [],
      };
    });
  };

  /**
   * 获取列表信息
   * @param {*} EnterType //other 从其他页面需要初始化数据 ，Refresh, 修改了刷新数据
   * clearSelect 是否清除页面多选中的，默认清除
   */
  const queryBranchDataById = (
    EnterTypes = 'Refresh',
    posId = '',
    clearSelect = true,
    deleteIndex = -1
  ) => {
    if (loading.value) return;

    if (pageType === 'fbfx' && !projectStore.asideMenuCurrentInfo?.sequenceNbr)
      return;
    if (
      pageType === 'csxm' &&
      (!projectStore.currentTreeInfo?.id || loading.value)
    )
      return;
    if (projectStore.currentTreeInfo?.levelType === 2) return;
    checkUnit();
    loading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      pageSize: limit.value,
      pageNum: page.value,
      isAllFlag: !!posId,
      colorList: JSON.parse(JSON.stringify(projectStore.checkColorList)),
    };
    console.log('apiData', apiData, api.getList);

    api.getList(apiData).then(async res => {
      // debugger
      console.log('queryBranchDataById', res, EnterTypes);
      if (res.status === 200) {
        if (!res.result) {
          loading.value = false;
          tableData.value = [];
          // 快速组价存的当前列表数据
          globalData.editAreaTable = [];
          return;
        }

        let conversionRuleList = await conversionRuleListAll();

        let resultData = res.result.data.map(item => {
          // 标准换算标识
          let conversionRuleData = conversionRuleList.find(
            a => a.sequenceNbr === item.sequenceNbr
          );
          if (conversionRuleData) {
            item.redArray = conversionRuleData.redArray;
            item.blackArray = conversionRuleData.blackArray;
          }
          if (item.type === '05') {
            const parentCustom = res.result.data.find(
              a => a.sequenceNbr === item.parentId
            );
            if (parentCustom) {
              item.parentCustom = JSON.parse(JSON.stringify(parentCustom));
            }
          }
          return item;
        });
        if (resultData[0]?.type == '0') {
          resultData[0].kind = '00';
          resultData[0].deName = resultData[0].deName || '单位工程';
        }

        resultData = resultData.map(a => {
          return { kind: a.type, ...a };
        });
        let testTreeData = xeUtils.toArrayTree(resultData, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        resultData = xeUtils
          .toTreeArray(addLevelToTree(testTreeData))
          .map(i => {
            i.key = i.sequenceNbr;
            i.annotationsVisible =
              i?.noteViewVisible || i?.isShowAnnotations || i?.noteEditVisible;

            delete i.children;
            return i;
          });

        changeListHandler(resultData);
        tableData.value = resultData;
        console.log('🚀 获取的数据 :', tableData.value);
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));

        // 快速组价存的当前列表数据
        globalData.editAreaTable = originalTableData.value;

        // virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
          loading.value = false;
          let gljCheckTab = projectStore.gljCheckTab;
          let upSelRow = gljCheckTab[
            projectStore.currentTreeInfo.sequenceNbr
          ]?.tabList.find(a => a.tabName == '措施项目');
          console.log('🚀 ~ nextTick ~ upSelRow:', upSelRow);
          if (deleteIndex !== -1) {
            //处理删除数据重新定位到删除选中的那行
            if (tableData.value.length > deleteIndex) {
              currentInfo.value = tableData.value[deleteIndex];
            } else if (tableData.value.length > 0) {
              currentInfo.value = tableData.value[tableData.value.length - 1];
            }
          } else if (posId) {
            const doSomething = () => {
              let postRow = tableData.value.find(i => i.sequenceNbr == posId);
              currentInfo.value = postRow;
              console.log(
                '🚀 ~ doSomething ~ currentInfo.value:',
                currentInfo.value
              );
              if (!postRow) {
                message.error('未查询到当前数据123!');
              } else {
                vexTable?.value.scrollTo(
                  { rowKey: currentInfo.value.key },
                  'auto'
                );
              }
              if (EnterTypes !== 'noPosition') {
                vexTable.value.scrollTo(
                  { rowKey: currentInfo.value.key },
                  'auto'
                );
              }
            };

            const idleCallbackId = requestIdleCallback(doSomething);
            clearTimeout(idleCallbackId);
            addDataHandler(posId);
          } else if (upSelRow && upSelRow.selRowId !== '') {
            let obj = tableData.value.find(
              a => a.sequenceNbr == upSelRow.selRowId
            );
            if (!obj) {
              obj = tableData.value[0];
              upSelRow.selRowId = tableData.value[0].sequenceNbr;
            }
            currentInfo.value = obj;
            posId = upSelRow.selRowId;
            if (EnterTypes !== 'noPosition' && EnterTypes !== 'queryRule') {
              setTimeout(() => {
                vexTable.value.scrollTo({ rowKey: obj.key }, 'auto');
              }, 100);
            }
          } else {
            currentInfo.value = tableData.value?.[0];
          }
          if (EnterTypes === 'queryRule') {
            queryRcjDataByDeId();
          }
          listCallback();
          // focusTable();
        });
        // if (clearSelect) {
        //   frameSelectRef?.value.clearSelect();
        // }
        // lockFlagHandler();
        if (DJGCrefreshFeeFile.value) {
          queryFeeFileData(true); //刷新当前行重新获取取费文件列表
          queryDjgcFileData(); //切换选中数据更新单价构成列
        }
        deleteStateFn();
        getOpenLevelList();
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
          projectStore.isAutoPosition = false;
        }, 500);
        console.log('==========tableData', tableData.value);
      }
    });
  };

  const conversionRuleListAll = async materialCode => {
    return new Promise((resolve, reject) => {
      detailApi
        .conversionRuleListAll({
          constructId: projectStore.currentTreeGroupInfo?.constructId,
          unitId: projectStore.currentTreeInfo?.id,
        })
        .then(res => {
          resolve(res.result);
        })
        .catch(error => {
          reject(error);
        });
    });
  };
  const handleChilder = data => {
    if (data?.children) {
      data.children = data.children.map(j => j.sequenceNbr);
      for (let i of data.children) {
        handleChilder(i);
      }
    } else {
      return data;
    }
  };

  /**
   * 虚拟滚动处理
   * @param {*} type
   * @param {*} posId
   */
  const virtualListHandler = (type, posId) => {
    EnterType.value = type;
    const initList = init(tableData.value);
    setTimeout(() => {
      initList();
      if (posId) {
        console.log('🚀反向定位 ~ nextTick ~ posId:', posId);
        scrollToPosition(posId, tableData.value);
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId
        );
        // vexTable.value.setCurrentRow(currentInfo.value);
        projectStore.isAutoPosition = false;
      }
    }, 10);
  };
  function findChildren(tree, parentId, result = []) {
    for (const node of tree) {
      if (node.parentId === parentId) {
        result.push(node);
        findChildren(tree, node.sequenceNbr, result); // 递归查找子节点
      }
    }
    return result;
  }
  function flattenTreeToSequenceNbrs(treeData, sequenceNbrKey = 'sequenceNbr') {
    const sequenceNbrs = [];

    function traverse(node) {
      sequenceNbrs.push(node[sequenceNbrKey]);
      if (node.children) {
        node.children.forEach(child => traverse(child));
      }
    }

    treeData.forEach(node => traverse(node));

    return sequenceNbrs;
  }

  /**
   * 插入数据逻辑处理
   * @returns
   */
  const addDataHandler = posId => {
    if (addDataSequenceNbr.value) {
      tableData.value.forEach(item => {
        if (isIndexAddInfo.value) {
          if (item.sequenceNbr === addDataSequenceNbr.value) {
            currentInfo.value = item;
            // vexTable.value?.setCurrentRow(item);
            // vexTable.value?.scrollToRow(item);
          }
        } else if (item.sequenceNbr === currentInfo.value.sequenceNbr) {
          currentInfo.value = item;
        }
      });
      nextTick(() => {
        // frameSelectRef?.value.clearSelect();
        // vexTable.value.setCurrentRow(currentInfo.value);
        resetCellData();
        console.log('nextTick', currentInfo.value);
      });
    } else if (!isIndexAddInfo.value) {
      if (posId) {
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId
        );
        return;
      }
      let hasCurrentInfo = true; // 有点击选中的数据
      if (!currentInfo.value?.sequenceNbr) {
        // 没有点击选中的，则直接替换成数据第一个
        hasCurrentInfo = false;
      } else {
        // 有选中数据，但是在列表中没有找到
        if (
          tableData.value.every(
            item => item.sequenceNbr !== currentInfo.value.sequenceNbr
          )
        ) {
          hasCurrentInfo = false;
        } else {
          currentInfo.value = tableData.value.find(
            item => item.sequenceNbr === currentInfo.value.sequenceNbr
          );
        }
      }

      let handleCurrentInfo = currentInfo.value; // 重新赋值，为了人才机等子页面能监听到变化掉借口
      if (!hasCurrentInfo) {
        // 没有选中的数据，默认第一个选中，并清除所有数据
        handleCurrentInfo = tableData.value[0];
      }
      currentInfo.value = '';
      setTimeout(() => {
        currentInfo.value = handleCurrentInfo;
        // frameSelectRef?.value.setCurrentRow();
        // vexTable.value.setCurrentRow(currentInfo.value);
      }, 300);
    }
  };

  /**
   * 锁定处理
   */
  // const lockFlagHandler = () => {
  //   lockFlag.value = tableData.value
  //     .filter(data => data.kind === '03')
  //     .some(item => item.isLocked);
  //   if (operateList?.value) {
  //     operateList.value.find(item => item.name === 'lock-subItem').label =
  //       lockFlag.value ? '整体解锁' : '整体锁定';
  //   }
  // };

  /**
   * 组价方案匹配条件筛选
   * @param {*} val
   */
  const filterData = val => {
    let tempList = [];
    tableData.value = [];
    if (val.length === 0 || !val) {
      tableData.value = originalTableData.value;
    } else {
      originalTableData.value.forEach(item => {
        if (val.includes(item.matchStatus)) {
          tempList.push(item.sequenceNbr);
        }
      });
      for (let i = 0; i < originalTableData.value.length; i++) {
        if (
          tempList.includes(originalTableData.value[i].sequenceNbr) ||
          tempList.includes(originalTableData.value[i].parentId)
        ) {
          tableData.value.push(originalTableData.value[i]);
        }
      }
      tableData.value.forEach((item, index) => {
        item.index = (page.value - 1) * limit.value + (index + 1);
      });
    }
    // const initList = init(tableData.value);
    // nextTick(() => {
    //   initList();
    // });
  };
  /**
   * 处理新的表格数据
   */
  const handleNewTable = async result => {
    let res = {
      result,
    };
    if (res.result[0]) {
      if (res.result[0].type == '0') {
        res.result[0].kind = '00';
        res.result[0].deName = res.result[0].deName || '单位工程';
      }
      let conversionRuleList = await conversionRuleListAll();
      const idMap = {};
      // 遍历数据，找到所有type为03的条目
      let testTreeData = xeUtils.toArrayTree(res.result, {
        key: 'sequenceNbr',
        parentKey: 'parentId',
      });
      res.result = xeUtils.toTreeArray(addLevelToTree(testTreeData));
      res.result.forEach((item, index) => {
        item.annotationsVisible = item.isShowAnnotations;
        // 标准换算标识
        conversionRuleList.map(a => {
          if (a.sequenceNbr === item.sequenceNbr) {
            item.redArray = a.redArray;
            item.blackArray = a.blackArray;
          }
        });
        idMap[item.deRowId] = item;
        item.key = item.sequenceNbr;
        item.displaySign = 0;
        delete item.children;
        // item.dispNo = index
        if (deMapFun.isDe(item.type)) {
          // 查找父级条目
          const parentId = item.parentId;
          const parentItem = parentId ? idMap[parentId] : null;
          // 检查父级type是否为0、01或02
          if (parentItem && ['0', '01', '02'].includes(parentItem.type)) {
            // 设置一个属性标识这是最父级type为03的数据
            item.isTopLevelType03 = true;
            if (item.type != '07') item.resQty = '';
          }
        }
      });
      changeListHandler(res.result);
      tableData.value = res.result.map(a => {
        return { kind: a.type, optionMenu: [3, 4, 5], ...a };
      });
      console.log('gljtableData', tableData.value);

      queryFeeFileData(true);
      setTimeout(() => {
        addDataSequenceNbr.value = '';
        isIndexAddInfo.value = false;
        addCurrentInfo.value = null;
      }, 500);
    }
  };
  const handleDataCustom = async result => {
    let res = {
      result,
    };
    let conversionRuleList = await conversionRuleListAll();

    let resultData = res.result.map(item => {
      // 标准换算标识
      let conversionRuleData = conversionRuleList.find(
        a => a.sequenceNbr === item.sequenceNbr
      );
      if (conversionRuleData) {
        item.redArray = conversionRuleData.redArray;
        item.blackArray = conversionRuleData.blackArray;
      }
      if (item.type === '05') {
        const parentCustom = res.result.find(
          a => a.sequenceNbr === item.parentId
        );
        if (parentCustom) {
          item.parentCustom = JSON.parse(JSON.stringify(parentCustom));
        }
      }
      return item;
    });
    if (resultData[0]?.type == '0') {
      resultData[0].kind = '00';
      resultData[0].deName = resultData[0].deName || '单位工程';
    }

    resultData = resultData.map(a => {
      return { kind: a.type, ...a };
    });
    let testTreeData = xeUtils.toArrayTree(resultData, {
      key: 'sequenceNbr',
      parentKey: 'parentId',
    });
    resultData = xeUtils.toTreeArray(addLevelToTree(testTreeData)).map(i => {
      i.key = i.sequenceNbr;
      i.annotationsVisible =
        i?.noteViewVisible || i?.isShowAnnotations || i?.noteEditVisible;

      delete i.children;
      return i;
    });

    changeListHandler(resultData);
    tableData.value = resultData;
    console.log('🚀 获取的数据 :', tableData.value);
    originalTableData.value = JSON.parse(JSON.stringify(tableData.value));
    // 快速组价存的当前列表数据
    globalData.editAreaTable = originalTableData.value;

    if (DJGCrefreshFeeFile.value) {
      queryFeeFileData(true); //刷新当前行重新获取取费文件列表
      queryDjgcFileData(); //切换选中数据更新单价构成列
    }
    deleteStateFn();
    getOpenLevelList();
    setTimeout(() => {
      addDataSequenceNbr.value = '';
      isIndexAddInfo.value = false;
      addCurrentInfo.value = null;
      projectStore.isAutoPosition = false;
    }, 500);
  };
  /**
   * 对列表原数据做处理，对之前分开处理做合并一块处理，后续需要对数据做循环处理，统一在这里做处理
   * @param {} data
   */
  const changeListHandler = data => {
    for (let i = 0; i < data.length; ++i) {
      let item = data[i];
      if (item.constructionMeasureType === 2) {
        item.isAwfData = true;
        findChildren(data, item.sequenceNbr).map(a => {
          a.isAwfData = true;
          return a;
        });
      }
      if (item.defaultLine) {
        item.measureType = '';
      }
      if (item.appendType && item.appendType.length > 0) {
        if (item.appendType.includes('换')) {
          item.changeFlag = '换';
        }
        if (item.appendType.includes('借')) {
          item.borrowFlag = '借';
        }
      }
      item.index = (page.value - 1) * limit.value + (i + 1);
      item[originalCode] = item[codeField];
      item[originalName] = item[nameField];
      item.originalQuantityExpression = item.quantityExpression;
      // item.originalQuantity = item.quantity;
      item.originalZjfPrice = item.zjfPrice;
    }
  };

  /**
   * 获取所有的取费文件列表
   */
  const queryFeeFileData = (isRefresh = false) => {
    detailApi.queryBaseFeeData().then(res => {
      if (res.status === 200 && res.result) {
        feeFileList.value = res.result;
        DJGCrefreshFeeFile.value = false;
      }
    });
  };

  watchEffect(() => {
    if (
      currentInfo.value?.sequenceNbr &&
      ['04'].includes(currentInfo.value?.kind)
    ) {
      djgcFileList.value = [];
      queryDjgcFileData();
    }
  });

  watch(
    () => isNextOpen.value,
    () => {
      if (isNextOpen.value) {
        zmList.value.forEach(item => {
          isNextOpen.value = false;
          addDataSequenceNbr.value = item.data.sequenceNbr;
          addDeInfo.value = item.data;
          queryRcjDataByDeId();
        });
      }
    }
  );
  /**
   * 获取所有的单价构成文件
   */
  const queryDjgcFileData = async (isRefresh = false) => {
    console.log('queryDjgcFileData-useSubItem', pageType);
    // debugger;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deStandard: projectStore.deStandardReleaseYear,
      type: pageType,
      deId: currentInfo.value?.sequenceNbr,
    };
    // isRefresh ? (apiData.deId = currentInfo.value?.sequenceNbr) : '';
    if (projectStore.currentTreeInfo.levelType === 1 && !apiData.singleId)
      return; //工程项目时候不需要调用
    // await detailApi.queryDjgcFileData(apiData).then(res => {
    //   // debugger;
    //   console.log(
    //     'queryDjgcFileData- djgcFileList.value',
    //     apiData,
    //     res,
    //     djgcFileList.value
    //   );
    //   if (res.status === 200 && res.result) {
    //     djgcFileList.value = res.result;
    //   }
    // });
  };
  let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
  /**
   * 获取人材机明细数据
   * @param {*} bol
   * @param {*} deItem
   * @returns
   */
  const queryRcjDataByDeId = (bol = true, deItem = null) => {
    let apiData = {
      deRowId: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
      type: bol ? addDeInfo.value?.type : deItem.type,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    if (!apiData.deRowId) return;
    console.log('定额明细', apiData);
    detailApi.getAllRcjDetail(apiData).then(res => {
      // console.log('定额明细数据', res);
      if (res.status === 200) {
        if (res.result?.length > 0 && deItem) {
          ishasRCJList.value = true;
        }
        console.log(
          '定额明细数据',
          res,
          ishasRCJList.value,
          projectStore.globalSettingInfo
        );

        if (bol) {
          mainMaterialTableData.value = res.result.filter(
            x => x.kind === 5 || x.kind === 4
          );
          const showUnpricedPop =
            projectStore.convenienceSettings.get('UNPRICED')?.unPricedPop;
          if (mainMaterialTableData.value.length > 0 && showUnpricedPop) {
            materialVisible.value = true;
          } else {
            queryRule();
          }
        } else {
          queryRule();
        }
      }
    });
  };
  /**
   * 选中行数据根据锁定状态判断删除按钮是否可以点击
   */
  const deleteStateFn = () => {
    if (!currentInfo.value) return;
    let isDetete = operateList.value.find(
      item => item.name === 'delete-subItem'
    );
    if (projectStore.tabSelectName === '措施项目') {
      if (
        (currentInfo.value?.optionMenu?.includes(4) ||
          currentInfo.value?.optionMenu?.includes(5)) &&
        ((currentInfo.value.kind === '03' &&
          currentInfo.value.hasOwnProperty('zjcsClassCode') &&
          currentInfo.value.zjcsClassCode !== null &&
          currentInfo.value.zjcsClassCode !== undefined &&
          Number(currentInfo.value.zjcsClassCode) === 0) ||
          currentInfo.value.constructionMeasureType === 2)
      ) {
        isDetete.disabled = true;
      } else {
        isDetete.disabled = currentInfo.value.isLocked;
      }
    } else {
      isDetete.disabled = currentInfo.value.isLocked;
    }
    console.log('isDetete', isDetete);
  };

  /**
   * 选中单条分部分项数据
   * @param {*} param0
   */
  const currentChangeEvent = ({ row }) => {
    // const $table = vexTable.value;
    // 判断单元格值是否被修改
    // if ($table.isEditByRow(currentInfo.value)) return;
    currentInfo.value = row;
    ishasRCJList.value = false;
    if (row.kind === '04' && row.rcjFlag === 1) {
      queryRcjDataByDeId(false, row);
    }
    projectStore.SET_SUB_CURRENT_INFO(row);
    deleteStateFn();
    // if (row.kind === '04') {
    //   queryDjgcFileData(); //切换选中数据更新单价构成列
    // }
    // emits('getCurrentInfo', currentInfo.value);
  };

  let editContent = ref('');
  // 编辑内容保存事件
  const saveContent = () => {
    const valueType = typeof editContent.value;
    if (
      valueType !== 'string' ||
      (editKey.value !== 'deName' && editContent.value.trim() === '')
    ) {
      if (editKey.value === 'quantityExpression') {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-qiangtixing',
          infoText: '请输入工程量表达式',
          confirm: () => {
            infoMode.hide();
          },
        });
      }
      isShowModel.value = false; //空的话弹框编辑关闭
      return;
    }
    const oldValue = currentInfo.value[editKey.value];
    currentInfo.value[editKey.value] = editContent.value;

    expressionEditEvent(editKey.value, currentInfo.value, () => {
      editContent.value = currentInfo.value.originalQuantityExpression;
      vexTable.value.revertData(currentInfo.value, 'quantityExpression');
      currentInfo.value.quantityExpression =
        currentInfo.value.originalQuantityExpression;
    });
    if (editKey.value !== 'quantityExpression') {
      updateFbData(currentInfo.value, editKey.value, null, oldValue);
    }
  };

  let showModelTitle = ref('名称编辑');
  /**
   * 打开编辑弹框方法
   * @param {*} field
   */
  const openEditDialog = field => {
    console.log(field);
    setTimeout(() => {
      editKey.value = field;
      switch (field) {
        case nameField:
          showModelTitle.value =
            currentInfo.value.kind === '01'
              ? '标题行名称'
              : currentInfo.value.kind === '03'
                ? '措施项名称编辑'
                : '定额名称编辑';
          break;
        case 'projectAttr':
          showModelTitle.value = '项目特征编辑';
          break;
        case 'quantityExpression':
          showModelTitle.value = '工程量表达式编辑';
          break;
      }
      isShowModel.value = true;
      editContent.value = currentInfo.value[field];
    }, 100);
  };

  let standardVisible = ref(false);
  /**
   * 获取定额是否存在标准换算信息
   * @returns
   */
  const queryRule = () => {
    const standardPop = projectStore.convenienceSettings.get(
      'STANDARD_CONVERSION'
    );
    if (!addDeInfo.value?.standardId || !standardPop) {
      if (addDeInfo.value?.standardId) {
        // 标准换算弹窗隐藏，继续判断显示子目关联弹窗
        getGlgSettingData(addDeInfo.value?.standardId);
      }
      return;
    }
    let apiData = {
      standardDeId: addDeInfo.value?.standardId,
      fbFxDeId: addDeInfo.value?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      libraryCode: addDeInfo.value?.libraryCode,
    };
    console.log('标准换算列表参数', apiData);
    detailApi.queryRule(apiData).then(res => {
      console.log('标准换算列表数据', res);
      if (res.status === 200 && res.result) {
        if (res.result.conversionList && res.result.conversionList.length > 0) {
          standardVisible.value = true;
        } else {
          // 标准换算弹窗隐藏，继续判断显示子目关联弹窗
          getGlgSettingData(addDeInfo.value?.standardId);
        }
      }
    });
  };

  // 临时删除
  const updateDelTempStatusColl = (selectDataRows, keys) => {
    // let ids = [];
    // let deRowId = '';
    // if (row.kind === '05') {
    //   ids.push(row.sequenceNbr);
    //   deRowId = row.parentId;
    // } else if (selectData.value?.length > 1) {
    //   selectData.value.forEach(item => {
    //     ids.push(item.sequenceNbr);
    //   });
    // } else {
    //   ids.push(currentInfo.value.sequenceNbr);
    // }
    let deRowId = '';
    let ids = [];
    let deList = [];
    if (selectDataRows && selectDataRows.length > 0) {
      selectDataRows.forEach(item => {
        ids.push(item.sequenceNbr);
        deList.push({
          id: item.sequenceNbr,
          type: item.type,
          parentId: item.parentId,
        });
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
      deList.push({
        id: currentInfo.value.sequenceNbr,
        type: currentInfo.value.type,
        parentId: currentInfo.value.parentId,
      });
    }
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
      deRowId,
      deList,
    };
    console.log('临时删除参数', apiData);
    detailApi.csxmTempRemoveDeRow(apiData).then(res => {
      console.log('res临时删除', res);
      message.success('数据临时删除成功');
      queryBranchDataById();
    });
  };
  // 取消临时删除
  const updateCancelDelTempStatusColl = (selectDataRows, keys) => {
    // let ids = [];
    // let deRowId = '';
    // if (row.kind === '05') {
    //   ids.push(row.sequenceNbr);
    //   deRowId = row.parentId;
    // } else if (selectData.value?.length > 1) {
    //   selectData.value.forEach(item => {
    //     ids.push(item.sequenceNbr);
    //   });
    // } else {
    //   ids.push(currentInfo.value.sequenceNbr);
    // }
    let deRowId = '';
    let ids = [];
    let deList = [];
    if (selectDataRows && selectDataRows.length > 0) {
      selectDataRows.forEach(item => {
        ids.push(item.sequenceNbr);
        deList.push({
          id: item.sequenceNbr,
          type: item.type,
          parentId: item.parentId,
        });
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
      deList.push({
        id: currentInfo.value.sequenceNbr,
        type: currentInfo.value.type,
        parentId: currentInfo.value.parentId,
      });
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
      deRowId,
      deList,
    };
    console.log('取消临时删除参数', apiData);
    detailApi.csxmCancelTempRemoveDeRow(apiData).then(res => {
      console.log('res取消临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById();
      }
    });
  };
  // 临时删除
  const batchDelByTypeOfColl = () => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      dataType: batchDataType.value,
      rangeType: codeType.value,
      lockFlag: isOpenLockedStatus.value,
      kindArr: [],
    };
    console.log('批量删除参数', apiData);
    detailApi.batchDelByTypeOfColl(apiData).then(res => {
      console.log('res批量删除参数', res);
      if (res.status === 200 && res.result) {
        if (currentInfo.value.tempDeleteFlag) {
          message.success('数据已取消临时删除');
        } else {
          message.success('数据临时删除成功');
        }
        batchDeleteVisible.value = false;
        queryBranchDataById('noPosition');
      }
    });
  };

  const handleMainList = (item, row) => {
    if (item.children && item.code == 'MainList') {
      if (row.kind == '04') {
        item.visible = false;
        return;
      }
      item.visible = true;

      let showCode = ['set-list', 'del-all'];
      if (row.ifMainQd) {
        showCode = ['del-current', 'del-all'];
      }

      item.children.forEach(childItem => {
        childItem.fatherCode = 'MainList';
        childItem.visible = showCode.includes(childItem.code);
      });
    }
  };

  let areaStatus = ref(false);
  let areaVisibleType = ref('');
  let areaData = ref(null);

  // 鼠标批注右键操作
  const handleMainListClick = (item, row) => {
    console.log('🚀 ~ handleNoteClick ~ row:', row);
    console.log('🚀 ~ handleNoteClick ~ item:', item);
    // ifMainQd
    areaData.value = row;
    switch (item.code) {
      case 'set-list':
        row.ifMainQd = true;
        updateFbData(row, 'ifMainQd');
        break;
      case 'del-current':
        row.ifMainQd = false;
        updateFbData(row, 'ifMainQd');
        break;
      case 'del-all':
        areaStatus.value = true;
        areaVisibleType.value = 'MainList-all';
      default:
        break;
    }
  };

  const closeAreaModal = v => {
    areaStatus.value = false;
    if (v) {
      console.log(
        '🚀 ~ closeAreaModal ~ areaVisibleType.value:',
        areaVisibleType.value
      );
      let delName =
        areaVisibleType.value == 'note-all'
          ? 'csxmDeleteAllDeAnnotations'
          : 'batchRmoveMainQdController';
      let obj = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
      };
      if (areaVisibleType.value == 'note-all') {
        obj['applyConstruct'] = v == '1' ? true : false;
      } else {
        obj['type'] = +v;
      }
      console.info(88888888888, obj);
      detailApi[delName](obj)
        .then(res => {
          queryBranchDataById();
        })
        .finally(() => {
          areaVisibleType.value = '';
        });
    }
  };

  //处理批注的右键
  const handleNote = (item, row) => {
    if (item.children && item.code == 'noteList') {
      let defineBtn = [1, 6]; // 默认展示的批注操作
      let hideNoteBtn = [2, 3, 4, 6]; // 隐藏的批注操作
      let showNoteBtn = [2, 3, 5, 6]; // 展示的批注操作
      let checkList = defineBtn;

      if (!row.annotations) {
        // 没有批注的
        checkList = defineBtn;
      } else {
        // 有批注，
        checkList = row?.isShowAnnotations ? showNoteBtn : hideNoteBtn;
      }

      item.children.forEach(childItem => {
        childItem.fatherCode = 'noteList';
        childItem.visible = checkList.includes(childItem.type);
      });
      if (row.kind === '05') {
        item.visible = false;
      }
    }
  };

  // 鼠标批注右键操作
  const handleNoteClick = (item, row) => {
    console.log('🚀 ~ handleNoteClick ~ row:', row);
    console.log('🚀 ~ handleNoteClick ~ item:', item);
    switch (item.code) {
      case 'edit-note':
      case 'add-note':
        editAnnotations(row);
        break;
      case 'del-note':
        updateFbData({ ...row, annotations: '' }, 'annotations');
        break;
      case 'show-note':
        updateFbData({ ...row, isShowAnnotations: true }, 'isShowAnnotations');
        break;
      case 'hide-note':
        updateFbData({ ...row, isShowAnnotations: false }, 'isShowAnnotations');
        break;
      case 'del-all-note':
        areaStatus.value = true;
        areaVisibleType.value = 'note-all';
        break;
      default:
        break;
    }
  };

  const onFocusNode = row => {
    if (AnnotationsCurrent.value != row.sequenceNbr) {
      console.log('🚀 ~手动选中');
      editAnnotations(row);
    }
  };

  const editAnnotations = row => {
    row.noteEditVisible = true;
    console.log('🚀 ~手动选中', row);
    row.annotationsVisible = true;
    tableData.value = [...tableData.value];
    AnnotationsCurrent.value = row.sequenceNbr;
    nextTick(() => {
      AnnotationsRefList.value[row.sequenceNbr]?.focusNode();
    });
  };

  // 关闭编辑的
  const closeAnnotations = (v, row) => {
    if (!row?.isShowAnnotations) {
      row.noteEditVisible = false;
      row.noteViewVisible = false;
    }
    if (v == row?.annotations) {
      return;
    }
    updateFbData({ ...row, annotations: v }, 'annotations');
  };

  const cellMouseEnterEvent = ({ row, rowIndex, column, $event }) => {
    if (['deName'].includes(column.field) && row?.annotations) {
      row.annotationsVisible = true;
      tableData.value = [...tableData.value];
      row.noteViewVisible = true;
    }
  };

  const cellMouseLeaveEvent = ({ row, rowIndex, column, $event }) => {
    if (
      ['deName'].includes(column.field) &&
      !row?.noteEditVisible &&
      row?.annotations &&
      !row?.isShowAnnotations
    ) {
      row.annotationsVisible = false;
      tableData.value = [...tableData.value];
      row.noteViewVisible = false;
    }
  };
  const formatRangeCellText = ({ column, record, value }) => {
    if (column.dataIndex === 'originalQuantity') {
      return record.quantity;
    } else {
      return value;
    }
  };
  // 快速组价
  watchEffect(() => {
    globalData.editCurrentInfo = currentInfo.value;
  });
  watch(
    () => indexVisible.value,
    () => {
      projectStore.isOpenIndexModal = {
        open: indexVisible.value,
        tab: pageType,
      };
    }
  );
  const getAnnotationsRef = (el, row) => {
    if (el) {
      AnnotationsRefList.value[row.sequenceNbr] = el;
    } else {
      AnnotationsRefList.value[row.sequenceNbr] = null;
    }
  };
  const needAddQDandFB = (item, row) => {
    let addBT = item.children.find(i => i.name === '插入标题');
    let addZX = item.children.find(i => i.name === '插入子项');
    if (projectStore.standardGroupOpenInfo.isOpen) {
      //标准组价子窗口不可以添加分部
      addBT.disabled = true;
      addZX.disabled = true;
    } else {
      item.children.forEach(i => {
        currentInfo.value?.optionMenu.forEach(child => {
          if (child === item.code) {
            item.disabled = false;
          }
        });
      });
    }
  };
  /**
   * 编辑区类型字段处理
   * @param {*} row
   * @returns
   */
  const getTypeText = row => {
    let info = {
      text: '',
      className: '',
    };
    if (projectStore.combinedVisible) {
      if (row.matchStatus === '1') {
        info.text = '精';
        info.className = 'flag-green';
      }
      if (row.matchStatus === '2') {
        info.text = '近';
        info.className = 'flag-orange';
      }
      if (row.matchStatus === '0') {
        info.text = '未';
        info.className = 'flag-red';
      }
    }
    if ((!row.borrowFlag && !row.changeFlag) || row.type === '费') {
      info.text = row.type;
    } else if (row.type !== '费') {
      info.className = 'code-flag';
      info.text = (row.borrowFlag || '') + ' ' + (row.changeFlag || '');
    }
    return info;
  };

  const queryProjectConvenientSetColl = () => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    detailApi.queryProjectConvenientSetColl(apiData).then(res => {
      console.log('查询便捷性设置', res);
      if (res.status === 200 && res.result) {
        if (res.result.deGlTcFlag) {
          getSubitemGuidanceColl();
        } else {
          if (
            zmList.value[zmList.value.length - 1]?.data?.sequenceNbr !==
            addDeInfo.value.sequenceNbr
          ) {
            isNextOpen.value = true;
          }
        }
      }
    });
  };

  const getSubitemGuidanceColl = () => {
    let apiData = {
      parentDe: JSON.parse(JSON.stringify(currentInfo.value)),
    };
    detailApi.getSubitemGuidanceColl(apiData).then(res => {
      console.log('获取子目列表', res);
      if (res.status === 200 && res.result) {
        if (res.result.length > 0) {
          associateSubQuotasVisible.value = true;
        } else {
          if (
            zmList.value[zmList.value.length - 1]?.data?.sequenceNbr !==
            addDeInfo.value.sequenceNbr
          ) {
            isNextOpen.value = true;
          }
        }
      }
    });
  };

  // 获取展开到列表
  const getOpenLevelList = () => {
    detailApi
      .measuresItemOpenLevelCheckList({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        unitId: projectStore.currentTreeInfo?.id,
      })
      .then(res => {
        openLevelCheckList.value = res.result;
        console.log('展开列表接口返回值', openLevelCheckList.value);
        operateList.value.forEach(item => {
          if (item.name == 'expandTo') {
            item.options.forEach(item => {
              item.isValid = res.result[item.kind]?.hasCheck;
            });
          }
        });
      });
  };

  const getGlgSettingData = deId => {
    console.log('————————————————————————————调用getGlgSettingData');
    csProject
      .getGljSetUp({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {
        console.log('res.result', res.result, JSON.stringify(res.result));
        const canPopUp = res.result.get('RELATION_DE')?.relationDePop;
        console.log('是否显示子目关联弹窗', canPopUp);
        if (canPopUp) {
          hasAssociatedWithSubItem(deId);
        }
      });
  };
  const hasAssociatedWithSubItem = deId => {
    const apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deId: deId || addDeInfo.value?.standardId,
    };
    console.log('——————————————是否有子目接口参数', apiData);
    detailApi.isAssociatedWithSubItem(apiData).then(res => {
      console.log('____________是否有子目', res);
      if (res.status === 200 && res.result) {
        associateSubQuotasVisible.value = true;
        getAssociatedQuota(apiData);
      } else {
        // 如果没有子目关联，重置。否则5-1 改 1-1 之后 isReplaceRow 会变为有值，再操作 新增 5-1， isReplaceRow 有值，会执行 zmCalculateVariable
        isReplaceRow.value = null;
      }
    });
  };
  const getAssociatedQuota = apiData => {
    console.log('____________addDeInfo.value?', addDeInfo.value);
    detailApi.getAssociatedQuota(apiData).then(res => {
      console.log('zzzzzzzzzzzzzzz子目关联接口返回了', res);
      if (res.status === 200) {
        handleZmAssociateData(res.result, apiData.deId);
      }
    });
  };
  const handleZmAssociateData = (data, deId) => {
    if (data.zmPointList?.length) {
      data.zmPointList.unshift('显示所有');
    }
    let index = 1;
    // 递归调用为子项添加dispNo
    const assignDispNo = items => {
      items.forEach(item => {
        item.dispNo = index++;
        if (item.children?.length) {
          assignDispNo(item.children);
        }
      });
    };
    if (data.zmDeList?.length) {
      assignDispNo(data.zmDeList);
    }
    data.zmVariableRuleList.forEach((item, index) => {
      item.index = index + 1;
    });
    if (isReplaceRow.value) {
      console.log('_______替换行', isReplaceRow.value);
      const replacedRow = originalTableData.value.find(
        item => item.sequenceNbr === isReplaceRow.value.sequenceNbr
      );
      if (replacedRow && !data.zmVariableRuleList[0].resultValue) {
        const num = replacedRow.quantity * extractNumber(replacedRow.unit);
        data.zmVariableRuleList[0].resultValue = num;
        isReplaceRow.value = null;
        console.log('带回的值', num);
        zmCalculateVariable(data.zmVariableRuleList, deId);
      }
    }
    Object.assign(zmAssociateData, data);
  };

  const zmCalculateVariable = (zmVariableRuleList, deId) => {
    const params = {
      zmVariableRuleList,
      standardId: deId,
    };
    console.log('重新计算子目规则计算参数', params);
    const apiParams = JSON.parse(JSON.stringify(params));
    detailApi.zmCalculateVariable(apiParams).then(res => {
      console.log('子目规则计算返回______', res);
      if (res.status === 200) {
        emits('updateZmData', res.result);
        handleZmAssociateData(res.result);
      }
    });
  };

  const extractNumber = str => {
    const num = parseFloat(str);
    const result = isNaN(num) ? 1 : num;
    console.log('__单位数字', result);
    return result;
  };

  const updateZmAssociateData = data => {
    handleZmAssociateData(data);
  };

  const updateIsReplaceRow = value => {
    isReplaceRow.value = value;
  };

  return {
    getTypeText,
    updateQdByName,
    dbNameCellClickEvent,
    bdNameTableList,
    bdNamePulldownRef,
    showUnitTooltipType,
    bdNameKeyupEvent,
    onCompositionEnd,
    onCompositionStart,
    getAnnotationsRef,
    isNameOpen,

    queryBranchDataById,
    queryFeeFileData,
    queryDjgcFileData,
    editClosedEvent,
    currentChangeEvent,
    updateFbData,

    onDragHeight,
    initVirtual,
    getScroll,
    renderedList: tableData,
    init,
    EnterType,
    scrollToPosition,
    mainMaterialTableData,

    saveContent,
    openEditDialog,
    showModelTitle,

    currentInfo,
    isShowModel,
    editContent,
    editKey,
    infoVisible,
    infoText,
    iconType,
    isSureModal,
    handleNewTable,

    ishasRCJList,
    isClearEdit,
    isSortQdCode,
    bdCode,
    rcjVisible,
    deVisible,
    bcDeRow,
    qdVisible,
    DJGCrefreshFeeFile,
    isUpdateFile,
    indexVisible,
    isUpdateQuantities,
    selectUnit,
    showUnitTooltip,
    addCurrentInfo,
    isIndexAddInfo,
    addDataSequenceNbr,
    lockFlag,
    feeFileList,
    pricingMethodList,
    pricingMethodList2,
    tableData,
    originalTableData,
    materialVisible,
    materialType,
    materialRow,
    priceVisible,
    updateDelTempStatusColl,
    updateCancelDelTempStatusColl,
    batchDelByTypeOfColl,
    batchDeleteVisible,
    batchALLDeleteVisible,
    codeType,
    radioStyle,
    isOpenLockedStatus,
    batchDataType,
    selectData,
    djgcFileList,
    handleNote,
    handleNoteClick,
    areaStatus,
    areaVisibleType,
    handleMainList,
    handleMainListClick,
    closeAreaModal,
    closeAnnotations,
    queryRcjDataByDeId,
    addDeInfo,
    AnnotationsRefList,
    cellMouseEnterEvent,
    cellMouseLeaveEvent,
    formatRangeCellText,
    standardVisible,
    queryRule,
    onFocusNode,
    renderLine,
    loading,
    isNotCostDe,
    deleteStateFn,
    needAddQDandFB,
    currentUpdateData,
    tableKeydown,
    setTableKeydownEnd,
    queryProjectConvenientSetColl,
    isNextOpen,
    zmList,
    openLevelCheckList,
    getOpenLevelList,
    zmAssociateData,
    associateSubQuotasVisible,
    hasAssociatedWithSubItem,
    getAssociatedQuota,
    updateZmAssociateData,
    getGlgSettingData,
    updateIsReplaceRow,
    isReplaceRow,
    currentZmDe,
    addZmlistRes,
    handleDataCustom,
  };
};
