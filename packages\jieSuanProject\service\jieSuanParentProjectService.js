'use strict';




const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {Service} = require("../../../core");

/**
 * 合同外工程归属,复用清单，以及关联合同
 * @class
 */
class JieSuanParentProjectService extends Service {

    constructor(ctx) {
        super(ctx);
        this.itemBillProjectOptionService = this.service.jieSuanProject.itemBillProjectOptionService;
        this.baseBranchProjectOptionService = this.service.jieSuanProject.baseBranchProjectOptionService;
        this.stepItemCostService = this.service.jieSuanProject.stepItemCostService;
        this._baseBranchProjectOptionService = this.service.jieSuanProject.baseBranchProjectOptionService;
    }

    /**
     * 合同内单项工程查询
     */
    async queryParentProject(constructId, singleId, unitId) {
        let proJectData = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtil.isEmpty(proJectData)) {
            return ResponseData.fail("没有找到查询项目")
        }

        let itemBillProject;
        if (!ObjectUtil.isEmpty(singleId)) {
            // 获取已归属
            let reuseMockAllData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            if (ObjectUtil.isEmpty(reuseMockAllData)) {
                return ResponseData.fail("没有找到查询项目")
            }
            itemBillProject = reuseMockAllData.root;
        }


        let singleProjects = proJectData.singleProjects;
        let datas = [];
        let istrue;
        for (let singleProject of singleProjects) {
            if (singleProject.originalFlag) {
                let arr = [];
                let array = new Array();
                array = PricingFileFindUtils.getSubSingleUnits(singleProject,array)
                for (let unitProject of array) {
                    istrue = false;
                    if (!ObjectUtil.isEmpty(itemBillProject) && !ObjectUtil.isEmpty(itemBillProject.parentProjectId)) {
                        if (itemBillProject.parentProjectId === unitProject.sequenceNbr) {
                            istrue = true;
                        }
                    }

                    arr.push({
                        name: unitProject.upName,
                        sequenceNbr: unitProject.sequenceNbr,
                        parentProjectName: singleProject.projectName + "\\" + unitProject.upName,
                        isSelect: istrue,
                        levelType: 3
                    });
                }
                datas.push({
                    name: singleProject.projectName,
                    sequenceNbr: singleProject.sequenceNbr,
                    levelType: 2,
                    children: arr
                });
            }
        }
        return ResponseData.success(datas);
    }

    /*
    * 修改合同外单项工程的工程归属
    * */
    async updateParentProject(constructId, singleId, unitId, parentProjectId, parentProjectName) {
        let fbfx;
        let csxm;
        // let proJectData;
        try {
            // proJectData = PricingFileFindUtils.getProjectObjById(constructId);
            fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        } catch (err) {
            return ResponseData.fail("获取该单位工程失败，请排查！！")
        }

        // 拿到所有单项
        // let singleProjects = proJectData.singleProjects;
        // let parentProjectName;
        // for (let singleProject of singleProjects) {
        //     let unitProjects = singleProject.unitProjects;
        //     parentProjectName = singleProject.projectName;
        //     let istrue = false;
        //     if (!ObjectUtil.isEmpty(unitProjects)) {
        //         for (let unitProject of unitProjects) {
        //             if (unitProject.sequenceNbr === parentProjectId) {
        //                 parentProjectName += "/" + unitProject.upName;
        //                 istrue = true;
        //                 break;
        //             }
        //         }
        //         if (istrue) {
        //             break;
        //         }
        //     }
        // }
        try {
            for (let fb of fbfx.getAllNodes()) {
                if (fb.kind === "0" || fb.kind === "01" || fb.kind === "02" || fb.kind === "03") {
                    fb.parentProjectName = parentProjectName;
                    fb.parentProjectId = parentProjectId;
                }
            }
            for (let fb of csxm.getAllNodes()) {
                if (fb.kind === "0" || fb.kind === "01" || fb.kind === "02" || fb.kind === "03") {
                    fb.parentProjectName = parentProjectName;
                    fb.parentProjectId = parentProjectId;
                }
            }
        } catch (err) {
            return ResponseData.fail("修改失败")
        }


        return ResponseData.success("修改成功");
    }


    /*
    * 根据单位工程查询合同清单，包含分部分项和措施项目
    * */
    async queryInventory(constructId, singleId, unitId, name, difference, small, big) {
        let fbfx;
        let csxm;

        // let proJectData;
        try {
            // proJectData = PricingFileFindUtils.getProjectObjById(constructId);
            fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        } catch (err) {
            return ResponseData.fail("获取该单位工程失败，请排查！！");
        }

        let saiFbfx;
        let saiCsxm;
        if (ObjectUtil.isEmpty(name)) {
            saiFbfx = fbfx.filter(a => a.kind === "03" && a.zjcsClassCode !== "0");
            saiCsxm = csxm.filter(a => a.kind === "03" && a.zjcsClassCode !== "0");
        } else {
            // 根据名称关键字搜索
            saiFbfx = fbfx.filter(a => a.kind === "03" && a.zjcsClassCode !== "0" && !ObjectUtil.isEmpty(a.bdName) && a.bdName.includes(name));
            saiCsxm = csxm.filter(a => a.kind === "03" && a.zjcsClassCode !== "0" && !ObjectUtil.isEmpty(a.name) && a.name.includes(name));
        }

        if (difference === true) {
            saiFbfx = saiFbfx.filter(a => a.quantityDifferenceProportion < small || a.quantityDifferenceProportion > big);
            saiCsxm = saiCsxm.filter(a => a.quantityDifferenceProportion < small || a.quantityDifferenceProportion > big);

        }

        let zhanArr = [];

        for (let noArrElement of saiFbfx) {
            this.forAte(noArrElement.sequenceNbr, fbfx, zhanArr);
        }
        let csxmzhanArr = [];
        for (let noArrElement of saiCsxm) {
            this.forAte(noArrElement.sequenceNbr, csxm, csxmzhanArr);
        }
        // 只显示赛选清单以及其他定额
        // 拿到所有显示清单id
        let arrfbfx = [];
        let arrcsxm = [];
        saiFbfx.forEach(a => arrfbfx.push(a.sequenceNbr));
        saiCsxm.forEach(a => arrcsxm.push(a.sequenceNbr));
        saiFbfx = fbfx.filter(a => zhanArr.includes(a.sequenceNbr) || arrfbfx.includes(a.parentId));
        saiCsxm = csxm.filter(a => csxmzhanArr.includes(a.sequenceNbr) || arrcsxm.includes(a.parentId));

        let i = 1;
        for (let saiFbfx1 of saiFbfx) {
            saiFbfx1.index = i++;
        }
        i = 1;
        for (let saiFbfx1 of saiCsxm) {
            saiFbfx1.index = i++;
        }

        return ResponseData.success({fbfx: saiFbfx, csxm: saiCsxm});
    }

    queryData(fbfx) {
        let saiFbfx = [];
        for (let fbfx1 of fbfx) {
            if (fbfx1.kind === "03") {
                let istrue = true;
                for (let fbfx2 of fbfx) {
                    if (fbfx1.sequenceNbr === fbfx2.parentId) {
                        if (fbfx2.isCostDe === 1) {
                            istrue = false;
                        }
                    }
                }
                if (istrue) {
                    saiFbfx.push(fbfx1);
                }
            }
        }
        return saiFbfx;
    }

    /*
        * 合同外清单复用
        * */
    async setInventory(constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, fbfxData, csxmData, istrue, small, big, relevanceName) {
        let data;
        if (!ObjectUtil.isEmpty(fbfxData)) {
            data = await this.setfbfxData(constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, fbfxData, istrue, small, big, relevanceName);
        }
        if (!ObjectUtil.isEmpty(csxmData)) {
            data += await this.setcsxmData(constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, csxmData, istrue, small, big, relevanceName);
        }

        if (ObjectUtil.isEmpty(data)) {
            return ResponseData.success("清单复制成功");
        }
        return ResponseData.fail(data);
    }

    async updateRelevanceInventory(constructId, reuseSingleId, reuseUnitId, type, reuseSequenceNbr, relevanceName, relevanceId) {

        let reuseMockAllData;
        if (type === "fbfx") {
            try {
                reuseMockAllData = PricingFileFindUtils.getFbFx(constructId, reuseSingleId, reuseUnitId);
            } catch (err) {
                return ResponseData.fail("获取当前单位工程失败，请排查！！");
            }
        } else if (type === "csxm") {
            try {
                reuseMockAllData = PricingFileFindUtils.getCSXM(constructId, reuseSingleId, reuseUnitId);
            } catch (err) {
                return ResponseData.fail("获取当前单位工程失败，请排查！！");
            }
        } else {
            return ResponseData.fail(type + "没有业务处理");
        }
        for (let relevanceNameElement of reuseMockAllData) {
            if (relevanceNameElement.sequenceNbr === reuseSequenceNbr) {
                relevanceNameElement.relevanceName = relevanceName;
                relevanceNameElement.relevanceId = relevanceId;
                break;
            }
        }

        return ResponseData.success("修改成功")
    }

    async queryRelevanceInventory(constructId, relevanceId, type) {
        let split = relevanceId.split("/");
        let reuseMockAllData;
        if (type === "fbfx") {
            try {
                reuseMockAllData = PricingFileFindUtils.getFbFx(constructId, split[0], split[1]);
            } catch (err) {
                return ResponseData.fail("获取当前单位工程失败，请排查！！");
            }
        } else if (type === "csxm") {
            try {
                reuseMockAllData = PricingFileFindUtils.getCSXM(constructId, split[0], split[1]);
            } catch (err) {
                return ResponseData.fail("获取当前单位工程失败，请排查！！");
            }
        } else {
            return ResponseData.fail(type + "没有业务处理");
        }
        let filter = reuseMockAllData.filter(a => a.sequenceNbr === split[2] || a.parentId === split[2]);
        return ResponseData.success({data: filter, singleId: split[0], unitId: split[1]});
    }

    // 措施项目清单
    async setcsxmData(constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, fbfxData, istrue, small, big, relevanceName) {

        let fbfx;
        try {
            fbfx = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        } catch (err) {
            return "获取复用的单位工程失败，请排查！！";
        }
        let reuseFbfx;
        try {
            reuseFbfx = PricingFileFindUtils.getCSXM(constructId, reuseSingleId, reuseUnitId);
        } catch (err) {
            return "获取当前单位工程失败，请排查！！";
        }


        for (let item of fbfxData) {
            // 获取复用清单数据
            let fbfxDatum = item.sequenceNbr;
            // 获取措施项目的父级
            let pointLine;
            let parentLine;

            for (let fbfx1 of fbfx) {
                if (fbfx1.sequenceNbr === item.parentId) {
                    parentLine = fbfx1;
                    break;
                }
            }
            if (ObjectUtil.isEmpty(parentLine)) {
                return item.name + "在项目中找不到,请排查！！";
            }

            for (let reuseFbfx1 of reuseFbfx) {
                if (!ObjectUtil.isEmpty(reuseFbfx1.measureType) && reuseFbfx1.measureType === parentLine.measureType) {
                    pointLine = reuseFbfx1;
                    break;
                }
            }


            // 如果找不到对应的措施类别，直接添加,并获取复制后措施类型行
            if (ObjectUtil.isEmpty(pointLine)) {

                // 获取最后一个标题
                let reuseFbfx2 = reuseFbfx[0];
                for (let reuseFbfx1 of reuseFbfx) {
                    if (reuseFbfx1.kind === "01") {
                        reuseFbfx2 = reuseFbfx1;
                    }
                }
                // 添加标题，并获取标题
                this.stepItemCostService.save(constructId, reuseSingleId, reuseUnitId, reuseFbfx2,
                    {
                        "kind": "01",
                        "name": parentLine.name,
                        "measureType": parentLine.measureType,
                        "itemCategory": parentLine.itemCategory
                    });
                reuseFbfx = PricingFileFindUtils.getCSXM(constructId, reuseSingleId, reuseUnitId);
                for (let reuseFbfx1 of reuseFbfx) {
                    if (!ObjectUtil.isEmpty(reuseFbfx1.measureType) && reuseFbfx1.measureType === parentLine.measureType) {
                        pointLine = reuseFbfx1;
                        break;
                    }
                }
            }

            let parentId = pointLine.sequenceNbr;
            // 拿到同级最后一个清单进行复制
            for (let reuseFbfx1 of reuseFbfx) {
                if (reuseFbfx1.parentId === parentId && reuseFbfx1.kind === "03") {
                    pointLine = reuseFbfx1;
                }
            }


            // 添加复用清单行
            let arr = [];
            arr.push(item.sequenceNbr);
            // 添加复用清单的定额
            if (rule === 2) {
                // 查询服用清单的所有定额
                for (let fbfx1 of fbfx) {
                    if (fbfx1.parentId === fbfxDatum) {
                        arr.push(fbfx1.sequenceNbr);
                    }
                }
            }
            // 复制合同内分部分项清单
            // this.itemBillProjectOptionService.insertLine(constructId, reuseSingleId, reuseUnitId, pointLine, itemBillProject, sequenceNbr);
            ParamUtils.setParam("commonParam", {constructId: constructId, singleId: singleId, unitId: unitId})
            const copyLine = this.stepItemCostService.copyLine(arr);
            if (!copyLine) {
                return item.name + "无法复用";
            }
            // 粘贴合同内清单至合同外
            const result = await this.stepItemCostService.pasteLine(constructId, reuseSingleId, reuseUnitId, pointLine);

            if (!result) {
                return item.name + "添加失败！！";
            }

            reuseFbfx = PricingFileFindUtils.getCSXM(constructId, reuseSingleId, reuseUnitId);

            // 查询到复制后清单
            let itemBillProject;
            for (let reuseFbfx1 of reuseFbfx) {
                if (reuseFbfx1.parentId === parentId && reuseFbfx1.fxCode === item.fxCode && reuseFbfx1.name === item.name) {
                    itemBillProject = reuseFbfx1;
                    break;
                }
            }

            itemBillProject.relevanceName = relevanceName + "/" + item.fxCode;
            itemBillProject.relevanceId = singleId + "/" + unitId + "/" + item.sequenceNbr;
            itemBillProject.stageType = null;
            // 合同外结算工程量
            let num;
            if (quantityRule === 1) {
                // 合同内结算工程量
                let truenum;
                // 判断是大于正量差范围时走当工程量增加K%以上逻辑
                if (item.quantityDifferenceProportion > big) {
                    num = NumberUtil.numberScale4(item.jieSuanQuantity - item.quantity * (1 + big * 0.01));
                    truenum = item.jieSuanQuantity - num;
                } else {
                    num = item.jieSuanQuantity;
                    truenum = 0;
                }
                if (istrue) {
                    await this._baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, item.sequenceNbr, {jieSuanQuantityExpression: truenum + ""}, "csxm");
                }

            } else if (quantityRule === 2) {
                num = item.jieSuanQuantity;
            } else if (quantityRule === 3) {
                num = 0;
            }
            await this._baseBranchProjectOptionService.updateByList(constructId, reuseSingleId, reuseUnitId, itemBillProject.sequenceNbr, {quantityExpression: num + ""}, "csxm");
            // reuseFbfx.splice(n++, 0, itemBillProject);
        }
        return "";
    }


    // 获取到合同外单位工程下最后一个二级子部的第一个子部
    // 最后一层子分部
    extracted(reuseFbfx) {
        let sequenceNbr = reuseFbfx[0].sequenceNbr;

        let b = [];
        b.push(sequenceNbr);
        b = this.foraaa(reuseFbfx, sequenceNbr, b);
        // 服用清单的上级
        let pointLine;
        // let n;
        for (let i = 0; i < reuseFbfx.length; i++) {
            let reuseFbfx1 = reuseFbfx[i];
            if (b[0] === reuseFbfx1.sequenceNbr) {
                pointLine = reuseFbfx1;
                // n = i;
                break;
            }
        }
        return pointLine;
    }

    async setfbfxData(constructId, singleId, unitId, reuseSingleId, reuseUnitId, rule, quantityRule, fbfxData, istrue, small, big, relevanceName) {

        let fbfx;
        let ret;
        // let csxm;

        // let proJectData;
        try {
            // proJectData = PricingFileFindUtils.getProjectObjById(constructId);
            fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            // csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        } catch (err) {
            return "获取复用的单位工程失败，请排查！！";
        }
        let reuseFbfx;
        // let reuseCsxm;
        try {
            // proJectData = PricingFileFindUtils.getProjectObjById(constructId);
            reuseFbfx = PricingFileFindUtils.getFbFx(constructId, reuseSingleId, reuseUnitId);
            // reuseCsxm = PricingFileFindUtils.getCSXM(constructId, reuseSingleId, reuseUnitId);
        } catch (err) {
            return "获取当前单位工程失败，请排查！！";
        }

        let pointLine = this.extracted(reuseFbfx);

        let parentId = pointLine.sequenceNbr;
        // 拿到同级最后一个清单进行复制
        for (let reuseFbfx1 of reuseFbfx) {
            if (reuseFbfx1.parentId === parentId && reuseFbfx1.kind === "03") {
                pointLine = reuseFbfx1;
            }
        }

        for (let item of fbfxData) {
            // 获取复用清单数据
            let fbfxDatum = item.sequenceNbr;
            // for (let fbfx1 of fbfx) {
            //     if (fbfx1.sequenceNbr === fbfxDatum) {
            //         item = fbfx1;
            //         break;
            //     }
            // }
            if (ObjectUtil.isEmpty(item)) {
                return item.bdName + "在项目中找不到,请排查！！";
            }
            // 添加复用清单行
            let arr = [];
            arr.push(item.sequenceNbr);
            // 添加复用清单的定额
            if (rule === 2) {
                // 查询服用清单的所有定额
                for (let fbfx1 of fbfx) {
                    if (fbfx1.parentId === fbfxDatum) {
                        arr.push(fbfx1.sequenceNbr);
                    }
                }
            }
            // 复制合同内分部分项清单
            // this.itemBillProjectOptionService.insertLine(constructId, reuseSingleId, reuseUnitId, pointLine, itemBillProject, sequenceNbr);
            try {
                ParamUtils.setParam("commonParam", {constructId: constructId, singleId: singleId, unitId: unitId})
                let copyLine = this.itemBillProjectOptionService.copyLine(arr);
                if (!copyLine) {
                    return item.bdName + "无法复用！！";
                }
                // 粘贴合同内清单至合同外
                const result = await this.itemBillProjectOptionService.pasteLine(constructId, reuseSingleId, reuseUnitId, pointLine);
                if (!result) {
                    return item.bdName + "添加失败！！";
                }
            } catch (err) {
                ret = "";
            }
            reuseFbfx = PricingFileFindUtils.getFbFx(constructId, reuseSingleId, reuseUnitId);

            // 查询到复制后清单
            let itemBillProject;
            for (let reuseFbfx1 of reuseFbfx) {
                if (reuseFbfx1.parentId === parentId && reuseFbfx1.bdCode === item.bdCode && reuseFbfx1.bdName === item.bdName) {
                    itemBillProject = reuseFbfx1;
                    break;
                }
            }

            itemBillProject.relevanceName = relevanceName + "/" + item.bdCode;
            itemBillProject.relevanceId = singleId + "/" + unitId + "/" + item.sequenceNbr;
            itemBillProject.stageType = null;
            // 合同外结算工程量
            let num;
            if (quantityRule === 1) {

                // 合同内结算工程量
                let truenum;
                // 判断是大于正量差范围时走当工程量增加K%以上逻辑
                if (item.quantityDifferenceProportion > big) {
                    num = NumberUtil.numberScale4(item.jieSuanQuantity - item.quantity * (1 + big * 0.01));
                    truenum = item.jieSuanQuantity - num;
                } else {
                    num = item.jieSuanQuantity;
                    truenum = 0;
                }
                if (istrue) {
                    await this.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, item.sequenceNbr, {jieSuanQuantityExpression: truenum + ""});
                }
            } else if (quantityRule === 2) {
                num = item.jieSuanQuantity;
            } else if (quantityRule === 3) {
                num = 0;
            }
            await this.itemBillProjectOptionService.updateByList(constructId, reuseSingleId, reuseUnitId, itemBillProject.sequenceNbr, {quantityExpression: num + ""});


            // reuseFbfx.splice(n++, 0, itemBillProject);
        }
        return ret;
    }

    // 遍历查询数组下最后一个分部分支
    foraaa(reuseFbfx, sequenceNbr, arr) {
        let b = [];
        for (let reuseFbfx1 of reuseFbfx) {
            if (reuseFbfx1.parentId === sequenceNbr && reuseFbfx1.type === "部") {
                b.push(reuseFbfx1.sequenceNbr);
            }
        }
        if (ObjectUtil.isEmpty(b)) {
            return arr;
        }
        arr = b;
        return this.foraaa(reuseFbfx, b[b.length - 1], arr);
    }

    // 获取某个节点的上级分支到根节点
    forAte(a, arr, zhanArr) {
        for (let arrElement of arr) {
            if (a === arrElement.sequenceNbr) {
                zhanArr.push(arrElement.sequenceNbr);
                this.forAte(arrElement.parentId, arr, zhanArr);
                break;
            }
        }
    }

}


JieSuanParentProjectService.toString = () => '[class JieSuanParentProjectService]';
module.exports = JieSuanParentProjectService;
