
import {ItemBillProject} from "../../../electron/model/ItemBillProject";
import {StageQuantitiesListVo} from "./StageQuantitiesListVo";


export class JieSuanItemBillProject extends ItemBillProject{


    /**
     * 备份的ysf文件程量----- 对应页面的合同工程量
     */
    public backQuantity: string;
    /**
     * 合同单价
     */
    public backPrice: string;
    /**
     * 合同合价
     */
    public backTotal: string;

    /**
     *合同工程量表达式
     */
    public backQuantityExpression:string;
    /**
     *合同工程量表达式值
     */
    public backQuantityExpressionNbr:number;


    /**
     * 是否是原始数据  true 是  false 不是
     */
    public originalFlag: boolean;

    /**
     * 锁定综合单价 ；false：不锁定； true ：锁定 (原始数据默认值)
     */
    public lockPriceFlag: boolean;

    /**
     * 量差
     */
    public quantityDifference: number;

    /**
     * 量差比例
     */
    public quantityDifferenceProportion: number;


    /**
     * 防寒子目
     */
    public coldResistantSuborder : string;

    /**
     * 定额含量 （清单合同工程量 与定额合同工程量比例  六位小数）
     */
    public deProportion : string;

    /**
     * 量差比例颜色 1 红色  2 绿色
     */
    public quantityDifferenceProportionColour:number;



    /**
     * 分期方式
     */
    public stageType: number;

    /**
     * 分期工程量明细
     */
    public stageQuantitiesList: StageQuantitiesListVo;

    /**
     * 标砖换算kind3关联备份
     */
    public backCreateDeId: string;
    public backRelationDeId: string;


}