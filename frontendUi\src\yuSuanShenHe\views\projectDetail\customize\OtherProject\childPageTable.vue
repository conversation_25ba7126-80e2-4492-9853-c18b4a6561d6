<!--
 * @Descripttion:其他项目子页面表格
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-06-25 14:45:45
-->
<template>
  <split
    horizontal
    ratio="3/1"
    :horizontalBottom="35"
    style="height: 100%"
    mode="vertical"
  >
    <template #one>
      <vxe-grid
        ref="childTable"
        v-bind="gridOptions"
        v-on="gridEvents"
        :row-class-name="shChangeLabel"
      >
        <template #dispNo_default="{ row }">
          <span>{{row.ysshSysj?.dispNo}}</span>
        </template>
        <template #name_default="{ row }">
          <span>{{row.ysshSysj?.name}}</span>
        </template>
        <template #unit_default="{ row }">
          <span>{{row.ysshSysj?.unit}}</span>
        </template>
        <template #provisionalSum_default="{ row }">
          <span>{{row.ysshSysj?.provisionalSum}}</span>
        </template>
        <template #changeTotal_default="{ row }">
          <span>{{row.ysshSysj?.changeTotal}}</span>
        </template>
        <template #changeExplain_default="{ row }">
          <span>{{row.ysshSysj?.changeExplain}}</span>
        </template>
        <template #content_default="{ row }">
          <span>{{row.ysshSysj?.content}}</span>
        </template>
        <template #total_default="{ row }">
          <span>{{row.ysshSysj?.total}}</span>
        </template>

        <template #fxName_default="{ row }">
          <span>{{row.ysshSysj?.fxName}}</span>
        </template>
        <template #xmje_default="{ row }">
          <span>{{row.ysshSysj?.xmje}}</span>
        </template>
        <template #serviceContent_default="{ row }">
          <span>{{row.ysshSysj?.serviceContent}}</span>
        </template>
        <template #rate_default="{ row }">
          <span>{{row.ysshSysj?.rate}}</span>
        </template>
        <template #fwje_default="{ row }">
          <span>{{row.ysshSysj?.fwje}}</span>
        </template>

        <template #tentativeQuantity_default="{ row }">
          <span>{{row.ysshSysj?.tentativeQuantity}}</span>
        </template>
        <template #price_default="{ row }">
          <span>{{row.ysshSysj?.price}}</span>
        </template>
        <template #taxRemoval_default="{ row }">
          <span>{{row.ysshSysj?.taxRemoval}}</span>
        </template>
        <template #jxTotal_default="{ row }">
          <span>{{row.ysshSysj?.jxTotal}}</span>
        </template>
        <template #csPrice_default="{ row }">
          <span>{{row.ysshSysj?.csPrice}}</span>
        </template>
        <template #amount_default="{ row }">
          <span>{{row.ysshSysj?.amount}}</span>
        </template>

        <template #changeIdentification="{ row }">
          <span :class="shChangeLabel(row.ysshSysj?.change).class">{{shChangeLabel(row.ysshSysj?.change).label}}</span>
        </template>

        <template #dispNo_edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="clear()"
            @keyup="row.dispNo = sortAndlength(row.dispNo, 10)"
          ></vxe-input>
        </template>
        <template #unit_edit="{ row }">
          <vxeTableEditSelect
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :filedValue="row.unit"
            :list="projectStore.unitListString"
            @update:filedValue="
          newValue => {
            saveCustomInput(newValue, row, 'unit', $rowIndex);
          }
        "
          ></vxeTableEditSelect>
          <span v-else>
            {{ row.unit }}
          </span>
        </template>
        <template #name_edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.name"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
        <template #worksName_edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.worksName"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
        <template #spec_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.specification"
            type="text"
            @blur="clear()"
          ></vxe-input>
          <span v-else>
            {{ row.specification }}
          </span>
        </template>
        <template #fxName_edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.fxName"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
        <template #content_edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.content"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
        <template #xmje_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            v-model.trim="row.xmje"
            :maxlength="10"
            type="text"
            @blur="(row.xmje = pureNumber(row.xmje, 2)), clear()"
          ></vxe-input>
          <span v-else>
            {{ row.xmje }}
          </span>
        </template>
        <template #quant_edit="{ row }">
          <vxe-input
            v-if="hasDataType() && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.quantitativeExpression"
            :maxlength="1000"
            type="text"
            @blur="clear()"
            @keyup="
          row.quantitativeExpression = row.quantitativeExpression.replace(
            /[^\d.\-\+\*\/\{\}\[\]\(\)]/g,
            ''
          )
        "
          ></vxe-input>
          <span v-else>
            {{ row.quantitativeExpression }}
          </span>
        </template>

        <template #tentativeQuantity_edit="{ row }">
          <vxe-input
            v-if="hasDataType() && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.tentativeQuantity"
            :maxlength="10"
            type="text"
            @blur="row.tentativeQuantity = pureNumber(row.tentativeQuantity, 6);clear()"
          ></vxe-input>
          <span v-else>
            {{ row.tentativeQuantity }}
          </span>
        </template>

        <template #service_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.serviceContent"
            type="text"
            @blur="clear()"
          ></vxe-input>
          <span v-else>
            {{ row.serviceContent }}
          </span>
        </template>
        <template #dec_edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.description"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
        <template #amount_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            v-model.trim="row.amount"
            :maxlength="10"
            type="text"
            @blur="(row.amount = pureNumber(row.amount, 6)), clear()"
          >
          </vxe-input>
          <span v-else>
            {{ row.amount }}
          </span>
        </template>
        <template #rate_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            v-model.trim="row.rate"
            :maxlength="10"
            type="text"
            @blur="(row.rate = pureNumber(row.rate, 6)), clear()"
          >
          </vxe-input>
          <span v-else>
            {{ row.rate }}
          </span>
        </template>
        <template #price_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            v-model.trim="row.price"
            :maxlength="10"
            type="text"
            @blur="(row.price = pureNumber(row.price, 6)), clear()"
          >
          </vxe-input>
          <span v-else>
            {{ row.price }}
          </span>
        </template>
        <template #taxRemoval_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            :clearable="false"
            v-model.trim="row.taxRemoval"
            :maxlength="10"
            type="text"
            @blur="(row.taxRemoval = pureNumber(row.taxRemoval, 4)), clear()"
          >
          </vxe-input>
          <span v-else>
            {{ row.taxRemoval }}
          </span>
        </template>
        <template #changeExplain_edit="{ row }">
          <vxe-input
            v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
            v-model="row.ysshSysj.changeExplain"
            :maxlength="50"
          />
          <span v-else>{{row.ysshSysj?.changeExplain}}</span>
        </template>
        <template #empty>
          <span style="color: #898989; font-size: 14px; display: block; margin: 25px 0">
            <img :src="getUrl('newCsProject/none.png')" />
          </span>
        </template>
      </vxe-grid>
    </template>
    <template #two>
      <comparisonPage
        :currentInfo="currentInfo"
        :pageType="props.pageType"
        :needTitle="true"
      ></comparisonPage>
    </template>
  </split>
</template>
<script setup>
import {
  onMounted,
  onUpdated,
  onActivated,
  ref,
  watch,
  reactive,
  getCurrentInstance,
  nextTick,
  toRaw,
} from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
// import csProject from '@/views/shenHeYuSuanProject/api/csProject';
import shApi from '@/api/shApi';
import qtxmCommon from './qtxmCommon';
import { message, Modal } from 'ant-design-vue';
import { insetBus } from '@/hooks/insetBus';
import { useCellClick } from '@/hooks/useCellClick';
import { useOtherProRaction } from '@/hooks/useOtherProRactionYSSH';
let $table; //全局定义
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: '_X_ROW_KEY' }); //有些行sequenceNbr没有数据
import {
  getUrl,
  pureNumber,
  sortAndlength,
  isNumericExpression,
  shChangeLabel,
} from '@/utils/index';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
let isCurrent = ref(null);
let copyInfo = ref(''); //复制数据行
let childTable = ref();
let deleteInfo = ref();
let insertType = ref('');
const currentInfo = ref({});

const props = defineProps(['pageType', 'columnList']);
const {
  getOperateParams,
  copyOperate,
  disposeCurrentIndex,
  operateParams,
  getCurrentIndex,
  msgInfo,
  deleteOperate,
  deleteModal,
  menuConfigOptions,
  pasteIsDisabled,
  editCheckLength,
  setInsertType,
  getChildPageData,
  getOperateData,
  updateChangeExplain,
  hasDataType,
  tabBtnIsValid,
} = useOtherProRaction({ pageType: props.pageType });
const clear = () => {
  //清除编辑状态
  $table.clearEdit();
};
const getTableData = async (sequenceNbr = '', isFirst, type, row) => {
  loading.value = true;
  console.log(123, '123123');
  let resData = await getChildPageData(); //获取子页面表格数据
  console.log(resData, 'resData123');
  if (resData.status === 200) {
    loading.value = false;
    tableData.value = gridOptions.value.data = resData.result;
    if (tableData.value && tableData.value.length > 0) {
      if (sequenceNbr) {
        getCurrentIndex($table, { sequenceNbr });
      }
      isCurrent.value
        ? $table.setCurrentRow(tableData.value[isCurrent.value])
        : $table.setCurrentRow(tableData.value[0]);
    }
    if (isFirst) currentInfo.value = resData.result ? resData.result[0] : [];
    if (type === 'update') {
      currentInfo.value = tableData.value.filter(
        item => item.sequenceNbr === row.sequenceNbr
      )[0];
    }
    setInsertType($table);
  } else {
    tableData.value = gridOptions.value.data = [];
  }
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
};
const editClosedEvent = ({ row, column }) => {
  const field = column.field;
  let value = row[field];
  editCheckLength(row, column, $table);
  if (
    field === 'quantitativeExpression' &&
    row.quantitativeExpression &&
    row.quantitativeExpression.length < 1000
  ) {
    const [isSuccess, msg] = isNumericExpression(row.quantitativeExpression);
    if (isSuccess) {
      $table.revertData(row, 'quantitativeExpression');
      message.warn('数量表达式输入非法，请重新输入');
      return;
    }
  }
  // 判断单元格值没有修改
  if ($table.isUpdateByRow(row, field)) {
    if (field === 'ysshSysj.changeExplain') {
      operate('updateChangeExplain', row);
    } else {
      operate('update', row);
    }
  }
};
const menuConfig = reactive({
  ...menuConfigOptions,
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    $table.setCurrentRow(row);
    pasteIsDisabled(row, $table);
    if (hasDataType()) {
      insertType.value = row.dataType;
      projectStore.SET_DATATYPE(insertType.value);
    }
    return true;
  },
});
const contextMenuClickEvent = ({ menu, row }) => {
  menu.code === 'delete'
    ? getCurrentIndex($table)
    : getCurrentIndex($table, row);
  switch (menu.code) {
    case 'copy':
      // 复制
      copyOperate(row, $table);
      message.success('复制成功');
      break;
    case 'delete':
      // 删除
      menuConfig.body.options[0][2].disabled = true;
      deleteOperate(row, $table);
      break;
    case 'paste':
      // 粘贴
      row = { ...projectStore.otherProCopyInfo.copyInfo };
      operate('paste', row);
      break;
    case 'add':
      // 插入
      operate('insert', row);
      break;
    case 'addNull':
      // 插入数据行
      operate('insertData', row);
      break;
    case 'addTitle':
      // 插入标题行
      operate('insertTitle', row);
      break;
  }
};
const cellBeforeEditMethodYSSH = ({ row }) => {
  if (row.ysshSysj.change === 2) return false;

  return true;
};
const gridOptions = ref({
  align: 'center',
  loading: loading.value,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  data: tableData.value,
  height: 'auto',
  menuConfig: menuConfig,
  keepSource: true,
  class: 'table-edit-common',
  cellClassName: selectedClassName,
  loading: false,
  editConfig: {
    trigger: 'click',
    mode: 'cell',
    beforeEditMethod: cellBeforeEditMethodYSSH,
  },
  columns: props.columnList,
});
const currentChange = ({ row }) => {
  if (hasDataType()) {
    insertType.value = row.dataType;
    projectStore.SET_DATATYPE(insertType.value);
    getCurrentIndex($table, row);
  }
};
const gridEvents = ref({
  menuClick: contextMenuClickEvent,
  editClosed: editClosedEvent,
  cellClick: cellData => {
    useCellClickEvent(cellData, tableCellClickEvent, []);
  },
  currentChange: currentChange,
});
// useCellClickEvent
const tableCellClickEvent = ({ row }) => {
  currentInfo.value = row;
  return true;
};

const operate = async (type, row) => {
  let data = null;
  if (type === 'updateChangeExplain') {
    data = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: row.sequenceNbr,
      changeExplain: row.ysshSysj.changeExplain,
      changeType:
        props.pageType == 'zlje'
          ? 5
          : props.pageType == 'zygczgj'
          ? 6
          : props.pageType == 'zcbfwf'
          ? 7
          : props.pageType == 'jrg'
          ? 8
          : '',
    };
  }
  getOperateParams(type, row, $table);
  let apiData = toRaw(operateParams.value);
  console.log(data, '修改增减说明入参');
  let resData =
    type === 'updateChangeExplain'
      ? await updateChangeExplain(data)
      : await getOperateData(apiData);
  console.log(resData, '响应');
  if (resData.status === 200) {
    isCurrent.value = disposeCurrentIndex.value;
    getTableData('', false, type, row);
    message.success(
      `${type === 'updateChangeExplain' ? '修改' : msgInfo.value}成功`
    );
    if (type === 'update') {
      getCurrentIndex($table, row);
    }
  }
};

watch(
  () => projectStore.asideMenuCurrentInfo,
  () => {
    console.log('*****************其他项目子页面', props.pageType);
    if (qtxmCommon.isOtherProChild(props.pageType)) {
      getTableData();
      insertOperate();
    }
  }
);

watch(
  () => deleteModal.value.isDelete,
  () => {
    if (
      qtxmCommon.isOtherProChild(props.pageType) &&
      deleteModal.value.isDelete
    ) {
      operate('delete', deleteModal.value.deleteRow);
    }
  }
);
// onActivated(() => {

// });
const insertOperate = () => {
  insetBus(
    bus,
    projectStore.componentId,
    qtxmCommon.getComId(props.pageType),
    async data => {
      if (data.name === 'insert') {
        operate('insert', {});
      }
      if (data.name === 'insert-op') {
        if (!data.activeKind) {
          tabBtnIsValid(data.options, childTable.value);
        } else {
          data.activeKind === '01'
            ? operate('insertData', $table.getCurrentRecord())
            : operate('insertTitle', $table.getCurrentRecord());
        }
      }
      if (data.name === 'feeExcel') message.info('功能建设中...');
    }
  );
};

onMounted(() => {
  window.addEventListener('keydown', keyDownOperate);
  nextTick(() => {
    getTableData('', true);
    $table = childTable.value;
    insertOperate();
  });
});
const keyDownOperate = event => {
  let select = $table && $table.getCurrentRecord();
  if (
    !select ||
    !qtxmCommon.isOtherProChild(props.pageType) ||
    projectStore.tabSelectName !== '其他项目'
  )
    return;
  let selectRow = $table.getCurrentRecord();
  if (event.ctrlKey && event.code == 'KeyC') {
    copyOperate(selectRow, $table);
    message.success('复制成功');
  }
  if (event.ctrlKey && event.code == 'KeyV') {
    // console.log(
    //   'asideTitle',
    //   projectStore.otherProCopyInfo.asideTitle,
    //   props.pageType
    // );
    //判断存储数据是否是当前页面可粘贴数据
    if (projectStore.otherProCopyInfo.asideTitle !== props.pageType) return;
    if (hasDataType()) {
      //有标题行和数据行的表格
      let copyDataType =
        projectStore.otherProCopyInfo &&
        projectStore.otherProCopyInfo.copyInfo &&
        projectStore.otherProCopyInfo.copyInfo[0]?.dataType;
      let flag = true;
      if (
        !projectStore.otherProCopyInfo ||
        (copyDataType === 1 && projectStore.dataType === 2)
      ) {
        flag = false;
      }
      if (flag) {
        operate('paste', projectStore.otherProCopyInfo.copyInfo);
      } else {
        message.info('选中行不可粘贴');
      }
    } else {
      getCurrentIndex($table, selectRow);
      let row = { ...projectStore.otherProCopyInfo.copyInfo };
      row.unitId = projectStore.currentTreeInfo?.id;
      operate('paste', row);
    }
  }
};

// 定位方法
const posRow = sequenceNbr => {
  getTableData(sequenceNbr);
};

defineExpose({
  posRow,
});
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
