/*
 * @Author: wangqiaoxin <EMAIL>
 * @Date: 2025-04-11 14:01:54
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-11 14:14:39
 * @Description: 这是默认设置
 */

/**
 * 输入框失焦时格式化数值
 * - 去除前导零(如 "05" → "5"，"-005" → "-5")
 * - 去除小数末尾零(如 "1.2300" → "1.23"，"5.0000" → "5")
 * - 非法多小数点返回 "0.00"(如 "1.2.3")
 * - 自动处理末位小数点(如 "123." → "123")
 * - 保留原始小数精度
 * @param {string} value - 输入值(允许数字/负号/小数点)
 * @returns {string} 格式化后的数值字符串，非法输入返回"0.00"
 */
export const formatNumberOnBlur = (value) => {
  // 1. 空值处理
  if (value === '') return value;
  // 2. 格式校验（允许负号、数字、单个小数点）
  const isValid = /^-?\d*\.?\d*$/.test(value);
  const dotCount = (value.match(/\./g) || []).length;
  if (!isValid || dotCount > 1) return '0.00';
  // 3. 提取符号
  const isNegative = value.startsWith('-');
  const numStr = isNegative ? value.slice(1) : value;
  // 4. 分割整数和小数部分
  let [integerPart, decimalPart] = numStr.split('.');
  integerPart = integerPart || '0'; // 处理 ".45" -> "0.45"
  // 5. 处理整数部分前导零
  integerPart = integerPart.replace(/^0+/, '') || '0';
  // 6. 处理小数末尾零（核心修改点）
  if (decimalPart !== undefined) {
    decimalPart = decimalPart
      .replace(/0+$/, '') // 移除末尾零
      .replace(/^0+/, '0'); // 处理全零情况（如 "0000" -> "0"）
    // 若处理后小数部分为空或全零，则移除小数点
    if (decimalPart === '' || /^0+$/.test(decimalPart)) {
      decimalPart = undefined;
    }
  }
  // 7. 组合结果
  let result = isNegative ? '-' : '';
  result += integerPart;
  if (decimalPart !== undefined) {
    result += `.${decimalPart}`;
  }
  // 8. 全零特判（如 "-0000.000" -> "0"）
  const isAllZero =
    integerPart === '0' && (decimalPart === undefined || decimalPart === '0');
  return isAllZero ? '0' : result;
}



/**
 * 校验输入是否为数字或者四则运算
 * @param {string} inputValue - 输入值
 * @returns {boolean} - 是否合法
 */
export function validateQuantityExpression(newValue) {
  function replaceChineseBrackets(str) {
    return str
      .replace(/[（（]/g, '(')
      .replace(/[））]/g, ')')
      .replace(/×/g, '*')
      .replace(/÷/g, '/');
  }

  let inputValue = replaceChineseBrackets(newValue);
  if (/[^0-9a-zA-Z_|\-|\*|\+|\/|\.|(|)|（|）]/g.test(inputValue)) {
    return false;
  }

  // 扩展校验
  return (
    checkBrackets(inputValue) && // 括号对称性（确保checkBrackets处理英文括号）
    !/(\.\d*\.)/.test(inputValue) && // 禁止多个小数点，允许如0.5或.5（若允许）
    !/[+\-*/]{2,}/.test(inputValue) && // 连续运算符检查
    !/^[+*/]/.test(inputValue) && // 禁止以+、*、/开头
    !/[+\-*/]$/.test(inputValue) && // 禁止以运算符结尾
    !/(^\..*\.)|(^\.$)/.test(inputValue)
  ); // 禁止多个点或以单个点结尾
}


function checkBrackets(str) {
  const stack = [];
  const brackets = { '(': ')' }; // 定义括号的匹配关系

  for (const char of str) {
    if (char === '(') {
      stack.push(char); // 遇到左括号入栈
    } else if (char === ')') {
      if (stack.length === 0 || brackets[stack.pop()] !== char) {
        return false; // 栈空或括号不匹配时返回失败
      }
    }
  }

  return stack.length === 0; // 栈空表示所有括号匹配
}