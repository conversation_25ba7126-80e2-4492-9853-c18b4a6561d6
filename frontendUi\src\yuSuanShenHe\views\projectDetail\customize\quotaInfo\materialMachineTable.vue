<!--
 * @Descripttion: 人材机明细
 * @Author: liuxia
 * @Date: 2023-05-29 11:39:52XS
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-02 14:27:42
-->
<template>
  <div class="material-machine-content">
    <div
      class="head"
      v-if="
        isNotCostDe &&
        (props.currentInfo?.bdCode || props.currentInfo?.fxCode) &&
        props.currentInfo?.levelMark != 0 &&
        !(props.currentInfo.rcjFlag === 1)
      "
    >
      <a-button
        type="text"
        @click="addRcjData"
      ><icon-font type="icon-biaodan-charu"></icon-font>插入</a-button>
      <a-button
        type="text"
        @click="bcRcjData"
      ><icon-font type="icon-biaodan-charu"></icon-font>补充</a-button>
      <a-button
        type="text"
        @click="deleteType"
      ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button>
    </div>
    <div class="content">
      <vxe-table
        ref="vexTable"
        keep-source
        :data="vexTableData"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        :tree-config="{
          children: 'rcjDetailsDTOs',
          expandAll: true,
        }"
        height="auto"
        @current-change="currentChangeEvent"
        @edit-closed="editClosedEvent"
        @cell-dblclick="cellDBLClickEvent"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, tableCellClick, ['materialCode']);
          }
        "
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: cellBeforeEditMethod,
        }"
        class="table-scrollbar table-edit-common"
        :cell-class-name="cellClassName"
        :row-class-name="rowClassName"
        :menu-config="contextmenuList"
        @menu-click="onContextMenuClick"
        :scroll-y="{ enabled: true, gt: 0 }"
      >
        <template v-if="props.currentInfo?.kind === '04'">
          <vxe-column
            field=""
            width="50"
            title=""
          >
            <template #default="{ row }">
              <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
                shChangeLabel(row.ysshSysj?.change).label
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            width="50"
            field="sortNo"
          > </vxe-column>
          <vxe-column
            tree-node
            title="编码"
            field="materialCode"
            width="120"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <div>{{ row.materialCode }}</div>
            </template>
            <template #edit="{ row }">
              <vxe-input
                v-model="row.materialCode"
                v-if="isNotCostDe"
                placeholder="请输入材料编码"
              ></vxe-input>
              <span v-else>{{ row.materialCode }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="类别"
            field="type"
            width="100"
            :edit-render="{}"
          >
            <template #default="{ row }">
              <span>{{ row.type }}</span>
            </template>
            <template #edit="{ row }">
              <vxe-select
                v-model="row.type"
                transfer
                v-if="
                  isNotCostDe &&
                  (row.kind === 2 || row.kind === 5 || row.kind === 4) &&
                  !isProportionalMaterials &&
                  !isChangeAva(row)
                "
              >
                <vxe-option
                  v-for="item in typeList"
                  :key="item.value"
                  :value="item.name"
                  :label="item.name"
                ></vxe-option>
              </vxe-select>
              <template v-else>{{ row.type }}</template>
            </template>
          </vxe-column>
          <vxe-column
            title="名称"
            field="materialName"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.materialName }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                :maxlength="50"
                v-model="row.materialName"
                v-if="isNotCostDe"
              />
              <span v-else>{{ row.materialName }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="规格型号"
            field="specification"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.specification }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                :maxlength="50"
                v-model="row.specification"
                v-if="isNotCostDe"
              />
              <span v-else>{{ row.specification }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="单位"
            field="unit"
            width="70"
            :edit-render="{}"
          >
            <template #default="{ row }">
              <span>{{ row.unit }}</span>
            </template>
            <template #edit="{ row }">
              <vxeTableEditSelect
                v-if="isNotCostDe"
                :transfer="true"
                :filedValue="row.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    row.unit = newValue;
                  }
                "
              ></vxeTableEditSelect>
              <span v-else>{{ row.unit }}</span>
            </template>
          </vxe-column>
          <vxe-colgroup title="标准定额">
            <vxe-column
              title="含量"
              field="initResQty"
              width="100"
            >
              <template #default="{ row }">
                <span>{{ row.initResQty }}</span>
              </template>
            </vxe-column>
            <vxe-column
              title="预算价"
              field="dePrice"
              width="100"
            >
              <template #default="{ row }">
                <span>{{ row.dePrice }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup title="送审">
            <vxe-column
              title="含量"
              field="resQty"
              width="100"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.resQty }}</span>
              </template>
            </vxe-column>
            <vxe-column
              title="市场价"
              field="marketPrice"
              width="100"
            >
              <template #default="{ row }">
                <span>{{ row.ysshSysj?.marketPrice }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup title="审定">
            <vxe-column
              title="含量"
              field="resQty"
              width="100"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
            >
              <template #default="{ row }">
                <span>{{ row.resQty }}</span>
              </template>

              <template #edit="{ row }">
                <vxe-input
                  v-model="row.resQty"
                  v-if="isNotCostDe"
                  @keyup="
                    row.resQty = (row.resQty.match(
                      /-?\d{0,8}(\.\d{0,6}|100)?/
                    ) || [''])[0]
                  "
                  @blur="row.resQty = row.resQty * 1 + ''"
                />
                <span v-else>{{ row.resQty }}</span>
              </template>
            </vxe-column>
            <vxe-column
              title="市场价"
              field="marketPrice"
              width="100"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
            >
              <template #default="{ row }">
                <span>{{ row.marketPrice }}</span>
              </template>
              <template #edit="{ row }">
                <vxe-input
                  v-model="row.marketPrice"
                  v-if="
                    props.currentInfo.kind === '04' &&
                    (!props.currentInfo?.isCostDe ||
                      props.currentInfo.isCostDe === 4) &&
                    row.ifLockStandardPrice !== 1 &&
                    (row.levelMark === 0 ||
                      !row.rcjDetailsDTOs ||
                      (row.levelMark !== 0 &&
                        row.rcjDetailsDTOs.length === 0)) &&
                    row.supplementDeRcjFlag !== 1
                  "
                  @keyup="
                    row.marketPrice = (row.marketPrice.match(
                      /\d{0,8}(\.\d{0,2}|100)?/
                    ) || [''])[0]
                  "
                />
                <span v-else>{{ row.marketPrice }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-column
            title="合计数量"
            field="totalNumber"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.totalNumber }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.totalNumber"
                v-if="isNotCostDe && isPartEdit"
                @keyup="
                  row.totalNumber = (row.totalNumber.match(
                    /\d{0,8}(\.\d{0,4}|100)?/
                  ) || [''])[0]
                "
                @blur="row.totalNumber = row.totalNumber * 1 + ''"
              />
              <span v-else>{{ row.totalNumber }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="合价"
            field="total"
            width="100"
          ></vxe-column>
          <vxe-column
            title="是否暂估"
            field="ifProvisionalEstimate"
            width="100"
          >
            <template #default="{ row }">
              <vxe-checkbox
                v-if="
                  isNotCostDe &&
                  (!row.rcjDetailsDTOs || +row.levelMark === 0) &&
                  !isChangeAva(row)
                "
                v-model="row.ifProvisionalEstimate"
                :checked-value="1"
                :unchecked-value="0"
                @change="updateStatus(row)"
              >
              </vxe-checkbox>
              <vxe-checkbox
                v-else
                :disabled="true"
                v-model="row.ifProvisionalEstimate"
                :checked-value="1"
                :unchecked-value="0"
                @change="updateStatus(row)"
              >
              </vxe-checkbox>
            </template>
          </vxe-column>
          <!--      <vxe-column title="是否锁定工程量" field="age" width="100">-->
          <!--        <template #default="{ row }">-->
          <!--          <a-checkbox></a-checkbox>-->
          <!--        </template>-->
          <!--      </vxe-column>-->
          <vxe-column
            title="原始含量"
            field="initResQty"
            width="100"
          ></vxe-column>
          <vxe-column
            title="价格来源"
            field="sourcePrice"
            width="100"
          ></vxe-column>
        </template>
        <template v-else>
          <vxe-column
            width="50"
            field="sortNo"
          > </vxe-column>
          <vxe-column
            tree-node
            title="编码"
            field="materialCode"
            width="120"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <div>{{ row.materialCode }}</div>
            </template>
            <template #edit="{ row }">
              <vxe-input
                v-model="row.materialCode"
                v-if="isNotCostDe"
                placeholder="请输入材料编码"
              ></vxe-input>
              <span v-else>{{ row.materialCode }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="类别"
            field="type"
            width="100"
            :edit-render="{}"
          >
            <template #default="{ row }">
              <span>{{ row.type }}</span>
            </template>
            <template #edit="{ row }">
              <vxe-select
                v-model="row.type"
                transfer
                v-if="
                  isNotCostDe &&
                  (row.kind === 2 || row.kind === 5 || row.kind === 4) &&
                  !isProportionalMaterials &&
                  !isChangeAva(row)
                "
              >
                <vxe-option
                  v-for="item in typeList"
                  :key="item.value"
                  :value="item.name"
                  :label="item.name"
                ></vxe-option>
              </vxe-select>
              <template v-else>{{ row.type }}</template>
            </template>
          </vxe-column>
          <vxe-column
            title="名称"
            field="materialName"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.materialName }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                :maxlength="50"
                v-model="row.materialName"
                v-if="isNotCostDe"
              />
              <span v-else>{{ row.materialName }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="规格型号"
            field="specification"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.specification }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                :maxlength="50"
                v-model="row.specification"
                v-if="isNotCostDe"
              />
              <span v-else>{{ row.specification }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="单位"
            field="unit"
            width="70"
            :edit-render="{}"
          >
            <template #default="{ row }">
              <span>{{ row.unit }}</span>
            </template>
            <template #edit="{ row }">
              <vxeTableEditSelect
                v-if="isNotCostDe"
                :transfer="true"
                :filedValue="row.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    row.unit = newValue;
                  }
                "
              ></vxeTableEditSelect>
              <span v-else>{{ row.unit }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="消耗量"
            field="resQty"
            width="90"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
            v-if="props.currentInfo?.kind === '04'"
          >
            <template #default="{ row }">
              <span>{{ row.resQty }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.resQty"
                v-if="isNotCostDe"
                @keyup="
                  row.resQty = (row.resQty.match(
                    /-?\d{0,8}(\.\d{0,6}|100)?/
                  ) || [''])[0]
                "
                @blur="row.resQty = row.resQty * 1 + ''"
              />
              <span v-else>{{ row.resQty }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="合计数量"
            field="totalNumber"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.totalNumber }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.totalNumber"
                v-if="isNotCostDe && isPartEdit"
                @keyup="
                  row.totalNumber = (row.totalNumber.match(
                    /\d{0,8}(\.\d{0,4}|100)?/
                  ) || [''])[0]
                "
                @blur="row.totalNumber = row.totalNumber * 1 + ''"
              />
              <span v-else>{{ row.totalNumber }}</span>
            </template>
          </vxe-column>
          <!-- showDePrice更改为dePrice -->
          <vxe-column
            :title="
              projectStore.deType === '12'
                ? '定额价'
                : projectStore.taxMade === 1
                ? '不含税基期价'
                : '含税基期价'
            "
            field="dePrice"
            width="110"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.dePrice }}</span>
            </template>
          </vxe-column>
          <!-- showMarketPrice更改为marketPrice -->
          <vxe-column
            :title="
              projectStore.deType === '12'
                ? '市场价'
                : projectStore.taxMade === 1
                ? '不含税市场价'
                : '含税市场价'
            "
            field="marketPrice"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.marketPrice }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.marketPrice"
                v-if="
                  isNotCostDe &&
                  row.ifLockStandardPrice !== 1 &&
                  (row.levelMark === 0 ||
                    !row.rcjDetailsDTOs ||
                    (row.levelMark !== 0 && row.rcjDetailsDTOs.length === 0)) &&
                  isPartEdit &&
                  !isChangeAva(row)
                "
                @blur="row.marketPrice = pureNumber(row.marketPrice, 2)"
              />
              <span v-else>{{ isChangeAva(row) ? '-' : row.marketPrice }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="合价"
            field="total"
            width="100"
          ></vxe-column>
          <vxe-column
            title="是否暂估"
            field="ifProvisionalEstimate"
            width="100"
          >
            <template #default="{ row }">
              <vxe-checkbox
                v-if="
                  isNotCostDe &&
                  (!row.rcjDetailsDTOs || +row.levelMark === 0) &&
                  !isChangeAva(row)
                "
                v-model="row.ifProvisionalEstimate"
                :checked-value="1"
                :unchecked-value="0"
                @change="updateStatus(row)"
              >
              </vxe-checkbox>
              <vxe-checkbox
                v-else
                :disabled="true"
                v-model="row.ifProvisionalEstimate"
                :checked-value="1"
                :unchecked-value="0"
                @change="updateStatus(row)"
              >
              </vxe-checkbox>
            </template>
          </vxe-column>
          <!--      <vxe-column title="是否锁定工程量" field="age" width="100">-->
          <!--        <template #default="{ row }">-->
          <!--          <a-checkbox></a-checkbox>-->
          <!--        </template>-->
          <!--      </vxe-column>-->
          <vxe-column
            title="原始含量"
            field="initResQty"
            width="100"
          ></vxe-column>
          <vxe-column
            title="价格来源"
            field="sourcePrice"
            width="100"
          ></vxe-column>
        </template>
      </vxe-table>
      <de-description-info
        :currentInfo="props.currentInfo"
        v-if="props.currentInfo?.kind === '04'"
      ></de-description-info>
      <description-info
        :currentInfo="props.currentInfo"
        v-if="props.currentInfo?.kind === '03'"
        :type="props.type"
      ></description-info>
    </div>
  </div>
  <bcRcj
    v-model:visible="rcjVisible"
    :code="bdCode"
    :materialInfo="currentInfo"
    :unitList="projectStore.unitListString"
    @bcCancel="rcjCancel"
    @rcjSaveData="rcjSaveData"
  ></bcRcj>

  <inquiryPopup
    v-if="priceVisible"
    :info="currentInfo"
    @closeDialog="closeInquiryPopup"
  ></inquiryPopup>
</template>

<script setup>
import {
  defineAsyncComponent,
  onActivated,
  onMounted,
  nextTick,
  ref,
  watch,
  reactive,
  computed,
  watchEffect,
} from 'vue';
import api from '@/api/projectDetail';
import feePro from '@/api/feePro';
import DescriptionInfo from '@/views/projectDetail/customize/descriptionInfo/index.vue';
import DeDescriptionInfo from '@/views/projectDetail/customize/deDescriptionInfo/index.vue';
import { projectDetailStore } from '@/store/projectDetail';
import infoMode from '@/plugins/infoMode';
import { message } from 'ant-design-vue';
import { useCellClick } from '@/hooks/useCellClick';
import { disposeDeTypeData } from '@/hooks/publicApiData';
import { pureNumber, shChangeLabel } from '@/utils/index';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
let vexTableData = ref([]); //表格数据
const props = defineProps([
  'tableData',
  'currentInfo',
  'type',
  'currentMaterialInfo',
  'RefreshList',
]);
/**
 * 是否配比材料
 */
const isProportionalMaterials = computed(() => {
  const { rcjFlag, type } = props.currentInfo || {};
  return rcjFlag === 1 && ['砼', '商砼', '浆', '商浆', '配比'].includes(type);
});

/**
 * 是否费用定额
 * 12的垂运，22的装饰超高、垂运不属于费用定额
 * isCostDe是否是费用定额 0不是  1 安文费 2 总价措施 3 超高 4 垂运 5 安装费'
 */
const isNotCostDe = computed(() => {
  const { kind, isCostDe } = props.currentInfo || {};
  return (
    kind === '04' &&
    (!isCostDe ||
      isCostDe === 4 ||
      (projectStore.deType === '22' && props.currentInfo.isCostDe === 3))
  );
});
const inquiryPopup = defineAsyncComponent(() =>
  import('@/components/inquiryPopup/index.vue')
);

const priceVisible = ref(false); //智能询价弹窗

const emits = defineEmits(['cellDBLClickEvent', 'updateData']);

const contextmenuList = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 1,
          name: '增加材料',
          visible: true,
          disabled: false,
        },
        {
          code: 2,
          name: '删除明细',
          visible: true,
          disabled: false,
        },
        {
          code: 3,
          name: '智能询价',
          visible: true,
          disabled: false,
        },
        {
          code: 4,
          name: '临时删除',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod: ({ options, column, columnIndex, row, rowIndex }) => {
    console.log('🚀 ~ row:', row, options);
    let tar = contextmenuList.body.options[0].find(a => a.name === '智能询价');
    if (
      ((row.levelMark === 1 || row.levelMark === 2) && row.markSum === 1) ||
      row.unit === '元' ||
      row.unit === '%'
    ) {
      tar.disabled = true;
      //单位为元、单位为%、补充人材机、已锁定市场价、已勾选是否汇总/二次解析的父级材料 都不可以智能询价  这些材料都不可以智能询价
    } else {
      tar.disabled = false;
    }

    if (row.libraryCode?.startsWith('2022')) {
      contextmenuList.body.options[0][2].visible = false;
    }

    options.forEach(list => {
      list.forEach(async (item, index) => {
        console.log('row', row);

        if (item.code === 4) {
          let parentInfo = vexTable.value.getParentRow(row);
          if (
            props.currentInfo.tempDeleteFlag ||
            (parentInfo?.tempDeleteFlag && !row.rcjDetailsDTOs)
          ) {
            item.disabled = true;
          } else {
            item.disabled = false;
          }
          if (row.tempDeleteFlag) {
            item.name = '取消临时删除';
          } else {
            item.name = '临时删除';
          }
        }
      });
    });
    return isNotCostDe.value;
  },
});

// 基期价、市场价为“-
const isChangeAva = row => {
  return projectStore.deType === '22' && Number(row.isChangeAva) === 0;
};
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备',
    value: 4,
  },
]);

const vexTable = ref();
const currentInfo = ref();
const projectStore = projectDetailStore();
let rcjVisible = ref(false);
let bdCode = ref('');
let unitList = ref([]);

const bcRcj = defineAsyncComponent(() =>
  import('@/views/projectDetail/customize/subItemProject/components/bcRcj.vue')
);

onMounted(() => {
  currentInfo.value = null;
  // console.log('vexTableData.value', vexTableData.value, props.tableData);
  // queryUnit();
});

watchEffect(() => {
  if (props.tableData?.length) {
    vexTableData.value = disposeDeTypeData(props.tableData, true, true);
    setTimeout(() => {
      currentInfo.value = vexTableData.value[0];
      vexTable.value?.setCurrentRow(currentInfo.value);
    }, 10);
  }
});

onActivated(() => {
  currentInfo.value = null;
  getFeePolicyDocData();
});

/**
 * 关闭了智能询价
 * @param {*} v  true 点击了确定按钮  false 点击了取消按钮
 */
const closeInquiryPopup = v => {
  priceVisible.value = false;
  if (v) {
    props.RefreshList();
  }
};

watch(
  () => props.currentInfo?.kind,
  () => {
    nextTick(() => {
      if (vexTable.value) {
        const $table = vexTable.value;
        if (props.currentInfo?.kind !== '04') {
          $table.hideColumn('resQty');
          $table.hideColumn('initResQty');
          $table.hideColumn('sourcePrice');
        } else {
          $table.showColumn('resQty');
          $table.showColumn('initResQty');
          $table.showColumn('sourcePrice');
        }
      }
    });
  },
  { deep: true }
);

watch(
  () => props.currentMaterialInfo,
  val => {
    if (!val) return;
    vexTable.value.setCurrentRow(props.currentMaterialInfo);
    vexTable.value.scrollToRow(props.currentMaterialInfo);
    nextTick(() => {
      currentInfo.value = vexTable.value.getRowById(
        props.currentMaterialInfo?.sequenceNbr
      );
    });
  }
);
watch(
  () => props.tableData,
  () => {
    vexTableData.value = disposeDeTypeData(props.tableData, true, true);

    // console.log('vexTableData.value', vexTableData.value, props.tableData);
  }
);
const rcjCancel = () => {
  const $table = vexTable.value;
  $table.revertData(currentInfo.value, 'materialCode');
  rcjVisible.value = false;
};

// ['QTCLFBFB','34000001-2','J00004','J00031','J00031','C11384','C00007','C000200'] 不能编辑
const isPartEdit = computed(() => {
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});

const cellDBLClickEvent = ({ row, column }) => {
  console.log('单元格双击', row);
  const $table = vexTable.value;
  let parentData = $table.getParentRow(row);
  console.log('parentData', parentData);
  row.parentId = parentData?.sequenceNbr;
  if (column.field === 'materialCode' && isNotCostDe.value) {
    emits('cellDBLClickEvent', row);
  }
};

const tableCellClick = ({ row }) => {
  if (row.tempDeleteFlag || row.resQty === null || row.resQty === undefined)
    return false;
  return true;
};

const cellClassName = ({ row, column, $columnIndex }) => {
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (props.currentInfo?.kind === '04' && column.field === 'marketPrice') {
    if (Number(row.marketPrice) !== Number(row.dePrice)) {
      return 'price-color ' + selectName;
    }
  } else if (
    props.currentInfo?.kind === '04' &&
    column.field === 'resQty' &&
    Number(row.resQty) !== row.initResQty
  ) {
    return 'resQty-color ' + selectName;
  } else if (
    row.kind === 5 &&
    (column.field === 'materialCode' ||
      column.field === 'materialName' ||
      column.field === 'type' ||
      column.field === 'specification')
  ) {
    return 'mainMaterial-color ' + selectName;
  }
  return selectName;
};

const rowClassName = ({ row }) => {
  let ClassStr = shChangeLabel({ row: row });
  if (props.currentInfo?.kind !== '04') {
    ClassStr += ' disable-bg';
  }
  if (row.tempDeleteFlag) {
    ClassStr += ' temp-delete';
  } else {
    ClassStr += ' normal-info';
  }
  return ClassStr;
};

const onContextMenuClick = ({ menu, row }) => {
  if (!row) {
    message.warning('请先选择要材料');
    return;
  }
  currentInfo.value = row;
  console.log(
    '🚀 ~ onContextMenuClick ~ currentInfo.value:',
    currentInfo.value
  );
  const value = menu.code;
  switch (value) {
    case 1:
      const $table = vexTable.value;
      let parentData = $table.getParentRow(row);
      console.log('parentData', parentData);
      row.parentId = parentData?.sequenceNbr;
      emits('cellDBLClickEvent', row);
      break;
    case 2:
      deleteType();
      break;
    case 3:
      priceVisible.value = true;
      break;
    case 4:
      updateDelTempStatusColl(row);
      break;
    default:
      break;
  }
};

const addRcjData = () => {
  const $table = vexTable.value;
  let parentData = $table.getParentRow(currentInfo.value);
  console.log('parentData', parentData);
  if (currentInfo.value) {
    currentInfo.value.parentId = parentData?.sequenceNbr;
  }
  emits('cellDBLClickEvent', currentInfo.value);
};

// 补充人材机
const bcRcjData = () => {
  bdCode.value = '';
  rcjVisible.value = true;
};

// 删除人材机
const deleteType = () => {
  if (!currentInfo.value) {
    message.warning('请先选择要删除的材料');
    return;
  }
  infoMode.show({
    isDelete: true,
    iconType: 'icon-querenshanchu',
    infoText: '是否确认删除?',
    descText: '是否删除当前已选中数据',
    confirm: () => {
      delDetail();
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};

// const deleteRcj = () => {
//   if (!currentInfo.value.rcjDetailsDTOs) {
//     delDetail();
//   } else {
//     delRcjData();
//   }
// };

// 删除人材机数据
const delRcjData = () => {
  console.log('currentInfo.value', currentInfo.value);
  let apiData = {
    rcjId: currentInfo.value.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    libraryCode: currentInfo.value.libraryCode,
  };

  api.delRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('删除成功');
      currentInfo.value = null;
      emits('updateData', 1);
    }
  });
};

// 删除单个配比材料数据
/**
 * 删除统一使用该方法
 */
const delDetail = (type = null) => {
  let apiData = {
    sequenceNbr: currentInfo.value.sequenceNbr,
    unitId:
      currentInfo.value.ysshSysj?.change === 2
        ? projectStore.currentTreeInfo?.ysshUnitId
        : projectStore.currentTreeInfo?.id,
    constructId:
      currentInfo.value.ysshSysj?.change === 2
        ? projectStore.currentTreeGroupInfo?.ssConstructId
        : projectStore.currentTreeGroupInfo?.constructId,
    singleId:
      currentInfo.value.ysshSysj?.change === 2
        ? projectStore.currentTreeGroupInfo?.ssSingleId
        : projectStore.currentTreeGroupInfo?.singleId,
    tempDeleteFlag: type === 1 ? !currentInfo.value.tempDeleteFlag : null,
    de: JSON.parse(JSON.stringify(props.currentInfo)),
  };
  console.log('apiData', apiData, currentInfo.value);
  api.delDetail(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200 && res.result) {
      if (type === 1) {
        if (currentInfo.value.tempDeleteFlag) {
          message.success('数据已取消临时删除');
        } else {
          message.success('数据临时删除成功');
        }
      } else {
        message.success('删除成功');
      }
      emits('updateData', 1);
    }
  });
};

const updateStatus = row => {
  updateConstructRcj(row, 'ifProvisionalEstimate');
};

const currentChangeEvent = ({ row }) => {
  const $table = vexTable.value;
  let parentData = $table.getParentRow(row);
  row.parentId = parentData?.sequenceNbr;
  console.log('选中当前行数据', row);
  currentInfo.value = row;
};

// 单元格退出编辑事件
const editClosedEvent = async ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  if ($table.isUpdateByRow(row, field)) {
    if (!row.materialCode) {
      currentInfo.value.materialCode = currentInfo.value.originalMaterialCode;
      return;
    }
    if (!(await isEditRenGongMarketPrice(field, row))) {
      $table.revertData(row, field);
      return;
    }
    if (field === 'marketPrice') {
      row.marketPrice = Number(row.marketPrice);
    }
    updateConstructRcj(row, field);
    isRcjCodeMainQuotaLibrary(field, row.materialCode);
  }
};

// 是否修改人工1、2、3类工市场价
const isEditRenGongMarketPrice = (field, row) => {
  return new Promise(resolve => {
    if (
      ['marketPrice'].includes(field) &&
      isSelectFeePolicyDoc.value &&
      renGongCodeList.includes(row.materialCode)
    ) {
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText: '该市场价已受政策文件调整，是否确认修改',
        isFunction: false,
        confirm: () => {
          resolve(true);
          infoMode.hide();
        },
        close: () => {
          resolve(false);
          infoMode.hide();
        },
      });
    } else {
      resolve(true);
    }
  });
};
// 一、二、三类工
const renGongCodeList = [
  '10000001',
  '10000002',
  '10000003',
  'JXPB-005',
  'R00001',
];
/**
 * 获取是否勾选政策文件
 */
const isSelectFeePolicyDoc = ref(false);
const getFeePolicyDocData = () => {
  const isProject = projectStore.currentTreeInfo.levelType === 1;
  let apiData = {
    type: isProject ? 1 : 2,
    constructId: isProject
      ? projectStore.currentTreeInfo?.id
      : projectStore.currentTreeGroupInfo?.constructId,
  };
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  feePro.getPolicyDocument(apiData).then(res => {
    console.log('政策文件', apiData, res);
    if (res.status === 200) {
      const { rgfId, awfId, gfId } = res.result;
      isSelectFeePolicyDoc.value = rgfId;
    }
  });
};
const updateConstructRcj = (row, field) => {
  projectStore.SET_GLOBAL_LOADING({
    loading: true,
    info: '设置中，请稍后...',
  });
  console.log('修改数据');
  if (field === 'materialCode') return;
  let value;
  if (field === 'type') {
    value = row[field] === '材料费' ? 2 : row[field] === '主材费' ? 5 : 4;
  }

  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    sequenceNbr: row.sequenceNbr,
    type: !row.rcjDetailsDTOs ? 2 : 1,
    libraryCode: row.libraryCode,
    constructProjectRcj: {
      [field === 'type' ? 'kind' : field]:
        field === 'type' ? value : row[field],
    },
  };
  console.log('修改人材机明细数据参数', apiData);
  api
    .updateConstructRcj(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        console.log('res', res);
        message.success('修改成功');
        emits('updateData', 1);
      }
    })
    .finally(() => {
      projectStore.SET_GLOBAL_LOADING({
        loading: false,
        info: '设置中，请稍后...',
      });
    });
};

// 判断输入的材料编码是否与主定额库编码相同
const isRcjCodeMainQuotaLibrary = (field, code) => {
  if (field !== 'materialCode') return;
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
  };
  api.isRcjCodeMainQuotaLibrary(apiData).then(res => {
    console.log('难道是这个么', res);
    if (res.status === 200) {
      if (res.result) {
        // 输入的编码为主定额库编码
        if (
          (!currentInfo.value.rcjDetailsDTOs && res.result.levelMark === 0) ||
          currentInfo.value.rcjDetailsDTOs
        ) {
          updateBjqRcjReplaceData(code);
        } else {
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-querenshanchu',
            infoText: '配合比材料下不允许增加配合比材料',
            confirm: () => {
              infoMode.hide();
              currentInfo.value.materialCode =
                currentInfo.value.originalMaterialCode;
            },
          });
        }
      } else {
        isStandardRcj(code);
      }
    }
  });
};

// 判断输入的材料编码是否标准人材机数据
const isStandardRcj = code => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
  };
  api.isStandardRcj(apiData).then(res => {
    console.log('================人材机是否是标准数据', res);
    if (res.status === 200) {
      if (res.result) {
        if (
          (!currentInfo.value.rcjDetailsDTOs && res.result.levelMark === 0) ||
          currentInfo.value.rcjDetailsDTOs
        ) {
          updateBjqRcjReplaceData(res.result.materialCode);
        } else {
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-querenshanchu',
            infoText: '配合比材料下不允许增加配合比材料',
            confirm: () => {
              infoMode.hide();
              currentInfo.value.materialCode =
                currentInfo.value.originalMaterialCode;
            },
          });
        }
      } else {
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: '主定额库不存在该材料编码,是否补充人材机？',
          confirm: () => {
            rcjVisible.value = true;
            bdCode.value = code;
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            currentInfo.value.materialCode =
              currentInfo.value.originalMaterialCode;
          },
        });
      }
    }
  });
};

// 分部分项 措施项目 替换编辑区的人材机数据
const updateBjqRcjReplaceData = code => {
  let apiData = {
    unitId:
      currentInfo.value.ysshSysj?.change === 2
        ? projectStore.currentTreeInfo?.ysshUnitId
        : projectStore.currentTreeInfo?.id,
    constructId:
      currentInfo.value.ysshSysj?.change === 2
        ? projectStore.currentTreeGroupInfo?.ssConstructId
        : projectStore.currentTreeGroupInfo?.constructId,
    singleId:
      currentInfo.value.ysshSysj?.change === 2
        ? projectStore.currentTreeGroupInfo?.ssSingleId
        : projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    code: code,
    region: 1,
  };
  console.log('明细区替换人材机', apiData);
  api.updateBjqRcjReplaceData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('人材机替换成功');
      rcjVisible.value = false;
      emits('updateData', 1);
    }
  });
};

// 分部分项 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    region: 1,
  };
  api.spRcjByPage(apiData).then(res => {
    console.log('1111111111', res);
    message.success('人材机替换成功');
    rcjVisible.value = false;
    emits('updateData', 1);
  });
};

// 分部分项 措施项目 添加编辑区的人材机数据
const addMxqBcRcjData = inputData => {
  inputData.isSupplement = 1;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: currentInfo.value
      ? JSON.parse(JSON.stringify(currentInfo.value))
      : null,
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: props.type,
    region: 1,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    deItemId: props.currentInfo.sequenceNbr,
  };
  console.log('新增参数', apiData);
  api.supplementRcjDetail(apiData).then(res => {
    console.log('1111111111', res);
    if (res.status === 200 && res.result) {
      message.success('人材机新建成功');
      rcjVisible.value = false;
      emits('updateData', 1);
    }
  });
};

const rcjSaveData = inputData => {
  if (bdCode.value) {
    spRcjByPage(inputData);
  } else {
    addMxqBcRcjData(inputData);
  }
};

const currentMaterialInfo = sequenceNbr => {
  if (props.tableData?.length > 0) {
    currentInfo.value = { sequenceNbr };
    const $table = vexTable.value;
    $table.setCurrentRow(currentInfo.value);
    projectStore.isAutoPosition = false;
    nextTick(() => {
      currentInfo.value = $table.getRowById(sequenceNbr);
      console.log('$table.getCurrentRow()', $table.getRowById(sequenceNbr));
    });
    // props.tableData.forEach(item => {
    //   if (item.sequenceNbr === sequenceNbr) {
    //     currentInfo.value = item;
    //   }
    //   item?.rcjDetailsDTOs?.forEach(child => {
    //     if (child.sequenceNbr === sequenceNbr) {
    //       currentInfo.value = child;
    //     }
    //   });
    // });
    console.log(
      'currentInfo.value人材机跳转',
      props.tableData,
      currentInfo.value
    );
  } else {
    setTimeout(() => {
      currentMaterialInfo(sequenceNbr);
    }, 1000);
  }
};
// 临时删除
const updateDelTempStatusColl = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    idList: [row.sequenceNbr],
    modelType: props.type,
    dataType: 3,
    tempDeleteFlag: !currentInfo.value.tempDeleteFlag,
  };
  if (!row.rcjDetailsDTOs) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '子级材料不允许临时删除',
      confirm: () => {
        infoMode.hide();
      },
    });
    return;
  }
  console.log('临时删除参数', apiData);
  delDetail(1);
};

defineExpose({
  currentMaterialInfo,
  vexTable,
});
</script>

<style lang="scss" scoped>
.material-machine-content {
  width: 100%;
  //display: flex;
  //flex-direction: column;
  //justify-content: space-between;
  height: 100%;
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: calc(100% - 40px);
  }
  :deep(.vxe-table) {
    width: 65%;
  }
  ::v-deep(
      .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table .normal-info .price-color) {
    color: #de3f3f;
  }
  ::v-deep(.vxe-table .normal-info .resQty-color) {
    color: #287cfa;
  }
  ::v-deep(.vxe-table .normal-info .mainMaterial-color) {
    color: #245299;
  }
  ::v-deep(.vxe-table .disable-bg) {
    background: #f3f2f3;
    color: #a7a7a7;
  }
  ::v-deep(.vxe-table .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    text-decoration: line-through;
  }
}
.delete-dialog {
  background: #67c23a;
  :deep(.ant-modal-content) {
    background: red;
    :deep(.ant-modal-header) {
      padding: 0;
    }
    :deep(.ant-modal-close-x) {
      color: #ffffff;
    }
    :deep(.ant-modal-footer) {
      text-align: center;
    }
  }
}
</style>
