<!--
 * @Descripttion: 
 * @Author: kongweiqiang
 * @Date: 2024-03-11 19:29:20
 * @LastEditors: renmingming
 * @LastEditTime: 2024-06-26 17:59:56
-->
<template>
  <common-modal
    v-model:modelValue="show"
    className="dialog-comm"
    :title="title"
    :width="1200"
    height="auto"
    @close="close"
  >
    <div class="head-action">
      <vxe-table
        border
        height="500"
        :column-config="{ resizable: true }"
        :scroll-y="{ enabled: false }"
        :span-method="mergeRowMethod"
        :data="tableData"
      >
        <vxe-column
          field="ysshAssociation.name"
          title="关联"
          fixed="left"
          width="120"
        ></vxe-column>
        <vxe-colgroup field="ysshSysj" title="送审">
          <vxe-column field="ysshSysj.bdCode" title="编码" width="160">
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.bdCode }}</span>
            </template>
          </vxe-column>
          <vxe-column field="ysshSysj.bdName" title="名称" width="160">
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.bdName }}</span>
            </template>
          </vxe-column>
          <vxe-column field="ysshSysj.unit" title="单位" width="80">
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.unit }}</span>
            </template>
          </vxe-column>
          <vxe-column field="ysshSysj.quantity" title="工程量" width="160">
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.quantity }}</span>
            </template>
          </vxe-column>
          <vxe-column field="ysshSysj.price" title="综合单价" width="160">
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.price }}</span>
            </template>
          </vxe-column>
          <vxe-column field="ysshSysj.total" title="综合合价" width="160">
            <template #default="{ row }">
              <span>{{ row.ysshSysj?.total }}</span>
            </template>
          </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup field="b" title="审定">
          <vxe-column field="bdCode" title="编码" width="160"> </vxe-column>
          <vxe-column field="bdName" title="名称" width="160"> </vxe-column>
          <vxe-column field="unit" title="单位" width="80"> </vxe-column>
          <vxe-column field="quantity" title="工程量" width="160"> </vxe-column>
          <vxe-column field="price" title="综合单价" width="160"> </vxe-column>
          <vxe-column field="total" title="综合合价" width="160"> </vxe-column>
        </vxe-colgroup>
      </vxe-table>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, ref, watch, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import shApi from '@/api/shApi.js';

const projectStore = projectDetailStore();
const btnLoading = ref(false);
const tableData = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
  },
  title: {
    type: String,
  },
});
const emit = defineEmits(['update:visible']);
const show = computed({
  get: () => props.visible,
  set: val => {
    console.log(val);
    if (!val) {
    }
    emit('update:visible', val);
  },
});
watch(
  () => props.visible,
  val => {
    if (val) {
      getData();
    }
  }
);
const mergeRowMethod = ({ row, _rowIndex, column, visibleData }) => {
  const fields = [
    'ysshAssociation.name',
    'ysshSysj.bdCode',
    'ysshSysj.bdName',
    'ysshSysj.unit',
    'ysshSysj.quantity',
    'ysshSysj.total',
    'ysshSysj.price',
  ];

  let cellValue = getdeepData(row, column.field);
  if (cellValue && fields.includes(column.field)) {
    const prevRow = visibleData[_rowIndex - 1];
    let nextRow = visibleData[_rowIndex + 1];
    if (
      prevRow &&
      getdeepData(prevRow, column.field) === cellValue &&
      prevRow?.ysshAssociation.name === row?.ysshAssociation.name
    ) {
      console.log(column.field, cellValue, row?.ysshAssociation.name);
      return { rowspan: 0, colspan: 0 };
    } else {
      let countRowspan = 1;
      while (
        nextRow &&
        getdeepData(nextRow, column.field) === cellValue &&
        nextRow?.ysshAssociation.name === row?.ysshAssociation.name
      ) {
        nextRow = visibleData[++countRowspan + _rowIndex];
      }
      if (countRowspan >= 1) {
        return { rowspan: countRowspan, colspan: 1 };
      }
    }
  }
  function getdeepData(row, field) {
    let arr = [];
    if (
      column.field?.indexOf('ysshSysj') > -1 ||
      column.field?.indexOf('ysshAssociation') > -1
    ) {
      arr = field.split('.');
      return row[arr[0]][arr[1]];
    } else {
      return row[field];
    }
  }
};
const getData = () => {
  // tableData.value = [
  //   {
  //     qdglName: '1',
  //     bdCode: '2',
  //     bdName: '3',
  //     price: 2,
  //     ysshSysj: {
  //       bdName: '4',
  //       price: 1,
  //     },
  //   },
  // ];
  const { constructId, singleId, ssConstructId, ssSingleId } =
    projectStore.currentTreeGroupInfo;
  const { id, ysshUnitId } = projectStore.currentTreeInfo;
  let apiData = {
    constructId,
    singleId,
    unitId: id,
    ssConstructId,
    ssSingleId,
    ssUnitId: ysshUnitId,
  };
  shApi.queryAllQdAssociationList(apiData).then(res => {
    console.log(res, 'res获取关联数据');
    tableData.value = res.result;
  });
};
const close = () => {
  show.value = false;
};
</script>
<style lang="scss" scoped>
.table {
  height: 60vh;
}
.footer {
  display: flex;
  justify-content: center;
  position: relative;
  top: 15px;
  :deep(.ant-btn) {
    margin-right: 15px;
  }
}
</style>
