<template>
  <div class="diff-price-setting">
    <common-modal
      className="dialog-comm resizeClass"
      title="价差取费设置"
      width="400"
      height="300"
      v-model:modelValue="props.visible"
      :mask="false"
    >
      <div class="range-content">
        <p class="single-item">
          <span>人工：</span>
          <vxe-select v-model="inputData.renGong" transfer>
            <vxe-option
              v-for="item in selectList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </p>
        <p class="single-item">
          <span>材料：</span>
          <vxe-select v-model="inputData.caiLiao" transfer>
            <vxe-option
              v-for="item in selectList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </p>
        <p class="single-item">
          <span>机械：</span>
          <vxe-select v-model="inputData.jiXie" transfer>
            <vxe-option
              v-for="item in selectList"
              :key="item.code"
              :value="item.code"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </p>
        <p>说明：设置完成后,将在费用汇总中记取相应费用。</p>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="sureHandle">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';

const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'updateData']);
const inputData = reactive({
  renGong: 3,
  caiLiao: 3,
  jiXie: 3,
});
const projectStore = projectDetailStore();
const selectList = reactive([
  {
    name: '记取税金',
    code: 1,
  },
  {
    name: '记取规费、税金',
    code: 2,
  },
  {
    name: '记取安文费、税金',
    code: 3,
  },
]);
const cancel = () => {
  emits('update:visible', false);
};

const sureHandle = () => {
  if (projectStore.currentTreeInfo.levelType === 1) {
    constructPriceDifferenceDeeSettingController();
  } else {
    priceDifferenceDeeSettingController();
  }
};

// 单位工程级别价差取费设置
const priceDifferenceDeeSettingController = () => {
  let obj = {
    1: inputData.renGong,
    2: inputData.caiLiao,
    3: inputData.jiXie,
  };
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    map: JSON.parse(JSON.stringify(obj)),
  };
  console.log('apiData', apiData);
  api.priceDifferenceDeeSettingController(apiData).then(res => {
    console.log('000000');
    if (res.status === 200 && res.result) {
      message.success('价差取费设置成功');
      cancel();
      emits('updateData');
    }
  });
};

// 工程项目级别价差取费设置
const constructPriceDifferenceDeeSettingController = () => {
  let obj = {
    1: inputData.renGong,
    2: inputData.caiLiao,
    3: inputData.jiXie,
  };
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    map: JSON.parse(JSON.stringify(obj)),
    kind: Number(projectStore.asideMenuCurrentInfo?.key),
  };
  console.log('apiData', apiData);
  api.constructPriceDifferenceDeeSettingController(apiData).then(res => {
    console.log('000000');
    if (res.status === 200 && res.result) {
      message.success('价差取费设置成功');
      cancel();
      emits('updateData');
    }
  });
};
</script>

<style lang="scss" scoped>
.range-content {
  margin-bottom: 15px;
}
</style>
