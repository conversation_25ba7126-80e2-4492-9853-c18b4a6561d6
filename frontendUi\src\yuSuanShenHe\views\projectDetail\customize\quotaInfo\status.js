/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-08-25 14:43:38
 * @LastEditors: wangru
 * @LastEditTime: 2024-05-10 10:22:31
 */
import { reactive } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
export const globalData = reactive({
  isEditStatus: false, // 是否修改了，修改了不允许切换了。
  deIdSeq: null, //选中定额id
  constructId: null,
  singleId: null, //单项ID
  unitId: null, //单位ID
  pageType: null, //pageType:1-分部分项  2-措施项目
});
export const settGlobalData = (seq, type) => {
  globalData.deIdSeq = seq;
  globalData.constructId = store.currentTreeGroupInfo?.constructId;
  globalData.singleId = store.currentTreeGroupInfo?.singleId;
  globalData.unitId = store.currentTreeInfo?.id;
  globalData.pageType = type;
};
