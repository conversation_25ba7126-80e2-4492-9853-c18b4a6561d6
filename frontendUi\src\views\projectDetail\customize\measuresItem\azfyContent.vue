<!--
 * @@Descripttion: 安装费用
 * @Author: wangru
 * @Date: 2023-08-04 15:56:53
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-26 16:30:11
-->
<template>
  <div>
    <div style=" display: flex;justify-content: space-between;height: 100%;">
      <div class="contentAll">
        <p class="selectContent">
          <a-button
            type="primary"
            ghost
            style="display: inline-block; margin: 0 20px 0 0"
            @click="revertData"
            ref="target"
          >恢复系统默认</a-button>
          <a-button
            type="primary"
            ghost
            style="display: inline-block; margin: 0 20px 0 0"
            @click="advancedModel = true"
            ref="target"
          >高级选项</a-button>
          <span>高层建筑高</span>
          <a-input-number
            v-model:value="layerInterval"
            style="width: 100px"
            :min="0"
            :max="9999"
            ref="target"
            :disabled="!isEdit"
            @keyup="updateTableList"
          >
            <template #addonBefore>
              <span
                @click="queryData('layerInterval++')"
                class="btnAdd"
              >+</span>
            </template>
            <template #addonAfter>
              <span
                @click="queryData('layerInterval--')"
                class="btnSub"
              >-</span>
            </template>
          </a-input-number>
          <span class="fontsingle">层，</span>
          <span>或</span>
          <a-input-number
            v-model:value="heightRange"
            style="width: 100px"
            :min="0"
            :max="9999"
            :disabled="!isEdit"
            @keyup="updateTableList"
          >
            <template #addonBefore>
              <span
                @click="queryData('heightRange++')"
                class="btnAdd"
              >+</span>
            </template>
            <template #addonAfter>
              <span
                @click="queryData('heightRange--')"
                class="btnSub"
              >-</span>
            </template>
          </a-input-number>
          <span class="fontsingle">米 </span>
        </p>
        <div class="table-content">
          <vxe-table
            align="center"
            :column-config="{ resizable: true, isCurrent: true }"
            :row-config="{ isCurrent: true }"
            :data="tableDataFirst"
            height="180"
            class="tableNo1"
            ref="upTable"
            keep-source
            @current-change="currentChange"
            @cell-click="cellClickEvent"
            :merge-cells="mergeCellsUpTable"
          >
            <vxe-column
              field="sortNo"
              min-width="80"
              title="序号"
            > </vxe-column>
            <vxe-column
              field="feeName"
              min-width="150"
              title="总价措施名称"
            >
            </vxe-column>
            <vxe-column
              field="type"
              min-width="120"
              title="计取方式"
            >
              <template #default="{ row }">
                <span
                  style="display: block;"
                  @click="openSelectTypeModal(row)"
                >
                  {{typeList.find(a=>a.classCode===row.type).className}}
                  <span style="float:right;margin: auto;width:17px;border-radius: 5px;cursor:pointer;border: 1px transparent solid;">
                    ...
                  </span>
                </span>

                <!-- <vxe-select
                v-model="row.type"
                transfer
                @change="selectChange(row, 'type')"
                placeholder="请选择计取方式"
              >
                <vxe-option
                  v-for="item in typeList"
                  :key="item.classCode"
                  :value="item.classCode"
                  :label="item.className"
                ></vxe-option>
              </vxe-select> -->
              </template>
            </vxe-column>
            <vxe-column
              field="baseDeScope"
              min-width="150"
              title="基数定额计取范围"
            >
              <template #default="{ row }">
                <span class="detailTitle">{{ row.baseDeScope }}</span>
                <vxe-button
                  status="primary"
                  content="详情"
                  @click="setlocationModel(row.feeCode)"
                ></vxe-button>
              </template>
            </vxe-column>
            <vxe-column
              field="isCheck"
              min-width="80"
              title="是否计取"
            >
              <template #default="{ row }">
                <vxe-checkbox
                  v-model="row.isCheck"
                  :checked-value="true"
                  :unchecked-value="false"
                  :disabled="!isEdit"
                ></vxe-checkbox>
              </template>
            </vxe-column>
          </vxe-table>
          <vxe-table
            v-if="tableNo2Loading"
            align="center"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true, isCurrent: true }"
            :data="tableDataSecond"
            height="200"
            class="tableNo2"
            ref="tableNo2"
            :edit-config="{ trigger: 'click', mode: 'cell' }"
            @edit-closed="editClosedEvent"
            keep-source
            show-overflow="tooltip"
            :merge-cells="mergeCells"
            @current-change="tableRowChange"
            @cell-click="cellClickDownEvent"
          >
            <vxe-column
              field="classLevel1Name"
              min-width="180"
              title="定额分册"
            >
            </vxe-column>
            <vxe-column
              field="isDefaultRow.classLevel2"
              min-width="90"
              title="对应章节"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
            >
              <template #default="{ row }">
                {{row.isDefaultRow.classLevel2}}
              </template>
              <template #edit="{ row }">
                <span style="float:left;width:calc(100% - 20px); white-space: nowrap;overflow:hidden;text-overflow: ellipsis;">
                  {{row.isDefaultRow.classLevel2}}
                </span>
                <span
                  style="float:right;margin: auto;width:17px;border-radius: 5px;cursor:pointer;border: 1px gray solid;"
                  @click="openSelectCom(row)"
                >
                  ...
                </span>
              </template>
            </vxe-column>
            <vxe-column
              field="isDefault"
              min-width="300"
              title="对应当前单位措施定额"
            >
              <template #default="{ row }">
                <vxe-select
                  v-model="row.isDefault"
                  transfer
                  @change="selectChange(row, 'isDefault')"
                  placeholder="请选择对应当前单位措施定额"
                >
                  <vxe-option
                    v-for="item in row.deList"
                    :key="item.sequenceNbr"
                    :value="item.sequenceNbr"
                    :label="item.deName"
                  ></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
            <vxe-column
              field="isDefaultRow.allocationMethod"
              min-width="120"
              title="基数分摊方式"
            >
              <template #default="{ row }">
                <vxe-select
                  v-model="row.isDefaultRow.allocationMethod"
                  transfer
                  @change="selectChange(row, 'allocationMethod')"
                  placeholder="请选择基数分摊方式"
                >
                  <vxe-option
                    v-for="item in allocationMethodList"
                    :key="item.classCode"
                    :value="item.classCode"
                    :label="item.className"
                  ></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
            <vxe-column
              field="isDefaultRow.calculateBase"
              min-width="170"
              title="计算基数"
            >
              <template #default="{ row }">
                <span v-if="row.isDefaultRow.allocationMethod === 0">
                  分别按人、材、机取费
                </span>
                <vxe-select
                  v-if="row.isDefaultRow.allocationMethod === 1"
                  v-model="row.isDefaultRow.calculateBase"
                  transfer
                  @change="selectChange(row, 'calculateBase')"
                  placeholder="请选择计算基数"
                >
                  <vxe-option
                    v-for="item in calculateBaseList"
                    :key="item.classCode"
                    :value="item.classCode"
                    :label="item.className"
                  ></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
            <vxe-column
              field="isDefaultRow.rate"
              min-width="80"
              title="费率"
              :edit-render="{ autofocus: '.vxe-input--inner' }"
            >
              <template #default="{ row }">
                <span v-if="row.isDefaultRow.allocationMethod === 0"> - </span>
                <span v-if="row.isDefaultRow.allocationMethod !== 0">
                  {{ row.isDefaultRow.rate }}
                </span>
              </template>
              <template #edit="{ row }">
                <span v-if="row.isDefaultRow.allocationMethod === 0"> - </span>
                <vxe-input
                  :clearable="false"
                  v-model="row.isDefaultRow.rate"
                  v-if="row.isDefaultRow.allocationMethod !== 0"
                  @blur="
                  (row.isDefaultRow.rate = pureNumber(
                    row.isDefaultRow.rate,
                    2
                  )),
                    clear()
                "
                ></vxe-input>
              </template>
            </vxe-column>
            <vxe-colgroup title="其中">
              <vxe-column
                field="isDefaultRow.rRate"
                min-width="80"
                title="人工%"
                :edit-render="{ autofocus: '.vxe-input--inner' }"
              >
                <template #edit="{ row }">
                  <vxe-input
                    :clearable="false"
                    v-model="row.isDefaultRow.rRate"
                    @blur="
                    clear(),
                      (row.isDefaultRow.rRate = pureNumber(
                        row.isDefaultRow.rRate,
                        2
                      ))
                  "
                  ></vxe-input> </template></vxe-column>
              <vxe-column
                field="isDefaultRow.cRate"
                min-width="80"
                title="材料%"
                :edit-render="{ autofocus: '.vxe-input--inner' }"
              >
                <template #edit="{ row }">
                  <vxe-input
                    :clearable="false"
                    v-model="row.isDefaultRow.cRate"
                    @blur="
                    (row.isDefaultRow.cRate = pureNumber(
                      row.isDefaultRow.cRate,
                      2
                    )),
                      clear()
                  "
                  ></vxe-input>
                </template>
              </vxe-column>
              <vxe-column
                field="isDefaultRow.jRate"
                min-width="80"
                title="机械%"
                :edit-render="{ autofocus: '.vxe-input--inner' }"
              >
                <template #edit="{ row }">
                  <vxe-input
                    :clearable="false"
                    v-model="row.isDefaultRow.jRate"
                    @blur="
                    (row.isDefaultRow.jRate = pureNumber(
                      row.isDefaultRow.jRate,
                      2
                    )),
                      clear()
                  "
                  ></vxe-input>
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-column
              field="isDefaultRow.relationList"
              width="250"
              title="具体清单"
              header-align="center"
              align="left"
            >
              <template #default="{ row }">
                <a-tooltip
                  placement="top"
                  v-if="
                  currentRow.type !== 4 ? row.isDefaultRow.relationList : ''
                "
                >
                  <template #title>
                    {{
                    currentRow.type !== 4 ? row.isDefaultRow.relationList : ''
                  }}
                  </template>
                  <span class="detailQD">{{
                  currentRow.type !== 4 ? row.isDefaultRow.relationList : ''
                }}</span>
                </a-tooltip>
                <vxe-button
                  v-if="!(outputType === 1 && currentRow.type === 0)"
                  class="detailBtn"
                  status="primary"
                  content="详情"
                  @click="getDetailQD(row)"
                  :disabled="currentRow.type === 4"
                ></vxe-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
      <div class="asideTree">
        <p class="asideTree-title">
          应用范围：
          <a-button
            size="small"
            @click="selectSameMajor"
            style="margin-top: -3px;"
          >
            <span style="font-size:12px;">
              选择同专业
            </span>
          </a-button>
        </p>
        <div class="asideTree-content">
          <a-tree
            v-model:checkedKeys="rightInfo.checkedKeys"
            v-model:expandedKeys="rightInfo.expandedKeysRight"
            :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
            checkable
            :tree-data="treeData"
          >
            <template #title="{ levelType, id, name, whetherNew, children }">
              <a-tooltip placement="leftTop">
                <template #title>{{ name }}</template>
                <span class="check-labels">{{ name }}</span>
              </a-tooltip>
            </template>
          </a-tree>
        </div>
      </div>
    </div>
    <p
      class="btnsOut"
      style="width: 300px !important"
    >
      <a-button
        type="primary"
        @click="emits('close')"
        ghost
      >取消</a-button>
      <a-button
        type="primary"
        @click="submit()"
        :loading="submitLoading"
      >确定</a-button>
    </p>
  </div>

  <common-modal
    title="基数定额计取范围设置"
    width="750"
    height="550"
    className="dialog-comm"
    v-model:modelValue="locationModel"
  >
    <div class="content">
      <p class="selectContent">
        <span>安装专业</span>
        <a-select
          v-model:value="deBookValue"
          :size="size"
          style="width: 230px; margin-right: 20px"
          :options="deBookList"
          :field-names="{ label: 'label', value: 'value' }"
          placeholder="请选择安装专业"
          @change="selectChange({}, 'deBookValue')"
        ></a-select>
        <span>章节筛选</span>
        <a-select
          v-model:value="chapterValue"
          :size="size"
          style="width: 230px"
          :options="chapterList"
          :field-names="{ label: 'label', value: 'value' }"
          placeholder="请选择章节"
          @change="selectChange({}, 'chapterValue')"
        ></a-select>
      </p>
      <div class="tableNo3">
        <vxe-table
          align="center"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true, height: 30 }"
          :data="tableDataThird"
          height="auto"
          width="450"
          ref="tableNo3"
          class='table-line'
          :tree-config="{
            transform: true,
            rowField: 'sequenceNbr',
            parentField: 'parentId',
            line: true,
            showIcon: true,
            expandAll: true,
			iconOpen: 'icon-caret-down',
			iconClose: 'icon-caret-right'
          }"
          :row-class-name="rowClassName"
          show-overflow
          keep-source
        >
          <vxe-column
            field="bdCode"
            width="30%"
            title="项目编码"
            tree-node
          >
          </vxe-column>
          <vxe-column
            field="type"
            width="10%"
            title="类型"
          > </vxe-column>
          <vxe-column
            field="name"
            width="40%"
            title="名称"
          > </vxe-column>
          <vxe-column
            field="isCheck"
            width="20%"
            title="是否计取"
          >
            <template #default="{ row }">
              <vxe-checkbox
                v-model="row.isCheck"
                :checked-value="true"
                :unchecked-value="false"
                @change="selectChange(row, 'table3check')"
              ></vxe-checkbox>
            </template>
          </vxe-column>
        </vxe-table>
        <a-radio-group
          v-model:value="radioListValue"
          class="radioList"
          @change="radioChange"
        >
          <a-radio :value="1">全选</a-radio>
          <a-radio :value="2">反选</a-radio>
        </a-radio-group>
      </div>
    </div>

    <p class="btns">
      <a-button
        @click="locationModel = false"
        style="margin-right: 20px"
      >取消</a-button>
      <a-button
        type="primary"
        @click="sureLocation()"
      >确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="指定具体清单"
    width="750"
    height="510"
    className="dialog-comm"
    v-model:modelValue="detailModel"
  >
    <div class="table-content tableNo4">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, height: 30 }"
        :data="tableDataFourth"
        height="auto"
        width="430"
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          line: true,
          expandAll: true,
        }"
        :row-class-name="rowClassName"
        show-overflow
        ref="tableNo4"
      >
        <vxe-column
          field="fxCode"
          min-width="180"
          title="项目编码"
          tree-node
        >
        </vxe-column>
        <vxe-column
          field="type"
          min-width="80"
          title="类型"
        > </vxe-column>
        <vxe-column
          field="name"
          min-width="180"
          title="名称"
        > </vxe-column>
        <vxe-column
          field="isCheck"
          min-width="180"
          title="计取至该清单"
        >
          <template #default="{ row }">
            <vxe-checkbox
              v-model="row.isCheck"
              :checked-value="true"
              :unchecked-value="false"
              @change="selectChange(row, 'qdisCheck')"
              :disabled="row.type !== '清'"
            ></vxe-checkbox>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <p
      class="btns"
      style="width: 300px !important"
    >
      <a-button
        type="primary"
        @click="detailModel = false"
        ghost
      >取消</a-button>
      <a-button
        type="primary"
        @click="sureJQ()"
      >确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="选择计算范围"
    width="550"
    height="450"
    className="dialog-comm"
    v-model:modelValue="selectCompute"
  >
    <div class="ComputeContent">
      <s-table
        size="small"
        ref="selectComTable"
        class="s-table"
        :expandedRowKeys="expandedRowKeys"
        :columns="tableOptions.columns"
        :scroll="{ y: 280 }"
        :animateRows="false"
        :pagination="false"
        rowKey="idx"
        :data-source="tableOptions.data"
        :loading="comTableLoading"
        :row-height="35"
        :rowSelection="{
            checkStrictly:false,
            hideSelectAll:true,
            selectedRowKeys: defineCheckRowKeys,
            onChange: onSelectChange,
            onSelect:setSelectIScheck
          }"
        childrenColumnName="childrenList"
        :custom-cell="customCell"
        :custom-row="customRow"
        :rowClassName="(row, index) => rowClassNameTree(row, index, tableOptions.data)"
      >
        <template #expandIcon="props">
          <span v-if="props.record.childrenList?.length > 0">
            <div
              v-if="props.expanded"
              style="display: inline-block; margin-right: 10px"
              @click="expandFun(1,props.record)"
            >
              <i class="ag-vxe-icon-caret-down"></i>
            </div>
            <div
              v-else
              style="display: inline-block; margin-right: 10px"
              @click="
              expandFun(2,props.record)"
            >
              <i class="ag-vxe-icon-caret-right"></i>
            </div>
          </span>
          <span
            v-else
            style="margin-right:29px"
          ></span>
        </template>
        <template #bodyCell="{
              text,
              record: row,
              index,
              column,
              key,
              openEditor,
              closeEditor,
            }">

          <div v-if="column.dataIndex === 'isCheck'">

          </div>
        </template>
      </s-table>
    </div>
    <p class="selectRightBtn">
      <a-button
        @click="selectBtnChange('1')"
        style="margin-right: 20px"
      >全选</a-button>
      <a-button
        @click="selectBtnChange('2')"
        style="margin-right: 20px"
      >反选</a-button>
    </p>
    <p class="selectBtn">
      <a-button
        @click="selectCompute = false"
        style="margin-right: 20px"
      >取消</a-button>
      <a-button
        type="primary"
        @click="sureSelectCompute()"
      >确定</a-button>
    </p>
  </common-modal>
  <info-modal
    v-model:infoVisible="infoVisible"
    :infoText="infoText"
    :isSureModal="isSureModal"
    :iconType="iconType"
    @updateCurrentInfo="updateCurrentInfo"
  ></info-modal>
  <info-modal
    v-model:infoVisible="editInfo"
    infoText="费用分摊项应该在0~100之间，并且和等于100，请重新输入"
    :isSureModal="true"
    @update:infoVisible="close"
    iconType="icon-querenshanchu"
    :isFunction="false"
  ></info-modal>
  <common-modal
    title="高级选项"
    width="450"
    height="300"
    className="dialog-comm"
    v-model:modelValue="advancedModel"
  >
    <div class="advanced-content">
      <div class="single">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-jieyongzimuanzhuangfeiyongjiqufangshi"
          ></icon-font>借用子目安装费用计取方式
        </div>
        <vxe-checkbox
          v-model="borrowRule"
          :checked-value="true"
          :unchecked-value="false"
        >使用借用的库的安装费用规则</vxe-checkbox>
      </div>
      <div class="single">
        <div class="title">
          <icon-font
            class="icon-font"
            type="icon-anzhuangfeiyongshuchufangshi"
          ></icon-font>安装费用输出方式
        </div>
        <a-radio-group
          v-model:value="outputType"
          class="radioList"
          @change="radioChange"
        >
          <a-radio :value="1">清单费用按每个分部分别计取</a-radio>
          <a-radio :value="2">清单费用按整个工程统一修改</a-radio>
        </a-radio-group>
      </div>
    </div>

    <p class="btns">
      <a-button
        @click="advancedModel = false"
        style="margin-right: 20px"
      >取消</a-button>
      <a-button
        type="primary"
        @click="advancedSure"
      >确定</a-button>
    </p>
  </common-modal>
  <common-modal
    title="计取位置"
    width="370"
    height="220"
    :position="{ top: 'calc(50% - 110px)', left: 'calc(50% - 150px)' }"
    className="dialog-comm"
    v-model:modelValue="selectTypeModal"
    @close="closeTypeModal"
  >
    <div class="select-type">
      <p>
        <span>计取位置：</span>
        <!-- <a-radio-group
          v-model:value="selectTypeValue.type"
          class="radioList"
          @change="selectChange(selectTypeValue, 'type')"
          v-for="item in typeList"
        >
          <a-radio :value="item.classCode">{{ item.className }}</a-radio>
        </a-radio-group> -->
        <a-select
          v-model:value="selectTypeValue.type"
          :size="'small'"
          style="width: 280px"
          :options="typeList"
          :field-names="{ label: 'className', value: 'classCode' }"
          placeholder="请选择章节"
          @change="selectChange(selectTypeValue, 'type')"
        ></a-select>
      </p>
      <p class="select-type-jtx">
        <span>具体项：</span>
      <p class="select-type-jtx-ipt">
        <a-tooltip :title="selectTypeValueQd.relationList">
          <span class="select-type-jtx-ipt-text">{{ selectTypeValueQd.relationList }}</span>
        </a-tooltip>
        <span
          :class="selectTypeValue.type===4?'select-type-jtx-ipt-disSpan':'select-type-jtx-ipt-span'"
          @click="getDetailQD(null)"
        >···
        </span>
      </p>
      </p>
    </div>

    <p class="btns">
      <a-button
        type="primary"
        @click="selectTypeSure"
        :disabled="selectTypeValue.type!==4&&!selectTypeValueQd.relationList"
      >确定</a-button>
    </p>
  </common-modal>
</template>
<script setup>
import {
  nextTick,
  onMounted,
  shallowRef,
  shallowReactive,
  reactive,
  ref,
  toRaw,
  watch,
  watchEffect,
  computed,
} from 'vue';
import { pureNumber } from '@/utils/index';
import api from '../../../../api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';
import infoMode from '@/plugins/infoMode.js';
import { constructLevelTreeStructureList } from '@/api/csProject';
import xeUtils, { find } from 'xe-utils';
import { useRoute } from 'vue-router';
const route = useRoute();
const store = projectDetailStore();
const target = ref(null); //点击输入框之外的地方可进行查询
let locationModel = ref(false); //基数定额详情弹框
let detailModel = ref(false); //具体清单详情弹框
let layerInterval = ref(0); //高层建筑高-层
let heightRange = ref(0); //高层建筑高-米
let radioListValue = ref(1); //全选，反选--单选按钮
const emits = defineEmits(['updateData', 'close']);
let deBookList = ref([]); //安装工程下拉框
let chapterList = ref([]); //章节下拉框
let deBookValue = ref(); //默认安装工程下拉框选中值
let chapterValue = ref(); //默认章节下拉框下拉框选中值
let tableDataThird = ref([]); //基数定额详情表格数据
let tableNo3 = ref(); //基数定额记取弹框
let tableNo4 = ref(); //具体清单
let tableNo2 = ref(); //下表格
let tableDataFourth = ref([]); //具体清单弹框data
let changeList = ref([]); //弹框改变的数据
let currentRow = ref(); //上表格选中行
let oldlayerInterval = ref(0); //高层建筑高-层
let oldheightRange = ref(0); //高层建筑高-米
let table2DetailRow = ref(); //下表格选中行的详情
let infoVisible = ref(false); // 提示信息框是否显示
let infoText = ref(''); // 提示信息框的展示文本
let iconType = ref(''); // 提示信息框的图标
let isSureModal = ref(false); // 提示信息框是否为确认提示框
let table2current = ref(); //下表格选中行数据
let isEdit = ref(true); //是否可以编辑分摊三种费率
let editInfo = ref(false);
let defaultRowList = ref([]);
let submitLoading = ref(false); //点击确定按钮loading
let defaultDownData = ref(); //默认展示的下表格数据
let cgCacheData = ref(); //缓存数据
let upTableCheckList = ref([]); //上表格选中数据
let selectCompute = ref(false); //章节选择范围
let selectTypeModal = ref(false); //选择计取位置弹框
let selectTypeValue = ref(null);
let selectTypeValueQd = reactive({
  relationListId: null,
  relationList: null,
}); //计取位置具体项value
const upTable = ref();
let tableDataFirst = ref([]);
let tableDataSecond = ref([]);
let mergeCells = ref(null);
let mergeCellsUpTable = ref(null); //上表格数据合并处理--存在全国统一安装工程预算定额河北省消耗量定额（2012）树结构
//高级设置弹参数
let advancedModel = ref(false); // 高级选项弹框是否展示
let borrowRule = ref(true); // 使用借用的库的安装费用规则
let outputType = ref(2); // 安装费用输出方式
const advancedSure = () => {
  getTableDownData();
  advancedModel.value = false;
};
let oldSelectType = null;
const openSelectTypeModal = row => {
  selectTypeModal.value = true;
  selectTypeValue.value = row;
  selectTypeValueQd.relationList = null;
  oldSelectType = row.type;
  refreshTypeValueQD();
};
const closeTypeModal = () => {
  //关闭计取位置弹框--右上角关闭，此时type恢复至原来
  selectTypeValue.value.type = oldSelectType;
  selectTypeModal.value = true;
};
const selectTypeSure = () => {
  selectTypeModal.value = false;
  if (selectTypeValueQd.relationListId && selectTypeValueQd.relationList) {
    tableDataSecond.value.map(b => {
      b.isDefaultRow.relationListId = selectTypeValueQd.relationListId;
      b.isDefaultRow.relationList = selectTypeValueQd.relationList;
    });
    tableDataSecond.value.map(item => {
      saveJqResult(upTable.value.getCurrentRecord(), item);
    });
    //存储此时对应的下表格数据
    let newValue = tableDataSecond.value;
    getLastDefaultDownDataRow(currentRow.value, newValue);
  }
};
const setMergeCells = () => {
  //名称相同的行合并
  let sameIndex = -1;
  // let sameList = [];
  let mergeList = [];
  tableDataSecond.value.forEach((item, index) => {
    if (index > sameIndex) {
      let flag = mergeList.filter(a => a.name === item.classLevel1Name);
      let target = flag && flag[flag.length - 1];
      if (target && target.idx === index - 1) {
        target.rowspan += 1;
        target.idx += 1;
      } else {
        mergeList.push({
          name: item.classLevel1Name,
          idx: index,
          row: index,
          col: 0,
          rowspan: 1,
          colspan: 1,
        });
      }
      sameIndex++;
    }
  });
  mergeCells.value = mergeList;
};
const close = () => {
  tableNo2.value.setCurrentRow(table2current.value);
  editInfo.value = false;
};
// onClickOutside(target, () => {
//   //改变高层建筑层高和米数，点击输入框之外的地方进行查询数据展示
//   if (
//     oldlayerInterval.value !== layerInterval.value ||
//     oldheightRange.value !== heightRange.value
//   ) {
//     upTableCheckList.value = [];
//     tableDataFirst.value.map(item => {
//       item.isCheck ? upTableCheckList.value.push(item.feeCode) : '';
//     });
//     getTableUpData(true);
//     oldlayerInterval.value = layerInterval.value;
//     oldheightRange.value = heightRange.value;
//   }
// });
let isEditInput = ref(false); //是否编辑建筑层高等
let table1current = ref(null); //表格1选中数据
//改变高层建筑层高和米数，实时查询数据展示
const updateTableList = () => {
  if (
    oldlayerInterval.value !== layerInterval.value ||
    oldheightRange.value !== heightRange.value
  ) {
    upTableCheckList.value = [];
    tableDataFirst.value.map(item => {
      item.isCheck ? upTableCheckList.value.push(item.feeCode) : '';
    });
    table1current.value = upTable.value.getCurrentRecord();
    getTableUpData(true, true);
    oldlayerInterval.value = layerInterval.value;
    oldheightRange.value = heightRange.value;
  }
};

const typeList = reactive([
  //记取方式列表
  {
    className: '指定措施清单',
    classCode: 3,
  },
  {
    className: '指定分部分项清单',
    classCode: 0,
  },
  {
    className: '对应分部分项清单',
    classCode: 4,
  },
]);
const allocationMethodList = reactive([
  //基数分摊方式列表
  {
    className: '非分摊计取',
    classCode: 0,
  },
  {
    className: '分摊计取',
    classCode: 1,
  },
]);
const calculateBaseList = reactive([
  //计算基数下拉列表
  {
    className: '人工费',
    classCode: 'RGF',
  },
  {
    className: '机械费',
    classCode: 'JXF',
  },
  {
    className: '材料费',
    classCode: 'CLF',
  },
  {
    className: '人工费+机械费',
    classCode: 'RGF+JXF',
  },
  {
    className: '人工费+材料费',
    classCode: 'RGF+CLF',
  },
  {
    className: '机械费+材料费',
    classCode: 'JXF+CLF',
  },
  {
    className: '人工费+机械费+材料费',
    classCode: 'RGF，CLF，JXF',
  },
]);

//点击恢复系统默认按钮
const revertData = () => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '确认恢复系统默认吗？',
    confirm: () => {
      layerInterval.value = 0;
      heightRange.value = 0;
      getTableUpData(true);
      infoMode.hide();
    },
    close: () => {
      infoMode.hide();
    },
  });
};
onMounted(() => {
  getList(); //右侧同专记取专业树
  getTableUpData();
});

let tableNo2Loading = ref(true);
let pos = reactive({
  scrollLeft: 0,
  scrollTop: 0,
});
let downCurrenRow = ref();
let isInit = ref(false);
// 处理合并数据
const handleMerge = () => {
  let mergeCells = [];
  tableDataSecond.value.forEach((item, index) => {
    mergeCells.push({
      row: index,
      col: 4,
      rowspan: 1,
      colspan: !item.isDefaultRow.allocationMethod ? 2 : 1,
    });
  });

  if (!isInit.value) {
    isInit.value = true;
    setTimeout(() => {
      tableNo2.value?.setMergeCells(mergeCells);
    }, 500);
  } else {
    tableNo2Loading.value = false;
    nextTick(() => {
      tableNo2Loading.value = true;
      setTimeout(() => {
        tableNo2.value?.setMergeCells(mergeCells);
        tableNo2.value?.setCurrentRow(downCurrenRow.value);
        tableNo2.value?.scrollTo(pos.scrollLeft, pos.scrollTop);
      }, 100);
    });
  }
};

watchEffect(() => {
  if (tableDataSecond.value.length > 0) {
    handleMerge();
  }
});

const clear = () => {
  //清除编辑状态
  const $table = tableNo2.value;
  $table.clearEdit();
};
const getTableUpData = (reset = false, isEdit = false) => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  isEditInput.value = isEdit;
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    layerInterval: Number(layerInterval.value),
    heightRange: Number(heightRange.value),
  };
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  // console.log('列表数据getTableUpData传参', formData);
  api.azCostMathList(formData).then(res => {
    if (res.status === 200 && res.result) {
      defaultDownData.value = JSON.parse(JSON.stringify(res.result));
      defaultList(); //处理默认数据
      //获取是否有缓存数据
      api.azCostMathCache(apiData).then(a => {
        if (a.status === 200) {
          console.log('安装费用-缓存数据', a);
          cgCacheData.value = a.result ? a.result.data : [];
          if (reset) {
            cgCacheData.value = [];
          } else {
            layerInterval.value =
              a.result && a.result.layerInterval ? a.result.layerInterval : 0;
            heightRange.value =
              a.result && a.result.heightRange ? a.result.heightRange : 0;
            oldlayerInterval.value =
              a.result && a.result.layerInterval ? a.result.layerInterval : 0;
            oldheightRange.value =
              a.result && a.result.heightRange ? a.result.heightRange : 0;
            borrowRule.value =
              a.result && a.result.borrowRule ? a.result.borrowRule : true;
            outputType.value =
              a.result && a.result.outputType ? a.result.outputType : 2;
          }
          getFinallyData(res.result);
        }
      });
    }
  });
};
let checkTypeList = reactive([]); //表格1的每行数据记取方式
const getFinallyData = data => {
  //对比缓存数据和获取到的列表数据
  if (isEditInput.value) {
    console.log(checkTypeList);
    //更改高层建筑不刷新表1的记取方式
    data.forEach(i => {
      const tar = checkTypeList.find(a => a.feeCode === i.feeCode);
      i.type = tar ? tar.type : i.type;
    });
  }
  //上表格存在树结构处理
  // data.unshift({
  //   sequenceNbr: '0',
  //   feeName: '全国统一安装工程预算定额河北省消耗量定额（2012）',
  //   type: 'total',
  // });
  tableDataFirst.value = data;
  // let mergeList = [];
  // data.map(a => {
  //   if (a.type === 'total') {
  //     mergeList.push({
  //       row: index,
  //       col: 4,
  //       rowspan: 1,
  //       colspan: !item.isDefaultRow.allocationMethod ? 2 : 1,
  //     });
  //   }
  // });
  // mergeCellsUpTable.value = mergeList;
  if (upTableCheckList.value.length > 0) {
    tableDataFirst.value.map(
      item =>
        (item.isCheck = upTableCheckList.value.includes(item.feeCode)
          ? true
          : false)
    );
  }
  console.log(
    'tableDataFirst.value && tableDataFirst.value[0]',
    tableDataFirst.value,
    table1current.value
  );
  if (isEditInput.value) {
    let target = tableDataFirst.value.findIndex(
      i => i.feeName === table1current.value.feeName
    );
    upTable.value.setCurrentRow(tableDataFirst.value[target]);
    currentRow.value = tableDataFirst.value[target];
  } else {
    upTable.value.setCurrentRow(
      tableDataFirst.value && tableDataFirst.value[0]
    );
    currentRow.value = tableDataFirst.value[0];
  }

  //有缓存数据下表格数据根据缓存数据展示，没有下表格数据接口获取
  if (cgCacheData.value.length > 0) {
    tableDataFirst.value.map(item => {
      let same = cgCacheData.value.filter(cg => cg.feeCode === item.feeCode);
      if (same && same.length > 0) {
        item.isCheck = same[0].isCheck;
        item.classLevelList = same[0].classLevelList;
        item.baseDeScope = same[0].baseDeScope;
        item.type = same[0].type;
        item.baseDeList = same[0].baseDeList;
        item.notSelectDeList = same[0].notSelectDeList;
      }
    });
    getDefaultRow(tableDataSecond.value);
    checkType(currentRow.value.type);
  } else {
    getTableDownData();
  }
};
//获取下表格数据的默认行
const getDefaultRow = list => {
  list.forEach(item => {
    item.isDefault = item.deList.filter(
      de => +de.isDefault === 1
    )[0].sequenceNbr;
    item.isDefaultRow = item.deList.filter(de => +de.isDefault === 1)[0];
    item.isDefaultRow.allocationMethod = Number(
      item.isDefaultRow.allocationMethod
    );
  });
};
const currentChange = ({ newValue, oldValue }) => {
  //基数定额表格点击行下表格切换对应的数据
  if (table2current.value) {
    upTable.value.setCurrentRow(oldValue);
    return;
  }
  currentRow.value = newValue;
  getTableDownData(true);
};
const cellClickEvent = ({ row, column }) => {
  //iscurrent点击currentChange事件不会触发
  currentRow.value = upTable.value.getCurrentRecord();
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
};
const cellClickDownEvent = ({ row, column }) => {
  const field = column.field;
  let list = [
    'isDefaultRow.allocationMethod',
    'isDefaultRow.rRate',
    'isDefaultRow.cRate',
    'isDefaultRow.jRate',
  ];
  if (table2current.value && !list.includes(field)) {
    upTable.value.revertData(row, field);
    editInfo.value = true;
    return;
  }
};
//记取方式切换
const checkType = async (type, isChangeUpRow) => {
  //切换记取方式重新获取下表格的具体清单
  let typeTar = checkTypeList.find(i => i.feeCode === currentRow.value.feeCode);
  if (typeTar) {
    typeTar.type = type;
  } else {
    checkTypeList.push({
      feeCode: currentRow.value.feeCode,
      type: type,
    });
  }
  // if (type === 4) return;//暂时注释-----下表格选择对应分部不展示的问题
  let hasList = defaultDownData.value.find(
    item =>
      item.feeCode === currentRow.value.feeCode &&
      item.type === currentRow.value.type &&
      !item.isfirst
  );
  if (isChangeUpRow) {
    let hasList2 = defaultDownData.value.find(
      item =>
        item.feeCode === currentRow.value.feeCode &&
        item.type === currentRow.value.type &&
        item.isfirst
    );
    tableDataSecond.value = hasList
      ? hasList.classLevelList
      : hasList2.classLevelList;
  } else if (hasList) {
    tableDataSecond.value = hasList.classLevelList;
  } else {
    let list = [];
    let dataList = defaultDownData.value.filter(
      item => item.feeCode === currentRow.value.feeCode && item.isfirst
    )[0];
    dataList.classLevelList.forEach(item => {
      list.push(item.isDefaultRow);
    });
    let formData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      data: JSON.parse(JSON.stringify(list)),
      type: currentRow.value.type,
      feeCode: currentRow.value.feeCode,
    };
    await api.getDefaultQdValue(formData).then(res => {
      console.log('getDefaultQdValue', res, formData);
      if (res.status === 200 && res.result) {
        currentRow.value.classLevelList.map(item => {
          item.isDefault = item.deList.filter(
            d => Number(d.isDefault) === 1
          )[0].sequenceNbr;
          item.isDefaultRow = item.deList.filter(
            d => Number(d.isDefault) === 1
          )[0];
          item.isDefaultRow.allocationMethod = Number(
            item.isDefaultRow.allocationMethod
          );
          let QD = res.result.filter(
            qd => qd.sequenceNbr === item.isDefaultRow.sequenceNbr
          );
          console.log(QD, item.isDefaultRow.relationList);
          if (QD && QD.length > 0) {
            item.isDefaultRow.relationList = QD[0].relationList;
            item.isDefaultRow.relationListId = QD[0].relationListId;
          }
          // let sameSeq = res.result.filter(
          //   qd => qd.sequenceNbr === item.isDefault
          // )[0];
          // if (sameSeq) {
          //   item.isDefaultRow.relationList = sameSeq.relationList;
          //   item.isDefaultRow.relationListId = sameSeq.relationListId;
          // }
          // console.log(sameSeq.relationList, item.isDefaultRow.relationList);
        });
      }
      tableDataSecond.value =
        currentRow.value && currentRow.value.classLevelList;
      tableNo2.value.reloadData(tableDataSecond.value);
      let newValue = tableDataSecond.value;
      getLastDefaultDownDataRow(currentRow.value, newValue);
    });
  }
  setMergeCells();
  refreshTypeValueQD();
};
const refreshTypeValueQD = () => {
  if (selectTypeValue.value?.type === 4) {
    selectTypeValueQd.relationList = null;
    selectTypeValueQd.relationListId = null;
  } else {
    let first = tableDataSecond.value[0].isDefaultRow;
    let second = tableDataSecond.value.find(
      a =>
        a.sequenceNbr !== first.sequenceNbr &&
        a.isDefaultRow.relationListId !== first.relationListId
    );
    if (second) {
      selectTypeValueQd.relationList = null;
      selectTypeValueQd.relationListId = null;
    } else {
      selectTypeValueQd.relationList = first.relationList;
      selectTypeValueQd.relationListId = first.relationListId;
    }
    console.log('切换计取方式', first, second, selectTypeValueQd);
  }
};
//获取最初的默认清单数据，具体清单不勾选展示默认数据
const defaultList = () => {
  defaultDownData.value.forEach(h => {
    h.isfirst = true;
    h.classLevelList.forEach(item => {
      item.isDefault = item.deList.filter(
        de => +de.isDefault === 1
      )[0].sequenceNbr;
      item.isDefaultRow = item.deList.filter(de => +de.isDefault === 1)[0];
      item.isDefaultRow.allocationMethod = Number(
        item.isDefaultRow.allocationMethod
      );
      defaultRowList.value.push(item.isDefaultRow);
      item.defaultName = item.isDefaultRow.relationList;
      item.defaultID = item.isDefaultRow.relationListId;
    });
  });
};

//安装费用列表下表格数据获取
const getTableDownData = (isChangeUpRow = false) => {
  const $table = tableNo2.value;
  // debugger;
  if ($table && outputType.value === 1 && currentRow.type === 0) {
    $table.revertData(tableDataSecond.value);
  }
  console.log('------------', currentRow.value);
  // if (isEditInput.value && currentRow.value.feeCode !== '210') {
  //   return;
  // }
  // let newValue = currentRow.value && currentRow.value.classLevelList;
  //   getLastDefaultDownDataRow(currentRow.value, newValue);
  // debugger;
  if (currentRow.value.type === 4) {
    currentRow.value.classLevelList.forEach(item => {
      item.isDefault = item.deList.filter(
        de => +de.isDefault === 1
      )[0].sequenceNbr;
      item.isDefaultRow = item.deList.filter(de => +de.isDefault === 1)[0];
      item.isDefaultRow.allocationMethod = Number(
        item.isDefaultRow.allocationMethod
      );
    });
    tableDataSecond.value = currentRow.value.classLevelList;
    setMergeCells();
  } else {
    checkType(currentRow.value.type, isChangeUpRow);
  }
  setTable2Select(true);
  console.log('------------', tableDataSecond.value);
};

//点击记取按钮
const submit = () => {
  if (tableDataFirst.value && tableDataFirst.value.length === 0) {
    emits('close');
    return;
  }
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    data: toRaw(tableDataFirst.value).filter(item => item.isCheck === true),
    layerInterval: Number(layerInterval.value),
    heightRange: Number(heightRange.value),
    borrowRule: borrowRule.value,
    outputType: outputType.value,
    unitIdList: rightInfo.checkedKeys,
  };
  let infoList = [];
  apiData.data &&
    apiData.data.map(item => {
      infoList.push(item.feeName);
      let has1 = defaultDownData.value.filter(
        b => b.feeCode === item.feeCode && b.type === item.type
      );
      item.classLevelList = toRaw(has1[has1.length - 1].classLevelList);
      // item.classLevelList.forEach(de => {
      //   if (!de.hasOwnProperty('isDefaultRow')) {
      //     de.isDefault = de.deList.filter(
      //       de => +de.isDefault === 1
      //     )[0].sequenceNbr;
      //     de.isDefaultRow = de.deList.filter(de => +de.isDefault === 1)[0];
      //   }
      // });
      if (item.type === 4) {
        item.classLevelList.forEach(de => {
          de.isDefaultRow.relationList = null;
          de.isDefaultRow.relationListId = null;
        });
      }
    });
  console.log('安装费用结果', defaultDownData.value);
  console.log('安装费用传参', apiData);
  if (submitLoading.value) return;
  submitLoading.value = true;
  api
    .azCostMath(apiData)
    .then(res => {
      console.log('安装费用结果', res, apiData);
      if (apiData.data.length === 0 && res.status === 200) {
        emits('updateData');
        emits('close');
      } else if (apiData.data.length > 0 && res.status === 200) {
        let info = infoList.join();
        infoVisible.value = true;
        isSureModal.value = true;
        infoText.value = `${info}计取成功！`;
        iconType.value = 'icon-ruotixing';
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};
const updateCurrentInfo = type => {
  //更新数据
  if (!infoVisible.value) return;
  infoVisible.value = false;
  if (type === 1) {
    emits('updateData');
  } else {
    emits('updateData');
    emits('close');
  }
};

//基数定额记取范围设置确定
const sureLocation = () => {
  // let currentRow.value = upTable.value.getCurrentRecord();
  !currentRow.value.hasOwnProperty('baseDeList') || !currentRow.value.baseDeList
    ? (currentRow.value.baseDeList = [])
    : '';
  currentRow.value.notSelectDeList = [];
  let checklist = [];
  tableDataThird.value.map(item => {
    if (item.type === '定' && item.isCheck) {
      checklist.push(item.sequenceNbr);

      // if (
      //   (currentRow.baseDeList.length > 0 &&
      //     !currentRow.baseDeList.includes(item.sequenceNbr)) ||
      //   currentRow.baseDeList.length === 0
      // ) {
      //   checklist.push(item.sequenceNbr);
      // }
    }
    if (item.type === '定' && !item.isCheck) {
      currentRow.value.notSelectDeList.push(item.sequenceNbr);
    }
  });
  if (checklist.length > 0) {
    currentRow.value.baseDeList = checklist;
  }
  let select = upTable.value.getCurrentRecord();
  let tar = table3SelectList.find(i => i.table1row.feeCode === select.feeCode);
  if (tar) {
    tar.selectList = tableDataThird.value.filter(i => i.isCheck);
  } else {
    table3SelectList.push({
      table1row: select,
      selectList: tableDataThird.value.filter(i => i.isCheck),
    });
  }
  locationModel.value = false;
};
//更新存储的默认数据
const getLastDefaultDownDataRow = (row, newValue) => {
  let list = defaultDownData.value.find(
    item =>
      item.feeCode === row.feeCode && item.type === row.type && !item.isfirst
  );
  console.log('defaultDownData.value', defaultDownData.value);
  if (list) {
    defaultDownData.value.map(item => {
      if (
        item.feeCode === row.feeCode &&
        item.type === row.type &&
        !item.isfirst
      ) {
        item.classLevelList = JSON.parse(JSON.stringify(newValue));
      }
    });
  } else {
    defaultDownData.value.push({
      ...row,
      classLevelList: JSON.parse(JSON.stringify(newValue)),
      isfirst: false,
    });
  }
};

//基数定额记取范围确定
let table4SelectList = reactive([]);
const sureJQ = () => {
  let table4change = tableDataFourth.value.filter(
    item => item.isCheck === true
  )[0];
  if (table4change) {
    //具体清单有勾选
    if (isSetAllQDid.value) {
      //记住勾选项
      selectTypeValueQd.relationList = `${table4change.fxCode} ${table4change.name}`;
      selectTypeValueQd.relationListId = table4change.sequenceNbr;
    } else {
      //设置下表格对应行具体清单数据
      table2DetailRow.value.isDefaultRow.relationListId =
        table4change.sequenceNbr;
      table2DetailRow.value.isDefaultRow.relationList = `${table4change.fxCode} ${table4change.name}`;
    }
  } else {
    //具体清单无勾选展示默认值
    let defaultDownDataRow = defaultDownData.value.filter(
      item => item.feeCode === currentRow.value.feeCode && item.isfirst === true
    )[0];
    defaultDownDataRow.classLevelList.map(item => {
      if (
        item.classLevel1Name === table2DetailRow.value.classLevel1Name &&
        item.classLevel2Name === table2DetailRow.value.classLevel2Name
      ) {
        table2DetailRow.value.isDefaultRow.relationListId = item.defaultID;
        table2DetailRow.value.isDefaultRow.relationList = item.defaultName;
      }
    });
  }
  let select = upTable.value.getCurrentRecord();
  if (!isSetAllQDid.value) {
    //单条设置
    let select2 = tableNo2.value.getCurrentRecord();
    saveJqResult(select, select2);
    let newValue = tableDataSecond.value;
    getLastDefaultDownDataRow(currentRow.value, newValue);
  }
  detailModel.value = false;
};
const saveJqResult = (select, select2) => {
  let tar = table4SelectList.find(
    i =>
      i.table1row.feeCode === select.feeCode &&
      i.table2row.sequenceNbr === select2.sequenceNbr
  );
  if (tar) {
    tar.selectList = tableDataFourth.value.filter(i => i.isCheck);
  } else {
    table4SelectList.push({
      table1row: select,
      table2row: select2,
      selectList: tableDataFourth.value.filter(i => i.isCheck),
    });
  }
};
//上表格行切换
const tableRowChange = ({ newValue, oldValue }) => {
  console.log('newValue', newValue, 'oldValue', oldValue);
  let total = 0;
  oldValue
    ? (total =
        Number(oldValue.isDefaultRow?.cRate) +
        Number(oldValue.isDefaultRow?.rRate) +
        Number(oldValue.isDefaultRow?.jRate))
    : '';
  if (
    oldValue &&
    oldValue.isDefaultRow?.allocationMethod === 1 &&
    total !== 100
  ) {
    editInfo.value = true;
    table2current.value = oldValue;
  }
};
watch(
  () => table2current.value,
  () => {
    if (table2current.value) {
      let total =
        Number(table2current.value.isDefaultRow.cRate) +
        Number(table2current.value.isDefaultRow.rRate) +
        Number(table2current.value.isDefaultRow.jRate);
      if (total === 100) {
        isEdit.value = true;
        table2current.value = null;
      } else {
        isEdit.value = false;
      }
    }
  }
);
const editClosedEvent = ({ row, column }) => {
  const $table = tableNo2.value;
  const field = column.field;
  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (
    row.isDefaultRow.allocationMethod === 1 &&
    (field === 'isDefaultRow.cRate' ||
      field === 'isDefaultRow.jRate' ||
      field === 'isDefaultRow.rRate')
  ) {
    if (table2current.value) {
      let total =
        Number(table2current.value.isDefaultRow.cRate) +
        Number(table2current.value.isDefaultRow.rRate) +
        Number(table2current.value.isDefaultRow.jRate);
      total === 100
        ? ((isEdit.value = true), (table2current.value = null))
        : (isEdit.value = false);
    } else {
      let total =
        Number(row.isDefaultRow.cRate) +
        Number(row.isDefaultRow.rRate) +
        Number(row.isDefaultRow.jRate);
      total === 100
        ? ''
        : ((isEdit.value = false), (table2current.value = row));
    }
  }
};
const setTotalRate = (item, row) => {
  let total = Number(item.rRate) + Number(item.jRate) + Number(item.cRate);
  if (item.allocationMethod === 1 && total !== 100) {
    isEdit.value = false;
    table2current.value = row;
  } else if (item.allocationMethod !== 1) {
    if (
      table2current.value &&
      table2current.value.isDefaultRow.sequenceNbr === item.sequenceNbr
    ) {
      table2current.value = null;
      isEdit.value = true;
    }
  }
};

//高层建筑高切换
const queryData = type => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  switch (type) {
    case 'layerInterval++':
      layerInterval.value++;
      break;
    case 'layerInterval--':
      layerInterval.value--;
      break;
    case 'heightRange++':
      heightRange.value++;
      break;
    case 'heightRange--':
      heightRange.value--;
      break;
  }
};
const selectChange = (row, type) => {
  downCurrenRow.value = row;
  switch (type) {
    case 'type':
      checkType(row.type);
      break;
    case 'isDefault':
      // 对应当前单位措施定额
      row.isDefaultRow = row.deList.find(
        item => item.sequenceNbr === row.isDefault
      );
      //处理基数分摊方式为number类型
      row.isDefaultRow.allocationMethod = row.isDefaultRow.allocationMethod / 1;
      break;
    case 'allocationMethod':
      // 基数分摊方式
      setTotalRate(row.isDefaultRow, row);
      const { scrollLeft, scrollTop } = tableNo2.value?.getScroll();
      pos.scrollLeft = scrollLeft;
      pos.scrollTop = scrollTop;
      break;
    case 'calculateBase':
      // 基数计算基数
      break;
    case 'chapterValue':
      getBaseDeList();
      break;
    case 'deBookValue':
      getChapterList();
      break;
    case 'table3check':
      row.children && row.children.length > 0
        ? fatherCheck(row, tableDataThird.value)
        : childCheck(row, tableDataThird.value);
      childCheck(row, tableDataThird.value);
      fatherCheck(row, tableDataThird.value);
      // 基数定额记取范围设置多选框
      break;
    case 'tableSelectCom':
      row.children && row.children.length > 0
        ? fatherCheck(row, selectComData.value)
        : childCheck(row, selectComData.value);
      childCheck(row, selectComData.value);
      fatherCheck(row, selectComData.value);
    case 'qdisCheck':
      console.log('tableDataFourth', tableDataFourth.value);
      if (row.isCheck) {
        //具体清单记取只可以选择一项
        tableDataFourth.value.map(item =>
          item.type === '清' &&
          item.sequenceNbr !== row.sequenceNbr &&
          item.isCheck
            ? (item.isCheck = false)
            : ''
        );
      }
      break;
  }
};

//父节点选中取消-子节点都随父选中取消
const fatherCheck = (father, tableData) => {
  console.log('fatherCheck', father);
  // debugger;
  if (!['清', '定', '部'].includes(father.type)) {
    tableData.map(item => (item.isCheck = father.isCheck));
    return;
  }
  let childList = tableData.filter(
    item => item.parentId === father.sequenceNbr
  );
  if (childList.length === 0) return;
  if (childList.length > 0) {
    childList.map(item => {
      item.isCheck = father.isCheck;
      fatherCheck(item, tableData);
    });
  }
};

//子节点选中取消-判断父节点是否需要选中取消操作
const childCheck = (child, tableData) => {
  if (
    tableData.filter(item => item.sequenceNbr === child.parentId).length === 0
  )
    return;
  let childFather = tableData.filter(
    item => item.sequenceNbr === child.parentId
  )[0];
  let childList = tableData.filter(
    item => item.parentId === childFather.sequenceNbr
  );
  if (childList.filter(item => item.isCheck === false).length === 0) {
    childFather.isCheck = true;
    childCheck(childFather, tableData);
  } else {
    childFather.isCheck = false;
    childCheck(childFather, tableData);
  }
};

//基数定额框章节列表
const getChapterList = () => {
  let formData = {
    feeCode: currentRow.value.feeCode,
    azType: deBookValue.value,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  chapterList.value = [];
  api.chapterDropDownBox(formData).then(res => {
    if (res.status === 200 && res.result) {
      res.result &&
        res.result.map(item => {
          chapterList.value.push({
            value: item === '全部章节' ? '' : item,
            label: item,
          });
        });
      chapterValue.value = chapterList.value[0].value;
      getBaseDeList();
    }
  });
};

//获取基数定额记取详情弹框安装专业下拉列表数据
const setlocationModel = feeCode => {
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  let formData = {
    feeCode: feeCode,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  deBookList.value = [];
  api.deBookDropDownBox(formData).then(res => {
    console.log('获取基数定额计取范围', formData, res);
    if (res.status === 200 && res.result) {
      res.result &&
        res.result.map(item => {
          deBookList.value.push({
            value: item === '全部专业' ? '' : item,
            label: item,
          });
        });
      deBookValue.value = deBookList.value[0].value;
      getChapterList();
    }
  });
  locationModel.value = true;
};

//获取基数定额记取详情表格数据
let table3SelectList = reactive([]);
const getBaseDeList = async () => {
  currentRow.value = upTable.value.getCurrentRecord();
  let formData = {
    feeCode: currentRow.value.feeCode,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    azType: deBookValue.value,
    zjType: chapterValue.value,
    classLevelList: JSON.parse(JSON.stringify(tableDataSecond.value)),
  };
  await api.baseDeList(formData).then(async res => {
    console.log('基数定额框章节列表', formData, res);
    if (res.status === 200 && res.result) {
      tableDataThird.value = res.result;
      if (
        currentRow.value.notSelectDeList &&
        currentRow.value.notSelectDeList.length > 0
      ) {
        tableDataThird.value.map(item => {
          if (item.type === '定') {
            item.isCheck = !currentRow.value.notSelectDeList.includes(
              item.sequenceNbr
            )
              ? true
              : false;
            childCheck(item, tableDataThird.value);
          }
        });
      } else if (!isEditInput.value) {
        tableDataThird.value.map(item => (item.isCheck = true));
      } else if (isEditInput.value) {
        let select = upTable.value.getCurrentRecord();
        console.log(
          table1current.value,
          currentRow.value,
          select,
          table3SelectList
        );

        let tar = table3SelectList.find(
          i => i.table1row.feeCode === select.feeCode
        );
        if (tar) {
          await tableDataThird.value.forEach(i => {
            i.isCheck = tar.selectList.find(
              a => a.sequenceNbr === i.sequenceNbr
            )
              ? true
              : false;
          });
        } else {
          tableDataThird.value.map(item => (item.isCheck = true));
        }
      }
      tableNo3.value.reloadData(tableDataThird.value);
      console.log('安装费用----基数定额列表查询', tableDataThird.value);
    }
  });
};
const radioChange = () => {
  //全选反选-只针对定额
  if (radioListValue.value === 1) {
    tableDataThird.value.map(item =>
      item.type === '定'
        ? ((item.isCheck = true), childCheck(item, tableDataThird.value))
        : ''
    );
  } else {
    tableDataThird.value.map(item =>
      item.type === '定'
        ? ((item.isCheck = !item.isCheck),
          childCheck(item, tableDataThird.value))
        : ''
    );
  }
};

//具体清单表格数据查询
let isSetAllQDid = ref(false); //是否设置全部下表格具体清单
const getDetailQD = row => {
  console.log('selectTypeValue.value', selectTypeValue.value, row);
  selectTypeValue.value = upTable.value.getCurrentRecord();
  if (selectTypeValue.value.type === 4) return; //计取方式为分部分项时不可打开具体清单弹框
  if (table2current.value) {
    editInfo.value = true;
    return;
  }
  detailModel.value = true;
  table2DetailRow.value = row;
  isSetAllQDid.value = false; //是否设置下表格全部数据
  if (!row) {
    currentRow.value = upTable.value.getCurrentRecord();
    row = tableDataSecond.value[0];
    isSetAllQDid.value = true;
  }
  let formData = {
    feeCode: currentRow.value.feeCode,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    type: currentRow.value.type,
    qdId: row.isDefaultRow.relationListId,
  };
  api.qdList(formData).then(res => {
    if (res.status === 200 && res.result) {
      tableDataFourth.value = res.result;
      let select = upTable.value.getCurrentRecord();
      let select2 = tableNo2.value.getCurrentRecord();
      let tar = table4SelectList.find(
        i =>
          i.table1row.feeCode === select.feeCode &&
          i.table2row.sequenceNbr === select2.sequenceNbr
      );
      if (isEditInput.value && tar) {
        tableDataFourth.value.forEach(i => {
          i.isCheck = tar.selectList.find(a => a.sequenceNbr === i.sequenceNbr)
            ? true
            : false;
        });
      } else {
        let selected = tableDataFourth.value.find(a => a.isCheck);
        console.log('tableDataFourth.value', tableDataFourth.value, row);
        if (!selected && !isSetAllQDid.value) {
          let selectItem = tableDataFourth.value.find(
            a => a.sequenceNbr === row.relationListId
          );
          if (selectItem) selectItem.isCheck = true;
        }
        if (selected) selected.isCheck = true;
      }
      tableNo4.value.reloadData(tableDataFourth.value);
    } else {
      tableDataFourth.value = [];
    }
  });
};
const rowClassName = ({ row }) => {
  let ClassStr = '';
  if (row.kind === '0') {
    ClassStr = 'row-unit';
  } else if (row.kind === '01' || row.kind === '02') {
    ClassStr = 'row-sub';
  } else if (row.kind === '03') {
    ClassStr = 'row-qd';
  }
  if (row.parentId == 1) {
    return `row-tree-title  ${ClassStr}`;
  } else {
    return ClassStr;
  }
};
const selectChangeEvent = ({ checked, row }) => {
  row.isCheck = checked;
};
const selectBtnChange = type => {
  console.log('selectBtnChange', type);
  if (type === '1') {
    defineCheckRowKeys.value = selectComData.value.map(item => {
      item.isCheck = 1;
      return item.idx;
    });
  } else {
    //反选
    let noChildList = selectComData.value.filter(a => !a.childrenList?.length);
    noChildList.map(a => (a.isCheck = !a.isCheck));
    let isCheckList = noChildList.filter(a => a.isCheck);
    let selectKeysList = isCheckList.map(item => {
      return item.idx;
    }); //设置选中行
    defineCheckRowKeys.value = selectKeysList;
    let list = [...isCheckList];
    let noCheckParlist = selectComData.value.filter(
      a => a.childrenList?.length > 0
    );
    let parIdx = null;
    noCheckParlist.map(a => {
      if (parIdx !== a.idx) {
        parIdx = a.idx;
        let childs = getChildList(a, []);
        let childs2 = childs.filter(b => !b.childrenList?.length);
        let childs3 = childs2.map(item => {
          return item.idx;
        });
        if (childs3.every(b => selectKeysList.includes(b))) {
          a.isCheck = 1;
          defineCheckRowKeys.value.push(parIdx);
        }
      }
    });
  }
};

const convertSequence = arr => {
  let result = '';
  let start = arr[0].idx;
  let end = arr[0].idx;
  for (let i = arr[0].typeNo; i <= arr[arr.length - 1].typeNo; i++) {
    let list = arr.filter(a => a.typeNo === i);
    if (list.length === 0) return;
    start = list[0].idx;
    if (list.length === 1) {
      if (result === '') {
        result += start;
      } else {
        result += ',' + start;
      }
    } else {
      end = list[list.length - 1].idx;
      if (result === '') {
        result += start + '~' + end;
      } else {
        result += ',' + start + '~' + end;
      }
    }
  }
  return result;
};
let disNoList = [];
let table2SelectZJ = ref([]);
let comTableLoading = ref(false);
const sureSelectCompute = () => {
  let type = 0;
  let list = [];
  // let checkList = selectComTable.value.getCheckboxRecords();
  // let checkList = selectComData.value.filter(a => a.isCheck);
  let checkList = selectComData.value.filter(a =>
    defineCheckRowKeys.value.includes(a.idx)
  );
  let treeList = [...selectComData.value];
  if (checkList.find(a => a.idx === '0')) {
    //最外层父级选中
    let childList = checkList.filter(a => a.parentId === '0');
    childList.forEach(a => {
      list.push({
        typeNo: type,
        idx: a.idx,
        name: a.name,
      });
    });
  } else {
    treeList.map((i, idx) => {
      if (checkList.find(a => a.idx === i.idx)) {
        // debugger;
        if (i.childrenList?.length > 0) {
          let childAllList = getChildList(i, []);
          // debugger;
          treeList.splice(idx + 1, childAllList.length - 1);
          //删除选中目标的子级
        }
        let isLastCheck = checkList.find(a => a.idx === treeList[idx - 1].idx); //上一项选中且前面没有push进去当前项的父级
        let now = i.idx.split('.');
        let last = treeList[idx - 1].idx.split('.');
        let lastSameLength = last.slice(0, now.length);
        let isXL =
          now[now.length - 1] - lastSameLength[lastSameLength.length - 1] === 1;
        if (isLastCheck && now.length === last.length && isXL) {
          i.typeNo = treeList[idx - 1].typeNo;
        } else {
          type++;
          i.typeNo = type;
        }
        list.push({
          typeNo: i.typeNo,
          idx: i.idx,
          name: i.name,
          parentId: i.parentId,
        });
      }
    });
  }
  // debugger;
  let result = convertSequence(list);
  let tableNoRow = tableNo2.value.getCurrentRecord();
  let isFind = table2SelectZJ.value.find(
    a =>
      a.tab2DefaultRowSeq === tableNoRow.isDefaultRow.sequenceNbr &&
      a.feeCode === upTable.value.getCurrentRecord().feeCode
    // &&a.type === upTable.value.getCurrentRecord().type
  );
  if (isFind) {
    isFind.classLevel2 = result;
    isFind.selectList = list;
  } else {
    table2SelectZJ.value.push({
      classLevel2: result,
      selectList: list,
      // type: upTable.value.getCurrentRecord().type,
      feeCode: upTable.value.getCurrentRecord().feeCode,
      tab2DefaultRowSeq: tableNoRow.isDefault,
    });
  }
  setTable2Select();
  console.log(result, list, 'tableNoRow', tableNoRow, table2SelectZJ.value);
  // console.log('sureSelectCompute--确定', list, disNoList);
  selectCompute.value = false;
};
const setTable2Select = (isSet = false) => {
  //设置下表格对应章节选择isSet--设置每条数据
  if (!isSet) {
    let tableNoRow = tableNo2.value.getCurrentRecord();
    let isFind = table2SelectZJ.value.find(
      a =>
        a.tab2DefaultRowSeq === tableNoRow.isDefault &&
        a.feeCode === upTable.value.getCurrentRecord().feeCode
      // &&a.type === upTable.value.getCurrentRecord().type
    );
    if (isFind) {
      tableNoRow.isDefaultRow.classLevel2 = isFind.classLevel2;
      tableNoRow.deList.find(
        c => c.sequenceNbr === tableNoRow.isDefault
      ).classLevel2 = isFind.classLevel2;
      //设置表格一的数据
      let target = currentRow.value.classLevelList.find(
        a => a.isDefault === tableNoRow.isDefault
      );
      target.isDefaultRow.classLevel2 = tableNoRow.isDefaultRow.classLevel2;
    }
  } else {
    tableDataSecond.value.map(a => {
      let isFind = table2SelectZJ.value.find(
        b =>
          b.tab2DefaultRowSeq === a.isDefault &&
          b.feeCode === upTable.value.getCurrentRecord().feeCode
        // &&b.type === upTable.value.getCurrentRecord().type
      );
      if (isFind) {
        a.isDefaultRow.classLevel2 = isFind.classLevel2;
        a.deList.find(c => c.sequenceNbr === a.isDefault).classLevel2 =
          isFind.classLevel2;
      }
    });
  }
};
let selectComData = ref([]);
let selectComTable = ref();
let defineCheckRowKeys = ref([]); //初次打开木人选中行
let expandedRowKeys = ref([]);
const rowClassNameTree = (row, index, data) => {
  let ClassStr = 'normal-info';
  if (row.idx == data[0]?.idx) {
    ClassStr += ' first-row';
  }
  return ClassStr;
};
const customCell = ({ record: row, rowIndex, column }) => {
  let className = '';
  let style = {};
  let level = 0;
  for (let i = 1; i <= 7; i++) {
    if (row[`classifyLevel${i}`]) level++;
  }
  row.customLevel = level;
  if (['isCheck'].includes(column.dataIndex) && row.childrenList?.length > 0) {
    className +=
      `Virtual-pdLeft-s${row.customLevel} ` +
      `Virtual-pdLeft-ag-s${row.customLevel} `;
  }
  if (['isCheck'].includes(column.dataIndex) && !row.childrenList?.length) {
    className += `Virtual-pdLeft-ag-noChild-s${row.customLevel} `;
  }
  if (['isCheck'].includes(column.dataIndex) && row.childrenList?.length > 0) {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line}`;
  }
  // 添加默认两行类名
  if (['isCheck'].includes(column.dataIndex) && !row.childrenList?.length) {
    const line = row.maxLine || 2;
    className += ` cell-line-break-${line}`;
  }
  return { style: style, class: className };
};
const customRow = record => {
  // let className = '';
  // let style = {};
  // let level = 0;
  // let row = record;
  // for (let i = 1; i <= 7; i++) {
  //   if (row[`classifyLevel${i}`]) level++;
  // }
  // row.customLevel = level;
  // if (row.childrenList?.length > 0) {
  //   className += `Virtual-ag-row-s${row.customLevel} `;
  // } else {
  //   className += `Virtual-ag-row-noChild-s${row.customLevel} `;
  // }
  // return { style: style, class: className };
};
let selectedRowList = ref([]);
const onSelectChange = (selectedRowKeys, selectedRows) => {
  defineCheckRowKeys.value = selectedRowKeys;
  selectedRowList.value = selectedRows;
};
const setSelectIScheck = (record, selected) => {
  console.log(record, selected);
  if (record.childrenList?.length > 0) {
    let tarList = getChildList(record, []);
    tarList.map(a => (a.isCheck = selected ? 1 : 0));
  } else {
    record.isCheck = selected ? 1 : 0;
  }

  console.log(selectComData.value);
};
let tableOptions = reactive({
  columns: [
    {
      title: '',
      dataIndex: 'isCheck',
      align: 'left',
      width: '25%',
    },
    {
      title: '专业/章节',
      dataIndex: 'name',
      align: 'left',
      width: '75%',
      ellipsis: true,
    },
  ],
  data: [],
});
const expandFun = (type, target) => {
  //展开收起设置
  let list = getChildList(target, []);
  let index = expandedRowKeys.value.findIndex(a => a === target.idx);
  if (type === 1) {
    //收起
    expandedRowKeys.value.splice(index, list.length);
  } else {
    let idxList = list.map(item => {
      return item.idx;
    });
    expandedRowKeys.value.splice(index, 0, ...idxList);
  }
};
const getChildList = (tar, tarList) => {
  //获取目标子列表数据
  tarList.push(tar);
  if (tar.childrenList && tar.childrenList.length > 0) {
    tar.childrenList.map(i => {
      getChildList(i, tarList);
    });
  }
  return tarList;
};
const openSelectCom = item => {
  // selectComData.value = [];
  defineCheckRowKeys.value = []; //清除默认选中行-warning
  selectCompute.value = true;
  comTableLoading.value = true;
  currentRow.value = upTable.value.getCurrentRecord();
  let tableNoRow = tableNo2.value.getCurrentRecord();
  let formData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    chapterStr: tableNoRow.isDefaultRow.classLevel2,
    fascicleStr: tableNoRow.classLevel1Name,
  };
  // console.log('formData', formData);
  api
    .queryBaseDeChapter(formData)
    .then(res => {
      if (res.status === 200 && res.result) {
        let treeData = xeUtils.toTreeArray([res.result], {
          children: 'childrenList',
        });
        tableOptions.data = [res.result];
        selectComData.value = treeData;
        expandedRowKeys.value = treeData.map(item => {
          return item.idx;
        }); //展开所有行数据
        let isCheckList = treeData.filter(a => a.isCheck);
        let selectKeysList = isCheckList.map(item => {
          return item.idx;
        }); //设置选中行
        setTimeout(() => {
          defineCheckRowKeys.value = selectKeysList;
        }, 100);

        console.log(
          '安装费用----基数定额列表查询',
          selectComData.value,
          res.result
        );
      }
    })
    .finally(() => {
      comTableLoading.value = false;
    });
};
//设置应用范围
const treeData = shallowRef([]);
let rightInfo = shallowReactive({
  checkedKeys: [],
  expandedKeysRight: '',
});
const initTreeData = shallowRef([]);

const getList = () => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
    res => {
      // debugger;
      const expandedKeys = [];
      res.result.forEach(e => {
        if (e.levelType != 3) {
          expandedKeys.push(e.id);
        }
        //暂时注释-原只有单位设置专业可勾选
        // if (!e.constructMajorType) {
        //   e.disabled = true;
        // }
      });
      rightInfo.expandedKeysRight = expandedKeys;
      rightInfo.checkedKeys = [store.currentTreeInfo?.id];
      initTreeData.value = res.result.filter(a => a.levelType === 3); //单位
      treeData.value = xeUtils.toArrayTree(res.result, {
        parentKey: 'parentId',
        childrenKey: 'children',
      });
      console.log('安装-treeData.value', treeData.value);
    }
  );
};
const selectSameMajor = e => {
  console.log('selectSameMajor', rightInfo, initTreeData.value);
  if (rightInfo.checkedKeys?.length === 0) {
    message.info('请选择单位工程');
    return;
  }
  let selectMajorList = [];
  let selectRows = initTreeData.value.filter(a =>
    rightInfo.checkedKeys.includes(a.id)
  );
  let list = [];
  initTreeData.value.map(a => {
    if (
      selectRows.find(
        d => d.constructMajorType === a.constructMajorType && a.levelType === 3
      )
    )
      list.push(a.id);
  });
  rightInfo.checkedKeys = [...list];
};
</script>
<style lang="scss" scoped>
@use './tableIcon.scss';
.contentAll {
  height: 100%;
  width: 83%;
  .selectContent {
    height: 40px;
    line-height: 40px;
    span {
      margin: 0 10px 0 0;
    }
    .btnAdd,
    .btnSub {
      width: 30px !important;
      display: inline-block;
      height: 30px !important;
      line-height: 30px;
      cursor: pointer;
      margin: 0 !important;
    }
    .fontsingle {
      margin: 0 10px;
    }
  }
  .table-content {
    .tableNo1 {
      width: 70%;
    }
    .tableNo2 {
      width: 98%;
      margin: 10px 0 0px;
    }
    .detailQD {
      display: inline-block;
      width: 190px;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }
    .detailTitle {
      margin-right: 15px;
    }
  }
}
.asideTree {
  width: 17%;
  border: 1px solid #d9d9d9;
  ::v-deep .ant-tree {
    font-size: 12px;
    overflow: auto;
    height: 395px;
  }
  &-title {
    padding: 5px 10px 0;
    display: flex;
    justify-content: space-between;
  }
  &-content {
    overflow-x: scroll;
  }
  .check-labels {
    white-space: nowrap;
  }
  ::-webkit-scrollbar {
    //滚动条的宽度
    width: 4px; //纵向滚动条的宽度
    height: 6px; //横向滚动条的高度
    background-color: transparent;
  }
}
.tableNo3 {
  height: 350px;
  .radioList {
    float: right;
    margin-top: 10px;
  }
}
.tableNo4 {
  height: 370px;
}
.btnsOut {
  width: 10%;
  display: flex;
  margin: 10px auto 0;
  justify-content: space-around;
}
.btns {
  width: 10%;
  display: flex;
  margin: 10px auto 0;
  justify-content: space-around;
}
.line {
  width: 100%;
  height: 7px;
  background: rgba(221, 221, 221, 0.39);
  opacity: 0.52;
  margin: 20px 0;
}
.detail {
  width: 73%;
}
.detailBtn {
  position: absolute;
  right: 5px;
  bottom: 2px;
}
.ComputeContent {
  width: 100%;
  height: 70%;
  position: relative;
}
.selectRightBtn {
  position: absolute;
  bottom: -27px;
  left: 20px;
}
.selectBtn {
  display: flex;
  position: absolute;
  bottom: -27px;
  right: 20px;
}

::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2;
}
::v-deep(.vxe-table .row-qd) {
  background: rgba(166, 195, 250, 0.2);
}
::v-deep(.vxe-body--row.row--current) {
  background: #a6c3fa;
}
:deep(.ant-input-number-input) {
  padding: 0 0px 0 3px;
}
::v-deep .table-content .ant-btn-primary {
  margin: 10px !important;
}
::v-deep .detail .ant-btn-primary {
  margin: 0 0 !important;
}
::v-deep .ant-input-group-addon {
  padding: 0 0 !important;
}

::v-deep(.vxe-select > .vxe-input) {
  //在原来基础上减去随机数1px,解决了小屏中vxe-select挡住表格边线问题
  $mini-hight: calc(var(--vxe-input-height-mini) - 1);
  height: $mini-hight;
  line-height: $mini-hight;
  .vxe-input--inner {
    border: none !important;
  }
}
::v-deep .ant-input-number-handler-wrap {
  display: none;
}
::v-deep .ant-input-number-group-addon {
  padding: 0px;
}
.advanced-content {
  .single {
    border: 1px solid #d9d9d9;
    font-size: 14px;
    padding: 15px 0 15px 15px;
    margin-bottom: 12px;
    position: relative;
    .title {
      position: absolute;
      font-size: 12px;
      color: #287cfa;
      top: -10px;
      background: white;
      .icon-font {
        margin-right: 6px;
      }
    }
    .vxe-checkbox {
      color: black;
    }
  }
  .single:nth-of-type(2) {
    margin-top: 20px;
  }
  .radioList {
    .ant-radio-wrapper {
      display: block;
      font-size: 12px;
    }
  }
}
.select-type {
  ::v-deep .ant-select-selector {
    .ant-select-selection-item {
      font-size: 13px;
    }
  }
  &-jtx {
    display: flex;
    justify-content: space-between;
    padding-right: 20px;
    margin-top: 17px;
    &-ipt {
      display: inline-block;
      border: 1px solid #bfbfbf;
      margin: 0;
      width: calc(100% - 60px);
      height: 24px;
      padding-left: 10px;
      line-height: 20px;
      &-disSpan,
      &-span {
        // margin-right: 5px;
        display: inline-block;
        width: 20px;
        height: 100%;
        float: right;
        text-align: center;
      }
      &-text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 248px;
        display: inline-block;
      }
      &-disSpan {
        background: #d9d9d9;
      }
      &-span {
        cursor: pointer;
      }
    }
  }
}
</style>
