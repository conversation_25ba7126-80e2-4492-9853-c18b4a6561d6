const MathItemHandler = require("./mathItemHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");

/**
 * ${"type": "DE","dst": "R","math": "-a","unit":"m2","base":{"id":"a","math": "*0.3","source":"DE","prop": "GCL"}}$
 */
class A$$VMathHandler extends MathItemHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        let jsonStr = mathItem.math.slice(1, -1);
        let mathObj = JSON.parse(jsonStr);
        mathItem.mathObj = mathObj;
    }

    async activeRCJ() {

    }
    async computeResQty() {
        let mathObj = this.mathItem.mathObj
        // 计算 base 里每个 id 的值
        const idValueMap = {};
        [mathObj.base].forEach(item => {
            const qty = this.getQty(item.source);
            const expr = `${qty}${item.math}`;
            idValueMap[item.id] = eval(expr);
        });

        // 计算外层 math 的值
        let outerExpr = mathObj.math;
        let deQuantity = this.getDeQuantity(mathObj.type, mathObj.dst);
        for (const id in idValueMap) {
            outerExpr = outerExpr.replace(new RegExp(id, 'g'), idValueMap[id]);
        }
        const result = eval(outerExpr);
    }

    getDeQuantity(type, dst) {
        //  TODO
        if (type === 'DE') {

            return 100;
        }

        if (type === 'RCJ') {
            return 100;
        }
    }

    getQty(source) {
        // 这里假设所有 source 的 qty 都是 100
        return 100;
    }
}

module.exports = A$$VMathHandler;