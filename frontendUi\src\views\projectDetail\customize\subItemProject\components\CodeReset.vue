<template>
  <common-modal
    className="dialog-comm"
    width="500"
    v-model:modelValue="show"
    title="编码重刷"
  >
    <div class="code-reset-content">请选择编码重刷范围：</div>
    <div class="code-reset-select">
      <a-radio-group v-model:value="refreshType">
        <a-radio :value="1">整体工程项目</a-radio>
        <a-radio :value="2">当前单位工程</a-radio>
      </a-radio-group>
    </div>
    <div style="text-align: center">
      <a-button @click="close" style="margin-right: 20px">取消 </a-button>
      <a-button type="primary" @click="batchRefresh"> 确定</a-button>
    </div>
  </common-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { computed } from '@vue/reactivity';
const emits = defineEmits(['refreshCallback', 'update:visible']);
const projectStore = projectDetailStore();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const show = computed({
  get: () => props.visible,
  set: val => {
    emits('update:visible', val);
  },
});
const close = () => {
  emits('update:visible', false);
};
let refreshType = ref(2);
const batchRefresh = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: refreshType.value,
  };
  console.log('编码冲刷参数', apiData);
  api.batchRefresh(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('项目编码重刷成功');
      emits('refreshCallback');
      close();
    }
  });
};
</script>
<style lang="scss" scoped>
.code-reset-content {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.code-reset-select {
  padding: 15px 0 40px;
  display: flex;
  align-items: center;
  font-size: 14px;
}
</style>
