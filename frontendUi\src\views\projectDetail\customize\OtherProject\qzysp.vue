<template>
  <div class="table-content">
    <child-page-table
      :pageType="'qzysp'"
      :columnList="columnList"
    ></child-page-table>
  </div>
</template>

<script setup>
import ChildPageTable from '@/views/projectDetail/customize/OtherProject/childPageTable.vue';

const columnList = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'type',
    title: '类别',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'type_edit' },
  },
  {
    field: 'project',
    title: '签证及索赔项目',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'project_edit' },
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'amount',
    title: '数量',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'amount_edit' },
  },
  {
    field: 'zhPrice',
    title: '综合单价',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'zhPrice_edit' },
  },
  {
    field: 'total',
    minWidth: 80,
    title: '合价',
    // editRender: { autofocus: '.vxe-input--inner' },
    // slots: { edit: 'quantity_edit' },
  },
  {
    field: 'qzspRelyOn',
    title: '签证及索赔依据',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'qzspRelyOn_edit' },
  },
];
</script>

<style scoped></style>
