import {
  ref
} from '@vue/reactivity';

export const humanOriginalTableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 40,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8], // 类型 0 所有人材机 1 人工调差 2 材料调差 3 机械调差 8 暂估价材料调差
    adjustmentType: [1, 2, 3, 4], // 4种调整法 1 造价信息价格差额调整法 2 结算价与基期价差额调整法 3 结算价与合同价差额调整法 4 价格指数差额调整法
    stageType: [0, 1], // 0 不分期 1 分期
    titleType: [0, 1, 2, 3], // 0 不分期时材料标题 1 材料标题 2 分期明细 3 工程项目级别材料标题
    fixed: 'left',
  },
  {
    title: '材料编码',
    field: 'materialCode',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    fixed: 'left',
    titleType: [0, 1, 2, 3],
  },
  {
    title: '类型',
    field: 'type',
    width: 80,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    fixed: 'left',
    titleType: [0, 1, 2, 3],
  },
  {
    title: '名称',
    field: 'materialName',
    width: 150,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    fixed: 'left',
    titleType: [0, 1, 2, 3],
  },
  {
    title: '规格型号',
    field: 'specification',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '单位',
    field: 'unit',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '合同数量',
    field: 'jieSuanTotalNumber',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '合同预算价',
    field: 'dePrice',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: false,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '合同/确认单价',
    field: 'marketPrice',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '合同合价',
    field: 'total',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '变值权重B',
    field: 'jieSuanValuetWeightB',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '基本价格指数F0',
    field: 'jieSuanBasePriceF0',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [4],
    stageType: [0, 1],
    titleType: [0, 2, 3],
  },
  {
    title: '现行价格指数Ft',
    field: 'jieSuanCurrentPriceF0',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [4],
    stageType: [0, 1],
    titleType: [0, 2, 3],
  },
  {
    title: '基期价',
    field: 'jieSuanBasePrice',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [1, 2],
    stageType: [0, 1],
    titleType: [0, 2, 3],
  },
  {
    title: '基期价来源',
    field: 'jieSuanBasePriceSource',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: false,
    type: [1, 2, 3],
    adjustmentType: [1, 2],
    stageType: [0, 1],
    titleType: [0, 2, 3],
  },
  {
    title: '基期价浮动率(%)',
    field: 'basePriceFloatRate',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: false,
    type: [1, 2, 3],
    adjustmentType: [1],
    stageType: [0, 1],
    titleType: [0, 2, 3],
  },
  {
    title: '第n期单价',
    field: 'jieSuanStagePrice',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '第n期调差工程量',
    field: 'jieSuanStageDifferenceQuantity',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '第n期单价来源',
    field: 'jieSuanStagePriceSource',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '结算单价',
    field: 'jieSuanPrice',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '调差工程量',
    field: 'jieSuanDifferenceQuantity',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    titleType: [0, 1, 3],
  },
  {
    title: '结算单价来源',
    field: 'jieSuanPriceSource',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: false,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0],
    titleType: [0, 2, 3],
  },
  {
    title: '合同市场价合计',
    field: 'jieSuanTotal',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    adjustmentType: [1],
    stageType: [0],
    titleType: [2],
  },
  {
    title: '合同除税系数(%)',
    field: 'taxRemoval',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    stageType: [0],
    titleType: [2],
  },
  {
    title: '合同进项税额',
    field: 'jxTotal',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    stageType: [0],
    titleType: [2],
  },
  {
    title: '结算数量',
    field: 'totalNumber',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    stageType: [0],
    titleType: [2],
  },
  {
    title: '结算市场价合计',
    field: 'total',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    stageType: [0],
    titleType: [2],
  },
  {
    title: '结算进项税额',
    field: 'jieSunJxTotal',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    stageType: [0],
    titleType: [2],
  },
  {
    title: '供货方式',
    field: 'ifDonorMaterial',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {},
    initialize: false,
    type: [0, 1, 2, 3],
    adjustmentType: [],
    stageType: [],
  },
  {
    title: '甲供数量',
    field: 'donorMaterialNumber',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    type: [0, 1, 2, 3],
    adjustmentType: [],
    stageType: [],
  },
  {
    title: '保管费率(%)',
    field: 'jieSuanAdminRate',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    type: [0, 1, 2, 3],
    adjustmentType: [],
    stageType: [],
  },
  {
    title: '是否调差',
    field: 'isDifference',
    width: 100,
    align: 'center',
    slot: true,
    initialize: true,
    type: [0, 1, 2, 3],
    adjustmentType: [],
    stageType: [],
  },
  {
    title: '是否暂估',
    field: 'ifProvisionalEstimate',
    width: 100,
    align: 'center',
    slot: true,
    initialize: false,
    type: [0, 1, 2, 3],
    adjustmentType: [],
    stageType: [],
  },
  {
    title: '风险幅度范围(%)',
    field: 'riskAmplitudeRangeMin',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [1, 2, 3],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '单价涨/跌幅(%)',
    field: 'jieSuanPriceLimit',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [1, 2, 3],
    stageType: [0],
    titleType: [0, 1, 3],
  },
  {
    title: '第n期单价涨/跌幅(%)',
    field: 'jieSuanPriceLimit',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '第n期单位价差',
    field: 'jieSuanUnitPriceDifferenc',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '第n期价差合计',
    field: 'jieSuanStagePriceDifferencSum',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '第n期除税系数(%)',
    field: 'jieSuanStagetaxRemoval',
    width: 140,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '第n期价差进项税额',
    field: 'jieSuanStagePriceDifferencInputTax',
    width: 160,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [1],
    titleType: [2],
  },
  {
    title: '单位价差',
    field: 'jieSuanPriceDifferenc',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '价差合计',
    field: 'jieSuanPriceDifferencSum',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0, 1],
    titleType: [0, 1, 3],
  },
  {
    title: '结算除税系数(%)',
    field: 'jieSuanTaxRemoval',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: false,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '结算价差进项税额',
    field: 'settlementPriceDifferencInputTax',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0, 1],
    titleType: [0, 1, 3],
  },
  {
    title: '取费',
    field: 'jieSuanFee',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {},
    classType: 1,
    initialize: true,
    type: [1, 2, 3, 8],
    adjustmentType: [1, 2, 3],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
  {
    title: '备注',
    field: 'remark',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    type: [0, 1, 2, 3, 8],
    adjustmentType: [1, 2, 3, 4],
    stageType: [0, 1],
    titleType: [0, 1, 2, 3],
  },
]);
export const humanTableColumns = ref([
  {
    title: '序号',
    field: 'dispNo',
    width: 40,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [1, 2], // 1 所有人材机数据 2 价差数据
    fixed: 'left',
  },
  {
    title: '材料编码',
    field: 'materialCode',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: true,
    fixed: 'left',
    classify: [1, 2],
  },
  {
    title: '类型',
    field: 'type',
    width: 80,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    fixed: 'left',
    classify: [1, 2],
  },
  {
    title: '名称',
    field: 'materialName',
    width: 150,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    fixed: 'left',
    classify: [1, 2],
  },
  {
    title: '规格型号',
    field: 'specification',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [1, 2],
  },
  {
    title: '单位',
    field: 'unit',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: true,
    classify: [1, 2],
  },
  {
    title: '数量',
    field: 'totalNumber',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: true,
    classify: [1, 2],
  },
  {
    title: '预算价',
    field: 'dePrice',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '合同/确认单价',
    field: 'marketPrice',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [1, 2],
  },
  {
    title: '基期价',
    field: 'jieSuanBasePrice',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '结算单价',
    field: 'jieSuanPrice',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '价格来源',
    field: 'sourcePrice',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '是否调差',
    field: 'isDifference',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '结算市场价合计',
    field: 'total',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '除税系数(%)',
    field: 'taxRemoval',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '进项税额',
    field: 'jxTotal',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '供货方式',
    field: 'ifDonorMaterial',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {},
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '甲供数量',
    field: 'donorMaterialNumber',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '保管费率(%)',
    field: 'jieSuanAdminRate',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '输出标记',
    field: 'outputToken',
    width: 100,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '产地',
    field: 'producer',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '厂家',
    field: 'manufactor',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '是否汇总(二次解析)',
    field: 'markSum',
    width: 130,
    align: 'center',
    slot: true,
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '质量等级',
    field: 'qualityGrade',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '品牌',
    field: 'brand',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: false,
    classify: [1],
  },
  {
    title: '送达地点',
    field: 'deliveryLocation',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [1],
  },
  {
    title: '风险幅度范围(%)',
    field: 'riskAmplitudeRangeMin',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '单价涨/跌幅(%)',
    field: 'jieSuanPriceLimit',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '单位价差',
    field: 'jieSuanPriceDifferenc',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '价差合计',
    field: 'jieSuanPriceDifferencSum',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '结算除税系数(%)',
    field: 'jieSuanTaxRemoval',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '结算价差进项税额',
    field: 'settlementPriceDifferencInputTax',
    width: 100,
    align: 'center',
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '取费',
    field: 'jieSuanFee',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {},
    classType: 1,
    initialize: true,
    classify: [2],
  },
  {
    title: '备注',
    field: 'remark',
    width: 100,
    align: 'center',
    slot: true,
    editRender: {
      autofocus: '.vxe-input--inner',
    },
    classType: 1,
    initialize: true,
    classify: [1, 2],
  },
]);
