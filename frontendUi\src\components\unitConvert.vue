<!--
 * @Descripttion: 单位转换弹窗
 * @Author: sunchen
 * @Date: 2023-12-28 17:33:47
 * @LastEditors: wangru
 * @LastEditTime: 2024-11-26 10:30:57
-->
<template>
  <common-modal
    className="dialog-comm"
    title="单位转换设置"
    width="530"
    height="330"
    v-model:modelValue="unitVisible"
    @close="cancel()"
  >
    <div
      style="padding: 0 30px 0"
      v-if="cumPiceInfo"
    >
      <h3 style="margin: 0 0 15px 0">
        待载入的材料与当前材料单位不一致，需设置转换系数后进行操作
      </h3>
      <div class="unitContent">
        <p>
          当前材料单位：<span class="blue">{{ cumPiceInfo.nowUnit }}</span>
        </p>
        <p class="special">
          转换公式：<span class="blue"><a-input-number
              :max="100000000"
              :min="0"
              v-model:value="cumPiceInfo.num"
              placeholder="请输入转换系数"
              style="display: inline-block; width: 130px; margin-right: 5px"
              @change="inputChange"
            />{{ cumPiceInfo.beforeUnit }}</span>
        </p>
        <p>
          待载材料{{isDiffDeType?'价格(元)':"含税市场价(元)"}}：<span class="blue">{{
         cumPiceInfo.marketPrice
          }}</span>
        </p>
        <p>
          转换后{{isDiffDeType?'材料价格(元)':"含税市场价(元)"}}：<span class="red">{{
            cumPiceInfo.marketPriceAfter
          }}</span>
        </p>
      </div>
      <p class="btns">
        <a-button
          @click="cancel()"
          :disabled="loading"
        >取消</a-button>
        <a-button
          type="primary"
          :disabled="loading"
          @click="sureChangePrice"
        >确定</a-button>
      </p>
    </div>
  </common-modal>
</template>
<script setup>
import { ref, watchEffect } from 'vue';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const poops = defineProps({
  priceInfo: {
    type: Object,
    default: () => ({}),
  },
  isDiffDeType: {
    //是否不需要区分含税不含税---true-同一
    type: Boolean,
    default: false,
  },
});

let unitVisible = ref(true);
let cumPiceInfo = ref(null);

watchEffect(() => {
  cumPiceInfo.value = poops.priceInfo;
  console.log('cumPiceInfo.value ', cumPiceInfo.value);
});

const emits = defineEmits(['closeDialog']);

const cancel = (type = null) => {
  unitVisible.value = false;
  console.log(type);
  console.log('cumPiceInfo.value', cumPiceInfo.value);
  emits('closeDialog', type ? cumPiceInfo.value : null);
};

const sureChangePrice = () => {
  if (!cumPiceInfo.value.num) {
    message.error('请输入转换系数');
    return;
  }
  cancel('ok');
};

const inputChange = eve => {
  let price =
    Number(cumPiceInfo.value.num) * Number(cumPiceInfo.value.marketPrice);
  cumPiceInfo.value.marketPriceAfter = price.toFixed(3);
};
</script>

<style lang="scss" scoped>
.unitContent {
  p {
    font-size: 14px;
    margin: 0 0 18px 0;
    color: #000000;
    .blue {
      color: #287cfa;
      margin-left: 5px;
    }
    .red {
      color: #de3f3f;
      margin-left: 5px;
    }
  }
  .special {
    margin: -10px 0 17px 0;
  }
}
.btns {
  margin: auto;
  width: 150px;
  display: flex;
  justify-content: space-between;
}
</style>
