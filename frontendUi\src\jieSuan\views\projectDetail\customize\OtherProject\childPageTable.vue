<!--
 * @Descripttion:其他项目子页面表格
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-25 15:18:13
-->
<template>
  <vxe-grid
    ref="childTable"
    v-bind="gridOptions"
    v-on="gridEvents"
  >
    <template #dispNo_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1"
        :clearable="false"
        v-model.trim="row.dispNo"
        type="text"
        @blur="clear()"
        @keyup="row.dispNo = sortAndlength(row.dispNo, 10)"
      ></vxe-input>
      <span v-else>{{ row.dispNo }}</span>
    </template>
    <template #unit_edit="{ row }">
      <vxeTableEditSelect
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :filedValue="row.unit"
        :list="projectStore.unitListString"
        @update:filedValue="newValue => {
    saveCustomInput(newValue, row, 'unit', $rowIndex);
  }
    "
      ></vxeTableEditSelect>
      <span v-else>{{ row.unit }}</span>
    </template>
    <template #name_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.name"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.name }}</span>
    </template>
    <template #worksName_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.worksName"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.worksName }}</span>
    </template>
    <template #spec_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.specification"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.specification }}</span>
    </template>
    <template #fxName_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.fxName"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.fxName }}</span>
    </template>
    <template #content_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.content"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.content }}</span>
    </template>
    <template #xmje_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        v-model.trim="row.xmje"
        :maxlength="10"
        type="text"
        @blur="(row.xmje = pureNumber(row.xmje, 2)), clear()"
      ></vxe-input>
      <span v-else>{{ row.xmje }}</span>
    </template>
    <template #jiesuanQuant_edit="{ row }">
      <vxe-input
        v-if="(!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        v-model.trim="row.quantitativeExpression"
        :maxlength="1000"
        type="text"
        @blur="clear()"
        @keyup="row.quantitativeExpression = row.quantitativeExpression.replace(/[^\d.\-\+\*\/\{\}\[\]\(\)]/g,'')"
      ></vxe-input>
      <span v-else>{{ row.quantitativeExpression }}</span>
    </template>
    <template #service_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.serviceContent"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.serviceContent }}</span>
    </template>
    <template #jiesuanTotal_edit="{ row }">
      <vxe-input
        v-if="(!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.amount"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.amount }}</span>
    </template>
    <template #jiesuanPrice_edit="{ row }">
      <vxe-input
        v-if=" (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.price"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.price }}</span>
    </template>
    <template #jieSuanFwje_edit="{ row }">
      <vxe-input
        v-if="row.settlementType===3&&(!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.jieSuanFwje"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.jieSuanFwje }}</span>
    </template>
    <template #jiesuanAmount_edit="{ row }">
      <vxe-input
        v-if=" (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.total"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.total }}</span>
    </template>
    <template #jiesuanTaxRemoval_edit="{ row }">
      <vxe-input
        v-if="(!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.taxRemoval"
        type="text"
        @blur="clear()"
      ></vxe-input>
      <span v-else>{{ row.taxRemoval }}</span>
    </template>
    <template #dec_edit="{ row }">
      <vxe-input
        :clearable="false"
        :maxlength="2000"
        v-model.trim="row.description"
        type="text"
        @blur="clear()"
      ></vxe-input>
    </template>
    <template #amount_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        v-model.trim="row.amount"
        :maxlength="10"
        type="text"
        @blur="(row.amount = pureNumber(row.amount, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.amount }}
      </span>
    </template>
    <template #rate_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        v-model.trim="row.rate"
        :maxlength="10"
        type="text"
        @blur="(row.rate = pureNumber(row.rate, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.rate }}
      </span>
    </template>
    <template #price_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        v-model.trim="row.price"
        :maxlength="10"
        type="text"
        @blur="(row.price = pureNumber(row.price, 6)), clear()"
      >
      </vxe-input>
      <span v-else>
        {{ row.price }}
      </span>
    </template>
    <template #taxRemoval_edit="{ row }">
      <vxe-input
        v-if="row.jiesuanOriginal !==1 && (!hasDataType() || (hasDataType() && row.dataType === 2))"
        :clearable="false"
        v-model.trim="row.taxRemoval"
        :maxlength="10"
        type="text"
        @blur="(row.taxRemoval = pureNumber(row.taxRemoval, 4)), clear()"
      >
      </vxe-input>
    </template>
    <template #jiesuanMode_default="{ row }">
      <span v-if="row.dataType===2">
        {{ row.jiesuanModeName }}
      </span>
      <span v-else> </span>
    </template>
    <template #jiesuanMode_edit="{ row }">
      <span v-if="row.dataType===1">
        {{ row.jiesuanModeName }}
      </span>
      <vxeTableEditSelect
        v-if="!hasDataType() || (hasDataType() && row.dataType === 2)"
        :filedValue="row.jiesuanModeName"
        :list="jiesuanModeName"
        @update:filedValue="newValue => {
    saveJsCustomInput(newValue, row);
  }
    "
      ></vxeTableEditSelect>
      <span v-else></span>
    </template>
    <template #empty>
      <span style="color: #898989; font-size: 14px; display: block; margin: 25px 0">
        <img :src="getUrl('newCsProject/none.png')" />
      </span>
    </template>
  </vxe-grid>
</template>
<script setup>
import {
  onMounted,
  onUpdated,
  onActivated,
  ref,
  watch,
  reactive,
  getCurrentInstance,
  nextTick,
  toRaw,
} from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import jiesuanApi from '@/api/jiesuanApi';
import qtxmCommon from './qtxmCommon';
import { message, Modal } from 'ant-design-vue';
import { insetBus } from '@/hooks/insetBus';
import { useCellClick } from '@/hooks/useCellClick';
import { useOtherProRaction } from '@/hooks/useOtherJsProRaction';
import operateList from '@/views/projectDetail/customize/operate';
let insertTwo = operateList.value.find(item => item.name == 'insert');
let $table; //全局定义
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
import {
  getUrl,
  pureNumber,
  sortAndlength,
  isNumericExpression,
} from '@/utils/index';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
let isCurrent = ref(null);
let copyInfo = ref(''); //复制数据行
let childTable = ref();
let deleteInfo = ref();
let insertType = ref('');
let jiesuanModeArr = ref([]);
let jiesuanModeName = ref('');

const props = defineProps(['pageType', 'columnList']);
const {
  getOperateParams,
  copyOperate,
  disposeCurrentIndex,
  operateParams,
  getCurrentIndex,
  msgInfo,
  deleteOperate,
  deleteModal,
  menuConfigOptions,
  pasteIsDisabled,
  editCheckLength,
  setInsertType,
  getChildPageData,
  getOperateData,
  hasDataType,
  tabBtnIsValid,
} = useOtherProRaction({ pageType: props.pageType, type: 2 });
const clear = () => {
  //清除编辑状态
  $table.clearEdit();
};
// 获取结算方式下拉菜单
const getjiesuanMode = async () => {
  jiesuanApi.getServiceCostSettlementType({}).then(res => {
    console.log('getServiceCostSettlementType', res);
    if (res.status === 200) {
      let arr = res.result;
      let jiesuanMode = [];
      for (let i in arr) {
        jiesuanMode.push(arr[i].value);
      }
      jiesuanModeArr.value = arr;
      jiesuanModeName.value = jiesuanMode.join(',');
    }
  });
};
// 获取结算方式名称
const getModeName = async id => {
  let modeArr = jiesuanModeArr.value;
  return modeArr.filter(res => res.code === id)[0].value;
};
// 获取结算方式id
const getModeId = async name => {
  if (!name) return;
  let modeArr = jiesuanModeArr.value;
  return modeArr.filter(res => res.value === name)[0].code;
};
const getTableData = async (sequenceNbr = '') => {
  loading.value = true;
  let resData = await getChildPageData(); //获取子页面表格数据
  if (resData.status === 200) {
    loading.value = false;
    let tableArr = resData.result;
    for (let s in tableArr) {
      if (tableArr[s].settlementType) {
        if (tableArr[s].dataType == 2) {
          tableArr[s]['jiesuanModeName'] = await getModeName(
            tableArr[s].settlementType
          );
        } else {
          tableArr[s]['jiesuanModeName'] = '';
        }
      }
    }
    tableData.value = gridOptions.value.data = resData.result;
    console.log('处理后 tableData.value', tableData.value);
    if (tableData.value && tableData.value.length > 0) {
      if (sequenceNbr) {
        getCurrentIndex($table, { sequenceNbr });
      }
      isCurrent.value
        ? $table.setCurrentRow(tableData.value[isCurrent.value])
        : $table.setCurrentRow(tableData.value[0]);
    }
    setInsertType($table, tableData.value);
  } else {
    tableData.value = gridOptions.value.data = [];
  }
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
};
const saveJsCustomInput = async (newValue, row) => {
  row['jiesuanModeName'] = newValue;
  row['jiesuanMode'] = await getModeId(newValue);
};
let isJsType = ref(false);
const editClosedEvent = ({ row, column }) => {
  const field = column.field;
  let value = row[field];
  editCheckLength(row, column, $table);
  if (
    field === 'quantitativeExpression' &&
    row.quantitativeExpression &&
    row.quantitativeExpression.length < 1000
  ) {
    const [isSuccess, msg] = isNumericExpression(row.quantitativeExpression);
    if (isSuccess) {
      $table.revertData(row, 'quantitativeExpression');
      message.warn('数量表达式输入非法，请重新输入');
      return;
    }
  }
  // 判断单元格值没有修改
  if ($table.isUpdateByRow(row, field)) {
    // if (['jiesuanModeName', 'jieSuanFwje', 'settlementType'].includes(field)) {
    //   isJsType.value = true;
    // } else {
    //   isJsType.value = false;
    // }
    if (['jiesuanModeName', 'settlementType'].includes(field)) {
      row.settlementType = row.jiesuanMode;
    }
    operate('update', row);
  }
};
const menuConfig = reactive({
  ...menuConfigOptions,
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    $table.setCurrentRow(row);
    pasteIsDisabled(row);
    if (hasDataType()) {
      insertType.value = row.dataType;
      projectStore.SET_DATATYPE(insertType.value);
    }
    return true;
  },
});
const contextMenuClickEvent = ({ menu, row }) => {
  menu.code === 'delete'
    ? getCurrentIndex($table)
    : getCurrentIndex($table, row);
  switch (menu.code) {
    case 'copy':
      // 复制
      copyOperate(row, $table);
      message.success('复制成功');
      break;
    case 'delete':
      // 删除
      menuConfig.body.options[0][2].disabled = true;
      deleteOperate(row, $table);
      break;
    case 'paste':
      // 粘贴
      row = { ...projectStore.otherProCopyInfo.copyInfo };
      operate('paste', row);
      break;
    case 'add':
      // 插入
      operate('insert', row);
      break;
    case 'addNull':
      // 插入数据行
      operate('insertData', row);
      break;
    case 'addTitle':
      // 插入标题行
      operate('insertTitle', row);
      break;
  }
};
const gridOptions = ref({
  align: 'center',
  loading: loading.value,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  data: tableData.value,
  height: '100%',
  menuConfig: menuConfig,
  keepSource: true,
  class: 'table-edit-common',
  cellClassName: selectedClassName,
  editConfig: {
    trigger: 'click',
    mode: 'cell',
    beforeEditMethod: cellBeforeEditMethod,
  },
  columns: props.columnList,
});
const currentChange = ({ row }) => {
  if (hasDataType()) {
    insertType.value = row.dataType;
    projectStore.SET_DATATYPE(insertType.value);
    getCurrentIndex($table, row);
  }
};
const gridEvents = ref({
  menuClick: contextMenuClickEvent,
  editClosed: editClosedEvent,
  cellClick: useCellClickEvent,
  currentChange: currentChange,
});
const updateServiceCost = async data => {};
const operate = async (type, row) => {
  getOperateParams(type, row, $table);
  let apiData = toRaw(operateParams.value);
  let resData;
  let zcbfwfData;
  // if (type === 'update' && isJsType.value) {
  //   console.log('apiData', apiData);
  //   zcbfwfData = {
  //     constructId: apiData.constructId,
  //     singleId: apiData.singleId,
  //     unitId: apiData.unitId,
  //     sequenceNbr: apiData.targetSequenceNbr,
  //     settlementType: apiData.projectServiceCost.jiesuanMode,
  //     jieSuanFwje: apiData.projectServiceCost.jieSuanFwje,
  //   };
  //   resData = await jiesuanApi.updateServiceCost(zcbfwfData);
  //   let costData = {
  //     constructId: apiData.constructId,
  //     singleId: apiData.singleId,
  //     unitId: apiData.unitId,
  //   };
  //   // jiesuanApi.countCostCodePrice(costData);
  //   isJsType.value = false;
  // } else {
  resData = await getOperateData(apiData);
  // }
  console.log('resData', resData, apiData, zcbfwfData);
  if (resData.status === 200) {
    isCurrent.value = disposeCurrentIndex.value;
    getTableData();
    message.success(`${msgInfo.value}成功`);
    if (type === 'update') {
      getCurrentIndex($table, row);
    }
  }
};

watch(
  () => projectStore.asideMenuCurrentInfo,
  () => {
    console.log('*****************其他项目子页面', props.pageType);
    if (qtxmCommon.isOtherProChild(props.pageType)) {
      insertTwo.disabled = false;
      getjiesuanMode();
      getTableData();
      insertOperate();
    }
  }
);

watch(
  () => deleteModal.value.isDelete,
  () => {
    if (projectStore.tabSelectName !== '其他项目') return;
    if (
      qtxmCommon.isOtherProChild(props.pageType) &&
      deleteModal.value.isDelete
    ) {
      operate('delete', deleteModal.value.deleteRow);
    }
  }
);
// onActivated(() => {

// });
const insertOperate = () => {
  insetBus(
    bus,
    projectStore.componentId,
    qtxmCommon.getComId(props.pageType),
    async data => {
      if (data.name === 'insert') {
        operate('insert', {});
      } else if (data.name === 'insert-op') {
        if (!data.activeKind) {
          tabBtnIsValid(data.options);
        } else {
          data.activeKind === '01'
            ? operate('insertData')
            : operate('insertTitle');
        }
      } else if (data.name === 'feeExcel') message.info('功能建设中...');
    }
  );
};
onMounted(() => {
  insertTwo.disabled = false;
  window.addEventListener('keydown', keyDownOperate);
  // nextTick(() => {
  getjiesuanMode();
  $table = childTable.value;
  getTableData();
  insertOperate();
  // });
});
const keyDownOperate = event => {
  let select = $table && $table.getCurrentRecord();
  if (
    !select ||
    !qtxmCommon.isOtherProChild(props.pageType) ||
    projectStore.tabSelectName !== '其他项目'
  )
    return;
  let selectRow = $table.getCurrentRecord();
  if (event.ctrlKey && event.code == 'KeyC') {
    copyOperate(selectRow, $table);
    message.success('复制成功');
  }
  if (event.ctrlKey && event.code == 'KeyV') {
    // console.log(
    //   'asideTitle',
    //   projectStore.otherProCopyInfo.asideTitle,
    //   props.pageType
    // );
    //判断存储数据是否是当前页面可粘贴数据
    if (projectStore.otherProCopyInfo.asideTitle !== props.pageType) return;
    if (hasDataType()) {
      //有标题行和数据行的表格
      let copyDataType =
        projectStore.otherProCopyInfo &&
        projectStore.otherProCopyInfo.copyInfo &&
        projectStore.otherProCopyInfo.copyInfo[0]?.dataType;
      let flag = true;
      if (
        !projectStore.otherProCopyInfo ||
        (copyDataType === 1 && projectStore.dataType === 2)
      ) {
        flag = false;
      }
      if (flag) {
        operate('paste', projectStore.otherProCopyInfo.copyInfo);
      } else {
        message.info('选中行不可粘贴');
      }
    } else {
      getCurrentIndex($table, selectRow);
      let row = { ...projectStore.otherProCopyInfo.copyInfo };
      row.unitId = projectStore.currentTreeInfo?.id;
      operate('paste', row);
    }
  }
};

// 定位方法
const posRow = sequenceNbr => {
  getTableData(sequenceNbr);
};

defineExpose({
  posRow,
});
</script>
<style lang="scss" scoped>
@import './otherProject.scss';

.pulldown-wrap {
  width: 100%;

  ::v-deep(.vxe-input) {
    width: 98%;
  }
}

.input-wrap-dropdown {
  max-height: 40vh;
  overflow-y: hidden;

  &:hover {
    overflow-y: auto;
  }

  box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);

  .list-item {
    padding: 5px 4px;

    &:hover {
      background-color: azure;
      cursor: pointer;
    }
  }
}
</style>
