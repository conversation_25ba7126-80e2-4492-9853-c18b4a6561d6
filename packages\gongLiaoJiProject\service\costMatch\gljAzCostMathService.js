'use strict';

const { Service } = require('../../../../core');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const { BaseDe2022 } = require('../../models/BaseDe2022');
const ProjectDomain = require('../../domains/ProjectDomain');
const FunctionTypeConstants = require('../../constants/FunctionTypeConstants');
const BranchProjectLevelConstant = require('../../constants/BranchProjectLevelConstant');
const CostDeMatchConstants = require('../../constants/CostDeMatchConstants');
const WildcardMap = require('../../core/container/WildcardMap');
const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const { BaseAnZhuangRate2022 } = require('../../models/BaseAnZhuangRate2022');
const DeTypeConstants = require('../../constants/DeTypeConstants');
const { ArrayUtil } = require('../../utils/ArrayUtil');
const { Snowflake } = require('../../utils/Snowflake');
const StandardDeModel = require('../../domains/deProcessor/models/StandardDeModel');
const { ConvertUtil } = require('../../utils/ConvertUtils');
const { PricingFileFindUtils } = require('../../../../electron/utils/PricingFileFindUtils');
const RcjMathEnum = require('../../../../electron/enum/RcjMathEnum');

/**
 * 安装费用记取
 * @class
 */
class GljAzCostMathService extends Service {

  constructor(ctx) {
    super(ctx);
    this.baseAnZhuangRate2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseAnZhuangRate2022);
    this.baseDe2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDe2022);

    this._anZjNumMap = new Map();
    this._anZjNumMap.set('1', ['第一册', '第一章', '一、', '1.', '第一节', '(1)', '①', '第一部分']);
    this._anZjNumMap.set('2', ['第二册', '第二章', '二、', '2.', '第二节', '(2)', '②', '第二部分']);
    this._anZjNumMap.set('3', ['第三册', '第三章', '三、', '3.', '第三节', '(3)', '③', '第三部分']);
    this._anZjNumMap.set('4', ['第四册', '第四章', '四、', '4.', '第四节', '(4)', '④', '第四部分']);
    this._anZjNumMap.set('5', ['第五册', '第五章', '五、', '5.', '第五节', '(5)', '⑤', '第五部分']);
    this._anZjNumMap.set('6', ['第六册', '第六章', '六、', '6.', '第六节', '(6)', '⑥', '第六部分']);
    this._anZjNumMap.set('7', ['第七册', '第七章', '七、', '7.', '第七节', '(7)', '⑦', '第七部分']);
    this._anZjNumMap.set('8', ['第八册', '第八章', '八、', '8.', '第八节', '(8)', '⑧', '第八部分']);
    this._anZjNumMap.set('9', ['第九册', '第九章', '九、', '9.', '第九节', '(9)', '⑨', '第九部分']);
    this._anZjNumMap.set('10', ['第十册', '第十章', '十、', '10.', '第十节', '(10)', '⑩', '第十部分']);
    this._anZjNumMap.set('11', ['第十一册', '第十一章', '十一、', '11.', '第十一节', '(11)', '⑪', '第十一部分']);
    this._anZjNumMap.set('12', ['第十二册', '第十二章', '十二、', '12.', '第十二节', '(12)', '⑫', '第十二部分']);
    this._anZjNumMap.set('13', ['第十三册', '第十三章', '十三、', '13.', '第十三节', '(13)', '⑬', '第十三部分']);
    this._anZjNumMap.set('14', ['第十四册', '第十四章', '十四、', '14.', '第十四节', '(14)', '⑭', '第十四部分']);
    this._anZjNumMap.set('15', ['第十五册', '第十五章', '十五、', '15.', '第十五节', '(15)', '⑮', '第十五部分']);
    this._anZjNumMap.set('16', ['第十六册', '第十六章', '十六、', '16.', '第十六节', '(16)', '⑯', '第十六部分']);
    this._anZjNumMap.set('17', ['第十七册', '第十七章', '十七、', '17.', '第十七节', '(17)', '⑰', '第十七部分']);
    this._anZjNumMap.set('18', ['第十八册', '第十八章', '十八、', '18.', '第十八节', '(18)', '⑱', '第十八部分']);
    this._anZjNumMap.set('19', ['第十九册', '第十九章', '十九、', '19.', '第十九节', '(19)', '⑲', '第十九部分']);
    this._anZjNumMap.set('20', ['第二十册', '第二十章', '二十、', '20.', '第二十节', '(20)', '⑳', '第二十部分']);
    this._anZjNumMap.set('21', ['第二十一册', '第二十一章', '二十一、', '21.', '第二十一节', '(21)', '㉑', '第二十一部分']);
    this._anZjNumMap.set('22', ['第二十二册', '第二十二章', '二十二、', '22.', '第二十二节', '(22)', '㉒', '第二十二部分']);
    this._anZjNumMap.set('23', ['第二十三册', '第二十三章', '二十三、', '23.', '第二十三节', '(23)', '㉓', '第二十三部分']);
    this._anZjNumMap.set('24', ['第二十四册', '第二十四章', '二十四、', '24.', '第二十四节', '(24)', '㉔', '第二十四部分']);
    this._anZjNumMap.set('25', ['第二十五册', '第二十五章', '二十五、', '25.', '第二十五节', '(25)', '㉕', '第二十五部分']);
    this._anZjNumMap.set('26', ['第二十六册', '第二十六章', '二十六、', '26.', '第二十六节', '(26)', '㉖', '第二十六部分']);
    this._anZjNumMap.set('27', ['第二十七册', '第二十七章', '二十七、', '27.', '第二十七节', '(27)', '㉗', '第二十七部分']);
    this._anZjNumMap.set('28', ['第二十八册', '第二十八章', '二十八、', '28.', '第二十八节', '(28)', '㉘', '第二十八部分']);
    this._anZjNumMap.set('29', ['第二十九册', '第二十九章', '二十九、', '29.', '第二十九节', '(29)', '㉙', '第二十九部分']);
    this._anZjNumMap.set('30', ['第三十册', '第三十章', '三十、', '30.', '第三十节', '(30)', '㉚', '第三十部分']);
    this._anZjNumMap.set('31', ['第三十一册', '第三十一章', '三十一、', '31.', '第三十一节', '(31)', '㉛', '第三十一部分']);
    this._anZjNumMap.set('32', ['第三十二册', '第三十二章', '三十二、', '32.', '第三十二节', '(32)', '㉜', '第三十二部分']);
    this._anZjNumMap.set('33', ['第三十三册', '第三十三章', '三十三、', '33.', '第三十三节', '(33)', '㉝', '第三十三部分']);
    this._anZjNumMap.set('34', ['第三十四册', '第三十四章', '三十四、', '34.', '第三十四节', '(34)', '㉞', '第三十四部分']);
    this._anZjNumMap.set('35', ['第三十五册', '第三十五章', '三十五、', '35.', '第三十五节', '(35)', '㉟', '第三十五部分']);
    this._anZjNumMap.set('36', ['第三十六册', '第三十六章', '三十六、', '36.', '第三十六节', '(36)', '㊱', '第三十六部分']);
    this._anZjNumMap.set('37', ['第三十七册', '第三十七章', '三十七、', '37.', '第三十七节', '(37)', '㊲', '第三十七部分']);
    this._anZjNumMap.set('38', ['第三十八册', '第三十八章', '三十八、', '38.', '第三十八节', '(38)', '㊳', '第三十八部分']);
    this._anZjNumMap.set('39', ['第三十九册', '第三十九章', '三十九、', '39.', '第三十九节', '(39)', '㊴', '第三十九部分']);
    this._anZjNumMap.set('40', ['第四十册', '第四十章', '四十、', '40.', '第四十节', '(40)', '㊵', '第四十部分']);
  }

  async azCostMathList(args) {
    let { constructId, singleId, unitId, borrowRule } = args;
    const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    const azBaseDe = yssAllData.find(item => CostDeMatchConstants.DEK_ANZHUANG_2022 == item.libraryCode);
    let anZhuangRateListAllData = await this.baseAnZhuangRate2022Dao.find();
    // 过滤出安装工程的数据  主要是去除掉房屋修缮相关的数据  后续处理房屋修缮时再取出来
    let anZhuangRateList = anZhuangRateListAllData.filter((item) => item.libraryCode == CostDeMatchConstants.DEK_ANZHUANG_2022);
    const id = 'az';
    let array = await this.getAnZhuangFeeData(anZhuangRateList, args, id);
    array.sort((a, b) => a.feeCode - b.feeCode);
    let resTree = [{
      id: id,
      feeName: '河北省建设工程消耗量标准（2022）-安装工程',
      children: array,
      libraryCode: CostDeMatchConstants.DEK_ANZHUANG_2022,
      viewFlag: ObjectUtils.isNotEmpty(azBaseDe) ? 1 : 0
    }];

    const fwxsBaseDe = yssAllData.find((item) => item.type == DeTypeConstants.DE_TYPE_DE && CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ == item.libraryCode);
    const fwxsBaseAnZhuangRateArray = anZhuangRateListAllData.filter(item => CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ == item.libraryCode);
    const fwxsId = 'fwxs';
    const fwxsArr = await this.getAnZhuangFeeData(fwxsBaseAnZhuangRateArray, args, fwxsId);
    fwxsArr.sort((a, b) => a.feeCode - b.feeCode);
    resTree.push({
      id: fwxsId,
      feeName: '河北省建设工程消耗量标准(2023)-房屋修缮工程',
      children: fwxsArr,
      libraryCode: CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ,
      viewFlag: ObjectUtils.isNotEmpty(fwxsBaseDe) ? 1 : 0
    });
    return resTree;
  }

  async getAnZhuangFeeData(anZhuangRateList, arg, parentId) {
    const map = anZhuangRateList.reduce((result, item) => {
      (result[item.feeName] = result[item.feeName] || []).push(item);
      return result;
    }, {});
    let keys = Object.keys(map);
    let array = [];
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      let anZhuangRate = map[key][0];
      let item = {
        sortNo: NumberUtil.add(i, 1),
        feeName: key,
        feeCode: anZhuangRate.feeCode,
        classLevelList: [],
        type: CostDeMatchConstants.ZJCS,
        typeName: key,
        baseDeScope: '预算书',
        isCheck: false,
        parentId: parentId
      };
      await this.deBookListHandler(item, key, map, arg);
      array.push(item);
    }
    return array;
  }

  /**
   * 安装费用----定额分册列表查询
   */
  async deBookListHandler(item, key, map, arg) {
    let anZhuangRateList = map[key];
    //组装分册集合数据
    let classLevel1Map = new Map();
    for (const anZhuangRate of anZhuangRateList) {
      const classLevel2List = classLevel1Map.get(anZhuangRate.classLevel2);
      if (ObjectUtils.isNotEmpty(classLevel2List)) {
        classLevel2List.push(anZhuangRate);
        classLevel1Map.set(anZhuangRate.classLevel2, classLevel2List);
      } else {
        classLevel1Map.set(anZhuangRate.classLevel2, [anZhuangRate]);
      }
    }
    let classLevel1Key = classLevel1Map.keys();
    for (const name of classLevel1Key) {
      let data = {};
      //定额分册名称
      const deList = classLevel1Map.get(name);
      data.classLevel1Name = deList[0].classLevel1;
      data.classLevel2Name = deList[0].classLevel2;
      deList.map(deItem => {
        deItem.typeName = item.typeName;
      });
      //排序
      deList.sort((a, b) => a.sortNumber - b.sortNumber);
      data.deList = this.cgCostHandler(key, deList, arg);
      item.classLevelList.push(data);
    }
  }

  /**
   * 超高条件查询处理
   */
  cgCostHandler(key, deList, arg) {
    let { layerInterval, heightRange } = arg;
    if (key !== '超高费') {
      return deList;
    }
    let array = [];
    if (heightRange == 0 && layerInterval == 0) {
      // 如果是默认的  直接返回默认的
      for (let i = 0; i < deList.length; i++) {
        array.push(deList[i]);
      }
      return array;
    }
    //条件查询
    for (let i = 0; i < deList.length; i++) {
      let de = deList[i];
      let deHeightRange = de.heightRange;
      let deLayerInterval = de.layerInterval;
      if (heightRange == 0 && layerInterval == 0) {
        array.push(de);
      } else {
        //计算层高
        if (!ObjectUtils.isEmpty(deHeightRange) && (heightRange !== 0 || ObjectUtils.isEmpty(heightRange))) {
          let item = deHeightRange.split('~');
          if (NumberUtil.isBetween(heightRange, parseInt(item[0]), parseInt(item[1]))) {
            array.push(de);
          }
        }
        //计算米
        if (!ObjectUtils.isEmpty(deLayerInterval) && (layerInterval !== 0 || ObjectUtils.isEmpty(layerInterval))) {
          let item1 = deLayerInterval.split('~');
          if (NumberUtil.isBetween(layerInterval, parseInt(item1[0]), parseInt(item1[1]))) {
            array.push(de);
          }
        }
      }
    }
    if (!ObjectUtils.isEmpty(array)) {
      deList.forEach((k) => {
        k.isDefault = 0;
      });
      const maxObject = array.reduce((max, obj) => {
        return obj.sortNumber > max.sortNumber ? obj : max;
      });
      maxObject.isDefault = 1;
    }
    return deList;
  }


  async baseDeList(args) {
    let { feeCode, unitId, singleId, constructId, azType, zjType, classLevelList, azClassLevelType } = args;
    //预算书下所有的数据
    let deByfbfxList = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    if (ObjectUtils.isEmpty(deByfbfxList)) {
      return [];
    }
    let libraryCodes = [CostDeMatchConstants.DEK_ANZHUANG_2022];
    if (azClassLevelType == 'fwxs') {
      libraryCodes = [CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ];
    }
    //分部分项定额
    let array = deByfbfxList.filter(k => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE].includes(k.type) && libraryCodes.includes(k.libraryCode));
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }
    //筛选专业
    if (!ObjectUtils.isEmpty(azType)) {
      array = array.filter((k) => k.classlevel01 === azType);
    }
    //筛选章节
    if (!ObjectUtils.isEmpty(zjType)) {
      array = array.filter((k) => k.classlevel02 === zjType);
    }
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }

    let result = []; //最终返回结果容器
    let zjSet = new Set();
    let libraryCodeSet = new Set();
    for (const classLevel of classLevelList) {
      libraryCodeSet.add(classLevel.isDefaultRow.libraryCode);
      const split = classLevel.isDefaultRow.classLevel2.split(',');
      for (const item of split) {
        if (item.includes('~')) {
          const zj1 = this.zjUtils(item);
          if (ObjectUtils.isNotEmpty(zj1)) {
            for (const zj of zj1) {
              zjSet.add(zj);
            }
          }
        } else {
          zjSet.add(item);
        }
      }
    }
    let zjSelect1 = this.zjSelect([...zjSet], array, azClassLevelType == 'fwxs', Array.from(libraryCodeSet));
    if (ObjectUtils.isNotEmpty(zjSelect1)) {
      zjSelect1 = ArrayUtil.distinctList(zjSelect1, 'sequenceNbr');
    }
    this.findDataByParentId(result, deByfbfxList, zjSelect1.map((a) => a.parentId));
    result.push(...zjSelect1);
    if (ObjectUtils.isNotEmpty(result)) {
      result = ObjectUtils.cloneDeep(result);
      result.map(item => {
        delete item.parent;
        delete item.children;
        delete item.prev;
        delete item.next;
      });
    }
    //处理值包含定额行以及父级的所有数据
    return result;
  }

  findDataByParentId(result, arr, parentIds) {
    for (const parentId of parentIds) {
      const foundItems = arr.find((item) => item.sequenceNbr === parentId);
      if (!result
        .map((i) => i.sequenceNbr)
        .includes(foundItems.sequenceNbr)) {
        result.push(foundItems);
      }
      if (ObjectUtils.isEmpty(foundItems.parentId) || foundItems.parentId == '0') {
        return;
      } else {
        this.findDataByParentId(result, arr, [foundItems.parentId]);
      }
    }
    return result;
  }

  zjSelect(zjList, array, isFwxsFlag = false, libraryCodeArr) {
    array = array.filter((item) => libraryCodeArr.includes(item.libraryCode));
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }
    let array1 = [];
    for (const zj of zjList) {
      // 把2.11.11.6 这种数据拆分  split1有几个 就说么有几层  split1有一个就说明只筛选classify_level2 split1有两个就说明筛选classify_level2和classify_level3 以此类推到classify_level7
      const split1 = zj.split('.');
      if (split1.length === 1) {
        // 筛选classify_level2
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 2) {
        // 筛选classify_level2 classify_level3
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel03) && item.classlevel03.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 3) {
        // 筛选classify_level2 classify_level3 classify_level4
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel03) && item.classlevel03.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel04) && item.classlevel04.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 4) {
        // 筛选classify_level2 classify_level3 classify_level4 classify_level5
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel03) && item.classlevel03.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel04) && item.classlevel04.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel05) && item.classlevel05.startsWith(this.getClassify_level5Str(split1[3], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 5) {
        // 筛选classify_level2 classify_level3 classify_level4 classify_level5 classify_level6
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel03) && item.classlevel03.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel04) && item.classlevel04.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel05) && item.classlevel05.startsWith(this.getClassify_level5Str(split1[3], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel06) && item.classlevel06.startsWith(this.getClassify_level6Str(split1[4]))));
      } else if (split1.length === 6) {
        // 筛选classify_level2 classify_level3 classify_level4 classify_level5 classify_level6 classify_level7
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel03) && item.classlevel03.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel04) && item.classlevel04.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel05) && item.classlevel05.startsWith(this.getClassify_level5Str(split1[3], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classlevel06) && item.classlevel06.startsWith(this.getClassify_level6Str(split1[4])) && ObjectUtils.isNotEmpty(item.classlevel07) && item.classlevel07.startsWith(this.getClassify_level7Str(split1[5]))));
      }
    }
    return array1;
  }

  getClassify_level2Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return '第' + this.numberToChinese(str) + '部分';
      } else {
        // 房屋修缮建筑工程
        return '第' + this.numberToChinese(str) + '章';
      }
    } else {
      return '第' + this.numberToChinese(str) + '章';
    }
  }

  getClassify_level3Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return '第' + this.numberToChinese(str) + '章';
      } else {
        // 房屋修缮建筑工程
        return this.numberToChinese(str) + '、';
      }
    } else {
      return '第' + this.numberToChinese(str) + '节';
    }
  }

  getClassify_level4Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return this.numberToChinese(str) + '、';
      } else {
        // 房屋修缮建筑工程
        return str + '.';
      }
    } else {
      return this.numberToChinese(str) + '、';
    }
  }

  getClassify_level5Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return str + '.';
      } else {
        // 房屋修缮建筑工程
        return '(' + str + ')';
      }
    } else {
      return str + '.';
    }
  }

  getClassify_level6Str(str) {
    return '(' + str + ')';
  }

  getClassify_level7Str(str) {
    switch (str + '') {
      case '1':
        return '①';
      case '2':
        return '②';
      case '3':
        return '③';
      case '4':
        return '④';
      case '5':
        return '⑤';
      case '6':
        return '⑥';
      case '7':
        return '⑦';
      case '8':
        return '⑧';
      case '9':
        return '⑨';
      default: {
        return '';
      }
    }
  }

  numberToChinese(num) {
    const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const units = ['', '十', '百', '千', '万', '十', '百', '千', '亿'];

    if (num === 0) return digits[0];

    let result = '';
    let digitArray = num.toString().split('').reverse();

    for (let i = 0; i < digitArray.length; i++) {
      let digit = parseInt(digitArray[i]);
      let unit = units[i];
      if (digit === 0) {
        if (result.startsWith(digits[0])) continue; // 避免重复的零
        if (i % 4 === 0 && result) unit = units[i]; // 在每个新的四位数字组的开始加上万或亿单位
      }
      result = digits[digit] + unit + result;
    }

    // 处理“十”这个特殊情况
    if (result.startsWith('一十')) {
      result = result.substring(1);
    }

    // 移除多余的零
    result = result.replace(/零+/g, '零').replace(/零+$/, '');

    return result;
  }

  zjUtils(zjStr) {
    // 假设每个部分的最大值
    let maxParts = [Infinity, Infinity, Infinity, Infinity, Infinity, Infinity]; // 对于任意长度的版本号，假设没有明确最大值
    function incrementVersion(version, maxParts) {
      let parts = version.split('.').map(Number);
      for (let i = parts.length - 1; i >= 0; i--) {
        parts[i]++;
        if (i < maxParts.length && parts[i] > maxParts[i]) {
          parts[i] = 0;
        } else {
          break;
        }
      }
      return parts.join('.');
    }

    function isVersionGreaterOrEqual(v1, v2) {
      let parts1 = v1.split('.').map(Number);
      let parts2 = v2.split('.').map(Number);
      let len = Math.max(parts1.length, parts2.length);

      for (let i = 0; i < len; i++) {
        let part1 = parts1[i] || 0;
        let part2 = parts2[i] || 0;
        if (part1 > part2) {
          return true;
        } else if (part1 < part2) {
          return false;
        }
      }
      return true; // 当两个版本号相等时返回 true
    }

    function expandRange(rangeStr, maxParts) {
      let [start, end] = rangeStr.split('~');
      let result = [];
      let current = start;
      while (!isVersionGreaterOrEqual(current, end)) {
        result.push(current);
        current = incrementVersion(current, maxParts);
      }
      result.push(end); // 添加结束版本号
      return result;
    }

    return expandRange(zjStr, maxParts);
  }

  async qdList(args) {
    let { feeCode, unitId, singleId, constructId, relationListId, selectQdId, selectQdName, type } = args;
    let array = [];
    //处理分部分项
    if (CostDeMatchConstants.FBFX === type) {
      array = array.concat(ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId));
    }
    if (CostDeMatchConstants.ZJCS === type) {
      array = array.concat(ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId));
    }
    if (ObjectUtils.isNotEmpty(array)) {
      array = array.filter(item => [DeTypeConstants.DE_TYPE_DEFAULT, DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB, DeTypeConstants.DE_TYPE_DELIST].includes(item.type));
    }
    let deepCopy = ObjectUtils.cloneDeep(array);
    deepCopy.sort((a, b) => a.index - b.index);
    let checkFlag = true;
    for (let i = deepCopy.length - 1; i >= 0; i--) {
      let item = deepCopy[i];
      delete item.parent;
      delete item.children;
      delete item.prev;
      delete item.next;
      let { kind, sequenceNbr } = item;
      if ([DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE].includes(item.type)) {
        deepCopy.splice(i, 1);
      }
      if (checkFlag) {
        // 如果是一个已存在的清单  那么用sequenceNbr和qdId作比较查看是否勾选
        if (ObjectUtils.isNotEmpty(selectQdId)) {
          if (sequenceNbr === selectQdId) {
            item.isCheck = 1;
            checkFlag = false;
          }
        } else {
          // 如果不是一个已存在的清单  那么就是一个清单册的
          if (item.deName === selectQdName) {
            item.isCheck = 1;
            checkFlag = false;
          }
        }
      }
    }
    return deepCopy;
  }


  async azCostMath(args) {
    // borrowRule： 使用借用的库的安装费用规则  true表示勾选  false表示不勾选
    let { constructId, singleId, unitId, data, borrowRule } = args;
    // 删除历史的安装费用定额
    await this.service.gongLiaoJiProject.gljConstructCostMathService.delCostDe(unitId, singleId, constructId, [CostDeMatchConstants.AZ_DE]);
    // 设置缓存
    const azCache = await this.azCostMathCache(args);
    if (ObjectUtils.isEmpty(azCache)) {
      const cache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_AZ_COST_MATCH_CACHE);
      if (ObjectUtils.isEmpty(cache)) {
        let cacheObj = {};
        cacheObj[unitId] = args;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_AZ_COST_MATCH_CACHE, cacheObj);
      } else {
        cache[unitId] = args;
      }
    } else {
      ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_AZ_COST_MATCH_CACHE)[unitId] = args;
    }
    const unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
    for (let key of Object.keys(data)) {
      if (!borrowRule) {
        // 如果未勾选使用借用库安装费用规则  那么不处理非当前单位专业的安装专业
        if (unit.deLibrary != CostDeMatchConstants.DEK_ANZHUANG_2022 && unit.deLibrary != CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ) {
          continue;
        }
        // 安装单位工程的不处理房修    房修单位工程不处理安装
        if (unit.deLibrary == CostDeMatchConstants.DEK_ANZHUANG_2022) {
          if (key == 'fwxs') {
            continue;
          }
        } else if (unit.deLibrary == CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ) {
          if (key == 'az') {
            continue;
          }
        }
      }
      // data中包含【安装工程】和【房屋修缮】两类数据
      let costData = data[key].filter(k => k.isCheck);
      if (ObjectUtils.isEmpty(costData)) {
        continue;
      }
      for (const feeItem of costData) {
        // type 选择的记取方式
        // baseDeList表示用户打开了基数定额弹窗并点击确认按钮之后，确认选择的基数定额id
        // notSelectDeList表示用户打开了基数定额弹窗并点击确认按钮之后，确认取消勾选的基数定额id
        let { type, classLevelList, feeCode, baseDeList, notSelectDeList } = feeItem;
        // 当前费用中已使用的基数定额记录
        let feeUsedBaseDeList = [];
        //处理前端如果没有点开详情按钮的话，则就是默认选择了所有的定额
        // 如果是本次打开安装记取弹窗的单位工程  那么没有值的时候就默认选择了所有的基数定额
        let deList = await this.baseDeList({
          feeCode: feeCode,
          unitId: unitId,
          singleId: singleId,
          constructId: constructId,
          classLevelList: classLevelList,
          azClassLevelType: key
        });
        baseDeList = deList.filter(k => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE].includes(k.type)).map(i => i.sequenceNbr);
        if (ObjectUtils.isNotEmpty(notSelectDeList)) {
          baseDeList = baseDeList.filter(k => !notSelectDeList.includes(k));
        }
        if (ObjectUtils.isNotEmpty(baseDeList)) {
          // 本次选择的基数定额数据存入缓存中
          const cacheObj = await this.azCostMathCache(args);
          cacheObj.data[key].find(item => item.feeCode == feeCode).baseDeList = baseDeList;
        }
        // 获取到当前费用项的费用定额
        let costDeList = await this.baseDe2022Dao.find({ where: { value: feeCode } });
        // //根据feeCode获取到所有的费用定额
        // let anZhuangRateList = await this.baseAnZhuangRate2022Dao.find({ where: { feeCode: feeCode } });

        // 所有定额
        let deDataList = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId && [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE].includes(item.type));
        // 所有人材机
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        //循环分册集合
        for (const fcItem of classLevelList) {
          //获取到选择的定额
          let selectDe = fcItem.isDefaultRow;
          // 过滤出用户在页面上选择的基数定额
          let selectBaseDeList = [];
          if (ObjectUtils.isNotEmpty(baseDeList)) {
            // 同时也过滤掉当前费用中其他分册已使用的基数定额
            selectBaseDeList = deDataList.filter(item => baseDeList.includes(item.sequenceNbr) && !feeUsedBaseDeList.includes(item.sequenceNbr));
          }
          // 根据前端选择的费用定额，在分部分项里面查询对应的符合条件基数定额有哪些
          // 这一步主要是确定出比如：在【超高费】下的【机械设备安装】、【电气设备超高费】等分册对应的基数定额
          let baseDeArray = this.getCostDeByBaseDe(selectBaseDeList, selectDe, null);
          //如果所对应的费用定额为空的话，就不行记取定额了
          if (ObjectUtils.isEmpty(baseDeArray)) {
            continue;
          }
          // 记录本次分册已使用的基数定额
          baseDeArray.map(item => feeUsedBaseDeList.push(item.sequenceNbr));
          // 确定费用定额要插入的位置，分部或者措施项
          let parentArr = await this.getAzParentNode(constructId, unitId, type, selectDe, baseDeArray);
          if (type == CostDeMatchConstants.FBFX || type == CostDeMatchConstants.ZJCS) {
            const cacheObj = await this.azCostMathCache(args);
            const feeObj = cacheObj.data[key].find(item => item.feeCode == feeCode).classLevelList.find((item) => item.isDefault == selectDe.sequenceNbr);
            feeObj.selectQdId = parentArr[0].deRowId;
            feeObj.selectQdName = parentArr[0].deName;
          }
          // 确认定额数据
          let costDe = this.confirmCostDe(selectDe, costDeList, baseDeList, baseDeArray, constructId, unitId);
          if (ObjectUtils.isNotEmpty(parentArr) && ObjectUtils.isNotEmpty(costDe)) {
            for (const parent of parentArr) {
              costDe.parentId = parent.deRowId;
              //确定计算费用定额时，所依据的基数定额有哪些
              let deIdList = this.getBaseDeByCostDe(type, parent, baseDeArray);
              //获取到基数定额详情数据集合
              let deList = deDataList.filter((k) => deIdList.includes(k.sequenceNbr));
              //筛选基数定额的人材机数据
              let baseRcjs = rcjList.filter((k) => deIdList.includes(k.deId) && this.calculateBaseHandler(selectDe.calculateBase, selectDe.allocationMethod).includes(k.kind));
              //根据分摊选择算 费率
              let mathRate = this.allocationMethodCostRate(selectDe, this.calculateBaseHandler(selectDe.calculateBase, selectDe.allocationMethod));
              //分别算人材机基数
              let { rBase, cBase, jBase } = await this.rcjBaseCost(selectDe, baseRcjs, deList, mathRate, constructId);
              //赋值计算基数
              costDe.caculatePrice = 1;
              costDe.baseNum = {
                1: rBase, 2: cBase, 3: jBase
              };
              let newCostDe = ObjectUtils.cloneDeep(costDe);
              // 新增定额数据
              if (type == CostDeMatchConstants.FBFX || type == CostDeMatchConstants.DYFBFX) {
                let deRow = await ProjectDomain.getDomain(constructId).deDomain.createDeRow(newCostDe);
                deRow = await ProjectDomain.getDomain(constructId).deDomain.appendBaseDe(constructId, unitId, costDe.standardId, newCostDe.deRowId);
                deRow.type = DeTypeConstants.DE_TYPE_ANZHUANG_FEE;  // 手动把type字段设置为安装费
                // deRow.unit = '元';
                deRow.measureType = null; // 总价措施费用定额施工组织措施类别为空
                await ProjectDomain.getDomain(constructId).deDomain.updateQuantity(constructId, unitId, newCostDe.deRowId, 1);
                // 重新计算人材机数据以及单价构成
                await this.updateTotalNumber(constructId, unitId, costDe, mathRate, deRow);
                await ProjectDomain.getDomain(constructId).deDomain.notify(deRow, false);
              } else {
                let deRow = await ProjectDomain.getDomain(constructId).csxmDomain.createDeRow(newCostDe);
                deRow = await ProjectDomain.getDomain(constructId).csxmDomain.appendBaseDe(constructId, unitId, costDe.standardId, newCostDe.deRowId);
                deRow.type = DeTypeConstants.DE_TYPE_ANZHUANG_FEE;  // 手动把type字段设置为安装费
                // deRow.unit = deRow.unitChanged == 1?deRow.unit:'元';
                deRow.measureType = null; // 总价措施费用定额施工组织措施类别为空
                await ProjectDomain.getDomain(constructId).csxmDomain.updateQuantity(constructId, unitId, newCostDe.deRowId, 1);
                // 重新计算人材机数据以及单价构成
                await this.updateTotalNumber(constructId, unitId, costDe, mathRate, deRow);
                await ProjectDomain.getDomain(constructId).csxmDomain.notify(deRow, false);
              }
            }
          }
        }
      }
    }
  }

  async updateTotalNumber(constructId, unitId, costDe, mathRate, newCostDe) {
    const rBase = costDe.baseNum[1];
    const cBase = costDe.baseNum[2];
    const jBase = costDe.baseNum[3];
    let deMathBase = null;
    let { rRate, cRate, jRate } = mathRate;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    if (ObjectUtils.isNotEmpty(rcjList)) {
      let delRcjList = [];
      for (let i = rcjList.length - 1; i >= 0; i--) {
        let item = rcjList[i];
        let { kind, sequenceNbr, deId } = item;
        if (newCostDe.sequenceNbr == deId) {
          //人
          if (kind == 1) {
            if (ObjectUtils.isEmpty(rRate) || rRate === 0 || rRate === '0.00') {
              delRcjList.push(item);
              continue;
            } else {
              deMathBase = rBase;
            }
          }
          //机
          if (kind == 3) {
            if (ObjectUtils.isEmpty(jRate) || jRate === 0 || jRate === '0.00') {
              delRcjList.push(item);
              continue;
            } else {
              deMathBase = jBase;
            }
          }
          //材料
          if (![1, 3, 4].includes(kind)) {
            if (ObjectUtils.isEmpty(cRate) || cRate === 0 || cRate === '0.00') {
              delRcjList.push(item);
              continue;
            } else {
              deMathBase = cBase;
            }
          }
          // 措施中人工费调整不进行修改
          item.marketPrice = 1;
          item.dePrice = 1;
          //合计数量
          item.totalNumber = NumberUtil.multiplyParams(NumberUtil.numberScale(item.resQty, precision.DETAIL.RCJ.resQty), NumberUtil.numberScale(newCostDe.quantity, precision.EDIT.DE.quantity), deMathBase);
        }
      }
      if (ObjectUtils.isNotEmpty(delRcjList)) {
        for (const delRcj of delRcjList) {
          await this.service.gongLiaoJiProject.gljRcjService.deleteRcjByCodeData(delRcj.deRowId, constructId, delRcj.unitId, delRcj.sequenceNbr, false, {});
        }
      }
    }
  }

  /**
   * 根据分摊方式算费率
   */
  allocationMethodCostRate(selectDe, array) {
    let { allocationMethod, rate, rRate, cRate, jRate } = selectDe;

    let mathRate = {};
    if (!ObjectUtils.isEmpty(allocationMethod)) {
      //分摊方式（0非分摊，1分摊）
      if (parseInt(allocationMethod) === 0) {
        /**
         * 【非分摊记取】：即安装费用定额的人材机费率分别记取，其人材机【计算基数】为基数定额的人工费/材料费/机械费；举例如下：
         * Eg2:   安装费用定额BM1采取费分摊方式记取，其中人工费费率25%，机械费25%；
         * 则安装费用定额人工费=基数定额人工费*25%；安装费用定额机械费=基数定额机械费*25%
         * 说明：安装费用定额的人工/材料/机械费率即对应【定额明细-人材机明细-消耗量】
         */
        //人
        if (array.includes(1)) {
          mathRate.rRate = rRate;
        }
        //机
        if (array.includes(3)) {
          //机械费费率
          mathRate.jRate = jRate;
        }
        //材料
        if (!array.includes([1, 3, 4])) {
          //材料费费率
          mathRate.cRate = cRate;
        }
        return mathRate;
      }
      if (parseInt(allocationMethod) === 1) {
        //【分摊记取】：即安装费用定额的人材机分摊总体费率，其人材机【计算基数】相同。需保证：人工分摊费率+材料分摊费率+机械分摊费率=100%;举例如下：
        // Eg1:   安装费用定额2-1968总体费率为7.56%，采取分摊计费法，其中人工占比11.11%，机械88.89%（11.11+88.89=100），
        // 则
        // 人工费费率=7.56%*11.11%=0.839916%；
        // 机械费费率=7.56%*88.89%=6.720084%
        // 安装费用人工费=0.839916%*（基数定额人工费+机械费）；安装费用机械费=6.720084%*（基数定额人工费+机械费）
        if (array.includes(1)) {
          //人工费费率
          const mathRRate = NumberUtil.multiply(NumberUtil.divide100(rate), NumberUtil.divide100(rRate));
          mathRate.rRate = mathRRate;
        }
        //机
        if (array.includes(3)) {
          //机械费费率
          const mathJRate = NumberUtil.multiply(NumberUtil.divide100(rate), NumberUtil.divide100(jRate));
          mathRate.jRate = mathJRate;
        }
        //材料
        if (!array.includes([1, 3, 4])) {
          //材料费费率
          const mathCRate = NumberUtil.multiply(NumberUtil.divide100(rate), NumberUtil.divide100(cRate));
          mathRate.cRate = mathCRate;
        }
        return mathRate;
      }
    }
  }

  /**
   * 分别人材机的计算基数
   */
  async rcjBaseCost(selectDe, baseRcjs, deList, mathRate, constructId) {
    let { allocationMethod } = selectDe;
    let { rRate, cRate, jRate } = mathRate;
    //获取计税方式    1：一般计税    0：简易计税
    // let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    //循环基数定额
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    //人，材，机 数据合计
    let rSum = 0;
    let jSum = 0;
    let cSum = 0;
    for (const de of deList) {
      // 人工费定额价合价、机械费定额价合价、材料费定额价合价
      rSum = NumberUtil.add(NumberUtil.numberScale(de.rdTotalSum, precision.EDIT.DE.rTotalSum), rSum);
      jSum = NumberUtil.add(NumberUtil.numberScale(de.jdTotalSum, precision.EDIT.DE.jTotalSum), jSum);
      cSum = NumberUtil.add(NumberUtil.numberScale(de.cdTotalSum, precision.EDIT.DE.cTotalSum), cSum);

      // 人工费
      // let baseRList = baseRcjs.filter((k) => k.kind === 1 && k.deId === de.sequenceNbr);
      // if (!ObjectUtils.isEmpty(baseRList)) {
      //   const RSum = baseRList.reduce((total, item) => total + NumberUtil.multiplyParams((taxCalculationMethod == 1 ? item.baseJournalPrice : item.baseJournalTaxPrice), item.totalNumber), 0);
      //   rSum = NumberUtil.add(RSum, rSum);
      // }
      // //筛选机明细
      // let baseJList = baseRcjs.filter((k) => k.kind === 3 && k.deId === de.sequenceNbr);
      // if (!ObjectUtils.isEmpty(baseJList)) {
      //   baseJList.forEach((k) => {
      //     const JSum = NumberUtil.multiplyParams((taxCalculationMethod == 1 ? k.baseJournalPrice : k.baseJournalTaxPrice), k.totalNumber);
      //     jSum = NumberUtil.add(JSum, jSum);
      //   });
      // }
      // // 筛选材料明细
      // let baseCList = baseRcjs.filter((k) => ![1, 3, 4].includes(k.kind) && k.deId === de.sequenceNbr);
      // if (!ObjectUtils.isEmpty(baseCList)) {
      //   baseCList.forEach((k) => {
      //     const CSum = NumberUtil.multiplyParams((taxCalculationMethod == 1 ? k.baseJournalPrice : k.baseJournalTaxPrice), k.totalNumber);
      //     cSum = NumberUtil.add(CSum, cSum);
      //   });
      // }
    }

    //  人工费  RGF
    //  机械费 JXF
    //  材料费 CLF
    //  人工费+机械费 RGF+JXF
    //  人工费+材料费  RGF+CLF
    //  机械费+材料费 JXF+CLF
    //  人工费+机械费+材料费  RGF+JXF+CLF

    let sum = 0;
    if (allocationMethod == 1) {
      let temp = {};
      temp.rSum = rSum;
      temp.jSum = jSum;
      temp.cSum = cSum;
      //分摊
      let baseMathArray = RcjMathEnum.baseMath[selectDe.calculateBase];
      for (const i of baseMathArray) {
        sum = NumberUtil.add(temp[i], sum);
      }
      //计算基数
      return {
        rBase: NumberUtil.multiply(rRate, sum),
        jBase: NumberUtil.multiply(jRate, sum),
        cBase: NumberUtil.multiply(cRate, sum)
      };
    }
    if (allocationMethod == 0) {
      //非分摊
      return {
        rBase: NumberUtil.multiply(NumberUtil.divide100(rRate), rSum),
        jBase: NumberUtil.multiply(NumberUtil.divide100(jRate), jSum),
        cBase: NumberUtil.multiply(NumberUtil.divide100(cRate), cSum)
      };
    }
  }

  /**
   * 计算基数处理
   */
  calculateBaseHandler(calculateBase, allocationMethod) {
    if (allocationMethod == 0) {
      return [1, 2, 3, 5, 6, 7, 8, 9, 10];
    }
    let array = [];
    if (calculateBase.includes('+')) {
      array = calculateBase.split('+');
    }
    if (calculateBase.includes('，')) {
      array = calculateBase.split('，');
    }
    let result = [];
    for (const iter of array) {
      //RGF，CLF，JXF
      if (iter === 'RGF') {
        result.push(1);
      }
      if (iter === 'JXF') {
        result.push(3);
      }
      if (iter === 'CLF') {
        result.push(...[2, 5, 6, 7, 8, 9, 10]);
      }
    }
    return result;
  }

  /**
   * 根据费用确定基数定额
   */
  getBaseDeByCostDe(type, parent, baseDeArray) {
    //指定分部分项 || 指定措施清单
    if (type == CostDeMatchConstants.FBFX || type == CostDeMatchConstants.ZJCS) {
      return baseDeArray.map((k) => k.sequenceNbr);
    }
    if (type === CostDeMatchConstants.DYFBFX) {
      //根据父级ID获取到基数定额
      let deList = baseDeArray.filter(item => item.parentId == parent.deRowId);
      return deList.map((k) => k.sequenceNbr);
    }
  }

  /**
   * 确定费用定额并且封装费用定额数据
   */
  confirmCostDe(selectDe, costDeList, baseDeIdList, baseDeArray, constructId, unitId) {
    let { libraryCode, classLevel1, deCode, deName } = selectDe;

    //根据前端选择的费用定额，在分部分项里面查询对应的基数定额
    baseDeArray = baseDeArray.filter(k => baseDeIdList.includes(k.sequenceNbr));
    if (ObjectUtils.isEmpty(baseDeArray)) {
      return null;
    }
    // 获取到费用定额
    // selectDe数据来源是base_anzhuang_rate   costDeList是base_de的数据
    // 这两个数据确认唯一一条，需要使用libraryCode、deCode、deName三个字段进行匹配
    let costDe = costDeList.find((k) => k.libraryCode === libraryCode && k.deCode === deCode && k.deName == deName);
    if (ObjectUtils.isEmpty(costDe) && ['C024', 'C025', 'C026', 'C027', 'C028', 'C029', 'C030'].includes(deCode)) {
      // 2025-05-21 这个if是因为改了系统调试费的费用定额名称(在定额前面加了前缀“系统调试费及联调费_”)
      // 但是历史文件的缓存中没有这个前缀，所以这里需要兼容  不能直接使用k.deName == deName
      costDe = costDeList.find((k) => k.libraryCode === libraryCode && k.deCode === deCode && k.deName == ('系统调试费及联调费_' + deName));
    }
    // if (libraryCode == CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_TJ) {
    //   // 【房屋修缮建筑工程】需要特殊处理，使用【房屋修缮安装工程】的费用定额数据
    //   // 原因是【房屋修缮建筑工程】在base_anzhuang_rate_2022中有【G009】【G010】这两条定额   但是在base_de_2022中没有【G009】【G010】这两条定额
    //   // 所以这里特殊处理为使用base_de_2022中【房屋修缮安装工程】的【G009】【G010】数据作为费用定额基础数据
    //   costDe = costDeList.find((k) => k.libraryCode === CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ && k.deCode === deCode && k.deName == deName);
    // }
    //封装费用定额其他数据
    let deData = new StandardDeModel(constructId, unitId, Snowflake.nextId(), null, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
    deData.deCode = costDe.deCode;
    deData.deName = costDe.deName;
    deData.quantity = 1;
    deData.unit = costDe.unit;
    if (deData.unit == '%') {
      deData.unit = '元';
    }
    deData.standardId = costDe.sequenceNbr;
    deData.libraryCode = costDe.libraryCode;
    deData.isAutoCost = true; //手动记取
    deData.isCostDe = CostDeMatchConstants.AZ_DE; //添加定额标识 安装费用定额
    deData.quantityExpression = '1'; //工程量表达式展示用
    deData.quantityExpressionNbr = 1; //工程量表达式计算用
    return deData;
  }

  async getAzParentNode(constructId, unitId, type, selectDe, baseDeArray) {
    let { feeName, selectQdName, selectQdId, sequenceNbr } = selectDe;
    let parentArr = [];
    if (type == CostDeMatchConstants.FBFX) {
      let parentNode;
      if (ObjectUtils.isNotEmpty(selectQdId)) {
        parentNode = ProjectDomain.getDomain(constructId).deDomain.getDeById(selectQdId);
      }
      if (ObjectUtils.isEmpty(parentNode)) {
        const parentFb = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId
          && (item.type == DeTypeConstants.DE_TYPE_FB || item.type == DeTypeConstants.DE_TYPE_ZFB)
          && item.deName == '安装费用'
        );
        if (ObjectUtils.isNotEmpty(parentFb)) {
          // 如果有多个【安装费用】的分部  取页面排序中的第一个可以插入定额的【安装费用】分部
          parentNode = this.getFirstAzFb(constructId, unitId);
          if (ObjectUtils.isEmpty(parentNode)) {
            let newQdNode = new StandardDeModel(constructId, unitId, Snowflake.nextId(), ProjectDomain.getDomain(constructId).deDomain.getRoot(unitId).deRowId, DeTypeConstants.DE_TYPE_FB);
            newQdNode.deName = '安装费用';
            newQdNode.pricingMethod = 2;    // 定额组价
            parentNode = await ProjectDomain.getDomain(constructId).deDomain.createDeRow(newQdNode);
          }
        } else {
          const fbNodes = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId
            && (item.type == DeTypeConstants.DE_TYPE_FB || item.type == DeTypeConstants.DE_TYPE_ZFB));
          if (ObjectUtils.isEmpty(fbNodes)) {
            parentNode = ProjectDomain.getDomain(constructId).deDomain.getRoot(unitId);
          } else {
            let newQdNode = new StandardDeModel(constructId, unitId, Snowflake.nextId(), ProjectDomain.getDomain(constructId).deDomain.getRoot(unitId).deRowId, DeTypeConstants.DE_TYPE_FB);
            newQdNode.deName = '安装费用';
            newQdNode.pricingMethod = 2;    // 定额组价
            parentNode = await ProjectDomain.getDomain(constructId).deDomain.createDeRow(newQdNode);
          }
        }
      }
      parentArr.push(parentNode);
    } else if (type == CostDeMatchConstants.ZJCS) {
      let parentNode;
      if (ObjectUtils.isNotEmpty(selectQdId)) {
        parentNode = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(selectQdId);
      }
      if (ObjectUtils.isEmpty(parentNode)) {
        const parentFb = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId
          && item.type == DeTypeConstants.DE_TYPE_DELIST && item.deName == feeName && item.pricingMethod == 2
          && (item.parent != null && item.parent.type != DeTypeConstants.DE_TYPE_DELIST && item.parent.deName != '不可竞争措施项目'));
        if (ObjectUtils.isNotEmpty(parentFb)) {
          parentNode = parentFb[0];
        } else {
          let parentCsxm = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId && item.type == DeTypeConstants.DE_TYPE_FB && item.deName != '不可竞争措施项目');
          if (ObjectUtils.isEmpty(parentCsxm)) {
            parentCsxm = [ProjectDomain.getDomain(constructId).csxmDomain.getRoot(unitId)];
          }
          let newQdNode = new StandardDeModel(constructId, unitId, Snowflake.nextId(), parentCsxm[0].deRowId, DeTypeConstants.DE_TYPE_DELIST);
          newQdNode.deName = feeName;
          newQdNode.pricingMethod = 2;    // 定额组价
          parentNode = await ProjectDomain.getDomain(constructId).csxmDomain.createDeRow(newQdNode);
        }
      }
      parentArr.push(parentNode);
    } else if (type == CostDeMatchConstants.DYFBFX) {
      let baseDeParentIds = baseDeArray.map(item => item.parentId);
      baseDeParentIds = [...new Set(baseDeParentIds)];
      baseDeParentIds.map(item => parentArr.push(ProjectDomain.getDomain(constructId).deDomain.getDeById(item)));
    }
    return parentArr;
  }

  getFirstAzFb(constructId, unitId) {
    const root = ProjectDomain.getDomain(constructId).deDomain.getRoot(unitId);
    if (ObjectUtils.isEmpty(root.children)) {
      return root;
    }
    return this.findAzFb(root);
  }

  findAzFb(fbNode) {
    if (ObjectUtils.isNotEmpty(fbNode) && [DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB].includes(fbNode.type) && fbNode.deName == '安装费用') {
      // 先确定这个分部是【安装费用】分部
      if (ObjectUtils.isEmpty(fbNode.children)) {
        return fbNode;
      } else {
        if (fbNode.children[0].type != DeTypeConstants.DE_TYPE_ZFB) {
          return fbNode;
        }
      }
    }
    const sortChildren = [...fbNode.children].sort((a, b) => a.index - b.index);
    for (const child of sortChildren) {
      const findAzFb = this.findAzFb(child);
      if (ObjectUtils.isNotEmpty(findAzFb)) {
        return findAzFb;
      }
    }
  }


  /**
   * 根据费用定额查询当前项目下符合条件的基数定额有哪些
   */
  getCostDeByBaseDe(itemBillProjects, selectDe, costDe) {
    let { classLevel1, classLevel2, libraryCode } = selectDe;
    //分部分项定额,只需要安装工程下的基数定额
    let array = itemBillProjects.filter((k) => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE].includes(k.type) && k.libraryCode == libraryCode && k.classlevel01 === classLevel1);
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }

    if (ObjectUtils.isNotEmpty(costDe) && costDe.costDeMatchType === CostDeMatchConstants.DYFBFX) {
      // 如果是对应分部分项  那么基数定额只能从对应清单下获取   也就是同一个清单下的
      array = array.filter((item) => item.parentId === costDe.parentId);
    }
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }

    // 先根据 , 分割
    let zjList = [];
    const split = classLevel2.split(',');
    for (const item of split) {
      if (item.includes('~')) {
        zjList = zjList.concat(this.zjUtils(item));
      } else {
        zjList.push(item);
      }
    }
    array = this.zjSelect(zjList, array, this.isFwxs(libraryCode), [libraryCode]);
    return array;
  }

  /**
   * 判断是不是房屋修缮
   */
  isFwxs(libraryCode) {
    return [CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ].includes(libraryCode);
  }


  async azCostMathCache(args) {
    let { unitId, singleId, constructId } = args;
    const azCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_AZ_COST_MATCH_CACHE);
    if (ObjectUtils.isNotEmpty(azCache)) {
      return azCache[unitId];
    }
    return null;
  }

  async queryBaseDeChapter(args) {
    // chapterStr表示当前选择了的章节字符串
    const { constructId, singleId, unitId, chapterStr, fascicleStr, azClassLevelType } = args;
    let libraryCode = CostDeMatchConstants.DEK_ANZHUANG_2022;
    if (azClassLevelType == 'fwxs') {
      // if (fascicleStr == '房屋修缮建筑工程') {
      //   libraryCode = CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_TJ;
      // } else {
      libraryCode = CostDeMatchConstants.DEK_ANZHUANG_2022_FWXS_AZ;
      // }
    }
    let result = null;
    result = await this.service.baseDe2022Service.listTreeByLibraryCode(libraryCode);
    result = result.childrenList[0];
    // 筛选分册数据
    result = this.filterFascicle(result, azClassLevelType, fascicleStr);
    // 完善章节数据
    this.fillZjData(result);
    if (ObjectUtils.isEmpty(chapterStr)) {
      return result;
    }
    // 先把章节字符串拆分并添加到checkArr里面
    let checkArr = [];
    const chapterStrSplit = chapterStr.split(',');
    for (const str of chapterStrSplit) {
      if (str.includes('~')) {
        checkArr = checkArr.concat(this.zjUtils(str));
      } else {
        checkArr.push(str);
      }
    }
    if (ObjectUtils.isNotEmpty(result)) {
      // 设置是否选中
      this.setCheckFlag(result, checkArr);
    }
    // 手动设置顶层的idx为0
    result.idx = '0';
    // 并给顶层的下级节点设置parentId
    if (ObjectUtils.isNotEmpty(result.childrenList)) {
      for (const item of result.childrenList) {
        item.parentId = result.idx;
      }
    }
    return result;
  }

  setCheckFlag(data, checkArr) {
    if (ObjectUtils.isEmpty(data.childrenList)) {
      return;
    }
    for (const item of data.childrenList) {
      if (checkArr.includes(item.idx)) {
        item.isCheck = 1;
      } else {
        item.isCheck = 0;
      }
      if (item.isCheck == 1) {
        // 如果当前的节点是选中的，那么他的所有子级也都选中
        if (ObjectUtils.isNotEmpty(data.childrenList)) {
          // 如果有子级  那么所有子级也选中
          this.setCheck(item);
        }
      } else {
        this.setCheckFlag(item, checkArr);
      }
    }
  }

  setCheck(data) {
    if (ObjectUtils.isEmpty(data.childrenList)) {
      return;
    }
    for (const item of data.childrenList) {
      item.isCheck = 1;
      this.setCheck(item);
    }
  }

  filterFascicle(data, azClassLevelType, fascicleStr) {
    let result = {};
    // 22的过滤掉【classlevel02 = 安装工程其它措施项目】的
    result = data;
    if (azClassLevelType == 'fwxs') {
      if (fascicleStr == '房屋修缮建筑工程') {
        result.childrenList = result.childrenList.filter((item) => item.classifyLevel2 != '附件1 其它措施项目、中小型机械使用费和工程水电费');
      } else {
        result.childrenList = result.childrenList.filter((item) => item.classifyLevel2 != '房屋修缮安装工程措施项目');
      }
    } else {
      result.childrenList = result.childrenList.filter((item) => item.classifyLevel2 != '安装工程其它措施项目');
    }
    result.idx = null;
    return result;
  }

  /**
   * 填充章节数据的idx和parentId
   */
  fillZjData(data) {
    if (ObjectUtils.isEmpty(data.childrenList)) {
      return;
    }
    for (const item of data.childrenList) {
      // 如果是22定额  那么就有7级 依次为：【第一章】 -> 【第一节】 -> 【一、】 -> 【1.】 -> 【(1)】 -> 【①】
      // 如果是12定额  那么就只有4级 依次为：【第一册】 —> 【第一章】 —> 【一、】 —> 【1.】
      item.idx = this.getIdxStr(data, item);
      item.parentId = data.idx;
      this.fillZjData(item);
    }
  }

  getIdxStr(parent, item) {
    let idx = '';
    if (ObjectUtils.isEmpty(item.name)) {
      return idx;
    }
    if (ObjectUtils.isNotEmpty(parent.idx)) {
      idx = parent.idx;
    }
    for (const [key, value] of this._anZjNumMap) {
      for (const v of value) {
        if (item.name.startsWith(v)) {
          if (ObjectUtils.isEmpty(idx)) {
            return key;
          } else {
            return idx + '.' + key;
          }
        }
        // 除了this._anZjNumMap中的以外，还有一种不在map中的：【12定额的classify_level4中有[36.钢管(沟槽连接)]、[35.钢骨架复合塑料管(电熔连接)]这种的，这种映射太多了，单独判断这种】
        const number = this.extractLeadingNumber(item.name);
        if (ObjectUtils.isNotEmpty(number)) {
          if (ObjectUtils.isEmpty(idx)) {
            return number;
          } else {
            return idx + '.' + number;
          }
        }
      }
    }
    return idx;
  }

  extractLeadingNumber(str) {
    // ^ 表示匹配字符串的开头
    // (\d+) 表示匹配并捕获一个或多个数字
    // \. 表示匹配一个点
    const regex = /^(\d+)\./;
    const match = str.match(regex);

    if (match) {
      // match[1] 包含捕获到的第一个数字
      return parseInt(match[1], 10);
    } else {
      // 如果没有匹配到，返回 null
      return null;
    }
  }


  // -------------------------------------------------------------------------------------------------------------------

  /**
   * 安装费用自动计算
   * 手动记取出来的费用定额按照缓存数据重新计算
   * 非记取出来的费用定额：
   *    预算书：基数定额的筛选范围直接使用 对应分部记取
   *    措施项目：基数定额的筛选范围直接使用 对应措施项记取
   */
  async autoMatchAzCost(args) {
    const { constructId, singleId, unitId } = args;
    // 预算书的所有数据
    const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    // 措施项目的所有数据
    const csxmAllData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
    // 预算书中的所有费用定额
    const yssAzCostDeArr = yssAllData.filter(item => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE].includes(item.type) && item.isCostDe == CostDeMatchConstants.AZ_DE);
    // 措施项目中的所有费用定额
    const csxmAzCostDeArr = csxmAllData.filter(item => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE].includes(item.type) && item.isCostDe == CostDeMatchConstants.AZ_DE);
    if (ObjectUtils.isEmpty(yssAzCostDeArr) && ObjectUtils.isEmpty(csxmAzCostDeArr)) {
      // 没有费用定额不处理
      return;
    }

    const azCache = await this.azCostMathCache(args);
    let typeMap = new Map();
    //获取缓存中的费用定额设置信息.
    if (ObjectUtils.isNotEmpty(azCache)) {
      for (let key of Object.keys(azCache.data)) {
        // 分为【安装工程】和【房屋修缮】
        let array = [];
        let baseDeMap = new Map();
        let notSelectBaseDeMap = new Map();
        for (const item of azCache.data[key]) {
          let { classLevelList, feeCode, baseDeList, notSelectDeList } = item;
          classLevelList.forEach(k => {
            array.push(k.isDefaultRow);
          });
          baseDeMap.set(feeCode, baseDeList);
          notSelectBaseDeMap.set(feeCode, notSelectDeList);
        }
        typeMap.set(key, {
          defaultRowArray: array,
          baseDeMap: baseDeMap,
          notSelectBaseDeMap: notSelectBaseDeMap
        });
      }
      // 先处理预算书和措施项目中手动记取出来的费用定额
      let matchCostDeArr = [...yssAzCostDeArr, ...csxmAzCostDeArr].filter(item => item.isAutoCost);
      if (ObjectUtils.isNotEmpty(matchCostDeArr)) {
        for (const matchCostDe of matchCostDeArr) {
          //该费用定额的缓存设置信息  这一行的作用是在缓存中查找到对应的费用定额基本信息  比如【超高费】的【第二册 电气设备安装工程】的【1~14超高费(60层/200m以下)(电气设备安装工程)】
          let array = [];
          let baseDeMap = new Map();
          let notSelectBaseDeMap = new Map();
          if (typeMap.size > 0) {
            array = typeMap.get('az').defaultRowArray;
            baseDeMap = typeMap.get('az').baseDeMap;
            notSelectBaseDeMap = typeMap.get('az').notSelectBaseDeMap;
            if (this.isFwxs(matchCostDe.libraryCode)) {
              array = typeMap.get('fwxs').defaultRowArray;
              baseDeMap = typeMap.get('fwxs').baseDeMap;
              notSelectBaseDeMap = typeMap.get('fwxs').notSelectBaseDeMap;
            }
          }
          // 由于【房屋修缮的垂直运输费】标准费用定额(base_anzhuang_rate)的deCode都是【5-77】,导致这里不能只用deCode和feeCode查询，所以需要加上deName作为判断条件
          let baseDe = await this.baseDe2022Dao.findOne({
            where: { sequenceNbr: matchCostDe.standardId }
          });
          let costDeSetInfo = array.find(k => k.deCode === matchCostDe.deCode && parseInt(k.feeCode) === matchCostDe.value && k.deName == baseDe.deName);
          if (ObjectUtils.isEmpty(costDeSetInfo) && matchCostDe.value == 220) {
            // 这是一段补丁代码，原因是base_anzhuang_rate_2022这个表的系统调试费deName加了前缀《系统调试费及联调费_》  导致deName变化  历史缓存就会出现匹配问题
            costDeSetInfo = array.find(k => k.deCode === matchCostDe.deCode && parseInt(k.feeCode) === matchCostDe.value && baseDe.deName.includes(k.deName));
          }
          let selectBaseDeList = yssAllData;
          // // 过滤出用户在页面上选择的基数定额
          // const baseDeList = baseDeMap.get(matchCostDe.value.toString());
          // 过滤出用户手动排除的基数定额   剩余的都是需要参与计算的
          const notSelectBaseDeList = notSelectBaseDeMap.get(matchCostDe.value.toString());
          if (ObjectUtils.isNotEmpty(notSelectBaseDeList)) {
            selectBaseDeList = yssAllData.filter(item => !notSelectBaseDeList.includes(item.sequenceNbr));
          }
          //根据前端选择的费用定额，在分部分项里面查询对应的符合条件基数定额有哪些
          let baseDeArray = this.getCostDeByBaseDe(selectBaseDeList, costDeSetInfo, matchCostDe);
          // 重新计算人材机数据以及单价构成
          await this.azUpdateTotalNumber(constructId, unitId, matchCostDe,
            baseDeArray.map(k => k.sequenceNbr), costDeSetInfo, [...yssAllData, ...csxmAllData]);
          if (ObjectUtils.isNotEmpty(ProjectDomain.getDomain(constructId).deDomain.getDeById(matchCostDe.deRowId))) {
            await ProjectDomain.getDomain(constructId).deDomain.notify(matchCostDe, false);
          } else {
            await ProjectDomain.getDomain(constructId).csxmDomain.notify(matchCostDe, false);
          }
        }
      }
    }

    let anZhuangRateList = await this.baseAnZhuangRate2022Dao.find();

    // 处理预算书中手动添加的安装费用定额
    const addYssAzCostDeArr = yssAzCostDeArr.filter(item => !item.isAutoCost);
    if (ObjectUtils.isNotEmpty(addYssAzCostDeArr)) {
      for (const costDe of addYssAzCostDeArr) {
        let array = [];
        if (typeMap.size > 0) {
          array = typeMap.get('az').defaultRowArray;
          if (this.isFwxs(costDe.libraryCode)) {
            array = typeMap.get('fwxs').defaultRowArray;
          }
        }
        //获取到该清单下的所有定额
        let qdByDeList = yssAllData.filter(k => k.parentId === costDe.parentId && k.value === 0 && [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE].includes(k.type));
        let costDeSet = array.find(k => k.deCode === costDe.deCode);
        if (ObjectUtils.isEmpty(costDeSet)) {
          costDeSet = anZhuangRateList.find(k => parseInt(k.feeCode) === costDe.value && k.deCode === costDe.deCode);
        }
        //根据前端选择的费用定额，在分部分项里面查询对应的符合条件基数定额有哪些
        let baseDeArray = this.getCostDeByBaseDe(qdByDeList, costDeSet, costDe);
        let domain = ProjectDomain.getDomain(constructId).deDomain;
        if (ObjectUtils.isNotEmpty(ProjectDomain.getDomain(constructId).csxmDomain.getDeById(costDe.deRowId))) {
          domain = ProjectDomain.getDomain(constructId).csxmDomain;
        }
        // await domain.updateQuantity(constructId, unitId, costDe.deRowId, 1, undefined, undefined, undefined, undefined, undefined, false);
        // 重新计算人材机数据以及单价构成
        await this.azUpdateTotalNumber(constructId, unitId, costDe,
          baseDeArray.map(k => k.sequenceNbr), costDeSet, [...yssAllData, ...csxmAllData]);
        await domain.notify({
          constructId,
          unitId,
          deRowId: costDe.deRowId
        }, false);
      }
    }

    // 措施项目中的所有手动添加的费用定额
    let zjcsCodeList = csxmAzCostDeArr.filter(k => !k.isAutoCost);
    if (ObjectUtils.isNotEmpty(zjcsCodeList)) {
      for (const costDe of zjcsCodeList) {
        let array = [];
        if (typeMap.size > 0) {
          array = typeMap.get('az').defaultRowArray;
          if (this.isFwxs(costDe.libraryCode)) {
            array = typeMap.get('fwxs').defaultRowArray;
          }
        }
        let costDeSet = array.find(k => k.deCode === costDe.deCode);
        if (ObjectUtils.isEmpty(costDeSet)) {
          costDeSet = anZhuangRateList.find(k => parseInt(k.feeCode) === costDe.value && k.deCode === costDe.deCode);
        }
        //在分部分项里面查询对应的符合条件基数定额有哪些
        let baseDeArray = this.getCostDeByBaseDe(yssAllData, costDeSet, costDe);
        let domain = ProjectDomain.getDomain(constructId).deDomain;
        if (ObjectUtils.isNotEmpty(ProjectDomain.getDomain(constructId).csxmDomain.getDeById(costDe.deRowId))) {
          domain = ProjectDomain.getDomain(constructId).csxmDomain;
        }
        // await domain.updateQuantity(constructId, unitId, costDe.deRowId, 1, undefined, undefined, undefined, undefined, undefined, false);
        // 重新计算人材机数据以及单价构成
        await this.azUpdateTotalNumber(constructId, unitId, costDe,
          baseDeArray.map(k => k.sequenceNbr), costDeSet, [...yssAllData, ...csxmAllData]);
        await domain.notify({
          constructId,
          unitId,
          deRowId: costDe.deRowId
        }, false);
      }
    }
  }

  async azUpdateTotalNumber(constructId, unitId, costDe, deIdList, selectDe, deDataList) {
    let { calculateBase } = selectDe;
    //获取到基数定额详情数据集合
    let deList = deDataList.filter(k => deIdList.includes(k.sequenceNbr));

    //获取单位下所有人材机数据
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);

    //筛选基数定额的人材机数据
    let baseRcjs = rcjList.filter(k => deIdList.includes(k.deId) && this.calculateBaseHandler(calculateBase, selectDe.allocationMethod).includes(k.kind));

    //根据分摊选择算 费率
    let mathRate = this.allocationMethodCostRate(selectDe, this.calculateBaseHandler(calculateBase, selectDe.allocationMethod));
    //分别算人材机基数
    let {
      rBase,
      cBase,
      jBase
    } = await this.rcjBaseCost(selectDe, baseRcjs, deList, mathRate, constructId);
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let deMathBase = null;
    let { rRate, cRate, jRate } = mathRate;
    //如果材料的费率为0，则删除该条材料
    for (let i = rcjList.length - 1; i >= 0; i--) {
      let item = rcjList[i];
      let { kind, sequenceNbr, deId } = item;
      //找到费用定额人材机
      if (deId === costDe.sequenceNbr) {
        //人
        if (kind === 1) {
          deMathBase = rBase;
        }
        //机
        if (kind === 3) {
          deMathBase = jBase;
        }
        //材料
        if (![1, 3, 4].includes(kind)) {
          deMathBase = cBase;
        }
        item.marketPrice = 1;
        item.dePrice = 1;
        //合计数量
        item.totalNumber = NumberUtil.multiplyParams(NumberUtil.numberScale(item.resQty, precision.DETAIL.RCJ.resQty), NumberUtil.numberScale(costDe.quantity, precision.EDIT.DE.quantity), deMathBase);
        //合价
        // item.total = NumberUtil.numberScale(NumberUtil.multiply(item.totalNumber, item.marketPrice), precision.EDIT.DERCJ.totalNumber);
      }
    }
    //赋值计算基数
    costDe.caculatePrice = 1;
    costDe.baseNum = { 1: rBase, 2: cBase, 3: jBase };
  }

}

GljAzCostMathService.toString = () => '[class GljAzCostMathService]';
module.exports = GljAzCostMathService;