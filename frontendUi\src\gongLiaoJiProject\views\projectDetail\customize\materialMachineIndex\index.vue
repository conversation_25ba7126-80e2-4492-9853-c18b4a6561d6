<!--
 * @Descripttion: 人材机索引
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: sunchen
 * @LastEditTime: 2025-03-17 17:04:42
-->
<template>
  <div>
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.indexVisible"
      :title="dialogTitle"
      :mask="false"
      :lockView="false"
      :lockScroll="false"
      width="auto"
      @cancel="close"
      @close="close"
    >
      <div class="dialog-wrap">
        <div class="contentCenter">
          <div class="left">
            <p class="head">
              <a-radio-group v-model:value="value1" button-style="solid">
                <a-radio-button value="dingE">人材机</a-radio-button>
              </a-radio-group>
            </p>
            <div class="search">
              <!-- <a-input-search
                v-model:value="materialName"
                :maxlength="50"
                placeholder="请输入编码或名称"
                style="width: 95%; margin: 10px 8px 10px"
                @search="onSearch"
              /> -->
              <IndexSearch
                v-model:value="materialName"
                type="renCJ"
                @search="onSearch"
              ></IndexSearch>
              <a-select
                v-model:value="selectValue"
                style="width: 95%; margin: 0 8px 10px"
                :options="selectOptions"
                placeholder="请选择"
                :field-names="{
                  label: 'libraryName',
                  value: 'libraryCode',
                }"
                @change="handleChange"
              ></a-select>
              <div class="tree" v-if="treeData.length > 0">
                <a-tree
                  v-model:expandedKeys="expandedKeys"
                  v-model:selectedKeys="selectedKeys"
                  :tree-data="treeData"
                  @select="selectChildren"
                  style="z-index: 10"
                  :field-names="{
                    title: 'materialName',
                    children: 'childrenList',
                    key: 'key',
                  }"
                >
                  <template #icon="{ key }">
                    <template v-if="key !== '0-0'"></template>
                  </template>
                </a-tree>
              </div>
            </div>
          </div>
          <div class="right">
            <p class="btns">
              <a-radio-group v-model:value="value2" button-style="solid">
                <a-radio-button value="list">人材机列表</a-radio-button>
              </a-radio-group>
              <span v-if='canInsert'>
                <a-button
                  v-if="props.businessType != 'HumanMachineSummary'"
                  type="primary"
                  class="btnNo1"
                  @click="updateCurrentInfo(1)"
                  :disabled="!currentInfo || props.indexLoading"
                  >插入</a-button
                >
                <!-- v-if="!isDlf" -->
                <a-button
                  type="primary"
                  :disabled="
                    !currentInfo ||
                    props.indexLoading ||
                    (props.currentInfo?.isFyrcj == 1 &&
                      currentInfo?.isFyrcj == 0 &&
                      props.businessType == 'HumanMachineSummary')
                  "
                  @click="updateCurrentInfo(2)"
                  >替换</a-button
                >
              </span>
            </p>
            <div class="table">
              <vxe-table
                align="center"
                :column-config="{ resizable: true }"
                ref="vexTable"
                class="table-scrollbar"
                :data="tableData"
                height="auto"
                :row-config="{
                  isCurrent: true,
                  keyField: 'sequenceNbr',
                }"
                @scroll="getScroll"
                :scroll-y="{ scrollToTopOnChange: true }"
                @current-change="currentChangeEvent"
                @cell-dblclick="
                  cellData => {
                    useCellDBLClickEvent(
                      cellData,
                      vexTable,
                      'materialMachineIndex',
                      cellDBLClickEvent
                    );
                  }
                "
              >
                <vxe-column type="seq" width="50" title="序号"> </vxe-column>
                <vxe-column
                  field="materialCode"
                  align="left"
                  width="100"
                  title="材料编码"
                >
                </vxe-column>
                <vxe-column field="materialName" align="left" title="材料名称">
                </vxe-column>
                <vxe-column field="specification" align="left" title="规格型号">
                </vxe-column>
                <vxe-column field="unit" title="单位"> </vxe-column>
                <vxe-column
                  field="baseJournalPrice"
                  width="100"
                  v-if="store.taxMade == 1"
                  align="right"
                  title="不含税基期价"
                >
                  <template #default="{ row }">
                    {{
                      Number(row.isDataTaxRate) == 0
                        ? '-'
                        : row.baseJournalPrice
                    }}
                  </template>
                </vxe-column>
                <vxe-column
                  field="baseJournalTaxPrice"
                  width="100"
                  v-else
                  align="right"
                  title="含税基期价"
                >
                  <template #default="{ row }">
                    {{
                      Number(row.isDataTaxRate) == 0
                        ? '-'
                        : row.baseJournalTaxPrice
                    }}
                  </template>
                </vxe-column>
                <!-- <vxe-column
                  field="taxRemoval"
                  title="除税系数%"
                  v-if="store.deType !== '22'"
                >
                </vxe-column> -->
              </vxe-table>
            </div>
          </div>
        </div>
      </div>
    </common-modal>
    <common-modal
      title="单位换算系数"
      width="554px"
      v-model:modelValue="unitVisible"
      className="dialog-comm"
    >
      <div class="dialog-content">
        <div class="init-text">
          替换的资源和当前资源单位不同，是否继续替换？
        </div>
        <div class="init-text">
          单位换算系数：<span>1{{ currentMaterialInfo?.unit }}&nbsp;=</span
          ><a-input
            v-model:value="conversionCoefficient"
            @keyup="
              conversionCoefficient = (conversionCoefficient.match(
                /\d{0,8}(\.\d{0,3}|100)?/
              ) || [''])[0]
            "
          /><span>{{ currentInfo?.unit }}</span>
        </div>
      </div>
      <div class="footer-btn-list">
        <a-button @click="unitClose">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import {
  reactive,
  ref,
  toRaw,
  nextTick,
  toRefs,
  watch,
  getCurrentInstance,
  watchEffect,
} from 'vue';
import api from '../../../../api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellDBLClickEvent } from '@gongLiaoJi/hooks/useCellClick';
import IndexSearch from '../inventoryAndQuotaIndex/IndexSearch.vue';
import deMapFun from '../deMap';
import { message } from 'ant-design-vue';

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const value1 = ref('dingE');
const value2 = ref('list');
const props = defineProps([
  'indexVisible',
  'currentMaterialInfo',
  'currentInfo',
  'indexLoading',
  'isDlf',
  'businessType',
]);

let currentMaterialInfoData = ref(null);
watchEffect(() => {
  currentMaterialInfoData.value = { ...toRaw(props.currentMaterialInfo) };
});

const emits = defineEmits([
  'currentQdDeInfo',
  'update:indexVisible',
  'addChildrenRcjData',
]);
const store = projectDetailStore();
let searchKey = ref();
let selectValue = ref();
let selectOptions = ref([]);
let expandedKeys = ref([]);
let queryForm = reactive({
  libraryCode: '',
  level1: '',
  level2: '',
  level3: '',
  level4: '',
  level5: '',
  level6: '',
  level7: '',
  materialName: '',
});
let scrollBeforeTop = 0;

let { materialName } = { ...toRefs(queryForm) };

const selectedKeys = ref([]);
let currentTreeInfo = ref();
let treeData = ref([]);
let currentInfo = ref();
let vexTable = ref();
let scrollSwitch = ref(false);
let page = ref(1);
let limit = ref(20);
const dialogTitle = ref('人材机索引');
let conversionCoefficient = ref('');
let unitVisible = ref(false);
let isSearch = ref(false); // 是否为点击搜索按钮查询数据

watch(
  () => props.indexVisible,
  () => {
    if (props.indexVisible) {
      queryDeLibrary();
      scrollBeforeTop = 0;
    }
  }
);
watch(
  () => selectedKeys.value,
  () => {
    nextTick(() => {
      const selectedNode = document.querySelector(
        '.dialog-wrap .ant-tree-treenode-selected'
      );
      let distanceFromTop = selectedNode?.offsetTop;
      const scrollableDiv = document.querySelector('.dialog-wrap .tree');
      if (scrollableDiv) {
        scrollableDiv.scrollTop = distanceFromTop;
      }
    });
  }
);
// 外面点击，里面联动
bus.on('currentChangeRcjEvent', value => {
  if (value && props.indexVisible) {
    queryRcjById();
  }
});

const getRcjTreeByCode = () => {
  console.log('selectValue', selectValue.value);
  api.getGsRcjTreeByCode({ libraryCode: selectValue.value }).then(res => {
    if (res.status === 200) {
      treeData.value = getInitData(res.result);
      // console.log('人材机数据', getInitData(res.result));
      if (
        !currentMaterialInfoData.value ||
        (!currentMaterialInfoData.value?.rcjId &&
          !currentMaterialInfoData.value?.standardId) //bug25359修复预算书双击定额下挂的主材，在人材机索引栏中没有正确定位
      ) {
        baseRcjInfo.value = null;
        initQuery();
        selectedKeys.value = [res.result[0].key];
        queryForm.libraryCode = selectValue.value;
        queryForm.level1 = res.result[0].materialName;
      } else {
        findRCJAndLevel(treeData.value, currentInfo.value);
      }

      queryBaseRcjLikeName();
    }
  });
};
const selectChildren = (selectedKeys, { node, event }) => {
  console.log('点击子节点', node); //选择左侧树对应右侧表格数据发生变化
  currentTreeInfo.value = node;
  initQuery();
  scrollBeforeTop = 1;
  let pathList = node.dataRef.path.split('/');
  queryForm.libraryCode = selectValue.value;
  pathList.forEach((item, index) => {
    queryForm[`level${index + 1}`] = item;
  });
  console.log('===========', queryForm);
  isSearch.value = false;
  setTimeout(() => {
    queryBaseRcjLikeName();
  }, 200);
};

const handleChange = value => {
  queryForm.libraryCode = value;
  treeData.value = [];
  currentMaterialInfoData.value = null;
  getRcjTreeByCode();
};
const tableData = ref();

const onSearch = () => {
  if ((materialName.value ?? '') === '') {
    let parentNode = findPathToRoot(treeData.value, selectedKeys.value[0]);
    if (parentNode.length > 0) {
      let currentNode = parentNode[parentNode.length - 1];
      currentTreeInfo.value = currentNode;
      initQuery();
      scrollBeforeTop = 1;
      let pathList = currentNode.path.split('/');
      queryForm.libraryCode = selectValue.value;
      pathList.forEach((item, index) => {
        queryForm[`level${index + 1}`] = item;
      });
      console.log('===========', queryForm);
      isSearch.value = false;
      setTimeout(() => {
        queryBaseRcjLikeName();
      }, 200);
    }
  } else {
    initQuery();
    scrollBeforeTop = 1;
    queryForm.libraryCode = selectValue.value;
    isSearch.value = true;
    queryForm.materialName = materialName.value;
    setTimeout(() => {
      queryBaseRcjLikeName(true);
    }, 200);
  }
};

const findPathToRoot = (tree, targetKey) => {
  let path = [];

  function dfs(node, currentPath) {
    if (node.key === targetKey) {
      path = [...currentPath, node]; // 找到目标，记录路径
      return true; // 终止搜索
    }

    if (node.childrenList && node.childrenList.length > 0) {
      for (const child of node.childrenList) {
        if (dfs(child, [...currentPath, node])) {
          return true; // 如果子节点找到，提前终止
        }
      }
    }
    return false;
  }

  // 遍历根节点
  for (const rootNode of tree) {
    if (dfs(rootNode, [])) {
      break;
    }
  }
  return path;
};

// 是否可以插入
let canInsert = ref(true)
watch(() => props.currentInfo,(newVal, oldVal)=>{
    canInsert.value = true
    if(props.indexVisible){
      canInsert.value = deMapFun.isEditRcj(props.currentInfo) && deMapFun.isDe(props.currentInfo?.kind) &&
        !['00', '01', '02', '05', '06', '09', '07', '-1'].includes(
          props.currentInfo?.kind
        ) 
    }
})

const queryDeLibrary = () => {
  api.getByLibraryAll().then(res => {
    if (res.status === 200) {
      selectOptions.value = res.result;
      console.log(
        'currentMaterialInfo',
        props.currentInfo,
        !currentMaterialInfoData.value,
        !currentMaterialInfoData.value?.rcjId
      );
      selectValue.value =
        props.currentInfo?.libraryCode ||
        store.currentTreeInfo.constructMajorType;
      if (
        !currentMaterialInfoData.value ||
        (!currentMaterialInfoData.value?.rcjId &&
          !currentMaterialInfoData.value?.standardId) //bug25359修复预算书双击定额下挂的主材，在人材机索引栏中没有正确定位
      ) {
        getRcjTreeByCode();
      } else {
        queryRcjById();
      }
    }
  });
};

const queryBaseRcjLikeName = (isSearch = false) => {
  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(queryForm)),
    page: page.value,
    limit: limit.value,
    baseRcjInfo: { ...toRaw(baseRcjInfo.value) },
  };
  let apiName = 'queryBaseRcjLikeName';
  if (isSearch) {
    apiName = 'queryAllBaseRcjLikeName';
    delete apiData.baseRcjInfo;
  }
  api[apiName](apiData).then(res => {
    console.log('🚀 ~ api.queryBaseRcjLikeName ~ res:', apiData, res);
    if (res.status === 200) {
      console.log(
        '111111111111人材机列表数据--queryBaseRcjLikeName',
        res.result,
        apiData
      );
      baseRcjInfo.value = null;

      if (page.value === 1) {
        tableData.value = [];
      }

      // 处理默认不是第一页的问题
      if (res.result?.page) {
        page.value = res.result.page;
      }

      tableData.value = [...tableData.value, ...res.result.list]; // tableData.value
      setScrollTop();
      if (
        currentMaterialInfoData.value?.rcjId ||
        currentMaterialInfoData.value?.standardId //bug25359修复预算书双击定额下挂的主材，在人材机索引栏中没有正确定位
      ) {
        let currentObj = tableData.value.filter(
          x =>
            x.materialName === currentInfo.value?.materialName &&
            x.materialCode === currentInfo.value?.materialCode
        )[0];
        console.log('getScroll', scrollBeforeTop);
        setTimeout(() => {
          if (!scrollBeforeTop) {
            nextTick(() => {
              vexTable.value.scrollToRow(currentObj);
              vexTable.value.setCurrentRow(currentObj);
            });
          } else {
            if (page.value === 1) {
              setScrollTop();
            }
          }
        }, 100);
      } else {
        let obj = tableData.value.filter(
          x => x?.sequenceNbr === currentInfo.value?.sequenceNbr
        )[0];
        if (!obj) {
          currentInfo.value = null;
          vexTable.value.clearCurrentRow();
        }
      }
      if (Math.ceil(res.result.total / limit.value) > page.value) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};

const getInitData = tree => {
  return tree.map(item => {
    item.key = item.materialName + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.materialName;
    if (!item.path || item.path.split('/').length < 2) {
      // expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.materialName + Math.ceil(Math.random() * 10000 + 1),
            path:
              (item.path ? item.path : item.materialName) +
              '/' +
              n.materialName,
          }))
        )
      : null;
    return item;
  });
};

// 选中单条清单定额数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};

const cellDBLClickEvent = ({ row }) => {
  currentInfo.value = row;
  if (currentInfo.value && !props.indexLoading && canInsert.value) {
    updateCurrentInfo(1);
  }
};

const getScroll = type => {
  if (
    Math.ceil(type.scrollTop + type.$event.target.clientHeight) >=
    type.scrollHeight
  ) {
    if (scrollSwitch.value) {
      const { scrollTop } = vexTable.value.getScroll();
      scrollBeforeTop = scrollTop;
      console.log('getScroll', scrollBeforeTop);
      page.value++;
      if (isSearch.value) {
        queryBaseRcjLikeName(true);
      } else {
        queryBaseRcjLikeName();
      }
    }
  }
};

const setScrollTop = () => {
  if (!scrollBeforeTop) return;
  setTimeout(() => {
    console.log('滚动到', scrollBeforeTop);
    vexTable.value?.scrollTo(0, scrollBeforeTop);
  }, 10);
};
const updateCurrentInfo = type => {
  // 如果是独立费页面
  if (props.isDlf) {
    emits('currentRcjInfo', currentInfo.value, type);
    return;
  }
  if (type === 2) {
    console.log(
      'updateCurrentInfo',
      currentInfo.value.unit,
      currentMaterialInfoData.value?.unit
    );
    if (
      currentInfo.value.unit !== currentMaterialInfoData.value?.unit &&
      currentMaterialInfoData.value
    ) {
      unitVisible.value = true;
      return;
    }
  }
  if (type === 1) {
    if (currentMaterialInfoData.value && !currentMaterialInfoData.value.pbs) {
      emits(
        'addChildrenRcjData',
        currentInfo.value,
        currentMaterialInfoData.value
      );
      return;
    }
    emits('currentRcjInfo', currentInfo.value);
  } else {
    emits('currentInfoReplace', currentInfo.value);
  }
  // allInit();
};
const close = () => {
  emits('update:indexVisible', false);
  bus.emit('focusTableData');
  allInit();
};

const initQuery = () => {
  queryForm = Object.assign(queryForm, {
    level1: '',
    level2: '',
    level3: '',
    level4: '',
    level5: '',
    level6: '',
    level7: '',
  });
  setTimeout(() => {
    page.value = 1;
  }, 200);
};

const allInit = () => {
  initQuery();
  materialName.value = '';
  queryForm.materialName = '';
  treeData.value = [];
  tableData.value = [];
  expandedKeys.value = [];
  selectedKeys.value = [];
  currentTreeInfo.value = null;
  currentInfo.value = null;
  conversionCoefficient.value = '';
};

const unitClose = () => {
  unitVisible.value = false;
  conversionCoefficient.value = null;
  bus.emit('focusTableData');
};

const handleOk = () => {
  if (!conversionCoefficient.value) {
    return message.warning('请输入单位换算系数！');
  }

  if (conversionCoefficient.value === '0') {
    return message.warning('转换系数不可为0');
  }

  currentInfo.value.conversionCoefficient = conversionCoefficient.value;
  conversionCoefficient.value = null;
  console.log('66666666666', currentInfo.value);
  unitVisible.value = false;
  emits('currentInfoReplace', currentInfo.value);
  // allInit();
};

let baseRcjInfo = ref(null);
// 查询当前人材机数据用于反向定位
const queryRcjById = () => {
  let apiData = {
    standardId:
      currentMaterialInfoData.value.rcjPbsId ||
      currentMaterialInfoData.value.rcjId ||
      currentMaterialInfoData.value.standardId, //bug25359修复预算书双击定额下挂的主材，在人材机索引栏中没有正确定位
    libraryCode: currentMaterialInfoData.value.libraryCode,
  };
  if (currentMaterialInfoData.value.rcjPbsId) {
    apiData.materialCode = currentMaterialInfoData.value.materialCode;
  }
  api.queryRcjById(apiData).then(res => {
    console.log('dddddddd12dddddddcurrentObj', apiData, res);
    if (res.status === 200 && res.result) {
      selectValue.value = res.result.libraryCode;
      currentInfo.value = res.result;
      queryForm = {
        ...queryForm,
        libraryCode: res.result.libraryCode,
        level1: res.result.level1,
        level2: res.result.level2 || '',
        level3: res.result.level3 || '',
        level4: res.result.level4 || '',
        level5: res.result.level5 || '',
        level6: res.result.level6 || '',
        level7: res.result.level7 || '',
      };

      // 为了后端传基础信息
      baseRcjInfo.value = res.result;
    }
    getRcjTreeByCode();
  });
};

// 反向查找人材机指定数据并展开选中
const findRCJAndLevel = (tree, targetData, level = 1) => {
  // console.log('targetData',targetData)
  if (!targetData) return;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.materialName === targetData[`level${level}`]) {
      expandedKeys.value.push(node.key);
      selectedKeys.value = [node.key];
    }
    if (node.childrenList) {
      const result = findRCJAndLevel(
        node.childrenList,
        currentInfo.value,
        level + 1
      );
      if (result) {
        console.log('反向定位数据', result);
        return result;
      }
    }
  }

  return null;
};
</script>

<style lang="scss" scoped>
// .custom-title {
//   display: flex;
//   align-items: center;
//   padding: 8px 20px 8px 0;
//   .title {
//     position: relative;
//     padding-left: 20px;
//     font-size: 16px;
//     line-height: 22px;
//     font-weight: 500;
//     flex: 1;
//     color: rgb(30, 144, 255);
//     &::after {
//       position: absolute;
//       top: 50%;
//       left: 0;
//       transform: translateY(-50%);
//       content: "";
//       display: block;
//       width: 2px;
//       height: 11px;
//       background: #1890ff;
//     }
//   }
//   img {
//     display: inline-block;
//     width: auto;
//     padding: 0 5px;
//     height: 16px;
//   }
// }
.dialog-wrap {
  width: 1000px;
}
.contentCenter {
  display: flex;
  justify-content: space-around;
  color: #b9b9b9;
  margin-top: 10px;
  height: calc(100% - 50px);
  .left {
    width: 35%;
    .search {
      width: 100%;
      // height: 350px;
      border: 1px solid #b9b9b9;
      .tree {
        width: 330px;
        height: 250px;
        // border: 1px solid #dcdfe6;
        margin-left: 10px;
        overflow: hidden;
        &:hover {
          overflow: auto;
        }
      }
    }
  }
  .right {
    width: 60%;
    .btns {
      display: flex;
      width: 100%;
      justify-content: space-between;
      .btnNo1 {
        margin: 0 15px;
      }
    }
    .table {
      height: 350px;
    }
  }
}
.dialog-content {
  padding: 46px;
  text-align: center;
}
.init-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  .icon {
    margin-right: 5px;
  }
  span {
    color: #1e90ff;
    margin: 0 2px;
  }
  .ant-input {
    width: 105px;
    margin-left: 5px;
  }
}
.init-text:nth-last-of-type(1) {
  margin-top: 25px;
}
.footer-btn-list {
  padding-bottom: 20px;
}
</style>
