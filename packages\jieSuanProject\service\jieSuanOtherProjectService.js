'use strict';


const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ResponseData } = require('../../../electron/utils/ResponseData');
const { Service } = require('../../../core');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const JieSuanSettlementTypeEnum = require('../enum/JieSuanSettlementTypeEnum');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const OtherProjectCalculationBaseConstant = require('../../../electron/enum/OtherProjectCalculationBaseConstant');
const { FormulaCalculateUtil } = require('../../../common/FormulaCalculateUtil');
const { ObjectUtils } = require('../../../electron/utils/ObjectUtils');
const { number } = require('mathjs');
const { OtherProjectServiceCost } = require('../../../electron/model/OtherProjectServiceCost');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const OtherProjectServiceCostService = require('../../../electron/service/otherProjectServiceCostService');

/**
 * 结算其他项目Service
 */
class JieSuanOtherProjectService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 初始化 结算 其他项目数据
   */
  async initOtherProjectAllData(args) {
    try {
      // 其他项目总表
      await this.initOtherProjectData(args);
      // 暂列金
      await this.initProvisionalData(args);
      // 专业工程暂估价
      await this.initZygczgjData(args);
      // 总承包服务费
      await this.initServiceCostsData(args);
      // 计日工
      await this.initDayWorkerData(args);
    } catch (e) {
      console.error('审核项目-初始化其他项目数据异常', e);
    }
  }

  /**
   * 其他项目总表数据初始化
   */
  async initOtherProjectData(args) {
    const { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProject)) {
      return;
    }
    for (const item of otherProject) {
      // 合同数量 = 结算数量
      item.jieSuanAmount = item.amount;
      item.amount = null;
      // 合同金额 = 结算金额 = 结算数量*对应子页面金额之和
      item.jieSuanTotal = item.total;
      item.total = null;
      // 合同进项税额 = 结算进项税额
      item.jieSuanJxTotal = item.jxTotal;
      item.jxTotal = null;
      // 合同除税合计 = 结算除税合计
      item.jieSuanCsTotal = item.csTotal;
      item.csTotal = null;
      if (unit.originalFlag) {
        // 标记初始数据，表示为合同内数据
        item.jiesuanOriginal = 1;
      }
    }
  }

  /**
   * 暂列金数据初始化
   */
  async initProvisionalData(args) {
    const { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectProvisional = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectProvisional)) {
      return;
    }
    for (const item of otherProjectProvisional) {
      // 合同数量 = 结算数量
      item.jieSuanAmount = item.amount;
      item.amount = null;
      // 合同单价 = 结算单价
      item.jieSuanPrice = item.price;
      item.price = null;
      // 合同暂定金额 = 结算暂定金额 = 【单价】*【数量】
      item.jieSuanProvisionalSum = item.provisionalSum;
      item.provisionalSum = null;
      // 合同除税系数 = 结算除税系数
      item.jieSuanTaxRemoval = item.taxRemoval;
      item.taxRemoval = null;
      // 合同进项合计 = 结算进项合计 = 暂定金额*除税系数%
      item.jieSuanJxTotal = item.jxTotal;
      item.jxTotal = null;
      // 合同除税单价 = 结算除税单价 = 单价*（1-除税系数%）
      item.jieSuanCsPrice = item.csPrice;
      item.csPrice = null;
      // 合同除税合计 = 结算除税合计 = 【暂列金额】*（1-除税系数%）
      item.jieSuanCsTotal = item.csTotal;
      item.csTotal = null;
      if (unit.originalFlag) {
        // 标记初始数据，表示为合同内数据
        item.jiesuanOriginal = 1;
      }
    }
  }

  /**
   * 专业工程暂估价数据初始化
   */
  async initZygczgjData(args) {
    const { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectZygcZgjList = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectZygcZgjList)) {
      return;
    }
    for (const item of otherProjectZygcZgjList) {
      // 合同数量 = 结算数量
      item.jieSuanAmount = item.amount;
      item.amount = null;
      // 合同单价 = 结算单价
      item.jieSuanPrice = item.price;
      item.price = null;
      // 合同金额 = 结算金额 = 【结算单价】*【结算数量】
      item.jieSuanTotal = item.total;
      item.total = null;
      // 合同除税系数(%) = 结算除税系数(%)
      item.jieSuanTaxRemoval = item.taxRemoval;
      item.taxRemoval = null;
      // 合同进项合计 = 结算进项合计 = 结算金额*结算除税系数%
      item.jieSuanJxTotal = item.jxTotal;
      item.jxTotal = null;
      // 合同除税单价 = 结算除税单价 = 结算单价*（1-结算除税系数%）
      item.jieSuanCsPrice = item.csPrice;
      item.csPrice = null;
      // 合同除税合计 = 结算除税合计 = 【结算金额】*（1-结算除税系数%）
      item.jieSuanCsTotal = item.csTotal;
      item.csTotal = null;
      if (unit.originalFlag) {
        // 标记初始数据，表示为合同内数据
        item.jiesuanOriginal = 1;
      }
    }
  }

  /**
   * 总承包服务费数据初始化
   */
  async initServiceCostsData(args) {
    const { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      return;
    }
    for (const item of otherProjectServiceCost) {
      // 结算方式   默认：同合同合价
      item.settlementType = JieSuanSettlementTypeEnum.custom.code;
      // 结算金额 = 数量*项目价值*费率
      item.jieSuanFwje = item.fwje;
      if (unit.originalFlag) {
        // 标记初始数据，表示为合同内数据
        item.jiesuanOriginal = 1;
      }
    }
    // 由于总承包服务费有初始的结算金额  所以初始化完就需要汇总到总表
    await this.updateServiceCostJieSuanFwje(args);
  }

  /**
   * 计日工数据初始化
   */
  async initDayWorkerData(args) {
    const { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const otherProjectDayWork = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectDayWork)) {
      return;
    }
    for (const item of otherProjectDayWork) {
      // 表达式
      item.quantitativeExpression = null;
      // 合同数量 = 结算数量
      item.jieSuanTentativeQuantity = item.tentativeQuantity;
      item.tentativeQuantity = null;
      // 合同综合单价 = 结算综合单价
      item.jieSuanPrice = item.price;
      item.price = null;
      // 合同合价 = 结算合价 = 【结算综合单价】*【结算数量】
      item.jieSuanTotal = item.total;
      item.total = null;
      // 合同除税系数(%) = 结算除税系数(%)
      item.jieSuanTaxRemoval = item.taxRemoval;
      item.taxRemoval = null;
      // 合同进项合计 = 结算进项合计 = 结算金额*结算除税系数%
      item.jieSuanJxTotal = item.jxTotal;
      item.jxTotal = null;
      // 合同除税单价 = 结算除税单价 = 结算单价*（1-结算除税系数%）
      item.jieSuanCsPrice = item.csPrice;
      item.csPrice = null;
      // 合同除税合价 = 结算除税合价 = 【结算金额】*（1-结算除税系数%）
      item.jieSuanCsTotal = item.csTotal;
      item.csTotal = null;
      if (unit.originalFlag) {
        // 标记初始数据，表示为合同内数据
        item.jiesuanOriginal = 1;
      }
    }
  }

  /**
   * 获取总承包服务的结算方式
   */
  async getServiceCostSettlementType(args) {
    const resArr = [];
    for (const [key, { code, value }] of Object.entries(JieSuanSettlementTypeEnum)) {
      resArr.push({ code: code, value: value });
    }
    return ResponseData.success(resArr);
  }

  async updateServiceCost(args) {
    // settlementType 表示结算方式  对应JieSuanSettlementTypeEnum
    // jieSuanFwje 表示结算金额
    const { constructId, singleId, unitId, sequenceNbr, settlementType, jieSuanFwje } = args;
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      throw new Error('总承包服务费数据异常');
    }
    const item = otherProjectServiceCost.find(item => item.sequenceNbr == sequenceNbr);
    if (ObjectUtil.isEmpty(item)) {
      throw new Error('修改的数据不存在');
    }
    if (item.dataType != 2) {
      // 标题行不能修改结算方式和结算金额
      throw new Error('仅支持数据行修改');
    }
    item.settlementType = settlementType;
    if (settlementType == JieSuanSettlementTypeEnum.equalContractTotal.code) {
      // 同合同金额
      item.jieSuanFwje = item.fwje;
    } else if (settlementType == JieSuanSettlementTypeEnum.baseCalculation.code) {
      // 按计算基数
      item.jieSuanFwje = NumberUtil.multiply(item.xmje, item.amount, NumberUtil.divide100(item.rate));
    } else if (settlementType == JieSuanSettlementTypeEnum.custom.code) {
      // 直接输入
      item.jieSuanFwje = null;
    }

    // 修改标题行的结算金额
    const parentItem = otherProjectServiceCost.find(i => i.sequenceNbr == item.parentId);
    const sameLevelItem = otherProjectServiceCost.filter(i => i.parentId == item.parentId);
    if (ObjectUtil.isNotEmpty(parentItem) && ObjectUtil.isNotEmpty(sameLevelItem)) {
      let total = 0;
      for (const { jieSuanFwje } of sameLevelItem) {
        total = NumberUtil.add(total, jieSuanFwje);
      }
      parentItem.jieSuanFwje = total;
    }

    return ResponseData.success(true);
  }

  /**
   * 获取总承包服务费的结算金额总和
   */
  async getServiceCostJieSuanFwje(args) {
    const { constructId, singleId, unitId } = args;
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      return;
    }
    // 筛选数据行
    const childrenItem = otherProjectServiceCost.filter(item => item.dataType == 2);
    if (ObjectUtil.isEmpty(childrenItem)) {
      return;
    }
    let total = 0;
    for (const item of childrenItem) {
      total = NumberUtil.add(total, item.jieSuanFwje);
    }
    return total;
  }

  // 操作其他项目总承包服务费数据
  async operateOtherProjectServiceCost(arg) {
    //操作 类型  1:插入 2:粘贴 3删除 4 修改
    let operateType = arg.operateType;

    switch (operateType) {
      case 1:
        await this.addProjectServiceCost(arg);
        break;
      case 2:
        await this.pasteProjectServiceCost(arg);
        break;
      case 3:
        await this.delectProjectServiceCost(arg);
        break;
      case 4:
        await this.updateProjectServiceCost(arg);
        break;
    }
    // 更新总表
    await this.updateServiceCostJieSuanFwje(arg);
  }

  /**
   * 新增总承包服务费数据
   */
  async addProjectServiceCost(arg) {
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let targetSequenceNbr = arg.targetSequenceNbr;
    let dataType = arg.dataType;

    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let list = unit.otherProjectServiceCosts;

    let number;
    if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
      number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

    } else {
      number = 0;
    }
    let otherProjectServiceCost1 = new OtherProjectServiceCost();
    otherProjectServiceCost1.sequenceNbr = Snowflake.nextId();

    otherProjectServiceCost1.fwje = OtherProjectServiceCostService.defaultXmje.toFixed(2);
    // 结算的结算金额默认为0
    otherProjectServiceCost1.jieSuanFwje = OtherProjectServiceCostService.defaultXmje.toFixed(2);
    // 结算方式默认为直接输入
    otherProjectServiceCost1.settlementType = JieSuanSettlementTypeEnum.custom.code;

    otherProjectServiceCost1.dataType = dataType;

    if (dataType === OtherProjectServiceCostService.datyTypeShuJu) {
      //otherProjectServiceCost1.amount = Number(1).toFixed(6);
      otherProjectServiceCost1.rate = OtherProjectServiceCostService.defaultRate.toFixed(2);
      otherProjectServiceCost1.xmje = OtherProjectServiceCostService.defaultRate.toFixed(2);
      if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
        let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (projectService.dataType === OtherProjectServiceCostService.datyTypeBiaoTi) {
          otherProjectServiceCost1.parentId = targetSequenceNbr;
          number = number + 1;
        } else {
          otherProjectServiceCost1.parentId = projectService.parentId;

        }
      }
    }

    list.splice(number, 0, otherProjectServiceCost1);
  }

  /**
   * 粘贴总承包服务费 数据
   */
  async pasteProjectServiceCost(arg) {
    await this.service.otherProjectServiceCostService.pasteProjectServiceCost(arg);
    // 进行标题行数据汇总  由于粘贴调用预算代码  无法明确获得粘贴后的数据  所以直接对所有标题行进行汇总
    await this.updateServiceCostTitleData(null, arg.constructId, arg.singleId, arg.unitId);
  }

  async delectProjectServiceCost(arg) {
    await this.service.otherProjectServiceCostService.delectProjectServiceCost(arg);
    // 进行标题行数据汇总  由于删除调用预算代码  无法明确获得删除后的数据  所以直接对所有标题行进行汇总
    await this.updateServiceCostTitleData(null, arg.constructId, arg.singleId, arg.unitId);
  }

  async updateProjectServiceCost(arg) {
    let constructId = arg.constructId;
    let singleId = arg.singleId;
    let unitId = arg.unitId;
    let targetSequenceNbr = arg.targetSequenceNbr;

    let fxName = arg.projectServiceCost.fxName;
    let xmje = arg.projectServiceCost.xmje;
    let rate = arg.projectServiceCost.rate;
    let dispNo = arg.projectServiceCost.dispNo;
    let unitBj = arg.projectServiceCost.unit;
    let serviceContent = arg.projectServiceCost.serviceContent;
    let settlementType = arg.projectServiceCost.settlementType;
    let jieSuanFwje = arg.projectServiceCost.jieSuanFwje;


    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let list = unit.otherProjectServiceCosts;
    let projectServiceCost = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
    if (!ObjectUtils.isEmpty(fxName)) {
      projectServiceCost.fxName = fxName;
    }

    if (!ObjectUtils.isEmpty(xmje)) {
      projectServiceCost.xmje = xmje;
    }

    if (!ObjectUtils.isEmpty(rate)) {
      projectServiceCost.rate = rate;
    }

    if (!ObjectUtils.isEmpty(dispNo)) {
      projectServiceCost.dispNo = dispNo;
    }

    if (!ObjectUtils.isEmpty(unitBj)) {
      projectServiceCost.unit = unitBj;
    }

    if (!ObjectUtils.isEmpty(serviceContent)) {
      projectServiceCost.serviceContent = serviceContent;
    }

    if (settlementType == JieSuanSettlementTypeEnum.custom.code) {
      // 直接输入
      projectServiceCost.jieSuanFwje = jieSuanFwje;
      if (settlementType != projectServiceCost.settlementType) {
        // 如果原来不是直接输入  说明是刚改的  需要把结算金额置空
        projectServiceCost.jieSuanFwje = null;
      }
    } else if (settlementType == JieSuanSettlementTypeEnum.equalContractTotal.code) {
      // 同合同价
      projectServiceCost.jieSuanFwje = projectServiceCost.xmje;
    } else if (settlementType == JieSuanSettlementTypeEnum.baseCalculation.code) {
      // 按计算基数
      projectServiceCost.jieSuanFwje = NumberUtil.multiplyToString(projectServiceCost.xmje,
        NumberUtil.multiply(projectServiceCost.rate, 0.01), OtherProjectServiceCostService.decimalPlaces);
    }
    projectServiceCost.settlementType = settlementType;

    if (projectServiceCost.dataType == 2) {
      // 如果修改的是数据行  需要进行标题行数据汇总
      await this.updateServiceCostTitleData(projectServiceCost.parentId, constructId, singleId, unitId);
    }
  }

  /**
   * 汇总总承包服务费的标题行数据
   * sequenceNbr为标题行的数据id  如果为空则汇总所有
   */
  async updateServiceCostTitleData(sequenceNbr, constructId, singleId, unitId) {
    const otherProjectServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(otherProjectServiceCost)) {
      return;
    }
    let titleArray = otherProjectServiceCost.filter(item => item.dataType == 1);
    if (ObjectUtil.isNotEmpty(sequenceNbr)) {
      titleArray = titleArray.filter(item => item.sequenceNbr == sequenceNbr);
    }
    if (ObjectUtil.isEmpty(titleArray)) {
      return;
    }
    for (const titleItem of titleArray) {
      const childrenArray = otherProjectServiceCost.filter(item => item.parentId == titleItem.sequenceNbr);
      if (ObjectUtil.isEmpty(childrenArray)) {
        continue;
      }
      let value = 0;
      for (const child of childrenArray) {
        value = NumberUtil.add(value, child.jieSuanFwje);
      }
      titleItem.jieSuanFwje = value;
    }
  }

  /**
   * 更新 其他项目总表 的 总承包服务费的结算金额
   */
  async updateServiceCostJieSuanFwje(args) {
    const { constructId, singleId, unitId } = args;
    let zcbfwfObject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId).filter(item => item.type == OtherProjectCalculationBaseConstant.zcbfwf);
    if (ObjectUtil.isEmpty(zcbfwfObject)) {
      return;
    }
    zcbfwfObject = zcbfwfObject[0];
    if (zcbfwfObject.calculationBase == 'ZCBFWF') {
      const total = await this.getServiceCostJieSuanFwje(args);
      // 如果计算基数就是总承包服务费  那么就不用计算别的了  直接使用
      zcbfwfObject.total = total;
    } else {
      // 如果不是默认的ZCBFWF   那就需要进行计算了
      if (ObjectUtil.isNotEmpty(zcbfwfObject.calculationBase)) {
        const costCodePrice = this.service.otherProjectService.costCodePrice(args);
        let codePriceMap = new Map();
        costCodePrice.forEach(a => codePriceMap.set(a.code, a.price));
        // 重新设置新的总承包服务费
        const total = await this.getServiceCostJieSuanFwje(args);
        costCodePrice.set('ZCBFWF', total);
        let doCalculate = FormulaCalculateUtil.doCalculate(zcbfwfObject.calculationBase, codePriceMap);
        if (ObjectUtils.isEmpty(doCalculate) || !doCalculate instanceof number) {
          throw new Error('公式存在未知引用，请检查并修改');
        }

        let rate = 100;
        //修改合计计算逻辑  需要乘以费率
        if (ObjectUtils.isNotEmpty(zcbfwfObject.rate)) {
          rate = zcbfwfObject.rate;
        }
        zcbfwfObject.total = doCalculate * zcbfwfObject.amount * NumberUtil.divide100(rate);
      }
    }
  }

}

JieSuanOtherProjectService.toString = () => '[class JieSuanOtherProjectService]';
module.exports = JieSuanOtherProjectService;
