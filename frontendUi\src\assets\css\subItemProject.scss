.association-selected {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
}
.normal-info .code-flag {
  color: #a73d3d;
}
.code-black {
  color: #2a2a2a;
}
.flag-green {
  background: #90c942;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-orange {
  background: #ffaa09;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-red {
  background: #de3f3f;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.multiple-select {
  height: 16px;
  line-height: 16px;
  //text-indent: 10px;
  cursor: pointer;
}
.subItem-project {
  background: #ffffff;
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    // height: 40px;
    padding: 0 10px;
  }
  .table-content {
    // height: calc(65%);
    height: 100%;
    //user-select: none;

    ::v-deep(.vxe-table .row-unit) {
      background: #e6dbeb;
    }
    ::v-deep(.vxe-table .row-sub) {
      background: #efe9f2;
    }
    ::v-deep(.vxe-table .row-qd) {
      background: #dce6fa;
    }
    ::v-deep(.vxe-body--row.row--current) {
      background: #a6c3fa;
    }
    ::v-deep(.vxe-table .row-qd .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
      }
    }
    ::v-deep(.vxe-table .normal-info .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        // text-overflow: ellipsis;
        //max-height: 3.0em; /* 高度为字体大小的两倍 */
        //line-height: 1.5em; /* 行高 */
        //height: auto; /* 高度为行高的两倍 */
      }
    }
    ::v-deep(.vxe-table .index-bg) {
      background-color: #ffffff;
    }
    ::v-deep(.vxe-table .temp-delete) {
      background: #f3f2f3;
      color: #a7a7a7;
      text-decoration: line-through;
    }
    ::v-deep(
        .vxe-table--render-default.is--tree-line
          .vxe-body--row
          .vxe-body--column
      ) {
      background-image: linear-gradient(#b9b9b9, #b9b9b9),
        linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
      background-image: linear-gradient(#b9b9b9, #b9b9b9),
        linear-gradient(#b9b9b9, #b9b9b9);
    }
  }
  .quota-content {
    // height: 35%;
    height: 100%;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    //user-select: none;
  }
  .project-attr {
    white-space: pre-wrap;
    text-align: left;
  }
}
.content {
  margin-bottom: 20px;
}
.edit-content {
  margin-bottom: 20px;
}
.my-dropdown4 {
  width: 600px;
  height: 200px;
  overflow: auto;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}

.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.reCheck {
  margin-top: 10px;
}
.code {
  margin-left: 8px;
}
.custom-header .icon-close {
  right: 20px;
  background: #ffffff;
  z-index: 20;
  padding: 3px;
}
.ceilingPrice-wrap {
  ::v-deep(.vxe-input--extra-suffix) {
    display: none;
  }
  ::v-deep(.vxe-input--inner) {
    padding-right: 0 !important;
  }
}
