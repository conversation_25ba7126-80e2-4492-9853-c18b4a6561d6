<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-11-15 16:56:26
 * @LastEditors: liuxia
 * @LastEditTime: 2024-10-29 17:38:39
-->
<template>
  <common-modal
    className="dialog-comm set-dialog"
    v-model:modelValue="visible"
    title="设置"
    @close="close"
  >
    <div class="content">
      <div class="list-label">
        <div
          :class="{ active: item.key === tabKey, label: true }"
          for=""
          v-for="item in tabList"
          :key="item.key"
          @click="tabChange(item.key)"
        >
          {{ item.name }}
        </div>
      </div>

      <div class="list-content" v-if="tabKey === 'file'">
        <div class="item">
          <span class="title">文件下载路径：</span>
          <div class="path">
            <button class="change" @click="setData('FILE_DOWNLOAD_PATH')">
              更改路径
            </button>
            <span>{{ FILE_DOWNLOAD_PATH }}</span>
          </div>
        </div>
        <div class="item">
          <span class="title">默认数据存储路径：</span>
          <div class="path">
            <button class="change" @click="setData('DEF_SAVE_PATH')">
              更改路径
            </button>
            <span>{{ DEF_SAVE_PATH }}</span>
          </div>
        </div>
      </div>
      <div class="list-content" v-if="tabKey === 'setting'">
        <a-radio-group
          v-if="projectAttrVisible"
          @click.stop="onChange($event, 'settingValue')"
          v-model:value="settingValue"
          name="radioGroup"
        >
          <a-radio value="1">推荐项目特征选中后自动关联组价方案</a-radio>
        </a-radio-group>

        <a-radio-group
          v-if="rgfInMeasureAndRPriceInMechanicalVisible"
          @click.stop="
            onChange($event, 'rgfInMeasureAndRPriceInMechanicalAction')
          "
          v-model:value="rgfInMeasureAndRPriceInMechanicalAction"
          name="radioGroup2"
          style="margin-top: 20px"
        >
          <a-radio value="1"
            >以系数计算的措施项目和机械台班中的人工单价参与调整</a-radio
          >
        </a-radio-group>
        <a-radio-group
          @click.stop="onChange($event, 'standardConversionShowFlag')"
          v-model:value="standardConversionShowFlag"
          name="radioGroup2"
          style="margin-top: 20px"
        >
          <a-radio :value="true">单位工程中标准换算弹窗展示</a-radio>
        </a-radio-group>
        <a-radio-group
          @click.stop="onChange($event, 'mainRcjShowFlag')"
          v-model:value="mainRcjShowFlag"
          name="radioGroup2"
          style="margin-top: 20px; display: block"
        >
          <a-radio :value="true">单位工程中主材弹窗展示</a-radio>
        </a-radio-group>

        <a-radio-group
          @click.stop="onChange($event, 'deGlTcFlag')"
          v-model:value="deGlTcFlag"
          name="radioGroup3"
          style="margin-top: 20px; display: block"
        >
          <a-radio :value="true">展示定额关联子目弹窗</a-radio>
        </a-radio-group>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { ref } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
let FILE_DOWNLOAD_PATH = ref();
let DEF_SAVE_PATH = ref();
let projectAttrVisible = ref(false);
let rgfInMeasureAndRPriceInMechanicalVisible = ref(false);
const emits = defineEmits(['closePopup']);

let tabList = [
  {
    key: 'file',
    name: '文件管理',
  },
  {
    key: 'setting',
    name: '便捷性设置',
  },
];
let tabKey = ref('file');

const tabChange = key => {
  tabKey.value = key;
};

let settingValue = ref('1');
let rgfInMeasureAndRPriceInMechanicalAction = ref('1');
let mainRcjShowFlag = ref(true); // 设置主材弹框
let standardConversionShowFlag = ref(true); // 设置标准换算弹框
let deGlTcFlag = ref(false);

const onChange = (e, v) => {
  if (e.target.nodeName.toLowerCase() === 'input') {
    if (v == 'settingValue') {
      settingValue.value = settingValue.value === '1' ? '' : '1';
    } else if (v === 'deGlTcFlag') {
      deGlTcFlag.value = !deGlTcFlag.value;
    } else if (v === 'standardConversionShowFlag') {
      standardConversionShowFlag.value = !standardConversionShowFlag.value;
    } else if (v === 'mainRcjShowFlag') {
      mainRcjShowFlag.value = !mainRcjShowFlag.value;
    } else {
      rgfInMeasureAndRPriceInMechanicalAction.value =
        rgfInMeasureAndRPriceInMechanicalAction.value === '1' ? '' : '1';
    }
    if (v === 'deGlTcFlag') {
      projectConvenientSetColl();
    } else if (v === 'standardConversionShowFlag') {
      standardConversionShowFlagColl();
    } else if (v === 'mainRcjShowFlag') {
      mainRcjShowFlagColl();
    } else {
      setRelateMergeScheme();
    }
  }
};

const projectConvenientSetColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    column: 'deGlTcFlag',
    value: deGlTcFlag.value,
  };
  api.projectConvenientSetColl(apiData).then(res => {
    if (res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    }
  });
};

const queryProjectConvenientSetColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  api.queryProjectConvenientSetColl(apiData).then(res => {
    console.log('查询便捷性设置', res);
    if (res.status === 200 && res.result) {
      deGlTcFlag.value = res.result.deGlTcFlag;
    }
  });
};

const setRelateMergeScheme = () => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    projectAttrRelateMergeScheme: !!settingValue.value,
    rgfInMeasureAndRPriceInMechanicalAction:
      !!rgfInMeasureAndRPriceInMechanicalAction.value,
  };
  csProject.projectAttrRelateMergeSchemeSet(params).then(res => {
    if (res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error('设置失败');
    }
  });
};
const visible = ref(true);
const close = () => {
  emits('closePopup');
};

const getData = () => {
  csProject
    .getSetUp({ constructId: projectStore.currentTreeGroupInfo?.constructId })
    .then(res => {
      if (res.result) {
        console.log('返回数据', res.result);
        res.result.forEach(e => {
          switch (e.paramsTag) {
            case 'DEF_SAVE_PATH':
              DEF_SAVE_PATH.value = e.content;
              break;
            case 'FILE_DOWNLOAD_PATH':
              FILE_DOWNLOAD_PATH.value = e.content;
              break;
            case 'PROJECTATTR':
              projectAttrVisible.value = true;
              settingValue.value = e.content ? '1' : '';
              break;
            case 'RGFINMEASUREANDRPRICEINMECHANICALACTION':
              rgfInMeasureAndRPriceInMechanicalVisible.value = true;
              rgfInMeasureAndRPriceInMechanicalAction.value = e.content
                ? '1'
                : '';
              break;
            case 'mainRcjShowFlag':
              mainRcjShowFlag.value = e.content;
              break;
            case 'standardConversionShowFlag':
              standardConversionShowFlag.value = e.content;
              break;
          }
        });
      }
    });
};

const setData = paramsTag => {
  csProject.setSetUp({ paramsTag }).then(res => {
    console.log(
      '🚀 ~ file: setUpPopup.vue:69 ~ csProject.setSetUp ~ res:',
      res
    );
    if (res.status == 200) {
      switch (res.result) {
        case 0:
          console.log('点击了取消');
          break;
        case 1:
          getData();
          break;
        default:
          break;
      }
    }
  });
};

// 设置主材
const mainRcjShowFlagColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    mainRcjShowFlag: mainRcjShowFlag.value,
  };
  api.mainRcjShowFlagColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error('设置失败');
    }
  });
};

// 设置标准换算
const standardConversionShowFlagColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    standardConversionShowFlag: standardConversionShowFlag.value,
  };
  api.standardConversionShowFlagColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error('设置失败');
    }
  });
};

getData();
queryProjectConvenientSetColl();
</script>

<style lang="scss">
.set-dialog {
  width: 60%;
  max-width: 600px;
  min-width: 200px;
  .vxe-modal--content {
    padding-bottom: 0 !important;
  }
  .content {
    display: flex;
    .list-label {
      min-height: 260px;
      padding-bottom: 20px;
      border-right: 1px solid rgba(224, 224, 224, 1);
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #2a2a2a;
        opacity: 1;
        padding: 4px 20px 4px 0;
        margin-bottom: 10px;
        border-right: 1px solid transparent;
        white-space: nowrap;
        cursor: pointer;
        &.active {
          border-right-color: #287cfa;
          color: #287cfa;
        }
      }
    }
    .list-content {
      padding: 0 60px 40px 40px;
      .item {
        margin-bottom: 23px;
        .title {
          display: block;
          font-size: 14px;
          font-weight: 400;
          color: #2a2a2a;
          margin-bottom: 20px;
        }
        .path {
          display: flex;
          align-items: center;
          .change {
            background: rgba(255, 255, 255, 0.39);
            border: 1px solid #bfbfbf;
            opacity: 1;
            border-radius: 3px;
            font-size: 14px;
            font-weight: 400;
            color: #2a2a2a;
            outline: none;
            padding: 6px 10px;
          }
          span {
            font-size: 14px;
            color: #898989;
            margin-left: 15px;
          }
        }
      }
    }
  }
}
</style>
