<template>
  <common-modal
    className="notice-beta modal betaHeader"
    title=" "
    width="963"
    height="630"
    v-model:modelValue="props.noticeBetaModal"
    :mask="false"
  >
    <div class="header">
      <span class="title">公测公告</span>
      <!-- <icon-font
        class="icon-font"
        type="icon-guanbi"
        @click="cancel"
      ></icon-font> -->
    </div>
    <div class="content">
      <p>尊敬的用户：</p>
      <div>
        感谢您关注并选择参与我们的工程建设行业计价软件平台公测！我们诚挚邀请您作为首批用户，共同体验我们的产品，并提供宝贵的反馈意见，帮助我们不断改进和优化。
      </div>
      <div class="detail">
        <div>
          1、通过平台您可以免费体验到包含但不限于概算计价、预算计价、结算计价、审核计价等多业务模块；
        </div>
        <div>
          2、反馈渠道：您可以通过平台内的意见反馈页提交反馈，反馈内容包含但不限于使用体验、功能建议和改进建议。快速反馈通道：<span
            class="blue"
            @click="openExternal('https://www.yunsuanfang.com/feedback')"
            >https://www.yunsuanfang.com/feedback</span
          >；
        </div>
        <div>3、公测期间可能会遇到系统稳定性和功能性调整，敬请谅解；</div>
        <div>
          4、公测期间我们将扩大测试范围，您可以邀请身边的同行业人员一起公测体验，并通过扫描下方二维码，参与我们【公测赢福利
          纵享三重礼】的线上活动赢取现金红包；
        </div>
        <div>
          5、如有任何疑问或需要进一步信息，请随时联系我们的客户服务团队：<span
            class="blue"
            >400-005-8008</span
          >。
        </div>
      </div>
      <div class="qr-code-imgs">
        <div class="img">
          <img src="@/assets/img/gongzhonghao-qrcode.png" alt="" />
          <div class="name">云算房公众号</div>
        </div>
        <div class="img">
          <img src="@/assets/img/huodong-qrcode.png" alt="" />
          <div class="name">活动二维码</div>
        </div>
      </div>
    </div>
    <div class="footer">
      <!-- <a-button @click="openDialog">公测推荐</a-button> -->
      <a-button type="primary" @click="cancel">知道了</a-button>
    </div>
  </common-modal>
</template>

<script setup>
const props = defineProps(['noticeBetaModal']);
const emits = defineEmits(['cancel', 'openDialog']);

const { shell } = require('electron');
// 假设你有一个链接地址
// 使用 shell 模块的 openExternal 方法打开链接

const openExternal = link => {
  shell.openExternal(link);
};

const openDialog = () => {
  emits('openDialog');
};

const cancel = () => {
  emits('cancel');
};
</script>

<style lang="scss" scoped>
.notice-beta {
  background: red;
  :deep(.vxe-modal--body .vxe-modal--content) {
    padding: 0;
  }
}
.header {
  background: url('@/assets/img/notice-img.png') no-repeat;
  width: 100%;
  height: 150px;
  position: relative;
  .title {
    display: block;
    font-size: 30px;
    color: #287cfa;
    text-align: center;
    line-height: 150px;
  }
  .icon-font {
    position: absolute;
    top: 24px;
    right: 24px;
  }
}
.content {
  font-size: 14px;
  color: #000000;
  padding: 0 54px 26px 54px;
  .blue {
    color: #287cfa;
  }
  .red {
    color: #de3f3f;
  }
  .detail {
    margin-top: 15px;
  }
}
.qr-code-imgs {
  display: flex;
  justify-content: center;
  padding-top: 22px;
  .img {
    margin: 0 2px;
  }
  img {
    width: 85px;
    height: 85px;
  }
  .name {
    font-size: 12px;
    text-align: center;
    color: #333333;
  }
}
.footer {
  border-top: 1px solid #b9b9b9;
  padding: 26px 54px 0 0;
  text-align: right;
  button + button {
    margin-left: 10px;
  }
}
</style>
