const {Service} = require('../../../core');
const {FreeRateModel} = require("../models/FreeRateModel");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ProjectDomain = require("../domains/ProjectDomain");
const {FreeRateProjectModel} = require("../models/FreeRateProjectModel");
const WildcardMap = require("../core/container/WildcardMap");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * 单项工程初始化服务  service
 */
class GljInitSingleProjectService extends Service {
    constructor(ctx) {
        super(ctx);

    }

    /**
     * 单项
     * @param projectModel
     */
    async init(projectModel, constructId) {
        await this.initProjectSingleFreeRates(constructId,projectModel.sequenceNbr);
    }

    /**
     * 移除单项工程
     * @param singleId
     * @param constructId
     */
    async remove(singleId, constructId) {

        // 页签缓存
        await this.service.gongLiaoJiProject.gljInitUnitProjectService.removeTableSetting(singleId, constructId);
        // 定额操作缓存
        await this.service.gongLiaoJiProject.gljInitUnitProjectService.removeDeSetting(singleId, constructId);
    }

    /**
     * 移除 单位工程
     * @param unitList
     * @param constructId
     */
    async removeUnit(unitList, constructId) {
        for (let unitId of unitList) {
            //移除 取费表
            await this.service.gongLiaoJiProject.gljInitUnitProjectService.remove(unitId, constructId);
        }
    }

    /**
     * 初始化项目取费表费率
     * @returns {Promise<void>}
     */
    async initProjectSingleFreeRates(constructId,singleId) {
        //获取工程项目费率说明  新建工程项目费率说明同项目
        let freeRateProjectModel = ObjectUtils.cloneDeep(ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM)) ;
        freeRateProjectModel.constructId = constructId;
        freeRateProjectModel.singleId = singleId;

        // 初始化单项工程取费率
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
        let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_FLSM);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FLSM, singleQfbMap.set(freeKey, freeRateProjectModel));


        let freeRateSProjectModel = new FreeRateProjectModel();
        freeRateSProjectModel.childFreeRate = new Map();
        freeRateSProjectModel.constructId = constructId;
        freeRateSProjectModel.singleId = singleId;

        // 初始化单项工程取费率
        let singleQfbMap1 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKey1 = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap1.set(freeKey1, freeRateSProjectModel));
    }


    /**
     * 修改计税方式后 初始化项目取费表费率
     * @returns {Promise<void>}
     */
    async initProjectSingleFreeRatesV1(constructId,singleId) {

        let args={};
        let qfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKey1 = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
        let single = qfbMap.get(freeKey1);
        let keys = Array.from(single.childFreeRate.keys());
        for(const key of keys){
            //获取每一个取费数据
            let qfObj = single.childFreeRate.get(key);
            args.libraryCode=qfObj.libraryCode;
            args.type=2;
            args.constructId=constructId;
            args.singleId=singleId;
            args.freeFile=qfObj;
            //恢复默认费率
            await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateDataV1(args);
            //更新取费表数据
            // ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, unitQfbMap.set(freeKey, newVar));
        }

    }



}

GljInitSingleProjectService.toString = () => '[class GljInitSingleProjectService]';
module.exports = GljInitSingleProjectService;
