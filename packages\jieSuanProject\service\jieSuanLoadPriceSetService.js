const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Service} = require("../../../core");


class JieSuanLoadPriceSetService extends Service{
    constructor(ctx) {
        super(ctx);
    }



    /**
     * 批量应用接口更新市场价、价格来源
     * @param args
     * @returns {Promise<void>}
     */
    async applyLoadingPriceInRcjDetails(args) {
        let {constructId, singleId, unitId, type, priceType, num, kind} = args;

        let rcjDatas = (await this.getCurrentLoadingRcjs(constructId, singleId, unitId, type)).filter(item => item.isExecuteLoadPrice);
        //拿到所有人材机  进行数据更新
        let differenceSetNum = await this.queryRcjDifferenceSet(args);
        let updataRcj = {};
        for (let i = 0; i < rcjDatas.length; i++) {
            let rcjData = rcjDatas[i]
            let jieSuanRcjDifferenceType = rcjData.jieSuanRcjDifferenceTypeList.find(a => a.rcjDifferenceType === differenceSetNum)
            //let jieSuanRcjDifferenceType=rcjData.jieSuanRcjDifferenceTypeList[differenceSetNum];
            if (priceType === 1 && num === null) {
                //单价
                jieSuanRcjDifferenceType.jieSuanDifferencePriceList[0].jieSuanPrice = rcjData.loadPrice;
                //单价来源
                jieSuanRcjDifferenceType.jieSuanDifferencePriceList[0].jieSuanPriceSource = rcjData.sourcePrice;
                updataRcj.jieSuanPrice = rcjData.loadPrice;
                updataRcj.jieSuanPriceSource = rcjData.sourcePrice;

            }
            if (priceType === 1 && num !== null) {
                //分期 i期 单价集合
                jieSuanRcjDifferenceType.jieSuanDifferencePriceList[num - 1].jieSuanPrice = rcjData.loadPrice
                //结算分期单价来源
                jieSuanRcjDifferenceType.jieSuanDifferencePriceList[num - 1].jieSuanPriceSource = rcjData.sourcePrice
                updataRcj.jieSuanPrice = rcjData.loadPrice;
                updataRcj.jieSuanPriceSource = rcjData.sourcePrice;

            }
            if (priceType === 2) {
                //基期价
                jieSuanRcjDifferenceType.jieSuanBasePrice = rcjData.loadPrice
                //基期价来源
                jieSuanRcjDifferenceType.jieSuanBasePriceSource = rcjData.sourcePrice
                updataRcj.jieSuanBasePrice = rcjData.loadPrice;
                updataRcj.jieSuanBasePriceSource = rcjData.sourcePrice;

            }
            // //计算高亮
            // if(rcjData.基期价!==rcjData.marketPriceBeforeLoading|| rcjData.单价!==rcjData.marketPriceBeforeLoading){
            //     rcjData.highlight=true;
            // }else{
            //     rcjData.highlight=false ;
            // }

        }

        if (type === 2) {
            await this.batchchangeRcjZaiJia(constructId, singleId, unitId, type, rcjDatas,updataRcj);
            //计算费用代码和更新费用汇总
            this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        }
        if (type == 1) { //工程项目层级
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            unitList = unitList.filter(k => k.originalFlag);
            for (let unit of unitList) {

                await this.batchchangeRcjZaiJia(constructId, unit.spId, unit.sequenceNbr, 2, rcjDatas,updataRcj);

                //计算费用代码和更新费用汇总
                await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.spId,
                    unitId: unit.sequenceNbr,
                });

                //清除单位的本次载价标识
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    for (let i = 0; i < unit.constructProjectRcjs.length; i++) {
                        unit.constructProjectRcjs[i].currentLoadingFinished = null;
                    }
                }
                if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
                    for (let i = 0; i < unit.rcjDetailList.length; i++) {
                        unit.rcjDetailList[i].currentLoadingFinished = null;
                    }
                }

            }
        }

    }

    /**
     * 点击批量载价 返回载价编辑弹窗数据
     * @returns {Promise<void>}
     */
    async loadingPrice(args) {
        //type 1 = 工程项目  2 单位工程
        //batchAllFlag,是否批量调整所有价格
        //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
        //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
        let {constructId, singleId,unitId,type,batchAllFlag,loadPriortyList,laodPriceConditionList,num,priceType} = args;

        //远程获取的人材机数据
        let remoteRcjData = await this.service.loadPriceSetService.getRemoteRcjData(args);

        let rcjData = await this.loadPriceRcjData(args, batchAllFlag);


        if (!ObjectUtils.isEmpty(rcjData)){
            rcjData = JSON.parse(JSON.stringify(rcjData))
        }
        for (const item of rcjData) {
            let filter = remoteRcjData.filter(itemRemote => itemRemote.id == item.standardId)[0];
            item.marketPriceBeforeLoading = item.marketPrice;
            item.marketSourcePriceBeforeLoading = item.sourcePrice;

            item.informationPrice = filter.informationPrice;
            item.recommendPrice = filter.recommendPrice;
            item.marketPrice = filter.marketPrice;
            item.informationSourcePrice = filter.informationSourcePrice;
            item.marketSourcePrice = filter.marketSourcePrice;
            item.recommendSourcePrice = filter.recommendSourcePrice;

            //原始的精准数据备份  用于优先级调整后
            item.marketPriceOrigin = filter.marketPrice;
            item.marketSourcePriceOrigin = filter.marketSourcePrice;//市场价价格来源
            item.recommendPriceOrigin = filter.recommendPrice;
            item.recommendSourcePriceOrigin = filter.recommendSourcePrice;
            item.informationPriceOrigin = filter.informationPrice;
            item.informationSourcePriceOrigin = filter.informationSourcePrice;

            item.informationPriceList = filter.informationPriceList;
            item.marketPriceList = filter.marketPriceList;
            item.recommendPriceList = filter.recommendPriceList;

            //挂待载价格
            await this.service.loadPriceSetService.updateLoadPriceByLevel(loadPriortyList, item, 0);

            //以下处理用于 有精准匹配 且匹配数据只有一条时 前端的放大镜不进行展示
            if (item.highlight) {
                if (item.informationPriceList != null && item.informationPriceList.length == 1) {
                    item.informationPriceList = null;
                }
                if (item.marketPriceList != null && item.marketPriceList.length == 1) {
                    item.marketPriceList = null;
                }
                if (item.recommendPriceList != null && item.recommendPriceList.length == 1) {
                    item.recommendPriceList = null;
                }
            }
            item.isExecuteLoadPrice = true;
        }

        if (type == 2) {  //单位工程
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            unit.unitRcjsLoading = rcjData;
        } else if (type == 1) {
            let project = PricingFileFindUtils.getProjectObjById(constructId);
            project.projectRcjsLoading = rcjData;
        }

        return rcjData.map(item => item.sequenceNbr);
        //查询人材机数据
        //调用接口得到精准、模糊数据
        //根据条件  更新人材机的待载价格、价格来源、信息价、市场价、推荐价、是否勾选全部置1
        //统计载入前后价格

    }


    async getCurrentLoadingRcjs(constructId, singleId, upId, type) {
        if (type == 2) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, upId);
            return unit.unitRcjsLoading;
        } else if (type == 1) {
            let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
            return projectObjById.projectRcjsLoading;
        }
        return null;
    }


    /**
     *   查询项目对应调整法
     */
    async queryRcjDifferenceSet(args) {
        let {constructId, singleId, unitId, kind,type} = args;
        if (type === 2) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            let rcjDifference = unit.rcjDifference.filter(a => a.kind === kind)
            return rcjDifference[0].rcjDifferenceType;
        }
        if (type === 1) {
            let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
            let  rcjDifferenceType= projectObj.jieSuanRcjDifferenceTypeList.find(diff => diff.rcjDifferenceType === args.kind).JieSuanPriceAdjustmentMethodType
            return rcjDifferenceType
        }

    }

    async batchchangeRcjZaiJia(constructId, singleId, unitId, type, rclList,updataRcj) {

        let rcjObj = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            type: type
        };
        for (const rcj of rclList) {
            //封装修改参数
            let constructProjectRcj = {
                //marketPrice: rcj.marketPriceBeforeLoading,  //合同确认单价
                jieSuanRcjDifferenceTypeList: rcj.jieSuanRcjDifferenceTypeList, //合同确认单价
                jieSuanPrice : updataRcj.jieSuanPrice,  //结算单价
                jieSuanPriceSource : updataRcj.jieSuanPriceSource,  //结算单价
                jieSuanBasePrice: updataRcj.jieSuanBasePrice,  //基期价
                jieSuanBasePriceSource: updataRcj.jieSuanBasePriceSource, //基期价来源
                highlight: true //载价记录
                // jieSuanBasePriceList: rcj.jieSuanBasePriceList,    //分期基期价集合
                // jieSuanBasePriceSourceList: rcj.jieSuanBasePriceSourceList,    //分期基期价来源
                // jieSuanRcjStagePrice: rcj.jieSuanRcjStagePrice, //分期 i期 单价集合
                // jieSuanStagePriceSourceList: rcj.jieSuanStagePriceSourceList,   //结算分期单价来源
                // highlight: rcj.highlight,
                // highlightList: rcj.highlightList,
            };
            rcjObj.sequenceNbr = rcj.sequenceNbr;
            rcjObj.constructProjectRcj = constructProjectRcj;
            await this.service.jieSuanProject.jieSuanRcjStageService.changeRcjNewJieSuan(rcjObj);
        }
    }

    /**
     * 清除载价
     * @param args
     * @returns {Promise<void>}
     */
    jiesuanClearLoadPriceUse(args){
        //type 1 = 工程项目  2 单位工程
        let {constructId, singleId, unitId,type,rcj,fqNum} = args;
        if (ObjectUtil.isEmpty(unitId)){
             this.clearLoadPriceUseConstruct(args);
        }else {
             this.clearLoadPriceUseUnit(constructId,singleId,unitId,rcj,fqNum);
        }

        return true;

    }

    /**
     * 工程项目汇总 清除载价
     * @param args
     * @returns {Promise<void>}
     */
     clearLoadPriceUseConstruct(args){

        //type 1 = 工程项目  2 单位工程
        let {constructId,rcj,fqNum} = args;

        let unitList = PricingFileFindUtils.getUnitList(constructId);
        for (let unit of unitList) {
            this.clearLoadPriceUseUnit(constructId,unit.spId,unit.sequenceNbr,rcj,fqNum);
        }
    }


    /**
     * 单位工程汇总 清除载价
     * @param constructId
     * @param singleId
     * @param unitId
     * @param rcjData 人材机对象
     * @returns {Promise<void>}
     */
    clearLoadPriceUseUnit(constructId,singleId,unitId,rcjData,fqNum){

        //type 1 = 工程项目  2 单位工程

        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //人材机一级
        let constructProjectRcjs = unitProject.constructProjectRcjs;

        //人材机二级
        let rcjDetailList = unitProject.rcjDetailList;


        let rcjs = constructProjectRcjs.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                && item.marketPrice === rcjData.marketPrice
        });
        //二级材料的子级材料
        let rcjDetails = rcjDetailList.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                && item.marketPrice === rcjData.marketPrice
        });

        if (rcjs.length > 0) {
            //修改市场价、价格来源等
            for (let j = 0; j < rcjs.length; j++) {
                //rcjs[j].marketPriceBeforeLoading = null;
                let constructRcjArrayElement =rcjs[j];
                //结算单价
                let index;
                if(ObjectUtils.isEmpty(fqNum)){
                    index=0;
                }else {
                    index=fqNum-1;
                }
                //constructRcjArrayElement.marketPrice = constructRcjArrayElement.marketPriceBeforeLoading;
                constructRcjArrayElement.marketPrice = constructRcjArrayElement.dePrice;
                constructRcjArrayElement.sourcePrice = null;
                let jieSuanRcjDifferenceTypeList1 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;
                if (!ObjectUtil.isEmpty(constructRcjArrayElement.jieSuanRcjDifferenceTypeList)){
                    for (let rcjDifferenceInfo of jieSuanRcjDifferenceTypeList1) {


                        rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPrice=rcjs[j].dePrice;
                        rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPriceSource=null;

                        rcjDifferenceInfo.jieSuanBasePrice=rcjs[j].dePrice;
                        rcjDifferenceInfo.jieSuanBasePriceSource=null;
                    }

                }


                rcjs[j].highlight = false;
                rcjs[j].marketPriceBeforeLoading = null;
            }

        }
        if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
            for (let j = 0; j < rcjDetails.length; j++) {

                //rcjDetails[j].marketPriceBeforeLoading = null;
                let constructRcjArrayElement =rcjDetails[j];
                //结算单价
                let index;
                if(ObjectUtils.isEmpty(fqNum)){
                    index=0;
                }else {
                    index=fqNum-1;
                }
                //constructRcjArrayElement.marketPrice = constructRcjArrayElement.marketPriceBeforeLoading;
                constructRcjArrayElement.marketPrice = constructRcjArrayElement.dePrice;
                constructRcjArrayElement.sourcePrice = null;

                if (!ObjectUtil.isEmpty(constructRcjArrayElement.jieSuanRcjDifferenceTypeList)){
                    let jieSuanRcjDifferenceTypeList1 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;

                    for (let rcjDifferenceInfo of jieSuanRcjDifferenceTypeList1) {
                        rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPrice=constructRcjArrayElement.dePrice;
                        rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPriceSource=null;

                        rcjDifferenceInfo.jieSuanBasePrice=constructRcjArrayElement.dePrice;
                        rcjDifferenceInfo.jieSuanBasePriceSource=null;
                    }

                }

                constructRcjArrayElement.highlight = false;
                rcjDetails[j].marketPriceBeforeLoading = null;
            }
        }

    }

    /**
     * 批量载价前的人材机数据查询
     * @param type
     * @param kind
     * @param constructId
     * @param singleId
     * @param unitId
     * @param isCheck 是否勾选批量调整所有价格
     */
    async loadPriceRcjData(args, isCheck) {
        let rcjData = await this.queryConstructRcjByDeIdNew(args);
        if (ObjectUtils.isEmpty(rcjData)) {
            return null;
        }
        //除单位为元、单位为%，已锁定市场价、已勾选是否汇总/二次解析的父级材料以外的
        rcjData = rcjData.filter(k => k.unit !== '元' && k.unit !== '%').filter(k => k.ifLockStandardPrice !== 1)
            .filter(k => k.supplementDeRcjFlag !== 1)
            .filter(k => (k.markSum == 1 && (k.levelMark != 1 && k.levelMark != 2)) || (k.markSum == 0 && (k.levelMark == 1 || k.levelMark == 2)))

        if (!isCheck) {
            //没勾的时候过滤  加上价格来源为自行询价条件
            rcjData = rcjData.filter(k => k.sourcePrice !== "自行询价");
        }
        return rcjData;
    }

    /**
     * 查询人材机汇总 载价查询专用版
     * @param type 数据类型 1 工程项目层级  2 单位层级
     * @param kind 人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土 7 主要材料.设备
     * @param constructId 工程项目id
     * @param singleId 单项工程id
     * @param unitId 单位工程id
     */
    async queryConstructRcjByDeIdNew(args) {
        if (!ObjectUtils.isEmpty(args.unitId) ) {
            let {constructId, singleId, unitId, kind, num} = args;
            let unitRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(args);
            return unitRcjQuery;
        } else {
            let {constructId, type, kind} = args;
            let cs ={};
            cs.constructId = constructId;
            cs.kind = kind;
            cs.type = Number(kind)>20?2:1;

            let projectRcjQuery =await this.service.jieSuanProject.jieSuanRcjProcess.projectRcjList(cs);
            return projectRcjQuery
        }
    }


    /**
     * 结算中鼠标右键查询人材机关联定额
     * @param args
     * @returns {Promise<void>}
     */
    async jieSuanGetRcjDe(args){
        let {constructId, singleId, unitId,rcj} = args;
        let result = await this.service.loadPriceSetService.getRcjDe(args);

        if (!ObjectUtils.isEmpty(result)){
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            let itemBillProjects = unit.itemBillProjects.getAllNodes();
            let measureProjectTables = unit.measureProjectTables.getAllNodes();

            for (let resultKey of result) {
                let filter = itemBillProjects.find(i=>i.sequenceNbr ==resultKey.sequenceNbr);
                if (ObjectUtils.isEmpty(filter)){
                    filter = measureProjectTables.find(i=>i.sequenceNbr ==resultKey.sequenceNbr);
                }
                if(!ObjectUtils.isEmpty(filter)){
                    resultKey.jieSuanGcl =filter.quantity;
                }

            }
        }

        return result;

    }
}

JieSuanLoadPriceSetService.toString = () => '[class JieSuanLoadPriceSetService]';
module.exports = JieSuanLoadPriceSetService;
