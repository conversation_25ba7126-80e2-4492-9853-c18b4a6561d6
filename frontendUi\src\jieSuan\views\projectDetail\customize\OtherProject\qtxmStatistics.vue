<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-09-12 11:17:17
-->
<template>
  <!-- <fee-header class="head"></fee-header> -->
  <div class="table-content">
    <vxe-grid
      ref="qtxmTable"
      v-if="handlerColumns.length"
      v-bind="gridOptions"
      @edit-closed="editClosedEvent"
      @menu-click="contextMenuClickEvent"
      @cell-click="useCellClickEvent"
    >
      <template #jiesuanTotal_edit="{ row }">
        <span v-if="!row.isEdit">{{ row.amount }}</span>
        <vxe-input
          v-if="row.isEdit"
          :clearable="false"
          v-model.trim="row.amount"
          type="text"
          :maxlength="10"
          @blur="
            row.amount = pureNumber(row.amount, 6);
            clear();
          "
        ></vxe-input>
      </template>
      <template #isMarkSafe="{ row }">
        <vxe-checkbox
          v-model="row.isMarkSafe"
          name="awf"
          :disabled="originalFlag"
          @change="checkChange(row)"
          v-if="
            row.extraName !== '材料暂估价' && row.extraName !== '设备暂估价'
          "
        ></vxe-checkbox>
      </template>
      <template #isMarkSj="{ row }">
        <vxe-checkbox
          v-model="row.isMarkSj"
          name="sj"
          :disabled="originalFlag"
          v-if="
            row.extraName !== '材料暂估价' && row.extraName !== '设备暂估价'
          "
          @change="checkChange(row)"
        ></vxe-checkbox>
      </template>
    </vxe-grid>
  </div>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />
</template>
<script setup>
// import FeeHeader from './FeeHeader.vue';
import {
  onMounted,
  onActivated,
  onUpdated,
  ref,
  watch,
  getCurrentInstance,
  nextTick,
} from 'vue';
import { pureNumber } from '@/utils/index';

import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import jiesuanApi from '@/api/jiesuanApi';
import qtxmCommon from './qtxmCommon';
import { insetBus } from '@/hooks/insetBus';
import { message } from 'ant-design-vue';
import operateList from '@/views/projectDetail/customize/operate';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
const originalFlag = ref(false);
let qtxmTable = ref();
const clear = () => {
  //清除编辑状态
  let $table = qtxmTable.value;
  $table.clearEdit();
};

import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
const {
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({ vxeGrid: qtxmTable });
const menuConfig = {
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'pageSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
};
const defaultColumns = [
  {
    field: 'dispNo',
    width: 60,
    title: '序号',
    classType: 1,
    initialize: true,
  },
  {
    field: 'extraName',
    width: 180,
    title: '名称',
    classType: 1,
    initialize: true,
  },
  {
    field: 'unit',
    width: 100,
    title: '单位',
    classType: 1,
    initialize: true,
    originalFlag: false,
  },
  {
    field: 'jieSuanTotal',
    width: 100,
    title: '合同数量',
    classType: 1,
    initialize: true,
    originalFlag: false,
  },
  {
    field: 'calculationBase',
    width: 150,
    title: '计算基数',
    classType: 1,
    initialize: true,
  },
  {
    field: 'jieSuanAmount',
    width: 100,
    title: '合同金额',
    classType: 1,
    initialize: true,
    originalFlag: false,
  },
  {
    field: 'jieSuanJxTotal',
    width: 180,
    title: '合同进项合计',
    classType: 1,
    initialize: true,
    originalFlag: false,
  },
  {
    field: 'jieSuanCsTotal',
    width: 180,
    title: '合同除税合计',
    classType: 1,
    initialize: true,
    originalFlag: false,
  },
  {
    field: 'isMarkSafe',
    width: 170,
    title: '计取安文费',
    fixed: 'right',
    slots: { default: 'isMarkSafe' },
    classType: 1,
    initialize: true,
  },
  {
    field: 'isMarkSj',
    width: 150,
    title: '计取税金',
    fixed: 'right',
    slots: { default: 'isMarkSj' },
    classType: 1,
    initialize: true,
  },
  {
    field: 'amount',
    width: 180,
    title: '结算数量',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'jiesuanTotal_edit' },
    classType: 1,
    initialize: true,
  },
  {
    field: 'total',
    width: 180,
    title: '结算金额',
    classType: 1,
    initialize: true,
  },
  {
    field: 'jxTotal',
    width: 180,
    title: '结算进项税额',
    classType: 1,
    initialize: true,
  },
  {
    field: 'csTotal',
    width: 180,
    title: '结算除税合计',
    classType: 1,
    initialize: true,
  },
];
let gridOptions = ref({
  align: 'center',
  keepSource: true,
  cellClassName: selectedClassName,
  loading: loading.value,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isHover: true,
  },
  data: [],
  height: 'auto',
  menuConfig: menuConfig,
  class: 'table-edit-common',
  editConfig: {
    trigger: 'click',
    mode: 'cell',
  },
  columns: handlerColumns,
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log(menu, row);
  if (menu.code === 'pageSetting') {
    showPageColumnSetting();
  }
};
// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};
// 定位方法
const posRow = sequenceNbr => {
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  // currentInfo.value = { sequenceNbr };
  getOtherProjectList();
};

defineExpose({
  posRow,
});

const editClosedEvent = ({ row, column }) => {
  console.log('其他项目修改', row);
  const $table = qtxmTable.value;
  const field = column.field;
  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'amount' && row.amount && row.amount.length > 20) {
    row.amount = value.slice(0, 20);
  }
  upDate(row);
};
const upDate = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    sequenceNbr: row.sequenceNbr,
    otherProject: {
      amount: row.amount,
      markSafa: row.markSafa,
      markSj: row.markSj,
    },
  };
  console.log(apiData);
  csProject.updateOtherProject(apiData).then(res => {
    if (res.status === 200) {
      let costData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeInfo?.parentId, //单项ID
        unitId: projectStore.currentTreeInfo?.id, //单位ID
      };
      // jiesuanApi.countCostCodePrice(costData);
      getOtherProjectList();
    }
  });
};
const getOtherProjectList = () => {
  originalFlag.value = projectStore.currentTreeInfo.originalFlag;
  loading.value = true;
  let insertData = operateList.value.find(item => item.name === 'insert');
  let deleteData = operateList.value.find(item => item.name === 'delete');
  insertData.disabled = true;
  deleteData.disabled = true;
  let apiData = qtxmCommon.requestParams();
  csProject.getOtherProjectList(apiData).then(res => {
    console.log(apiData, res);
    if (res.status === 200) {
      loading.value = false;
      res.result &&
        res.result.map(item => {
          item.isMarkSafe = item.markSafa === 1 ? true : false;
          item.isMarkSj = item.markSj === 1 ? true : false;
          if (
            item.extraName.trim() === '材料暂估价' ||
            item.extraName.trim() === '设备暂估价'
          ) {
            item.isEdit = false;
          } else {
            item.isEdit = true;
          }
        });

      tableData.value = res.result;
      gridOptions.value.data = res.result;
      console.log('********getOtherProjectList', gridOptions.value);
    }
  });
};
const getOtherProjectMode = () => {
  csProject.getOtherProjectMode().then(res => {
    if (res.status === 200) {
      console.info(111111111);
      console.info(res);
    }
  });
};

watch(
  () => projectStore.tabSelectName,
  async () => {
    // console.log('*****************其他项目');
    if (projectStore.tabSelectName === '其他项目') {
      let filterColumns = JSON.parse(JSON.stringify(defaultColumns));
      await initColumns({
        columns: filterColumns.filter(item => {
          if (!projectStore.currentTreeInfo.originalFlag) {
            return item.originalFlag !== false;
          }
          return true;
        }),
        pageName: 'qtxm',
      });
      getOtherProjectList();
    }
  }
);
onMounted(async () => {
  let filterColumns = JSON.parse(JSON.stringify(defaultColumns));
  await initColumns({
    columns: filterColumns.filter(item => {
      if (!projectStore.currentTreeInfo.originalFlag) {
        return item.originalFlag !== false;
      }
      return true;
    }),
    pageName: 'qtxm',
  });
  getOtherProjectList();
  // getOtherProjectMode();
});

const changeEdit = val => {
  // console.log('val', val);
};
const checkChange = row => {
  console.log('复选框CheckChange', row);
  row.markSafa = row.isMarkSafe ? 1 : 0;
  row.markSj = row.isMarkSj ? 1 : 0;
  upDate(row);
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  // console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
};
onActivated(() => {
  // insetBus(bus,2, projectStore.componentId, 'qtxmStatistics', async data => {
  //   if (data.name === 'insert') message.info('功能建设中...');
  //   if (data.name === 'delete') message.info('功能建设中...');
  // });
});
</script>
<style lang="scss" scoped>
// @import './otherProject.scss';
.table-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
