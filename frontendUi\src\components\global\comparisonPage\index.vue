<!--
 * @Descripttion:详细对比
 * @Author: wangru
 * @Date: 2024-06-14 09:22:42
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-03 14:16:24
-->
<template>
  <div class="standard-type-table">
    <div
      class="head-action"
      v-show="needTitle"
    >
      <a-tabs
        v-model:activeKey="tabsValue"
        type="card"
        :hideAdd="true"
      >
        <a-tab-pane
          key="xxdb"
          tab="详细对比"
        ></a-tab-pane>
      </a-tabs>
    </div>
    <vxe-table
      ref="vexTable"
      :class="[
        'standard-type-table table-edit-common',
        props.isSetStandard ? 'table-edit-common' : '',
      ]"
      border
      height="auto"
      :data="tableData"
      keep-source
      :scroll-y="{ enabled: true, gt: 30 }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :column-config="{ resizable: true }"
      @current-change="currentChangeEvent"
      @edit-closed="editClosedEvent"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :cell-class-name="selectedClassName"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData, null, ['val']);
        }
      "
    >
      <vxe-column
        field="type"
        min-width="80"
        title="审核过程"
      >
        <template #default="{ row, $rowIndex }">
          {{ $rowIndex === 0 ? '送审' : '审定' }}
        </template>
      </vxe-column>
      <template v-for="item in gridOptions.columns">
        <vxe-column
          :field="item.field"
          :min-width="item.minWidth"
          :title="item.title"
          :cell-render="{}"
        >
          <template
            #default="{ row }"
            v-if="item.field === 'ifDonorMaterial'"
          >
            {{ Number(row.ifDonorMaterial) === 1 ? '是' : '否' }}
          </template>
          <template
            #default="{ row }"
            v-if="item.field === 'donorMaterialNumber'&&props.pageType==='rcjhz'"
          >
            {{ row.hasOwnProperty('change') ? row.donorMaterialNumber : row.origindonorMaterialNum }}
          </template>
        </vxe-column>
      </template>
    </vxe-table>
  </div>
</template>
<script>
export default {
  name: 'comparisonPage',
};
</script>
<script setup>
import { onMounted, ref, watch, reactive } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, selectedClassName, cellBeforeEditMethod } =
  useCellClick();

const props = defineProps(['currentInfo', 'type', 'pageType', 'needTitle']);
console.log(props.pageType, 'pageType');
const emits = defineEmits(['saveData']);
const tabsValue = ref('xxdb');
let tableData = ref([]);
const projectStore = projectDetailStore();
const currentInfo = ref(props.currentInfo);
const vexTable = ref();

watch(
  () => props.currentInfo,
  val => {
    tabsValue.value = 'xxdb';
    initData(props.currentInfo);
  },
  {
    deep: true,
  }
);
onMounted(() => {
  tabsValue.value = 'xxdb';
  initData(props.currentInfo);
});
const gridOptions = reactive({ columns: [] });
const initData = currentInfo => {
  switch (props.pageType) {
    case 'fyhz':
      // 费用汇总
      gridOptions.columns = [
        { field: 'name', title: '名称', minWidth: 70 },
        { field: 'code', title: '费用代号', minWidth: 70 },
        { field: 'calculateFormula', title: '计算基数', minWidth: 150 },
        { field: 'rate', title: '费率（%）', minWidth: 110 },
        { field: 'price', title: '金额', minWidth: 70 },
      ];
      break;
    case 'qtxm':
      //其他项目-汇总页面
      gridOptions.columns = [
        { field: 'extraName', title: '名称', minWidth: 180 },
        {
          field: 'calculationBase',
          title: '计算公式(计算基数)',
          minWidth: 140,
        },
        { field: 'amount', title: '数量', minWidth: 40 },
        { field: 'total', title: '金额', minWidth: 120 },
      ];
      break;
    case 'zlje':
      //其他项目-暂列金额
      gridOptions.columns = [
        { field: 'name', title: '名称', minWidth: 180 },
        { field: 'provisionalSum', title: '暂列金额', minWidth: 120 },
        {
          field: 'description',
          title: '备注',
          minWidth: 140,
        },
      ];
      break;
    case 'zygczgj':
      //其他项目-专业工程暂估价
      gridOptions.columns = [
        { field: 'dispNo', title: '序号', minWidth: 100 },
        { field: 'name', title: '工程名称', minWidth: 180 },
        { field: 'content', title: '工程内容', minWidth: 220 },
        { field: 'total', title: '金额', minWidth: 120 },
        {
          field: 'description',
          title: '备注',
          minWidth: 140,
        },
      ];
      break;
    case 'jrg':
      //其他项目-计日工
      gridOptions.columns = [
        { field: 'dispNo', title: '序号', minWidth: 100 },
        { field: 'worksName', title: '名称', minWidth: 180 },
        { field: 'specification', title: '规格', minWidth: 220 },
        { field: 'unit', title: '单位', minWidth: 120 },
        { field: 'tentativeQuantity', title: '数量', minWidth: 120 },
        { field: 'csPrice', title: '除税单价', minWidth: 120 },
        { field: 'total', title: '合价', minWidth: 120 },
        { field: 'price', title: '综合单价', minWidth: 120 },
        { field: 'taxRemoval', title: '除税系数(%)', minWidth: 120 },
        { field: 'jxTotal', title: '进项税额', minWidth: 120 },
      ];
      break;
    case 'zcbfwf':
      //其他项目-总承包服务费
      gridOptions.columns = [
        { field: 'fxName', title: '名称', minWidth: 180 },
        { field: 'xmje', title: '项目价值', minWidth: 220 },
        { field: 'serviceContent', title: '服务内容', minWidth: 120 },
        { field: 'rate', title: '费率(%)', minWidth: 120 },
        { field: 'fwje', title: '金额', minWidth: 120 },
        {
          field: 'description',
          title: '备注',
          minWidth: 140,
        },
      ];
      break;
    case 'rcjhz':
      //人材机汇总
      gridOptions.columns = [
        { field: 'materialCode', title: '材料编码', minWidth: 180 },
        { field: 'materialName', title: '名称', minWidth: 220 },
        { field: 'totalNumber', title: '工程量', minWidth: 120 },
        { field: 'dePrice', title: '预算价', minWidth: 120 },
        { field: 'marketPrice', title: '市场价', minWidth: 120 },
        { field: 'taxRemoval', title: '除税系数(%)', minWidth: 120 },
        { field: 'jxTotal', title: '进项税额', minWidth: 120 },
        { field: 'ifDonorMaterial', title: '是否甲供', minWidth: 120 },
        { field: 'donorMaterialNumber', title: '甲供数量', minWidth: 120 },
      ];
      break;
    case 'quotaqd':
      // 分部分项措施项目清单
      gridOptions.columns = [
        {
          field: props.type === 1 ? 'bdCode' : 'fxCode',
          title: '编码',
          minWidth: 180,
        },
        {
          field: 'name',
          title: '名称',
          minWidth: 220,
        },
        { field: 'projectAttr', title: '项目特征', minWidth: 220 },
        { field: 'quantity', title: '工程量', minWidth: 120 },
        { field: 'price', title: '综合单价', minWidth: 120 },
        { field: 'total', title: '综合合价', minWidth: 120 },
      ];
      break;
    case 'quotade':
      // 分部分项措施项目定额
      gridOptions.columns = [
        {
          field: props.type === 1 ? 'bdCode' : 'fxCode',
          title: '编码',
          minWidth: 180,
        },
        {
          field: 'name',
          title: '名称',
          minWidth: 220,
        },
        { field: 'unit', title: '单位', minWidth: 220 },
        { field: 'quantity', title: '工程量', minWidth: 120 },
        { field: 'zjfPrice', title: '单价', minWidth: 120 },
        { field: 'zjfTotal', title: '合价', minWidth: 120 },
        { field: 'price', title: '综合单价', minWidth: 120 },
        { field: 'total', title: '综合合价', minWidth: 120 },
      ];
      break;
  }
  setTable(currentInfo);
};
// onMounted(() => {
//   setTable(props.currentInfo);
// });
const setTable = val => {
  if (val?.ysshSysj) {
    tableData.value = [val.ysshSysj, val];
    console.log(tableData.value, 'val.ysshSysj');
  } else {
    tableData.value = [];
  }
};
// 选中单条分部分项数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
};
</script>

<style lang="scss" scoped>
.standard-type-table {
  width: 100%;
  height: 100%;
  :deep(.vxe-table) {
    width: 97% !important;
    .vxe-table--render-wrapper {
      .vxe-table--main-wrapper {
        display: flex;
        flex-direction: column;
        .vxe-table--body-wrapper {
          flex: 1;
          height: auto !important;
          min-height: auto !important;
        }
      }
    }
  }
  .table-edit-common {
    width: 100% !important;
  }
  .head-action {
    margin-bottom: 5px;
    height: 35px;
    background: #e7e7e7;
    flex: 1;
    :deep(.ant-tabs-tab) {
      height: 35px;
      background: transparent;
      border: none;
      color: #7c7c7c;
    }
    :deep(.ant-tabs-tab-active) {
      background: #ffffff;
      border-top: 2px solid #4786ff;
      .ant-tabs-tab-btn {
        color: #000000;
      }
    }
    button {
      float: right;
      margin-right: 15px;
    }
  }
}
</style>
