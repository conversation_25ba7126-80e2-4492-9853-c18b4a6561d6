<template>
  <div class="filter-adjustment-material">
    <common-modal
      className="dialog-comm resizeClass"
      title="自动过滤调差材料"
      width="800"
      height="300"
      v-model:modelValue="props.visible"
      :mask="false"
      @close="cancel"
    >
      <div class="range-content">
        <div>设置方式</div>
        <a-radio-group v-model:value="setType" style="width: 100%">
          <p class="radioP">
            <a-radio :value="1">合同计价文件中主要材料、工程设备</a-radio>
          </p>
          <p class="radioP">
            <a-radio :value="2"
              >取合同中材料价值排在前<a-input
                v-model:value="count"
              />位的材料</a-radio
            >
          </p>
          <p class="radioP">
            <a-radio :value="3"
              >取占合同中材料总值<a-input
                v-model:value="rate"
              />%的所有材料</a-radio
            >
          </p>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="filterDifferenceRcj">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive, ref, watch } from 'vue';
import api from '@/api/jiesuanApi';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';

const props = defineProps(['visible']);
const emits = defineEmits(['update:visible', 'updateData']);
const projectStore = projectDetailStore();

const setType = ref(1); // 选择自动过滤方式
const count = ref();
const rate = ref(); // 类型为3时输入框内容

const cancel = () => {
  emits('update:visible', false);
};

const filterDifferenceRcj = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: setType.value,
    value: setType.value === 2 ? count.value : rate.value,
  };
  api.filterDifferenceRcj(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('结果3333', res);
      message.success('自动过滤调差方式设置成功');
      cancel();
      emits('updateData');
    }
  });
};
</script>

<style lang="scss" scoped>
.range-content {
  .radioP {
    margin-bottom: 10px;
    font-size: 14px;
    color: #2a2a2a;
  }
  :deep(.ant-input) {
    width: 100px;
    margin: 0 5px;
    height: 30px;
  }
}
</style>
