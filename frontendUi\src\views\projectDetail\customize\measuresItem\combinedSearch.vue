<template>
  <vxe-modal
    width="450px"
    :lockView="false"
    :lockScroll="false"
    v-model:modelValue="projectStore.combinedVisible"
    className="search-comm"
    :position="{ top: '58px', left: '710px' }"
    }
  >
    <template #header>
      <div class="list-search">
        <div class="search-content">
          <span class="title">组价方案匹配</span>
          <a-checkbox-group
            v-model:value="projectStore.combinedSearchList"
            :options="searchList"
            @change="change"
            :disabled="disabled"
          />
        </div>
        <icon-font type="icon-guanbi" @click="close" />
      </div>
    </template>
  </vxe-modal>
</template>

<script setup>
import { reactive, ref, watch } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';

const props = defineProps(['isSearchModel']);
const emits = defineEmits(['update:isSearchModel', 'filterData']);
const projectStore = projectDetailStore();
const disabled = ref(false);
const searchList = reactive([
  {
    label: '精准组价',
    value: '1',
  },
  {
    label: '近似组价',
    value: '2',
  },
  {
    label: '未匹配组价',
    value: '0',
  },
]);

watch(
  () => projectStore.componentId,
  () => {
    if (
      projectStore.componentId !== 'subItemProject' &&
      projectStore.componentId !== 'measuresItem'
    ) {
      disabled.value = true;
    } else {
      disabled.value = false;
    }
  }
);
const change = checkedValue => {
  console.log('checkedValue', checkedValue);
  projectStore.SET_COMBINED_SEARCH_LIST(checkedValue);
};

const close = () => {
  projectStore.SET_COMBINED_VISIBLE(false);
  projectStore.SET_COMBINED_SEARCH_LIST([]);
};
</script>
<style lang="scss">
.search-comm {
  .vxe-modal--header {
    padding: 4px 0;
    width: 450px;
    height: 46px;
    background-color: #ffffff;
    color: #1f1f1f;
    z-index: 10;
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.26);
    .list-search {
      width: 434px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      background: rgba(255, 255, 255, 0.39);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 0 4px 4px;
      .title {
        background: #1e90ff;
        font-size: 12px;
        display: inline-block;
        width: 92px;
        height: 38px;
        line-height: 38px;
        margin-right: 18px;
        border-radius: 6px 0 0 6px;
        color: #ffffff;
      }
      .search-content {
        border: 1px solid #e2e2e2;
        border-radius: 6px;
        margin-right: 7px;
      }
      :deep(.ant-checkbox-group) {
        .ant-checkbox-wrapper {
          color: #1f1f1f;
        }
      }
      :deep(.ant-checkbox-wrapper:nth-of-type(1)) {
        color: #1f1f1f;
        .ant-checkbox-inner {
          background-color: #ffffff;
          color: #67c23a;
          border-color: #67c23a;
          &:hover {
            border-color: #67c23a;
          }
        }
        .ant-checkbox-checked .ant-checkbox-inner::after {
          border: 2px solid #67c23a;
          border-top: 0;
          border-left: 0;
        }
        .ant-checkbox-checked::after {
          border: 1px solid #67c23a;
        }
      }
      :deep(.ant-checkbox-wrapper:nth-of-type(2)) {
        color: #1f1f1f;
        .ant-checkbox-inner {
          background-color: #ffffff;
          color: #ffaa09;
          border-color: #ffaa09;
          &:hover {
            border-color: #ffaa09;
          }
        }
        .ant-checkbox-checked .ant-checkbox-inner::after {
          border: 2px solid #ffaa09;
          border-top: 0;
          border-left: 0;
        }
        .ant-checkbox-checked::after {
          border: 1px solid #ffaa09;
        }
      }
      :deep(.ant-checkbox-wrapper:nth-of-type(3)) {
        color: #1f1f1f;
        .ant-checkbox-inner {
          background-color: #ffffff;
          color: #ff5757;
          border-color: #ff5757;
          &:hover {
            border-color: #ff5757;
          }
        }
        .ant-checkbox-checked .ant-checkbox-inner::after {
          border: 2px solid #ff5757;
          border-top: 0;
          border-left: 0;
        }
        .ant-checkbox-checked::after {
          border: 1px solid #ff5757;
        }
      }
    }
  }
  .vxe-modal--content {
    padding: 0;
  }
}
</style>
<style lang="scss" scoped>
.search-comm {
  width: 450px;
  height: 46px;
  position: absolute;
  display: flex;
  align-items: center;
  top: 0;
  left: 710px;
  margin: auto;
  background-color: #ffffff;
  z-index: 10;
  box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.26);
  cursor: pointer;
  .list-search {
    width: 434px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    background: rgba(255, 255, 255, 0.39);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 4px;
    .title {
      background: #1e90ff;
      font-size: 12px;
      display: inline-block;
      width: 92px;
      height: 38px;
      line-height: 38px;
      margin-right: 18px;
      border-radius: 6px 0 0 6px;
      color: #ffffff;
    }
    .search-content {
      border: 1px solid #e2e2e2;
      border-radius: 6px;
      margin-right: 7px;
    }
    :deep(.ant-checkbox-wrapper:nth-of-type(1)) {
      .ant-checkbox-inner {
        background-color: #ffffff;
        color: #67c23a;
        border-color: #67c23a;
        &:hover {
          border-color: #67c23a;
        }
      }
      .ant-checkbox-checked .ant-checkbox-inner::after {
        border: 2px solid #67c23a;
        border-top: 0;
        border-left: 0;
      }
      .ant-checkbox-checked::after {
        border: 1px solid #67c23a;
      }
    }
    :deep(.ant-checkbox-wrapper:nth-of-type(2)) {
      .ant-checkbox-inner {
        background-color: #ffffff;
        color: #ffaa09;
        border-color: #ffaa09;
        &:hover {
          border-color: #ffaa09;
        }
      }
      .ant-checkbox-checked .ant-checkbox-inner::after {
        border: 2px solid #ffaa09;
        border-top: 0;
        border-left: 0;
      }
      .ant-checkbox-checked::after {
        border: 1px solid #ffaa09;
      }
    }
    :deep(.ant-checkbox-wrapper:nth-of-type(3)) {
      .ant-checkbox-inner {
        background-color: #ffffff;
        color: #ff5757;
        border-color: #ff5757;
        &:hover {
          border-color: #ff5757;
        }
      }
      .ant-checkbox-checked .ant-checkbox-inner::after {
        border: 2px solid #ff5757;
        border-top: 0;
        border-left: 0;
      }
      .ant-checkbox-checked::after {
        border: 1px solid #ff5757;
      }
    }
  }
}
</style>
