<!--
 * @Descripttion: 导出。ysf文件
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: liuxia
 * @LastEditTime: 2024-09-05 17:31:37
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="auto"
    @close="cancel"
    v-model:modelValue="show"
    :title="type === 'copyTo' ? '复制到' : '批量删除'"
  >
    <!-- {{checkedKeys}} -->
    <div class="tree-content-wrap">
      <div class="dialog-content">
        <!-- <div class="title">请选择需要导出的项目</div> -->
        <div class="list" v-if="tree">
          <a-tree
            :defaultExpandAll="true"
            checkable
            :checkStrictly="type === 'copyTo'"
            show-line
            :tree-data="tree"
            :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
            v-model:checkedKeys="checkedKeys"
            @check="checkSelect($event)"
          >
            <template #switcherIcon="{ switcherCls, children }">
              <down-outlined :class="switcherCls" />
            </template>
          </a-tree>
        </div>
      </div>
      <div class="group-list" v-if="type !== 'copyTo'">
        <a-radio-group v-model:value="dataStatus" @change="changeCheck">
          <a-radio value="all">全部</a-radio>
          <a-radio value="part">取消全部</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          @click="handleOk"
          :disabled="checkedKeys.length === 0"
          :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import {
  ref,
  reactive,
  watch,
  nextTick,
  toRaw,
  defineExpose,
  computed,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import { projectDetailStore } from '@/store/projectDetail';
import infoMode from '@/plugins/infoMode.js';
const store = projectDetailStore();
const route = useRoute();
// const treeData = ref(null);
const submitLoading = ref(false);
const checkedKeys = ref([]);
const dataStatus = ref(null);
const currentIndex = calculateDepth(store.$state.currentTreeInfo);
const props = defineProps({
  visible: {
    type: Boolean,
  },
  type: {
    type: String,
    default: 'copyTo',
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  currentInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['update:visible', 'saveTree']);
const show = computed({
  get: () => props.visible,
  set: val => {
    emit('update:visible', val);
  },
});
const tree = ref(null);
watch(
  () => props.visible,
  val => {
    if (val && props.type === 'copyTo') {
      tree.value = xeUtils.clone(props.treeData, true);

      calculateLevels(tree.value[0], 0);
      addDisabledAttribute(tree.value, store.$state.currentTreeInfo.id);
      tree.value = xeUtils.clone([filterNodes(tree.value[0])], true);
      console.log(
        'a1111',
        tree.value,
        props.treeData,
        store.$state.currentTreeInfo
      );
    } else if (val && props.type === 'batchDelete') {
      tree.value = xeUtils.clone(props.treeData, true);
    }
  }
);
function addDisabledAttribute(treeData, targetId) {
  // 遍历树形数据
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    // 找到目标节点
    if (node.id === targetId) {
      // 将当前节点及其子节点的 disabled 属性设置为 true
      setDisabledRecursively(node);
      break; // 已找到目标节点，不再需要继续遍历
    } else if (node.children && node.children.length > 0) {
      // 递归处理子节点
      addDisabledAttribute(node.children, targetId);
    }
  }
}

function setDisabledRecursively(node) {
  // 将当前节点的 disabled 属性设置为 true
  node.disabled = true;
  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    for (let i = 0; i < node.children.length; i++) {
      setDisabledRecursively(node.children[i]);
    }
  }
}

// 超过十级不可选中
function calculateLevels(node, level = 0) {
  let dragNode = store.$state.currentTreeInfo;
  node.level = level + currentIndex;
  if (node.level > 10) {
    node.disabled = true;
  }
  if (
    dragNode.levelType === 3 &&
    node.levelType === 2 &&
    node.hasOwnProperty('children')
  ) {
    // 单位工程移动到目标单项时，此单项必须为最子节点
    if (node.children.findIndex(item => item.levelType === 2) !== -1) {
      node.disabled = true;
    }
  }
  if (dragNode.levelType === 3 && node.levelType === 1) {
    // 单位不可移动到最根节点下
    node.disabled = true;
  }
  if (dragNode.levelType === 2 && node.hasOwnProperty('children')) {
    if (node.children.findIndex(item => item.levelType === 3) !== -1) {
      node.disabled = true;
    }
  }
  node.children?.forEach(child => calculateLevels(child, level + 1));
}
function filterNodes(node) {
  if (node.levelType === 3) {
    return null; // 返回 null 表示过滤该节点
  }

  if (node.children && node.children.length > 0) {
    // 递归过滤子节点
    node.children = node.children.map(filterNodes).filter(Boolean);
  }

  return node;
}
// 示例树结构

function calculateDepth(node) {
  if (!node?.children || node?.children?.length === 0) {
    return 1; // Leaf node, depth is 0
  } else {
    let maxChildDepth = 1;
    for (let child of node?.children) {
      const childDepth = calculateDepth(child);
      maxChildDepth = Math.max(maxChildDepth, childDepth);
    }
    return maxChildDepth + 1; // Add 1 to include current node
  }
}
// const checkSelect = eve => {
//   // let allList = xeUtils.toTreeArray(treeData.value);
//   if ((eve.length === props.treeData.length) > 0) {
//     dataStatus.value = 'all';
//   } else if (eve.length === 0 && props.treeData.length > 0) {
//     dataStatus.value = 'part';
//   }
// };

const checkSelect = eve => {
  let allList = xeUtils.toTreeArray(props.treeData);
  if (eve.length === allList.length) {
    dataStatus.value = 'all';
  } else if (!eve.length) {
    dataStatus.value = '';
  } else if (eve.length !== allList.length) {
    dataStatus.value = 'part';
  }
};

const cancel = () => {
  show.value = false;
  checkedKeys.value = [];
  dataStatus.value = null;
};

const changeCheck = () => {
  if (dataStatus.value === 'all') {
    checkedKeys.value = [props.treeData[0]?.id];
  } else {
    checkedKeys.value = [];
  }
};
// 删除树形树数据内数据
const removeNodesFromTree = (tree, idsToRemove) => {
  return tree.filter(node => {
    if (!idsToRemove.includes(node.id)) {
      // 如果当前节点不应被删除，检查其子节点
      if (node.children) {
        node.children = removeNodesFromTree(node.children, idsToRemove);
      }
      return true; // 返回true以保留当前节点
    }
    return false; // 返回false以删除当前节点
  });
};
// 根据id查找目标节点
function findNodeById(treeData, targetId) {
  // 遍历树形数据
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    // 检查当前节点是否是目标节点
    if (node.id === targetId) {
      return node; // 返回找到的节点
    }
    // 如果当前节点有子节点，则递归查找
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeById(node.children, targetId);
      if (foundNode) {
        return foundNode; // 如果在子节点中找到了目标节点，则返回
      }
    }
  }
  // 如果遍历完整个树都没有找到目标节点，则返回 null
  return null;
}

const handleOk = async () => {
  let copyTreeList = tree.value;
  if (props.type === 'copyTo') {
    let dragObj = toRaw(props.currentInfo);
    delete dragObj.parent;
    let copyToList = xeUtils.clone(props.treeData, true);
    checkedKeys.value.checked.map(a => {
      let obj = findNodeById(copyToList, a);
      if (obj.hasOwnProperty('children')) {
        obj.children?.push(dragObj);
      } else {
        obj.children = [dragObj];
      }
      dragObj.parentId = obj.id;
      dragObj.copyFromId = dragObj.id;
      // 校验复制后的数据是否有重复name
      if (obj.children?.filter(a => a.name === dragObj.name).length > 1) {
        if (obj.children.filter(a => a.id === dragObj.id).length > 1) {
          obj.children.filter(a => a.id === dragObj.id)[
            obj.children.filter(a => a.id === dragObj.id).length - 1
          ].name = addObjectWithName(obj.children, dragObj.name);
        } else {
          obj.children.filter(a => a.id === dragObj.id)[0].name =
            addObjectWithName(obj.children, dragObj.name);
        }
      }
    });
    console.log();
    emit('saveTree', JSON.parse(JSON.stringify(copyToList[0])));
    cancel();
  } else {
    infoMode.show({
      iconType: 'icon-querenshanchu',
      infoText: '确认删除所有选中数据吗？',
      isFunction: false,
      confirm: () => {
        let result = [];
        if (removeNodesFromTree(copyTreeList, checkedKeys.value).length === 0) {
          copyTreeList[0].children = [];
          result = copyTreeList;
        } else {
          result = removeNodesFromTree(copyTreeList, checkedKeys.value);
        }
        emit('saveTree', JSON.parse(JSON.stringify(result[0])), false);
        cancel();
        infoMode.hide();
      },
      close: () => {
        infoMode.hide();
      },
    });
  }
};
function addObjectWithName(data, newName) {
  // 获取当前存在的name值
  const existingNames = new Set(data.map(item => item.name));

  // 如果newName已经存在，则找到下一个可用的name
  let newUniqueName = newName;
  let counter = 1;
  while (existingNames.has(newUniqueName)) {
    // 如果newName已经是a-X格式，则递增X
    if (newUniqueName.match(/^a-(\d+)$/)) {
      const currentNumber = parseInt(RegExp.$1, 10);
      newUniqueName = `a-${currentNumber + 1}`;
    } else {
      // 否则，在newName后添加计数器
      newUniqueName = `${newName}-${counter}`;
      counter++;
    }
  }
  return newUniqueName;
}
const flattenTree = treeList => {
  const result = [];

  function traverse(node) {
    result.push(node); // 将当前节点添加到结果数组中

    if (node.children && node.children.length > 0) {
      // 递归处理子节点
      for (let i = 0; i < node.children.length; i++) {
        const data = node.children[i];
        data.selected =
          checkedKeys.value.includes(data.id) ||
          ['all'].includes(dataStatus.value);
        traverse(data);
      }
    }
  }
  // 遍历树列表中的每个根节点
  for (let i = 0; i < treeList.length; i++) {
    const root = treeList[i];
    root.selected =
      checkedKeys.value.includes(root.id) || ['all'].includes(dataStatus.value);
    traverse(root);
  }

  return result;
};

const updateParentSelection = node => {
  if (node.children && node.children.length > 0) {
    let hasSelectedChild = node.children.some(
      child => child.selected || setStatus(node.children)
    );
    if (hasSelectedChild) {
      node.selected = true;
    }
  } else {
    return node.selected;
  }
};

const setStatus = list => {
  for (let i of list) {
    updateParentSelection(i);
    if (i.children && i.children.length) {
      setStatus(i.children);
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-content-wrap {
  width: 60vw;
  max-width: 800px;
  height: 60vh;
  display: flex;
  flex-direction: column;
}
.dialog-content {
  background: rgba(250, 250, 250, 0.39);
  border: 1px solid #e1e1e1;
  display: flex;
  flex-direction: column;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;
  &:hover {
    overflow: auto;
  }
  .title {
    padding: 7px 13px;
    background-color: #eaeaea;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
  }
  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    ::v-deep .ant-tree {
      background-color: #fafafa;
      .ant-tree-switcher-noop {
        opacity: 0;
      }
      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 14px 0 0px 16px;
}
.footer-btn-list {
  margin-top: 30px;
}
</style>
