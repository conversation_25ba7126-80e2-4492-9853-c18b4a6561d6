<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2024-05-28 10:54:49
-->
<template>
  <div class="table-content">
    <child-page-table
      :pageType="'jrg'"
      :columnList="columnList"
    ></child-page-table>
    <!-- <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isHover: true, isCurrent: true }"
      :data="tableData"
      height="auto"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      ref="jrgTable"
      @edit-closed="editClosedEvent"
      keep-source
      @current-change="currentChange"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
      }"
      :cell-class-name="selectedClassName"
      class="table-edit-common"
      @cell-click="useCellClickEvent"
    >
      <vxe-column
        field="dispNo"
        min-width="60"
        title="序号"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="clear()"
            @keyup="row.dispNo = sortAndlength(row.dispNo, 10)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="worksName"
        min-width="180"
        title="名称"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.worksName"
            type="text"
            :maxlength="2000"
            @blur="clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="specification"
        min-width="180"
        title="规格型号"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.specification"
            type="text"
            @blur="clear()"
          ></vxe-input>
          <span v-else>
            {{ row.specification }}
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="unit"
        min-width="100"
        title="单位"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxeTableEditSelect
            v-if="row && row.dataType === 2"
            :filedValue="row.unit"
            :list="projectStore.unitListString"
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'unit', $rowIndex);
              }
            "
          ></vxeTableEditSelect>
          <span v-else>
            {{ row.unit }}
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="quantitativeExpression"
        min-width="180"
        title="数量表达式"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.quantitativeExpression"
            type="text"
            @blur="clear()"
            :maxlength="1000"
            @keyup="
              row.quantitativeExpression = row.quantitativeExpression.replace(
                /[^\d.\-\+\*\/\{\}\[\]\(\)]/g,
                ''
              )
            "
          ></vxe-input>
          <span v-else>
            {{ row.quantitativeExpression }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="tentativeQuantity" min-width="180" title="暂定数量">
      </vxe-column>
      <vxe-column
        field="price"
        min-width="120"
        title="综合单价"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.price"
            type="text"
            :maxlength="10"
            @blur="(row.price = pureNumber(row.price, 2)), clear()"
          ></vxe-input>
          <span v-else>
            {{ row.price }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="total" min-width="60" title="合价"> </vxe-column>
      <vxe-column
        field="taxRemoval"
        min-width="180"
        title="除税系数(%)"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row && row.dataType === 2"
            :clearable="false"
            v-model.trim="row.taxRemoval"
            type="text"
            :maxlength="10"
            @blur="(row.taxRemoval = pureNumber(row.taxRemoval, 4)), clear()"
          ></vxe-input>
          <span v-else>
            {{ row.taxRemoval }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="jxTotal" min-width="180" title="进项合计">
      </vxe-column>
      <vxe-column field="csPrice" min-width="180" title="除税单价">
      </vxe-column>
      <vxe-column field="csTotal" min-width="180" title="除税合计">
      </vxe-column>
      <template #empty>
        <span
          style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          "
        >
          <img :src="getUrl('newCsProject/none.png')" />
        </span>
      </template>
    </vxe-table> -->
  </div>
</template>
<script setup>
import ChildPageTable from './childPageTable.vue';
const columnList = [
  {
    field: 'dispNo',
    title: '序号',
    minWidth: 60,
    editRender: { autofocus: '.vxe-input--inner' },
    type: 'text',
    slots: { edit: 'dispNo_edit' },
  },
  {
    field: 'worksName',
    title: '名称',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'worksName_edit' },
  },
  {
    field: 'specification',
    title: '规格型号',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'spec_edit' },
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'unit_edit' },
  },
  {
    field: 'quantitativeExpression',
    title: '数量表达式',
    minWidth: 180,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'quant_edit' },
  },
  {
    field: 'tentativeQuantity',
    minWidth: 80,
    title: '暂定数量',
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'tentativeQuantity_edit' },
  },
  {
    field: 'price',
    title: '综合单价',
    minWidth: 100,
    editRender: { autofocus: '.vxe-input--inner' },
    slots: { edit: 'price_edit' },
  },
  {
    field: 'total',
    minWidth: 60,
    title: '合价',
  },
  // {
  //   field: 'taxRemoval',
  //   title: '除税系数(%)',
  //   minWidth: 180,
  //   editRender: { autofocus: '.vxe-input--inner' },
  //   slots: { edit: 'taxRemoval_edit' },
  // },
  // {
  //   field: 'jxTaxAmount',
  //   minWidth: 100,
  //   title: '进项税额',
  // },
  // {
  //   field: 'csPrice',
  //   minWidth: 100,
  //   title: '除税单价',
  // },
  // {
  //   field: 'csTotal',
  //   minWidth: 100,
  //   title: '除税合价',
  // },
];
// import FeeHeader from './FeeHeader.vue';
// import {
//   onMounted,
//   onUpdated,
//   ref,
//   watch,
//   reactive,
//   toRaw,
//   onActivated,
//   getCurrentInstance,
//   nextTick,
// } from 'vue';
// import { projectDetailStore } from '../../../../store/projectDetai l';
// import csProject from '../../../../api/csProject';
// import {
//   isNumericExpression,
//   pureNumber,
//   sortAndlength,
//   getUrl,
// } from '@/utils/index';
// import { useOtherProRaction } from '@/hooks/useOtherProRaction';
// const {
//   getOperateParams,
//   copyOperate,
//   disposeCurrentIndex,
//   operateParams,
//   getCurrentIndex,
//   msgInfo,
//   deleteOperate,
//   deleteModal,
//   menuConfigOptions,
//   pasteIsDisabled,
//   editCheckLength,
//   tabBtnIsValid,
// } = useOtherProRaction({ pageType: 'jrg' });
// let $table; //全局定义
// import qtxmCommon from './qtxmCommon';
// import { message, Modal } from 'ant-design-vue';
// import { insetBus } from '@/hooks/insetBus';
// import { useCellClick } from '@/hooks/useCellClick';
// const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
//   useCellClick();
// const cxt = getCurrentInstance();
// const bus = cxt.appContext.config.globalProperties.$bus;
// let insertType = ref('');
// let copyInfo = ref(''); //复制数据行
// const projectStore = projectDetailStore();
// const jrgTable = ref();
// let loading = ref(false);
// let tableData = ref([]);
// const activeKey = ref(1);
// let deleteModel = ref(false);
// let deleteInfo = ref();
// let isCurrent = ref(null);
// const clear = () => {
//   //清除编辑状态
//   $table.clearEdit();
// };
// const saveCustomInput = (newValue, row, name, index) => {
//   row[name] = newValue;
// };
// const editClosedEvent = ({ row, column }) => {
//   const field = column.field;
//   let value = row[field];
//   editCheckLength(row, column, $table);
//   if (
//     field === 'quantitativeExpression' &&
//     row.quantitativeExpression &&
//     row.quantitativeExpression.length < 1000
//   ) {
//     const [isSuccess, msg] = isNumericExpression(row.quantitativeExpression);
//     if (isSuccess) {
//       $table.revertData(row, 'quantitativeExpression');
//       message.warn('数量表达式输入非法，请重新输入');
//       return;
//     }
//   }
//   if ($table.isUpdateByRow(row, field)) {
//     operate('update', row);
//   }
// };
// const getOtherProjectJrgList = (sequenceNbr = '') => {
//   loading.value = true;
//   let apiData = qtxmCommon.requestParams();
//   csProject.getOtherProjectJrgList(apiData).then(res => {
//     if (res.status === 200) {
//       loading.value = false;
//       tableData.value = res.result;
//       if (sequenceNbr) {
//         getCurrentIndex($table, { sequenceNbr });
//       }
//       if (tableData.value && tableData.value.length > 0) {
//         isCurrent.value
//           ? $table.setCurrentRow(tableData.value[isCurrent.value])
//           : $table.setCurrentRow(tableData.value[0]);
//         insertType.value =
//           $table.getCurrentRecord() && $table.getCurrentRecord().dataType;
//       } else {
//         insertType.value = 0;
//       }
//       projectStore.SET_DATATYPE(insertType.value);
//       projectStore.isAutoPosition = false;
//     }
//   });
// };

// const menuConfig = reactive({
//   ...menuConfigOptions,
//   visibleMethod({ options, column, columnIndex, row, rowIndex }) {
//     if (!row) return;
//     //根据右键选择的数据设置插入数据行和标题行的置灰状态和粘贴的置灰状态
//     pasteIsDisabled(row);
//     $table.setCurrentRow(row);
//     insertType.value = row.dataType;
//     projectStore.SET_DATATYPE(insertType.value);
//     return true;
//   },
// });

// const operate = (type, row) => {
//   getOperateParams(type, row, $table);
//   let apiData = toRaw(operateParams.value);
//   csProject.otherProjectDayWork(apiData).then(res => {
//     if (res.status === 200) {
//       isCurrent.value = disposeCurrentIndex.value;
//       message.success(`${msgInfo.value}成功`);
//       getOtherProjectJrgList();
//     }
//   });
// };
// const contextMenuClickEvent = ({ menu, row }) => {
//   menu.code === 'delete'
//     ? getCurrentIndex($table)
//     : getCurrentIndex($table, row);
//   switch (menu.code) {
//     case 'copy':
//       // 复制
//       copyOperate(row, $table);
//       message.success('复制成功');
//       break;
//     case 'delete':
//       // 删除
//       menuConfig.body.options[0][2].disabled = true;
//       deleteOperate(row, $table);
//       break;
//     case 'paste':
//       // 粘贴
//       row = { ...projectStore.otherProCopyInfo.copyInfo };
//       operate('paste', row);
//       break;
//     case 'addNull':
//       // 插入数据行
//       operate('insertData', row);
//       break;
//     case 'addTitle':
//       // 插入标题行
//       operate('insertTitle', row);
//       break;
//   }
// };

// const currentChange = ({ row }) => {
//   insertType.value = row.dataType;
//   projectStore.SET_DATATYPE(insertType.value);
//   getCurrentIndex($table, row);
// };
// watch(
//   () => projectStore.asideMenuCurrentInfo,
//   () => {
//     if (projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm06') {
//       getOtherProjectJrgList();
//     }
//   }
// );
// watch(
//   () => deleteModal.value.isDelete,
//   () => {
//     if (
//       projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm06' &&
//       deleteModal.value.isDelete
//     ) {
//       operate('delete', deleteModal.value.deleteRow);
//     }
//   }
// );
// onActivated(() => {
//   insetBus(bus, projectStore.componentId, 'qtxmJrg', async data => {
//     if (data.name === 'insert-op') {
//       if (!data.activeKind) {
//         tabBtnIsValid(data.options);
//       } else {
//         data.activeKind === '01'
//           ? operate('insertData', {})
//           : operate('insertTitle', {});
//       }
//     }
//     if (data.name === 'feeExcel') message.info('功能建设中325425435...');
//   });
// });
// onMounted(() => {
//   getOtherProjectJrgList();
//   window.addEventListener('keydown', keyDownOperate);
//   nextTick(() => {
//     $table = jrgTable.value;
//   });
// });
// const keyDownOperate = event => {
//   let select = $table && $table.getCurrentRecord();
//   if (!select) return;
//   if (
//     !$table ||
//     projectStore.asideMenuCurrentInfo?.sequenceNbr !== 'qtxm06' ||
//     projectStore.tabSelectName !== '其他项目'
//   )
//     return;
//   if (event.ctrlKey && event.code == 'KeyC') {
//     copyOperate(select, $table);
//     message.success('复制成功');
//   }
//   let copyDataType =
//     projectStore.otherProCopyInfo &&
//     projectStore.otherProCopyInfo.copyInfo &&
//     projectStore.otherProCopyInfo.copyInfo[0]?.dataType;
//   let flag = true;
//   if (
//     !projectStore.otherProCopyInfo ||
//     (copyDataType === 1 && projectStore.dataType === 2)
//   ) {
//     flag = false;
//   }
//   if (event.ctrlKey && event.code == 'KeyV' && flag) {
//     operate('paste', projectStore.otherProCopyInfo.copyInfo);
//   } else if (event.ctrlKey && event.code == 'KeyV' && !flag) {
//     message.info('选中行不可粘贴');
//   }
//   //暂时不需要快捷键删除
//   // if (event.ctrlKey && event.code == 'KeyD') {
//   //   deleteItem(select);
//   // }
// };

// // 定位方法
// const posRow = sequenceNbr => {
//   getOtherProjectJrgList(sequenceNbr);
// };

// defineExpose({
//   posRow,
// });
</script>
<style lang="scss" scoped>
@import './otherProject.scss';
</style>
